<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>规则设置</title>
    <link rel="stylesheet" th:href="@{~/css/basic/global.css}">
    <link rel="stylesheet" th:href="@{~/css/basic/earlyWarnSet.css}">
</head>

<body>
<div class="dialog-wrap">
    <div class="dialog">
        <div class="dialog-con">
            <div class="rule-left">
                <div class="rule-search">
                    <input type="text" placeholder="搜索系统字段">
                    <button>搜索</button>
                </div>
                <div class="rule-list-wrap">
                    <div class="sel-all"><span id="selAll">全选</span></div>
                    <div class="rule-list" id="ruleList">

                    </div>
                </div>
            </div>
            <div class="rule-right">
                <div class="rule-title">已选系统字段</div>
                <div class="sel-rule" id="selRule">
                </div>
            </div>
        </div>
        <div class="dialog-btn">
            <button class="btn-cancel">取消</button>
            <button class="btn-sure">确定</button>
        </div>
    </div>
</div>
</body>
<script th:src="@{~/js/jquery-3.3.1.min.js}"></script>
<script th:src="@{../js/my.util.js}"></script>
<script th:inline="javascript">
    var rightBo = [[${bo}]];
    var warnRule = [[${warnRule}]]
    $(function () {
        getRule();
    })

    function getRule() {
        $.get("/warnRule/getRule", function (res) {
            if (U.su(res)) {
                var html = "";
                var data = res.data;
                data.forEach(item => {
                    html += '<div class="rule-item">\n' +
                        '       <h3 >' + item.yjgzgl_gzmc + '</h3>\n' +
                        '<span class="sel" data-id="' + item.yjgzgl_gzbh + '"></span>' +
                        '</div>'
                })
                $("#ruleList").html(html);
            } else {
                U.fail("请设置学分预警规则")
            }
            if (warnRule) {
                var ruleJson = JSON.parse(warnRule.rule);
                for (let i = 0; i < ruleJson.length; i++) {
                    var rule = ruleJson[i];
                    var htmlStr = "";
                    if (i == 0) {
                        htmlStr = '<div class="rule-item">' +
                            '<h3>' + rule.content + '</h3>' +
                            '<div class="select-input score-sel tab-sel">' +
                            '<div class="name ckd">' + rule.sign.replaceAll("&lt;", "<").replaceAll("&gt;", ">") + '</div>' +
                            '<em></em>' +
                            '<div class="select-dropdown ">' +
                            '<ul class="dropdown-list sign">' +
                            ' <li class="cur" value="0">>=</li>' +
                            ' <li value="1">></li>' +
                            ' <li value="1"><=</li>' +
                            ' <li value="1"><</li>' +
                            ' </ul>' +
                            ' </div>' +
                            '</div>' +
                            ' <input type="number" class="score" value="' + rule.score + '" min="0">' +
                            '<span class="sel" data-id="' + rule.id + '"></span>' +
                            '</div>'
                    } else {
                        htmlStr = '<div class="rule-item">' +
                            '<div class="select-input score-sel tab-sel">';
                        if (rule.model == 0) {
                            htmlStr += '<div class="name ckd">且</div>';
                        } else {
                            htmlStr += '<div class="name ckd">或</div>';
                        }


                        htmlStr += '<em></em>' +
                            '<div class="select-dropdown ">' +
                            '<ul class="dropdown-list">' +
                            ' <li class="cur" value="0">且</li>' +
                            ' <li value="1">或</li>' +
                            ' </ul>' +
                            ' </div>' +
                            '</div>' +
                            '<h3>' + rule.content + '</h3>' +
                            '<div class="select-input score-sel tab-sel">' +
                            '<div class="name ckd">' + rule.sign.replaceAll("&lt;", "<").replaceAll("&gt;", ">") + '</div>' +
                            '<em></em>' +
                            '<div class="select-dropdown ">' +
                            '<ul class="dropdown-list sign">' +
                            ' <li class="cur" value="0">>=</li>' +
                            ' <li value="1">></li>' +
                            ' <li value="1"><=</li>' +
                            ' <li value="1"><</li>' +
                            ' </ul>' +
                            ' </div>' +
                            '</div>' +
                            ' <input type="number" class="score" value="' + rule.score + '" min="0">' +
                            '<span class="sel" data-id="' + rule.id + '"></span>' +
                            '</div>'
                    }
                    $("#selRule").append(htmlStr)
                    $(".rule-left .rule-item .sel[data-id='" + rule.id + "']").addClass("selected")
                }
            }
        })
    }

    // 添加
    function addItem(ele) {
        var id = ele.attr('data-id');
        var parent = ele.parent();
        var txt = parent.find('h3').text();
        var val = parent.find('input').val()
        var len = $("#selRule .rule-item").length
        if (len == 0) {
            var str = '<div class="rule-item">' +
                '<h3>' + txt + '</h3>' +
                '<div class="select-input score-sel tab-sel">' +
                '<div class="name ckd">>=</div>' +
                '<em></em>' +
                '<div class="select-dropdown ">' +
                '<ul class="dropdown-list sign">' +
                ' <li class="cur" value="0">>=</li>' +
                ' <li value="1">></li>' +
                ' <li value="1"><=</li>' +
                ' <li value="1"><</li>' +
                ' </ul>' +
                ' </div>' +
                '</div>' +
                ' <input type="number" class="score" value="' + val + '" min="0">' +
                '<span class="sel" data-id="' + id + '"></span>' +
                '</div>'
        } else {
            var str = '<div class="rule-item">' +
                '<div class="select-input score-sel tab-sel">' +
                '<div class="name ckd">且</div>' +
                '<em></em>' +
                '<div class="select-dropdown ">' +
                '<ul class="dropdown-list">' +
                ' <li class="cur" value="0">且</li>' +
                ' <li value="1">或</li>' +
                ' </ul>' +
                ' </div>' +
                '</div>' +
                '<h3>' + txt + '</h3>' +
                '<div class="select-input score-sel tab-sel">' +
                '<div class="name ckd">>=</div>' +
                '<em></em>' +
                '<div class="select-dropdown ">' +
                '<ul class="dropdown-list sign">' +
                ' <li class="cur" value="0">>=</li>' +
                ' <li value="1">></li>' +
                ' <li value="1"><=</li>' +
                ' <li value="1"><</li>' +
                ' </ul>' +
                ' </div>' +
                '</div>' +
                ' <input type="number" class="score" value="' + val + '" min="0">' +
                '<span class="sel" data-id="' + id + '"></span>' +
                '</div>'
        }
        $("#selRule").append(str)
    }

    // 添加
    $("#ruleList").on('click', '.rule-item .sel', function (e) {
        e.stopPropagation()
        if ($(this).hasClass('selected')) return;
        $(this).addClass('selected')
        var notSelLen = $("#ruleList .sel:not(.selected)").length
        notSelLen == 0 ? $("#selAll").text('取消全选') : $("#selAll").text('全选')
        addItem($(this))
    })
    // 全选/取消全选
    $("#selAll").click(function () {
        if ($(this).text() === "全选") {
            $(this).text('取消全选')
            $("#ruleList .sel:not(.selected)").each(function () {
                $(this).addClass('selected')
                addItem($(this))
            })
        } else {
            $(this).text('全选')
            $("#selRule").html('')
            $("#ruleList .selected").removeClass('selected')
        }
    })

    // 搜索
    function search(val) {
        if (val) {
            $("#ruleList .rule-item").each(function () {
                $(".sel-all").hide()
                $("#ruleList").height('420px')
                var text = $(this).text()
                if (text.indexOf(val) != -1) {
                    $(this).show()
                } else {
                    $(this).hide()
                }
            })
        } else {
            $("#ruleList .rule-item").show()
            $(".sel-all").show()
            $("#ruleList").height('366px')
        }
    }

    $(".rule-search input").on('keyup', function (e) {
        var val = $(this).val()
        if (e.keyCode == 13) {
            search(val)
        }
    })
    $(".rule-search button").click(function () {
        var val = $(this).prev().val()
        search(val)
    })
    // 删除
    $("#selRule").on('click', '.rule-item .sel', function () {
        var id = $(this).attr("data-id")
        var itemIndex = $(this).parents(".rule-item").index()
        if (itemIndex == 0 && $("#selRule .rule-item").length >= 2) {
            $("#selRule .rule-item").eq(1).find('.select-input').eq(0).remove()
        }
        $(this).parent().remove()
        $("#ruleList .sel[data-id='" + id + "']").removeClass('selected')
        $("#selAll").text('全选')
    })
    // 下拉选择
    $("#selRule").on("click", ".select-input .name", function (e) {
        $(this).parent().toggleClass("clicked");
        e.stopPropagation();
    })
    $("#selRule").on("click",
        ".dropdown-list li",
        function (e) {
            $(this).addClass("cur").siblings().removeClass("cur");
            let kosl = '';
            kosl = $(this).text();
            if (kosl == '为空') {
                kosl = '';
            }
            $(this).parents(".select-input").find(".name").addClass("ckd");
            $(this).parents(".select-input").find(".name").text(kosl);

            $(this).parents(".select-input").removeClass("clicked");
        })
    $(document).click(function (e) {
        var target = $(e.target)
        if (target.closest(".select-input").length == 0) {
            $(".select-input").removeClass("clicked");
        }
    })
    // 点击确定
    $(".btn-sure").click(function () {
        var selItem = []
        $("#selRule .rule-item").each(function () {
            var id = $(this).find('.sel').attr('data-id');
            var content = $(this).find('h3').text();
            var sign = $(this).find('.sign li.cur').text();
            var score = $(this).find('input').val()
            var model = $(this).find('.cur').attr('value')
            selItem.push({
                id: id,
                content: content,
                sign: sign,
                score: score,
                model: model
            })
        })

        if (selItem == [] || selItem.length == 0) {
            U.fail("请选择预警规则")
            return false;
        }
        for (let i = 0; i < selItem.length; i++) {
            var score = selItem[i].score;
            if (score == "" || score == undefined) {
                U.fail("请输入【" + selItem[i].content + "】预警分值");
                return false;
            }
        }
        console.log(selItem)

        $.post("/warnRule/saveOrUpdate", {
            id: warnRule == null ? null : warnRule.id,
            formUserId: rightBo.formUserId,
            rule: JSON.stringify(selItem).replaceAll("<", "&lt;").replaceAll(">", "&gt;")
        }, function (res) {
            if (res.code == 200) {
                warnRule = res.data
                U.success("保存成功")
                setTimeout(function () {
                    U.closePop()
                }, 2000)
            } else {
                U.success("保存失败")
            }

        })
    })
    $(".btn-cancel").click(function () {
        U.closePop()
    })
</script>

</html>