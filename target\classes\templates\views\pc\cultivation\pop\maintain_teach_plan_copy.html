<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>复制专业教学计划</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/global.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/layui/css/layui.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/slideCommon.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/teachPlan.css'}"/>
    <style>
        .error-tips {
            color: #f56c6c;
            font-size: 12px;
            margin-top: 5px;
            display: none;
            position: relative;
            padding-left: 20px;
        }

        .error-tips::before {
            content: "!";
            position: absolute;
            left: 0;
            top: 0;
            width: 16px;
            height: 16px;
            background-color: #f56c6c;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 16px;
            font-weight: bold;
        }

        .error-tips.visible {
            display: block;
        }

        /* 只读字段样式 */
        .readonly-field {
            background-color: #f5f7fa !important;
            color: #909399 !important;
            cursor: not-allowed !important;
        }

        .readonly-field:hover {
            border-color: #dcdfe6 !important;
        }

        /* 遮罩层样式 */
        .marker {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999;
            display: none;
        }

        .dialog {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1000;
            background-color: #fff;
        }

        #loadingDialog {
            text-align: center;
            padding: 20px;
        }

        #loadingDialog .loading {
            width: 50px;
            height: 50px;
            animation: rotate 1.5s linear infinite;
            margin: 20px auto;
            display: block;
        }

        #loadingDialog p {
            margin-top: 15px;
            color: #606266;
            font-size: 14px;
        }

        @keyframes rotate {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }

        @keyframes pulse {
            0% {
                opacity: 0.6;
            }
            50% {
                opacity: 1;
            }
            100% {
                opacity: 0.6;
            }
        }

        /* 优化对齐样式 */
        .item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .item .label {
            width: 70px;
            text-align: right;
            padding-right: 15px;
            flex-shrink: 0;
        }

        .item .label.required::after {
            content: "*";
            color: #f56c6c;
            margin-left: 4px;
        }

        .schoolSel.error {
            border-color: #f56c6c;
        }

        .item .j-search-con {
            flex: 1;
            position: relative;
        }

        .error-tips {
            margin-left: 140px;
        }

        /* 弹窗大小设置 */
        .dialog {
            width: 500px;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            display: none;
        }

        /* 下拉框样式优化 */
        .j-select-year {
            position: absolute;
            width: 100%;
            background: white;
            border: 1px solid #e4e7ed;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            z-index: 9999;
            display: none;
            overflow: hidden;
        }

        /* 动态定位下拉框，避免遮挡 */
        .j-select-year.dropdown-above {
            bottom: 100%;
            top: auto;
            margin-bottom: 2px;
        }

        .j-select-year.dropdown-below {
            top: 100%;
            bottom: auto;
            margin-top: 2px;
        }

        .j-select-year ul {
            max-height: 250px;
            overflow-y: auto;
            margin: 0;
            padding: 5px 0;
            list-style: none;
        }

        /* 下拉框选项样式 */
        .j-select-year ul li {
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            border-left: 3px solid transparent;
        }

        .j-select-year ul li:hover {
            background-color: #f8fafc;
            color: #374151;
        }

        .j-select-year ul li:hover:not(.active) {
            background-color: #f1f5f9;
        }

        .schoolSel {
            width: 100%;
            padding: 8px 10px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            cursor: pointer;
        }

        .schoolSel.error {
            border-color: #f56c6c;
            background-color: #fff0f0;
        }

        .loading-item {
            text-align: center;
            color: #909399;
            padding: 10px 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .loading-spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #409eff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        .no-data {
            text-align: center;
            color: #909399;
            padding: 10px 0;
        }

        .marker {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999;
            display: none;
        }

        .dialog {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1000;
            background-color: #fff;
        }
    </style>
</head>

<body>
<!-- 遮罩层 -->
<div class="marker" id="marker"></div>

<!-- 导出教学任务书对话框 -->
<div class="dialog" id="teachPlanDialog">
    <div class="dialog-con">
        <!-- 学年学期选择 -->
        <div class="item">
            <div class="label required">学年学期</div>
            <div class="j-search-con single-box">
                <input type="text" name="termSel" placeholder="请选择" readonly class="schoolSel" id="termSelect">
                <span class="j-arrow"></span>
                <div class="j-select-year">
                    <div class="search">
                        <input type="text" placeholder="搜索" id="termSearchInput">
                        <span></span>
                    </div>
                    <ul name="term" formAlias="xnxq" fieldAlias="xnxq_xnxqh"></ul>
                </div>
                <div class="error-tips" id="termError">请选择学年学期</div>
            </div>
        </div>
        <!-- 专业选择 -->
        <div class="item">
            <div class="label required">专业名称</div>
            <div class="j-search-con single-box">
                <input type="text" placeholder="请选择" readonly class="schoolSel" id="majorSelect">
                <span class="j-arrow"></span>
                <div class="j-select-year">
                    <div class="search">
                        <input type="text" placeholder="搜索" id="majorSearchInput">
                        <span></span>
                    </div>
                    <ul name="major" formAlias="yxsj" fieldAlias="yxsj_yxmc"></ul>
                </div>
                <div class="error-tips" id="majorError">请选择专业</div>
            </div>
        </div>
        <!-- 专业编号 -->
        <div class="item">
            <div class="label">专业编号</div>
            <div class="j-search-con single-box">
                <input type="text" placeholder="请先选择专业" readonly class="schoolSel readonly-field"
                       id="majorCodeSelect" style="background-color: #f5f7fa; color: #909399; cursor: not-allowed;">
                <div class="error-tips" id="majorCodeError">专业编号将根据所选专业自动填充</div>
            </div>
        </div>
        <!-- 年级选择 -->
        <div class="item">
            <div class="label required">年级</div>
            <div class="j-search-con single-box">
                <input type="text" placeholder="请选择" readonly class="schoolSel" id="gradeSelect">
                <span class="j-arrow"></span>
                <div class="j-select-year">
                    <div class="search">
                        <input type="text" placeholder="搜索" id="gradeSearchInput">
                        <span></span>
                    </div>
                    <ul name="grade" formAlias="njsj" fieldAlias="nj_njmc"></ul>
                </div>
                <div class="error-tips" id="gradeError">请选择年级</div>
            </div>
        </div>
    </div>
    <div class="dialog-footer">
        <button class="btn-cancel" id="cancelBtn">取消</button>
        <button class="btn-sure" id="confirmBtn">确定</button>
    </div>
</div>

<!-- 加载中对话框 -->
<div class="dialog" id="loadingDialog">
    <div class="dialog-con" style="text-align: center;">
        <div style="padding: 30px 20px;">
            <img class="loading" th:src="${_CPR_+_VR_+'/images/cultivation/loading.png'}" alt="加载中">
            <p style="animation: pulse 1.5s infinite;">教学任务书生成中，请稍后至导出记录中查看</p>
        </div>
    </div>
</div>

<script th:src="${_CPR_+_VR_+'/plugin/layui/layui.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/cultivation/slideCommon.js'}"></script>
<script th:inline="javascript">
    // 常量和配置
    const _VR_ = /*[[${_VR_}]]*/ '';
    const formUserId = /*[[${formUserId}]]*/ '';

    // DOM 元素
    const $teachPlanDialog = document.getElementById('teachPlanDialog');
    const $loadingDialog = document.getElementById('loadingDialog');
    const $termSelect = document.getElementById('termSelect');
    const $majorSelect = document.getElementById('majorSelect');
    const $majorCodeSelect = document.getElementById('majorCodeSelect');
    const $gradeSelect = document.getElementById('gradeSelect');
    const $termError = document.getElementById('termError');
    const $majorError = document.getElementById('majorError');
    const $gradeError = document.getElementById('gradeError');
    const $confirmBtn = document.getElementById('confirmBtn');
    const $marker = document.getElementById('marker');
    const $cancelBtn = document.getElementById('cancelBtn');

    // 专业数据缓存
    let majorDataCache = [];
    let majorCodeMap = new Map(); // 专业名称和编号的映射关系

    // 获取默认值
    const defaultMajorName = /*[[${info?.tjzyjxjh_mc}]]*/ '';
    const defaultMajorCode = /*[[${info?.tjzyjxjh_bh}]]*/ '';

    // 初始化
    document.addEventListener('DOMContentLoaded', function () {
        initEventListeners();
        showDialog($teachPlanDialog);
        // 如果有默认专业编号，设置默认选中状态
        if (defaultMajorCode) {
            setDefaultMajorSelection();
        }
    });

    // 显示对话框
    function showDialog(dialog) {
        if ($marker) {
            $marker.style.display = 'block';
        }
        if (dialog) {
            dialog.style.display = 'block';
        }
    }

    // 初始化事件监听
    function initEventListeners() {
        // 下拉框点击事件
        document.querySelectorAll('.schoolSel').forEach(function (element) {
            element.addEventListener('click', handleSelectClick);
        });

        // 搜索框输入事件
        document.getElementById('termSearchInput').addEventListener('input', function () {
            filterOptions('term', this.value);
        });

        document.getElementById('majorSearchInput').addEventListener('input', function () {
            filterOptions('major', this.value);
        });

        document.getElementById('gradeSearchInput').addEventListener('input', function () {
            filterOptions('grade', this.value);
        });

        // 确定按钮点击事件
        $confirmBtn.addEventListener('click', handleConfirm);

        // 取消按钮点击事件
        if ($cancelBtn) {
            $cancelBtn.addEventListener('click', hideDialogs);
        }

        // 点击外部关闭下拉框
        document.addEventListener('click', function (e) {
            if (!e.target.closest('.j-search-con')) {
                hideDropdowns();
            }
        });

        // 按ESC键关闭弹窗
        document.addEventListener('keydown', function (e) {
            if (e.key === 'Escape') {
                hideDialogs();
            }
        });
    }

    // 隐藏所有对话框
    function hideDialogs() {
        document.querySelectorAll('.dialog').forEach(function (dialog) {
            dialog.style.display = 'none';
        });
        if ($marker) {
            $marker.style.display = 'none';
        }
        window.parent.postMessage(JSON.stringify({action: 0}), '*');
    }

    // 隐藏所有下拉框
    function hideDropdowns() {
        document.querySelectorAll('.j-select-year').forEach(function (dropdown) {
            dropdown.style.display = 'none';
        });
    }

    // 处理下拉框点击
    function handleSelectClick(e) {
        e.stopPropagation();

        const parent = this.parentElement;
        const selectYear = parent.querySelector('.j-select-year');
        const ulElement = parent.querySelector('ul');
        const formAlias = ulElement.getAttribute('formAlias');
        const fieldAlias = ulElement.getAttribute('fieldAlias');
        const name = ulElement.getAttribute('name');

        // 如果当前下拉框已显示，则隐藏它
        if (selectYear.style.display === 'block') {
            selectYear.style.display = 'none';
            return;
        }

        // 隐藏所有下拉框，显示当前下拉框
        hideDropdowns();

        // 动态调整下拉框位置，避免遮挡
        adjustDropdownPosition(selectYear, parent);

        selectYear.style.display = 'block';

        // 如果没有选项，加载数据
        if (ulElement.children.length === 0) {
            showLoading(ulElement);
            loadData(name, formAlias, fieldAlias);
        } else if (name === 'major' && defaultMajorCode) {
            // 如果是专业下拉框且有默认专业编号，确保选中状态正确
            setMajorSelectionByCode();
        }

        // 聚焦搜索框
        focusSearchInput(selectYear);
    }

    // 动态调整下拉框位置
    function adjustDropdownPosition(dropdown, container) {
        const containerRect = container.getBoundingClientRect();
        const dialogRect = document.getElementById('teachPlanDialog').getBoundingClientRect();
        const dropdownHeight = 320; // 下拉框最大高度（与CSS保持一致）
        const minSpaceRequired = 150; // 最小所需空间
        const spaceBelow = dialogRect.bottom - containerRect.bottom - 20; // 减去一些边距
        const spaceAbove = containerRect.top - dialogRect.top - 20; // 减去一些边距
        dropdown.classList.remove('dropdown-above', 'dropdown-below');
        if (spaceBelow < minSpaceRequired && spaceAbove > dropdownHeight) {
            dropdown.classList.add('dropdown-above');
        } else {
            dropdown.classList.add('dropdown-below');
        }
    }

    // 显示加载中状态
    function showLoading(element) {
        element.innerHTML = '<li class="loading-item"><span class="loading-spinner"></span>加载中...</li>';
    }

    // 聚焦搜索输入框
    function focusSearchInput(container) {
        const searchInput = container.querySelector('input');
        if (searchInput) {
            setTimeout(() => searchInput.focus(), 100);
        }
    }

    // 根据类型加载数据
    function loadData(name, formAlias, fieldAlias) {
        const ulElement = document.querySelector(`ul[name="${name}"]`);
        if (!ulElement) return;

        // 如果是专业数据，使用专门的接口
        if (name === 'major') {
            loadMajorData(ulElement);
            return;
        }

        fetch(`${_VR_}/teacherIdle/getFormDistinctFiled`, {
            method: 'POST',
            headers: {'Content-Type': 'application/x-www-form-urlencoded'},
            body: new URLSearchParams({
                formAlias: formAlias,
                fieldAlias: fieldAlias
            })
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error('网络响应错误');
                }
                return response.json();
            })
            .then(data => {
                const hasData = data && data.list && data.list.length > 0;
                if (hasData) {
                    renderOptions(name, data);
                } else {
                    showNoData(ulElement);
                }
            })
            .catch(error => {
                console.error(`加载${name}数据失败:`, error);
                showLoadError(ulElement);
            });
    }

    // 加载专业数据（包含专业编号）
    function loadMajorData(ulElement) {
        showLoading(ulElement);

        fetch(`${_VR_}/cultivation/maintainTeachPlan/getMajorList`, {
            method: 'POST',
            headers: {'Content-Type': 'application/x-www-form-urlencoded'},
            body: new URLSearchParams({
                // 可以添加查询参数，比如学院ID等
            })
        }).then(response => {
            if (!response.ok) {
                throw new Error('网络响应错误');
            }
            return response.json();
        }).then(data => {
            const hasData = data && data.list && data.list.length > 0;
            if (hasData) {
                majorDataCache = data.list;
                data.list.forEach(item => {
                    if (item.zysj_zymc && item.zysj_zybh) {
                        majorCodeMap.set(item.zysj_zymc, item.zysj_zybh);
                    }
                });
                renderMajorOptions(ulElement, data.list);
                // 如果有默认专业编号，在数据加载完成后设置选中状态
                if (defaultMajorCode) {
                    setMajorSelectionByCode();
                }
            } else {
                showNoData(ulElement);
            }
        }).catch(error => {
            console.error('加载专业数据失败:', error);
            showLoadError(ulElement);
        });
    }

    // 显示无数据提示
    function showNoData(element) {
        element.innerHTML = '<li class="no-data">暂无数据</li>';
    }

    // 显示加载错误提示
    function showLoadError(element) {
        element.innerHTML = '<li class="no-data">加载失败，请重试</li>';
    }

    // 渲染选项
    function renderOptions(name, data) {
        const ulElement = document.querySelector(`ul[name="${name}"]`);
        if (!ulElement) return;

        // 生成HTML
        const html = generateOptionsHtml(name, data);

        if (!html) {
            showNoData(ulElement);
            return;
        }

        ulElement.innerHTML = html;

        // 添加选项点击事件
        attachOptionClickEvents(ulElement, name);
    }

    // 渲染专业选项
    function renderMajorOptions(ulElement, majorList) {
        if (!majorList || majorList.length === 0) {
            showNoData(ulElement);
            return;
        }
        const sortedList = [...majorList].sort((a, b) => {
            const nameA = a.zysj_zymc || a;
            const nameB = b.zysj_zymc || b;
            return String(nameA).localeCompare(String(nameB));
        });
        ulElement.innerHTML = sortedList.map(item => {
            const majorName = item.zysj_zymc || item;
            const majorCode = item.zysj_zybh || '';
            return `<li data-major-code="${majorCode}">${majorName}</li>`;
        }).join('');
        // 添加专业选项点击事件
        attachMajorOptionClickEvents(ulElement);
    }

    // 生成选项HTML
    function generateOptionsHtml(name, data) {
        if (!data.list || !Array.isArray(data.list) || data.list.length === 0) {
            return '';
        }

        // 按降序排列，最新在前
        const sortedList = [...data.list].sort((a, b) =>
            String(b).localeCompare(String(a))
        );

        return sortedList
            .map(item => `<li>${item}</li>`)
            .join('');
    }

    // 添加选项点击事件
    function attachOptionClickEvents(container, name) {
        container.querySelectorAll('li').forEach(li => {
            // 跳过特殊项
            if (li.classList.contains('no-data') || li.classList.contains('loading-item')) {
                return;
            }

            li.addEventListener('click', function (e) {
                handleOptionClick(e, this, name);
            });
        });
    }

    // 处理选项点击
    function handleOptionClick(e, element, name) {
        e.stopPropagation();

        const input = element.closest('.j-search-con').querySelector('.schoolSel');
        const dropdown = element.closest('.j-select-year');
        const ulElement = dropdown.querySelector('ul');

        // 移除所有选项的选中状态
        ulElement.querySelectorAll('li').forEach(li => {
            li.classList.remove('active');
        });

        // 添加当前选项的选中状态
        element.classList.add('active');

        // 设置输入框值
        input.value = element.textContent;

        // 如果有data属性，保存它
        if (element.dataset.uid) {
            input.dataset.uid = element.dataset.uid;
        }

        // 隐藏下拉框
        dropdown.style.display = 'none';

        // 移除错误状态
        input.classList.remove('error');

        // 隐藏错误提示
        const errorElement = document.getElementById(`${name}Error`);
        if (errorElement) {
            errorElement.classList.remove('visible');
        }
    }

    // 添加专业选项点击事件
    function attachMajorOptionClickEvents(container) {
        container.querySelectorAll('li').forEach(li => {
            // 跳过特殊项
            if (li.classList.contains('no-data') || li.classList.contains('loading-item')) {
                return;
            }

            li.addEventListener('click', function (e) {
                handleMajorOptionClick(e, this);
            });
        });
    }

    // 处理专业选项点击
    function handleMajorOptionClick(e, element) {
        e.stopPropagation();

        const majorName = element.textContent.trim();
        const majorCode = element.dataset.majorCode || '';
        const dropdown = element.closest('.j-select-year');
        const ulElement = dropdown.querySelector('ul');

        // 移除所有选项的选中状态
        ulElement.querySelectorAll('li').forEach(li => {
            li.classList.remove('active');
        });

        // 添加当前选项的选中状态
        element.classList.add('active');

        // 更新专业名称输入框
        const majorInput = document.getElementById('majorSelect');
        majorInput.value = majorName;

        // 更新专业编号输入框
        const majorCodeInput = document.getElementById('majorCodeSelect');
        majorCodeInput.value = majorCode;
        majorCodeInput.placeholder = majorCode ? '' : '暂无编号';

        // 更新专业编号映射关系
        if (majorName && majorCode) {
            majorCodeMap.set(majorName, majorCode);
        }

        // 隐藏下拉框
        dropdown.style.display = 'none';

        // 移除错误状态
        majorInput.classList.remove('error');

        // 隐藏错误提示
        const errorElement = document.getElementById('majorError');
        if (errorElement) {
            errorElement.classList.remove('visible');
        }
    }

    // 设置选项的选中状态（用于回显已选择的值）
    function setactiveOption(name, value) {
        const ulElement = document.querySelector(`ul[name="${name}"]`);
        if (!ulElement || !value) return;

        // 移除所有选中状态
        ulElement.querySelectorAll('li').forEach(li => {
            li.classList.remove('active');
        });

        // 查找匹配的选项并设置选中状态
        ulElement.querySelectorAll('li').forEach(li => {
            if (li.textContent.trim() === value.trim()) {
                li.classList.add('active');
            }
        });
    }

    // 更新输入框值时同步选中状态
    function updateInputValue(inputId, value, optionName) {
        const input = document.getElementById(inputId);
        if (input) {
            input.value = value;
            // 同步更新下拉框选中状态
            setactiveOption(optionName, value);
        }
    }

    // 设置默认专业选中状态
    function setDefaultMajorSelection() {
        if (!defaultMajorCode) return;
        $majorCodeSelect.value = defaultMajorCode;
        $majorSelect.value = defaultMajorName;
        const ulElement = document.querySelector('ul[name="major"]');
        if (ulElement && ulElement.children.length > 0) {
            setMajorSelectionByCode();
        }
    }

    // 根据专业编号设置选中状态和回填专业名称
    function setMajorSelectionByCode() {
        if (!defaultMajorCode) return;

        const ulElement = document.querySelector('ul[name="major"]');
        const majorInput = document.getElementById('majorSelect');
        const majorError = document.getElementById('majorError');

        if (!ulElement) return;

        // 查找匹配的专业选项
        ulElement.querySelectorAll('li').forEach(li => {
            const majorCode = li.dataset.majorCode || '';
            if (majorCode === defaultMajorCode) {
                // 设置选中状态
                li.classList.add('active');

                // 回填专业名称
                const majorName = li.textContent.trim();
                if (majorInput) {
                    majorInput.value = majorName;
                    majorInput.classList.remove('error');
                }

                // 隐藏错误提示
                if (majorError) {
                    majorError.classList.remove('visible');
                }
            }
        });
    }

    // 过滤选项
    function filterOptions(name, keyword) {
        const ulElement = document.querySelector(`ul[name="${name}"]`);
        if (!ulElement) return;

        const lowercaseKeyword = keyword.toLowerCase();
        let hasMatches = false;

        // 过滤项目
        ulElement.querySelectorAll('li').forEach(item => {
            // 跳过特殊项
            if (item.classList.contains('no-data') || item.classList.contains('loading-item')) {
                return;
            }

            const text = item.textContent.toLowerCase();
            const isMatch = text.includes(lowercaseKeyword);

            item.style.display = isMatch ? '' : 'none';
            if (isMatch) hasMatches = true;
        });

        // 更新无匹配结果提示
        updateNoMatchMessage(ulElement, hasMatches, keyword);
    }

    // 更新无匹配结果提示
    function updateNoMatchMessage(container, hasMatches, keyword) {
        let noMatchElem = container.querySelector('.no-match');

        // 如果没有匹配且有关键词，显示无匹配提示
        if (!hasMatches && keyword) {
            if (!noMatchElem) {
                noMatchElem = document.createElement('li');
                noMatchElem.className = 'no-data no-match';
                noMatchElem.textContent = '无匹配结果';
                container.appendChild(noMatchElem);
            } else {
                noMatchElem.style.display = '';
            }
        } else if (noMatchElem) {
            noMatchElem.style.display = 'none';
        }
    }

    // 处理确认按钮点击
    function handleConfirm() {
        if (!validateFormWithFeedback()) {
            return;
        }
        submitExportRequest();
    }

    // 显示加载对话框
    function showLoadingDialog() {
        $teachPlanDialog.style.display = 'none';
        $loadingDialog.style.display = 'block';
    }

    // 隐藏加载对话框
    function hideLoadingDialog() {
        $loadingDialog.style.display = 'none';
    }

    // 带反馈的表单验证
    function validateFormWithFeedback() {
        const termValid = validateField($termSelect, $termError);
        const majorValid = validateField($majorSelect, $majorError);
        const gradeValid = validateField($gradeSelect, $gradeError);

        const isValid = termValid && majorValid && gradeValid;

        if (!isValid) {
            // 添加抖动效果
            addShakeEffect();
        }

        return isValid;
    }

    // 验证单个字段
    function validateField(inputElement, errorElement) {
        const isValid = !!inputElement.value;

        if (isValid) {
            errorElement.classList.remove('visible');
            inputElement.classList.remove('error');
        } else {
            errorElement.classList.add('visible');
            inputElement.classList.add('error');
        }

        return isValid;
    }

    // 添加抖动效果
    function addShakeEffect() {
        document.querySelectorAll('.schoolSel.error').forEach(input => {
            input.animate([
                {transform: 'translateX(-5px)'},
                {transform: 'translateX(5px)'},
                {transform: 'translateX(-5px)'},
                {transform: 'translateX(5px)'},
                {transform: 'translateX(0)'}
            ], {
                duration: 400,
                easing: 'ease-in-out'
            });
        });
    }

    // 提交导出请求
    function submitExportRequest() {
        const params = {
            formUserId: formUserId,
            tjzyjxjh_xq: $termSelect.value,
            tjzyjxjh_mc: $majorSelect.value,
            tjzyjxjh_nj: $gradeSelect.value,
            tjzyjxjh_bh: $majorCodeSelect.value
        };
        fetch(`${_VR_}/cultivation/maintainTeachPlan/copyHandle`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: new URLSearchParams(params)
        }).then(response => {
            if (!response.ok) {
                throw new Error(`网络响应失败`);
            }
            return response.json();
        }).then(data => {
            if (data.success === true || data.code === 200 || data.status === 'success') {
                showSuccessMessage('复制成功');
            } else {
                const errorMessage = data.message || data.msg || data.error || '操作失败';
                showErrorMessage(errorMessage);
            }
        }).catch(error => {
            showErrorMessage('操作失败');
        })
    }

    // 显示成功消息
    function showSuccessMessage(message) {
        layui.use('layer', function () {
            const layer = layui.layer;
            layer.msg(message, {
                icon: 1,
                time: 3000
            });
        });
        setTimeout(() => {
            window.parent.postMessage(JSON.stringify({action: 1}), '*');
        }, 1500);
    }

    // 显示错误消息
    function showErrorMessage(message) {
        layui.use('layer', function () {
            const layer = layui.layer;
            layer.msg(message, {
                icon: 2,
                time: 4000
            });
        });
        // 显示回主对话框，让用户可以重新操作
        $teachPlanDialog.style.display = 'block';
    }

    // 隐藏所有对话框
    function hideDialogs() {
        document.querySelectorAll('.dialog').forEach(function (dialog) {
            dialog.style.display = 'none';
        });
        document.querySelector('.marker').style.display = 'none';
        window.parent.postMessage(JSON.stringify({action: 0}), '*')
    }

    // 初始化
    document.addEventListener('DOMContentLoaded', function () {
        initEventListeners();
        showDialog($teachPlanDialog);
    });
</script>
</body>
</html>