<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导入</title>
    <link rel="stylesheet" th:href="@{~/plugin/layui/css/layui.css}">

    <style>
        .masker {
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.6);
            position: fixed;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
        }

        .dialog {
            border-radius: 10px;
            background-color: #ffffff;
            width: 660px;
            position: fixed;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
        }

        .dialog .dialog-title {
            border-bottom: 1px solid #E5E6EB;
            height: 56px;
            line-height: 56px;
            color: #1D2129;
            font-size: 16px;
            text-indent: 30px;
        }

        .dialog-title span {
            display: block;
            width: 30px;
            height: 30px;
            background: url(/images/examination/ungrant-icon.png) no-repeat center;
            background-size: 26px;
            position: absolute;
            right: 14px;
            top: 14px;
            cursor: pointer;
        }

        .dialog form {
            margin: 20px 20px 80px;
        }

        .dialog form .layui-input-block {
            width: 240px;
        }

        .layui-form-select dl dd.layui-this {
            background-color: #4D88FF;
        }

        .note {
            margin: 20px 30px;
        }

        .note h5 {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .note p {
            line-height: 26px;
            color: #464545;
        }

        .note p a {
            color: #4D88FF;
            cursor: pointer;
        }

        .upload {
            display: flex;
            justify-content: center;
        }

        .upload button {
            border-radius: 30px;
            width: 120px;
            margin: 0 auto;
        }
    </style>
</head>

<body>
    <div class="masker"></div>
    <div class="dialog" id="exportRecord">
<!--        <div class="dialog-title">导入考表</div>-->
        <div class="dialog-con">
            <form action="" class="layui-form">
<!--                <div class="layui-form-item">-->
<!--                    <label class="layui-form-label">导入模式</label>-->
<!--                    <div class="layui-input-block">-->
<!--                        <select name="interest" lay-filter="modal">-->
<!--                            <option value="0" selected>仅新增数据</option>-->
<!--                            <option value="1">仅更新数据</option>-->
<!--                            <option value="2">更新和新增数据</option>-->
<!--                        </select>-->
<!--                    </div>-->
<!--                </div>-->
                <div class="note">
                    <h5>注意事项：</h5>
<!--                    <p>-->
<!--                        1.表格第一行是表头，对应表单字段,请不要轻易删改。-->
<!--                    </p>-->
                    <p>
                        1.文件中数据不能超过10000行、200列。
                    </p>
<!--                    <p>-->
<!--                        3.子表单明细同样占用行数。-->
<!--                    </p>-->
                    <p>2.为保证数据导入顺利，请<a th:href="@{~/templateFile/examinationImportTemplate.xlsx}" download="导入模板.xlsx">下载excel模板</a>，并仔细阅读其中的导入示例和说明。
                    </p>
                </div>
                <div class="upload">

                    <button type="button" class="layui-btn demo-class-accept layui-btn-normal"
                        lay-options="{accept: 'file'}">
                        上传文件
                    </button>
                </div>
            </form>

        </div>
    </div>
</body>
<script th:src="@{~/plugin/layui/layui.js}"></script>
<script>
    layui.use(['form', 'layer', 'upload'], function () {
        var form = layui.form, upload = layui.upload,
            layer = layui.layer;

        // 渲染
        upload.render({
            elem: '.demo-class-accept',
            url: '/examination/result/importExcel', //上传地址
            accept: 'file', // 普通文件
            done: function (res) {
                console.log(res)
                if (res.code==200){
                    layer.msg("正在导入,稍后请在导入记录中查看导入结果", {icon: 1, time: 2000});
                }else {
                    layer.msg(res.msg, {icon: 2, time: 2000});
                }
            }, error: function () {
                layer.msg("系统繁忙", {icon: 2, time: 2000});
            }
        });
    })
</script>

</html>