<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>重置密码</title>
    <link rel="stylesheet" th:href="@{../css/cultivation/global.css}">
    <link rel="stylesheet" th:href="@{../css/cultivation/batchChangeDepth.css}">
    <link rel="stylesheet" th:href="@{../plugin/layui/css/layui.css}">
    <script th:src="@{../plugin/layui/layui.js}"></script>
</head>
<body>
<div class="dialog-mark" id="dialogTip">
    <div class="d-dialog dialog-tip">
        <div class="dialog-con">
            <p></p>
        </div>
        <div class="dialog-btn">
            <button class="btn-cancel">取消</button>
            <button class="btn-submit">确认</button>
        </div>
    </div>
    <div class="d-dialog dialog-modify">
        <div class="modify"><img src="/images/cultivation/loading1.gif" alt="">正在修改中</div>
    </div>
</div>
</body>
<script th:inline="javascript">
    document.domain = "chaoxing.com";
    const formUserId = [[${formUserId}]];
    const formId = [[${formId}]];
    const fid = [[${fid}]];
    const alias = [[${alias}]];
    const enc = [[${enc}]];
    layui.use(['jquery','layer'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        generatePwd(8);
        $(".dialog-con p").text("重置密码为:"+pwd);
        $(".btn-submit").click(function (){
            $.post("../admin/formData/resetPwd", {
                formUserId:formUserId,
                formId:formId,
                fid:fid,
                alias:alias,
                enc:enc,
                pwd:pwd
            }, function(result){
                if(result.success){
                    layer.msg(result.message, {icon: 1, time: 2000});
                    setTimeout(closePop,2000);

                }else {
                    layer.msg(result.message, {icon: 2, time: 2000});
                }
            }, "json");
        })
        $(".btn-cancel").click(function (){
            closePop();
        })
    })

    function closePop(){
        window.parent.postMessage(JSON.stringify({action:1}),"*");
    }

    let pwd = '';
    function generatePwd(len) {
        pwd = '';
        let arr = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        for(var i = len; i > 0; i--) {
            pwd += arr[Math.floor(Math.random() * arr.length)];
        }
        if (!/^.*?[\d]+.*$/.test(pwd) || !/^.*?[A-Za-z].*$/.test(pwd) || !/^.{8,30}$/.test(pwd)){
            generatePwd(8);
        }
        return pwd;
    }

</script>
</html>