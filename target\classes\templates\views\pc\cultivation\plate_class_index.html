<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模块开班管理</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/global.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/plugin/layui-v2.8.18/layui/css/layui.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/slideCommon.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/layuiReset.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/plateDailog.css'}">
    <link rel="stylesheet"
          th:href="@{${_CPR_+_VR_}+'/css/cultivation/plateType.css'(v=${new java.util.Date().getTime()})}">
    <script th:src="${_CPR_+_VR_+'/plugin/layui-v2.8.18/layui/layui.js'}"></script>
    <style>
        .j-search-con {
            width: 200px;
        }
    </style>
</head>

<body>
<div class="main">
    <div class="m-top">
        <div class="title">板块开班管理</div>
    </div>
    <form action="" class="layui-form form-search" lay-filter='formSearch' style="padding: 24px 32px 0;">
        <div class="layui-inline">
            <label class="layui-form-label" style="width: 56px;">学年学期</label>
            <div class="layui-input-inline">
                <div class="j-search-con single-box">
                    <input type="text" name="semester" placeholder="请选择" readonly="" class="schoolSel"
                           formAlias="plateClass" fieldAlias="semester" source="table">
                    <span class="j-arrow"></span>
                    <div class="j-select-year">
                        <ul>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label" style="width: 56px;">板块类型名称</label>
            <div class="layui-input-inline">
                <div class="j-search-con single-box">
                    <input type="text" name="plateTypeName" placeholder="请选择" readonly="" class="schoolSel"
                           formAlias="plateType" fieldAlias="name" source="table">
                    <span class="j-arrow"></span>
                    <div class="j-select-year">
                        <ul>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label" style="width: 56px;">板块项目名称</label>
            <div class="layui-input-inline">
                <div class="j-search-con single-box">
                    <input type="text" name="classNameArr" placeholder="请选择" readonly="" class="schoolSel"
                           formAlias="plateClass" fieldAlias="classNameArr" source="table">
                    <span class="j-arrow"></span>
                    <div class="j-select-year">
                        <ul>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-inline">
            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            <button type="submit" class="layui-btn " lay-submit lay-filter="formSearch">查询</button>
        </div>
    </form>
    <div class="table-opt">
        <button type="submit" class="layui-btn" id="addPlate">添加</button>
        <button type="submit" class="layui-btn layui-btn-disabled" id="addClassTime">安排上课时间</button>
        <button type="submit" class="layui-btn" id="addPlan">计划对应</button>
        <!--<button type="submit" class="layui-btn layui-btn-primary" id="btnExport">导出</button>-->
    </div>
    <div class="table-box">
        <table class="layui-hide" id="plateType" lay-filter="plateType"></table>
    </div>
</div>
<div class="dialog" id="addChildType">
    <div class="dialog-title">
        <h5>添加</h5><span class="close"></span>
    </div>
    <div class="dialog-con">
        <form action="" class="layui-form form-item" lay-filter='formItem'>
            <div class="layui-form-item">
                <label class="layui-form-label"><i>*</i>板块开课名称</label>
                <div class="layui-input-block">
                    <input type="text" name="plateClassName" placeholder="请输入板块开课名称" class="layui-input"
                           required lay-verify="required" style="width: 200px;">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><i>*</i>学年学期</label>
                <div class="layui-input-block">
                    <div class="j-search-con single-box">
                        <input type="text" name="semester" placeholder="请选择" readonly="" class="schoolSel"
                               formAlias="xnxq" fieldAlias="xnxq_xnxqh" th:value="${semester}">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <ul>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><i>*</i>板块名称</label>
                <div class="layui-input-block">
                    <div class="j-search-con single-box">
                        <input type="hidden" name="plateTypeId"/>
                        <input type="text" name="plateTypeName" formAlias="plateTypeCourse" source="relation"
                               fieldAlias="plateTypeName"
                               placeholder="请选择"
                               readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <ul>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><i>*</i>类型级别</label>
                <div class="layui-input-block">
                    <div class="j-search-con single-box">
                        <input type="hidden" name="plateTypeLevelId"/>
                        <input type="text" name="plateTypeLevelName" formAlias="plateTypeLevel" source="relation"
                               fieldAlias="levelName"
                               placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <ul>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="dialog-footer">
        <button class="btnCancel" id="btnPlateCancel">取消</button>
        <button id="btnPlateSure">确定</button>
    </div>
</div>
<div class="dialog" id="addPlanCorrespond">
    <div class="dialog-title">
        <h5>计划对应</h5><span class="close"></span>
    </div>
    <div class="dialog-con">
        <form action="" class="layui-form form-item" lay-filter='correspondFormItem'>
            <div class="layui-form-item">
                <label class="layui-form-label"><i>*</i>学年学期</label>
                <div class="layui-input-block">
                    <div class="j-search-con single-box">
                        <input type="text" name="semester" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <ul>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><i>*</i>板块名称</label>
                <div class="layui-input-block">
                    <div class="j-search-con single-box">
                        <input type="hidden" name="plateTypeId"/>
                        <input type="text" name="plateTypeName" placeholder="请选择"
                               readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <ul>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><i>*</i>类型级别</label>
                <div class="layui-input-block">
                    <div class="j-search-con single-box">
                        <input type="hidden" name="plateTypeLevelId"/>
                        <input type="text" name="plateTypeLevelName"
                               placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <ul>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><i>*</i>板块课程</label>
                <div class="layui-input-block">
                    <div class="j-search-con single-box">
                        <input type="text" name="courseName" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <ul>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="dialog-footer">
        <button class="btnCancel" id="btnPlanCorrespondCancel">取消</button>
        <button id="btnPlanCorrespondSure">确定</button>
    </div>
</div>
<div class="dialog" id="addClassTimeDialog">
    <div class="dialog-title">
        <h5>上课时间</h5><span class="close"></span>
    </div>
    <div class="dialog-con">
        <div class="table-opt" style="justify-content:flex-start">
            <button type="submit" class="layui-btn" id="addPlateTime">添加</button>
        </div>
        <div class="table-box">
            <table class="layui-hide" id="plateTime" lay-filter="plateTime"></table>
        </div>
    </div>
    <div class="dialog-footer">
        <button class="btnCancel" id="plateTimeCancel">取消</button>
        <button id="plateTimeSure">确定</button>
    </div>
</div>
<div class="dialog" id="dialogTip" style="display: none;">
    <div class="dialog-con">
        <img src="/images/cultivation/mooc/tips-error.png" alt="">
        <p>保存成功</p>
        <button id="tipSuccessBtn">确定</button>
    </div>
</div>
<div id="tipsBox"></div>
</body>
<script type="text/html" id="plateTypeToolBar">
    <span class="opt-btn" lay-event="maintainItem">维护板块对象 </span>
    <span class="opt-btn" lay-event="setClassTime">安排上课时间</span>
    <span class="opt-btn color-red" lay-event="del">删除</span>
</script>
<!-- 星期 -->
<script type="text/html" id="selectWeek">
    <div class="j-search-con multiple-box" style="width: 150px;">
        <input type="text" placeholder="请选择" name="week" readonly="" class="schoolSel">
        <span class="j-arrow"></span>
        <div class="j-select-year">
            <ul>
                {{# layui.each(['星期一','星期二','星期三','星期四','星期五'], function(i, v){ }}
                <li value="{{=  }}">{{= v }}</li>
                {{# }); }}
            </ul>
        </div>
    </div>
</script>
<!-- 节次 -->
<script type="text/html" id="selectFestivals">
    <div class="festivals layui-table-cell" style="display: flex;align-items: center;">
        <div class="j-search-con single-box" style="width: 110px;">
            <input type="text" placeholder="请选择" name="startFestivals" readonly="" class="schoolSel">
            <span class="j-arrow"></span>
            <div class="j-select-year">
                <ul>
                </ul>
            </div>
        </div>
        <div style="margin: 0 10px;">-</div>
        <div class="j-search-con single-box" style="width: 110px;">
            <input type="text" placeholder="请选择" name="endFestivals" readonly="" class="schoolSel">
            <span class="j-arrow"></span>
            <div class="j-select-year">
                <ul>
                </ul>
            </div>
        </div>
    </div>
</script>
<!-- 操作 -->
<script type="text/html" id="toolBarOpt">
    <div class="opt-btn-wrap">
        <span class="opt-btn" lay-event="oddWeek">单周</span>
        <span class="opt-btn" lay-event="evenWeek">双周</span>
        <span class="opt-btn" lay-event="allWeek">全选</span>
        <span class="opt-btn color-red" lay-event="clearWeek">清除选择</span>
        <span class="opt-btn color-red" lay-event="del">删除行</span>
    </div>
</script>
<script th:inline="javascript">
    const _VR_ = [[${_VR_}]];
    const fid = [[${fid}]];
    const id = "";
    const semester = [[${semester}]];
</script>
<script th:src="@{${_CPR_+_VR_}+'/js/cultivation/plateItemType.js'(v=${new java.util.Date().getTime()})}"></script>
<script th:src="@{${_CPR_+_VR_}+'/js/cultivation/plateCommon.js'(v=${new java.util.Date().getTime()})}"></script>
</html>