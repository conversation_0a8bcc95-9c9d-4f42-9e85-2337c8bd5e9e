<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <title>选择课程</title>
    <link rel="stylesheet" th:href="@{../css/cultivation/jquery.searchableSelect.css}"/>
    <link rel="stylesheet" th:href="@{../css/cultivation/newStyle.css(v=${new java.util.Date().getTime()})}"/>
    <link rel="stylesheet" th:href="@{../plugin/layui/css/layui.css}"/>
    <style>
        html, body {
            height: 100%;
        }

        body {
            overflow-y: auto;
            zoom: 1;
            transform: scale(1);
            width: 100%;
            height: 100%;
        }

        .layui-laypage .layui-laypage-curr .layui-laypage-em {
            background-color: #4C88FF !important;
        }

        .layui-form-radio > i:hover, .layui-form-radioed > i {
            color: #4C88FF !important;
        }

        body .layui-layer-btn .layui-layer-btn0 {
            background: #4C88FF;
            border: 1px solid #4C88FF;
            border-radius: 20px;
            display: inline-block;
            width: 60px;
            height: 34px;
            text-align: center;
            line-height: 34px;
            font-size: 14px;
            color: #FFFFFF;
        }

        body .layui-layer-btn .layui-layer-btn1 {
            background: #FFFFFF;
            border: 1px solid #94C1FF;
            border-radius: 20px;
            display: inline-block;
            width: 60px;
            height: 34px;
            text-align: center;
            line-height: 34px;
            font-size: 14px;
            color: #4C88FF;
        }

        body .layui-layer-btn {
            padding: 32px 15px 12px;
        }

        #popScroll {
            max-height: unset;
        }

        .wid840 {
            max-height: unset;
        }

        .popSearch_cont {
            padding: 10px 30px 0;
        }

        body .layui-layer-btn {
            padding: 22px 15px 12px;
        }
    </style>
</head>
<body>
<div class="popDiv popMove wid840">
    <div class="popBody" id="popScroll">
        <div class="popSearch clearAfter">
            <div class="popSearch_row fl">
                <div class="popSearch_name">
                    <div class="popSearch_table">
                        <div class="popSearch_cell"><span>课程编号</span></div>
                    </div>
                </div>
                <div class="popSearch_per"><input type="text" placeholder="课程编号" name="kcbh"
                                                  class="popSearch_input"></div>
            </div>
            <div class="popSearch_row fl">
                <div class="popSearch_name">
                    <div class="popSearch_table">
                        <div class="popSearch_cell"><span>课程名称</span></div>
                    </div>
                </div>
                <div class="popSearch_per"><input type="text" placeholder="课程名称" name="kcmc"
                                                  class="popSearch_input"></div>
            </div>
            <div class="popSearch_row fl">
                <div class="popSearch_name">
                    <div class="popSearch_table">
                        <div class="popSearch_cell"><span>是否启用</span></div>
                    </div>
                </div>
                <select name="sfqy" class="qselect">
                    <option value="">请选择</option>
                    <option value="是">是</option>
                    <option value="否">否</option>
                </select>
            </div>
        </div>
        <div class="popSearch_btns">
            <div class="popSearch_clear fr">清空筛选</div>
            <div class="popSearch_search_btn fr" lay-filter="search" lay-submit>筛选</div>
        </div>

        <div class="popSearch_cont">
            <table lay-filter="jxqTable" class="layui-table" id="jxqTable">
            </table>
        </div>
    </div>
    <div class="layui-layer-btn"><a class="layui-layer-btn0">确定</a><a class="layui-layer-btn1">关闭</a></div>
</div>
<script th:src="@{../js/jquery-3.3.1.min.js}"></script>
<script th:src="@{../js/jquery.nicescroll.min.js}"></script>
<script th:src="@{../js/cultivation/jquery.searchableSelect.js}"></script>
<script th:src="@{../plugin/layui/layui.js}"></script>
<script th:inline="javascript">
    var fid = [[${fid}]];

    //弹窗居中
    function MoveFixed(userClass) {
        $(userClass).css({
            top: function () {
                return 0;
            }, left: function () {
                return ($(window).width() - $(this).width()) / 2;
            }
        });

    }

    window.onresize = function () {
        MoveFixed('.popMove')
    }
    MoveFixed('.popMove')

    function scrollBox(userID) {
        $(userID).niceScroll({
            cursorborder: "",
            cursorwidth: 8,
            cursorcolor: "#CFD8E6",
            boxzoom: false,
            autohidemode: true
        });
        $(userID).getNiceScroll().resize(); //检测滚动条是否重置大小（当窗口改变大小时）
    }

    scrollBox("#popScroll");
    document.domain = document.domain.split('.').slice(-2).join('.');
    $('.qselect').change();
    $('.qselect').searchableSelect();

    function getSelectData() {
        var checkRows = table.checkStatus('jxqTable').data;
        if (checkRows.length == 0) {
            return 0;
        }
        var rowData = checkRows[0];
        return rowData;
    }

    $(".popSearch_search_btn").click(function () {
        var kcmc = $("input[name='kcmc']").val();
        var kcbh = $("input[name='kcbh']").val();
        var sfqy = $("select[name='sfqy']").val();
        var field = {kck_sfqy: sfqy, kck_kcmc: kcmc, kck_kcbh: kcbh, fid: fid};
        insTb.reload({where: field, page: {curr: 1}});
    })
    $(".layui-layer-btn0").click(function () {
        let selectedData = table.checkStatus('jxqTable').data[0];
        window.parent.postMessage({
            "type": "setFieldValue",
            "data": [
                {
                    "alias": "kcmc",
                    "val": [selectedData.kck_kcmc],
                    "compt": "editinput"
                },
                {
                    "alias": "kcid",
                    "val": [{val: selectedData.kck_kcbh}],
                    "compt": "selectbox"
                },
                {
                    "alias": "zyksz_kkys",
                    "val": [{val: selectedData.kck_kkyx}],
                    "compt": "selectbox"
                },
                {
                    "alias": "xf",
                    "val": [selectedData.kck_xf],
                    "compt": "numberinput"
                },
                {
                    "alias": "zhouxs",
                    "val": [selectedData.kck_mzxs],
                    "compt": "numberinput"
                },
                {
                    "alias": "zyksz_sfcsjhj",
                    "val": [{val: selectedData.kck_sfcsjhj}],
                    "compt": "selectbox"
                },
                {
                    "alias": "sjzs",
                    "val": [selectedData.kck_sjzs],
                    "compt": "numberinput"
                },
                {
                    "alias": "shijianxs",
                    "val": [selectedData.kck_sjxs],
                    "compt": "numberinput"
                },
                {
                    "alias": "llxs",
                    "val": [selectedData.kck_llxs],
                    "compt": "numberinput"
                },
                {
                    "alias": "syxs",
                    "val": [selectedData.kck_syxs],
                    "compt": "numberinput"
                },
                {
                    "alias": "shangjxs",
                    "val": [selectedData.kck_shangjxs],
                    "compt": "numberinput"
                },
                {
                    "alias": "qtxs",
                    "val": [selectedData.kck_qtxs],
                    "compt": "numberinput"
                },
                {
                    "alias": "kcxz",
                    "val": [{val: selectedData.kck_kcxz}],
                    "compt": "selectbox"
                },
                {
                    "alias": "chooseCompulsory",
                    "val": [{val: selectedData.kck_kcsx}],
                    "compt": "selectbox"
                },
                {
                    "alias": "zyksz_ksxs",
                    "val": [{val: selectedData.kck_ksxs}],
                    "compt": "selectbox"
                },
                {
                    "alias": "81",
                    "val": [{val: selectedData.kck_jyz}],
                    "compt": "selectbox"
                },
                {
                    "alias": "zyksz_ssxq",
                    "val": [{val: selectedData.kck_ssxq}],
                    "compt": "selectbox"
                },
                {
                    "alias": "zyksz_kcshx",
                    "val": [{val: selectedData.kck_kcshx}],
                    "compt": "selectbox"
                },
                {
                    "alias": "zyksz_kcfl",
                    "val": [{val: selectedData.kck_kcfl}],
                    "compt": "selectbox"
                },
                {
                    "alias": "zyksz_xklb",
                    "val": [{val: selectedData.kck_xklb}],
                    "compt": "selectbox"
                },
                {
                    "alias": "zyksz_sfzyhxk",
                    "val": [{val: selectedData.kck_sfzyhxk}],
                    "compt": "selectbox"
                },
                {
                    "alias": "zyksz_sfxskc",
                    "val": [{val: selectedData.kck_sfxskc}],
                    "compt": "selectbox"
                },
                {
                    "alias": "zyksz_xskcwz",
                    "val": [selectedData.kck_xskcwz],
                    "compt": "editinput"
                },
                {
                    "alias": "zyksz_sfszsf",
                    "val": [{val: selectedData.kck_sfszsf}],
                    "compt": "selectbox"
                },
                {
                    "alias": "zyksz_kczy",
                    "val": [{val: selectedData.kck_kczy}],
                    "compt": "selectbox"
                },
                {
                    "alias": "zyksz_xnfzks",
                    "val": [selectedData.kck_xnfzks],
                    "compt": "numberinput"
                },
                {
                    "alias": "zyksz_xnfzxm",
                    "val": [selectedData.kck_xnfzxm],
                    "compt": "editinput"
                },
                {
                    "alias": "zyksz_syxm",
                    "val": [selectedData.kck_syxm],
                    "compt": "editinput"
                },
                {
                    "alias": "zyksz_sxxm",
                    "val": [selectedData.kck_sxxm],
                    "compt": "editinput"
                },
                {
                    "alias": "zyksz_sxixm",
                    "val": [selectedData.kck_sxixm],
                    "compt": "editinput"
                }
            ]
        }, '*');
        window.parent.postMessage({type: "close"}, '*');

    });
    $(".popSearch_clear").click(function () {
        location.reload();
    })
    $(".layui-layer-btn1").click(function () {
        window.parent.postMessage({type: "close"}, '*');
    })

    var table = "", insTb = "", kck_kcbh = "", kck_kcmc = "", kkyx = "";
    layui.use('table', function () {
        table = layui.table;
        insTb = table.render({
            elem: '#jxqTable',
            url: '../processData/getCourseLibData',
            where: {deptId: fid},
            page: true,
            height: 340,
            cols: [
                [
                    {type: 'radio', field: 'id', fixed: 'left', width: 50},
                    {field: 'kck_kcbh', title: '课程编码', minWidth: 200},
                    {field: 'kck_kcmc', title: '课程名称', minWidth: 250},
                    {field: 'kck_kkyx', title: '开课院系', minWidth: 220},
                    {field: 'kck_kc lb', title: '课程类别', minWidth: 150},
                    {field: 'kck_kcgs', title: '课程归属', minWidth: 200}
                ]
            ],
            done: function () {
                scrollBox("#popScroll");
            }
        });

        table.on('row(jxqTable)', function (obj) {
            //obj.tr.addClass('tr_bj_color').siblings().removeClass('tr_bj_color');
            kck_kcbh = obj.data.kck_kcbh;
            kck_kcmc = obj.data.kck_kcmc;
            kkyx = obj.data.kck_kkyx;
        });
    });

</script>
</body>
</html>