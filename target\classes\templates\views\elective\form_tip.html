<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据弹窗</title>
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/global.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/tip.css?v=1'">


</head>

<body>

<!--    <div style="width:200px;text-align: center;margin:420px auto 20px; cursor: pointer;font-size:20px;" id="tips1">数据处理弹窗1</div>-->
<!--    <div style="width:200px;text-align: center;margin:20px auto; cursor: pointer;font-size:20px;" id="tips2">数据处理弹窗2</div>-->
<!--    <div style="width:200px;text-align: center;margin:20px auto; cursor: pointer;font-size:20px;" id="tips3">数据处理弹窗3</div>-->
<!--    <div style="width:200px;text-align: center;margin:20px auto; cursor: pointer;font-size:20px;" id="tips4">数据处理弹窗4</div>-->


</body>

<script th:src="${_CPR_}+'/elective/js/jquery-3.3.1.min.js'"></script>
<script th:src="${_CPR_}+'/elective/js/tips.js?v=1'"></script>
<script th:inline="javascript">
    var path = [[${path}]];
    // var formTopBtnBO = [[${formTopBtnBO}]];
    var queryId = [[${queryId}]];
    var inr;
    myConfirm({
        title: '',
        message: '<div class="suc-box"><span>处理中...推送成功<i class="suc">0</i>条数据，</span></div><p>请勿关闭此页面</p>',
        type: 'loading',
        isShowBtn: false,
        isShowBtnCancel: false,
        confirmCallback: function () {
        },
        cancelCallback: function () {
        }
    });
    $.ajax({
        type: "get",
        url: path,
        success: function (data) {
            inr = setInterval(function () {
                loading();
            }, 1000);
        }
    });

    function loading() {
        $.ajax({
            type: "get",
            url: "/elective/task/formTopBtn/tip/progress",
            data: {key: queryId},
            dataType: 'json',
            success: function (data) {
                if (data.state >= 0) {
                    $(".jw-dialog").remove();
                    if (data.failNum > 0) {
                        myConfirm({
                            title: '',
                            message: '<div class="warn-box">处理中...推送成功<i class="suc">' + data.successNum + '</i>条数据，失败<i class="error">' + data.failNum + '</i>条数据，</div><p>请勿关闭此页面</p>',
                            type: 'loading',
                            isShowBtn: false,
                            isShowBtnCancel: false,
                            confirmCallback: function () {
                            },
                            cancelCallback: function () {
                            }
                        });
                    } else {
                        myConfirm({
                            title: '',
                            message: '<div class="suc-box"><span>处理中...推送成功<i class="suc">' + data.successNum + '</i>条数据，</span></div><p>请勿关闭此页面</p>',
                            type: 'loading',
                            isShowBtn: false,
                            isShowBtnCancel: false,
                            confirmCallback: function () {
                            },
                            cancelCallback: function () {
                            }
                        });
                    }
                    // if (d.successNum)
                    if (data.state == 1) {
                        $(".jw-dialog").remove();
                        clearInterval(inr);
                        if (data.failNum > 0) {
                            myConfirm({
                                title: '',
                                message: '<p>处理完成，推送成功 <i class="suc">' + data.successNum + '</i> 条数据，失败 <i class="error">' + data.failNum + '</i> 条数据，</p><div class="inform"><div class="inform-con"><div class="name">推送失败原因：</div><div class="text">' + data.msg + '</div></div></div>',
                                type: 'warning',
                                width: '672px',
                                isShowBtn: true,
                                isShowBtnCancel: false,
                                confirmCallback: function () {
                                    window.parent.postMessage(JSON.stringify({action: 1}), "*");
                                },
                                cancelCallback: function () {
                                }
                            });
                        } else {
                            myConfirm({
                                title: '',
                                message: '<p>处理完成，推送成功 <i class="suc">' + data.successNum + '</i> 条数据</p>',
                                type: 'success',
                                isShowBtn: true,
                                isShowBtnCancel: false,
                                confirmCallback: function () {
                                    window.parent.postMessage(JSON.stringify({action: 1}), "*");
                                },
                                cancelCallback: function () {
                                }
                            });
                        }
                    }
                } else {
                    //异常
                    clearInterval(inr);
                }
            }
        });
    }

</script>

</html>