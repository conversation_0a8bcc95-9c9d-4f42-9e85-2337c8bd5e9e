<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>教学计划</title>
    <link rel="stylesheet" th:href="@{/plugin/layui/css/layui.css}">
    <script th:src="@{/plugin/layui/layui.js}"></script>
</head>
<body>
    <form class="layui-form" style="width: 500px;height: 300px;margin: 40px 40px;">
        <div class="layui-form-item">
            <label class="layui-form-label">开课学期</label>
            <div class="layui-input-block">
                <select name="semester" lay-verify="required">
                    <option value=""></option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">年级</label>
            <div class="layui-input-block">
                <select name="grade" lay-verify="required">
                    <option value=""></option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">专业</label>
            <div class="layui-input-block">
                <select name="major" lay-verify="required">
                    <option value=""></option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="btn-complate">提交</button>
                <button type="reset" class="layui-btn layui-btn-primary">取消</button>
            </div>
        </div>
    </form>
</body>
<script th:src="@{/js/jquery-1.11.3.min.js}"></script>
<script th:src="@{/js/cultivation/cultivationProcess.js?v=1}"></script>
<script th:inline="javascript">
    document.domain = "chaoxing.com";
    var formUserId = [[${formUserId}]];
    var formId = [[${formId}]];
    var fid = [[${fid}]];
    var uid = [[${uid}]];
    var formSelects = "";
    layui.use(['form','layer'], function () {
        var form = layui.form;
        var layer = layui.layer;
        form.on("select(major)", function (data) {
            var dataVal = data.value;
            $("select[name='level']").find("option[value="+dataVal.split(",")[1]+"]").attr("selected",true);
            form.render('select');
        });
        //监听提交
        form.on('submit(btn-complate)', function(data){
            var semester = data.field.semester;
            var grade = data.field.grade;
            var level = data.field.level;
            var major = data.field.major.split(",")[0];
            let loading = layer.load(1);
            $.post("/api/pygc/teachPlanCopy", {
                formUserId:formUserId,
                formId:formId,
                fid:fid,
                uid:uid,
                semester:semester,
                grade:grade,
                level:level,
                major:major
            }, function(result){
                if(result.success){
                    window.parent.postMessage(JSON.stringify({action:1}),"*");
                }else {
                    layer.msg(result.message, {icon: 2, time: 2000});
                }
                layer.close(loading);
            }, "json");
            return false;
        })
    })
    $(".layui-btn-primary").click(function (){
        window.parent.postMessage(JSON.stringify({action:1}),"*");
    })
</script>
</html>