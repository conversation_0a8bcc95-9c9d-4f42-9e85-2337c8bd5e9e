<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>生成留级名单提示页面</title>
        <link rel="stylesheet" th:href="@{~/css/creditManage/global.css}">
        <link rel="stylesheet" th:href="@{~/plugin/layui/css/layui.css}">
        <link rel="stylesheet" th:href="@{~/css/creditManage/common.css}">
        <link rel="stylesheet" th:href="@{~/css/creditManage/reset.css}">
        <link rel="stylesheet" th:href="@{~/css/creditManage/scoreRule.css}">
        <link rel="stylesheet" th:href="@{~/css/creditManage/scoreSet.css}">
    </head>
    <body>

    </body>
    <script th:src="@{~/js/jquery-3.3.1.min.js}"></script>
    <script th:src="@{~/js/creditManage/common.js}"></script>
    <script th:src="@{~/plugin/layui/layui.js}"></script>
    <script th:src="@{~/js/my.util.js}"></script>
    <script>
        // document.domain = "chaoxing.com";
        var vo = {
            "formId": [[${vo.formId}]],
            "fid": [[${vo.fid}]],
            "uid": [[${vo.uid}]],
            "roleid": [[${vo.roleid}]],
            "queryId": "[[${vo.queryId}]]",
            "selectTotal": [[${vo.selectTotal}]]
        };

        // 初始化页面
        layui.use(['jquery', 'laydate', "form"], function () {
            var form = layui.form;
            var $ = layui.jquery;

            // 先检查是否生成过留级名单
            $.ajax({
                type: "GET",
                url: "/api/new/credit/stay/grade/checked/is/generated",
                data: vo,
                dataType: 'json',
                success: function (result) {
                    if (result && result.code === 200) {
                        var msg = "确认生成留级名单吗？";
                        if (result.data) {
                            msg = '本学期的留级名单已生成，是否重新生成？';
                        }
                        U.confirm({
                            title: "提示",
                            msg: msg,
                            sureBtnTxt: '确定',
                            cancelBtnTxt: '取消',
                            sure: function () {
                                // 点击 确定 按钮时执行的方法
                                generateStayGradeList();
                            },
                            cancel: function () {
                                U.closePop();
                            }
                        });
                    }
                }
            });

            /**
             * 生成留级名单数据
             */
            function generateStayGradeList() {
                $.ajax({
                    type: "post",
                    url: "/api/new/credit/stay/grade/generate/list",
                    data: vo,
                    dataType: 'json',
                    success: function (result) {
                        if (result) {
                            if (result.code === 200) {
                                layer.msg(result.msg, {icon: 1, time: 3000});
                            } else {
                                layer.msg(result.msg, {icon: 2, time: 3000});
                            }
                            setTimeout(U.closePop, 3000);
                        }
                    }
                });
            }
        });
    </script>
</html>