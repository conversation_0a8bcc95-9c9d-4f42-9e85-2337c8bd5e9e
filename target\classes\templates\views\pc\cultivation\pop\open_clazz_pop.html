<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>行政班单班开班</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/global.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/plugin/layui/css/layui.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/slideCommon.css'}"/>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/teachPlan.css'}"/>
</head>

<body>
<div class="marker"></div>
<div class="dialog" id="offerCourseDialog" style="display: block;">
    <div class="dialog-con">
        <img th:src="${_CPR_+_VR_+'/images/cultivation/tips-icon1.png'}" alt="">
        <p th:if="${type == 1}">请确认所选教学计划，各专业下每个行政班分开为单个教学班进行教学？</p>
        <p th:if="${type == 2}">请确认所选教学计划，各专业下全部行政班按照专业合并为一个教学班进行教学？</p>
    </div>
    <div class="dialog-footer">
        <button class="btn-cancel">取消</button>
        <button class="btn-sure">确定</button>
    </div>
</div>
</body>
<script th:src="${_CPR_+_VR_+'/js/jquery-3.3.1.min.js'}"></script>
<script th:inline="javascript">
    const formId = [[${formTopBtnVO.formId}]];
    const fid = [[${formTopBtnVO.fid}]];
    const uid = [[${formTopBtnVO.uid}]];
    const queryId = [[${formTopBtnVO.queryId}]];
    const type = [[${type}]];
    const _VR_ = [[${_VR_}]];
    const selectTotal = [[${formTopBtnVO.selectTotal}]];
    $("#btnRecord").click(function () {
        location.href = _VR_ + "/sync/record/list.html?tableName=jxjhkc&fid=" + fid;
    })
    $(".btn-cancel").click(function () {
        window.parent.postMessage(JSON.stringify({action: 1}), "*");
    })
    $(".btn-sure").click(function () {
        location.href = _VR_ + "/api/form-btn/gm/outpost.popup?code=2T10002&type=" + type + "&formId=" + formId + "&fid=" + fid + "&queryId=" + queryId + "&uid=" + uid + "&selectTotal=" + selectTotal;
    })
</script>
</html>