<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基础设置</title>
    <link rel="stylesheet" th:href="@{~/css/creditManage/global.css}">
    <link rel="stylesheet" th:href="@{~/plugin/layui/css/layui.css}">
    <link rel="stylesheet" th:href="@{~/css/creditManage/common.css}">
    <link rel="stylesheet" th:href="@{~/css/creditManage/reset.css}">
    <link rel="stylesheet" th:href="@{~/css/creditManage/scoreRule.css}">
    <link rel="stylesheet" th:href="@{~/css/creditManage/scoreSet.css}">
    <style>
        /* 产品样式优化 */
        .top {
            /*border-bottom: 1px solid #E8EBF1;*/
            margin-bottom: 20px;
        }
        .con-title {
            font-size: 16px;
            color: #1d2129;
            margin: 0;
            font-weight: 700;
        }
        .btn {
            position: absolute;
            top: 12px;
            right: 30px;
            width: 116px;
            height: 36px;
            text-align: center;
            line-height: 36px;
            cursor: pointer;
            font-size: 14px;
            color: #FFFFFF;
            background: #4D88FF;
            box-shadow: 0 0 10px #4D88FF;
            border-radius: 4px;
        }
        .container {
            padding: 0 25px;
            margin: 0;
        }
        .top:after {
            content: "";
            width: 105%;
            height: 1px;
            background: #e8ebf1;
            border-radius: 2px;
            position: absolute;
            left: -25px;
            bottom: -2px;
        }
        .dialog-apply {
            overflow: hidden;
            position: unset;
            transform: translate(0, 0);
            margin: 0;
            background-color: #ffffff;
            min-height: calc(100vh);
            border-radius: 6px;
        }
    </style>
</head>

<body>
<div class="dialog dialog-apply">
    <div class="container">
        <div class="top">
            <div class="tab">
                <ul>
                    <li class="cur con-title">基础设置</li>
                </ul>
            </div>
            <div class="btn" lay-submit lay-filter="saveSetting">保存设置</div>
        </div>
        <form class="layui-form set-form" id="formCon"   lay-filter="formCon" action="">
            <div class="layui-form-item">
                <label class="layui-form-label" style="width: 119px;"><em>*</em>学年学期</label>
                <div class="layui-input-block">
                    <input type="text" name="xnxq" disabled required value=""
                        style="color: #86909C;cursor:not-allowed" placeholder="请输入" autocomplete="off"
                        class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label" style="width: 119px;"><em>*</em>参加学分互认年级</label>
                <div class="layui-input-block">
                    <div class="j-search-con multiple-box">
                        <input type="text" placeholder="请选择" readonly class="schoolSel" name="grade">
                        <span class="j-arrow"></span>
                        <div class="j-select-year ">
                            <div class="allSelect">
                                全选
                            </div>
                            <ul id="grade">

                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label" style="width: 119px;"><em>*</em>学分互认申请时间</label>
                <div class="layui-input-block">
                    <input type="text" name="time" id="selTime" required placeholder="请输入"
                        autocomplete="off" class="layui-input" style="width: 300px;">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label" style="width: 119px;"><em>*</em>学生是否可申请</label>
<!--                <div class="radio">-->
<!--                    <div class="limit-switch">-->
<!--                        <input type="checkbox" name="stuApply" lay-skin="switch" checked>-->
<!--                        <em class="tit">默认为班主任申请本班学生学分互认；开启后，学生可申请本人学分互认</em>-->
<!--                    </div>-->
<!--                </div>-->
                <div class="layui-input-block stu-apply">
                    <input type="checkbox" name="stuApply" lay-skin="switch" checked>
                    <span class="tips">默认为班主任申请本班学生学分互认；开启后，学生可申请本人学分互认</span>
                </div>
            </div>
<!--            <div class="layui-form-item " style="display: flex;justify-content: center;">-->
<!--                <button type="submit" class="layui-btn btn-submit" lay-submit lay-filter="saveSetting">保存设置</button>-->
<!--            </div>-->
        </form>
    </div>
</div>
</body>
<script th:src="@{~/js/jquery-3.3.1.min.js}"></script>
<script th:src="@{~/plugin/layui/layui.js}"></script>
<script th:src="@{~/js/creditManage/common.js}"></script>
<script th:src="@{~/js/my.util.js}"></script>
<script th:inline="javascript">
    var fid = [[${fid}]];
    layui.use(["table", "jquery", "laydate", "form"], function () {
        var laydate = layui.laydate;
        var form = layui.form;
        // 日期时间范围
        laydate.render({
            elem: '#selTime',
            type: 'datetime',
            range: true
        });
        $(function (){
            $.get("/basic/xnxq",{fid:fid,curr:true},function (res){
                if (res.code==200){
                    $("input[name=xnxq]").val(res.data.xnxq_xnxqh)
                    $("input[name=time]").val(res.data.xnxq_xqkssj+" - "+res.data.xnxq_xqjssj)

                    getBasicSet(fid,res.data.xnxq_xnxqh)
                }else {
                    U.fail("请检查学年学期设置")
                }
            })
            $.get("/basic/grade",{fid:fid},function (res){
                if (res.code==200){
                    var data = res.data;
                    let html='';
                    for (let i = 0; i < data.length; i++) {
                        html+='<li>'+data[i].nj_njmc+'</li>'
                    }
                    $("#grade").html(html)
                }else {
                    U.fail("请检查年级设置")
                }
            })
        })

        function getBasicSet(fid,xnxq){
            $.get("/credit/mr/getBasicSet",{fid:fid, xnxq:xnxq},function (res){
                if (res.code==200){
                    var data = res.data;
                    $("input[name=grade]").val(data.grade)
                    $("input[name=time]").val(data.kssj+" - "+data.jssj)
                    var open = data.open;
                    if (open===1){
                        $("input[name=stuApply]").attr("checked","true");
                    }else {
                        $("input[name=stuApply]").removeAttr("checked");
                    }

                    var gradeData = data.grade.split(",");
                    for (let i = 0; i < gradeData.length; i++) {
                        let grade = gradeData[i];
                        $("#grade li").each((index,value) => {
                            if ($(value).html()===grade){
                                $(value).addClass("active")
                            }
                        })
                    }
                    form.render();
                }
            })
        }


        form.on('submit(saveSetting)', function (data) {
            var field = form.val('formCon');

            if (field["grade"]==undefined||field["grade"]==""){
                U.fail("请选择参与年级")
                return false;
            }

            let time=field["time"]
            if (time==undefined||time==""){
                U.fail("请选择申请时间")
                return false;
            }
            let dataJson={
                fid:fid,
                xnxq:field["xnxq"],
                grade:field["grade"],
                kssj:new Date(time.split(" - ")[0]),
                jssj:new Date(time.split(" - ")[1]),
                open:field["stuApply"]==="on"?1:0,
            }
            $.get("/credit/mr/saveBasicSet",dataJson,function (res){
                if (res.code==200){
                    U.success("保存成功")
                }else {
                    U.fail("保存失败");
                }
            })
        });
    })
</script>

</html>