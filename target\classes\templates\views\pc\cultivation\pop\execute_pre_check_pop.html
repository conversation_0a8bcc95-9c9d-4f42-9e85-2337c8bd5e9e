<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>开始统计</title>
    <style>
        * {
            margin: 0;
            padding: 0;
        }

        .marsker {
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            position: fixed;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
        }

        .dialog {
            width: 460px;
            padding: 40px 0;
            box-sizing: border-box;
            background-color: #fff;
            border-radius: 10px;
            position: fixed;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
        }

        .dialog img {
            display: block;
            margin: 0 auto 24px;
            border-radius: 50%;
        }

        .dialog p {
            font-size: 16px;
            text-align: center;
            color: #1D2129;
        }

        .loading img {
            animation: loading 2.3s linear infinite;
        }

        .success img {
            width: 60px;
            height: 60px;
        }

        .btnSure {
            width: 88px;
            height: 36px;
            border-radius: 18px;
            background: #4D88FF;
            box-shadow: 0px 0px 10px 0px rgba(0, 108, 226, 0.40);
            text-align: center;
            line-height: 36px;
            font-size: 14px;
            color: #fff;
            margin: 22px auto 0;
            cursor: pointer;
        }

        @keyframes loading {
            0% {
                transform: rotate(0);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        .delete_pop {
            z-index: 29;
            position: fixed;
            width: 570px;
            min-height: 260px;
            top: 50%;
            left: 50%;
            -webkit-transform: translate(-50%, -50%);
            -moz-transform: translate(-50%, -50%);
            -ms-transform: translate(-50%, -50%);
            -o-transform: translate(-50%, -50%);
            background: #fff;
            box-shadow: 0px 0px 30px 0px rgba(21, 23, 28, 0.2);
            border-radius: 10px;
            overflow: hidden;
        }

        .delete_pop .del_top {
            position: relative;
            line-height: 56px;
            padding: 0 30px;
            font-size: 18px;
            color: #242933;
            border-bottom: 1px solid #F2F2F2;
        }

        .delete_pop .del_top em {
            font-style: normal;
            font-size: 12px;
            color: #A8ACB3;
            margin-left: 16px;
        }

        .delete_pop .del_top em label {
            color: #4C88FF;
        }

        .delete_pop .del_top .del_close:hover {
            opacity: 0.7;
        }

        .delete_pop .del_cont {
            min-height: 28px;
            line-height: 28px;
            padding: 77px 30px;
            font-size: 16px;
            color: #656A73;
        }

        .delete_pop .del_btm {
            position: relative;
            line-height: 70px;
            padding: 0 30px;
            text-align: right;
            border-top: 1px solid #F2F2F2;
        }

        .delete_pop .del_btm span {
            display: inline-block;
            width: 90px;
            height: 34px;
            line-height: 34px;
            font-size: 14px;
            text-align: center;
            border-radius: 20px;
            cursor: pointer;
        }

        .delete_pop .del_btm .del_cal {
            border: 1px solid #99BBFF;
            color: #4C88FF;
        }

        .delete_pop .del_btm .del_cal:hover {
            background: #F5F8FF;
        }

        .delete_pop .del_btm .imp_err_btn {
            padding: 0 10px;
        }

        .delete_pop .del_btm .del_sent {
            margin-left: 30px;
            border: 1px solid #4C88FF;
            background: #4C88FF;
            color: #fff;
            box-shadow: 0px 2px 8px 0px rgba(39, 111, 255, 0.3);
        }

        .delete_pop .del_btm .del_sent:hover {
            opacity: 0.7;
        }

        .delete_pop .del_btm .noClick {
            color: #898989;
            border: 1px solid #F1F1F2;
            background: #F1F1F2;
            box-shadow: none;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
<div class="dialog loading">
    <img src="/images/cultivation/loading.png" alt="">
    <p>数据处理中</p>
    <p class="light">（关闭弹窗不影响更新进程）</p>
</div>
<div class="dialog success" style="display: none;">
    <img src="/images/cultivation/success-icon.png" alt="">
    <p>执行完毕</p>
</div>
<div class="dialog fail" style="display: none;">
    <img src="/images/cultivation/tips.png" alt="">
    <p style="text-align:left;"></p>
</div>
<div class="delete_pop" style="display: none;">
    <div class="del_cont">审核通过的数据重新审核不通过后会同步删除已生成的教学计划和开课信息，请确认是否执行？</div>
    <div class="del_btm">
        <span class="del_cal">取消</span> <span class="del_sent">确定</span>
    </div>
</div>
</body>
<script th:src="@{${_CPR_}+'/js/jquery-3.3.1.min.js'}"></script>
<script>
    $(document).ready(function () {
        executePreCheck();
        window.parent.postMessage(JSON.stringify({"command": "urlPopWndClose", "type": "register"}), '*');
        $(window).on('message', function (e) {
            const message = e.originalEvent.data; // 获取 postMessage 传递的数据
            const data = JSON.parse(message);
            if (data.command === "urlPopWndClose" && data.appName === "officeApp") {
                window.parent.postMessage(JSON.stringify({action: 1}), "*");
            }
        });
    })

    $(".del_cal").click(function () {
        window.parent.postMessage(JSON.stringify({action: 1}), "*");
    })

    $(".del_sent").click(function () {
        let formAlias = $(".delete_pop").attr("formAlias");
        let formUserIds = $(".delete_pop").attr("formUserIds");
        let fieldAlias = $(".delete_pop").attr("fieldAlias");
        let url = $(".delete_pop").attr("enquireUrl");
        let status = $(".delete_pop").attr("status");
        $.post(url, {
            fid: "[[${formTopBtnBO.fid}]]",
            uid: "[[${formTopBtnBO.uid}]]",
            queryId: "[[${formTopBtnBO.queryId}]]",
            formId: "[[${formTopBtnBO.formId}]]",
            status: status,
            formAlias: formAlias,
            formUserIds: formUserIds,
            fieldAlias: fieldAlias,
            fieldVal: "强制删除,未通过"
        }, function (res) {
            window.parent.postMessage(JSON.stringify({action: 1}), "*");
        }, "json");
    })

    function executePreCheck() {
        let url = "[[${executeUrl}]]".replace("&amp;", "&");
        $.post(url, {
            fid: "[[${formTopBtnBO.fid}]]",
            uid: "[[${formTopBtnBO.uid}]]",
            queryId: "[[${formTopBtnBO.queryId}]]",
            formId: "[[${formTopBtnBO.formId}]]",
            roleid: "[[${formTopBtnBO.roleid}]]",
            selectTotal: "[[${formTopBtnBO.selectTotal}]]"
        }, function (res) {
            if (res.code === 200) {
                if (res.data && res.data.formUserIds && res.data.formUserIds.length > 0) {
                    $(".delete_pop").show().attr("formAlias", res.data.formAlias).attr("formUserIds", res.data.formUserIds)
                        .attr("fieldAlias", res.data.fieldAlias).attr("enquireUrl", res.data.enquireUrl).attr("status", res.data.status);
                    $(".delete_pop .del_cont").text(res.msg)
                } else {
                    $(".success").show().find("p").text(res.msg);
                }
            } else if (res.code === 500) {
                $(".fail").show().find("p").text(res.msg);
            }
        }, "json");
    }
</script>
</html>