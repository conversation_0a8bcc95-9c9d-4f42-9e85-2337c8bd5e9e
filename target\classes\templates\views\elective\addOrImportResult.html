<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>选课结果数据</title>
    <th:block th:include="common :: header('选课结果数据')"/>
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/global.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/layui/css/layui.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/reset.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/resultData.css?v=1'">
    <!-- 2025.4.9 修改 -->
    <style>
        .data-course-con {
            position: relative;
        }

        #resultData .dialog-con1 .add-course-btn {
            position: absolute;
            top: 0;
            right: 60px;
            margin-bottom: 0;
        }


        .layui-radio-disbaled > i {
            color: #e2e2e2 !important;
        }

        .radio-disabled {
            background: url(/elective/rollcall/images/radio-cur-disabled-bg.png) no-repeat center;
            background-size: 20px;
            pointer-events: none;
        }

        .radio-disabled .layui-form-radio {
            /* opacity: 0;
            pointer-events: none; */
            /* background-color: red; */
            display: none;
        }
    </style>
</head>
<body>
<div class="marker"></div>
<div class="dialog" id="resultData">
    <div class="dialog-title">
        <h3>添加/导入选课结果数据</h3><span class="pu-close" style="display:none;"></span>
    </div>
    <div class="dialog-con1">
        <div class="from-wrap">
            <ul class="data-nav">
                <li class="active">添加选课结果数据</li>
                <li>导入选课结果数据</li>
            </ul>
            <div class="data-box-wrap">
                <div class="data-box data-course" id="addResult-data-course">
                    <div class="data-course-con">
                        <form action="" class="layui-form result-form">
                            <div class="layui-form-item">
                                <label class="layui-form-label">选课计划</label>
                                <div class="layui-input-block">
                                    <div class="j-search-con single-box" id="addResultDiv">
                                        <input type="text" name="teacherName" placeholder="请选择" readonly=""
                                               class="schoolSel">
                                        <span class="j-arrow"></span>
                                        <div class="j-select-year">
                                            <div class="search">
                                                <input type="text" placeholder="搜索">
                                                <span></span>
                                            </div>
                                            <ul name="teacherName" id="jhul">
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <!-- 2025.3.31 新增 -->
                                <div class="elective-list">
                                    <div class="el-item">
                                        <div class="course">
                                            <div class="name">选修课信息：</div>
                                            <div class="txt">选择选修课</div>
                                        </div>
                                        <div class="student">
                                            <div class="name">学生信息：</div>
                                            <div class="txt">
                                                <span class="sel-stu">选择学生</span>
                                                <i>共<em>8</em>人</i>
                                            </div>
                                        </div>
                                        <div class="delete"></div>
                                    </div>
                                </div>
                                <!--<label class="layui-form-label">选修课</label>
                                <div class="layui-input-block">
                                    <div class="j-search-con single-box">
                                        <input type="text" name="teacherName" placeholder="请选择" readonly=""
                                            class="schoolSel">
                                        <span class="j-arrow"></span>
                                        <div class="j-select-year">
                                            <div class="search">
                                                <input type="text" placeholder="搜索">
                                                <span></span>
                                            </div>
                                            <ul name="teacherName" class="xxkul">
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="sel-stu">选择学生</div>
                                </div>-->
                            </div>
                        </form>
                        <div class="add-course-btn" id="addCourseBtn"><img th:src="${_CPR_}+'/elective/images/add.png'"
                                                                           alt="">添加选修课
                        </div>
                    </div>
                    <div class="failTable">
                        <div class="tips">除以下数据外选课结果已添加成功</div>
                        <table class="layui-hide errorTable" id="errorTable" lay-filter="errorTable">
                        </table>
                    </div>
                    <div class="addTips tip-success">添加成功</div>
                </div>
                <div class="data-box data-upload">
                    <div class="data-upload-wrap">
                        <div class="tips" style="margin: 20px 0; ">请下载导入模板，按格式修改后导入 <a
                                href="/templateFile/addresult.xls">下载导入模板</a><a href="javascript:downFail();"
                                                                                id="downFailButton"
                                                                                style="display:none;">下载错误数据</a></div>
                        <div class="layui-upload-drag" style="display: block;" id="ID-upload-demo-drag">
                            <img th:src="${_CPR_}+'/elective/images/add-icon1.png'" alt="">
                            <div class="intro">点击或拖拽文件到此处上传</div>
                            <div class="intro1">只能上传EXCEL文件</div>
                            <div class="layui-hide" id="uploadExcel">
                                <img th:src="${_CPR_}+'/elective/images/excel.png'"><span></span>
                            </div>
                        </div>
                    </div>
                    <div class="addTips tip-success">添加成功</div>
                    <div class="addTips tip-fail" style="padding:0;top: 60%;display: none"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="dialog-btn">
        <button class="pu-cancel" id="stuCancel" style="display:none;">取消</button>
        <button class="pu-sure"
                id="stuSure">确定
        </button>
    </div>
</div>
<!-- 添加选课学生 -->
<div id="selStu" class="dialog">
    <div class="dialog-title">
        <h3>选择学生</h3><span class="pu-close"></span>
    </div>
    <div class="dialog-con">
        <div class="z-search">
            <form action="" class="layui-form">
                <div class="layui-form-item">
                    <label class="layui-form-label">所在校区 </label>
                    <div class="layui-input-block">
                        <div class="j-search-con multiple-box">
                            <input type="text" name="school" placeholder="请选择" readonly="" class="schoolSel" id="xqip">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul name="school" id="xqDiv">
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">所属年级</label>
                    <div class="layui-input-block">
                        <div class="j-search-con multiple-box">
                            <input type="text" name="grade" placeholder="请选择" readonly="" class="schoolSel" id="njip">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul name="grade" id="njDiv">
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">所属院系 </label>
                    <div class="layui-input-block">
                        <div class="j-search-con multiple-box">
                            <input type="text" name="depth" placeholder="请选择" readonly="" class="schoolSel" id="yxip">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul name="depth" id="yxDiv">
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">所属专业 </label>
                    <div class="layui-input-block">
                        <div class="j-search-con multiple-box">
                            <input type="text" name="major" placeholder="请选择" readonly="" class="schoolSel" id="zyip">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul name="major" id="zyDiv">
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"> 所属班级 </label>
                    <div class="layui-input-block">
                        <div class="j-search-con multiple-box">
                            <input type="text" name="classes" placeholder="请选择" readonly="" class="schoolSel" id="bjip">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul name="classes" id="bjDiv">
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <button class="z-btn">查询</button>
                <div class="clear"></div>
            </form>
        </div>
        <div class="z-tab-search">
            <ul>
                <li class="active">按学生显示</li>
                <li>按班级显示</li>
            </ul>
            <input type="text" placeholder="请输入" id="xmip">
            <img th:src="${_CPR_}+'/elective/images/search-icon.png'" alt="">
        </div>
        <div class="z-table z-table-sel">
            <div class="tabBox table1">
                <table class="layui-hide mtTable2" id="mtTable2" lay-filter="mtTable2">
                </table>
                <div class="z-check">
                    <span class="check" id="checkAll"></span>选择全部数据
                </div>
                <div class="z-has-check">已选<span class="num">0</span>个</div>
            </div>
            <div class="tabBox table2" style="display: none;">
                <table class="layui-hide mtTable3" id="mtTable3" lay-filter="mtTable3">
                </table>
                <div class="z-check">
                    <span class="check" id="checkAllClass"></span>选择全部数据
                </div>
                <div class="z-has-check">已选<span class="num">0</span>个</div>
            </div>
        </div>
    </div>
    <div class="dialog-btn">
        <button class="pu-cancel">取消</button>
        <button class="pu-sure" id="stuSureBtn">确定</button>
    </div>
</div>
<!-- 添加选课课程 2025.3.31 新增 -->
<div id="selCourse" class="dialog" style="display: none;width: 1098px;">
    <div class="dialog-title">
        <h3>选择课程</h3><span class="pu-close"></span>
    </div>
    <div class="dialog-con">

        <div class="z-search">
            <form action="" class="layui-form">
                <div class="layui-form-item">
                    <label class="layui-form-label">开课年级</label>
                    <div class="layui-input-block">
                        <div class="j-search-con multiple-box">
                            <input type="text" name="grade" placeholder="请选择" readonly="" class="schoolSel"
                                   id="njCourse">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul name="grade" id="njDiv1">
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">开课院系 </label>
                    <div class="layui-input-block">
                        <div class="j-search-con multiple-box">
                            <input type="text" name="depth" placeholder="请选择" readonly="" class="schoolSel"
                                   id="yxCourse">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul name="depth" id="yxDiv1">
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">开课专业 </label>
                    <div class="layui-input-block">
                        <div class="j-search-con multiple-box">
                            <input type="text" name="major" placeholder="请选择" readonly="" class="schoolSel"
                                   id="zyCourse">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul name="major" id="zyDiv1">
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">开设课程 </label>
                    <div class="layui-input-block">
                        <div class="j-search-con multiple-box">
                            <input type="text" name="course" placeholder="请选择" readonly="" class="schoolSel"
                                   id="kskcCourse">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul name="course" id="kskcDiv1">
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">开课校区 </label>
                    <div class="layui-input-block">
                        <div class="j-search-con multiple-box">
                            <input type="text" name="school" placeholder="请选择" readonly="" class="schoolSel"
                                   id="xqCourse">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul name="school" id="xqDiv1">
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">课程类型 </label>
                    <div class="layui-input-block">
                        <div class="j-search-con multiple-box">
                            <input type="text" name="courseType" placeholder="请选择" readonly="" class="schoolSel"
                                   id="lxCourse">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul name="courseType" id="kclxDiv1">
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <button class="z-btn">查询</button>
                <div class="clear"></div>
            </form>
        </div>
        <div class="z-tab-search">
            <input type="text" placeholder="请输入" id="keyWordCourse">
            <img th:src="${_CPR_}+'/elective/images/search-icon.png'" alt="">
        </div>
        <div class="z-table z-table-sel">
            <div class="tabBox table1">
                <table class="layui-hide mtTable2" id="mtTable4" lay-filter="mtTable4">
                </table>
            </div>
        </div>
    </div>
    <div class="dialog-btn">
        <button class="pu-cancel">取消</button>
        <button class="pu-sure" id="stuSureBtn1">确定</button>
    </div>
</div>
<div class="loading">
    <img th:src="${_CPR_}+'/elective/images/loading.png'" alt="">
</div>
</body>
<script type="text/javascript">
    var fid = [[${fid}]];
    var array = [];
    var json = {};
    var thisJson = {};
    var opnum = "";
    var taskbdid;
    $.ajax({
        type: "POST",
        url: "/elective/course/rule/getHistoryPlan",
        data: {fid: fid},
        dataType: 'json',
        success: function (result) {
            var html = "";
            if (result) {
                for (var i = 0; i < result.list.length; i++) {
                    html += "<li data-id=\"" + result.list[i].bdid + "\" >" + result.list[i].xkjhbJhmc + "</li>";
                }
            }
            $("#jhul").html(html);
        }
    });

    function addResultGetNj() {
        $.ajax({
            type: "POST",
            url: "/elective/getNj",
            data: {fid: fid},
            dataType: 'json',
            success: function (result) {
                var html = "";
                if (result) {
                    for (var i = 0; i < result.data.length; i++) {
                        var name = result.data[i].name;
                        var id = result.data[i].id;
                        html += "<li data-id=\"" + id + "\">" + name + "</li>";
                    }
                }
                $("#njDiv").html(html);
                $("#njDiv1").html(html);
            }
        });
    }

    function addResultGetYx() {
        $.ajax({
            type: "POST",
            url: "/elective/getYX",
            data: {fid: fid},
            dataType: 'json',
            success: function (result) {
                var html = "";
                if (result) {
                    for (var i = 0; i < result.data.length; i++) {
                        var name = result.data[i].name;
                        var id = result.data[i].id;
                        html += "<li data-id=\"" + id + "\">" + name + "</li>";
                    }
                }
                $("#yxDiv").html(html);
                $("#yxDiv1").html(html);
            }
        });
    }

    function addResultGetXq() {
        $.ajax({
            type: "POST",
            url: "/elective/getXQ",
            data: {fid: fid},
            dataType: 'json',
            success: function (result) {
                var html = "";
                if (result) {
                    for (var i = 0; i < result.data.length; i++) {
                        var name = result.data[i].name;
                        var id = result.data[i].id;
                        html += "<li data-id=\"" + id + "\">" + name + "</li>";
                    }
                }
                $("#xqDiv").html(html);
                $("#xqDiv1").html(html);
            }
        });
    }

    function addResultGetZY(yx) {
        $.ajax({
            type: "POST",
            url: "/elective/getZY",
            data: {fid: fid, addScoreYx: yx},
            dataType: 'json',
            success: function (result) {
                var html = "";
                if (result) {
                    for (var i = 0; i < result.data.length; i++) {
                        var name = result.data[i].name;
                        var id = result.data[i].id;
                        html += "<li data-id=\"" + id + "\">" + name + "</li>";
                    }
                }
                $("#zyDiv").html(html);
                $("#zyDiv1").html(html);
            }
        });
    }

    function addResultGetBJ(yx, zy) {
        $.ajax({
            type: "POST",
            url: "/elective/getBj",
            data: {fid: fid, yx: yx, zy: zy},
            dataType: 'json',
            success: function (result) {
                var html = "";
                if (result) {
                    for (var i = 0; i < result.data.length; i++) {
                        var name = result.data[i].name;
                        var id = result.data[i].id;
                        html += "<li data-id=\"" + id + "\">" + name + "</li>";
                    }
                }
                $("#bjDiv").html(html);
            }
        });
    }

    $.ajax({
        type: "POST",
        url: "/elective/getKC",
        data: {fid: fid},
        dataType: 'json',
        success: function (result) {
            var html = "";
            if (result) {
                for (var i = 0; i < result.data.length; i++) {
                    var name = result.data[i].name;
                    var id = result.data[i].id;
                    html += "<li data-id=\"" + id + "\">" + name + "</li>";
                }

            }
            $("#kskcDiv1").html(html);
        }
    });

    $.ajax({
        type: "POST",
        url: "/elective/getLX",
        data: {fid: fid},
        dataType: 'json',
        success: function (result) {
            var html = "";
            if (result) {
                for (var i = 0; i < result.data.length; i++) {
                    var name = result.data[i].name;
                    var id = result.data[i].id;
                    html += "<li data-id=\"" + id + "\">" + name + "</li>";
                }

            }
            $("#kclxDiv1").html(html);
        }
    });

    function downFail() {
        window.location = "/elective/task/downFail"
    }
</script>
<script th:src="${_CPR_}+'/elective/js/jquery-3.3.1.min.js'"></script>
<script th:src="${_CPR_}+'/elective/layui/layui.js'"></script>
<script th:src="${_CPR_}+'/elective/js/common.js'"></script>
<!--<script th:src="${_CPR_}+'/elective/js/resultData.js?v=6'"></script>-->
<script>
    layui.use(["jquery", "table", "layer", "upload"], function () {
        var table = layui.table,
            form = layui.form,
            layer = layui.layer,
            upload = layui.upload;
        var thisxxkid;
        var courseName;
        // tab切换
        $(".data-nav li").click(function () {
            $(this).addClass("active").siblings().removeClass("active");
            var idx = $(this).index();
            $(".data-box-wrap .data-box").eq(idx).show().siblings().hide();
            if (idx == '1') {
                $.ajax({
                    type: "POST",
                    url: "/elective/task/getAddresultFailCount",
                    data: {status: 2},
                    dataType: 'json',
                    success: function (data) {
                        if (data.count > 0) {
                            $("#downFailButton").show();
                        } else {
                            $("#downFailButton").hide();
                        }
                    }
                });
            }
        })

        // 添加选修课
        $("#addCourseBtn").click(function () {
            var bdid = $("#jhul").find(".active").attr("data-id");
            var str = '<div class="el-item">' +
                '<div class="course">' +
                '<div class="name">选修课信息：</div>' +
                '<div class="txt">选择选修课</div>' +
                '</div>' +
                '<div class="student">' +
                '<div class="name">学生信息：</div>' +
                '<div class="txt">' +
                '<span class="sel-stu">选择学生</span>' +
                '<i>共<em>8</em>人</i>' +
                '</div>' +
                '</div>' +
                '<div class="delete"></div>' +
                '</div>';
            $(".elective-list").append(str);
            var parent = $(this).parent().parent();
            if (parent.outerHeight(true) > 360) {
                parent.addClass("courseScroll");
                console.log(parent.scrollHeight);
                parent[0].scrollTop = parent[0].scrollHeight;
            } else {
                parent.removeClass("courseScroll")
            }
            // $.ajax({
            //     type: "POST",
            //     url: "/elective/getDxxxkByTaskBdid",
            //     data: {fid: fid, bdid: bdid},
            //     dataType: 'json',
            //     success: function (result) {
            //         var html = "";
            //         if (result) {
            //             for (var i = 0; i < result.taskCourses.length; i++) {
            //                 html += "<li data-id=\"" + result.taskCourses[i].id + "\" >" + result.taskCourses[i].xxkXxkmc + "</li>";
            //             }
            //         }
            //         $(".xxkul").html(html);
            //     }
            // });
        })
        //删除 选修课
        $("#resultData").on("click", ".elective-list .el-item .delete", function () {
            opnum = $(".elective-list .el-item .delete").index(this);
            array.splice(opnum, 1);
            $(this).parents(".el-item").remove();
        })
        // 下拉多选
        $(".data-course").on("click", ".j-search-con .schoolSel", function (e) {
            let parent = $(this).parent();
            var isHasSlide = parent.find(".j-arrow").hasClass("j-arrow-slide");
            $(".j-select-year").removeClass("slideShow");
            $(".j-arrow").removeClass("j-arrow-slide");
            if (isHasSlide) {
                parent.find(".j-arrow").removeClass("j-arrow-slide");
                parent.find(".j-select-year").removeClass("slideShow");
            } else {
                parent.find(".j-arrow").addClass("j-arrow-slide");
                parent.find(".j-select-year").addClass("slideShow");
            }
            stopBubble(e);
        });

        //  选择-单选
        $(".data-course").on("click", ".j-search-con.single-box .j-select-year li ", function (e) {
            var flag = false;
            var parentss = $(this).parents(".data-course");
            var ppid = parentss.attr("id");
            if (ppid == 'addResult-data-course') {
                if ($(this).parents("ul").attr("id") != 'jhul') {
                    var exit = $(".layui-form-item");
                    var xxkid = "xxk" + $(this).attr("data-id");
                    for (var i = 0; i < exit.length; i++) {
                        var tempid = exit.eq(i).attr("xxkid");
                        if (xxkid == tempid) {
                            flag = true;
                            break;
                        }
                    }
                    if (!flag) {
                        $(this).parents(".layui-form-item").attr("xxkid", xxkid);
                    }
                }
            }
            if (flag) {
                return false;
            }
            $(this).addClass("active").siblings().removeClass();
            var parents = $(this).parents(".j-search-con");
            var schoolSelEle = parents.find(".schoolSel");
            var txt = $(this).text();
            schoolSelEle.val(txt);
            parents.find(".j-arrow").removeClass("j-arrow-slide");
            parents.find(".j-select-year").removeClass("slideShow");
            stopBubble(e);
        });
        // 删除
        $(".result-form").on('click', ".sel-del", function () {
            var xxkid = $(this).parents(".layui-form-item").attr("xxkid");
            $.each(array, function (index, item) {
                if (item != undefined && item.xxkid == xxkid) {
                    array.splice(index, 1);
                }
            });
            $(this).parents(".layui-form-item").remove();
        })
        // 选择学生
        var layerIndex, selStuEle;

        $(".result-form").on('click', '.sel-stu', function () {
            var xxkid = $(this).parents(".el-item").attr("xxkid");
            thisJson = {};
            $.each(array, function (index, item) {
                if (item != undefined && item.xxkid == xxkid) {
                    thisJson = item;
                }
            });
            if (thisJson.isAll == "true") {
                $("#checkAll").addClass("checked");
            } else {
                $("#checkAll").removeClass("checked");
            }
            opnum = $(".sel-stu").index(this);
            if (xxkid != undefined) {
                json = thisJson;
                json.xxkid = xxkid;
                thisxxkid = xxkid;
                selStuEle = $(this);
                layerIndex = layer.open({
                    type: 1,
                    title: false,
                    closeBtn: false,
                    shadeClose: true,
                    isOutAnim: true,
                    shadeClose: false,

                    content: $("#selStu"),
                    area: ["auto", "auto"],
                    success: function (layero, index) {
                        tableSet()
                    },
                });
                addResultGetNj();
                addResultGetYx();
                addResultGetXq();
                addResultGetZY("");
                addResultGetBJ("", "");
            }
        })
        // 导入选课学生
        upload.render({
            elem: "#ID-upload-demo-drag",
            url: "/elective/task/importResults", // 此处用的是第三方的 http 请求演示，实际使用时改成您自己的上传接口即可。

            accept: "file",
            exts: "xlsx|xls",
            size: 500,

            before: function (obj) {
                var files = (this.files = obj.pushFile()); // layui文档标注这是一个文件队列，(每次选定文件都会往其中添加)
                var fileName, fileKey;
                var i = 0;
                var j = 0;
                for (var key in files) {
                    i++;
                    fileName = files[key].name; //针对一个文件直接赋值就可以了
                }
                //判断队列每次只保存最后一个文件

                if (i > 1) {
                    for (var key in files) {
                        j++;
                        if (i == j) {
                            fileName = files[key].name;
                            fileKey = key;
                        } else {
                            delete files[key]; //删除队列中的文件
                        }
                    }
                }
                var ext = fileName.substr(fileName.lastIndexOf(".") + 1); //获得后缀
                uploadFileName = fileName;
                if ("xlsx,xls".indexOf(ext) == -1) {
                    layer.msg("请上传excel文件");
                    return false;
                }
            },
            done: function (res) {
                if (res.status) {
                    layer.msg("上传成功");
                    $("#uploadExcel")
                        .removeClass("layui-hide")
                        .find("span")
                        .text(uploadFileName);
                } else {
                    layer.msg(res.msg);
                }
            },
            error: function (index, upload) {
                layer.msg("上传失败，请重新上传");
            },
        });
        var inr;
        var exit = false;
        // 添加
        $("#stuSure").click(function () {
            var navIndex = $(".data-nav li.active").index();
            if (navIndex == 0) {
                if ($(".data-course .data-course-con").is(':hidden')) {
                    window.parent.postMessage(JSON.stringify({action: 1}), "*");
                    $("#resultData,.marker").hide();
                    return false;
                }
                $(".loading").show()
                $.ajax({
                    type: "POST",
                    url: "/elective/task/addResults",
                    data: {array: JSON.stringify(array)},
                    dataType: 'json',
                    aysnc: 'false',
                    success: function (data) {
                        $(".loading").fadeOut('fast');
                        $(".data-course .data-course-con").hide();
                        if (data.count > 0) {
                            // 有错误信息
                            getFail();
                            $(".data-course .failTable").show();
                        } else {
                            // 成功
                            $(".data-course .addTips").fadeIn('fast')
                        }
                    }
                });
            } else {
                if (exit) {
                    window.parent.postMessage(JSON.stringify({action: 1}), "*");
                    $("#resultData,.marker").hide();
                    return false;
                }
                // if ($(".data-upload .data-upload-wrap").is(':hidden')) {
                //     window.parent.postMessage(JSON.stringify({action: 1}), "*");
                //     $("#resultData,.marker").hide();
                //     return false;
                // }
                if ($("#uploadExcel").find("span").text() == '') {
                    return false;
                }
                $(".loading").show()
                $.ajax({
                    type: "POST",
                    url: "/elective/task/importResultsSub",
                    dataType: 'json',
                    aysnc: 'false',
                    success: function (data) {
                        inr = setInterval(function () {
                            getState();
                        }, 1000);
                    }
                });
            }


            /*   $(".loading").show()
              setTimeout(function () {
                  $(".loading").fadeOut('fast');
                  $("#addTips").fadeIn('fast').delay(2000)
                      .fadeOut('fast', function () {
                          $("#resultData,.marker").hide();
                      });
              }, 2000) */

        })


        function getState() {
            $.ajax({
                type: "POST",
                url: "/elective/task/getImportResultsSubState",
                dataType: 'json',
                success: function (data) {
                    if (data.data.state == 1) {
                        $(".loading").fadeOut('fast');
                        $("#up·loadExcel")
                            .addClass("layui-hide")
                            .find("span")
                            .text("");
                        clearInterval(inr);
                        exit = true;
                        if (data.data.failNum == 0) {
                            $(".data-upload-wrap").hide();
                            $(".data-upload .addTips").fadeIn('fast')
                            $("#downFailButton").hide();
                        } else {
                            $("#ID-upload-demo-drag").hide();
                            $(".data-upload .tip-fail").html("成功上传" + data.data.succNum + "条" + "，失败" + data.data.failNum + "条<br>失败原因请下载错误数据查看")
                            $(".data-upload .tip-fail").fadeIn('fast')
                            $("#downFailButton").show();
                        }
                    }

                }
            });
        }

        // 取消
        $("#stuCancel,#resultData .pu-close").click(function () {
            $("#resultData,.marker").hide();
        })
        /*********************************** 学生范围* start ****************************/
        // 查询
        $("#selStu .z-btn").click(function () {
            tableSet();
            return false;
        })
        $("#selCourse .z-btn").click(function () {
            addResultSearchCourse(courseName);
            return false;
        })
        // 添加选课学生-按学生显示
        var mtTable2 = [
            {
                id: 1,
                name: "23计算机科学",
                code: "202301010101",
                grade: "2023",
                depth: "计算机学院",
                major: "计算机学院",
                class: "23计算机1班",
                school: "南校区",
                isAdd: false,
            },
            {
                id: 2,
                name: "23计算机科学",
                code: "202301010101",
                grade: "2023",
                depth: "计算机学院",
                major: "计算机学院",
                class: "23计算机1班",
                school: "南校区",
                isAdd: false,
            },
            {
                id: 3,
                name: "23计算机科学",
                code: "202301010101",
                grade: "2023",
                depth: "计算机学院",
                major: "计算机学院",
                class: "23计算机1班",
                school: "南校区",
                isAdd: false,
            },
            {
                id: 4,
                name: "23计算机科学",
                code: "202301010101",
                grade: "2023",
                depth: "计算机学院",
                major: "计算机学院",
                class: "23计算机1班",
                school: "南校区",
                isAdd: false,
            },
        ];

        table.on('checkbox(mtTable2)', function (obj) {
            if ($("#checkAll").hasClass("checked")) {
                var str = "";
                if (json.notStr != undefined) {
                    str = json.notStr;
                }
                var temp = str.split(",");
                var ids = [];
                for (var i = 0; i < temp.length; i++) {
                    if (temp[i] != '') {
                        ids.push(temp[i]);
                    }
                }
                if (obj.data.rowInfo != undefined) {
                    var code = obj.data.xsxkjg_uid;
                    if (obj.checked) {
                        ids.splice($.inArray(code, ids), 1);
                    } else {
                        if ($.inArray(code, ids) == -1) {
                            ids.push(code);
                        }
                    }
                } else {
                    var checkStatus = table.checkStatus('mtTable2');
                    if (checkStatus.data.length == 0) {
                        var data = table.cache.mtTable2;
                        for (var i = 0; i < data.length; i++) {
                            var code = data[i].xsxkjg_uid;
                            if ($.inArray(code, ids) == -1) {
                                ids.push(code);
                            }
                        }
                    } else {
                        for (var i = 0; i < checkStatus.data.length; i++) {
                            var code = checkStatus.data[i].xsxkjg_uid;
                            if ($.inArray(code, ids) > -1) {
                                ids.splice($.inArray(code, ids), 1);
                            }
                        }
                    }
                }
                json.notStr = ids.toString();
                var allCount = parseInt(json.allCount) - ids.length;
                $(".table1 .z-has-check .num").text(allCount);
            } else {
                var str = "";
                if (json.str != undefined) {
                    str = json.str;
                }
                var strs = str.split(",");
                var codes = [];
                for (var i = 0; i < strs.length; i++) {
                    if (strs[i] != "") {
                        codes.push(strs[i]);
                    }
                }
                if (obj.data.rowInfo != undefined) {
                    var code = obj.data.xsxkjg_uid;
                    if (obj.checked) {
                        if ($.inArray(code, codes) == -1) {
                            codes.push(code);
                        }
                    } else {
                        codes.splice($.inArray(code, codes), 1);
                    }
                } else {
                    var checkStatus = table.checkStatus('mtTable2');
                    if (checkStatus.data.length == 0) {
                        var data = table.cache.mtTable2;
                        for (var i = 0; i < data.length; i++) {
                            var code = data[i].xsxkjg_uid;
                            codes.splice($.inArray(code, codes), 1);
                        }
                    } else {
                        for (var i = 0; i < checkStatus.data.length; i++) {
                            var code = checkStatus.data[i].xsxkjg_uid;
                            if ($.inArray(code, codes) == -1) {
                                codes.push(code);
                            }
                        }
                    }
                }
                json.str = codes.toString();
                //        var selLen = table.checkStatus('mtTable2').data.length;
                $(".table1 .z-has-check .num").text(codes.length);
            }
            obj.update({isAdd: obj.checked})
        })
        // 选择全部
        $("#checkAll").click(function () {
            $(this).toggleClass("checked");
            if ($(this).hasClass("checked")) {
                $('div[lay-id="mtTable2"] input[type=checkbox]').prop("checked", true);
                $('div[lay-id="mtTable2"] .layui-form-checkbox').addClass(
                    "layui-form-checked"
                );
                mtTable2.map((item) => {
                    item.isAdd = true;
                });
                $(".table1 .z-has-check .num").text(json.allCount);
            } else {
                $('div[lay-id="mtTable2"] input[type=checkbox]').prop("checked", false);
                $('div[lay-id="mtTable2"] .layui-form-checkbox').removeClass(
                    "layui-form-checked"
                );
                mtTable2.map((item) => {
                    item.isAdd = false;
                });
                $(".table1 .z-has-check .num").text(0);
            }
        });
        // 添加选课学生-按班级显示
        var mtTable3 = [
            {
                id: 1,
                name: "23计算机科学",
                code: "202301010101",
                grade: "2023",
                depth: "计算机学院",
                major: "计算机学院",
                class: "23计算机1班",
                school: "南校区",
                isAdd: false,
                classCount: 20,
            },
            {
                id: 2,
                name: "23计算机科学",
                code: "202301010101",
                grade: "2023",
                depth: "计算机学院",
                major: "计算机学院",
                class: "23计算机1班",
                school: "南校区",
                isAdd: false,
                classCount: 20,
            },
        ];
        table.on('checkbox(mtTable3)', function (obj) {
            if ($("#checkAllClass").hasClass("checked")) {
                var str = "";
                if (json.notStr != undefined) {
                    str = json.notStr;
                }
                var temp = str.split(",");
                var ids = [];
                for (var i = 0; i < temp.length; i++) {
                    if (temp[i] != '') {
                        ids.push(temp[i]);
                    }
                }
                if (obj.data.rowInfo != undefined) {
                    var code = obj.data.bjxx_bjmc;
                    if (obj.checked) {
                        ids.splice($.inArray(id, ids), 1);
                    } else {
                        if ($.inArray(code, ids) == -1) {
                            ids.push(code);
                        }
                    }
                } else {
                    var checkStatus = table.checkStatus('mtTable3');
                    if (checkStatus.data.length == 0) {
                        var data = table.cache.mtTable3;
                        for (var i = 0; i < data.length; i++) {
                            var code = data[i].bjxx_bjmc;
                            if ($.inArray(code, ids) == -1) {
                                ids.push(code);
                            }
                        }
                    } else {
                        for (var i = 0; i < checkStatus.data.length; i++) {
                            var code = checkStatus.data[i].code;
                            if ($.inArray(code, ids) > -1) {
                                ids.splice($.inArray(code, ids), 1);
                            }
                        }
                    }
                }
                json.notStr = ids.toString();
                var allCount = parseInt(json.allCount) - ids.length;
                $(".table2 .z-has-check .num").text(allCount);
            } else {
                var str = "";
                if (json.str != undefined) {
                    str = json.str;
                }
                var strs = str.split(",");
                var codes = [];
                for (var i = 0; i < strs.length; i++) {
                    if (strs[i] != "") {
                        codes.push(strs[i]);
                    }
                }
                if (obj.data.rowInfo != undefined) {
                    var code = obj.data.bjxx_bjmc;
                    if (obj.checked) {
                        if ($.inArray(code, codes) == -1) {
                            codes.push(code);
                        }
                    } else {
                        codes.splice($.inArray(code, codes), 1);
                    }
                } else {
                    var checkStatus = table.checkStatus('mtTable3');
                    if (checkStatus.data.length == 0) {
                        var data = table.cache.mtTable3;
                        for (var i = 0; i < data.length; i++) {
                            var code = data[i].bjxx_bjmc;
                            codes.splice($.inArray(code, codes), 1);
                        }
                    } else {
                        for (var i = 0; i < checkStatus.data.length; i++) {
                            var code = checkStatus.data[i].bjxx_bjmc;
                            if ($.inArray(code, codes) == -1) {
                                codes.push(code);
                            }
                        }
                    }
                }
                json.str = codes.toString();
                $(".table2 .z-has-check .num").text(codes.length);
            }
            obj.update({isAdd: obj.checked})
        })

        function tableSet() {
            var str = $(".z-tab-search ul").find(".active").text();
            if (str == '按学生显示') {
                addResultSearchStu();
            }
            if (str == '按班级显示') {
                addResultSearchBj();
            }
        }

        // 选择全部
        $("#checkAllClass").click(function () {
            $(this).toggleClass("checked");
            if ($(this).hasClass("checked")) {
                $('div[lay-id="mtTable3"] input[type=checkbox]').prop("checked", true);
                $('div[lay-id="mtTable3"] .layui-form-checkbox').addClass(
                    "layui-form-checked"
                );
                mtTable3.map((item) => {
                    item.isAdd = true;
                });
                $(".table2 .z-has-check .num").text(json.allCount);
            } else {
                $('div[lay-id="mtTable3"] input[type=checkbox]').prop("checked", false);
                $('div[lay-id="mtTable3"] .layui-form-checkbox').removeClass(
                    "layui-form-checked"
                );
                mtTable3.map((item) => {
                    item.isAdd = false;
                });
                $(".table2 .z-has-check .num").text(0);
            }
        });

        //  选择
        $("#selStu .z-tab-search ul li").click(function () {
            $(this).addClass("active").siblings().removeClass("active");
            var idx = $(this).index();
            $(".z-table-sel .tabBox").eq(idx).show().siblings().hide();
            if (idx == 1) table.reload("mtTable3");
            json = {};
            tableSet();
        });

        // 确定
        $("#stuSureBtn").click(function () {
            if ($("#checkAll").hasClass("checked") || $("#checkAllClass").hasClass("checked")) {
                json.isAll = "true";
            } else {
                json.isAll = "false";
            }
            json.xxkid = thisxxkid;
            var str = $(".z-tab-search ul").find(".active").text();
            if (str == '按学生显示') {
                json.status = "stu";
            }
            if (str == '按班级显示') {
                json.status = "bj";
            }
            if (json.isAll == "true") {
                var selStuNjIp = $("#njip").val();
                var selStuYxIp = $("#yxip").val();
                var selStuZyIp = $("#zyip").val();
                var selStuBjIp = $("#bjip").val();
                var selStuXqIp = $("#xqip").val();
                var selStuXmIp = $("#xmip").val();
                json.selStuNjIp = selStuNjIp;
                json.selStuYxIp = selStuYxIp;
                json.selStuZyIp = selStuZyIp;
                json.selStuBjIp = selStuBjIp;
                json.selStuXqIp = selStuXqIp;
                json.selStuXmIp = selStuXmIp;
            }
            var taskbdid = $("#jhul").find(".active").attr("data-id");
            json.taskbdid = taskbdid;
            var flag = false;
            if ((json.isAll == "false" && json.str != "" && json.str != undefined) || (json.isAll == "true")) {
                flag = true
            }
            if (flag) {
                selStuEle.text('已选学生')
            } else {
                selStuEle.text('选择学生')
            }
            array.splice(opnum, 1);
            array.push(json);
            layer.close(layerIndex);
        });
        // 取消
        $(".pu-cancel,.pu-close").click(function () {
            layer.close(layerIndex);
        });
        $(".layui-tips").on({
            mouseenter: function () {
                var that = this;
                var con = $(this).attr("data-tip");
                layTips = layer.tips(con, that, {
                    tips: 1,
                });
            },
            mouseleave: function () {
                layer.close(layTips);
            },
        });

        //
        /*********************************** 学生范围* end ****************************/
        function addResultSearchStu() {
            var taskbdid = $("#jhul").find(".active").attr("data-id");
            var selStuNjIp = $("#njip").val();
            var selStuYxIp = $("#yxip").val();
            var selStuZyIp = $("#zyip").val();
            var selStuBjIp = $("#bjip").val();
            var selStuXqIp = $("#xqip").val();
            var selStuXmIp = $("#xmip").val();
            var mtTable2 = [];
            table.render({
                elem: "#mtTable2",
                url: '/elective/studentRange/getStudentV3?taskbdid=' + taskbdid + '&fid=' + fid + '&nj=' + selStuNjIp + '&yx=' + selStuYxIp + '&zy=' + selStuZyIp + '&bj=' + selStuBjIp + '&xq=' + selStuXqIp + "&xm=" + selStuXmIp + "&xxkid=" + thisxxkid.replace("xxk", ""),
                data: mtTable2,
                page: true,
                height: 396,
                cols: [
                    [
                        {
                            type: "checkbox",
                            fixed: "left",
                            width: 70,
                        },
                        {
                            field: "xsxkjg_xm",
                            title: "姓名",
                            align: "center",
                            minWidth: 140,
                        },
                        {
                            field: "xsxkjg_xh",
                            title: "学号",
                            align: "center",
                            minWidth: 178,
                        },
                        {
                            field: "xsxkjg_ssnj",
                            title: "所属年级",
                            align: "center",
                            minWidth: 109,
                        },
                        {
                            field: "xsxkjg_ssxy",
                            title: "所属院系",
                            align: "center",
                            minWidth: 136,
                        },
                        {
                            field: "xsxkjg_sszy",
                            title: "所属专业",
                            align: "center",
                            minWidth: 105,
                        },
                        {
                            field: "xsxkjg_szbj",
                            title: "所在班级",
                            align: "center",
                            minWidth: 129,
                        },
                        {
                            field: "xsxkjg_szxq",
                            title: "所在校区",
                            align: "center",
                            minWidth: 128,
                        },
                        {
                            field: "rowInfo",
                            title: "rowInfo",
                            align: "center",
                            minWidth: 110,
                            hide: true,
                        },
                    ],
                ],
                done: function (res, curr, count) {
                    json.allCount = count;
                    var num = 0;
                    if ($("#checkAll").hasClass("checked")) {
                        var notStr = "";
                        if (json.notStr != undefined) {
                            notStr = json.notStr;
                        }
                        var temp = notStr.split(",");
                        var ids = [];
                        for (var i = 0; i < temp.length; i++) {
                            if (temp[i] != '') {
                                ids.push(temp[i]);
                            }
                        }
                        res.data.forEach(function (item, index) {
                            var code = item.xsxkjg_uid;
                            if ($.inArray(code, ids) == -1) {
                                num++;
                                $('div[lay-id="mtTable2"] .layui-table-fixed-l .layui-table-body').find('tr').eq(index).find('input[type=checkbox]').prop('checked', true);
                                $('div[lay-id="mtTable2"] .layui-table-fixed-l .layui-table-body').find('tr').eq(index).find(".layui-form-checkbox").addClass('layui-form-checked');
                            }
                        });
                    } else {
                        var str = "";
                        if (json.str != undefined) {
                            str = json.str;
                        }
                        var temp = str.split(",");
                        var ids = [];
                        for (var i = 0; i < temp.length; i++) {
                            if (temp[i] != '') {
                                ids.push(temp[i]);
                            }
                        }
                        res.data.forEach(function (item, index) {
                            var code = item.xsxkjg_uid;
                            if ($.inArray(code, ids) != -1) {
                                num++;
                                $('div[lay-id="mtTable2"] .layui-table-fixed-l .layui-table-body').find('tr').eq(index).find('input[type=checkbox]').prop('checked', true);
                                $('div[lay-id="mtTable2"] .layui-table-fixed-l .layui-table-body').find('tr').eq(index).find(".layui-form-checkbox").addClass('layui-form-checked');
                            }
                        });
                    }
                    if (num == res.data.length) {
                        $('div[lay-id="mtTable2"] input[type=checkbox]').prop("checked", true);
                        $('div[lay-id="mtTable2"] .layui-form-checkbox').addClass(
                            "layui-form-checked"
                        );
                    }
                },
            });
        }

        function addResultSearchBj() {
            var taskbdid = $("#jhul").find(".active").attr("data-id");
            var selStuNjIp = $("#njip").val();
            var selStuYxIp = $("#yxip").val();
            var selStuZyIp = $("#zyip").val();
            var selStuBjIp = $("#bjip").val();
            var selStuXqIp = $("#xqip").val();
            var selStuXmIp = $("#xmip").val();
            var mtTable3 = [];
            table.render({
                elem: "#mtTable3",
                url: '/elective/studentRange/getBj?taskbdid=' + taskbdid + '&fid=' + fid + '&nj=' + selStuNjIp + '&yx=' + selStuYxIp + '&zy=' + selStuZyIp + '&bj=' + selStuBjIp + '&xq=' + selStuXqIp + "&xm=" + selStuXmIp + "&xxkid=" + thisxxkid.replace("xxk", ""),
                data: mtTable3,
                page: true,
                height: 396,
                cols: [
                    [
                        {
                            type: "checkbox",
                            fixed: "left",
                            width: 70,
                        },
                        {
                            field: "bjxx_bjmc",
                            title: "班级名称",
                            align: "center",
                            minWidth: 140,
                        },

                        {
                            field: "bjxx_rxnf",
                            title: "所属年级",
                            align: "center",
                            minWidth: 129,
                        },
                        {
                            field: "bjxx_ssyx",
                            title: "所属院系",
                            align: "center",
                            minWidth: 136,
                        },
                        {
                            field: "bjxx_zy",
                            title: "所属专业",
                            align: "center",
                            minWidth: 125,
                        },

                        {
                            field: "bjxx_szxq",
                            title: "所在校区",
                            align: "center",
                            minWidth: 128,
                        },
                        {
                            field: "bjxx_njrs",
                            title: "班级人数",
                            align: "center",
                            minWidth: 80,
                        },
                        {
                            field: "rowInfo",
                            title: "rowInfo",
                            align: "center",
                            minWidth: 110,
                            hide: true,
                        },
                    ],
                ],
                done: function (res, curr, count) {
                    json.allCount = count;
                    var num = 0;
                    if ($("#checkAllClass").hasClass("checked")) {
                        var notStr = ""
                        if (json.notStr != undefined) {
                            notStr = json.notStr;
                        }
                        var temp = notStr.split(",");
                        var ids = [];
                        for (var i = 0; i < temp.length; i++) {
                            if (temp[i] != '') {
                                ids.push(temp[i]);
                            }
                        }
                        res.data.forEach(function (item, index) {
                            var code = item.bjxx_bjmc;
                            if ($.inArray(code, ids) == -1) {
                                num++;
                                $('div[lay-id="mtTable3"] .layui-table-fixed-l .layui-table-body').find('tr').eq(index).find('input[type=checkbox]').prop('checked', true);
                                $('div[lay-id="mtTable3"] .layui-table-fixed-l .layui-table-body').find('tr').eq(index).find(".layui-form-checkbox").addClass('layui-form-checked');
                            }
                        });
                    } else {
                        var str = "";
                        if (json.str != undefined) {
                            str = json.str;
                        }
                        var temp = str.split(",");
                        var ids = [];
                        for (var i = 0; i < temp.length; i++) {
                            if (temp[i] != '') {
                                ids.push(temp[i]);
                            }
                        }
                        res.data.forEach(function (item, index) {
                            var code = item.bjxx_bjmc;
                            if ($.inArray(code, ids) != -1) {
                                num++;
                                $('div[lay-id="mtTable3"] .layui-table-fixed-l .layui-table-body').find('tr').eq(index).find('input[type=checkbox]').prop('checked', true);
                                $('div[lay-id="mtTable3"] .layui-table-fixed-l .layui-table-body').find('tr').eq(index).find(".layui-form-checkbox").addClass('layui-form-checked');
                            }
                        });
                    }
                    if (num == res.data.length) {
                        $('div[lay-id="mtTable3"] input[type=checkbox]').prop("checked", true);
                        $('div[lay-id="mtTable3"] .layui-form-checkbox').addClass(
                            "layui-form-checked"
                        );
                    }
                },
            });
        }

        $("#xmip").keyup(function () {
            if (event.keyCode == 13) {
                tableSet();
            }
        });

        $("#keyWordCourse").keyup(function () {
            if (event.keyCode == 13) {
                addResultSearchCourse(courseName);
            }
        });

        function getFail() {
            var mtTableData = []
            table.render({
                elem: "#errorTable",
                url: '/elective/task/getAddresultFail',
                data: mtTableData,
                page: true,
                height: 396,
                limit: 10,
                cols: [
                    [
                        {
                            field: "xxkname",
                            title: "选修课",
                            align: "center",
                            width: 210,
                        },
                        {
                            field: "realname",
                            title: "学生",
                            align: "center",
                            width: 210,
                        },
                        {
                            field: "studentCode",
                            title: "学号",
                            align: "center",
                            width: 210,
                        },
                        {
                            field: "msg",
                            title: "失败原因",
                            align: "center",
                            width: 200,
                        }
                    ],
                ],
                done: function (res, curr, count) {
                },
            });
        }

        /** 2025.3.31 新增js start */

        $("#resultData").on("click", ".elective-list .el-item .course .txt", function () {

            opnum = $(".elective-list .el-item .course .txt").index(this);
            let value = $("input[name='teacherName']").val().trim();
            if (value == '') {
                layer.msg("请选择计划");
                return false;
            }
            addResultGetNj();
            addResultGetYx();
            addResultGetXq();
            addResultGetZY("");

            $("#targetId").removeAttr("id");
            $(this).parents(".el-item").attr("id", "targetId");
            let name = $(this).text();
            courseName = name;
            layerIndex = layer.open({
                type: 1,
                title: false,
                closeBtn: false,
                shadeClose: true,
                isOutAnim: true,
                shadeClose: false,

                content: $("#selCourse"),
                area: ["auto", "auto"],
                success: function (layero, index) {
                    addResultSearchCourse(name);
                    /*table.reload("mtTable4",{
                        done: function (res, curr, count) {
                            if(name!="选择选修课"){
                                // 确保数据存在且非空
                                if (!res.data || res.data.length === 0) return;

                                // // 获取目标任务的ID
                                // const targetTaskId = $('#relationTask').data('taskId');
                                // if (!targetTaskId) return;

                                // 遍历当前页数据
                                res.data.forEach((item, i) => {
                                    if (item.name === name) {
                                        console.log(name);
                                        const rowIndex = item.LAY_TABLE_INDEX;
                                        // 精确选中目标行单选框
                                        const radio = $(`tr[data-index="${rowIndex}"] input[type="radio"]`);
                                        radio.prop('checked', true);
                                        // 高亮选中行（可选）
                                        radio.closest('tr').addClass('layui-table-click');
                                        form.render('radio');
                                    }
                                });
                            }else{
                                res.data.forEach((item, i) => {
                                    const radio = $(`tr input[type="radio"]`);
                                    radio.prop('checked', false);
                                    // 高亮选中行（可选）
                                    radio.closest('tr').removeClass('layui-table-click');
                                    form.render('radio');
                                });
                            }
                        }
                    });*/
                },
            });
        })

        function getRadioData() {
            var checkStatus = table.checkStatus('mtTable4'); // 'yourTableFilter'是表格的filter值，用于唯一标识表格
            var data = checkStatus.data.filter(function (item) {
                return !item.disabled; // 过滤掉被禁用的行
            });
            // var data = checkStatus.data; // 获取选中行的数据
            console.log(data); // 输出选中行的数据

            if (data.length > 0) {
                var pu = true;
                for (let j = 0; j < array.length; j++) {
                    if (array[j].xxkid == data[0].id) {

                        pu = false;
                    }
                }
                if (pu) {
                    if (array.length > opnum) {
                        json = array[opnum];
                    } else {
                        json = {};
                    }
                    json.xxkid = data[0].id;
                    array.splice(opnum, 1);
                    array.push(json);
                }
                $("#targetId").addClass("clicked").find(".course .txt").text(data[0].xxkXxkmc);
                $("#targetId").attr("xxkid", data[0].id);
            }
        }

        function getCheckedData() {
            var checkStatus = table.checkStatus('mtTable2'); // 'yourTableFilter'是表格的filter值，用于唯一标识表格
            var data = checkStatus.data; // 获取选中行的数据
            console.log(data); // 输出选中行的数据

            if (data.length > 0) {

                let str = '';
                data.forEach(function (item, index) {
                    str += item.name + ','
                });

                console.log(str);

                $("#targetId").find(".sel-stu").text(str.slice(0, -1));
                $("#targetId").find(".txt i").show().text("共" + data.length + "人");
            }

            // $("#targetId").addClass("clicked").find(".course .txt").text(data[0].name);
        }

        $("#selCourse .dialog-btn button.pu-sure").click(function () {
            getRadioData();
            layer.close(layerIndex);

        })

        function addResultSearchCourse(name) {
            table.render({
                elem: "#mtTable4",
                page: true,
                url: "/elective/getDxxxkByTaskBdidPage",
                where: {
                    fid: fid
                    , bdid: taskbdid
                    , nj: $("#njCourse").val()
                    , yx: $("#yxCourse").val()
                    , zy: $("#zyCourse").val()
                    , kskc: $("#kskcCourse").val()
                    , xq: $("#xqCourse").val()
                    , lx: $("#lxCourse").val()
                    , keyWord: $("#keyWordCourse").val()
                },
                cols: [
                    [
                        {
                            type: "radio",
                            // fixed: "left",
                            width: 70,
                        },
                        {
                            field: "xxkXxkmc",
                            title: "选修课名称",
                            align: "center",
                            minWidth: 140,
                        },
                        {
                            field: "xxkKknj",
                            title: "开课年级",
                            align: "center",
                            minWidth: 109,
                        },
                        {
                            field: "xxkKkyx",
                            title: "开课院系",
                            align: "center",
                            minWidth: 136,
                        },
                        {
                            field: "xxkKkzy",
                            title: "开课专业",
                            align: "center",
                            minWidth: 105,
                        },
                        {
                            field: "kskc",
                            title: "开设课程",
                            align: "center",
                            minWidth: 129,
                        },
                        {
                            field: "xxkKkxiaoqu",
                            title: "开课校区",
                            align: "center",
                            minWidth: 105,
                        },
                        {
                            field: "xxkKclx",
                            title: "课程类型",
                            align: "center",
                            minWidth: 128,
                        },
                        {
                            field: "id",
                            title: "rowInfo",
                            align: "center",
                            hide: true,
                        },
                    ],
                ],
                done: function (res, curr, count) {
                    res.data.forEach((item, i) => {
                        if (item.xxkXxkmc === name) {
                            console.log(name);
                            const rowIndex = item.LAY_TABLE_INDEX;
                            // 精确选中目标行单选框
                            const radio = $(`tr[data-index="${rowIndex}"] input[type="radio"]`);
                            radio.prop('checked', true);
                            // 高亮选中行（可选）
                            radio.closest('tr').addClass('layui-table-click');
                            form.render('radio');
                        }
                    });
                    var trs = document.querySelectorAll('#selCourse .layui-table-body tr');

                    trs.forEach(function (tr, index) {
                        let text = $(tr).find("[data-field='id']").find("div").eq(0).text();
                        let xxkXxkmc = $(tr).find("[data-field='xxkXxkmc']").find("div").eq(0).text();
                        for (let i = 0; i < array.length; i++) {
                            if (text == array[i].xxkid && xxkXxkmc != name) {
                                tr.querySelector('input[type="radio"]').parentNode.classList.add('radio-disabled');
                            }
                        }
                    });
                }
            });
        }

        /** 2025.3.31 新增js end */
    })

</script>
</html>