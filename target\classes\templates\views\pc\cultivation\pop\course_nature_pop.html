<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <title>学生列表</title>
    <link rel="stylesheet" th:href="@{../css/cultivation/jquery.searchableSelect.css}"/>
    <link rel="stylesheet" th:href="@{../css/cultivation/newStyle.css}"/>
    <link rel="stylesheet" th:href="@{../plugin/layui/css/layui.css}"/>
    <style>
        html, body {
            height: 100%;
        }

        .layui-laypage .layui-laypage-curr .layui-laypage-em {
            background-color: #4C88FF !important;
        }

        .layui-laypage .layui-laypage-limits {
            display: none !important;
        }

        .btn-warning {
            color: #fff;
            background-color: #f0ad4e;
            border-color: #eea236;
        }

        .btn {
            float: right;
            display: inline-block;
            padding: 6px 12px;
            margin-bottom: 0;
            font-size: 14px;
            font-weight: 400;
            line-height: 1.42857143;
            text-align: center;
            white-space: nowrap;
            vertical-align: middle;
            -ms-touch-action: manipulation;
            touch-action: manipulation;
            cursor: pointer;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            background-image: none;
            border: 1px solid transparent;
            border-radius: 4px;
        }
    </style>
</head>
<body>
<div class="popDiv popMove wid840">
    <div class="popBody" id="popScroll" style="max-height: 500px;">
        <div class="popSearch_cont">
            <button class="btn btn-sm btn btn-sm btn-warning" onclick="count1()"><i class="fa "></i>一键对应</button>
            <table lay-filter="courseNatureTable" class="layui-table" id="courseNatureTable">
            </table>
        </div>
    </div>
</div>
<script th:src="@{../js/jquery-3.3.1.min.js}"></script>
<script th:src="@{../js/jquery.nicescroll.min.js}"></script>
<script th:src="@{../js/cultivation/jquery.searchableSelect.js}"></script>
<script th:src="@{../plugin/layui/layui.js}"></script>
<script th:inline="javascript">
    var formUserId = [[${formUserId}]];

    //弹窗居中
    function MoveFixed(userClass) {
        $(userClass).css({
            top: function () {
                return 0;
            }, left: function () {
                return ($(window).width() - $(this).width()) / 2;
            }
        });

    }

    window.onresize = function () {
        MoveFixed('.popMove')
    }
    MoveFixed('.popMove')

    function scrollBox(userID) {
        $(userID).niceScroll({
            cursorborder: "",
            cursorwidth: 8,
            cursorcolor: "#CFD8E6",
            boxzoom: false,
            autohidemode: true
        });
        $(userID).getNiceScroll().resize(); //检测滚动条是否重置大小（当窗口改变大小时）
    }

    //scrollBox("#popScroll");
    var updArray = [];
    document.domain = document.domain.split('.').slice(-2).join('.');
    var table = "", insTb = "";
    layui.use('table', function () {
        table = layui.table;
        insTb = table.render({
            elem: '#courseNatureTable',
            height: 300,
            url: '../processData/getCourseNatureData?formUserId=' + formUserId,
            cols: [
                [
                    {field: 'nature', title: '课程性质'},
                    {field: 'yxCredit', title: '应修学分', edit: true},
                    {field: 'credit', title: '已设置课程学分'}
                ]
            ],
            done: function () {
                scrollBox("#popScroll");
            }
        });
        table.on("edit(courseNatureTable)", function (obj) {
            updArray.push(obj.data);
        })
    });

    function count1() {
        updArray = [];
        var array = layui.table.cache["courseNatureTable"];
        for (var i = 0; i < array.length; i++) {
            $(".layui-table-box tbody tr[data-index='" + i + "'] td[data-field='yxCredit'] div").text(array[i].credit);
            array[i].yxCredit = array[i].credit;
            updArray.push(array[i]);
        }
    }
</script>
</body>
</html>
