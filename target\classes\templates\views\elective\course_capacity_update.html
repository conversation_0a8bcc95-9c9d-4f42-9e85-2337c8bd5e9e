<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>调整选修课容量</title>
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/global.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/reset_capacity_update.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/ajustCapacity.css?v=1'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/layui/css/layui.css'">
</head>

<body>
<div class="marker"></div>
<div class="dialog" id="adjustCourse">
            <div class="dialog-title">
                <h3>调整选修课容量</h3>
                <span class="pu-close"></span>
            </div>
    <div class="dialog-con">
        <div class="adjust-item">
            <div class="adjust-item-label">调整方式</div>
            <div class="adjust-item-block">
                <ul name="" id="adjustType">
                    <li value="1" class="active">调整到指定比例</li>
                    <li value="2">调整到指定容量</li>
                </ul>
            </div>
        </div>
        <div class="adjust-item adjust-percent">
            <div class="adjust-item-label">
                调整比例<span></span>
                <div class="tips-con">
                    调整后容量和调整前容量的百分比，100%则容量不变，大于100%即扩大容量，小于100%即减少容量
                </div>
            </div>
            <div class="adjust-item-block  flex">
                <input type="number" placeholder="请输入调整百分比" min="0" class="capacityCount"/><span>%</span>
            </div>
        </div>
        <div class="adjust-item adjust-capacity">
            <div class="adjust-item-label">
                指定容量
            </div>
            <div class="adjust-item-block flex">
                <input type="number" placeholder="请输入调整后的选修课容量" min="0" class="capacityCount1"/>
            </div>
        </div>

        <div class="adjust-item adjust-gender">
            <div class="adjust-item-label">
                可选学生性别
            </div>
            <div class="j-search-con single-box">
                <input type="text" name="studentGender" placeholder="请选择" id="studentGender" readonly="" class="schoolSel">
                <span class="j-arrow"></span>
                <div class="j-select-year">
                    <ul name="studentGender">
                        <li data-id="0" class="">全部</li>
                        <li data-id="1" class="">男</li>
                        <li data-id="2" class="">女</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="adjust-item adjust-ratio hide">
            <div class="adjust-item-label" style="width: 84px;">
                男女比例<span></span>
                <div class="tips-con" style="left: -45%;">
                    填写男生限选人数:女生限选人数的比例值，比例为1则男女人数相等
                </div>
            </div>
            <div class="adjust-item-block flex">
                <input type="number" placeholder="请输入" min="0"  id="limitedRatio" class="sex-ratio no-spinners" />
            </div>
        </div>
<!--        <div class="adjust-item adjust-text hide">-->
<!--            <div class="limited-number">-->
<!--                <span>男生限选人数：</span><em>15</em>-->
<!--            </div>-->
<!--            <div class="limited-number">-->
<!--                <span>女生限选人数：</span><em>15</em>-->
<!--            </div>-->
<!--        </div>-->
    </div>
    <div class="dialog-btn">
        <button class="pu-cancel" id="stuCancel">取消</button>
        <button class="pu-sure" id="stuSure">确定</button>
    </div>
</div>
</body>
<script th:src="${_CPR_}+'/elective/js/jquery-3.3.1.min.js'"></script>
<script th:src="${_CPR_}+'/elective/js/common_capacity_update.js'"></script>
<script th:src="${_CPR_}+'/elective/layui/layui.js'"></script>
<script>
    layui.use(['layer'], function () {
        var layer = layui.layer;
        $("#adjustType li").click(function () {
            $(this).addClass('active').siblings().removeClass('active');
            var hasSel = $(this).hasClass('active');
            var index = $(this).index();
            if (index == 0) {
                $('.adjust-percent').css({'display': hasSel ? 'flex' : 'none'});
                $('.adjust-capacity').css({'display': !hasSel ? 'flex' : 'none'})
            } else {
                $('.adjust-percent').css({'display': !hasSel ? 'flex' : 'none'});
                $('.adjust-capacity').css({'display': hasSel ? 'flex' : 'none'})
            }
        })
        $("#stuCancel,.pu-close").click(function () {
            window.parent.postMessage(JSON.stringify({action: 1}), "*");
        })
        // 点击确定
        $("#stuSure").click(function () {
            var type = $("#adjustType li.active").attr('value');
            var percent = $(".capacityCount").val();
            var capacity = $(".capacityCount1").val();
            var cap = type == '1' ? percent : capacity;
            // if (cap == '' || cap <= 0) {
            //     layer.msg("请输入合法容量")
                // return false;
            // }
            $.ajax({
                type: "POST",
                url: "/elective/course/rule/update/capacity?uid=[[${uid}]]&queryId=[[${queryId}]]&fid=[[${fid}]]&capacity="+cap,
                data: {type: type, capacity: cap,studentGender:$("#studentGender").val(),limitedRatio:$("#limitedRatio").val()},
                dataType: 'json',
                success: function (data) {
                    if (data.status) {
                        location.href = "/elective/course/rule/update/result?queryId=[[${queryId}]]";
                    } else {
                        layer.msg(data.msg);
                    }
                }
            });
        })


        //可选学生性别选择

        $(".j-search-con.single-box .j-select-year ul li").click(function () {
            let index = $(this).index();

            if (index == 0) {
                $(".dialog#adjustCourse .adjust-item.adjust-ratio").removeClass("hide");
                $(".dialog#adjustCourse .adjust-item.adjust-text").removeClass("hide");
            } else {
                $(".dialog#adjustCourse .adjust-item.adjust-ratio").addClass("hide");
                $(".dialog#adjustCourse .adjust-item.adjust-text").addClass("hide");
            }
        })
    })
</script>

</html>