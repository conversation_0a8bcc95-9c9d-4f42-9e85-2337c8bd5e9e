<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no,initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <title></title>
    <script src="js/responsive.js"></script>
    <link rel="stylesheet" href="css/global.css">
    <link rel="stylesheet" href="css/iosSelect.css">
    <link rel="stylesheet" href="css/commonV1.css">
    <link rel="stylesheet" href="css/staV1.css">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/layui/css/layui.css'">
    <!-- 2024.11.21 -->
    <link rel="stylesheet" href="css/xzb_detail_sta.css?v=3">
    <link rel="stylesheet" href="css/xzb_detail_head.css?v=2">
    <!-- 2024.11.21 -->
</head>

<body>
<!-- 2024.11.21 -->
<div class="header">
    <div class="head-signal"></div>
    <div class="head-con">
        <a href="javascript:void(0)" onclick="AppUtils.closeView();" class="head-return">
            <img src="images/back-icon1.png" alt="">
        </a>
        <span class="head-title">
                <span>[[${uname}]]</span>
            </span>
        <span class="head-right" th:if="${bzr}">
                <span>修改点名</span>
            </span>
    </div>
</div>
<!-- 2024.11.21 -->

<div class="stu-list-wrap">
    <div class="class-mes">
        <ul>
            <li>正常:<i>0</i></li>
            <li th:each="state:${rollcallStateList}" th:inline="text">[[${state.stateNamePc}]]:<i>0</i></li>
        </ul>
    </div>
    <div class="total">
        <span>共<i>0</i>条记录</span>
    </div>
    <div class="stu-list">
    </div>
</div>

<!-- 2024.11.21 -->
<div class="marsks"></div>
<div class="bot-window modify-call">
    <div class="mc-head">
        <div class="cancle">取消</div>
        <div class="title">修改点名结果</div>
        <div class="save">保存</div>
    </div>
    <div class="bot-list">
        <ul class="types">
            <li class="single">正常</li>
            <li th:each="state:${rollcallStateList}"
                th:class="${state.stateAlias == 'cd' || state.stateAlias == 'zt'} ? 'multi':'single'"
                th:text="${state.stateNamePc}"></li>
            <!--            <li class="multi">迟到</li>-->
            <!--            <li class="single">旷课</li>-->
            <!--            <li class="single">请假</li>-->
            <!--            <li class="multi">早退</li>-->
        </ul>
    </div>

</div>

<div class="save-change">
    <div class="save-text">保存修改</div>
</div>

<!-- 2024.11.21 -->
</body>
<script th:src="${_CPR_}+'/elective/js/jquery-3.3.1.min.js'"></script>
<script src="js/CXJSBridge.js"></script>
<script src="js/iosSelect.js"></script>
<script type="text/javascript" src="/js/app.utils.js"></script>
<script src="js/zepto.min.js"></script>
<script src="js/headHeight.js?v=1"></script><!-- 2024.11.21 -->
<!--<script src="https://cdn.bootcss.com/vConsole/3.2.0/vconsole.min.js"></script>-->
<script th:src="${_CPR_}+'/elective/layui/layui.js'"></script>
<script th:inline="javascript">
    layui.use(['layer'], function () {
        var layer = layui.layer;
    })
    // var vConsole = new VConsole();
    var courseTime = [[${courseTime}]];
    var uid = [[${uid}]];
    var rollcallStateList = [[${rollcallStateList}]];
    var stateCss = ["late", "truant", "ask-leave", "leave-early", "others"];
    getData();

    //2024.11.21
    function _jsBridgeReady() {
        jsBridge.postNotification('CLIENT_TOOLBAR_TYPE', {
            'toolbarType': 0
        });
        jsBridge.postNotification('CLIENT_WEB_BOUNCES', {
            "forbiddenFlag": 1
        });
    }

    setTimeout(function () {
        var stuH = $(window).height() - $(".stu-list-wrap").offset().top;
        $(".stu-list-wrap").css({
            height: stuH + "px"
        })
    }, 500)

    $(".bot-window .bot-list ul li.single").click(function () {
        $(this).addClass("cur").siblings().removeClass("cur");
    })

    $(".bot-window .bot-list ul li.multi").click(function () {
        $(".bot-window .bot-list ul li.single").removeClass("cur");
        $(this).toggleClass("cur");
    })

    //修改改名
    $(".head-right span").click(function () {
        $(".stu-list-wrap .stu-list").addClass("editable");
        $(".save-change").show();
    })

    var editid = 0;
    var editData = [];
    //保存修改
    $(".save-change .save-text").click(function () {
        if (editData.length > 0) {
            editRollCallData();
        }
        $(".stu-list-wrap .stu-list").removeClass("editable");
        $(".save-change").hide();
    })

    //点名状态修改
    $(".stu-list-wrap").on("click", ".editable .stu .state", function () {
        let text = $(this).find("span").text();

        let textArr = text.split(',');
        $(".bot-window .bot-list ul li").removeClass("cur");
        console.log(textArr.length);

        if (textArr.length == 1) {
            $(".bot-window .bot-list ul li").each(function () {
                if ($(this).text() == text) {
                    $(this).addClass("cur").siblings().removeClass("cur");
                }
            })
        } else {
            for (let i = 0; i < textArr.length; i++) {
                $(".bot-window .bot-list ul li").each(function () {
                    if ($(this).text() == textArr[i]) {
                        $(this).addClass("cur");
                    }
                })
            }
        }

        editid = $(this).parent().attr("data-id");
        $(this).attr("id", 'objectId');
        $(".marsks").show();
        $(".modify-call").addClass("move");
    })

    //取消修改状态
    $(".bot-window.modify-call .mc-head .cancle").click(function () {
        $(".marsks").hide();
        $(".modify-call").removeClass("move");
        $("#objectId").removeAttr("id");
    })

    //保存修改状态
    $(".bot-window.modify-call .mc-head .save").click(function () {

        let numbers = $(".bot-window .bot-list ul li.cur").length;
        let className = 'normal';
        var dataState = {id: editid, cd: '0', zt: '0', kk: '0', qj: '0', state: '0'};
        let text = '';
        console.log(numbers);
        if (numbers == 1) {
            text = $(".bot-window .bot-list ul li.cur").text();
            for (let j = 0; j < rollcallStateList.length; j++) {
                if (rollcallStateList[j].stateNamePc == text) {
                    className = stateCss[j];
                    if (rollcallStateList[j].type == 1) {
                        dataState.state = rollcallStateList[j].id;
                    } else {
                        switch (text) {
                            case '请假':
                                dataState.qj = '1';
                                break;
                            case '旷课':
                                dataState.kk = '1';
                                break;
                            case '迟到':
                                dataState.cd = '1';
                                break;
                            case '早退':
                                dataState.zt = '1';
                                break;
                        }
                    }
                }
            }
        } else if (numbers == 2) {

            let textArr = [];
            $(".bot-window .bot-list ul li.cur").each(function () {
                textArr.push($(this).text());
            })
            dataState.cd = '1';
            dataState.zt = '1';
            text = textArr.join(",");
            for (let j = 0; j < rollcallStateList.length; j++) {
                if (rollcallStateList[j].stateNamePc == "迟到" || rollcallStateList[j].stateNamePc == "早退") {
                    className = stateCss[j];
                    break;
                }
            }
        }


        $("#objectId").attr("class", "state " + className);
        $("#objectId").attr("class", "state " + className).find("span").text(text);


        $(".marsks").hide();
        $(".modify-call").removeClass("move");
        $("#objectId").removeAttr("id");

        delEditData(editid);
        editData.push(dataState);
        console.log(editData)
    })

    // $(document).ready(function () {
    //     var stuH = $(window).height() - $(".stu-list-wrap").offset().top;
    //     $(".stu-list-wrap").css({height: stuH + "px"})
    // })
    function getData() {
        $.ajax({
            type: "POST",
            url: "/elective/rollcall/getXzbStuStaticNum",
            data: {courseTime: courseTime, uid: uid},
            dataType: 'json',
            success: function (data) {
                for (let i = 0; i < data.data.length; i++) {
                    $(".class-mes ul li").eq(i).find("i").text(data.data[i]);
                }
            }
        });
        $.ajax({
            type: "POST",
            url: "/elective/rollcall/getXzbStuDetailList",
            data: {courseTime: courseTime, uid: uid},
            dataType: 'json',
            success: function (data) {
                if (data.status) {
                    var html = "";
                    var zcHtml = "<div class='state normal'><span>正常";
                    var cdHtml = "<div class='state late'><span>";
                    var kkHtml = "<div class='state absenteeism'><span>";
                    var qjHtml = "<div class='state leave'><span>";
                    var ztHtml = "<div class='state early-departure'><span>";
                    var otHtml = "<div class='state others'><span>";
                    for (let i = 0; i < data.data.length; i++) {
                        var tempHtml = "<div class='stu' data-id = '" + data.data[i].id + "'>" +
                            "<div class='grace'></div>" +
                            "<div class='course-name'>" + data.data[i].course + "</div>" +
                            "<div class='inform'>" +
                            "<span>第" + data.data[i].zc + "周</span>" +
                            "<span>周" + data.data[i].xq + "</span>" +
                            "<span>第" + data.data[i].kj + "节</span>" +
                            "</div>";
                        if (data.data[i].val) {
                            tempHtml += "<div class='evaluate'>";
                            tempHtml += "   <div class='name'>评价：</div>";
                            tempHtml += "    <div class='text'>" + data.data[i].val + "</div>";
                            tempHtml += "</div>";
                        }
                        if (data.data[i].cd == 0 && data.data[i].kk == 0 && data.data[i].zt == 0 && data.data[i].qj == 0 && data.data[i].state == 0) {
                            html += tempHtml + zcHtml;
                            html += "</span><i></i></div></div>";
                        } else {
                            for (let j = 0; j < rollcallStateList.length; j++) {
                                if ((rollcallStateList[j].type == 0 && rollcallStateList[j].stateAlias == 'cd' && data.data[i].cd == 1) ||
                                    (rollcallStateList[j].type == 0 && rollcallStateList[j].stateAlias == 'kk' && data.data[i].kk == 1) ||
                                    (rollcallStateList[j].type == 0 && rollcallStateList[j].stateAlias == 'zt' && data.data[i].zt == 1) ||
                                    (rollcallStateList[j].type == 0 && rollcallStateList[j].stateAlias == 'qj' && data.data[i].qj == 1) ||
                                    (rollcallStateList[j].type == 1 && data.data[i].state == rollcallStateList[j].id)) {
                                    let stateName = rollcallStateList[j].stateNamePc;
                                    if (data.data[i].cd == 1 && data.data[i].zt == 1) {
                                        stateName = "迟到,早退";
                                    }
                                    if (j == 0) {
                                        html += tempHtml + cdHtml;
                                    } else if (j == 1) {
                                        html += tempHtml + kkHtml;
                                    } else if (j == 2) {
                                        html += tempHtml + qjHtml;
                                    } else if (j == 3) {
                                        html += tempHtml + ztHtml;
                                    } else if (j == 4) {
                                        html += tempHtml + otHtml;
                                    }
                                    html += stateName;
                                    break;
                                }
                            }
                            html += "</span><i></i></div></div>";
                        }
                    }
                    $(".total").find("i").html(data.data.length);
                    $(".stu-list").html(html);
                } else {
                    jsBridge.postNotification('CLIENT_DISPLAY_MESSAGE', {'message': '请求失败，请重试', 'gravity': '1'});
                }
            }
        });
    }

    function editRollCallData() {
        var loading = layer.load(0, {
            shade: [0.5, '#c0c0c0']
        });
        $.ajax({
            type: "POST",
            url: "/elective/rollcall/editRollCallData",
            contentType: "application/json; charset=utf-8",
            data: JSON.stringify(editData),
            // async:false,
            dataType: 'json',
            success: function (data) {
                layer.close(loading);
                layer.msg("修改成功")
                getData()
                jsBridge.postNotification('CLIENT_REFRESH_STATUS', {'status': '1'});
            }
        })
    }

    function delEditData(id) {
        $.each(editData, function (index, item) {
            if (item != undefined && item.id == id) {
                editData.splice(index, 1);
            }
        });
    }
</script>

</html>