<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量开课</title>
    <link rel="stylesheet" th:href="@{../plugin/layui/css/layui.css}">
    <script th:src="@{../plugin/layui/layui.js}"></script>
</head>
<body>
<form class="layui-form" style="width: 500px;height: 300px;margin: 40px 40px;">
    <div class="layui-form-item">
        <label class="layui-form-label">学时类型</label>
        <div class="layui-input-block">
            <input type="radio" name="classHourType" value="总学时" title="总学时" lay-filter="aaa">
            <input type="radio" name="classHourType" value="理论学时" title="理论学时" checked lay-filter="aaa">
            <input type="radio" name="classHourType" value="实验学时" title="实验学时" checked lay-filter="aaa">
            <input type="radio" name="classHourType" value="上机学时" title="上机学时" checked lay-filter="aaa">
            <input type="radio" name="classHourType" value="实践学时" title="实践学时" checked lay-filter="aaa">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">教室类型</label>
        <div class="layui-input-block">
            <select name="classRoomType" lay-verify="required">
                <option value=""></option>
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">周次配置</label>
        <div class="layui-input-block">
            <input type="text" name="scheduleWeek" lay-verify="required" autocomplete="off"
                   class="layui-input">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">连排节次</label>
        <div class="layui-input-block">
            <input type="text" name="lpSection" lay-verify="required" autocomplete="off"
                   class="layui-input">
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-input-block">
            <button class="layui-btn" lay-submit lay-filter="btn-complate">提交</button>
            <button type="reset" class="layui-btn layui-btn-primary">取消</button>
        </div>
    </div>
</form>
</body>
<script th:src="@{../js/jquery-1.11.3.min.js}"></script>
<script th:inline="javascript">
    let queryId = [[${queryId}]];
    let uid = [[${uid}]];
    let fid = [[${fid}]];
    layui.use(["form"], function () {
        var form = layui.form;
        getData("classRoomType", "../processData/getDictData?dictType=JSLX");
        form.on('submit(btn-complate)', function (data) {
            if (!/[0-9]+[-]{1}[0-9]+[:]{1}[0-9]+/.test(data.field.scheduleWeek)) {
                layer.msg("安排周次输入格式有误,请按照1-1:4,2-20:2的格式输入", {icon: 2, time: 2000});
                flag = false;
                return false;
            }
            data.field.queryId = queryId;
            data.field.fid = fid;
            data.field.uid = uid;
            $.post("../processData/batchFastClassStarts", data.field, function (result) {
                layer.msg("开课成功", {icon: 1, time: 2000});
                for (let i = 0; i < result.infos.length; i++) {
                    const info = result.infos[i];
                    addCourseInformation(info.id, fid);
                    updFastClassStartsForm(info.id, info.teachPlanNo, info.code);
                }
                setTimeout(function () {
                    window.parent.postMessage(JSON.stringify({action: 1}), "*");
                }, 2000)
            }, "json");
            return false;
        })
    })

    function addCourseInformation(detailId) {
        $.post("../processData/addCourseInformation", {detailId: detailId, fid: fid, type: 1}, function (result) {
        }, "json");
    }

    function updFastClassStartsForm(id, teachPlanNo, code) {
        $.post("../processData/updFastClassStartsForm", {
            fid: fid,
            id: id,
            kskkJxjhbh: teachPlanNo,
            kskkWaprs:0,
            classNo: code,
            appName: "522441",
            flag: "是"
        }, function (result) {
        }, "json");
    }

    $(".layui-btn-primary").click(function () {
        window.parent.postMessage(JSON.stringify({action: 1}), "*");
    })

    function getData(selName, url) {
        $.post(url, function (result) {
            for (let i = 0; i < result.list.length; i++) {
                const option = result.list[i];
                const name = option.dictName;
                $("select[name=\"" + selName + "\"]").append(new Option(name, name));
            }
            layui.form.render('select');
        }, "json");
    }

</script>
</html>