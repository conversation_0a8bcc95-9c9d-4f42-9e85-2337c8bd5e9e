<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
    <head>
        <meta charset="UTF-8"/>
        <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
        <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
        <title>信息上传</title>
        <link rel="stylesheet" th:href="@{~/css/form/global.css}">
        <link rel="stylesheet" th:href="@{~/plugin/layui/css/layui.css}">
        <link rel="stylesheet" th:href="@{~/css/form/jquery.mCustomScrollbar.css}">
        <link rel="stylesheet" th:href="@{~/css/form/index.css}">
        <script th:src="@{~/plugin/layui/layui.js}"></script>
    </head>
    <body>
        <div class="yz-header">
            <div class="name">信息上传</div>
        </div>
        <div class="yz-con">
            <div class="yz-scroll layui-form">
                <div class="layui-input-inline" style="display: block; margin:0 16px 15px;">
                    <select name="typeCover" lay-filter="typeCover" lay-verify="required" id="formListSelect">
                        <!--                        <option value="0">全部</option>-->
                    </select>
                </div>
                <div class="yz-menu">
                    <div class="menu-list" id="menu-list">
                        <input type="text" id="invite_code" value="testAlias" style="display: none;">
                        <ul id="fieldListUl">
                            <li>
                                <div class="name">努力加载中...</div>
                                <div class="copy">点击复制：testAlias
                                    <input type="hidden" class="alias" value="testAlias">
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="yz-main" style="display:block;">
                <div class="yz-cons">
                    <div class="layui-form">
                        <div class="layui-form-item">
                            <div class="layui-input-inline">
                                <div class="upload-files">
                                    <button class="layui-btn layui-chaoxing-default-btn" type="button" id="uploadA"
                                            name="testFile">
                                        <span class="select-file">上传</span>
                                    </button>
                                    <span class="texts">未选择任何文件</span>
                                </div>
                                <div class="data-shows hide">
                                    <ul>
                                        <li>
                                            <span></span>
                                            <i class="layui-icon layui-icon-close"></i>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="cons"></div>
                        <div class="submit" lay-submit lay-filter="demo-submit">提交</div>
                    </div>
                </div>
            </div>
        </div>
        <script th:src="@{~/js/jquery-3.3.1.min.js}"></script>
        <script th:src="@{~/js/form/jquery.mCustomScrollbar.concat.min.js}"></script>
        <script>
            $(function () {
                let fid = [[${fid}]];
                let uid = [[${uid}]];

                /**
                 * 记录选中表单的表单名称和接口标识
                 */
                let formNameAndAliasSelect = "---";
                /**
                 * 表单名称
                 */
                let formName = "---";
                /**
                 * 记录选中表单的接口标识
                 */
                let formAlias = "---";


                console.log(fid + "---" + uid);
                // 侧边栏点击
                $(".yz-con").on("click", ".yz-menu .menu-list ul li", function () {
                    $(this).addClass("cur").siblings().removeClass("cur");
                });

                ifHH();

                function ifHH() {
                    let iframe = $(window).height() - $(".yz-header").height() - 40;
                    $(".yz-scroll").css("height", iframe);
                    $(".yz-menu").css("height", iframe - 60);
                }

                $(window).resize(function () {
                    ifHH();
                });

                //左侧滚动条
                $(".yz-menu").mCustomScrollbar({
                    axis: "y"
                });

                $(".layui-form-item").on("click", ".layui-input-inline .data-shows ul li .layui-icon", function () {
                    $(this).parent().remove();
                });

                $(".yz-con").on("click", ".yz-menu .menu-list ul li .label-top", function () {
                    if ($(this).hasClass("no-children")) {
                        $(".yz-con .yz-menu .menu-list ul li .cur").removeClass("cur");
                        $(this).addClass("cur");
                    } else {
                        $(this).toggleClass("active");
                        $(this).parent().find(".label-con").stop().slideToggle();
                    }
                });

                $(".yz-con").on("click", ".yz-menu .menu-list ul li .label-con .item-list .item", function () {
                    $(".yz-con .yz-menu .menu-list ul li .cur").removeClass("cur");
                    $(this).addClass("cur");
                });

                // 复制
                $(".yz-con").on("click", ".yz-menu .menu-list ul li .copy", function (e) {
                    var element = e.target;
                    var content = $(element).find("input.alias")[0].value;
                    $("#invite_code").val("${" + content + "}");
                    copyToClipboard('invite_code');
                    stopBubble(e);
                });

                function stopBubble(e) {
                    if (e && e.stopPropagation)
                        e.stopPropagation();
                    else {
                        window.event.cancelBubble = true;
                    }
                }

                /**
                 * 复制指定id的元素的内容到剪贴板
                 */
                function copyToClipboard(elementId) {
                    // 创建元素用于复制
                    var aux = document.createElement("input");
                    // 获取复制内容
                    var content = document.getElementById(elementId).value;
                    // 设置元素内容
                    aux.setAttribute("value", content);
                    // 将元素插入页面进行调用
                    document.body.appendChild(aux);
                    // 复制内容
                    aux.select();
                    //复制选中的文字到剪贴板;
                    // document.execCommand('copy');
                    try {
                        document.execCommand('copy');
                        // 删除创建元素
                        document.body.removeChild(aux);
                        layer.msg("复制成功", {icon: 1, time: 2000});
                    } catch (exception) {
                        layer.msg("复制失败", {icon: 2, time: 2000});
                    }
                }

                layui.use(['form', 'upload', 'table'], function () {
                    var $ = layui.jquery;
                    var form = layui.form;
                    var upload = layui.upload;
                    var table = layui.table;

                    // 初始化表单下拉框列表值
                    $.ajax({
                        type: 'post',
                        dataType: 'json',
                        url: '/api/form/info/upload/form/list',
                        data: {},
                        success: function (res) {
                            if (res.code === 200) {
                                renderFormListSelect(res.data);
                            } else {
                                layer.msg(res.msg, {icon: 2, time: 2000});
                            }
                        }
                    });

                    // 绑定表单下拉框的列表项点击事件
                    form.on('select(typeCover)', function (data) {
                        var elem = data.elem; // 获得 select 原始 DOM 对象
                        var value = data.value; // 获得被选中的值
                        var othis = data.othis; // 获得 select 元素被替换后的 jQuery 对象
                        // layer.msg(this.innerHTML + ' 的 value: ' + value); // this 为当前选中 <option> 元素对象
                        // 渲染点击的表单字段到侧边栏
                        renderSingleFormField(value);
                        formNameAndAliasSelect = this.innerHTML + "---" + value;
                        formName = this.innerHTML;
                        formAlias = value;
                    });

                    /**
                     * 渲染表单下拉框
                     */
                    function renderFormListSelect(formList) {
                        if (!formList || formList.length === 0) {
                            return;
                        }
                        // $("#formListSelect").empty();
                        for (let i = 0; i < formList.length; i++) {
                            var single = formList[i];
                            $("#formListSelect").append("<option value='" + single.appName + "'>" + single.name + "</option>");
                        }
                        // 重新渲染下拉框，并渲染第一个表单的字段到侧边栏
                        form.render('select');
                        if (formList.length > 0) {
                            var firstForm = formList[0];
                            renderSingleFormField(formList[0].appName);
                            formNameAndAliasSelect = firstForm.name + "---" + firstForm.appName;
                            formName = firstForm.name;
                            formAlias = firstForm.appName;
                        }
                    }

                    /**
                     * 渲染根据表单标识，渲染表单字段列表栏
                     */
                    function renderSingleFormField(appName) {
                        // 初始化表单下拉框列表值
                        $.ajax({
                            type: 'post',
                            dataType: 'json',
                            url: '/api/form/info/upload/form/detail',
                            data: {"appName": appName},
                            success: function (res) {
                                if (res.code === 200) {
                                    renderFormDetailInfo(res.data);
                                } else {
                                    layer.msg(res.msg, {icon: 2, time: 2000});
                                }
                            }
                        });
                    }

                    /**
                     * 渲染表单字段列表栏
                     */
                    function renderFormDetailInfo(formFieldList) {
                        if (!formFieldList || formFieldList.length === 0) {
                            return;
                        }
                        $("#fieldListUl").empty();
                        for (let i = 0; i < formFieldList.length; i++) {
                            var formField = formFieldList[i];
                            var html = "";
                            if (formField.type === "normal") {
                                html += "<li>" +
                                    "<div class='label-top no-children'>" +
                                    "   <div class='name'>" + formField.label + "</div>" +
                                    "   <div class='copy' style='float: right'>复制：" + formField.alias +
                                    "       <input type='hidden' class='alias' value='" + formField.alias + "'>" +
                                    "   </div>" +
                                    "</div>" +
                                    "</li>";
                            } else if (formField.type === "subForm") {
                                html += "<li>" +
                                    "<div class='label-top'>" +
                                    "   <div class='name'>" + formField.label + "</div>" +
                                    "   <span class='slide-arrow'></span>" +
                                    "</div>" +
                                    "<div class='label-con' style='display: none'>" +
                                    "   <div class='item-list'>";
                                var subFieldList = formField.subFieldList;
                                if (subFieldList && subFieldList.length !== 0) {
                                    for (var j = 0; j < subFieldList.length; j++) {
                                        var subField = subFieldList[j];
                                        html += "<div class='item'>" +
                                            "   <div class='name'>" + subField.label + "</div>" +
                                            "   <div class='copy'>复制：" + subField.alias +
                                            "       <input type='hidden' class='alias' value='" + subField.alias + "'>" +
                                            "   </div>" +
                                            "</div>";
                                    }
                                }
                                html += "</div>" +
                                    "</div>" +
                                    "</li>";
                            }
                            $("#fieldListUl").append(html);
                        }
                    }

                    /**
                     * 点击提交按钮触发
                     */
                    form.on('submit(demo-submit)', function (data) {
                        var field = data.field; // 获取表单全部字段值
                        var elem = data.elem; // 获取当前触发事件的元素 DOM 对象，一般为 button 标签
                        var elemForm = data.form; // 获取当前表单域的 form 元素对象，若容器为 form 标签才会返回。
                        // 显示填写结果，仅作演示用
                        // layer.alert(JSON.stringify(field), {
                        //     title: '当前填写的字段值'
                        // });
                        if (!field.file) {
                            layer.msg("请先上传文件", {icon: 2, time: 2000});
                            return;
                        }
                        layer.confirm('确认上传【' + formNameAndAliasSelect + "】的模板文件【" + field.file.substr(field.file.lastIndexOf("\\") + 1) + "】？", {
                            btn: ['确定', '取消'] //按钮
                        }, function () {
                            // layer.msg('第一个回调', {icon: 1});
                            inst.upload();
                            // form.submit();
                        }, function () {
                            layer.msg('已取消上传模板文件', {
                                time: 2000, // 20s 后自动关闭
                                // btn: ['明白了', '知道了']
                            });
                        });
                        return false; // 阻止默认 form 跳转
                    });
                    // 返回渲染上传组件实体
                    var inst = renderUpload();

                    /**
                     * 渲染上传组件
                     */
                    var UPLOAD_FILES;

                    function renderUpload() {
                        return upload.render({
                            elem: '#uploadA',
                            url: '/api/form/info/upload/file/submit',
                            auto: false,
                            accept: 'file', //普通文件
                            exts: 'pdf|docx',
                            done: function (res) {
                                let fileName = $("#uploadA").parents(".layui-form-item").find(".data-shows ul li span").text();
                                if (res.code === 200) {
                                    layer.msg("上传成功：" + fileName, {icon: 1, time: 2000});
                                    clearFile();
                                } else {
                                    layer.msg(res.msg, {icon: 2, time: 2000});
                                }
                                $("#uploadA").parent().find("input").val('');
                                $("#uploadA").parent().find(".texts").show();
                                $("#uploadA").parents(".layui-form-item").find(".data-shows").addClass("hide");
                                $("#uploadA").parents(".layui-form-item").find(".data-shows ul li span").text("");
                            },
                            data: {
                                formName: function () {
                                    return formName;
                                },
                                formAlias: function () {
                                    return formAlias;
                                }
                            },
                            error: function () {
                            },
                            choose: function (obj) {
                                // 清空原来的文件，然后把新选择的文件填入
                                UPLOAD_FILES = obj.pushFile();
                                obj.preview(function (index, file, result) {
                                    let texts = file.name;
                                    $("#uploadA").parent().find(".texts").hide();
                                    $("#uploadA").parents(".layui-form-item").find(".data-shows").removeClass("hide");
                                    $("#uploadA").parents(".layui-form-item").find(".data-shows ul li span").text(texts);
                                });
                            }
                        });
                    }

                    //清空文件队列
                    function clearFile() {
                        for (let x in UPLOAD_FILES) {
                            delete UPLOAD_FILES[x];
                        }
                    }
                });
            });
        </script>
    </body>
</html>