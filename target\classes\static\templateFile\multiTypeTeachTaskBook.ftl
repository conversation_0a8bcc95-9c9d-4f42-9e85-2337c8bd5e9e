<?mso-application progid="Word.Document"?>
<pkg:package xmlns:pkg="http://schemas.microsoft.com/office/2006/xmlPackage">
    <pkg:part pkg:name="/_rels/.rels" pkg:contentType="application/vnd.openxmlformats-package.relationships+xml">
        <pkg:xmlData>
            <Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
                <Relationship Id="rId4" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument" Target="word/document.xml"/>
                <Relationship Id="rId2" Type="http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties" Target="docProps/core.xml"/>
                <Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties" Target="docProps/app.xml"/>
                <Relationship Id="rId3" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties" Target="docProps/custom.xml"/>
            </Relationships>
        </pkg:xmlData>
    </pkg:part>
    <pkg:part pkg:name="/word/_rels/document.xml.rels" pkg:contentType="application/vnd.openxmlformats-package.relationships+xml">
        <pkg:xmlData>
            <Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
                <Relationship Id="rId8" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/fontTable" Target="fontTable.xml"/>
                <Relationship Id="rId7" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/image" Target="media/image2.png"/>
                <Relationship Id="rId6" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/image" Target="media/image1.png"/>
                <Relationship Id="rId5" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme" Target="theme/theme1.xml"/>
                <Relationship Id="rId4" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/endnotes" Target="endnotes.xml"/>
                <Relationship Id="rId3" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/footnotes" Target="footnotes.xml"/>
                <Relationship Id="rId2" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/settings" Target="settings.xml"/>
                <Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles" Target="styles.xml"/>
            </Relationships>
        </pkg:xmlData>
    </pkg:part>
    <pkg:part pkg:name="/word/document.xml" pkg:contentType="application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml">
        <pkg:xmlData>
            <w:document xmlns:wpc="http://schemas.microsoft.com/office/word/2010/wordprocessingCanvas" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:wp14="http://schemas.microsoft.com/office/word/2010/wordprocessingDrawing" xmlns:wp="http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing" xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main" xmlns:w14="http://schemas.microsoft.com/office/word/2010/wordml" xmlns:w10="urn:schemas-microsoft-com:office:word" xmlns:w15="http://schemas.microsoft.com/office/word/2012/wordml" xmlns:wpg="http://schemas.microsoft.com/office/word/2010/wordprocessingGroup" xmlns:wpi="http://schemas.microsoft.com/office/word/2010/wordprocessingInk" xmlns:wne="http://schemas.microsoft.com/office/word/2006/wordml" xmlns:wps="http://schemas.microsoft.com/office/word/2010/wordprocessingShape" xmlns:wpsCustomData="http://www.wps.cn/officeDocument/2013/wpsCustomData" mc:Ignorable="w14 w15 wp14">
                <#list itemList?keys as key>
                    <w:body>
                        <w:p>
                            <w:pPr>
                                <w:spacing w:line="352" w:lineRule="exact"/>
                                <w:ind w:firstLine="3102"/>
                            </w:pPr>
                            <w:r>
                                <w:rPr>
                                    <w:position w:val="-7"/>
                                </w:rPr>
                                <w:drawing>
                                    <wp:inline distT="0" distB="0" distL="0" distR="0">
                                        <wp:extent cx="2269490" cy="223520"/>
                                        <wp:effectExtent l="0" t="0" r="0" b="0"/>
                                        <wp:docPr id="2" name="IM 2"/>
                                        <wp:cNvGraphicFramePr/>
                                        <a:graphic xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main">
                                            <a:graphicData uri="http://schemas.openxmlformats.org/drawingml/2006/picture">
                                                <pic:pic xmlns:pic="http://schemas.openxmlformats.org/drawingml/2006/picture">
                                                    <pic:nvPicPr>
                                                        <pic:cNvPr id="2" name="IM 2"/>
                                                        <pic:cNvPicPr/>
                                                    </pic:nvPicPr>
                                                    <pic:blipFill>
                                                        <a:blip r:embed="rId6"/>
                                                        <a:stretch>
                                                            <a:fillRect/>
                                                        </a:stretch>
                                                    </pic:blipFill>
                                                    <pic:spPr>
                                                        <a:xfrm>
                                                            <a:off x="0" y="0"/>
                                                            <a:ext cx="2269972" cy="223878"/>
                                                        </a:xfrm>
                                                        <a:prstGeom prst="rect">
                                                            <a:avLst/>
                                                        </a:prstGeom>
                                                    </pic:spPr>
                                                </pic:pic>
                                            </a:graphicData>
                                        </a:graphic>
                                    </wp:inline>
                                </w:drawing>
                            </w:r>
                        </w:p>
                        <w:p>
                            <w:pPr>
                                <w:spacing w:before="175" w:line="350" w:lineRule="exact"/>
                                <w:ind w:firstLine="3614"/>
                            </w:pPr>
                            <w:r>
                                <w:rPr>
                                    <w:position w:val="-7"/>
                                </w:rPr>
                                <w:drawing>
                                    <wp:inline distT="0" distB="0" distL="0" distR="0">
                                        <wp:extent cx="1583055" cy="222250"/>
                                        <wp:effectExtent l="0" t="0" r="0" b="0"/>
                                        <wp:docPr id="4" name="IM 4"/>
                                        <wp:cNvGraphicFramePr/>
                                        <a:graphic xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main">
                                            <a:graphicData uri="http://schemas.openxmlformats.org/drawingml/2006/picture">
                                                <pic:pic xmlns:pic="http://schemas.openxmlformats.org/drawingml/2006/picture">
                                                    <pic:nvPicPr>
                                                        <pic:cNvPr id="4" name="IM 4"/>
                                                        <pic:cNvPicPr/>
                                                    </pic:nvPicPr>
                                                    <pic:blipFill>
                                                        <a:blip r:embed="rId7"/>
                                                        <a:stretch>
                                                            <a:fillRect/>
                                                        </a:stretch>
                                                    </pic:blipFill>
                                                    <pic:spPr>
                                                        <a:xfrm>
                                                            <a:off x="0" y="0"/>
                                                            <a:ext cx="1583217" cy="222263"/>
                                                        </a:xfrm>
                                                        <a:prstGeom prst="rect">
                                                            <a:avLst/>
                                                        </a:prstGeom>
                                                    </pic:spPr>
                                                </pic:pic>
                                            </a:graphicData>
                                        </a:graphic>
                                    </wp:inline>
                                </w:drawing>
                            </w:r>
                        </w:p>
                        <w:p>
                            <w:pPr>
                                <w:pStyle w:val="2"/>
                                <w:spacing w:before="199" w:line="183" w:lineRule="auto"/>
                                <w:ind w:left="29"/>
                            </w:pPr>
                            <w:r>
                                <w:rPr>
                                    <w:b/>
                                    <w:bCs/>
                                    <w:spacing w:val="-5"/>
                                    <w:u w:val="single"/>
                                </w:rPr>
                                <w:t>${key?split("|")[1]}</w:t>
                            </w:r>
                            <w:r>
                                <w:rPr>
                                    <w:b/>
                                    <w:bCs/>
                                    <w:spacing w:val="-5"/>
                                </w:rPr>
                                <w:t>老师：</w:t>
                            </w:r>
                        </w:p>
                        <w:p>
                            <w:pPr>
                                <w:pStyle w:val="2"/>
                                <w:spacing w:before="314" w:line="184" w:lineRule="auto"/>
                                <w:ind w:left="313"/>
                                <w:outlineLvl w:val="0"/>
                            </w:pPr>
                            <w:r>
                                <w:rPr>
                                    <w:b/>
                                    <w:bCs/>
                                    <w:spacing w:val="-1"/>
                                </w:rPr>
                                <w:t>经研究决定，现将您在${key?split("|")[2]}学期的授课任务安排如下，请作好上课准备。</w:t>
                            </w:r>
                        </w:p>
                        <w:p>
                            <w:pPr>
                                <w:spacing w:before="54"/>
                            </w:pPr>
                        </w:p>
                        <w:tbl>
                            <w:tblPr>
                                <w:tblStyle w:val="6"/>
                                <w:tblW w:w="9921" w:type="dxa"/>
                                <w:jc w:val="center"/>
                                <w:tblBorders>
                                    <w:top w:val="single" w:color="000000" w:sz="6" w:space="0"/>
                                    <w:left w:val="single" w:color="000000" w:sz="6" w:space="0"/>
                                    <w:bottom w:val="single" w:color="000000" w:sz="6" w:space="0"/>
                                    <w:right w:val="single" w:color="000000" w:sz="6" w:space="0"/>
                                    <w:insideH w:val="single" w:color="000000" w:sz="6" w:space="0"/>
                                    <w:insideV w:val="single" w:color="000000" w:sz="6" w:space="0"/>
                                </w:tblBorders>
                                <w:tblLayout w:type="fixed"/>
                                <w:tblCellMar>
                                    <w:top w:w="0" w:type="dxa"/>
                                    <w:left w:w="0" w:type="dxa"/>
                                    <w:bottom w:w="0" w:type="dxa"/>
                                    <w:right w:w="0" w:type="dxa"/>
                                </w:tblCellMar>
                            </w:tblPr>
                            <w:tblGrid>
                                <w:gridCol w:w="2268"/>
                                <w:gridCol w:w="2268"/>
                                <w:gridCol w:w="850"/>
                                <w:gridCol w:w="737"/>
                                <w:gridCol w:w="737"/>
                                <w:gridCol w:w="737"/>
                                <w:gridCol w:w="737"/>
                                <w:gridCol w:w="850"/>
                                <w:gridCol w:w="737"/>
                            </w:tblGrid>
                            <w:tr>
                                <w:tblPrEx>
                                    <w:tblBorders>
                                        <w:top w:val="single" w:color="000000" w:sz="6" w:space="0"/>
                                        <w:left w:val="single" w:color="000000" w:sz="6" w:space="0"/>
                                        <w:bottom w:val="single" w:color="000000" w:sz="6" w:space="0"/>
                                        <w:right w:val="single" w:color="000000" w:sz="6" w:space="0"/>
                                        <w:insideH w:val="single" w:color="000000" w:sz="6" w:space="0"/>
                                        <w:insideV w:val="single" w:color="000000" w:sz="6" w:space="0"/>
                                    </w:tblBorders>
                                    <w:tblCellMar>
                                        <w:top w:w="0" w:type="dxa"/>
                                        <w:left w:w="0" w:type="dxa"/>
                                        <w:bottom w:w="0" w:type="dxa"/>
                                        <w:right w:w="0" w:type="dxa"/>
                                    </w:tblCellMar>
                                </w:tblPrEx>
                                <w:trPr>
                                    <w:trHeight w:val="693" w:hRule="atLeast"/>
                                    <w:jc w:val="center"/>
                                </w:trPr>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="2268" w:type="dxa"/>
                                        <w:tcBorders>
                                            <w:top w:val="single" w:color="000000" w:sz="12" w:space="0"/>
                                            <w:left w:val="single" w:color="000000" w:sz="12" w:space="0"/>
                                        </w:tcBorders>
                                        <w:vAlign w:val="top"/>
                                    </w:tcPr>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="7"/>
                                            <w:spacing w:before="256" w:line="183" w:lineRule="auto"/>
                                            <w:ind w:left="56"/>
                                            <w:jc w:val="center"/>
                                        </w:pPr>
                                        <w:r>
                                            <w:rPr>
                                                <w:b/>
                                                <w:bCs/>
                                                <w:spacing w:val="-1"/>
                                            </w:rPr>
                                            <w:t>班次</w:t>
                                        </w:r>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="2268" w:type="dxa"/>
                                        <w:tcBorders>
                                            <w:top w:val="single" w:color="000000" w:sz="12" w:space="0"/>
                                        </w:tcBorders>
                                        <w:vAlign w:val="top"/>
                                    </w:tcPr>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="7"/>
                                            <w:spacing w:before="256" w:line="183" w:lineRule="auto"/>
                                            <w:ind w:left="52"/>
                                            <w:jc w:val="center"/>
                                        </w:pPr>
                                        <w:r>
                                            <w:rPr>
                                                <w:b/>
                                                <w:bCs/>
                                                <w:spacing w:val="-1"/>
                                            </w:rPr>
                                            <w:t>课程名称</w:t>
                                        </w:r>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="850" w:type="dxa"/>
                                        <w:tcBorders>
                                            <w:top w:val="single" w:color="000000" w:sz="12" w:space="0"/>
                                        </w:tcBorders>
                                        <w:vAlign w:val="center"/>
                                    </w:tcPr>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="7"/>
                                            <w:spacing w:before="105" w:line="192" w:lineRule="auto"/>
                                            <w:ind w:left="57"/>
                                            <w:jc w:val="center"/>
                                            <w:rPr>
                                                <w:rFonts w:hint="eastAsia" w:eastAsia="微软雅黑"/>
                                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                            </w:rPr>
                                        </w:pPr>
                                        <w:r>
                                            <w:rPr>
                                                <w:rFonts w:hint="eastAsia"/>
                                                <w:b/>
                                                <w:bCs/>
                                                <w:spacing w:val="-1"/>
                                                <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                            </w:rPr>
                                            <w:t>周学时</w:t>
                                        </w:r>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="737" w:type="dxa"/>
                                        <w:tcBorders>
                                            <w:top w:val="single" w:color="000000" w:sz="12" w:space="0"/>
                                        </w:tcBorders>
                                        <w:vAlign w:val="top"/>
                                    </w:tcPr>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="7"/>
                                            <w:spacing w:before="108" w:line="186" w:lineRule="auto"/>
                                            <w:ind w:left="60" w:right="148" w:firstLine="4"/>
                                            <w:jc w:val="center"/>
                                            <w:rPr>
                                                <w:b/>
                                                <w:bCs/>
                                                <w:spacing w:val="-2"/>
                                            </w:rPr>
                                        </w:pPr>
                                        <w:r>
                                            <w:rPr>
                                                <w:b/>
                                                <w:bCs/>
                                                <w:spacing w:val="-2"/>
                                            </w:rPr>
                                            <w:t>上课</w:t>
                                        </w:r>
                                    </w:p>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="7"/>
                                            <w:spacing w:before="108" w:line="186" w:lineRule="auto"/>
                                            <w:ind w:left="60" w:right="148" w:firstLine="4"/>
                                            <w:jc w:val="center"/>
                                        </w:pPr>
                                        <w:r>
                                            <w:rPr>
                                                <w:b/>
                                                <w:bCs/>
                                                <w:spacing w:val="-2"/>
                                            </w:rPr>
                                            <w:t>周</w:t>
                                        </w:r>
                                        <w:r>
                                            <w:rPr>
                                                <w:b/>
                                                <w:bCs/>
                                            </w:rPr>
                                            <w:t>数</w:t>
                                        </w:r>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="737" w:type="dxa"/>
                                        <w:tcBorders>
                                            <w:top w:val="single" w:color="000000" w:sz="12" w:space="0"/>
                                        </w:tcBorders>
                                        <w:vAlign w:val="top"/>
                                    </w:tcPr>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="7"/>
                                            <w:spacing w:before="108" w:line="186" w:lineRule="auto"/>
                                            <w:ind w:left="69" w:right="120" w:hanging="10"/>
                                            <w:jc w:val="center"/>
                                            <w:rPr>
                                                <w:b/>
                                                <w:bCs/>
                                                <w:spacing w:val="-1"/>
                                            </w:rPr>
                                        </w:pPr>
                                        <w:r>
                                            <w:rPr>
                                                <w:b/>
                                                <w:bCs/>
                                                <w:spacing w:val="-1"/>
                                            </w:rPr>
                                            <w:t>理论</w:t>
                                        </w:r>
                                    </w:p>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="7"/>
                                            <w:spacing w:before="108" w:line="186" w:lineRule="auto"/>
                                            <w:ind w:left="69" w:right="120" w:hanging="10"/>
                                            <w:jc w:val="center"/>
                                        </w:pPr>
                                        <w:r>
                                            <w:rPr>
                                                <w:b/>
                                                <w:bCs/>
                                                <w:spacing w:val="-1"/>
                                            </w:rPr>
                                            <w:t>课</w:t>
                                        </w:r>
                                        <w:r>
                                            <w:rPr>
                                                <w:b/>
                                                <w:bCs/>
                                            </w:rPr>
                                            <w:t>时</w:t>
                                        </w:r>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="737" w:type="dxa"/>
                                        <w:tcBorders>
                                            <w:top w:val="single" w:color="000000" w:sz="12" w:space="0"/>
                                        </w:tcBorders>
                                        <w:vAlign w:val="top"/>
                                    </w:tcPr>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="7"/>
                                            <w:spacing w:before="108" w:line="186" w:lineRule="auto"/>
                                            <w:ind w:left="76" w:right="113" w:hanging="7"/>
                                            <w:jc w:val="center"/>
                                            <w:rPr>
                                                <w:b/>
                                                <w:bCs/>
                                                <w:spacing w:val="-2"/>
                                            </w:rPr>
                                        </w:pPr>
                                        <w:r>
                                            <w:rPr>
                                                <w:b/>
                                                <w:bCs/>
                                                <w:spacing w:val="-2"/>
                                            </w:rPr>
                                            <w:t>实验</w:t>
                                        </w:r>
                                    </w:p>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="7"/>
                                            <w:spacing w:before="108" w:line="186" w:lineRule="auto"/>
                                            <w:ind w:left="76" w:right="113" w:hanging="7"/>
                                            <w:jc w:val="center"/>
                                        </w:pPr>
                                        <w:r>
                                            <w:rPr>
                                                <w:b/>
                                                <w:bCs/>
                                                <w:spacing w:val="-2"/>
                                            </w:rPr>
                                            <w:t>课</w:t>
                                        </w:r>
                                        <w:r>
                                            <w:rPr>
                                                <w:b/>
                                                <w:bCs/>
                                            </w:rPr>
                                            <w:t>时</w:t>
                                        </w:r>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="737" w:type="dxa"/>
                                        <w:tcBorders>
                                            <w:top w:val="single" w:color="000000" w:sz="12" w:space="0"/>
                                        </w:tcBorders>
                                        <w:vAlign w:val="top"/>
                                    </w:tcPr>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="7"/>
                                            <w:spacing w:before="108" w:line="186" w:lineRule="auto"/>
                                            <w:ind w:left="83" w:right="122" w:hanging="7"/>
                                            <w:jc w:val="center"/>
                                            <w:rPr>
                                                <w:b/>
                                                <w:bCs/>
                                                <w:spacing w:val="-2"/>
                                            </w:rPr>
                                        </w:pPr>
                                        <w:r>
                                            <w:rPr>
                                                <w:b/>
                                                <w:bCs/>
                                                <w:spacing w:val="-2"/>
                                            </w:rPr>
                                            <w:t>实践</w:t>
                                        </w:r>
                                    </w:p>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="7"/>
                                            <w:spacing w:before="108" w:line="186" w:lineRule="auto"/>
                                            <w:ind w:left="83" w:right="122" w:hanging="7"/>
                                            <w:jc w:val="center"/>
                                        </w:pPr>
                                        <w:r>
                                            <w:rPr>
                                                <w:b/>
                                                <w:bCs/>
                                                <w:spacing w:val="-2"/>
                                            </w:rPr>
                                            <w:t>课</w:t>
                                        </w:r>
                                        <w:r>
                                            <w:rPr>
                                                <w:b/>
                                                <w:bCs/>
                                            </w:rPr>
                                            <w:t>时</w:t>
                                        </w:r>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="850" w:type="dxa"/>
                                        <w:tcBorders>
                                            <w:top w:val="single" w:color="000000" w:sz="12" w:space="0"/>
                                        </w:tcBorders>
                                        <w:vAlign w:val="top"/>
                                    </w:tcPr>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="7"/>
                                            <w:spacing w:before="105" w:line="187" w:lineRule="auto"/>
                                            <w:ind w:left="64" w:right="114"/>
                                            <w:jc w:val="center"/>
                                            <w:rPr>
                                                <w:b/>
                                                <w:bCs/>
                                                <w:spacing w:val="-1"/>
                                            </w:rPr>
                                        </w:pPr>
                                        <w:r>
                                            <w:rPr>
                                                <w:b/>
                                                <w:bCs/>
                                                <w:spacing w:val="-1"/>
                                            </w:rPr>
                                            <w:t>本期</w:t>
                                        </w:r>
                                    </w:p>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="7"/>
                                            <w:spacing w:before="105" w:line="187" w:lineRule="auto"/>
                                            <w:ind w:left="64" w:right="114"/>
                                            <w:jc w:val="center"/>
                                        </w:pPr>
                                        <w:r>
                                            <w:rPr>
                                                <w:b/>
                                                <w:bCs/>
                                                <w:spacing w:val="-1"/>
                                            </w:rPr>
                                            <w:t>学时</w:t>
                                        </w:r>
                                        <w:r>
                                            <w:rPr>
                                                <w:b/>
                                                <w:bCs/>
                                            </w:rPr>
                                            <w:t>数</w:t>
                                        </w:r>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="737" w:type="dxa"/>
                                        <w:tcBorders>
                                            <w:top w:val="single" w:color="000000" w:sz="12" w:space="0"/>
                                            <w:right w:val="single" w:color="000000" w:sz="12" w:space="0"/>
                                        </w:tcBorders>
                                        <w:vAlign w:val="top"/>
                                    </w:tcPr>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="7"/>
                                            <w:spacing w:before="256" w:line="183" w:lineRule="auto"/>
                                            <w:ind w:left="68"/>
                                            <w:jc w:val="center"/>
                                        </w:pPr>
                                        <w:r>
                                            <w:rPr>
                                                <w:b/>
                                                <w:bCs/>
                                                <w:spacing w:val="-1"/>
                                            </w:rPr>
                                            <w:t>备注</w:t>
                                        </w:r>
                                    </w:p>
                                </w:tc>
                            </w:tr>
                            <#list itemList[key]?keys as data>
                                <#list itemList[key][data] as data>
                                    <w:tr>
                                        <w:tblPrEx>
                                            <w:tblBorders>
                                                <w:top w:val="single" w:color="000000" w:sz="6" w:space="0"/>
                                                <w:left w:val="single" w:color="000000" w:sz="6" w:space="0"/>
                                                <w:bottom w:val="single" w:color="000000" w:sz="6" w:space="0"/>
                                                <w:right w:val="single" w:color="000000" w:sz="6" w:space="0"/>
                                                <w:insideH w:val="single" w:color="000000" w:sz="6" w:space="0"/>
                                                <w:insideV w:val="single" w:color="000000" w:sz="6" w:space="0"/>
                                            </w:tblBorders>
                                            <w:tblCellMar>
                                                <w:top w:w="0" w:type="dxa"/>
                                                <w:left w:w="0" w:type="dxa"/>
                                                <w:bottom w:w="0" w:type="dxa"/>
                                                <w:right w:w="0" w:type="dxa"/>
                                            </w:tblCellMar>
                                        </w:tblPrEx>
                                        <w:trPr>
                                            <w:trHeight w:val="23" w:hRule="atLeast"/>
                                            <w:jc w:val="center"/>
                                        </w:trPr>
                                        <w:tc>
                                            <w:tcPr>
                                                <w:tcW w:w="2268" w:type="dxa"/>
                                                <w:tcBorders>
                                                    <w:left w:val="single" w:color="000000" w:sz="12" w:space="0"/>
                                                </w:tcBorders>
                                                <w:vAlign w:val="center"/>
                                            </w:tcPr>
                                            <w:p>
                                                <w:pPr>
                                                    <w:pStyle w:val="7"/>
                                                    <w:spacing w:before="105" w:line="187" w:lineRule="auto"/>
                                                    <w:ind w:left="64" w:right="114"/>
                                                    <w:jc w:val="both"/>
                                                    <w:rPr>
                                                        <w:b w:val="0"/>
                                                        <w:bCs w:val="0"/>
                                                        <w:spacing w:val="-1"/>
                                                    </w:rPr>
                                                </w:pPr>
                                                <w:r>
                                                    <w:rPr>
                                                        <w:b w:val="0"/>
                                                        <w:bCs w:val="0"/>
                                                        <w:spacing w:val="-1"/>
                                                    </w:rPr>
                                                    <#assign clazzStr = "">
                                                    <#list data.kkxxb_jxbzc as clazz>
                                                        <#assign clazzStr += clazz+",">
                                                    </#list>
                                                    <w:t>${clazzStr?substring(0, clazzStr?length - 1)}</w:t>
                                                </w:r>
                                            </w:p>
                                        </w:tc>
                                        <w:tc>
                                            <w:tcPr>
                                                <w:tcW w:w="2268" w:type="dxa"/>
                                                <w:vAlign w:val="center"/>
                                            </w:tcPr>
                                            <w:p>
                                                <w:pPr>
                                                    <w:pStyle w:val="7"/>
                                                    <w:spacing w:before="105" w:line="187" w:lineRule="auto"/>
                                                    <w:ind w:left="64" w:right="114"/>
                                                    <w:jc w:val="both"/>
                                                    <w:rPr>
                                                        <w:b w:val="0"/>
                                                        <w:bCs w:val="0"/>
                                                        <w:spacing w:val="-1"/>
                                                    </w:rPr>
                                                </w:pPr>
                                                <w:r>
                                                    <w:rPr>
                                                        <w:b w:val="0"/>
                                                        <w:bCs w:val="0"/>
                                                        <w:spacing w:val="-1"/>
                                                    </w:rPr>
                                                    <w:t><#if data.kkxxb_kcmc??>${data.kkxxb_kcmc}</#if></w:t>
                                                </w:r>
                                            </w:p>
                                        </w:tc>
                                        <w:tc>
                                            <w:tcPr>
                                                <w:tcW w:w="850" w:type="dxa"/>
                                                <w:vAlign w:val="center"/>
                                            </w:tcPr>
                                            <w:p>
                                                <w:pPr>
                                                    <w:pStyle w:val="7"/>
                                                    <w:spacing w:before="105" w:line="187" w:lineRule="auto"/>
                                                    <w:ind w:left="64" w:right="114"/>
                                                    <w:jc w:val="center"/>
                                                    <w:rPr>
                                                        <w:b w:val="0"/>
                                                        <w:bCs w:val="0"/>
                                                        <w:spacing w:val="-1"/>
                                                    </w:rPr>
                                                </w:pPr>
                                                <w:r>
                                                    <w:rPr>
                                                        <w:b w:val="0"/>
                                                        <w:bCs w:val="0"/>
                                                        <w:spacing w:val="-1"/>
                                                    </w:rPr>
                                                    <w:t><#if data.kkxxb_zhouxs??>${data.kkxxb_zhouxs}</#if></w:t>
                                                </w:r>
                                            </w:p>
                                        </w:tc>
                                        <w:tc>
                                            <w:tcPr>
                                                <w:tcW w:w="737" w:type="dxa"/>
                                                <w:vAlign w:val="center"/>
                                            </w:tcPr>
                                            <w:p>
                                                <w:pPr>
                                                    <w:pStyle w:val="7"/>
                                                    <w:spacing w:before="105" w:line="187" w:lineRule="auto"/>
                                                    <w:ind w:left="64" w:right="114"/>
                                                    <w:jc w:val="center"/>
                                                    <w:rPr>
                                                        <w:b w:val="0"/>
                                                        <w:bCs w:val="0"/>
                                                        <w:spacing w:val="-1"/>
                                                    </w:rPr>
                                                </w:pPr>
                                                <w:r>
                                                    <w:rPr>
                                                        <w:b w:val="0"/>
                                                        <w:bCs w:val="0"/>
                                                        <w:spacing w:val="-1"/>
                                                    </w:rPr>
                                                    <w:t><#if data.kkxxb_zc??>${data.kkxxb_zc}</#if></w:t>
                                                </w:r>
                                            </w:p>
                                        </w:tc>
                                        <w:tc>
                                            <w:tcPr>
                                                <w:tcW w:w="737" w:type="dxa"/>
                                                <w:vAlign w:val="center"/>
                                            </w:tcPr>
                                            <w:p>
                                                <w:pPr>
                                                    <w:pStyle w:val="7"/>
                                                    <w:spacing w:before="105" w:line="187" w:lineRule="auto"/>
                                                    <w:ind w:left="64" w:right="114"/>
                                                    <w:jc w:val="center"/>
                                                    <w:rPr>
                                                        <w:b w:val="0"/>
                                                        <w:bCs w:val="0"/>
                                                        <w:spacing w:val="-1"/>
                                                    </w:rPr>
                                                </w:pPr>
                                                <w:r>
                                                    <w:rPr>
                                                        <w:b w:val="0"/>
                                                        <w:bCs w:val="0"/>
                                                        <w:spacing w:val="-1"/>
                                                    </w:rPr>
                                                    <w:t><#if data.kkxxb_lilunxs??>${data.kkxxb_lilunxs}</#if></w:t>
                                                </w:r>
                                            </w:p>
                                        </w:tc>
                                        <w:tc>
                                            <w:tcPr>
                                                <w:tcW w:w="737" w:type="dxa"/>
                                                <w:vAlign w:val="center"/>
                                            </w:tcPr>
                                            <w:p>
                                                <w:pPr>
                                                    <w:pStyle w:val="7"/>
                                                    <w:spacing w:before="105" w:line="187" w:lineRule="auto"/>
                                                    <w:ind w:left="64" w:right="114"/>
                                                    <w:jc w:val="center"/>
                                                    <w:rPr>
                                                        <w:b w:val="0"/>
                                                        <w:bCs w:val="0"/>
                                                        <w:spacing w:val="-1"/>
                                                    </w:rPr>
                                                </w:pPr>
                                                <w:r>
                                                    <w:rPr>
                                                        <w:b w:val="0"/>
                                                        <w:bCs w:val="0"/>
                                                        <w:spacing w:val="-1"/>
                                                    </w:rPr>
                                                    <w:t><#if data.kkxxb_shiyanxs??>${data.kkxxb_shiyanxs}</#if></w:t>
                                                </w:r>
                                            </w:p>
                                        </w:tc>
                                        <w:tc>
                                            <w:tcPr>
                                                <w:tcW w:w="737" w:type="dxa"/>
                                                <w:vAlign w:val="center"/>
                                            </w:tcPr>
                                            <w:p>
                                                <w:pPr>
                                                    <w:pStyle w:val="7"/>
                                                    <w:spacing w:before="105" w:line="187" w:lineRule="auto"/>
                                                    <w:ind w:left="64" w:right="114"/>
                                                    <w:jc w:val="center"/>
                                                    <w:rPr>
                                                        <w:b w:val="0"/>
                                                        <w:bCs w:val="0"/>
                                                        <w:spacing w:val="-1"/>
                                                    </w:rPr>
                                                </w:pPr>
                                                <w:r>
                                                    <w:rPr>
                                                        <w:b w:val="0"/>
                                                        <w:bCs w:val="0"/>
                                                        <w:spacing w:val="-1"/>
                                                    </w:rPr>
                                                    <w:t><#if data.kkxxb_shijixs??>${data.kkxxb_shijixs}</#if></w:t>
                                                </w:r>
                                            </w:p>
                                        </w:tc>
                                        <w:tc>
                                            <w:tcPr>
                                                <w:tcW w:w="850" w:type="dxa"/>
                                                <w:vAlign w:val="center"/>
                                            </w:tcPr>
                                            <w:p>
                                                <w:pPr>
                                                    <w:pStyle w:val="7"/>
                                                    <w:spacing w:before="105" w:line="187" w:lineRule="auto"/>
                                                    <w:ind w:right="114"/>
                                                    <w:jc w:val="center"/>
                                                    <w:rPr>
                                                        <w:rFonts w:hint="eastAsia"/>
                                                        <w:b w:val="0"/>
                                                        <w:bCs w:val="0"/>
                                                        <w:spacing w:val="-1"/>
                                                        <w:lang w:eastAsia="zh-CN"/>
                                                    </w:rPr>
                                                </w:pPr>
                                                <w:r>

                                                    <w:rPr>
                                                        <w:rFonts w:hint="eastAsia"/>
                                                        <w:b w:val="0"/>
                                                        <w:bCs w:val="0"/>
                                                        <w:spacing w:val="-1"/>
                                                        <w:lang w:eastAsia="zh-CN"/>
                                                    </w:rPr>
                                                    <w:t><#if data.kkxxb_zongxs??>${data.kkxxb_zongxs}</#if></w:t>
                                                </w:r>
                                            </w:p>
                                        </w:tc>
                                        <w:tc>
                                            <w:tcPr>
                                                <w:tcW w:w="737" w:type="dxa"/>
                                                <w:tcBorders>
                                                    <w:right w:val="single" w:color="000000" w:sz="12" w:space="0"/>
                                                </w:tcBorders>
                                                <w:vAlign w:val="center"/>
                                            </w:tcPr>
                                            <w:p>
                                                <w:pPr>
                                                    <w:pStyle w:val="7"/>
                                                    <w:spacing w:before="105" w:line="187" w:lineRule="auto"/>
                                                    <w:ind w:left="64" w:right="114"/>
                                                    <w:jc w:val="left"/>
                                                    <w:rPr>
                                                        <w:b w:val="0"/>
                                                        <w:bCs w:val="0"/>
                                                        <w:spacing w:val="-1"/>
                                                    </w:rPr>
                                                </w:pPr>
                                            </w:p>
                                        </w:tc>
                                    </w:tr>
                                </#list>
                            </#list>
                            <#if assistantData[key]??>
                                <#list assistantData[key]?keys as data>
                                <#list assistantData[key][data] as data>
                                <w:tr>
                                    <w:tblPrEx>
                                        <w:tblBorders>
                                            <w:top w:val="single" w:color="000000" w:sz="6" w:space="0"/>
                                            <w:left w:val="single" w:color="000000" w:sz="6" w:space="0"/>
                                            <w:bottom w:val="single" w:color="000000" w:sz="6" w:space="0"/>
                                            <w:right w:val="single" w:color="000000" w:sz="6" w:space="0"/>
                                            <w:insideH w:val="single" w:color="000000" w:sz="6" w:space="0"/>
                                            <w:insideV w:val="single" w:color="000000" w:sz="6" w:space="0"/>
                                        </w:tblBorders>
                                        <w:tblCellMar>
                                            <w:top w:w="0" w:type="dxa"/>
                                            <w:left w:w="0" w:type="dxa"/>
                                            <w:bottom w:w="0" w:type="dxa"/>
                                            <w:right w:w="0" w:type="dxa"/>
                                        </w:tblCellMar>
                                    </w:tblPrEx>
                                    <w:trPr>
                                        <w:trHeight w:val="23" w:hRule="atLeast"/>
                                        <w:jc w:val="center"/>
                                    </w:trPr>
                                    <w:tc>
                                        <w:tcPr>
                                            <w:tcW w:w="2268" w:type="dxa"/>
                                            <w:tcBorders>
                                                <w:left w:val="single" w:color="000000" w:sz="12" w:space="0"/>
                                            </w:tcBorders>
                                            <w:vAlign w:val="center"/>
                                        </w:tcPr>
                                        <w:p>
                                            <w:pPr>
                                                <w:pStyle w:val="7"/>
                                                <w:spacing w:before="105" w:line="187" w:lineRule="auto"/>
                                                <w:ind w:left="64" w:right="114"/>
                                                <w:jc w:val="both"/>
                                                <w:rPr>
                                                    <w:b w:val="0"/>
                                                    <w:bCs w:val="0"/>
                                                    <w:spacing w:val="-1"/>
                                                </w:rPr>
                                            </w:pPr>
                                            <w:r>
                                                <w:rPr>
                                                    <w:b w:val="0"/>
                                                    <w:bCs w:val="0"/>
                                                    <w:spacing w:val="-1"/>
                                                </w:rPr>
                                                <#assign clazzStr = "">
                                                <#list data.kkxxb_jxbzc as clazz>
                                                    <#assign clazzStr += clazz+",">
                                                </#list>
                                                <w:t>${clazzStr?substring(0, clazzStr?length - 1)}</w:t>
                                            </w:r>
                                        </w:p>
                                    </w:tc>
                                    <w:tc>
                                        <w:tcPr>
                                            <w:tcW w:w="2268" w:type="dxa"/>
                                            <w:vAlign w:val="center"/>
                                        </w:tcPr>
                                        <w:p>
                                            <w:pPr>
                                                <w:pStyle w:val="7"/>
                                                <w:spacing w:before="105" w:line="187" w:lineRule="auto"/>
                                                <w:ind w:left="64" w:right="114"/>
                                                <w:jc w:val="both"/>
                                                <w:rPr>
                                                    <w:b w:val="0"/>
                                                    <w:bCs w:val="0"/>
                                                    <w:spacing w:val="-1"/>
                                                </w:rPr>
                                            </w:pPr>
                                            <w:r>
                                                <w:rPr>
                                                    <w:b w:val="0"/>
                                                    <w:bCs w:val="0"/>
                                                    <w:spacing w:val="-1"/>
                                                </w:rPr>
                                                <w:t><#if data.kkxxb_kcmc??>${data.kkxxb_kcmc}</#if></w:t>
                                            </w:r>
                                        </w:p>
                                    </w:tc>
                                    <w:tc>
                                        <w:tcPr>
                                            <w:tcW w:w="850" w:type="dxa"/>
                                            <w:vAlign w:val="center"/>
                                        </w:tcPr>
                                        <w:p>
                                            <w:pPr>
                                                <w:pStyle w:val="7"/>
                                                <w:spacing w:before="105" w:line="187" w:lineRule="auto"/>
                                                <w:ind w:left="64" w:right="114"/>
                                                <w:jc w:val="center"/>
                                                <w:rPr>
                                                    <w:b w:val="0"/>
                                                    <w:bCs w:val="0"/>
                                                    <w:spacing w:val="-1"/>
                                                </w:rPr>
                                            </w:pPr>
                                            <w:r>
                                                <w:rPr>
                                                    <w:b w:val="0"/>
                                                    <w:bCs w:val="0"/>
                                                    <w:spacing w:val="-1"/>
                                                </w:rPr>
                                                <w:t><#if data.kkxxb_zhouxs??>${data.kkxxb_zhouxs}</#if></w:t>
                                            </w:r>
                                        </w:p>
                                    </w:tc>
                                    <w:tc>
                                        <w:tcPr>
                                            <w:tcW w:w="737" w:type="dxa"/>
                                            <w:vAlign w:val="center"/>
                                        </w:tcPr>
                                        <w:p>
                                            <w:pPr>
                                                <w:pStyle w:val="7"/>
                                                <w:spacing w:before="105" w:line="187" w:lineRule="auto"/>
                                                <w:ind w:left="64" w:right="114"/>
                                                <w:jc w:val="center"/>
                                                <w:rPr>
                                                    <w:b w:val="0"/>
                                                    <w:bCs w:val="0"/>
                                                    <w:spacing w:val="-1"/>
                                                </w:rPr>
                                            </w:pPr>
                                            <w:r>
                                                <w:rPr>
                                                    <w:b w:val="0"/>
                                                    <w:bCs w:val="0"/>
                                                    <w:spacing w:val="-1"/>
                                                </w:rPr>
                                                <w:t><#if data.kkxxb_zc??>${data.kkxxb_zc}</#if></w:t>
                                            </w:r>
                                        </w:p>
                                    </w:tc>
                                    <w:tc>
                                        <w:tcPr>
                                            <w:tcW w:w="737" w:type="dxa"/>
                                            <w:vAlign w:val="center"/>
                                        </w:tcPr>
                                        <w:p>
                                            <w:pPr>
                                                <w:pStyle w:val="7"/>
                                                <w:spacing w:before="105" w:line="187" w:lineRule="auto"/>
                                                <w:ind w:left="64" w:right="114"/>
                                                <w:jc w:val="center"/>
                                                <w:rPr>
                                                    <w:b w:val="0"/>
                                                    <w:bCs w:val="0"/>
                                                    <w:spacing w:val="-1"/>
                                                </w:rPr>
                                            </w:pPr>
                                            <w:r>
                                                <w:rPr>
                                                    <w:b w:val="0"/>
                                                    <w:bCs w:val="0"/>
                                                    <w:spacing w:val="-1"/>
                                                </w:rPr>
                                                <w:t><#if data.kkxxb_lilunxs??>${data.kkxxb_lilunxs}</#if></w:t>
                                            </w:r>
                                        </w:p>
                                    </w:tc>
                                    <w:tc>
                                        <w:tcPr>
                                            <w:tcW w:w="737" w:type="dxa"/>
                                            <w:vAlign w:val="center"/>
                                        </w:tcPr>
                                        <w:p>
                                            <w:pPr>
                                                <w:pStyle w:val="7"/>
                                                <w:spacing w:before="105" w:line="187" w:lineRule="auto"/>
                                                <w:ind w:left="64" w:right="114"/>
                                                <w:jc w:val="center"/>
                                                <w:rPr>
                                                    <w:b w:val="0"/>
                                                    <w:bCs w:val="0"/>
                                                    <w:spacing w:val="-1"/>
                                                </w:rPr>
                                            </w:pPr>
                                            <w:r>
                                                <w:rPr>
                                                    <w:b w:val="0"/>
                                                    <w:bCs w:val="0"/>
                                                    <w:spacing w:val="-1"/>
                                                </w:rPr>
                                                <w:t><#if data.kkxxb_shiyanxs??>${data.kkxxb_shiyanxs}</#if></w:t>
                                            </w:r>
                                        </w:p>
                                    </w:tc>
                                    <w:tc>
                                        <w:tcPr>
                                            <w:tcW w:w="737" w:type="dxa"/>
                                            <w:vAlign w:val="center"/>
                                        </w:tcPr>
                                        <w:p>
                                            <w:pPr>
                                                <w:pStyle w:val="7"/>
                                                <w:spacing w:before="105" w:line="187" w:lineRule="auto"/>
                                                <w:ind w:left="64" w:right="114"/>
                                                <w:jc w:val="center"/>
                                                <w:rPr>
                                                    <w:b w:val="0"/>
                                                    <w:bCs w:val="0"/>
                                                    <w:spacing w:val="-1"/>
                                                </w:rPr>
                                            </w:pPr>
                                            <w:r>
                                                <w:rPr>
                                                    <w:b w:val="0"/>
                                                    <w:bCs w:val="0"/>
                                                    <w:spacing w:val="-1"/>
                                                </w:rPr>
                                                <w:t><#if data.kkxxb_shijixs??>${data.kkxxb_shijixs}</#if></w:t>
                                            </w:r>
                                        </w:p>
                                    </w:tc>
                                    <w:tc>
                                        <w:tcPr>
                                            <w:tcW w:w="850" w:type="dxa"/>
                                            <w:vAlign w:val="center"/>
                                        </w:tcPr>
                                        <w:p>
                                            <w:pPr>
                                                <w:pStyle w:val="7"/>
                                                <w:spacing w:before="105" w:line="187" w:lineRule="auto"/>
                                                <w:ind w:right="114"/>
                                                <w:jc w:val="center"/>
                                                <w:rPr>
                                                    <w:rFonts w:hint="eastAsia"/>
                                                    <w:b w:val="0"/>
                                                    <w:bCs w:val="0"/>
                                                    <w:spacing w:val="-1"/>
                                                    <w:lang w:eastAsia="zh-CN"/>
                                                </w:rPr>
                                            </w:pPr>
                                            <w:r>

                                                <w:rPr>
                                                    <w:rFonts w:hint="eastAsia"/>
                                                    <w:b w:val="0"/>
                                                    <w:bCs w:val="0"/>
                                                    <w:spacing w:val="-1"/>
                                                    <w:lang w:eastAsia="zh-CN"/>
                                                </w:rPr>
                                                <w:t><#if data.kkxxb_zongxs??>${data.kkxxb_zongxs}</#if></w:t>
                                            </w:r>
                                        </w:p>
                                    </w:tc>
                                    <w:tc>
                                        <w:tcPr>
                                            <w:tcW w:w="737" w:type="dxa"/>
                                            <w:tcBorders>
                                                <w:right w:val="single" w:color="000000" w:sz="12" w:space="0"/>
                                            </w:tcBorders>
                                            <w:vAlign w:val="center"/>
                                        </w:tcPr>
                                        <w:p>
                                            <w:pPr>
                                                <w:pStyle w:val="7"/>
                                                <w:spacing w:before="105" w:line="187" w:lineRule="auto"/>
                                                <w:ind w:left="64" w:right="114"/>
                                                <w:jc w:val="left"/>
                                                <w:rPr>
                                                    <w:b w:val="0"/>
                                                    <w:bCs w:val="0"/>
                                                    <w:spacing w:val="-1"/>
                                                </w:rPr>
                                            </w:pPr>
                                        </w:p>
                                    </w:tc>
                                </w:tr>
                                </#list>
                            </#list>
                            </#if>
                        </w:tbl>
                        <w:p>
                            <w:pPr>
                                <w:pStyle w:val="2"/>
                                <w:spacing w:before="46" w:line="184" w:lineRule="auto"/>
                                <w:rPr>
                                    <w:b/>
                                    <w:bCs/>
                                    <w:spacing w:val="-1"/>
                                </w:rPr>
                            </w:pPr>
                        </w:p>
                        <w:p>
                            <w:pPr>
                                <w:pStyle w:val="2"/>
                                <w:spacing w:before="46" w:line="240" w:lineRule="auto"/>
                                <w:rPr>
                                    <w:b/>
                                    <w:bCs/>
                                    <w:spacing w:val="-1"/>
                                </w:rPr>
                            </w:pPr>
                        </w:p>
                        <w:p>
                            <w:pPr>
                                <w:pStyle w:val="2"/>
                                <w:spacing w:before="3" w:line="240" w:lineRule="auto"/>
                                <w:ind w:right="9"/>
                                <w:rPr>
                                    <w:b/>
                                    <w:bCs/>
                                    <w:spacing w:val="-2"/>
                                </w:rPr>
                            </w:pPr>
                        </w:p>
                        <w:p>
                            <w:pPr>
                                <w:pStyle w:val="2"/>
                                <w:spacing w:before="3" w:line="240" w:lineRule="auto"/>
                                <w:ind w:left="5710" w:right="8" w:firstLine="891"/>
                                <w:jc w:val="right"/>
                                <w:rPr>
                                    <w:b/>
                                    <w:bCs/>
                                </w:rPr>
                            </w:pPr>
                            <w:r>
                                <w:rPr>
                                    <w:b/>
                                    <w:bCs/>
                                    <w:spacing w:val="-1"/>
                                </w:rPr>
                                <w:t>教  务  处</w:t>
                            </w:r>
                            <w:r>
                                <w:rPr>
                                    <w:b/>
                                    <w:bCs/>
                                </w:rPr>
                                <w:t xml:space="preserve"> </w:t>
                            </w:r>
                        </w:p>
                        <w:p>
                            <w:pPr>
                                <w:pStyle w:val="2"/>
                                <w:spacing w:before="3" w:line="240" w:lineRule="auto"/>
                                <w:ind w:left="5710" w:right="8" w:firstLine="891"/>
                                <w:jc w:val="right"/>
                            </w:pPr>
                            <w:r>
                                <w:rPr>
                                    <w:rFonts w:hint="eastAsia"/>
                                    <w:b/>
                                    <w:bCs/>
                                    <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                </w:rPr>
                                <#if key?split("|")[2]?split("-")[2] == "1">
                                    <#assign year = key?split("|")[2]?split("-")[0]>
                                    <#assign month = "9">
                                </#if>
                                <#if key?split("|")[2]?split("-")[2] == "2">
                                    <#assign year = key?split("|")[2]?split("-")[1]>
                                    <#assign month = "3">
                                </#if>
                                <w:t>${year}</w:t>
                            </w:r>
                            <w:r>
                                <w:rPr>
                                    <w:b/>
                                    <w:bCs/>
                                    <w:spacing w:val="-2"/>
                                </w:rPr>
                                <w:t>年</w:t>
                            </w:r>
                            <w:r>
                                <w:rPr>
                                    <w:rFonts w:hint="eastAsia"/>
                                    <w:b/>
                                    <w:bCs/>
                                    <w:spacing w:val="-2"/>
                                    <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                </w:rPr>
                                <w:t>${month}</w:t>
                            </w:r>
                            <w:r>
                                <w:rPr>
                                    <w:b/>
                                    <w:bCs/>
                                    <w:spacing w:val="-2"/>
                                </w:rPr>
                                <w:t>月</w:t>
                            </w:r>
                            <w:r>
                                <w:rPr>
                                    <w:rFonts w:hint="eastAsia"/>
                                    <w:b/>
                                    <w:bCs/>
                                    <w:spacing w:val="-2"/>
                                    <w:lang w:val="en-US" w:eastAsia="zh-CN"/>
                                </w:rPr>
                                <w:t>1</w:t>
                            </w:r>
                            <w:r>
                                <w:rPr>
                                    <w:b/>
                                    <w:bCs/>
                                    <w:spacing w:val="-2"/>
                                </w:rPr>
                                <w:t>日</w:t>
                            </w:r>
                            <w:bookmarkStart w:id="0" w:name="_GoBack"/>
                            <w:bookmarkEnd w:id="0"/>
                        </w:p>
                        <w:p>
                            <w:r>
                                <w:br w:type="page"/>
                            </w:r>
                        </w:p>
                        <w:p>
                            <w:pPr>
                                <w:sectPr>
                                    <w:pgSz w:w="11900" w:h="16839"/>
                                    <w:pgMar w:top="813" w:right="1054" w:bottom="0" w:left="1086" w:header="0" w:footer="0" w:gutter="0"/>
                                    <w:cols w:equalWidth="0" w:num="1">
                                        <w:col w:w="9759"/>
                                    </w:cols>
                                </w:sectPr>
                            </w:pPr>
                        </w:p>
                        <w:p>
                            <w:pPr>
                                <w:pStyle w:val="2"/>
                                <w:spacing w:before="3" w:line="160" w:lineRule="auto"/>
                                <w:ind w:right="8"/>
                                <w:jc w:val="both"/>
                            </w:pPr>
                        </w:p>
                        <w:sectPr>
                            <w:type w:val="continuous"/>
                            <w:pgSz w:w="11900" w:h="16839"/>
                            <w:pgMar w:top="813" w:right="1054" w:bottom="0" w:left="1086" w:header="0" w:footer="0" w:gutter="0"/>
                            <w:cols w:equalWidth="0" w:num="2">
                                <w:col w:w="2049" w:space="100"/>
                                <w:col w:w="7611"/>
                            </w:cols>
                        </w:sectPr>
                    </w:body>
                </#list>
            </w:document>
        </pkg:xmlData>
    </pkg:part>
    <pkg:part pkg:name="/docProps/app.xml" pkg:contentType="application/vnd.openxmlformats-officedocument.extended-properties+xml">
        <pkg:xmlData>
            <Properties xmlns="http://schemas.openxmlformats.org/officeDocument/2006/extended-properties" xmlns:vt="http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes">
                <TotalTime>2</TotalTime>
                <ScaleCrop>false</ScaleCrop>
                <LinksUpToDate>false</LinksUpToDate>
                <Application>WPS Office_12.1.0.15990_F1E327BC-269C-435d-A152-05C5408002CA</Application>
            </Properties>
        </pkg:xmlData>
    </pkg:part>
    <pkg:part pkg:name="/docProps/core.xml" pkg:contentType="application/vnd.openxmlformats-package.core-properties+xml">
        <pkg:xmlData>
            <cp:coreProperties xmlns:cp="http://schemas.openxmlformats.org/package/2006/metadata/core-properties" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:dcterms="http://purl.org/dc/terms/" xmlns:dcmitype="http://purl.org/dc/dcmitype/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <dcterms:created xsi:type="dcterms:W3CDTF">2023-10-23T15:41:00Z</dcterms:created>
                <dc:creator>Data</dc:creator>
                <cp:lastModifiedBy>WPS_1661481478</cp:lastModifiedBy>
                <dcterms:modified xsi:type="dcterms:W3CDTF">2023-12-20T08:18:02Z</dcterms:modified>
            </cp:coreProperties>
        </pkg:xmlData>
    </pkg:part>
    <pkg:part pkg:name="/docProps/custom.xml" pkg:contentType="application/vnd.openxmlformats-officedocument.custom-properties+xml">
        <pkg:xmlData>
            <Properties xmlns="http://schemas.openxmlformats.org/officeDocument/2006/custom-properties" xmlns:vt="http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes">
                <property fmtid="{D5CDD505-2E9C-101B-9397-08002B2CF9AE}" pid="2" name="CRO">
                    <vt:lpwstr>wqlLaW5nc29mdCBQREYgdG8gV1BTIDkw</vt:lpwstr>
                </property>
                <property fmtid="{D5CDD505-2E9C-101B-9397-08002B2CF9AE}" pid="3" name="Created">
                    <vt:filetime>2023-10-23T15:49:20Z</vt:filetime>
                </property>
                <property fmtid="{D5CDD505-2E9C-101B-9397-08002B2CF9AE}" pid="4" name="KSOProductBuildVer">
                    <vt:lpwstr>2052-12.1.0.15990</vt:lpwstr>
                </property>
                <property fmtid="{D5CDD505-2E9C-101B-9397-08002B2CF9AE}" pid="5" name="ICV">
                    <vt:lpwstr>76208E44E61347DDBD46B02AF3F48741_13</vt:lpwstr>
                </property>
            </Properties>
        </pkg:xmlData>
    </pkg:part>
    <pkg:part pkg:name="/word/endnotes.xml" pkg:contentType="application/vnd.openxmlformats-officedocument.wordprocessingml.endnotes+xml">
        <pkg:xmlData>
            <w:endnotes xmlns:wpc="http://schemas.microsoft.com/office/word/2010/wordprocessingCanvas" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:wp14="http://schemas.microsoft.com/office/word/2010/wordprocessingDrawing" xmlns:wp="http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing" xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main" xmlns:w14="http://schemas.microsoft.com/office/word/2010/wordml" xmlns:w15="http://schemas.microsoft.com/office/word/2012/wordml" xmlns:w10="urn:schemas-microsoft-com:office:word" xmlns:wpg="http://schemas.microsoft.com/office/word/2010/wordprocessingGroup" xmlns:wpi="http://schemas.microsoft.com/office/word/2010/wordprocessingInk" xmlns:wne="http://schemas.microsoft.com/office/word/2006/wordml" xmlns:wps="http://schemas.microsoft.com/office/word/2010/wordprocessingShape" xmlns:wpsCustomData="http://www.wps.cn/officeDocument/2013/wpsCustomData" mc:Ignorable="w14 w15 wp14">
                <w:endnote w:type="separator" w:id="0">
                    <w:p>
                        <w:pPr>
                            <w:spacing w:line="240" w:lineRule="auto"/>
                        </w:pPr>
                        <w:r>
                            <w:separator/>
                        </w:r>
                    </w:p>
                </w:endnote>
                <w:endnote w:type="continuationSeparator" w:id="1">
                    <w:p>
                        <w:pPr>
                            <w:spacing w:line="240" w:lineRule="auto"/>
                        </w:pPr>
                        <w:r>
                            <w:continuationSeparator/>
                        </w:r>
                    </w:p>
                </w:endnote>
            </w:endnotes>
        </pkg:xmlData>
    </pkg:part>
    <pkg:part pkg:name="/word/fontTable.xml" pkg:contentType="application/vnd.openxmlformats-officedocument.wordprocessingml.fontTable+xml">
        <pkg:xmlData>
            <w:fonts xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main" xmlns:w14="http://schemas.microsoft.com/office/word/2010/wordml" mc:Ignorable="w14">
                <w:font w:name="Times New Roman">
                    <w:panose1 w:val="02020603050405020304"/>
                    <w:charset w:val="00"/>
                    <w:family w:val="roman"/>
                    <w:pitch w:val="variable"/>
                    <w:sig w:usb0="20007A87" w:usb1="80000000" w:usb2="00000008" w:usb3="00000000" w:csb0="000001FF" w:csb1="00000000"/>
                </w:font>
                <w:font w:name="宋体">
                    <w:panose1 w:val="02010600030101010101"/>
                    <w:charset w:val="86"/>
                    <w:family w:val="auto"/>
                    <w:pitch w:val="default"/>
                    <w:sig w:usb0="00000203" w:usb1="288F0000" w:usb2="00000006" w:usb3="00000000" w:csb0="00040001" w:csb1="00000000"/>
                </w:font>
                <w:font w:name="Wingdings">
                    <w:panose1 w:val="05000000000000000000"/>
                    <w:charset w:val="02"/>
                    <w:family w:val="auto"/>
                    <w:pitch w:val="default"/>
                    <w:sig w:usb0="00000000" w:usb1="00000000" w:usb2="00000000" w:usb3="00000000" w:csb0="80000000" w:csb1="00000000"/>
                </w:font>
                <w:font w:name="Arial">
                    <w:panose1 w:val="020B0604020202020204"/>
                    <w:charset w:val="01"/>
                    <w:family w:val="swiss"/>
                    <w:pitch w:val="default"/>
                    <w:sig w:usb0="E0002EFF" w:usb1="C000785B" w:usb2="00000009" w:usb3="00000000" w:csb0="400001FF" w:csb1="FFFF0000"/>
                </w:font>
                <w:font w:name="黑体">
                    <w:panose1 w:val="02010609060101010101"/>
                    <w:charset w:val="86"/>
                    <w:family w:val="auto"/>
                    <w:pitch w:val="default"/>
                    <w:sig w:usb0="800002BF" w:usb1="38CF7CFA" w:usb2="00000016" w:usb3="00000000" w:csb0="00040001" w:csb1="00000000"/>
                </w:font>
                <w:font w:name="Courier New">
                    <w:panose1 w:val="02070309020205020404"/>
                    <w:charset w:val="01"/>
                    <w:family w:val="modern"/>
                    <w:pitch w:val="default"/>
                    <w:sig w:usb0="E0002EFF" w:usb1="C0007843" w:usb2="00000009" w:usb3="00000000" w:csb0="400001FF" w:csb1="FFFF0000"/>
                </w:font>
                <w:font w:name="Symbol">
                    <w:panose1 w:val="05050102010706020507"/>
                    <w:charset w:val="02"/>
                    <w:family w:val="roman"/>
                    <w:pitch w:val="default"/>
                    <w:sig w:usb0="00000000" w:usb1="00000000" w:usb2="00000000" w:usb3="00000000" w:csb0="80000000" w:csb1="00000000"/>
                </w:font>
                <w:font w:name="Calibri">
                    <w:panose1 w:val="020F0502020204030204"/>
                    <w:charset w:val="00"/>
                    <w:family w:val="swiss"/>
                    <w:pitch w:val="default"/>
                    <w:sig w:usb0="E4002EFF" w:usb1="C200247B" w:usb2="00000009" w:usb3="00000000" w:csb0="200001FF" w:csb1="00000000"/>
                </w:font>
                <w:font w:name="微软雅黑">
                    <w:panose1 w:val="020B0503020204020204"/>
                    <w:charset w:val="86"/>
                    <w:family w:val="auto"/>
                    <w:pitch w:val="default"/>
                    <w:sig w:usb0="80000287" w:usb1="2ACF3C50" w:usb2="00000016" w:usb3="00000000" w:csb0="0004001F" w:csb1="00000000"/>
                </w:font>
            </w:fonts>
        </pkg:xmlData>
    </pkg:part>
    <pkg:part pkg:name="/word/footnotes.xml" pkg:contentType="application/vnd.openxmlformats-officedocument.wordprocessingml.footnotes+xml">
        <pkg:xmlData>
            <w:footnotes xmlns:wpc="http://schemas.microsoft.com/office/word/2010/wordprocessingCanvas" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:wp14="http://schemas.microsoft.com/office/word/2010/wordprocessingDrawing" xmlns:wp="http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing" xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main" xmlns:w14="http://schemas.microsoft.com/office/word/2010/wordml" xmlns:w15="http://schemas.microsoft.com/office/word/2012/wordml" xmlns:w10="urn:schemas-microsoft-com:office:word" xmlns:wpg="http://schemas.microsoft.com/office/word/2010/wordprocessingGroup" xmlns:wpi="http://schemas.microsoft.com/office/word/2010/wordprocessingInk" xmlns:wne="http://schemas.microsoft.com/office/word/2006/wordml" xmlns:wps="http://schemas.microsoft.com/office/word/2010/wordprocessingShape" xmlns:wpsCustomData="http://www.wps.cn/officeDocument/2013/wpsCustomData" mc:Ignorable="w14 w15 wp14">
                <w:footnote w:type="separator" w:id="0">
                    <w:p>
                        <w:pPr>
                            <w:spacing w:line="240" w:lineRule="auto"/>
                        </w:pPr>
                        <w:r>
                            <w:separator/>
                        </w:r>
                    </w:p>
                </w:footnote>
                <w:footnote w:type="continuationSeparator" w:id="1">
                    <w:p>
                        <w:pPr>
                            <w:spacing w:line="240" w:lineRule="auto"/>
                        </w:pPr>
                        <w:r>
                            <w:continuationSeparator/>
                        </w:r>
                    </w:p>
                </w:footnote>
            </w:footnotes>
        </pkg:xmlData>
    </pkg:part>
    <pkg:part pkg:name="/word/media/image1.png" pkg:contentType="image/png">
        <pkg:binaryData>iVBORw0KGgoAAAANSUhEUgAAAO8AAAAZCAYAAADZu3m9AAAOtUlEQVR4nO2ce7xVRRXHv+dykZcX FARBiK6IQhgQKpoaCuILFEHLJxeh0I+hoZhSZpqZJQaWzyzK8FG+H5SpgKahkZIPnommqahplI80 8AEKxz/WTHvtdWa/zrngR+v3+czn7L1mzdqz58yaWbNmzYb/DZSArT/qSnxM0PWjrsDHCA1A+40k u7SR5G5SNADdXWpXsGxn4Amg7NIWVdahLlB2JDAE2BXYBdg5RxoMdEx4xpZV1i0PWgAvAxOB7ZH3 CWEboraaRvH23pTorlJbR9tK0TYvKG9zV96iCWmPhkDeGS7vLKB1hvzBwLXANcBnUvgGAn93cjMV uAPQqRlTy4TndAFeBK4GzsmqlMKpRB1qYoFyuLqUVfpewfIAPVT5ekUvV5naEkcJuNLl9TV5DS61 SKlfnZOZNCiAdEpdh6SB4n7Dl9V5irz38JwpaWBJe/ahAdqXc8oB6KnKbafo1yr6c8Tbo0TlO6a1 13GKb2QK38uK74SsilfbCZPS5xOe09XwaXwK2NMla94epcocnvUyAVxM/gYOQZf9WQI9bwop2N6G p7vKW1xQfhI+q3hOTeDpG5D38xSZFKxb3pT3/9FlhjrackXbL6eckBL2cXm9Df2bqtxok6eVPoTD Fe/eKXzdjNz6FN5mb/yBCc/ZwvBpTFP0I0yefulRJq+O7JHaDhq7ZfDbWW4HU34PR++NWBMdgG1V /u7IzPYvUy5tzf1Dw9vG0Z+hWNtb9HH1n6p4hgO9gAGIpQRi8mk5C9X1FSn1/iPxAebfwM1G1l3A yhreIQm6zCBHu0vRdi8gqz2wwcjs4vKONfTOSB/RtANzPOOLiv8LjjYUGAuMQfr2wcBBRvZYRx+F WBhNwDAvVDP2V+krin6uo/n7lw3vQyrPj1oWbRXPKpN3oso7wOR9SeUdbPK0SducyWKyybfrn+sy 5OVZz96j+B9ztEOQtj8JMQPHI51pnPudgJhjpxNeEmS9px8o/6BoU5DRXvPdQ/KarlHxjXG02xWt hKyd/f00RAF0ek3l54Wun19D3qJogwvIAhkwtcx+jq5n5suQyeIURfttTvmHqjJ+AniX8P+Sq4/W A+uAzZyw5eph2ou2yOQ9Y+6fIhrpPkiovP5j3jF5+n59QvmPEpcDl7rr04C3TX4T8D6iXBr/Qcwg +74hHAqscdeT3e8dLm1MnEdkdoI4S0YDk4CfOtp+SEcbAcxNkbWdSz0NfYO6ftUljTeJrIAQ+iFK /oCirSeyktabX4j3t9HAPsCFwEuKvhUwE1jr7h9HHItzESeUxxL3rE7IQH2UyisB16v7lcCZ7vo3 SJu9ggxyHn9C2jPLyZWE10CUN0lZNlPXbxYQnDR66j/wvZS8IqPvO8hA8iyiUBuAVsiMBTAPUSAL vXa+FWmHbkgDh8zbMmJutgDudTzWegihPZWK3hExLy3eBj6HdK43csjOg4HITLHY3c8GvoX8t+2A bxA5ezxuSZE3x/2egjjZ7KB0oUsWRf5Tjd7I/+uh18NaeXX/CeE8xEI8GXgU2SHw8g4L8CeZwf0D NLuUg0h5R7vfxgCPXxq1RCa8esQCmgg8rfg6IQ6uX7n7epRuriZsshyv6N0czd/PN7yzVN62gYpC fF21xOSNU3nDTV6a2WxRT+U6sUeKvDKwlOLbCh1JNmeyUqsc8s+tQf6tRtYglefXhm2BBQVkPpzy Ho0ZZUuO199/PfC+f1P5GpsbWdqfslbRezvajYq2i+LVMo5Jkd9cycN771cn8N2peGco+jykL39a 0e7G7OT4tU0I2nGUZ5bJgnYEbQzTeDNkT7e3os1E1ucac5FlwE7ufgDSuAOBZYb3GqQBX0dGxzIy 0KxTPBchnQZkve7XnhOAJ931eGRdjym7E2KqvYLMvB+QvN+YF53N/RR1vSihzFRkK0RjDmLagczS ZxLNKK2Iv4fHNMRUvJ5s72serEH8KX6NeAFihhZBF3N/m7rWM/njxBW+KEJ6tI/7bUe0JAJ5n4fV /VGI38JjEaIj/RRtBNLm3keQ6obeN6NiRaGftTaRqzp0wr2QwlTEU2rrvgZxrMwi/o5LkRnvXFXm 2MCzjiHecY8gMmOGGj5v8mkzXdfHK+k2gee8BPwVMU03EDmDAB5E3td721sgs2lPxFnkUVLv8C6R qbark+eVcUbg+TsiXtESsg72vFMIKy7AIy4tI668tUQLnUhkqR2I9KMkv0oIE9T1/cT7nra4tkAs k/7IQLqOyAOdhXkZ+fua+y5I2y4A9gJuUHmTiLYk5yCTylKV/yoy4SwHWRPa6X4nRZul6LWYzTp6 xzphqjWb65GZzpoj33e/NxLG9S7//UBZO2iFTCLrja3GpAL5A0M8Fxs+uzXh0YPKwA6NkarM6er6 dmA6cFNKPXXdpqjrQ4ijUeUleZu1J7eI2eyh6+Vn3rxmsy7rrS2PjsAlSHuPBq5KaY+01Bo4Ghko Q2vgpHJN5v4HgbItiOtOrH9Y5a0zjNpz6Gna6wf5lHd7xWM3/6tV3q2pbJBh5t5GNNn16gHqeq+E uuvOC5XKVK3yWnieCwx9qMrzUWbHKFoojDHPNtpMUy8tc4C61nud1pnTmPGMEsXWliH8WOU/62h5 lNd2+iwLQLdHUeVNQr+UcsuRvfIyMnvfSKX/xSu132oarzOt8l6m7u8NCPJpIfGIlizlHax4TjN5 tTqsLnf5fn3ztCrzE8O7VOV5C6CByAMZglVevfentw30QKDjV8eb8knwPOcn0MtETos7FC20lm1F 5X+j0zKkbZLydac7Wl3bffjGjOeUkP+lFuW1gTJtyae856n77ybI1uhEtOPQEQnAaY8E+vg+ZtNM 0sNXH8t43z2IW0Unq7I7Gt5uGGjlnU5lI2kkVeApdZ2kvDrCxLrna1XeEvGtrV6mfo2OPirj/ZJg lddaJ0VSGkLKe7CiT1b0OmTLzeeFYnl/CdyHmM9HKN4BLj+pQ/rBx1/rENU05V2BbBVZ5e2MrN1f y0hvkKwIWmYT2cprlyTWkVcE2qxP6qch6JhpnbQlcSDSd3W+b4M/K9okK9w6rKaq652p3Mfriyiq xUKSI6s8mtT1igzeoigTd6I8hzirvId3GRLfq9fae5MveCKETXVcqw/wO3W/AFmTd0EGqCVE4Z6z EE+v3kPWBzl2NLJLxN9jMqKke9ZQ3zNdHXoR7R/7LY+2iBl7DvA1xN8AEn12CRJFtjpF9iwk6g9k nzkLR6rrNVQGhoA41Q5CdhPSHFT6RNl9SPBKB6JJqIQoXGtH95FYL6hy84kcmrOBfyJK7NtBv9/x iLNKW4O/CNQrNvM2ut+iBwDyrHn1yGJPHjXXPq9Gmtn47QJyoHLm1Q6r95Dth9uIxzMvUfR1pnwS 9Mxr12t50iMpsgcE+PWpmRFIIEAtM+8YpAM/GXgWxOOgdyM+O96d0Tb9EK+sD6LJYzb3Q/6fUCAG jl60jfMkX19N05ZPKO66wfBrvQyepqsnPvquREbId0PMNaC7uX8/yNW8WIt0WLt3ew9hr14R6MFn AvF9Xh8+eLSrQzdkdhlGMbySg+c9JNTOD3iDke2OxYiZOA7ZazwohyyrPFnWRTtk5tJOvtkp/IOQ CDKPOuKRRCOItk9CWIG0aRGsoPoQxFqxAplp5yNr76zt0dXI+WDvrNTx81eFCtRTucaoVXFDf7pe w10eyNcng5rLJN2ScGfaHxn1HyF7JgyhDfH1dRpmkDzqW/RS103A2Yji3Y9sfT2EeFpfpDK8dBwy ix5JtCe6GvhRyvOmk/5xgrqEa4964vuPSTgLCUjQTrVJREEKY5F4YRDP68aYPJLwALI89Pu6SdbR RUTLgAOQ/XeNEtJGrYl7/h9ArLYrqdzrDeFCKncaepIS/qnNj+ZIvePiK7yRoTOt+gTT/iZPHwnM MptbII3rv0SQla5DZoS0AaNlQN4J6jrN22xPqlwdkN+AzHqh+g0j+eMGGiXCX3o4W8laqa59mKH2 NkN8q0jv9Y9X19pstvvkq5C1s96FKCGRav7+eSoPtWvHm46ASvPiFgmPtEiTG8KlSm6WbycJY5SM PQL5vUnWxeAx1liQczNBO8HqEE+dxzzCQfd6BrBmTtoM0IC8dF9EyW2QPUiDbIesIRYQeVpBOquP dV2FmDiPI/HBK5GB5vWATH0ofySy1n0H6RTTXT07I3t285EO+pD7/QtiFfRA1kFJgSQgsy7AW65O qxDHSxlpZ5/aIIPQ7q7OJyJOlSsQZVjh3tvPlL4dbSfWSqVPlXVIqN8ZiJf7eMQz6meI4YiDEKTT 6ki1XYjPbmVXNz+bHYZEOS0nfyTVMwHaoyn8tyG+lLwoquwhhKzLEvLuVxNfUlgsdL/HAb9HrK8y xDX85CrTeiXDj+ol4t+PKlN5VMxDH0a365qxKk+boCXCjhG94B9C5azaxzVA2ozcgcrtpheIz2S1 pPNdvd4y9B0c/Ts1yg/NwvqAgp+VZqXIaIEo4RpDt5ZRCHMSZIaijzxmG15qbIO0dHNG/a3zyP5H 1eBIJeOryKQQkv8S4pTbEhl0k+qxPYZQLX6tZPgpvoTY+V6xL0kp3wlRqj5UjvKTlOwmk9eeypd6 EPdiGWgg+oCYTuNcvvX2aotgENkH8NOSP1qmT1rZDfgOyCdr7BceslLIUgAx1TyP/5LDXEU7zcgp UfkNsDKVp7RCeFTx/8P93pBaQiyVMuKL8LHe3ixfhzjwnke2AYum5109fFum+QI87iTcvkVPoHno b1gd4uplZYf2jvcM8P33aOIIZE1jFaMIuiIz7kAqQ/VKiFlVrXmu12GhkXsucg5yFNV/hrMrYt7d RFxJ/Qg8JKVsS2Qfc1dksDoMce2fhAw8JyDvP5HoaxhtVPnOrnwWWiLf+uqLmKQDE1LSp1v3ImpH 7/k+CxlcFxN96WQG8QFU+yPSPpym4WPHy8jhi+PI+BaTQ55Bd1PBBli8TfzgSVEMQZyJi5D/yW83 PoFMeFmO2v6IxWgjBv+PFOQ5f/tJRj35v+j4ScM25HMYVouadlY+BN3DikSxehoCAAAAAElFTkSu QmCC</pkg:binaryData>
    </pkg:part>
    <pkg:part pkg:name="/word/media/image2.png" pkg:contentType="image/png">
        <pkg:binaryData>iVBORw0KGgoAAAANSUhEUgAAAKcAAAAZCAYAAABU1j7pAAAKhklEQVR4nO2bfZDWVRXHP/sCuwu7 AgsrgaKCJclgSS/KlASEuw4hAbaFvIjYi0hiMxJqvjRrviVjNY1COWmZkpVOWlRqMwyWVDqaWxn4 kppspSEmBqyxksDTH+denvOcvff38uyzajN9Z87s8zv33HPv797zO/fcc+/C//FWRc2b3YE3G1UJ ZQX3dzPwrojM14FtwI3Arpxtfx/oBJ4G7gX256wfwqHA352unwALM9RpAsY4mgucA5wJ3BaR/ziw CdjunpuBLwGvZ+xjA/Bd4OEUuS5gMDATGadConT52IzMwTnAP/upjU8DN7nfyxF78agDngCWAL/N qrDgaJ/ijUEMwMoUgPfm7LCuOx4xjBnANGBqAk0DTnbyE4zO4Urng47X6p5fBl417SaR/XBbgBdd 2Q5VfkQOnZ4+nzI2DUZ+Zoo8QDXwQeCqDLIeLaadM3LUzYMLVRufUfxhpv0FMQV1wOHAEGCQqrDb 8cYA6x1vFbLsaMVJqHb664ABjqfrXkT+CS4A3zLt6H5f63jNZeq2Xvdtpvx8xx9Rhu4lKeO1UMk+ EyivBd4BnAZ8G9hr9E9N0e/xqKk3D5iTQO3A5Iy6NT6p2pij+FXAj0wfPhtScBz5Bnii+n1DSucm Kdktjqd1XZCzbU9rTDv1qmyV4vco/p+QkKIbWS5nAMcD57nyRchHlPYeBWAkMFQ9+49Fe3Dvjd6j eFEPgXzIuo2VwL+Av+Ucm4aENgBm59TnaWmK3hDOVPU/Eii/3LSxwgrkMc6TgfnqeVJK58YqWW/I Wl8jMskDkMmpcuTLdyletZMbBhxi2qlTdVYq/rHAAcevBXYquVonc7Xi/SXhXTqV3DWuD/pdnkOW fcvTz6cn6D9Lya0HRpu6MXoN+CbwUWSVS9pPTMio09KzCTqTcIbScUpEZolpq1UXDkVebDoSu2jB Beq3X2aezfAy9znZwxSvw/G0XAy+fGeCzEBkM/IM8KTR6w3ly4p3JTBLPfsl9j+KNyqhPR9j+jiw KfDeaTQ/orvOyPn4/gHFexhYTbbxC+GEhH4dpeSqgLtMeVOK7p8l6M5Cf3Z/tyGbQUDipibEi3jP 5SvsQDZF/vlIZDnL0tgjTr/++i9zPDu470SyApq0jC0b7+rZCQ2RXSoHm+dD1e9H42N/EPXqt/Wc zyMhg+VlMc6vBcYlhnKMU3vlAhLeLDe80cj8rzf8ozPo1x9ROfRhJASogeKSlpRCaDbPXeb5FmRn PBpJqQBsBB4CfpfhhTyeTCl/LMDzH5HHfsL5wUYkttzontuA7wGLkZTRxUr2PIrhwl5kUq5A0i17 VRudwM8Rw/e4CTgbiTlfdrwlwDok5uxU/baYSHGT1R+YDHxHPT+AvP8BZCWZ5fgvBOoeR3Ko43Gr 09cF7KE4N6dRXI02I6k4j3PV7y7gfqu0L9bujeFExTvJ6M/iOctp2yIUc5a72Sognia2cdjq9Nvd +r6ArOUtMv22nry/PGeD68tXKf1AqoDfRPowNof+GBYpfa2mTMfjg3WB95xzECPrQTxDA+LWPbYi HqHGKdd5Oj84RyheuYGzx3D3d4f7ewDJyWleFlTnlLdIqvuU+2t3xSHPbXlD1O8G4JWc/SoXPchm 0s9ZA+JRr0D2GSGcjuwdNlOZgxILbfz/1gXeOH/q/rYgyW2bP7wEWd7r6Z1AnoCkh+Yp3nb6BjtZ 3QFeGq5DPMTtijcb+EdKvSnIyReIR/FLt/bwUBzI180zGA8QKHtRPTcjm7oQfk96JkT3yeJcJNQY hxhiE5K5WIAcZmTBNY50W39FQsHnEa/Yk1FXLtSq3/VI/DgrIPeDBB1nIQarc3d5lppKoYrSAfex 4DGKNxTxhj6tFII+hmx2siHsR07FhgMfcs9+eRqOGDXAWiS+rVI0ABnnh5AYrwWZ7FHITtVjTKTt rBiELOP3kW95/jXwDcLzXoXs6o9Cjht7kHFYh+y2u12bety041rj9Idws/s7GLd/aUTcetY4bKX7 +7Ti6d3wrwKN5o05MbydAZ6Xm0A8rbUb8Tx5T4maKa4UFl7mhzl1hugEpdc7Cl2+GllOfwHcgxi8 Jy2n+fcAG4CXKKa76gm3fz8Spg2kNM+oNylDkROrzkB97wDaKzAWlqbXIpP3RdWZl5AJPwQJVj22 I1/IC8hX1UoxHGhTcmvpjaSEcF/RTTjNcS2lu/CsOJ/sIcSryMddCewL8C5yFIL+iE9N0f0a8Dkk /bYQyUd2Id7tROQjuUDJT0OMUuNu5DLNc8AvkTAhtqpUAgczSNOQl11K0ZCmULTi25EgfjLwBVc2 gLDF25cCWZ58eQelu+onnEy5nlPz9Bm9PiECGcwrVfk8ZPmwSfQ0Y9OesxVZioc4PY3IxMW8cRPy 0Y9A8sWhI8bQ+yX1I01OQ+dz+0IWjciGuAV5v0ZkbD2drerONWWaHlNy+oIRNUiMMhGZuKRlq8PV uTdDx0ECcl9+HfA+9XyJk+mLcU5FJj92fOlhl/dqZAnzz/asPgT7vjpLMT1Q7ukp4mf2Mf2VkNOo hHE+0ktrOpJSSRq7lFwNxD1gEi12ysYZvl7eNfRpz93IfUb/PMXJJBlnAQkntkfkPNKMEyTo9jL2 RGZQpI5GaDwakdAiy9iNzqG/EnJZsELpWhcof78qT9ocx5DVOIPv9DjJA3ozsuvVu/sqZLnXcrcR ji+bgY85mmvqhDYCtrM7kV2slgnFtlmM0x432g8uDbbe9ZQeQBQoXXVC1wGXEY/D32jjPMnoGheQ manKO8poI4tx2oOIg/CDexVyzFYPvF0J2oD7MGTTEJrkbuLeYYiRvVyVpRmnHqBNhC8hZDFOkNs7 tt/VCfIe+s5BAVmGbjC8TZTePVhK6cUXTV+hd0bAjsMk4BNI6ukUZHJbjZzntSHj1E5pJiAGe/p1 WURO38eM3QtIwmLTVw+9r9G0NaZoPJLD0q5+tiurpfRWs6djA7zVlAa19tb1HooXjwHuRM7o9QGA NvhG5IJt6ATmcOADSOwZM85aZPJ0XGPpVmTAYrGh9fqWNiBGbo0TZBO0J1DHelBrnD9OaTNGj0fe oYnS646edqu+HI/cRZiB7D+0XBajt9DGbUO/UBowdq2OLQHhNnrfmi4Ad1CcyGGU3pP0VIck6S0/ SwrGy4bSLBqjAvqXAe8G/hgo89SRUFZAUlHao/5BlbUjJyf6Y/CTq/O6y1T9KkpvBo2nN6xx3pHS xxjp21UjkZOxmOwWZP48Tk2QzRKXW6xS9e1lYz0edxH/fzWgtxd8EBnUjYZvL3eATORaJdNEeElr CdQNwU5UVtkCMti1lB4WeLqe4vl2I73vRxaI/wNaG8Udq7/zaOM0vVG8MKBjIOEb4fY9QAzraOTf REYgp08xGuHkx9I7tLoz8I7bEC8ZwoGA/PKIbBp0VqfdlOXKgdcghngpYqia343krGJnwR5HInGs xsXIlbM8X163q7Mhg+waxGhW0Nsra09oz709apDlxP9PyzERuSzQN81X56yrjaGShxc6Xr6F9PuZ 8xGD/hSS3077t48k3Kjanpa1Un+e3MTay+IF/9fbraN4Lv4Kb9ytozSMRE5e+vNkp2L4LwGI47nl 5/RWAAAAAElFTkSuQmCC</pkg:binaryData>
    </pkg:part>
    <pkg:part pkg:name="/word/settings.xml" pkg:contentType="application/vnd.openxmlformats-officedocument.wordprocessingml.settings+xml">
        <pkg:xmlData>
            <w:settings xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w10="urn:schemas-microsoft-com:office:word" xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main" xmlns:w14="http://schemas.microsoft.com/office/word/2010/wordml" xmlns:sl="http://schemas.openxmlformats.org/schemaLibrary/2006/main" xmlns:wpsCustomData="http://www.wps.cn/officeDocument/2013/wpsCustomData" mc:Ignorable="w14">
                <w:zoom w:percent="100"/>
                <w:displayBackgroundShape w:val="1"/>
                <w:bordersDoNotSurroundHeader w:val="0"/>
                <w:bordersDoNotSurroundFooter w:val="0"/>
                <w:documentProtection w:enforcement="0"/>
                <w:displayHorizontalDrawingGridEvery w:val="1"/>
                <w:displayVerticalDrawingGridEvery w:val="1"/>
                <w:noPunctuationKerning w:val="1"/>
                <w:characterSpacingControl w:val="doNotCompress"/>
                <w:footnotePr>
                    <w:footnote w:id="0"/>
                    <w:footnote w:id="1"/>
                </w:footnotePr>
                <w:endnotePr>
                    <w:endnote w:id="0"/>
                    <w:endnote w:id="1"/>
                </w:endnotePr>
                <w:compat>
                    <w:spaceForUL/>
                    <w:ulTrailSpace/>
                    <w:doNotExpandShiftReturn/>
                    <w:doNotWrapTextWithPunct/>
                    <w:doNotUseEastAsianBreakRules/>
                    <w:useFELayout/>
                    <w:doNotUseIndentAsNumberingTabStop/>
                    <w:compatSetting w:name="compatibilityMode" w:uri="http://schemas.microsoft.com/office/word" w:val="14"/>
                </w:compat>
                <w:docVars>
                    <w:docVar w:name="commondata" w:val="eyJoZGlkIjoiN2QzMTU4NTRmYTU4YzUyZDJhZTBlOGI3YTIzN2IzZjgifQ=="/>
                </w:docVars>
                <w:rsids>
                    <w:rsidRoot w:val="00000000"/>
                    <w:rsid w:val="1C3C45BD"/>
                    <w:rsid w:val="1DEF28E6"/>
                    <w:rsid w:val="1E7A630E"/>
                    <w:rsid w:val="3415343B"/>
                    <w:rsid w:val="34D3493A"/>
                    <w:rsid w:val="37180CA8"/>
                    <w:rsid w:val="419F5860"/>
                    <w:rsid w:val="45921C25"/>
                    <w:rsid w:val="58913C74"/>
                    <w:rsid w:val="5F792D22"/>
                    <w:rsid w:val="606A75CF"/>
                    <w:rsid w:val="64E4239C"/>
                    <w:rsid w:val="6A773014"/>
                    <w:rsid w:val="77811976"/>
                    <w:rsid w:val="78DF4BA6"/>
                    <w:rsid w:val="F2F94F42"/>
                </w:rsids>
                <m:mathPr>
                    <m:brkBin m:val="before"/>
                    <m:brkBinSub m:val="--"/>
                    <m:smallFrac m:val="0"/>
                    <m:dispDef/>
                    <m:lMargin m:val="0"/>
                    <m:rMargin m:val="0"/>
                    <m:defJc m:val="centerGroup"/>
                    <m:wrapIndent m:val="1440"/>
                    <m:intLim m:val="subSup"/>
                    <m:naryLim m:val="undOvr"/>
                </m:mathPr>
                <w:themeFontLang w:val="en-US" w:eastAsia="zh-CN"/>
                <w:clrSchemeMapping w:bg1="light1" w:t1="dark1" w:bg2="light2" w:t2="dark2" w:accent1="accent1" w:accent2="accent2" w:accent3="accent3" w:accent4="accent4" w:accent5="accent5" w:accent6="accent6" w:hyperlink="hyperlink" w:followedHyperlink="followedHyperlink"/>
            </w:settings>
        </pkg:xmlData>
    </pkg:part>
    <pkg:part pkg:name="/word/styles.xml" pkg:contentType="application/vnd.openxmlformats-officedocument.wordprocessingml.styles+xml">
        <pkg:xmlData>
            <w:styles xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main" xmlns:w14="http://schemas.microsoft.com/office/word/2010/wordml" xmlns:w10="urn:schemas-microsoft-com:office:word" xmlns:sl="http://schemas.openxmlformats.org/schemaLibrary/2006/main" xmlns:wpsCustomData="http://www.wps.cn/officeDocument/2013/wpsCustomData" mc:Ignorable="w14">
                <w:docDefaults>
                    <w:rPrDefault>
                        <w:rPr>
                            <w:rFonts w:ascii="Times New Roman" w:hAnsi="Times New Roman" w:eastAsia="宋体" w:cs="Times New Roman"/>
                        </w:rPr>
                    </w:rPrDefault>
                    <w:pPrDefault/>
                </w:docDefaults>
                <w:latentStyles w:count="260" w:defQFormat="0" w:defUnhideWhenUsed="1" w:defSemiHidden="1" w:defUIPriority="99" w:defLockedState="0">
                    <w:lsdException w:qFormat="1" w:unhideWhenUsed="0" w:uiPriority="0" w:name="Normal"/>
                    <w:lsdException w:qFormat="1" w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="heading 1"/>
                    <w:lsdException w:qFormat="1" w:uiPriority="0" w:name="heading 2"/>
                    <w:lsdException w:qFormat="1" w:uiPriority="0" w:name="heading 3"/>
                    <w:lsdException w:qFormat="1" w:uiPriority="0" w:name="heading 4"/>
                    <w:lsdException w:qFormat="1" w:uiPriority="0" w:name="heading 5"/>
                    <w:lsdException w:qFormat="1" w:uiPriority="0" w:name="heading 6"/>
                    <w:lsdException w:qFormat="1" w:uiPriority="0" w:name="heading 7"/>
                    <w:lsdException w:qFormat="1" w:uiPriority="0" w:name="heading 8"/>
                    <w:lsdException w:qFormat="1" w:uiPriority="0" w:name="heading 9"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="index 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="index 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="index 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="index 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="index 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="index 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="index 7"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="index 8"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="index 9"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="toc 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="toc 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="toc 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="toc 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="toc 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="toc 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="toc 7"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="toc 8"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="toc 9"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Normal Indent"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="footnote text"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="annotation text"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="header"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="footer"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="index heading"/>
                    <w:lsdException w:qFormat="1" w:uiPriority="0" w:name="caption"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="table of figures"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="envelope address"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="envelope return"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="footnote reference"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="annotation reference"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="line number"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="page number"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="endnote reference"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="endnote text"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="table of authorities"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="macro"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="toa heading"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Bullet"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Number"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Bullet 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Bullet 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Bullet 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Bullet 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Number 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Number 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Number 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Number 5"/>
                    <w:lsdException w:qFormat="1" w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Title"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Closing"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Signature"/>
                    <w:lsdException w:qFormat="1" w:unhideWhenUsed="0" w:uiPriority="0" w:name="Default Paragraph Font"/>
                    <w:lsdException w:qFormat="1" w:unhideWhenUsed="0" w:uiPriority="0" w:name="Body Text"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Body Text Indent"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Continue"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Continue 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Continue 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Continue 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Continue 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Message Header"/>
                    <w:lsdException w:qFormat="1" w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Subtitle"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Salutation"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Date"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Body Text First Indent"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Body Text First Indent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Note Heading"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Body Text 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Body Text 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Body Text Indent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Body Text Indent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Block Text"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Hyperlink"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="FollowedHyperlink"/>
                    <w:lsdException w:qFormat="1" w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Strong"/>
                    <w:lsdException w:qFormat="1" w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Emphasis"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Document Map"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Plain Text"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="E-mail Signature"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Normal (Web)"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="HTML Acronym"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="HTML Address"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="HTML Cite"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="HTML Code"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="HTML Definition"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="HTML Keyboard"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="HTML Preformatted"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="HTML Sample"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="HTML Typewriter"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="HTML Variable"/>
                    <w:lsdException w:qFormat="1" w:unhideWhenUsed="0" w:uiPriority="0" w:name="Normal Table"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="annotation subject"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Simple 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Simple 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Simple 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Classic 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Classic 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Classic 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Classic 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Colorful 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Colorful 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Colorful 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Columns 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Columns 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Columns 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Columns 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Columns 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Grid 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Grid 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Grid 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Grid 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Grid 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Grid 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Grid 7"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Grid 8"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table List 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table List 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table List 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table List 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table List 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table List 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table List 7"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table List 8"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table 3D effects 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table 3D effects 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table 3D effects 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Contemporary"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Elegant"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Professional"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Subtle 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Subtle 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Web 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Web 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Web 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Balloon Text"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Grid"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Theme"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="60" w:semiHidden="0" w:name="Light Shading"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="61" w:semiHidden="0" w:name="Light List"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="62" w:semiHidden="0" w:name="Light Grid"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="63" w:semiHidden="0" w:name="Medium Shading 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="64" w:semiHidden="0" w:name="Medium Shading 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="65" w:semiHidden="0" w:name="Medium List 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="66" w:semiHidden="0" w:name="Medium List 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="67" w:semiHidden="0" w:name="Medium Grid 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="68" w:semiHidden="0" w:name="Medium Grid 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="69" w:semiHidden="0" w:name="Medium Grid 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="70" w:semiHidden="0" w:name="Dark List"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="71" w:semiHidden="0" w:name="Colorful Shading"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="72" w:semiHidden="0" w:name="Colorful List"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="73" w:semiHidden="0" w:name="Colorful Grid"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="60" w:semiHidden="0" w:name="Light Shading Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="61" w:semiHidden="0" w:name="Light List Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="62" w:semiHidden="0" w:name="Light Grid Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="63" w:semiHidden="0" w:name="Medium Shading 1 Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="64" w:semiHidden="0" w:name="Medium Shading 2 Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="65" w:semiHidden="0" w:name="Medium List 1 Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="66" w:semiHidden="0" w:name="Medium List 2 Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="67" w:semiHidden="0" w:name="Medium Grid 1 Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="68" w:semiHidden="0" w:name="Medium Grid 2 Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="69" w:semiHidden="0" w:name="Medium Grid 3 Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="70" w:semiHidden="0" w:name="Dark List Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="71" w:semiHidden="0" w:name="Colorful Shading Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="72" w:semiHidden="0" w:name="Colorful List Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="73" w:semiHidden="0" w:name="Colorful Grid Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="60" w:semiHidden="0" w:name="Light Shading Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="61" w:semiHidden="0" w:name="Light List Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="62" w:semiHidden="0" w:name="Light Grid Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="63" w:semiHidden="0" w:name="Medium Shading 1 Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="64" w:semiHidden="0" w:name="Medium Shading 2 Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="65" w:semiHidden="0" w:name="Medium List 1 Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="66" w:semiHidden="0" w:name="Medium List 2 Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="67" w:semiHidden="0" w:name="Medium Grid 1 Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="68" w:semiHidden="0" w:name="Medium Grid 2 Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="69" w:semiHidden="0" w:name="Medium Grid 3 Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="70" w:semiHidden="0" w:name="Dark List Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="71" w:semiHidden="0" w:name="Colorful Shading Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="72" w:semiHidden="0" w:name="Colorful List Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="73" w:semiHidden="0" w:name="Colorful Grid Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="60" w:semiHidden="0" w:name="Light Shading Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="61" w:semiHidden="0" w:name="Light List Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="62" w:semiHidden="0" w:name="Light Grid Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="63" w:semiHidden="0" w:name="Medium Shading 1 Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="64" w:semiHidden="0" w:name="Medium Shading 2 Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="65" w:semiHidden="0" w:name="Medium List 1 Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="66" w:semiHidden="0" w:name="Medium List 2 Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="67" w:semiHidden="0" w:name="Medium Grid 1 Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="68" w:semiHidden="0" w:name="Medium Grid 2 Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="69" w:semiHidden="0" w:name="Medium Grid 3 Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="70" w:semiHidden="0" w:name="Dark List Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="71" w:semiHidden="0" w:name="Colorful Shading Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="72" w:semiHidden="0" w:name="Colorful List Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="73" w:semiHidden="0" w:name="Colorful Grid Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="60" w:semiHidden="0" w:name="Light Shading Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="61" w:semiHidden="0" w:name="Light List Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="62" w:semiHidden="0" w:name="Light Grid Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="63" w:semiHidden="0" w:name="Medium Shading 1 Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="64" w:semiHidden="0" w:name="Medium Shading 2 Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="65" w:semiHidden="0" w:name="Medium List 1 Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="66" w:semiHidden="0" w:name="Medium List 2 Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="67" w:semiHidden="0" w:name="Medium Grid 1 Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="68" w:semiHidden="0" w:name="Medium Grid 2 Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="69" w:semiHidden="0" w:name="Medium Grid 3 Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="70" w:semiHidden="0" w:name="Dark List Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="71" w:semiHidden="0" w:name="Colorful Shading Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="72" w:semiHidden="0" w:name="Colorful List Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="73" w:semiHidden="0" w:name="Colorful Grid Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="60" w:semiHidden="0" w:name="Light Shading Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="61" w:semiHidden="0" w:name="Light List Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="62" w:semiHidden="0" w:name="Light Grid Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="63" w:semiHidden="0" w:name="Medium Shading 1 Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="64" w:semiHidden="0" w:name="Medium Shading 2 Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="65" w:semiHidden="0" w:name="Medium List 1 Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="66" w:semiHidden="0" w:name="Medium List 2 Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="67" w:semiHidden="0" w:name="Medium Grid 1 Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="68" w:semiHidden="0" w:name="Medium Grid 2 Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="69" w:semiHidden="0" w:name="Medium Grid 3 Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="70" w:semiHidden="0" w:name="Dark List Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="71" w:semiHidden="0" w:name="Colorful Shading Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="72" w:semiHidden="0" w:name="Colorful List Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="73" w:semiHidden="0" w:name="Colorful Grid Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="60" w:semiHidden="0" w:name="Light Shading Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="61" w:semiHidden="0" w:name="Light List Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="62" w:semiHidden="0" w:name="Light Grid Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="63" w:semiHidden="0" w:name="Medium Shading 1 Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="64" w:semiHidden="0" w:name="Medium Shading 2 Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="65" w:semiHidden="0" w:name="Medium List 1 Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="66" w:semiHidden="0" w:name="Medium List 2 Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="67" w:semiHidden="0" w:name="Medium Grid 1 Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="68" w:semiHidden="0" w:name="Medium Grid 2 Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="69" w:semiHidden="0" w:name="Medium Grid 3 Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="70" w:semiHidden="0" w:name="Dark List Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="71" w:semiHidden="0" w:name="Colorful Shading Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="72" w:semiHidden="0" w:name="Colorful List Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="73" w:semiHidden="0" w:name="Colorful Grid Accent 6"/>
                </w:latentStyles>
                <w:style w:type="paragraph" w:default="1" w:styleId="1">
                    <w:name w:val="Normal"/>
                    <w:semiHidden/>
                    <w:qFormat/>
                    <w:uiPriority w:val="0"/>
                    <w:pPr>
                        <w:kinsoku w:val="0"/>
                        <w:autoSpaceDE w:val="0"/>
                        <w:autoSpaceDN w:val="0"/>
                        <w:adjustRightInd w:val="0"/>
                        <w:snapToGrid w:val="0"/>
                        <w:spacing w:line="240" w:lineRule="auto"/>
                        <w:jc w:val="left"/>
                        <w:textAlignment w:val="baseline"/>
                    </w:pPr>
                    <w:rPr>
                        <w:rFonts w:ascii="Arial" w:hAnsi="Arial" w:eastAsia="Arial" w:cs="Arial"/>
                        <w:snapToGrid w:val="0"/>
                        <w:color w:val="000000"/>
                        <w:kern w:val="0"/>
                        <w:sz w:val="21"/>
                        <w:szCs w:val="21"/>
                        <w:lang w:val="en-US" w:eastAsia="en-US" w:bidi="ar-SA"/>
                    </w:rPr>
                </w:style>
                <w:style w:type="character" w:default="1" w:styleId="5">
                    <w:name w:val="Default Paragraph Font"/>
                    <w:semiHidden/>
                    <w:qFormat/>
                    <w:uiPriority w:val="0"/>
                </w:style>
                <w:style w:type="table" w:default="1" w:styleId="4">
                    <w:name w:val="Normal Table"/>
                    <w:semiHidden/>
                    <w:qFormat/>
                    <w:uiPriority w:val="0"/>
                    <w:tblPr>
                        <w:tblCellMar>
                            <w:top w:w="0" w:type="dxa"/>
                            <w:left w:w="108" w:type="dxa"/>
                            <w:bottom w:w="0" w:type="dxa"/>
                            <w:right w:w="108" w:type="dxa"/>
                        </w:tblCellMar>
                    </w:tblPr>
                </w:style>
                <w:style w:type="paragraph" w:styleId="2">
                    <w:name w:val="Body Text"/>
                    <w:basedOn w:val="1"/>
                    <w:semiHidden/>
                    <w:qFormat/>
                    <w:uiPriority w:val="0"/>
                    <w:rPr>
                        <w:rFonts w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑" w:cs="微软雅黑"/>
                        <w:sz w:val="24"/>
                        <w:szCs w:val="24"/>
                        <w:lang w:val="en-US" w:eastAsia="en-US" w:bidi="ar-SA"/>
                    </w:rPr>
                </w:style>
                <w:style w:type="paragraph" w:styleId="3">
                    <w:name w:val="Normal (Web)"/>
                    <w:basedOn w:val="1"/>
                    <w:uiPriority w:val="0"/>
                    <w:pPr>
                        <w:spacing w:before="0" w:beforeAutospacing="1" w:after="0" w:afterAutospacing="1"/>
                        <w:ind w:left="0" w:right="0"/>
                        <w:jc w:val="left"/>
                    </w:pPr>
                    <w:rPr>
                        <w:kern w:val="0"/>
                        <w:sz w:val="24"/>
                        <w:lang w:val="en-US" w:eastAsia="zh-CN" w:bidi="ar"/>
                    </w:rPr>
                </w:style>
                <w:style w:type="table" w:customStyle="1" w:styleId="6">
                    <w:name w:val="Table Normal"/>
                    <w:semiHidden/>
                    <w:unhideWhenUsed/>
                    <w:qFormat/>
                    <w:uiPriority w:val="0"/>
                    <w:tblPr>
                        <w:tblCellMar>
                            <w:top w:w="0" w:type="dxa"/>
                            <w:left w:w="0" w:type="dxa"/>
                            <w:bottom w:w="0" w:type="dxa"/>
                            <w:right w:w="0" w:type="dxa"/>
                        </w:tblCellMar>
                    </w:tblPr>
                </w:style>
                <w:style w:type="paragraph" w:customStyle="1" w:styleId="7">
                    <w:name w:val="Table Text"/>
                    <w:basedOn w:val="1"/>
                    <w:semiHidden/>
                    <w:qFormat/>
                    <w:uiPriority w:val="0"/>
                    <w:rPr>
                        <w:rFonts w:ascii="微软雅黑" w:hAnsi="微软雅黑" w:eastAsia="微软雅黑" w:cs="微软雅黑"/>
                        <w:sz w:val="21"/>
                        <w:szCs w:val="21"/>
                        <w:lang w:val="en-US" w:eastAsia="en-US" w:bidi="ar-SA"/>
                    </w:rPr>
                </w:style>
            </w:styles>
        </pkg:xmlData>
    </pkg:part>
    <pkg:part pkg:name="/word/theme/theme1.xml" pkg:contentType="application/vnd.openxmlformats-officedocument.theme+xml">
        <pkg:xmlData>
            <a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office">
                <a:themeElements>
                    <a:clrScheme name="Office">
                        <a:dk1>
                            <a:sysClr val="windowText" lastClr="000000"/>
                        </a:dk1>
                        <a:lt1>
                            <a:sysClr val="window" lastClr="FFFFFF"/>
                        </a:lt1>
                        <a:dk2>
                            <a:srgbClr val="1F497D"/>
                        </a:dk2>
                        <a:lt2>
                            <a:srgbClr val="EEECE1"/>
                        </a:lt2>
                        <a:accent1>
                            <a:srgbClr val="4F81BD"/>
                        </a:accent1>
                        <a:accent2>
                            <a:srgbClr val="C0504D"/>
                        </a:accent2>
                        <a:accent3>
                            <a:srgbClr val="9BBB59"/>
                        </a:accent3>
                        <a:accent4>
                            <a:srgbClr val="8064A2"/>
                        </a:accent4>
                        <a:accent5>
                            <a:srgbClr val="4BACC6"/>
                        </a:accent5>
                        <a:accent6>
                            <a:srgbClr val="F79646"/>
                        </a:accent6>
                        <a:hlink>
                            <a:srgbClr val="0000FF"/>
                        </a:hlink>
                        <a:folHlink>
                            <a:srgbClr val="800080"/>
                        </a:folHlink>
                    </a:clrScheme>
                    <a:fontScheme name="Office">
                        <a:majorFont>
                            <a:latin typeface="Cambria"/>
                            <a:ea typeface=""/>
                            <a:cs typeface=""/>
                            <a:font script="Jpan" typeface="ＭＳ ゴシック"/>
                            <a:font script="Hang" typeface="맑은 고딕"/>
                            <a:font script="Hans" typeface="宋体"/>
                            <a:font script="Hant" typeface="新細明體"/>
                            <a:font script="Arab" typeface="Times New Roman"/>
                            <a:font script="Hebr" typeface="Times New Roman"/>
                            <a:font script="Thai" typeface="Angsana New"/>
                            <a:font script="Ethi" typeface="Nyala"/>
                            <a:font script="Beng" typeface="Vrinda"/>
                            <a:font script="Gujr" typeface="Shruti"/>
                            <a:font script="Khmr" typeface="MoolBoran"/>
                            <a:font script="Knda" typeface="Tunga"/>
                            <a:font script="Guru" typeface="Raavi"/>
                            <a:font script="Cans" typeface="Euphemia"/>
                            <a:font script="Cher" typeface="Plantagenet Cherokee"/>
                            <a:font script="Yiii" typeface="Microsoft Yi Baiti"/>
                            <a:font script="Tibt" typeface="Microsoft Himalaya"/>
                            <a:font script="Thaa" typeface="MV Boli"/>
                            <a:font script="Deva" typeface="Mangal"/>
                            <a:font script="Telu" typeface="Gautami"/>
                            <a:font script="Taml" typeface="Latha"/>
                            <a:font script="Syrc" typeface="Estrangelo Edessa"/>
                            <a:font script="Orya" typeface="Kalinga"/>
                            <a:font script="Mlym" typeface="Kartika"/>
                            <a:font script="Laoo" typeface="DokChampa"/>
                            <a:font script="Sinh" typeface="Iskoola Pota"/>
                            <a:font script="Mong" typeface="Mongolian Baiti"/>
                            <a:font script="Viet" typeface="Times New Roman"/>
                            <a:font script="Uigh" typeface="Microsoft Uighur"/>
                            <a:font script="Geor" typeface="Sylfaen"/>
                        </a:majorFont>
                        <a:minorFont>
                            <a:latin typeface="Calibri"/>
                            <a:ea typeface=""/>
                            <a:cs typeface=""/>
                            <a:font script="Jpan" typeface="ＭＳ 明朝"/>
                            <a:font script="Hang" typeface="맑은 고딕"/>
                            <a:font script="Hans" typeface="宋体"/>
                            <a:font script="Hant" typeface="新細明體"/>
                            <a:font script="Arab" typeface="Arial"/>
                            <a:font script="Hebr" typeface="Arial"/>
                            <a:font script="Thai" typeface="Cordia New"/>
                            <a:font script="Ethi" typeface="Nyala"/>
                            <a:font script="Beng" typeface="Vrinda"/>
                            <a:font script="Gujr" typeface="Shruti"/>
                            <a:font script="Khmr" typeface="DaunPenh"/>
                            <a:font script="Knda" typeface="Tunga"/>
                            <a:font script="Guru" typeface="Raavi"/>
                            <a:font script="Cans" typeface="Euphemia"/>
                            <a:font script="Cher" typeface="Plantagenet Cherokee"/>
                            <a:font script="Yiii" typeface="Microsoft Yi Baiti"/>
                            <a:font script="Tibt" typeface="Microsoft Himalaya"/>
                            <a:font script="Thaa" typeface="MV Boli"/>
                            <a:font script="Deva" typeface="Mangal"/>
                            <a:font script="Telu" typeface="Gautami"/>
                            <a:font script="Taml" typeface="Latha"/>
                            <a:font script="Syrc" typeface="Estrangelo Edessa"/>
                            <a:font script="Orya" typeface="Kalinga"/>
                            <a:font script="Mlym" typeface="Kartika"/>
                            <a:font script="Laoo" typeface="DokChampa"/>
                            <a:font script="Sinh" typeface="Iskoola Pota"/>
                            <a:font script="Mong" typeface="Mongolian Baiti"/>
                            <a:font script="Viet" typeface="Arial"/>
                            <a:font script="Uigh" typeface="Microsoft Uighur"/>
                            <a:font script="Geor" typeface="Sylfaen"/>
                        </a:minorFont>
                    </a:fontScheme>
                    <a:fmtScheme name="Office">
                        <a:fillStyleLst>
                            <a:solidFill>
                                <a:schemeClr val="phClr"/>
                            </a:solidFill>
                            <a:gradFill rotWithShape="1">
                                <a:gsLst>
                                    <a:gs pos="0">
                                        <a:schemeClr val="phClr">
                                            <a:tint val="50000"/>
                                            <a:satMod val="300000"/>
                                        </a:schemeClr>
                                    </a:gs>
                                    <a:gs pos="35000">
                                        <a:schemeClr val="phClr">
                                            <a:tint val="37000"/>
                                            <a:satMod val="300000"/>
                                        </a:schemeClr>
                                    </a:gs>
                                    <a:gs pos="100000">
                                        <a:schemeClr val="phClr">
                                            <a:tint val="15000"/>
                                            <a:satMod val="350000"/>
                                        </a:schemeClr>
                                    </a:gs>
                                </a:gsLst>
                                <a:lin ang="16200000" scaled="1"/>
                            </a:gradFill>
                            <a:gradFill rotWithShape="1">
                                <a:gsLst>
                                    <a:gs pos="0">
                                        <a:schemeClr val="phClr">
                                            <a:shade val="51000"/>
                                            <a:satMod val="130000"/>
                                        </a:schemeClr>
                                    </a:gs>
                                    <a:gs pos="80000">
                                        <a:schemeClr val="phClr">
                                            <a:shade val="93000"/>
                                            <a:satMod val="130000"/>
                                        </a:schemeClr>
                                    </a:gs>
                                    <a:gs pos="100000">
                                        <a:schemeClr val="phClr">
                                            <a:shade val="94000"/>
                                            <a:satMod val="135000"/>
                                        </a:schemeClr>
                                    </a:gs>
                                </a:gsLst>
                                <a:lin ang="16200000" scaled="0"/>
                            </a:gradFill>
                        </a:fillStyleLst>
                        <a:lnStyleLst>
                            <a:ln w="9525" cap="flat" cmpd="sng" algn="ctr">
                                <a:solidFill>
                                    <a:schemeClr val="phClr">
                                        <a:shade val="95000"/>
                                        <a:satMod val="105000"/>
                                    </a:schemeClr>
                                </a:solidFill>
                                <a:prstDash val="solid"/>
                            </a:ln>
                            <a:ln w="25400" cap="flat" cmpd="sng" algn="ctr">
                                <a:solidFill>
                                    <a:schemeClr val="phClr"/>
                                </a:solidFill>
                                <a:prstDash val="solid"/>
                            </a:ln>
                            <a:ln w="38100" cap="flat" cmpd="sng" algn="ctr">
                                <a:solidFill>
                                    <a:schemeClr val="phClr"/>
                                </a:solidFill>
                                <a:prstDash val="solid"/>
                            </a:ln>
                        </a:lnStyleLst>
                        <a:effectStyleLst>
                            <a:effectStyle>
                                <a:effectLst>
                                    <a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0">
                                        <a:srgbClr val="000000">
                                            <a:alpha val="38000"/>
                                        </a:srgbClr>
                                    </a:outerShdw>
                                </a:effectLst>
                            </a:effectStyle>
                            <a:effectStyle>
                                <a:effectLst>
                                    <a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0">
                                        <a:srgbClr val="000000">
                                            <a:alpha val="35000"/>
                                        </a:srgbClr>
                                    </a:outerShdw>
                                </a:effectLst>
                            </a:effectStyle>
                            <a:effectStyle>
                                <a:effectLst>
                                    <a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0">
                                        <a:srgbClr val="000000">
                                            <a:alpha val="35000"/>
                                        </a:srgbClr>
                                    </a:outerShdw>
                                </a:effectLst>
                                <a:scene3d>
                                    <a:camera prst="orthographicFront">
                                        <a:rot lat="0" lon="0" rev="0"/>
                                    </a:camera>
                                    <a:lightRig rig="threePt" dir="t">
                                        <a:rot lat="0" lon="0" rev="1200000"/>
                                    </a:lightRig>
                                </a:scene3d>
                                <a:sp3d>
                                    <a:bevelT w="63500" h="25400"/>
                                </a:sp3d>
                            </a:effectStyle>
                        </a:effectStyleLst>
                        <a:bgFillStyleLst>
                            <a:solidFill>
                                <a:schemeClr val="phClr"/>
                            </a:solidFill>
                            <a:gradFill rotWithShape="1">
                                <a:gsLst>
                                    <a:gs pos="0">
                                        <a:schemeClr val="phClr">
                                            <a:tint val="40000"/>
                                            <a:satMod val="350000"/>
                                        </a:schemeClr>
                                    </a:gs>
                                    <a:gs pos="40000">
                                        <a:schemeClr val="phClr">
                                            <a:tint val="45000"/>
                                            <a:satMod val="350000"/>
                                            <a:shade val="99000"/>
                                        </a:schemeClr>
                                    </a:gs>
                                    <a:gs pos="100000">
                                        <a:schemeClr val="phClr">
                                            <a:shade val="20000"/>
                                            <a:satMod val="255000"/>
                                        </a:schemeClr>
                                    </a:gs>
                                </a:gsLst>
                                <a:path path="circle">
                                    <a:fillToRect l="50000" t="-80000" r="50000" b="180000"/>
                                </a:path>
                            </a:gradFill>
                            <a:gradFill rotWithShape="1">
                                <a:gsLst>
                                    <a:gs pos="0">
                                        <a:schemeClr val="phClr">
                                            <a:tint val="80000"/>
                                            <a:satMod val="300000"/>
                                        </a:schemeClr>
                                    </a:gs>
                                    <a:gs pos="100000">
                                        <a:schemeClr val="phClr">
                                            <a:shade val="30000"/>
                                            <a:satMod val="200000"/>
                                        </a:schemeClr>
                                    </a:gs>
                                </a:gsLst>
                                <a:path path="circle">
                                    <a:fillToRect l="50000" t="50000" r="50000" b="50000"/>
                                </a:path>
                            </a:gradFill>
                        </a:bgFillStyleLst>
                    </a:fmtScheme>
                </a:themeElements>
                <a:objectDefaults/>
            </a:theme>
        </pkg:xmlData>
    </pkg:part>
</pkg:package>