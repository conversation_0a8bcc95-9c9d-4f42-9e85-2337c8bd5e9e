<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>详情</title>
    <link rel="stylesheet" th:href="@{../../../../css/basic/global.css}">
    <link rel="stylesheet" th:href="@{../../../../css/basic/userExportRecord.css}">
</head>
<body>
<div class="common_popup export_record_popup">
    <h6 class="popup_top">导出记录</h6>
    <div class="popup_cont">
        <div id="exportCommit" class="table_wrap">
            <div class="nodata" style="display: none;">暂无数据</div>
            <table>
                <thead>
                    <tr>
                        <th>导出文件名</th>
                        <th>文件类型</th>
                        <th>导出时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
            <div class="isbottom" style="display: none;">已经到底啦~(&gt;_&lt;)~~</div>
        </div>
    </div>
    <div class="popup_btm">
        <div class="totals">共 <span></span> 条数据</div>
        <div class="refresh_btn">
            <span></span>刷新
        </div>
    </div>
</div>
<script th:src="@{../../../../js/jquery-3.3.1.min.js}"></script>
<script th:src="@{../../../../js/my.util.js}"></script>
<script th:src="@{../../../../js/basic/user_export_record.js}"></script>
<script>
    let fid = [[${fid}]];
    let uid = [[${uid}]];
    let _VR_ = "[[${_VR_}]]";
</script>
</body>
</html>