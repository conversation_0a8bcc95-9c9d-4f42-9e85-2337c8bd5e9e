<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>毕业学分要求</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/global.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/plugin/layui/css/layui.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/creditReques.css'}"/>
</head>

<body>
<div class="marker"></div>
<div class="dialog" id="creditRequesDialog" style="display: block;">
    <div class="dialog-con">
        <div class="major-con">
            <ul class="major-mes">
                <li>年级：<span th:text="${info.pyfagl_nj}"></span></li>
                <li>院系：<span th:text="${info.pyfagl_suxb}"></span></li>
                <li>专业：<span th:text="${info.pyfagl_zy}"></span></li>
                <li>培养层次：<span th:text="${info.pyfagl_pycc}"></span></li>
                <li>学制：<span th:text="${info.pyfagl_xz}"></span></li>
            </ul>
            <button id="oneCorrespondence">一键对应</button>
        </div>
        <div class="table-score">
            <table class="layui-hide" id="scoreTab" lay-filter="scoreTab"></table>
        </div>

    </div>
    <div class="dialog-footer">
        <button class="btn-cancel">取消</button>
        <button class="btn-sure">确定</button>
    </div>
</div>
<div class="dialog" id="courseScoreDialog">
    <div class="dialog-title">
        <h5>毕业学分要求</h5>
        <div class="close-btn"></div>
    </div>
    <div class="dialog-con">
        <!--<button id="oneCorrespondence">一键对应</button>-->
        <div class="table-score">
            <table class="layui-hide" id="scoreTab1" lay-filter="scoreTab1"></table>
        </div>
    </div>
    <div class="dialog-footer">
        <button class="btn-cancel">取消</button>
        <button class="btn-sure">确定</button>
    </div>
</div>
</body>
<script th:src="${_CPR_+_VR_+'/plugin/layui/layui.js'}"></script>
<script th:src="@{${_CPR_+_VR_}+'/js/cultivation/creditRequet.js'(v=${new java.util.Date().getTime()})}"></script>
<script th:inline="javascript">
    let _VR_ = [[${_VR_}]];
    let formId = [[${formId}]];
    let fid = [[${fid}]];
    let formUserId = [[${formUserId}]];
    let natureType = [];
</script>
</html>