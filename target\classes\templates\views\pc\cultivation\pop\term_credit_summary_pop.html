<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <title>毕业学分要求</title>
    <link rel="stylesheet" th:href="@{../css/cultivation/style.css}"/>
    <!DOCTYPE html>
    <style>
        table {
            border-collapse: collapse;
            text-align: center;
            margin: 0 auto;
        }

        table span {
            display: inline-block;
            width: 180px;
        }

        table tr td {
            height: 50px;
        }

        #time span {
            width: 30px;
        }

        .s1 span {
            width: 70px;
        }
    </style>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="ibox float-e-margins">
        <div class="ibox-content">
            <div>
                <table border="1" width="80%" style="border-bottom:0px">
                    <tr>
                        <td style="background-color:#d6d6d6">开课学期</td>
                        <td style="background-color:#d6d6d6">学分汇总</td>
                    </tr>
                </table>
                <table border="1" width="80%" style="border-bottom:0px" id="bx">
                    <tbody id="tdata">
                    </tbody>
                </table>
                <table border="1" width="80%">
                    <tr>
                        <td style="background-color:#FAEBD7" width="50%">合计:</td>
                        <td style="background-color:#FAEBD7" id="total">0</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>
</body>
<script th:src="@{../js/jquery-3.3.1.min.js}"></script>
<script th:src="@{../plugin/layui/layui.js}"></script>
<script th:inline="javascript">
    let formUserId = [[${formRightBtnVO.formUserId}]];
    let fid = [[${formRightBtnVO.fid}]];
    let uid = [[${formRightBtnVO.uid}]];
    document.domain = document.domain.split('.').slice(-2).join('.');
    $(document).ready(function () {
        $.post("/cultivation/termCreditSummary", {
            formUserId: formUserId,
            fid: fid,
            uid: uid
        }, function (result) {
            if (result.code === 200) {
                const data = result.data;
                const array = data.reduce(function (groups, item) {
                    const group = item.zyk_kkxq; // 根据哪个字段进行分组
                    if (!groups[group]) groups[group] = {zyk_kkxq: group, sum: 0}; // 初始化分组
                    groups[group].sum += item.xf; // 对另一个字段进行求和
                    return groups;
                }, {});
                let html = "";
                let sum = 0;
                for (const key in array) {
                    const group = array[key];
                    html += "<tr>";
                    html += "<td width=\"50%\">" + group.zyk_kkxq + "</td>";
                    html += "<td>" + group.sum + "</td>";
                    html += "</tr>";
                    sum += group.sum;
                }
                $("#tdata").html(html);
                $("#total").text(sum);
            }
        }, "json");
    })
</script>
</body>
</html>

