<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1, user-scalable=no">
    <title>选课</title>
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/global.css'"/>
    <!-- <link rel="stylesheet" href="css/common.css"> -->
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/selCourseM.css'"/>
    <script th:src="${_CPR_}+'/elective/js/responsive-1.0.js'"></script>


</head>

<body>
<div class="head-signal" style="width: 100%;"></div>
<div class="tophead">
    <div class="head bottomLine">
        <div class="leftHead">
            <div class="back"></div>
            <div class="select-week">
                <span>第<i>1</i>周</span>
                <em></em>
            </div>
        </div>
        <div class="centerHead">
            <div class="selectBox">
                <div class="selectWeek"><span class="week" week="1"></span><em></em></div>
            </div>
        </div>
        <div class="rightHead">
            <div class="menu" id="menu"></div>
            <ul class="menu-list" id="menuList">
                <li class="active">课表显示</li>
                <li>列表显示</li>
            </ul>
        </div>
    </div>
</div>

<div class="z-table" id="Curriculum">
    <div class="thead">
        <ul>
            <li><span class="week" id="yue"></span>
                <span class="weekdate"></span>
            </li>
            <li><span class="week">一</span>
                <span class="weekdate" id="week1"></span>
            </li>
            <li><span class="week">二</span>
                <span class="weekdate" id="week2"></span>
            </li>
            <li><span class="week">三</span>
                <span class="weekdate" id="week3"></span>
            </li>
            <li><span class="week">四</span>
                <span class="weekdate" id="week4"></span>
            </li>
            <li><span class="week">五</span>
                <span class="weekdate" id="week5"></span>
            </li>
            <li><span class="week">六</span>
                <span class="weekdate" id="week6"></span>
            </li>
            <li><span class="week">日</span>
                <span class="weekdate" id="week7"></span>
            </li>

        </ul>
    </div>
    <div class="tbody">
        <ul>
            <li><span class="section">早自习</span>
                <span class="time">7:00<br>7:30</span>
            </li>
            <li></li>
            <li></li>
            <li></li>
            <li class="selCourse letter">待选课</li>
            <li></li>
            <li></li>
            <li></li>
        </ul>
        <ul>
            <li><span class="section">第一节</span>
                <span class="time">8:00<br>8:40</span>
            </li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
        </ul>
        <ul>
            <li><span class="section">第二节</span>
                <span class="time">8:50<br>9:30</span>
            </li>
            <li>数学</li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
        </ul>
        <ul>
            <li><span class="section">第三节</span>
                <span class="time">9:40<br>10:20</span>
            </li>
            <li>语文</li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
        </ul>
        <ul>
            <li><span class="section">第四节</span>
                <span class="time">10:20<br>11:00</span>
            </li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
        </ul>
        <ul>
            <li><span class="section">第五节</span>
                <span class="time">13:00<br>13:40</span>
            </li>
            <li></li>
            <li></li>
            <li></li>
            <li class="selCourse letter">待选课</li>
            <li></li>
            <li></li>
            <li></li>
        </ul>
        <ul>
            <li><span class="section">第六节</span>
                <span class="time">13:50<br>14:30</span>
            </li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
        </ul>
        <ul>
            <li><span class="section">第七节</span>
                <span class="time">14:40<br>15:20</span>
            </li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
        </ul>
        <ul>
            <li><span class="section">第八节</span>
                <span class="time">15:30<br>16:20</span>
            </li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
        </ul>
        <ul>
            <li><span class="section">晚自习</span>
                <span class="time">18:00<br>19:00</span>
            </li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
        </ul>
    </div>
</div>
<div id="list" style="display: none;">
    <div class="z-search-course">
        <input type="text" placeholder="请输入课程名" id="xxkmc2">
        <div class="z-search-con">
            <h3>筛选课程</h3>
            <img th:src="${_CPR_}+'/elective/images/arrowgrayRight.png'">
        </div>

    </div>
    <div class="z-list" id="selCourseList">
    </div>
</div>

<!-- 进入提示 -->
<div class="dialog-wrap dialog-plan" style="display: none;">
    <div class="dialog ">
        <div class="dialog-con">
            <div class="plan">
                <h1>选课计划</h1>
                <p>2023-08-04 ~ 2023-08-10 </p>
                <p>选课说明文字</p>
            </div>
            <div class="plan">
                <h1>选课计划</h1>
                <p>2023-08-04 ~ 2023-08-10</p>
                <p>选课说明文字</p>
            </div>
        </div>
        <div class="dialog-btn" id="planSel">
            确定
        </div>
    </div>
</div>
<!-- 选择周次 -->
<div class="week-dialog">
    <div class="w-con active">
        <div class="w-head">
            <h3>点击查看该周周次</h3>
        </div>
        <div class="w-box">
            <ul>
                <li class="cur">1</li>
            </ul>
        </div>

    </div>
</div>
<!-- 选择学期 -->
<div class="selectWeek-dialog">
    <div class="w-con active">
        <div class="w-head">
            <h3>点击选择学期</h3>
        </div>
        <div class="w-box">
            <ul id="xnxqdiv">
                <li class="cur">1</li>
            </ul>
        </div>

    </div>
</div>
<!-- 可选课程 -->
<div class="dialog-wrap dialog-course">
    <div class="dialog">
        <div class="dialog-con">
            <div class="z-tab-search">
                <input type="text" id="xxkmc1">
                <div class="z-search"><img th:src="${_CPR_}+'/elective/images/search-icon.png'" alt="">搜索</div>
            </div>
            <div class="z-list" id="zList">
            </div>
        </div>
        <div class="dialog-btn">
            <button class="cancel">取消</button>
            <button class="sure" id="courseSel">确定</button>
        </div>
    </div>
</div>
<!-- 详情 -->
<div class="dialog-wrap dialog-mes">
    <div class="dialog">
        <div class="dialog-con">
            <h3>22数据库</h3>
            <p><span>开设课程 ：</span>数据库数据库数据库数据库数据库数据库数据库数据库数据库数据库数据库</p>
            <p><span>开课年级 ：</span>2022级</p>
            <p><span>开课院系 ：</span>计算机学院</p>
            <p><span>开课专业 ：</span>计算机专业</p>
            <p><span>课程类型 ：</span>专业选修课</p>
            <p><span>上课时间 ：</span>1-20周，周一，第七节</p>
            <p><span>上课地点 ：</span>南校区</p>
            <p><span>授课教师 ：</span>李四</p>
            <p><span>学分 ：</span>2分</p>
            <p><span>课程容量：</span>50</p>
        </div>
        <div class="dialog-btn">
            <button class="cancel">取消</button>
            <button class="sure" id="exitCourse">退课</button>
        </div>
    </div>
</div>

<div id="tipsBox"></div>
<div id="captcha"></div>
</body>

<script th:inline="javascript">
    const _VR_ = [[${_VR_}]] || '';
    var fid = [[${fid}]];
    let r = [[${r}]];
    let academicYear = [[${academicYear}]];
    let dates = [[${dates}]];
    let fidEnc = [[${fidEnc}]];
    let allAcademicYear = [[${allAcademicYear}]];
</script>
<script th:src="${_CPR_}+'/elective/js/jquery-3.3.1.min.js'"></script>
<script th:src="${_CPR_}+'/elective/js/mobileVersion.js?a=1'"></script>
<!--<script th:src="${_CPR_}+'/elective/js/selCourseM.js?v=1'"></script>-->
<script th:src="${_CPR_}+'/elective/layui/layui.js'"></script>
<script th:src="${_CPR_}+'/js/base.js'"></script>
<script th:src="${_CPR_}+'/js/my.util.js'"></script>
<script type="text/javascript" src="https://kb.chaoxing.com/res/app/curriculum/js/CXJSBridge.js"></script>
<script type="text/javascript" src="https://kb.chaoxing.com/res/app/curriculum/js/app.utils.js"></script>
<script type="text/javascript" src="https://kb.chaoxing.com/res/app/curriculum/js/tools.js"></script>
<script type='text/javascript' src='https://captcha.chaoxing.com/load.min.js?t='></script>
<!--<script src="https://cdn.bootcss.com/vConsole/3.2.0/vconsole.min.js"></script>-->
<script>
    // var vConsole = new VConsole();
    layui.use(['layer'], function () {
        var layer = layui.layer;
    })
    var xxk_kknj = '';
    var xxk_kkyx = '';
    var xxk_kkzy = '';
    var kskc = '';
    var xxk_kkxiaoqu = '';
    var xxk_kclx = '';
    var jh = "";
    var jc = '';
    var zc = 1;
    var stuOptional;
    var stuSelectCourseCid = [];
    var opcid;
    var finaltaskBdid = 0;
    var finaltaskcid = 0;
    var opCourse = 1;
    var captchaIns = null;
    var listtab = 1;
    var yzm = false;
    var jcArr = [];
    var xnxqh = "";
    if (academicYear) {
        xnxqh = academicYear.xnxq_xnxqh;
        if (academicYear.xnxq_xnxqmc) {
            $(".selectWeek .week").text(academicYear.xnxq_xnxqmc);
        } else {
            $(".selectWeek .week").text(academicYear.xnxq_xnxqh);
        }
    }
    if (allAcademicYear) {
        var html = "";
        for (let i = 0; i < allAcademicYear.length; i++) {
            if (academicYear && academicYear.xnxq_xnxqmc && academicYear.xnxq_xnxqmc == allAcademicYear[i].xnxq_xnxqmc) {
                html += "<li data-id='" + allAcademicYear[i].xnxq_xnxqh + "' class='cur'>" + allAcademicYear[i].xnxq_xnxqmc + "</li>";
            } else if (academicYear && academicYear.xnxq_xnxqh && academicYear.xnxq_xnxqh == allAcademicYear[i].xnxq_xnxqh) {
                html += "<li data-id='" + allAcademicYear[i].xnxq_xnxqh + "' class='cur'>" + allAcademicYear[i].xnxq_xnxqh + "</li>";
            } else if (allAcademicYear[i].xnxq_xnxqmc) {
                html += "<li data-id='" + allAcademicYear[i].xnxq_xnxqh + "' class=''>" + allAcademicYear[i].xnxq_xnxqmc + "</li>";
            } else {
                html += "<li data-id='" + allAcademicYear[i].xnxq_xnxqh + "' class=''>" + allAcademicYear[i].xnxq_xnxqh + "</li>";
            }
        }
        $("#xnxqdiv").html(html);
    }
    initCXCaptcha({
        captchaId: 'dRgtnuKwnxSvXXOl0btdNZqATWH8Kmjv',
        element: '#captcha',
        mode: 'popup',
        // type: 'iconclick',
        onVerify: function (err, data) {
            /**
             * 第一个参数是err（Error的实例），验证失败才有err对象
             * 第二个参数是data对象，验证成功后的相关信息，data数据结构为key-value，如下：
             * {
             * validate: 'xxxxx' // 二次验证信息
             * }
             **/
            if (err) return; // 当验证失败时，内部会自动refresh方法，无需手动再调用一次
            if (opCourse == 1) {
                electiveCourses(data.validate)
            } else {
                dropCourses(data.validate);
            }
            captchaIns.refresh()
        }
    }, function onload(instance) {
        captchaIns = instance;
    }, function onerror(err) {
    });

    function yzmdropCourses(taskBdid, cid) {
        opCourse = 2;
        finaltaskBdid = taskBdid;
        finaltaskcid = cid;
        if (captchaIns && yzm) {
            captchaIns.popUp();
        } else {
            dropCourses("");
        }
    }

    function yzmelectiveCourses(taskBdid, cid) {
        opCourse = 1;
        finaltaskBdid = taskBdid;
        finaltaskcid = cid;
        if (captchaIns && yzm) {
            captchaIns.popUp();
        } else {
            electiveCourses("");
        }
    }

    function _jsBridgeReady() {
        jsBridge.postNotification('CLIENT_TOOLBAR_TYPE', {'toolbarType': 0});
        jsBridge.bind('CLIENT_WEB_EXTRAINFO', function (object) {
            if (object.hasOwnProperty('xxk_kknj')) {
                xxk_kknj = object.xxk_kknj;
                xxk_kkyx = object.xxk_kkyx;
                xxk_kkzy = object.xxk_kkzy;
                kskc = object.kskc;
                xxk_kkxiaoqu = object.xxk_kkxiaoqu;
                xxk_kclx = object.xxk_kclx;
                jh = object.jh;
                optional_courses(2);
            }
        });
    }

    if (academicYear) {
        var selectHtml = '';
        for (var i = 1; i <= Number(academicYear.xnxq_jsz); i++) {
            selectHtml += '<li>' + i + '</li>';
        }
        $('.week-dialog .w-box ul').html(selectHtml);
        $('.week-dialog .w-box ul li:first').addClass('cur');
    }
    $(".back").on('click', function () {
        AppUtils.closeView();
    })
    $(".z-search").on('click', function () {
        $(".z-search").hide();
        $("#xxkmc1").show().focus();
    })
    //选择周次
    $(".select-week").click(function () {
        $(".week-dialog").show();
        setTimeout(function () {
            $(".week-dialog .w-con").addClass("active");
        }, 10);
    });
    //周次切换
    $(".week-dialog").on("click", ".w-con .w-box ul li", function () {
        $(this).addClass("cur").siblings().removeClass("cur");
        $(".week-dialog").hide();
        $(".week-dialog .w-con").removeClass("active");
        let texts = $(this).text();
        $(".tophead .leftHead .select-week span i").text(texts);
    });
    //选择学期
    $(".selectWeek").click(function () {
        $(".selectWeek-dialog").show();
    });
    //学期切换
    $(".selectWeek-dialog").on("click", ".w-con .w-box ul li", function () {
        $(this).addClass("cur").siblings().removeClass("cur");
        $(".selectWeek-dialog").hide();
        // $(".selectWeek-dialog .w-con").removeClass("active");
        let texts = $(this).text();
        $(".tophead .centerHead .selectBox .week").text(texts);
        window.location.href = "/elective/task/mobile/index?fidEnc=" + fidEnc + "&xnxqh=" + $(this).attr("data-id");
    });
    // 选课计划确定
    $("#planSel").click(function () {
        $(".dialog-plan").hide();
    });
    $("#menu").click(function (e) {
        $(this).next().show();
        e.stopPropagation();
    });
    // 跳转
    $(".z-search-course .z-search-con").click(function () {
        openA("筛选课程", "/elective/task/mobile/searech?fid=" + fid);
    });
    U.ajax({
        type: 'post',
        url: "../elective/task/getTask",
        data: {fid: fid, xnxqh: xnxqh},
        dataType: 'json',
        async: false,
        success: function (result) {
            if (result.code !== 200 || !result.data || result.data.length === 0) {
                return false;
            }
            var html = "";
            if (result.data.length == 0) {
                html += "<div class='plan'>";
                html += "<h1>当前无选课计划</h1>";
                html += "</div>";
                $(".dialog-plan .dialog .dialog-con").html(html);
                $(".dialog-plan").show();
                return;
            }
            for (let i = 0; i < result.data.length; i++) {
                html += "<div class='plan'>";
                html += "<h1>" + result.data[i].xkjhbJhmc + "</h1>";
                html += "<p>" + result.data[i].xkjhbKssj + "~" + result.data[i].xkjhbJssj + "</p>";
                html += "<p>" + result.data[i].xkjhbXksm + "</p>";
                html += "</div>";
            }
            $(".dialog-plan .dialog .dialog-con").html(html);
            $(".dialog-plan").show();
        }
    })
    if (r) {
        if (r.code !== 200) {
            layer.msg(r.msg);
        } else {
            optionalCourse()
            stuOptionalCou();
        }
    } else {
        layer.msg("课表数据异常");
    }
    // 点击待选课
    var selCourseEle;
    $(".tbody").on("click", ".selCourse", function () {
        selCourseEle = $(this);
        if ($(this).text() == "待选课") {
            $(".dialog-course").show();
            $("#xxkmc1").val("");
            $(".z-search").show();
            $("#xxkmc1").hide();
            optional_courses(1);
        } else {
            $(".dialog-mes").show();
            courseDetail($(this).attr("cid"))
        }
        $(".dialog-course .z-course.active").removeClass("active");
    });
    // 列表选择课程
    $("#selCourseList").on("click", ".z-course .btn-sel", function () {
        var taskBdid = $(this).attr("taskBdid");
        var cid = $(this).attr("cid");
        opcid = cid;
        yzmelectiveCourses(taskBdid, cid);
        // var succ = electiveCourses(taskBdid, cid);
        // if (succ) {
        //     refreshData2()
        // }
        // var p = $(this).parents(".z-course");
        // var em = p.find(".marginCount");
        // var txt = parseInt(em.text()) - 1;
        // em.text(txt);
        //
        // $(this).prevAll().css("display", "block");
        // $(this).hide();
        // p.addClass("active");
    });
    var taskBdid = "";
    var cid = "";
    // 选择课程
    $("#zList").on("click", '.z-course:not(".z-full")', function () {
        taskBdid = $(this).attr("taskBdid");
        cid = $(this).attr("id");
        $(this).addClass("active").siblings().removeClass("active");
    });
    $(".cancel").click(function () {
        $(".dialog-wrap").hide();
    });
    // 课程确定
    $("#courseSel").click(function () {
        if ($(".z-course.active").length > 0) {
            if (!taskBdid || !cid) {
                $("#tipsBox").text("异常").fadeIn().delay(1000).fadeOut();
                return false;
            }
            opcid = cid;
            yzmelectiveCourses(taskBdid, cid);
            // var succ = electiveCourses(taskBdid, cid);
            // if (succ) {
            //     // var courseTxt = $("#zList .z-course.active h3").text();
            //     // selCourseEle.text(courseTxt);
            //     $(".dialog-course").hide();
            //     refreshData1()
            // }
            // var courseTxt = $(".z-course.active h3").text();
            // selCourseEle.text(courseTxt).removeClass("letter");
            // $(".dialog-course").hide();
        } else {
            $("#tipsBox").text("请选择课程").fadeIn().delay(1000).fadeOut();
        }
    });
    // 退课
    $("#selCourseList").on("click", ".z-course .btn-exit", function () {
        var taskBdid = $(this).attr("taskBdid");
        var cid = $(this).attr("cid");
        opcid = cid;
        yzmdropCourses(taskBdid, cid);
        // var succ = dropCourses(taskBdid, cid);
        // if (succ) {
        //     refreshData2()
        // }
        // var p = $(this).parents(".z-course");
        // $(this).prevAll().show();
        // $(this).hide().prev().hide();
        // $(this).next().show();
        // p.removeClass("active");
        // var em = p.find(".marginCount");
        // var txt = parseInt(em.text()) + 1;
        // em.text(txt);
    });
    // 退课
    $("#exitCourse").click(function () {
        var taskBdid = $("#" + opcid + "").attr("taskBdid");
        yzmdropCourses(taskBdid, opcid);
        // var succ = dropCourses(taskBdid, opcid);
        // if (succ) {
        //     $(".dialog-mes").hide();
        //     refreshData2();
        //     refreshData1();
        // }
        // $(".dialog-mes").hide();
        // if (!$("#Curriculum").is(":hidden")) {
        //     selCourseEle.text("待选课").addClass("letter");
        // } else {
        //     var p = detailEle.parents(".z-course");
        //     detailEle.hide().next().hide();
        //     detailEle.nextAll(".btn-sel").show();
        //     p.removeClass("active");
        //     var em = p.find(".marginCount");
        //     var txt = parseInt(em.text()) + 1;
        //     em.text(txt);
        // }
    });
    $("#menuList li").click(function () {
        var idx = $(this).index();
        $(this).addClass("active").siblings().removeClass("active");
        if (idx == 0) {
            $("#Curriculum").show();
            $("#list").hide();
            $(".select-week").show();
            refreshData1()
            listtab = 1;
        } else {
            $("#Curriculum").hide();
            $("#list").show();
            $(".select-week").hide();
            // 课程列表高度
            var sH = $(window).height() - $("#selCourseList").offset().top;
            $("#selCourseList").height(sH);
            refreshData2()
            listtab = 2;
        }
        $(this).parent().hide();
    });
    //周次切换
    // $(".week-dialog").on("click", ".w-con .w-box ul li", function () {
    //     $(this).addClass("cur").siblings().removeClass("cur");
    //     $(".week-dialog").hide();
    //     $(".week-dialog .w-con").removeClass("active");
    //     let texts = $(this).text();
    //     zc = texts;
    //     stuOptionalCou();
    //     $(".tophead .leftHead .select-week span i").text(texts);
    // });
    // 点击详情
    var detailEle;
    $("body").on('click', '.btn-detail', function () {
        detailEle = $(this);
        courseDetail($(this).attr("cid"));
    });
    $("#xxkmc1").keyup(function (e) {
        if (e.keyCode == 13) {
            optional_courses(1);
            if ($("#xxkmc1").val() == '') {
                $(".z-search").show();
                $("#xxkmc1").hide();
            }
        }
    })
    $("#xxkmc2").keyup(function (e) {
        if (e.keyCode == 13) {
            optional_courses(2);
        }
    })

    function optionalCourse() {
        U.ajax({
            type: 'post',
            url: "../elective/task/optional/course",
            data: {fid: fid, xnxqh: xnxqh},
            dataType: 'json',
            async: false,
            success: function (result) {
                if (result.code !== 200) {
                    return false;
                }
                if (!result.data) {
                    $(".dialog-plan").hide();
                    return false;
                }
                stuSelectCourseCid = [];
                stuOptional = result.data;
                if (stuOptional) {
                    for (let z = 0; z < stuOptional.length; z++) {
                        var selected_course = $.parseJSON(stuOptional[z].selectedCourse);
                        for (let y = 0; y < selected_course.length; y++) {
                            stuSelectCourseCid.push(selected_course[y].cid)
                        }
                    }

                }
            }
        })
    }

    function stuOptionalCou() {
        var djj = 0; //第几节课
        var html = "";
        for (let i = 0; i < r.data.lessons.length; i++) {
            jcArr[r.data.lessons[i][0].actualNum] = r.data.lessons[i][0].lessonNumName;
            html += "<ul>";
            html += "<li><span class='section'> " + r.data.lessons[i][0].lessonNumName + "</span>";
            html += " <span class='time'>" + r.data.lessons[i][0].begin + "<br>" + r.data.lessons[i][0].end + "</span>";
            html += "</li>";
            if (r.data.lessons[i][0].period === 2 || r.data.lessons[i][0].period === 3 || r.data.lessons[i][0].period === 1 || r.data.lessons[i][0].period === 4) {
                djj++;
            }
            for (let j = 1; j <= 7; j++) {
                if (r.data.lessons[i][0].period !== 2 && r.data.lessons[i][0].period !== 3 && r.data.lessons[i][0].period !== 1 && r.data.lessons[i][0].period !== 4) {
                    html += "<li></li>";
                    continue;
                }
                if (!stuOptional) {
                    html += "<li></li>";
                    continue;
                }
                var xknum = 0; //0灰色
                var xkfalse = true;
                var temphtml = "";
                one :for (let z = 0; z < stuOptional.length; z++) {
                    var selected_course = $.parseJSON(stuOptional[z].selectedCourse);
                    var optional_course = $.parseJSON(stuOptional[z].optionalCourse);
                    if (selected_course.length > 0) {
                        two: for (let y = 0; y < selected_course.length; y++) {
                            var allSj = selected_course[y].allSj;
                            for (let l = 0; l < allSj.length; l++) {
                                if (allSj[l] == (zc + ',' + j + ',' + djj)) {
                                    temphtml = "<li class='selCourse' djj = '" + djj + "' zc = '" + j + "'  cid='" + selected_course[y].cid + "'>" + selected_course[y].kcmc + "</li>";
                                    xkfalse = false;
                                    break one;
                                }
                            }
                        }
                    }
                    if (optional_course.length > 0) {
                        two: for (let y = 0; y < optional_course.length; y++) {
                            var allSj = optional_course[y].allSj;
                            for (let l = 0; l < allSj.length; l++) {
                                if (allSj[l] == (zc + ',' + j + ',' + djj)) {
                                    temphtml = "<li class='selCourse' djj = '" + djj + "' zc = '" + j + "'>待选课</li>";
                                    xkfalse = false;
                                    break two;
                                }
                            }
                        }
                    }
                    if (xkfalse) {
                        temphtml = "<li ></li>";
                    }
                }
                if (xkfalse) {
                    temphtml = "<li ></li>";
                }
                html += temphtml;
            }
            html += "</ul>";
        }
        $(".tbody").html(html);
    }

    function electiveCourses(validate) {
        var succ = true;
        U.ajax({
            type: 'post',
            url: "../elective/task/elective/courses",
            data: {fid: fid, taskBdid: finaltaskBdid, cid: finaltaskcid, validate: validate, captchaIns: !!captchaIns},
            dataType: 'json',
            async: false,
            success: function (result) {
                if (result.code !== 200) {
                    layer.msg(result.msg)
                    succ = false;
                    return
                }
                if (listtab == 1) {
                    refreshData1()
                    $(".dialog-course").hide();
                } else {
                    refreshData2()
                }
                layer.msg("报名成功")
            }
        })
        return succ;
    }

    function dropCourses(validate) {
        var succ = true;
        U.ajax({
            type: 'post',
            url: "../elective/task/drop/courses",
            data: {fid: fid, taskBdid: finaltaskBdid, cid: finaltaskcid, validate: validate, captchaIns: !!captchaIns},
            dataType: 'json',
            async: false,
            success: function (result) {
                if (result.code !== 200) {
                    if ('不在计划时间范围内报名不允许退课' == result.msg) {
                        //姓名 tksqb_xm
                        //学号 tksqb_xh  0009
                        //uid tksqb_uid
                        //所属退课计划 13
                        //退选选修课名称 tksqb_txxxkmc
                        var param = {
                            'data': [
                                {
                                    'alias': 'tksqb_xm',
                                    'val': ['' + result.data.tksqb_xm + ''],
                                    'compt': 'editinput'
                                }
                                , {
                                    'alias': 'tksqb_xh',
                                    'val': ['' + result.data.tksqb_xh + ''],
                                    'compt': 'editinput'
                                }
                                , {
                                    'alias': 'tksqb_uid',
                                    'val': ['' + result.data.tksqb_uid + ''],
                                    'compt': 'editinput'
                                }
                                , {'alias': '13', 'val': ['' + result.data.taskname + ''], 'compt': 'editinput'}
                                , {
                                    'alias': 'tksqb_txxxkmc',
                                    'val': ['' + result.data.tksqb_txxxkmc + ''],
                                    'compt': 'editinput'
                                }
                            ]
                        }
                        // 创建表单
                        var temp_form = document.createElement('form')
                        // 填写表单数据
                        temp_form.action = result.data.url;
                        temp_form.target = '_blank'
                        temp_form.method = 'post'
                        temp_form.style.display = 'none'
                        // 添加参数
                        var opt = document.createElement('textarea')
                        opt.name = 'precast'
                        opt.value = JSON.stringify(param)
                        temp_form.appendChild(opt)
                        document.body.appendChild(temp_form)
                        // 提交数据
                        // temp_form.submit()
                        openA("", result.data.url + "&precast=" + encodeURIComponent(JSON.stringify(param)))
                        return
                    }
                    yzm = result.data;
                    layer.msg(result.msg)
                    succ = false;
                    return
                }
                layer.msg("退课成功")
                yzm = result.data;
                if (listtab == 1) {
                    refreshData1()
                    $(".dialog-mes").hide();
                } else {
                    refreshData2()
                    $(".dialog-mes").hide();
                }
            }
        })
        return succ;
    }

    function courseDetail(cid) {
        opcid = cid;
        U.ajax({
            type: 'post',
            url: "../elective/task/course/detail",
            data: {fid: fid, cid: cid},
            dataType: 'json',
            async: false,
            success: function (result) {
                if (result.code !== 200) {
                    layer.msg(result.msg)
                    return false;
                }
                var html = "";
                var d = result.data;
                html += "<h3 id='" + d.id + "'  taskBdid = '" + d.taskBdid + "'>" + d.xxkmc + "</h3>";
                html += "<p><span>所属选课计划：</span>" + d.xxkmc + "</p>";
                html += "<p><span>开设课程 ：</span>" + d.xxkXxkmc + "</p>";
                html += "<p><span>开课年级 ：</span>" + d.xxkKknj + "</p>";
                html += " <p><span>开课院系 ：</span>" + d.xxkKkyx + "</p>";
                html += "<p><span>开课专业 ：</span>" + d.xxkKkzy + "</p>";
                html += " <p><span>课程类型 ：</span>" + d.xxkKclx + "</p>";
                html += " <p><span>上课时间 ：</span>" + getsksjStr(d.xxkSksj) + "</p>";
                html += "<p><span>上课地点 ：</span>" + d.xxkSkdd + "</p>";
                html += "<p><span>授课教师 ：</span>" + d.xxkSkjs + "</p>";
                // html+="<p><span>学分 ：</span>2分</p>";
                html += "<p><span>学分 ：</span>" + d.xxkXxkxf + "分</p>";
                if (d.xxkXxkrl == -1) {
                    html += "<p><span>课程容量：</span>无上限</p>";
                } else {
                    html += "<p><span>课程容量：</span>" + d.xxkXxkrl + "</p>";
                }
                html += "<p><span>可选性别 ：</span>" + d.xxkKxxsxb + "</p>";
                html += "<p><span>编组 ：</span>" + d.courseGroupName + "</p>";
                $(".dialog-mes .dialog .dialog-con").html(html);
                $(".dialog-mes").show();
            }
        })
    }

    //开课年级 xxk_kknj
    //开课院系 xxk_kkyx
    //开课专业  xxk_kkzy
    //开设课程  kskc
    //开课校区  xxk_kkxiaoqu
    //课程类型  xxk_kclx
    //选修课名称  xxk_xxkmc
    function optional_courses(divv) {
        var xxk_kknj1 = '';
        var xxk_kkyx1 = '';
        var xxk_kkzy1 = '';
        var kskc1 = '';
        var xxk_kkxiaoqu1 = '';
        var xxk_kclx1 = '';
        var xxk_xxkmc1 = '';
        var jc1 = '';
        var jh1 = '';
        if (divv === 2) {
            xxk_kknj1 = xxk_kknj;
            xxk_kkyx1 = xxk_kkyx;
            xxk_kkzy1 = xxk_kkzy;
            kskc1 = kskc;
            xxk_kkxiaoqu1 = xxk_kkxiaoqu;
            xxk_kclx1 = xxk_kclx;
            xxk_xxkmc1 = $("#xxkmc2").val();
            jc1 = "";
            jh1 = jh;
        } else {
            xxk_kknj1 = "";
            xxk_kkyx1 = "";
            xxk_kkzy1 = "";
            kskc1 = "";
            xxk_kkxiaoqu1 = "";
            xxk_kclx1 = "";
            xxk_xxkmc1 = $("#xxkmc1").val();
            jh1 = "";
            if (selCourseEle) {
                jc1 = zc + ',' + selCourseEle.attr("zc") + ',' + selCourseEle.attr("djj");
            }
        }
        U.ajax({
            type: 'post',
            url: "../elective/task/optional/courses/list",
            data: {
                fid: fid,
                xxk_kknj: xxk_kknj1,
                xxk_kkyx: xxk_kkyx1,
                xxk_kkzy: xxk_kkzy1
                ,
                kskc: kskc1,
                xxk_kkxiaoqu: xxk_kkxiaoqu1,
                xxk_kclx: xxk_kclx1,
                xxk_xxkmc: xxk_xxkmc1,
                jc: jc1,
                jh: jh1,
                xnxqh: xnxqh
            },
            dataType: 'json',
            async: false,
            success: function (result) {
                if (result.code !== 200) {
                    layer.msg(result.msg)
                    return false;
                }
                if (!result.data) {
                    return false;
                }
                var html = "";
                for (let i = 0; i < result.data.length; i++) {
                    let d = result.data[i];
                    if (d.selectNum == d.xxkXxkrl) {
                        html += "<div class='z-course z-full' taskBdid = '" + d.taskBdid + "' id='" + d.id + "'>";
                    } else {
                        html += "<div class='z-course' taskBdid = '" + d.taskBdid + "' id='" + d.id + "'>";
                    }
                    if (divv === 2) {
                        html += "<div class='z-course-mes'>";
                    }
                    html += "<div class='z-course-title'>";
                    html += "<h3>" + d.xxkXxkmc + "</h3>";
                    html += "<span>" + d.taskName + "</span>";
                    html += "</div>";
                    html += "<p><span>" + d.xxkKkzy + "</span><span>" + d.xxkKclx + "</span><span>" + d.xxkSkjs + "</span></p>";
                    // html += " <p>学分：<span>" + d.xxkXxkxf + "分</span></p>";
                    html += "<p><span>" + getsksjStr(d.xxkSksj) + "</span><span>" + d.xxkSkdd + "</span></p>";
                    if (divv === 2) {
                        if (d.xxkXxkrl == -1) {
                            html += "<p><span class='marginCount'>余量：<em></em>" + d.selectNum + "/无上限</span></p>";
                        } else {
                            html += "<p><span class='marginCount'>余量：<em></em>" + d.selectNum + "/" + d.xxkXxkrl + "</span></p>";
                        }
                    } else {
                        if (d.xxkXxkrl == -1) {
                            html += "<p>余量：<span>" + d.selectNum + "/无上限</span></p>";
                        } else {
                            html += "<p>余量：<span>" + d.selectNum + "/" + d.xxkXxkrl + "</span></p>";
                        }
                    }
                    html += "</div>";
                    if (divv === 2) {
                        html += "<div class='z-btn'>";
                        if (stuSelectCourseCid.indexOf(d.id) != -1) {
                            html += "<span class='btn-detail' cid='" + d.id + "' style='display: block;'>详情</span>";
                            html += "<span class='btn-exit' taskBdid = '" + d.taskBdid + "' cid = '" + d.id + "' style='display: block;'>退课</span>";
                            html += "<span class='btn-sel' taskBdid = '" + d.taskBdid + "' cid = '" + d.id + "' style='display: none'>选课</span>";
                        } else {
                            html += "<span class='btn-detail' cid='" + d.id + "' >详情</span>";
                            html += "<span class='btn-exit' taskBdid = '" + d.taskBdid + "' cid = '" + d.id + "' >退课</span>";
                            html += "<span class='btn-sel' taskBdid = '" + d.taskBdid + "' cid = '" + d.id + "'>选课</span>";
                        }

                        html += "</div>";
                        html += "</div>";
                    }
                }
                if (divv === 2) {
                    $("#selCourseList").html(html);
                } else {
                    $("#zList").html(html);
                }
            }
        })
    }

    function refreshData2() {
        optionalCourse()
        optional_courses(2);
    }

    function refreshData1() {
        optionalCourse()
        stuOptionalCou()
        // optional_courses(1);
    }

    //[{"xxk_skjc":"3","xxk_skxq":"2","xxk_skzc":"1-5,10"}]
    //获取上课时间
    function getsksjStr(sj) {
        let sjsjon = $.parseJSON(sj);
        var sksjstr = "";
        for (let i = 0; i < sjsjon.length; i++) {
            let split = sjsjon[i].xxk_skzc.split(",");
            for (let j = 0; j < split.length; j++) {
                sksjstr += split[j] + "周、";
            }
            sksjstr += "周" + getxq(sjsjon[i].xxk_skxq) + "、";
            sksjstr += getjc(sjsjon[i].xxk_skjc);
            sksjstr += ";";
        }
        return sksjstr;
    }

    function getjc(xxk_skjc) {
        var skxqstr = "";
        let split = xxk_skjc.split(",");
        for (let i = 0; i < split.length; i++) {
            if (split[i].indexOf("-") != -1) {
                if (skxqstr != '') {
                    skxqstr += ",";
                }
                let split1 = split[i].split("-");
                skxqstr += getjcStr(split1[0]) + "-" + getjcStr(split1[1]);
            } else {
                if (skxqstr != '') {
                    skxqstr += ",";
                }
                skxqstr += getjcStr(split[i]);
            }
        }
        return skxqstr;
    }

    function getxq(xxk_skxq) {
        var skxqstr = "";
        let split = xxk_skxq.split(",");
        for (let i = 0; i < split.length; i++) {
            if (split[i].indexOf("-") != -1) {
                if (skxqstr != '') {
                    skxqstr += ",";
                }
                let split1 = split[i].split("-");
                skxqstr += getxqStr(split1[0]) + "-" + getxqStr(split1[1]);
            } else {
                if (skxqstr != '') {
                    skxqstr += ",";
                }
                skxqstr += getxqStr(split[i]);
            }
        }
        return skxqstr;
        // for (let i = 0; i < xxk_skxq.length; i++) {
        //     let split = xxk_skxq[i].split(",");
        //     for (let j = 0; j < split.length; j++) {
        //         skxqstr += split[j] + "周、";
        //     }
        // }
    }

    function getxqStr(xq) {
        if (xq == 1) {
            return "一";
        } else if (xq == 2) {
            return "二";
        } else if (xq == 3) {
            return "三";
        } else if (xq == 4) {
            return "四";
        } else if (xq == 5) {
            return "五";
        } else if (xq == 6) {
            return "六";
        } else if (xq == 7) {
            return "日";
        } else {
            return "";
        }
    }

    function getjcStr(jc) {
        return jcArr[jc];
        // if (jc == 1) {
        //     return "一";
        // } else if (jc == 2) {
        //     return "二";
        // } else if (jc == 3) {
        //     return "三";
        // } else if (jc == 4) {
        //     return "四";
        // } else if (jc == 5) {
        //     return "五";
        // } else if (jc == 6) {
        //     return "六";
        // } else if (jc == 7) {
        //     return "七";
        // } else if (jc == 8) {
        //     return "八";
        // } else if (jc == 9) {
        //     return "九";
        // } else if (jc == 10) {
        //     return "十";
        // } else if (jc == 11) {
        //     return "十一";
        // } else if (jc == 12) {
        //     return "十二";
        // } else {
        //     return "";
        // }
    }
</script>


</html>