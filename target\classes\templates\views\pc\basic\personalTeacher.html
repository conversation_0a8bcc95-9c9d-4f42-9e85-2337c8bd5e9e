<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教师基本信息</title>
    <link rel="stylesheet" th:href="@{~/css/basic/global.css}">
    <link rel="stylesheet" th:href="@{~/css/basic/personMessage.css}">
</head>

<body>
    <div class="main">
        <div class="m-top">
            <div class="title">教师基本信息</div>
        </div>
        <div class="person-mes">
            <div class="photo">
                <img th:src="${photoUrl}" alt="">
            </div>
            <div class="stu-info">
                <dl>
                    <dt th:text="${teacherInfo.jsjbxx_xm}">姓名：<span>孙棣楚</span></dt>
                    <dd th:text="${teacherInfo.jsjbxx_jsgh}">工号：<span>2019000000</span></dd>
                </dl>
                <button onclick="editInfo()">进入编辑</button>
            </div>
        </div>
        <div class="person-intro"></div>
        <div class="i-top">
            <h3>教师信息</h3>
        </div>
        <ul class="person-nav">
            <li class="active">基本信息</li>
            <li>任职信息</li>
            <li>其他信息</li>
        </ul>
        <div class="person-box-wrap">
            <div class="person-box">
                <dl>
                    <dt>身份证号：</dt>
                    <dd th:text="${teacherInfo.jsjbxx_sfzjh}">012105200012120012</dd>
                </dl>
                <dl>
                    <dt>出生日期：</dt>
                    <dd th:text="${teacherInfo.jsxx_csrq}">中国</dd>
                </dl>
                <dl>
                    <dt>联系电话：</dt>
                    <dd th:text="${teacherInfo.jsjbxx_lxdh}">×××省××市××区县</dd>
                </dl>
                <dl>
                    <dt>国籍：</dt>
                    <dd th:text="${teacherInfo.jsjbxx_gj}">13365428970</dd>
                </dl>
                <dl>
                    <dt>民族：</dt>
                    <dd th:text="${teacherInfo.jsjbxx_mz}">012105200012120012</dd>
                </dl>
                <dl>
                    <dt>籍贯：</dt>
                    <dd th:text="${teacherInfo.jsjbxx_jg}">×××省××市××区县</dd>
                </dl>
                <dl>
                    <dt>出生地：</dt>
                    <dd th:text="${teacherInfo.jsjbxx_csd}">2000-12-12</dd>
                </dl>
                <dl>
                    <dt>家庭住址：</dt>
                    <dd th:text="${teacherInfo.jsjbxx_jtzz}">汉族</dd>
                </dl>
                <dl>
                    <dt>家庭邮政编码：</dt>
                    <dd th:text="${teacherInfo.jsjbxx_jtyzbm}">×××省××市××区县</dd>
                </dl>
                <dl>
                    <dt>性别：</dt>
                    <dd th:text="${teacherInfo.jsjbxx_xb}">×××省××市××区县</dd>
                </dl>
            </div>
            <div class="person-box">
                <dl>
                    <dt>所在校区：</dt>
                    <dd th:text="${teacherInfo.jsjbxx_szxq}">012105200012120012</dd>
                </dl>
                <dl>
                    <dt>部门：</dt>
                    <dd th:text="${teacherInfo.jsjbxx_bm}">中国</dd>
                </dl>
                <dl>
                    <dt>系部：</dt>
                    <dd th:text="${teacherInfo.jsjbxx_yx}">×××省××市××区县</dd>
                </dl>
               
                <dl>
                    <dt>教研室/科室：</dt>
                    <dd th:text="${teacherInfo.jsjbxx_jysks}">汉族</dd>
                </dl>
                <dl>
                    <dt>当前状态：</dt>
                    <dd th:text="${teacherInfo.jsjbxx_dqzt}">×××省××市××区县</dd>
                </dl>
                <dl>
                    <dt>教职工类别：</dt>
                    <dd th:text="${teacherInfo.jsjbxx_jzglb}">×××省××市××区县</dd>
                </dl>
                <dl>
                    <dt>行政职务：</dt>
                    <dd th:text="${teacherInfo.jsjbxx_xzzw}">×××省××市××区县</dd>
                </dl>
                <dl>
                    <dt>是否外聘：</dt>
                    <dd th:text="${teacherInfo.jsjbxx_sfwp}">×××省××市××区县</dd>
                </dl>
                <dl>
                    <dt>是否特聘：</dt>
                    <dd th:text="${teacherInfo.jsjbxx_sftp}">×××省××市××区县</dd>
                </dl>
                <dl>
                    <dt>有否教师资格：</dt>
                    <dd th:text="${teacherInfo.jsjbxx_yfjszg}">×××省××市××区县</dd>
                </dl>
                <dl>
                    <dt>文化程度：</dt>
                    <dd th:text="${teacherInfo.jsjbxx_whcd}">×××省××市××区县</dd>
                </dl>
            </div>
            <div class="person-box">
             
                <dl>
                    <dt>宗教信仰：</dt>
                    <dd th:text="${teacherInfo.jsjbxx_zjxy}">012105200012120012</dd>
                </dl>
                <dl>
                    <dt>港澳台侨外：</dt>
                    <dd th:text="${teacherInfo.jsjbxx_gwtqw}">×××省××市××区县</dd>
                </dl>
                <dl>
                    <dt>婚姻状况：</dt>
                    <dd th:text="${teacherInfo.jsjbxx_hyzk}">2000-12-12</dd>
                </dl>
                <dl>
                    <dt>健康状况：</dt>
                    <dd th:text="${teacherInfo.jsjbxx_jkzk}">汉族</dd>
                </dl>
                <dl>
                    <dt>血型：</dt>
                    <dd th:text="${teacherInfo.jsjbxx_xx}">×××省××市××区县</dd>
                </dl>
            </div>
        </div>
    </div>
</body>
<script th:src="@{~/js/jquery-3.6.0.min.js}"></script>
<script th:inline="javascript">
    var editUrl = [[${editUrl}]];
    $(function () {
        $(".person-nav li").click(function () {
            $(this).addClass("active").siblings().removeClass("active")
            var index = $(this).index()
            $(".person-box-wrap .person-box").eq(index).css('display', 'flex').siblings().hide()
        })
    })
    function editInfo() {
        window.open(editUrl);
    }
</script>

</html>