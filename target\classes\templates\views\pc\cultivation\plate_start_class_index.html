<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>板块开课管理</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/global.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/plugin/layui-v2.8.18/layui/css/layui.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/slideCommon.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/layuiReset.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/plateDailog.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/plateType.css'}">
    <script th:src="${_CPR_+_VR_+'/plugin/layui-v2.8.18/layui/layui.js'}"></script>
    <style>
        .j-search-con {
            width: 200px;
        }
    </style>
</head>

<body>
<div class="main">
    <div class="m-top">
        <div class="title">板块开课管理</div>
    </div>
    <form action="" class="layui-form form-search" lay-filter='formSearch' style="padding: 24px 32px 0;">
        <div class="layui-inline">
            <label class="layui-form-label" style="width: 56px;">学年学期</label>
            <div class="layui-input-inline">
                <div class="j-search-con single-box">
                    <input type="text" placeholder="请选择" readonly="" class="schoolSel" name="semester"
                           formAlias="plateClass" fieldAlias="semester" source="table">
                    <span class="j-arrow"></span>
                    <div class="j-select-year">
                        <ul>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label" style="width: 56px;">板块类型名称</label>
            <div class="layui-input-inline">
                <div class="j-search-con single-box">
                    <input type="text" name="plateTypeName" placeholder="请选择" readonly="" class="schoolSel"
                           formAlias="plateType" fieldAlias="name" source="table">
                    <span class="j-arrow"></span>
                    <div class="j-select-year">
                        <ul>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label" style="width: 56px;">板块类型级别</label>
            <div class="layui-input-inline">
                <div class="j-search-con single-box">
                    <input type="text" name="plateTypeLevelName" placeholder="请选择" readonly="" class="schoolSel"
                           formAlias="plateTypeLevel" fieldAlias="levelName" source="table">
                    <span class="j-arrow"></span>
                    <div class="j-select-year">
                        <ul>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label" style="width: 56px;">板块项目名称</label>
            <div class="layui-input-inline">
                <div class="j-search-con single-box">
                    <input type="text" name="classNameArr" placeholder="请选择" readonly="" class="schoolSel"
                           formAlias="plateClass" fieldAlias="classNameArr" source="table">
                    <span class="j-arrow"></span>
                    <div class="j-select-year">
                        <ul>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label" style="width: 56px;">课程名称</label>
            <div class="layui-input-inline">
                <div class="j-search-con single-box">
                    <input type="text" name="courseName" placeholder="请选择" readonly="" class="schoolSel"
                           formAlias="plateTypeCourse" fieldAlias="courseName" source="table">
                    <span class="j-arrow"></span>
                    <div class="j-select-year">
                        <div class="search">
                            <input type="text" placeholder="搜索">
                            <span></span>
                        </div>
                        <ul>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label" style="width: 56px;">开课状态</label>
            <div class="layui-input-inline">
                <div class="j-search-con single-box">
                    <input type="text" name="status" placeholder="请选择" readonly="" class="schoolSel">
                    <span class="j-arrow"></span>
                    <div class="j-select-year">
                        <ul>
                            <li>未安排</li>
                            <li>部分安排</li>
                            <li>已安排</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-inline">
            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            <button type="submit" class="layui-btn" lay-submit lay-filter="formSearch">查询</button>
        </div>
    </form>
    <div class="table-box">
        <table class="layui-hide" id="plateType" lay-filter="plateType"></table>
    </div>
</div>
</body>
<script type="text/html" id="plateToolBar">
    <span class="opt-btn" lay-event="startCourse">开课</span>
</script>
<script th:inline="javascript">
    const _VR_ = [[${_VR_}]];
    const fid = [[${fid}]];
    const id = "";
</script>
<script th:src="${_CPR_+_VR_+'/js/cultivation/plateStuSubItem.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/cultivation/plateCommon.js'}"></script>
</html>