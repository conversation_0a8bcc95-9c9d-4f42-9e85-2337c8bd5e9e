<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>继承</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/basic/global.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/basic/slideCommon.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/basic/dialog.css'}">
</head>
<body>

<div class="mask"></div>
<div class="dialog" id="inheritDialog">
    <div class="dialog-con" style="height: 395px;overflow-y: auto;">
        <div class="dialog-con-title">继承来源设置</div>
        <div class="set-con">
            <div class="set-item">
                <h4>数据来源</h4>
                <div class="set-block">
                    <div class="j-search-con single-box">
                        <input type="text" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <ul data-name="dataOrigin">
                                <li value="0">教师基本信息</li>
                                <li value="1">学期教师数据</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="set-item">
                <h4>来源学期</h4>
                <div class="set-block">
                    <div class="j-search-con single-box">
                        <input type="text" placeholder="请选择" th:value="${previousSemester}" readonly=""
                               class="schoolSel"
                               formAlias="xqjssj" fieldAlias="xqjssj_xnxq">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <ul data-name="originTerm">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="set-item">
                <h4>目标学期</h4>
                <div class="set-block">
                    <div class="j-search-con single-box">
                        <input type="text" placeholder="请选择" th:value="${semester}" readonly="" class="schoolSel"
                               formAlias="xnxq" fieldAlias="xnxq_xnxqh">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <ul data-name="term">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="set-item">
                <h4>继承教师状态</h4>
                <div class="set-block">
                    <div class="j-search-con single-box">
                        <input type="text" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <ul data-name="teacherStatus">
                                <li value="0">继承全部状态教师</li>
                                <li value="1">仅继承在职教师</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="set-item">
                <h4>继承角色</h4>
                <div class="set-block">
                    <div class="j-search-con multiple-box">
                        <input type="text" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <ul data-name="inheritRole">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="set-item">
                <h4>继承方式</h4>
                <div class="set-block">
                    <div class="j-search-con single-box">
                        <input type="text" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <ul data-name="inheritType">
                                <li value="0">追加</li>
                                <li value="1">覆盖</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="dialog-footer">
        <button class="btnCancel">取消</button>
        <button class="btnSure" id="btnInheritSure">确定</button>
    </div>
</div>
</body>
<script th:src="${_CPR_+_VR_+'/js/jquery-1.11.3.min.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/basic/slideCommon.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/my.util.js'}"></script>
<script th:inline="javascript">
    const _VR_ = [[${_VR_}]];
    $(function () {
        $("ul[data-name=dataOrigin]").on('click', 'li', function () {
            const isTeacherInfo = $(this).text().trim() === '教师基本信息';
            $(this).parents('.set-item').next()[isTeacherInfo ? 'hide' : 'show']();
            $(this).parents('.set-con').find(".set-item:eq(4)")[isTeacherInfo ? 'show' : 'hide']();
        })

        $("#btnInheritSure").click(function () {
            const selParams = {};
            $("#inheritDialog ul").each(function () {
                const key = $(this).attr("data-name");
                let val = $(this).find(".active").attr('value');
                let ipt = $(this).parents(".j-search-con").find("input").val();
                selParams[key] = val ? val : ipt;
            })
            const requiredFields = [
                {key: 'dataOrigin', message: '请选择数据来源'},
                {key: 'originTerm', message: '请选择来源学期'},
                {key: 'term', message: '请选择目标学期'},
                {key: 'teacherStatus', message: '请选择继承教师状态'},
                {key: 'inheritRole', message: '请选择继承角色'},
                {key: 'inheritType', message: '请选择继承方式'}
            ];
            for (const field of requiredFields) {
                if (selParams["dataOrigin"] === "0" && field.key === "originTerm") {
                    continue;
                }
                if (selParams["dataOrigin"] === "1" && field.key === "inheritRole") {
                    continue;
                }
                if (!selParams[field.key]) {
                    U.fail(field.message);
                    return false;
                }
            }
            U.ajax({
                type: 'post',
                url: "/basic/pop/inheritSemesterTeacher",
                data: $.param(selParams),
                dataType: 'json',
                success: function (result) {
                    if (!result.status) {
                        U.fail(result.msg);
                        return false;
                    }
                    U.success("继承成功");
                    setTimeout(function () {
                        window.parent.postMessage(JSON.stringify({action: 1}), "*");
                    }, 2000);
                }
            })
        })
        $("#inheritDialog .close,.btnCancel").click(function () {
            window.parent.postMessage(JSON.stringify({action: 1}), "*");
            $("#inheritDialog,.mask").hide();
        })
        $.get(_VR_ + "/back/role/getRoleList", {status: 1}, function (result) {
            if (result.dataList) {
                let html = "";
                for (let i = 0; i < result.dataList.length; i++) {
                    let item = result.dataList[i];
                    html += "<li>" + item.roleName + "</li>";
                }
                $("ul[data-name='inheritRole']").html(html);
            }
        }, "json");
    })
</script>

</html>