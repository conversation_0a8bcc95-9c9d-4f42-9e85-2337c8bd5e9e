<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
    <head>
        <meta charset="UTF-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>实习学分类型</title>
        <link rel="stylesheet" th:href="@{~/css/creditManage/global.css}">
        <link rel="stylesheet" th:href="@{~/plugin/layui/css/layui.css}">
        <link rel="stylesheet" th:href="@{~/css/creditManage/common.css}">
        <link rel="stylesheet" th:href="@{~/css/creditManage/reset.css}">
        <link rel="stylesheet" th:href="@{~/css/creditManage/index.css}">
        <script th:src="@{~/plugin/layui/layui.js}"></script>
    </head>
    <body>
        <div class="main">
            <div class="top">
                <div class="title">
                    <div class="back">返回</div>
                    <div class="levelone">学分参数设置</div>
                    <div class="icon"></div>
                    <div class="leveltwo">实习学分类型</div>
                </div>
                <div class="btn" id="saveBth">保存设置</div>
            </div>
            <div class="con">
                <div class="c-item">
                    <h3>学分数据来源设置</h3>
                    <input type="hidden" id="creditDataSourceSetId">
                    <div class="lab">
                        <div class="name" style="width:154px;">来源数据类型</div>
                        <div class="j-search-con single-box">
                            <input type="text" placeholder="请选择" readonly class="schoolSel" th:value="分数"
                                   id="sourceDataType">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <ul>
                                    <li class="active">分数</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="lab">
                        <div class="name" style="width:154px;">是否设定统一标准学分</div>
                        <div class="switc-con">
                            <div class="switch switch-open"><span></span></div>
                            <div class="switch-con" id="isSetUnifyStandardCredit">开启</div>
                        </div>
                    </div>
                    <div class="lab">
                        <div class="name" style="width:154px;">标准学分分值</div>
                        <div class="input">
                            <input type="number" name="title" lay-verify="title" autocomplete="off"
                                   placeholder="请输入整数,最多输入两位有效小数位数"
                                   class="layui-input" id="standardCredit" style="width: 270px">
                        </div>
                    </div>
                </div>
                <div class="c-item" id="cTime">
                    <div class="c-top">
                        <h4>学分换算规则设置</h4>
                        <div class="btns-list">
                            <span class="add">新增换算规则</span>
                        </div>
                    </div>
                    <table class="layui-hide" id="internshipTable" lay-filter="internshipTable"></table>
                </div>
                <!--                <div class="save-btn" id="saveBth">保存设置</div>-->
            </div>
        </div>
        <!-- 添加 -->
        <div class="popup formulaConversion">
            <div class="popup-box">
                <div class="pu-title">添加</div>
                <input type="hidden" id="creditConversionRuleSetDataId">
                <div class="pu-con">
                    <div class="lab t-lab">
                        <div class="f-top">级制名称</div>
                        <div class="input">两档制</div>
                    </div>
                    <div class="lab t-lab">
                        <div class="f-top">等级名称</div>
                        <div class="input">通过</div>
                    </div>
                    <div class="lab score-range">
                        <div class="f-top"><em>*</em>成绩区间</div>
                        <div class="oprate">
                            <div class="inputs">
                                <input class="input inp layui-input" type="number" id="leftScore" placeholder="请输入">
                                <div class="error">请重新输入</div>
                                <div class="select-input pre-sel">
                                    <div class="name ckd" id="leftMark">≤</div>
                                    <em></em>
                                    <div class="select-dropdown">
                                        <ul class="dropdown-list">
                                            <li>=</li>
                                            <li>&lt;</li>
                                            <li class="cur">≤</li>
                                            <li class="leftMarkEmptyLi">为空</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="select-input score-sel tab-sel">
                                <div class="name ckd" id="scoreType">请选择</div>
                                <em></em>
                                <div class="select-dropdown ">
                                    <ul class="dropdown-list">
                                        <li class="cur">实习成绩</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="inputs right-inp">
                                <div class="select-input next-sel">
                                    <div class="name ckd" id="rightMark">≤</div>
                                    <em></em>
                                    <div class="select-dropdown ">
                                        <ul class="dropdown-list">
                                            <li>=</li>
                                            <li>&lt;</li>
                                            <li class="cur">≤</li>
                                            <li class="rightMarkEmptyLi">为空</li>
                                        </ul>
                                    </div>
                                </div>
                                <input class="input inpf layui-input" type="number" id="rightScore" placeholder="请输入">
                                <div class="error">请重新输入</div>
                            </div>
                        </div>
                    </div>
                    <div class="lab">
                        <div class="f-top"><em>*</em>计算公式</div>
                        <div class="section">
                            <div class="tit">实际学分=</div>
                            <div class="s-con">
                            </div>
                        </div>
                    </div>
                    <div class="lab btns">
                        <div class="available">
                            <h3>可用变量</h3>
                            <div class="a-con">
                                <ul>
                                    <li>原始成绩</li>
                                    <li style="display: none" id="standardCreditLi">标准学分</li>
                                </ul>
                            </div>
                        </div>
                        <div class="keyboard">
                            <h3>可用变量</h3>
                            <div class="k-con">
                                <span class="sign lbracket">(</span>
                                <span class="sign rbracket">)</span>
                                <span class="delet"></span>
                                <span class="empty">清空</span>
                                <span class="num">7</span>
                                <span class="num">8</span>
                                <span class="num">9</span>
                                <span class="sign sign-add">+</span>
                                <span class="num">4</span>
                                <span class="num">5</span>
                                <span class="num">6</span>
                                <span class="sign sign-cancle">－</span>
                                <span class="num">1</span>
                                <span class="num">2</span>
                                <span class="num">3</span>
                                <span class="sign sign-mul">×</span>
                                <span class="num zero">0</span>
                                <span class="num spot">.</span>
                                <span class="sign sign-except">÷</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="pu-btn">
                    <button class="pu-cancel">取消</button>
                    <button class="pu-sure">确定</button>
                </div>
            </div>
        </div>
    </body>
    <script type="text/html" id="tmplToolBar">
        <div class="oprate-table">
            <div class="edit" lay-event="edit">编辑</div>
            <div class="delet" lay-event="delet">删除</div>
        </div>
    </script>
    <script th:src="@{~/js/jquery-3.3.1.min.js}"></script>
    <script th:src="@{~/js/creditManage/common.js}"></script>
    <script th:inline="javascript">
        layui.use(['jquery', 'table', 'form'], function () {

            var zw = new RegExp("[\u4E00-\u9FA5]+");

            // 从页面中取得csId和学年学期参数值
            var csId = [[${csId}]];
            var term = [[${term}]];

            let insertIdCount = -1;

            let deleteIdList = [];
            let updateDataList = [];
            let insertDataList = [];

            var table = layui.table,
                $ = layui.jquery;
            let tabData = [
            ];
            // table
            table.render({
                elem: '#internshipTable',
                data: tabData,
                cols: [
                    [
                        {
                            field: "id",
                            title: "id",
                            align: "center",
                            hide: true
                        },
                        {
                            field: "gradeInterval",
                            title: "成绩区间",
                            align: "center",
                            hide: true
                        },
                        {
                            field: "calculationFormula",
                            title: "计算公式",
                            align: "center",
                            hide: true
                        },
                        {
                            field: "gradeIntervalShow",
                            title: "成绩区间",
                            align: "center",
                        },
                        {
                            field: "calculationFormulaShow",
                            title: "计算公式",
                            align: "center",
                        },
                        {
                            title: "操作",
                            align: "center",
                            toolbar: "#tmplToolBar",
                        },
                    ]
                ],
                page: false,
                limit: Number.MAX_VALUE,
                done: function (res, curr, count) {

                }
            });

            table.on('tool(internshipTable)', function (obj) {
                var rowData = obj.data;
                if (obj.event === 'edit') {
                    editFn(rowData);
                } else if (obj.event === 'delet') {
                    layer.confirm('确定删除吗？', function (index) {
                        if (rowData.id < 0) {
                            for (let i = 0; i < insertDataList.length; i++) {
                                if (insertDataList[i].id === rowData.id) {
                                    insertDataList.splice(i, 1);
                                }
                            }
                        } else {
                            deleteIdList.push(rowData.id);
                        }
                        for (let i = 0; i < tabData.length; i++) {
                            if (tabData[i].id === rowData.id) {
                                tabData.splice(i, 1);
                            }
                        }
                        obj.del();
                        layer.close(index);
                    });
                }
            });

            /**
             * 点击编辑按钮
             */
            function editFn(rowData) {
                $(".formulaConversion .popup-box .pu-title").text("编辑");
                // console.log(rowData);
                $("#creditConversionRuleSetDataId").val(rowData.id);
                // 回显成绩区间
                try {
                    rowData.gradeInterval = rowData.gradeInterval.replaceAll("&lt;", "<");
                    let gradeInterval = JSON.parse(rowData.gradeInterval);
                    if (gradeInterval.leftScore !== undefined) {
                        $(".formulaConversion .popup-box .pu-con .lab .oprate .inputs .inp").val(gradeInterval.leftScore);
                    }
                    if (gradeInterval.leftMark !== undefined) {
                        $(".formulaConversion .popup-box .pu-con .lab .oprate .inputs .pre-sel").find(".name").text(gradeInterval.leftMark);
                        $(".formulaConversion .popup-box .pu-con .lab .oprate .inputs .pre-sel .select-dropdown ul li").each(function () {
                            if ($(this).text() === gradeInterval.leftMark || ($(this).text() === "为空" && gradeInterval.leftMark === "")) {
                                if (!$(this).hasClass("cur")) {
                                    $(this).addClass("cur");
                                }
                            } else {
                                $(this).removeClass("cur");
                            }
                        });
                    }
                    if (gradeInterval.scoreType) {
                        $(".formulaConversion .popup-box .pu-con .lab .oprate .tab-sel").find(".name").addClass("ckd").text(gradeInterval.scoreType);
                        $(".formulaConversion .popup-box .pu-con .lab .oprate .tab-sel .select-dropdown ul li").each(function () {
                            if ($(this).text() === gradeInterval.scoreType) {
                                if (!$(this).hasClass("cur")) {
                                    $(this).addClass("cur");
                                }
                            } else {
                                $(this).removeClass("cur");
                            }
                        });
                    }
                    if (gradeInterval.rightMark !== undefined) {
                        $(".formulaConversion .popup-box .pu-con .lab .oprate .inputs .next-sel").find(".name").text(gradeInterval.rightMark);
                        $(".formulaConversion .popup-box .pu-con .lab .oprate .inputs .next-sel .select-dropdown ul li").each(function () {
                            if ($(this).text() === gradeInterval.rightMark || ($(this).text() === "为空" && gradeInterval.rightMark === "")) {
                                if (!$(this).hasClass("cur")) {
                                    $(this).addClass("cur");
                                }
                            } else {
                                $(this).removeClass("cur");
                            }
                        });
                    }
                    if (gradeInterval.rightScore !== undefined) {
                        $(".formulaConversion .popup-box .pu-con .lab .oprate .inputs .inpf").val(gradeInterval.rightScore);
                    }
                } catch (e) {
                    throw new Error("解析成绩区间json数组失败：" + rowData.gradeInterval);
                }
                // 计算公式中的元素数组，例：["实际学分","=","原始成绩","÷","60","×","标准学分"]
                let calculationFormulaArray = JSON.parse(rowData.calculationFormula);
                // 回显计算公式
                try {
                    let contextEle = $(".formulaConversion .popup-box .pu-con .lab .section .s-con");
                    let numList = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "."];
                    let markList = ["+", "-", "×", "÷", "(", ")"];
                    contextEle.text("");
                    for (let i = 0; i < calculationFormulaArray.length; i++) {
                        if (i === 0 || i === 1) {
                            continue;
                        }
                        let item = calculationFormulaArray[i];
                        if (numList.indexOf(item) !== -1) {
                            if (item === "0") {
                                $(contextEle).append('<span class="num zero">0</span>');
                            } else if (item === ".") {
                                $(contextEle).append('<span class="num spot">.</span>');
                            } else {
                                $(contextEle).append('<span class="num">' + item + '</span>');
                            }
                        } else if (markList.indexOf(item) !== -1) {
                            $(contextEle).append('<span class="sign">' + item + '</span>');
                        } else {
                            $(contextEle).append('<span>' + item + '</span>');
                        }
                    }
                } catch (e) {
                }
                // 回显可用变量
                // $(".formulaConversion .popup-box .pu-con .lab .available .a-con ul li").eq(0).addClass("disabled");
                // $(".formulaConversion .popup-box .pu-con .lab .oprate .select-input .select-dropdown .dropdown-list li").eq(0).addClass("cur");
                $(".formulaConversion .popup-box .pu-con .lab.t-lab").hide();
                $(".formulaConversion").show();
            }

            // 下拉
            $(".formulaConversion").on("click", ".popup-box .pu-con .lab .select-input .name", function (e) {
                $(this).parent().toggleClass("clicked");
                stopBubble(e);
            });

            $(".formulaConversion .popup-box .pu-con").on("click", ".lab .oprate .select-input .select-dropdown .dropdown-list li",
                function (e) {
                    $(this).addClass("cur").siblings().removeClass("cur");
                    let kosl = '';
                    kosl = $(this).text();
                    if (kosl == '为空') {
                        kosl = '';
                        if ($(this).hasClass("leftMarkEmptyLi")) {
                            $("#leftScore").val(kosl);
                        } else if ($(this).hasClass("rightMarkEmptyLi")) {
                            $("#rightScore").val(kosl);
                        }
                    }
                    $(this).parents(".select-input").find(".name").addClass("ckd");
                    $(this).parents(".select-input").find(".name").text(kosl);
                    $(this).parents(".select-input").removeClass("clicked");
                }
            );

            function stopBubble(e) {
                if (e && e.stopPropagation)
                    e.stopPropagation();
                else {
                    window.event.cancelBubble = true;
                }
            }

            $(document).on("click", function (event) {
                var _con = $('.select-input');
                if (!_con.is(event.target) && _con.has(event.target).length === 0) {
                    $(".select-input").removeClass("clicked");
                }
            });

            // 成绩计算
            $(".formulaConversion .popup-box .pu-con").on("click", ".lab .available .a-con ul li", function () {
                if ($(this).hasClass("disabled")) {
                    return false;
                }
                // $(this).addClass("disabled");
                let txt = $(this).text();
                $(".formulaConversion .popup-box .pu-con .lab .section .s-con").append('<span>' + txt + '</span>');
            });

            // 后移
            $(".formulaConversion .popup-box .pu-con").on("click", ".lab .keyboard .k-con .delet", function () {
                // let txt = $(".formulaConversion .popup-box .pu-con .lab .section .s-con").find("span").last().text();
                // $(".formulaConversion .popup-box .pu-con .lab .available .a-con ul li.disabled").each(function () {
                //     if ($(this).text() == txt) {
                //         $(this).removeClass("disabled");
                //     }
                // })
                $(".formulaConversion .popup-box .pu-con .lab .section .s-con").find("span").last().remove();
            });

            // 清空
            $(".formulaConversion .popup-box .pu-con").on("click", ".lab .keyboard .k-con .empty", function () {
                // $(".formulaConversion .popup-box .pu-con .lab .available .a-con ul li.disabled").removeClass("disabled")
                $(".formulaConversion .popup-box .pu-con .lab .section .s-con").find("span").remove();
            });

            // 加减乘除
            $(".formulaConversion .popup-box .pu-con").on("click", ".lab .keyboard .k-con .sign", function () {
                let signTxt = $(this).text();
                $(".formulaConversion .popup-box .pu-con .lab .section .s-con").append('<span class="sign">' + signTxt + '</span>');
            });

            // 输入数字
            $(".formulaConversion .popup-box .pu-con .lab").on("click", ".keyboard .k-con .num", function () {
                let signTxt = $(this).text();
                let cls = $(this).attr("class");
                $(".formulaConversion .popup-box .pu-con .lab .section .s-con").append('<span class="' + cls + '">' + signTxt + '</span>');
            });

            // 点击弹窗的确定或取消按钮，弹窗隐藏
            $(".formulaConversion .popup-box .pu-btn button").click(function () {
                if ($(this).text() === "确定") {
                    let creditConversionRuleSetDataId = $("#creditConversionRuleSetDataId").val();
                    let leftScore = $("#leftScore").val();
                    let leftMark = $("#leftMark").text();
                    let scoreType = $("#scoreType").text();
                    let rightMark = $("#rightMark").text();
                    let rightScore = $("#rightScore").val();
                    let gradeInterval = {};
                    let gradeIntervalShow = "";
                    // 校验成绩区间是否正确
                    if (checkGradeInterval(leftScore, leftMark, rightMark, rightScore)) {
                        gradeIntervalShow += (leftScore !== "" && leftScore !== undefined ? leftScore : "");
                        gradeInterval.leftScore = (leftScore !== "" && leftScore !== undefined ? leftScore : "");
                        gradeIntervalShow += (leftMark !== "" ? leftMark : "");
                        gradeInterval.leftMark = (leftMark !== "" ? leftMark : "");
                        gradeIntervalShow += (scoreType !== "" ? scoreType : "");
                        gradeInterval.scoreType = (scoreType !== "" ? scoreType : "");
                        gradeIntervalShow += (rightMark !== "" ? rightMark : "");
                        gradeInterval.rightMark = (rightMark !== "" ? rightMark : "");
                        gradeIntervalShow += (rightScore !== "" && rightScore !== undefined ? rightScore : "");
                        gradeInterval.rightScore = (rightScore !== "" && rightScore !== undefined ? rightScore : "");
                        // console.log("正确的成绩区间：" + gradeIntervalShow);
                    } else {
                        layer.msg("成绩区间设置不正确：" + leftScore + leftMark + scoreType + rightMark + rightScore, {
                            icon: 2,
                            time: 2000
                        });
                        return;
                    }
                    gradeInterval = JSON.stringify(gradeInterval);
                    let calculationFormula = ["实际学分", "="];
                    let calculationFormulaShow = "";
                    $(".formulaConversion .popup-box .pu-con .lab .section .s-con").find("span").each(function () {
                        calculationFormulaShow += $(this).text();
                        calculationFormula.push($(this).text());
                    });
                    calculationFormula = JSON.stringify(calculationFormula);
                    // 校验计算公式是否正确
                    if (checkFormula(calculationFormulaShow)) {
                        // console.log("正确的计算公式：" + calculationFormulaShow);
                    } else {
                        layer.msg("计算公式设置不正确：" + calculationFormulaShow, {
                            icon: 2,
                            time: 2000
                        });
                        return;
                    }
                    calculationFormulaShow = "实际学分=" + calculationFormulaShow;
                    // 如果id不为空，则是更新数据；否则是新增数据
                    if (creditConversionRuleSetDataId) {
                        creditConversionRuleSetDataId = parseInt(creditConversionRuleSetDataId);
                        for (let i = 0; i < tabData.length; i++) {
                            let rowData = tabData[i];
                            if (rowData.id === creditConversionRuleSetDataId) {
                                rowData.gradeInterval = gradeInterval;
                                rowData.gradeIntervalShow = gradeIntervalShow;
                                rowData.calculationFormula = calculationFormula;
                                rowData.calculationFormulaShow = calculationFormulaShow;

                                // 编辑需要新增的数据,id从-1开始递减
                                if (creditConversionRuleSetDataId < 0) {
                                    for (let j = 0; j < insertDataList.length; j++) {
                                        let insertRowData = insertDataList[j];
                                        if (insertRowData.id === creditConversionRuleSetDataId) {
                                            insertRowData.gradeInterval = gradeInterval;
                                            insertRowData.gradeIntervalShow = gradeIntervalShow;
                                            insertRowData.calculationFormula = calculationFormula;
                                            insertRowData.calculationFormulaShow = calculationFormulaShow;
                                        }
                                    }
                                } else {
                                    let flag = false;
                                    for (let j = 0; j < updateDataList.length; j++) {
                                        let updateRowData = updateDataList[j];
                                        if (updateRowData.id === creditConversionRuleSetDataId) {
                                            updateRowData.gradeInterval = gradeInterval;
                                            updateRowData.gradeIntervalShow = gradeIntervalShow;
                                            updateRowData.calculationFormula = calculationFormula;
                                            updateRowData.calculationFormulaShow = calculationFormulaShow;
                                            flag = true;
                                            break;
                                        }
                                    }
                                    if (!flag) {
                                        let updateRowData = {};
                                        updateRowData.id = creditConversionRuleSetDataId;
                                        updateRowData.gradeInterval = gradeInterval;
                                        updateRowData.gradeIntervalShow = gradeIntervalShow;
                                        updateRowData.calculationFormula = calculationFormula;
                                        updateRowData.calculationFormulaShow = calculationFormulaShow;
                                        updateDataList.push(updateRowData);
                                    }
                                }
                                break;
                            }
                        }
                    } else {
                        let insertRowData = {};
                        insertRowData.id = insertIdCount--;
                        insertRowData.gradeInterval = gradeInterval;
                        insertRowData.gradeIntervalShow = gradeIntervalShow;
                        insertRowData.calculationFormula = calculationFormula;
                        insertRowData.calculationFormulaShow = calculationFormulaShow;
                        insertDataList.push(insertRowData);
                        tabData.push(insertRowData);
                    }
                    table.reload("internshipTable", {data: tabData});
                    // console.log(insertDataList);
                    // console.log(updateDataList);
                    // console.log(deleteIdList);
                }
                $(".formulaConversion").hide();
            });

            // 成绩添加
            $(".main .con .c-item .add-rule span").click(function () {
                $(".formulaConversion .popup-box .pu-title").text("添加");
                $("#creditConversionRuleSetDataId").val("");
                $(".formulaConversion .popup-box .pu-con .lab.t-lab").show();
                $(".formulaConversion .popup-box .pu-con .lab.score-range").hide();
                $(".formulaConversion .popup-box .pu-con .lab .oprate .inputs .inp").val("");
                $(".formulaConversion .popup-box .pu-con .lab .oprate .inputs .inpf").val("");
                $(".formulaConversion .popup-box .pu-con .lab .oprate .tab-sel").find(".name").removeClass("ckd").text("请选择");
                $(".formulaConversion .popup-box .pu-con .lab .oprate .select-input .select-dropdown .dropdown-list li").eq(0).removeClass("cur");
                $(".formulaConversion .popup-box .pu-con .lab .section .s-con").empty();
                $(".formulaConversion .popup-box .pu-con .lab .available .a-con ul li").removeClass("disabled");
                $(".formulaConversion").show();
            });

            // 点击返回按钮，后退
            $(".back").click(function () {
                history.back();
            });

            // 点击新增换算规则按钮
            $(".main .con .c-item .c-top .btns-list span.add").click(function () {
                $(".formulaConversion .popup-box .pu-title").text("添加");
                $("#creditConversionRuleSetDataId").val("");
                $(".formulaConversion .popup-box .pu-con .lab.t-lab").hide();
                $(".formulaConversion .popup-box .pu-con .lab .oprate .inputs .inp").val("");
                $(".formulaConversion .popup-box .pu-con .lab .oprate .inputs .inpf").val("");
                $(".formulaConversion .popup-box .pu-con .lab .oprate .tab-sel").find(".name").text("实习成绩");
                $(".formulaConversion .popup-box .pu-con .lab .oprate .select-input .select-dropdown .dropdown-list li").eq(0).removeClass("cur");
                $(".formulaConversion .popup-box .pu-con .lab .section .s-con").empty();
                $(".formulaConversion .popup-box .pu-con .lab .available .a-con ul li").removeClass("disabled");
                $(".formulaConversion").show();
            });

            // 来源数据类型下拉框点击事件
            $(".origin .j-search-con").on("click", ".j-select-year ul li", function () {
                let ind = $(this).index();
                if (ind == 0) {
                    $(".main .con .c-item .score-lab").hide();
                    $(".main .con .c-item .c-top .btns-list span.add").show();
                    $(".fraction-table").show();
                    $(".score-table").hide();
                    $(".main .con .c-item .add-rule").hide();
                } else {
                    $(".main .con .c-item .score-lab").show();
                    $(".main .con .c-item .c-top .btns-list span.add").hide();
                    $(".fraction-table").hide();
                    $(".score-table").show();
                    $(".main .con .c-item .add-rule").show();
                }
            });

            // 是否设定统一标准学分的开关
            $(".switc-con .switch").click(function () {
                if ($(this).hasClass("switch-open")) {
                    $(this).removeClass("switch-open");
                    $(this).next().text('关闭');
                    $(this).parents(".lab").next().hide();
                    $("#standardCreditLi").css("display", "none");
                } else {
                    $(this).addClass("switch-open");
                    $(this).next().text('开启');
                    $(this).parents(".lab").next().show();
                    $("#standardCreditLi").css("display", "block");
                }
            });

            // 点击保存设置按钮
            $("#saveBth").click(function () {
                let id = $("#creditDataSourceSetId").val();
                let sourceDataType = $("#sourceDataType").val();
                let isSetUnifyStandardCredit = $("#isSetUnifyStandardCredit").text() === "开启" ? 0 : 1;
                let standardCredit = $("#standardCredit").val();
                let checkStandardCredit = true;
                if (isSetUnifyStandardCredit === 0) {
                    if (standardCredit === "") {
                        checkStandardCredit = false;
                    } else {
                        let index = standardCredit.indexOf(".");
                        if (index === 0) {
                            checkStandardCredit = false;
                        } else if (index !== -1) {
                            // 截取小数点和之后的字符
                            let subStr = standardCredit.slice(index);
                            if (subStr.length > 3) {
                                checkStandardCredit = false;
                            }
                        }
                    }
                }
                if (!sourceDataType) {
                    layer.confirm('学分数据来源设置，来源数据类型未设置', {
                        btn: ['取消', '确定'] //按钮
                    });
                } else if (!checkStandardCredit) {
                    layer.confirm('标准学分分值只能设置整数，最多设置2位有效小数位数，请重新设置', {
                        btn: ['取消', '确定'] //按钮
                    });
                } else if (tabData.length === 0) {
                    layer.confirm('学分换算规则未设置，请设置后保存', {
                        btn: ['取消', '确定'] //按钮
                    });
                } else if (!checkContinue(tabData)) {
                    layer.confirm('成绩区间不连续，请检查', {
                        btn: ['取消', '确定'] //按钮
                    });
                } else if (!checkGradeIntervalContinue(tabData)) {
                    layer.confirm('成绩区间设置有误，请重新设置', {
                        btn: ['取消', '确定'] //按钮
                    });
                } else {
                    $.ajax({
                        type: 'post',
                        dataType: 'json',
                        data: {
                            "creditDataSourceSetJson": JSON.stringify({
                                "id": id,
                                "csId": csId,
                                "term": term,
                                "sourceDataType": sourceDataType,
                                "isSetUnifyStandardCredit": isSetUnifyStandardCredit,
                                "standardCredit": isSetUnifyStandardCredit === 0 ? standardCredit : undefined,
                            }),
                            "insertDataListJson": JSON.stringify(insertDataList),
                            "updateDataListJson": JSON.stringify(updateDataList),
                            "deleteIdListJson": JSON.stringify(deleteIdList)

                        },
                        url: '/new/credit/internship/saveInternshipCreditSet',
                        success: function (res) {
                            if (res.code == 200) {
                                layer.msg("保存成功", {icon: 1, time: 2000});
                                insertDataList = [];
                                updateDataList = [];
                                deleteIdList = [];
                                // 加载学分数据来源设置
                                loadCreditDataSourceSetData();
                            } else {
                                layer.msg(res.msg, {icon: 2, time: 2000});
                            }
                        }
                    });
                }
            });

            // 立即执行函数，初始化页面
            $(function () {
                // 加载学分数据来源设置
                loadCreditDataSourceSetData();
            });

            /**
             * 加载学分数据来源设置
             */
            function loadCreditDataSourceSetData() {
                // 获取学分数据来源设置
                $.ajax({
                    type: 'get',
                    dataType: 'json',
                    url: '/new/credit/internship/getCreditDataSourceSet?term='+term,
                    success: function (res) {
                        if (res.code == 200) {
                            let data = res.data;
                            if (data) {
                                $("#creditDataSourceSetId").val(data.id);
                                if (data.sourceDataType) {
                                    $("#sourceDataType").val(data.sourceDataType);
                                }
                                let isSetUnifyStandardCreditEle = $(".switc-con .switch");
                                if (data.isSetUnifyStandardCredit === 0) {
                                    $(isSetUnifyStandardCreditEle).next().text('开启');
                                    $(isSetUnifyStandardCreditEle).parents(".lab").next().show();
                                    $("#standardCreditLi").css("display", "block");
                                    if (!$(isSetUnifyStandardCreditEle).hasClass("switch-open")) {
                                        $(isSetUnifyStandardCreditEle).addClass("switch-open");
                                    }
                                } else if (data.isSetUnifyStandardCredit === 1) {
                                    $(isSetUnifyStandardCreditEle).next().text('关闭');
                                    $(isSetUnifyStandardCreditEle).parents(".lab").next().hide();
                                    $("#standardCreditLi").css("display", "none");
                                    if ($(isSetUnifyStandardCreditEle).hasClass("switch-open")) {
                                        $(isSetUnifyStandardCreditEle).removeClass("switch-open");
                                    }
                                }
                                if (data.standardCredit === 0 || data.standardCredit === "0.0") {
                                    $("#standardCredit").val(0);
                                } else if (data.standardCredit) {
                                    $("#standardCredit").val(data.standardCredit);
                                }
                                loadCreditConversionRuleData();
                            } else {
                                $("#standardCreditLi").css("display", "block");
                            }
                            // console.log(res);
                        } else {
                            layer.msg(res.msg, {icon: 2, time: 2000});
                        }
                    }
                });
            }

            /**
             * 加载学分换算规则设置
             */
            function loadCreditConversionRuleData() {
                var pid = $("#creditDataSourceSetId").val();
                // 获取学分换算规则设置
                $.ajax({
                    type: 'get',
                    dataType: 'json',
                    url: '/new/credit/internship/getCreditConversionRuleSet?pid='+pid,
                    success: function (res) {
                        if (res.code == 200) {
                            let data = res.data;
                            if (data) {
                                tabData = res.data;
                                tabData.forEach(function (element) {
                                    try {
                                        let gradeInterval = JSON.parse(element.gradeInterval);
                                        let gradeIntervalShow = "";
                                        gradeIntervalShow += gradeInterval.leftScore === undefined ? "" : gradeInterval.leftScore;
                                        gradeIntervalShow += gradeInterval.leftMark ? gradeInterval.leftMark : "";
                                        gradeIntervalShow += gradeInterval.scoreType ? gradeInterval.scoreType : "实习成绩";
                                        gradeIntervalShow += gradeInterval.rightMark ? gradeInterval.rightMark : "";
                                        gradeIntervalShow += gradeInterval.rightScore === undefined ? "" : gradeInterval.rightScore;
                                        element.gradeIntervalShow = gradeIntervalShow;
                                        element.calculationFormulaShow = JSON.parse(element.calculationFormula).join("");
                                    } catch (e) {
                                        element.gradeIntervalShow = element.gradeInterval;
                                        element.calculationFormulaShow = element.calculationFormula;
                                    }
                                });
                                table.reload("internshipTable", {data: tabData});
                            }
                            // console.log(res);
                        } else {
                            layer.msg(res.msg, {icon: 2, time: 2000});
                        }
                    }
                });
            }

            /**
             * 检查成绩区间是否正确
             */
            function checkGradeInterval(leftScore, leftMark, rightMark, rightScore) {
                if (leftMark && (leftScore === "" || leftScore === undefined)) {
                    return false;
                }
                if ((leftScore !== "" && leftScore !== undefined) && leftMark === "") {
                    return false;
                }
                if (rightMark && (rightScore === "" || rightScore === undefined)) {
                    return false;
                }
                if ((rightScore !== "" && rightScore !== undefined) && rightMark === "") {
                    return false;
                }
                if (leftMark === "=" && rightMark === "=" || leftMark === "" && rightMark === "") {
                    return false;
                }
                if ((leftScore !== "" && leftScore !== undefined) && (rightScore !== "" && rightScore !== undefined)) {
                    if (leftScore - rightScore > 0) {
                        return false;
                    }
                }
                return true;
            }

            /**
             * 保存之前，检查所有的成绩区间是否连续
             */
            function checkGradeIntervalContinue(tabData) {
                let result = true;
                if (tabData && tabData.length) {
                    // 先获取第一个成绩区间范围，并放到总区间范围中
                    let gradeIntervalCompare = JSON.parse(tabData[0].gradeInterval);
                    let gradeIntervalArray = [];
                    gradeIntervalArray.push(gradeIntervalCompare);
                    for (let i = 1; i < tabData.length; i++) {
                        let gradeInterval = JSON.parse(tabData[i].gradeInterval);
                        let leftMarkSource = gradeInterval.leftMark === "&lt;" ? "<" : gradeInterval.leftMark;
                        let leftScoreSource = gradeInterval.leftScore;
                        let rightMarkSource = gradeInterval.rightMark === "&lt;" ? "<" : gradeInterval.rightMark;
                        let rightScoreSource = gradeInterval.rightScore;
                        // 循环判断当前成绩区间是否和总区间范围有交集冲突
                        for (let j = 0; j < gradeIntervalArray.length; j++) {
                            let compareTarget = gradeIntervalArray[j];
                            let leftMarkTarget = compareTarget.leftMark === "&lt;" ? "<" : compareTarget.leftMark;
                            let leftScoreTarget = compareTarget.leftScore;
                            let rightMarkTarget = compareTarget.rightMark === "&lt;" ? "<" : compareTarget.rightMark;
                            let rightScoreTarget = compareTarget.rightScore;

                            if (leftMarkTarget && !rightMarkTarget) {
                                // 对比区域只有左边，例如：60≤实习成绩
                                if (leftMarkSource && !rightMarkSource) {
                                    result = false;
                                    break;
                                } else if (rightMarkSource) {
                                    let differ = leftScoreTarget - rightScoreSource;
                                    if (differ === 0) {
                                        if (leftMarkTarget !== "<" && rightMarkSource !== "<") {
                                            result = false;
                                            break;
                                        }
                                    } else if (differ < 0) {
                                        result = false;
                                        break;
                                    }
                                }
                            } else if (!leftMarkTarget && rightMarkTarget) {
                                // 对比区域只有右边，例如：实习成绩≤60
                                if (leftMarkSource) {
                                    let differ = rightScoreTarget - leftScoreSource;
                                    if (differ === 0) {
                                        if (rightMarkTarget !== "<" && leftMarkSource !== "<") {
                                            result = false;
                                            break;
                                        }
                                    } else if (differ > 0) {
                                        result = false;
                                        break;
                                    }
                                } else if (!leftMarkSource && rightMarkSource) {
                                    result = false;
                                    break;
                                }
                            } else if (leftMarkTarget && rightMarkTarget) {
                                // 对比区域两边都有，例如：60≤实习成绩≤100
                                if (leftMarkSource && !rightMarkSource) {
                                    let differ = rightScoreTarget - leftScoreSource;
                                    if (differ === 0) {
                                        if (rightMarkTarget !== "<" && leftMarkSource !== "<") {
                                            result = false;
                                            break;
                                        }
                                    } else if (differ > 0) {
                                        result = false;
                                        break;
                                    }
                                } else if (!leftMarkSource && rightMarkSource) {
                                    let differ = leftScoreTarget - rightScoreSource;
                                    if (differ === 0) {
                                        if (leftMarkTarget !== "<" && rightMarkSource !== "<") {
                                            result = false;
                                            break;
                                        }
                                    } else if (differ < 0) {
                                        result = false;
                                        break;
                                    }
                                } else if (leftMarkSource && rightMarkSource) {
                                    let differ1 = rightScoreTarget - leftScoreSource;
                                    let differ2 = leftScoreTarget - rightScoreSource;
                                    if (differ1 === 0) {
                                        if (rightMarkTarget !== "<" && leftMarkSource !== "<") {
                                            result = false;
                                            break;
                                        }
                                    } else if (differ1 > 0) {
                                        if (differ2 === 0) {
                                            if (leftMarkTarget !== "<" && rightMarkSource !== "<") {
                                                result = false;
                                                break;
                                            }
                                        } else if (differ2 < 0) {
                                            result = false;
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                        // 如果有冲突，则跳出循环
                        if (!result) {
                            break;
                        } else {
                            gradeIntervalArray.push(gradeInterval);
                        }
                    }
                }
                return result;
            }

            /**
             * 检查成绩区间是否连续
             */
            function checkContinue(tabData) {

                if (tabData.length < 1) {
                    return true;
                }
                for (let i = 0; i < tabData.length; i++) {
                    let flag = true;
                    for (let j = 0; j < tabData.length - i - 1; j++) {
                        var curData = JSON.parse(tabData[j].gradeInterval);
                        var nextData = JSON.parse(tabData[j + 1].gradeInterval);
                        if (curData.leftScore == undefined || curData.leftScore == null || curData.leftScore == "") {
                            if (j == 0) {
                                continue;
                            }
                            let temp = tabData[j];
                            tabData[j] = tabData[0];
                            tabData[0] = temp;
                            flag = false;
                        } else if (parseFloat(curData.leftScore) > parseFloat(nextData.leftScore)) {
                            let temp = tabData[j];
                            tabData[j] = tabData[j + 1];
                            tabData[j + 1] = temp;
                            flag = false;
                        }
                    }
                    if (flag) {
                        break;
                    }
                }
                for (let i = 1; i < tabData.length; i++) {
                    var data = JSON.parse(tabData[i].gradeInterval.replaceAll("<", "&lt;"));
                    var preData = JSON.parse(tabData[i - 1].gradeInterval.replaceAll("<", "&lt;"));
                    if (data.leftMark === preData.rightMark && data.leftScore === preData.rightScore) {
                        return false;
                    }
                    if (data.leftMark !== "≤" && preData.rightMark !== "≤") {
                        return false;
                    }
                    if ((data.leftMark === "≤" && preData.rightMark === "<" || data.leftMark === "<" && preData.rightMark === "≤") && data.leftScore !== preData.rightScore) {
                        return false;
                    }
                }
                return true;
            }

            /**
             * 验证公式是否正确
             */
            function checkFormula(string) {
                // 剔除空白符
                string = string.replace(/\s/g, ' ');

                // 错误情况，空字符串
                if ("" === string) {
                    return false;
                }

                // 错误情况，运算符连续
                if (/[\+\×\÷\-\－\*\/]{2,}/.test(string)) {
                    return false;
                }

                // 空括号
                if (/\(\)/.test(string)) {
                    return false;
                }

                // 错误情况，括号不配对
                var stack = [];
                for (var i = 0, item; i < string.length; i++) {
                    item = string.charAt(i);
                    if ('(' === item) {
                        stack.push('(');
                    } else if (')' === item) {
                        if (stack.length > 0) {
                            stack.pop();
                        } else {
                            return false;
                        }
                    }
                }
                if (0 !== stack.length) {
                    return false;
                }

                // 错误情况，(后面是运算符
                if (/\([\+\×\÷\-\－\*\/]/.test(string)) {
                    return false;
                }

                // 错误情况，)前面是运算符
                if (/[\+\×\÷\-\－\*\/]\)/.test(string)) {
                    return false;
                }

                // 错误情况，(前面不是运算符
                if (/[^\+\×\÷\-\－\*\/]\(/.test(string)) {
                    return false;
                }

                // 错误情况，)后面不是运算符
                if (/\)[^\+\×\÷\-\－\*\/]/.test(string)) {
                    return false;
                }

                //错误情况，运算符号不能在首末位
                if (/^[\+\×\÷\-\*\/.]|[\+\×\÷\-\－\*\/.]$/.test(string)) {
                    return false;
                }

                for (var i = 0; i < string.length; i++) {
                    if (zw.test(string.charAt(i))) {
                        var item = string.charAt(i);
                        //判断中文前是不是运算符
                        if (i != 0 && !zw.test(string.charAt(i - 1)) && !/[\+\×\÷\-\－\*\/]/.test(string.charAt(i - 1))) {
                            return false;
                        }
                        //判断中文后是不是运算符
                        if (i != string.length - 1 && !zw.test(string.charAt(i + 1)) && !/[\+\×\÷\-\－\*\/]/.test(string.charAt(i + 1))) {
                            return false;
                        }
                        if (item == '原' || item == '标') {
                            //判断下一组数据是否还是中文
                            if (zw.test(string.charAt(i + 4))) {
                                return false;
                            }
                            i += 2;
                        }
                    }
                }

                // // 错误情况，中文后面不是运算符
                // if (!/[\u4e00-\u9fa5]$/.test(string) && /[\u4e00-\u9fa5]/.test(string)) {
                //     if (!(/[\u4e00-\u9fa5][\+\×\÷\-\－\*\/]/.test(string))) {
                //         return false;
                //     }
                // }
                //
                // // 错误情况，中文前面不是运算符
                // if (!/^[\u4e00-\u9fa5]/.test(string) && /[\u4e00-\u9fa5]/.test(string)) {
                //     if (!(/[\+\×\÷\-\－\*\/][\u4e00-\u9fa5]/.test(string))) {
                //         return false;
                //     }
                // }
                // //错误情况，首尾是中文，中间不是运算符
                // if (/^[\u4e00-\u9fa5]/.test(string) && /[\u4e00-\u9fa5]$/.test(string)) {
                //     if (!(/[\+\×\÷\-\－\*\/][\u4e00-\u9fa5]/.test(string)&&/[\u4e00-\u9fa5][\+\×\÷\-\－\*\/]/.test(string))) {
                //         return false;
                //     }
                // }


                return true;
            }
        });
    </script>
</html>