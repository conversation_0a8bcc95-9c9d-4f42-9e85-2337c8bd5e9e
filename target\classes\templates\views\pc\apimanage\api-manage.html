<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">

<head>
    <link rel="stylesheet" th:href="${_CPR_}+'/css/global.css'">
    <th:block th:include="common :: header('教务表单接口管理')"/>
    <th:block th:include="common :: layui-css-2_9_13"/>
    <th:block th:include="common :: jquery-mCustomScrollbar-css"/>
    <link rel="stylesheet" th:href="${_CPR_+'/css/apimanage/common.css'}">
    <link rel="stylesheet" th:href="${_CPR_+'/css/apimanage/reset.css'}">
    <link rel="stylesheet" th:href="${_CPR_}+'/css/apimanage/api.manage.css'">
</head>

<body>
<div class="z-main">
    <div class="z-title">
        <h3>教务表单接口管理</h3>
    </div>
    <div class="z-box">
        <!-- api检索 -->
        <div class="box-con box-common" style="display: block;">
            <div class="z-search">
                <div action="" class="layui-form">
                    <div class="layui-form-item">
                        <label class="layui-form-label">协议</label>
                        <div class="layui-input-block">
                            <div class="j-search-con single-box">
                                <input type="text" name="teacherName" placeholder="请选择" readonly=""
                                       class="schoolSel">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <ul id="search-scheme">
                                        <li class="">http</li>
                                        <li class="">https</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">域名</label>
                        <div class="layui-input-block">
                            <div class="j-search-con">
                                <input type="text" id="search-domain" placeholder="请输入">
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">URI</label>
                        <div class="layui-input-block">
                            <div class="j-search-con">
                                <input type="text" id="search-uri" placeholder="请输入">
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">接口类型</label>
                        <div class="layui-input-block">
                            <div class="j-search-con single-box">
                                <input type="text" name="teacherName" placeholder="请选择" readonly=""
                                       class="schoolSel">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <ul id="search-type">
                                        <li data-type="1" class="">字段数据从第三方获取</li>
                                        <li data-type="2" class="">按钮字段(button)配置的URL</li>
                                        <li data-type="3" class="">后台顶部按钮打开URL</li>
                                        <li data-type="4" class="">后台右侧按钮打开URL</li>
                                        <li data-type="5" class="">提交校验（第三方校验)</li>
                                        <li data-type="7" class="">进入表单校验</li>
                                        <li data-type="8" class="">前端事件</li>
                                        <li data-type="9" class="">数据推送</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <button class="z-btn" id="search-api">查询</button>
                    <div class="clear"></div>

                </div>
            </div>
            <div class="z-btns" style="margin-bottom: 18px;">
                <div class="addRule" id="addScoreLimit"><img th:src="${_CPR_}+'/images/apimanage/add-icon.png'">添加接口
                </div>
                <div class="addRule" id="replaceApi"><img th:src="${_CPR_}+'/images/apimanage/replace.png'">替换接口
                </div>
                <div class="del">删除</div>
            </div>

            <div class="z-table">
                <table class="layui-hide materialTable4" id="materialTable4" lay-filter="materialTable4">
                </table>
            </div>
        </div>
    </div>
</div>
<!-- 接口添加/修改 -->
<div id="selScoreGates" class="dialog">
    <div class="dialog-title">
        <h3>新增接口</h3><span class="pu-close"></span>
    </div>
    <div class="dialog-con">
        <div action="" class="layui-form">
            <input type="hidden" id="api-id">
            <div class="form-item">
                <!--接口替换选项-->
                <div class="layui-form-item replace" style="display: none;">
                    <label class="layui-form-label">单位</label>
                    <div class="layui-input-block">
                        <div class="j-search-con multiple-box">
                            <input type="text" id="replace-unit-name" value="" placeholder="请选择" readonly=""
                                   class="schoolSel">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul id="replace-units">
                                    <li class="all">全部</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <!--接口替换选项-->
                <div class="layui-form-item replace" style="display: none;">
                    <label class="layui-form-label">操作类型</label>
                    <div class="layui-input-block">
                        <div class="j-search-con single-box">
                            <input type="text" id="replace-type-name" value="" placeholder="请选择" readonly=""
                                   class="schoolSel">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul id="replace-type">
                                    <li data-type="1">接口替换</li>
                                    <li data-type="2">http协议替换</li>
                                    <li data-type="3">域名替换</li>
                                    <li data-type="4">uri替换</li>
                                    <li data-type="5">接口参数替换</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="layui-form-item">
                    <label class="layui-form-label">所属模块</label>
                    <div class="layui-input-block">
                        <div class="j-search-con single-box">
                            <input type="text" id="module-name" value="" placeholder="请选择" readonly=""
                                   class="schoolSel">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul id="modules">

                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">接口类型</label>
                    <div class="layui-input-block">
                        <div class="j-search-con single-box">
                            <input type="text" id="type-name" value="未拆分版教务接口" placeholder="请选择" readonly=""
                                   class="schoolSel">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul id="type">
                                    <li data-type="1" class="active">未拆分版教务接口</li>
                                    <li data-type="2">拆分版教务接口</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">表单接口类型</label>
                    <div class="layui-input-block">
                        <div class="j-search-con single-box">
                            <input type="text" id="form-api-type-name" value="" placeholder="请选择" readonly=""
                                   class="schoolSel">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul id="form-api-type">
                                    <li data-type="1">字段数据从第三方获取</li>
                                    <li data-type="2">按钮字段(button)配置的URL</li>
                                    <li data-type="3">后台顶部按钮打开URL</li>
                                    <li data-type="4">后台右侧按钮打开URL</li>
                                    <li data-type="5">提交校验(第三方校验)</li>
                                    <li data-type="7">进入表单校验</li>
                                    <li data-type="8">前端事件</li>
                                    <li data-type="9">数据推送</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">接口所在的表单别名</label>
                    <div class="layui-input-block" style="width: 240px;">
                        <input type="text" id="form-alias" lay-verify="required" placeholder="请输入"
                               autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">接口状态</label>
                    <div class="layui-input-block">
                        <div class="j-search-con single-box">
                            <input type="text" id="state-name" value="上线" placeholder="请选择" readonly=""
                                   class="schoolSel">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul id="state">
                                    <li data-type="1" class="active">上线</li>
                                    <li data-type="2">下线</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-item">
                <div class="layui-form-item">
                    <label class="layui-form-label">URL </label>
                    <div class="layui-input-block" style="width: 100%;">
                        <input type="text" id="url" lay-verify="required" placeholder="请输入"
                               autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
            <div class="form-item">
                <div class="layui-form-item">
                    <label class="layui-form-label">备注 </label>
                    <div class="layui-input-block" style="width: 100%;">
                        <input type="text" id="remark" lay-verify="required" placeholder="请输入"
                               autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>

            <div class="form-item">
                <div class="layui-form-item replace-item old-value replace">
                    <label class="layui-form-label">旧值</label>
                    <div class="layui-input-block" style="width: 240px;">
                        <input type="text" id="old-value" lay-verify="required" placeholder=""
                               autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item replace-item new-value replace">
                    <label class="layui-form-label">新值</label>
                    <div class="layui-input-block" style="width: 240px;">
                        <input type="text" id="new-value" lay-verify="required" placeholder=""
                               autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
            <div class="form-item">
                <div class="layui-form-item replace-item replace">
                    <label class="layui-form-label" style="width: 100%;"><h3>从下表中选择接口以替换</h3></label>
                </div>
            </div>
            <div class="form-item url-list replace-item replace">
                <table class="layui-hide materialTable5" id="materialTable5" lay-filter="materialTable5">
                </table>
            </div>
        </div>
    </div>
    <div class="dialog-btn">
        <button class="pu-cancel">取消</button>
        <button class="pu-sure" id="scoreSure">确定</button>
    </div>
</div>
<!-- 接口详情查看 -->
<div id="selScoreCheck" class="dialog dialogCheck">
    <div class="dialog-title">
        <h3>接口详情</h3><span class="pu-close"></span>
    </div>
    <div class="dialog-con">

    </div>
</div>
</body>
<script th:src="${_CPR_}+'/js/apimanage/common.js'"></script>
<th:block th:include="common :: layui-js-2_9_13"/>
<script th:src="${_CPR_}+'/js/apimanage/api.manage.js'"></script>
<script th:src="${_CPR_}+'/js/apimanage/api.manage.event.js'"></script>
<script type="text/html" id="tmplToolBar">
    <div class="oprate-table">
        <span class="edit" lay-event="check">查看</span>
        <span class="edit" lay-event="edit">编辑</span>
        <span class="delete" lay-event="delete">删除</span>
    </div>
</script>


</html>