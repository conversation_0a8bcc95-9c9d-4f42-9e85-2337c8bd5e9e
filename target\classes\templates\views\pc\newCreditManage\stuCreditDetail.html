<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学分详情</title>
    <link rel="stylesheet" th:href="@{~/css/creditManage/global.css}">
    <link rel="stylesheet" th:href="@{~/plugin/layui/css/layui.css}">
    <link rel="stylesheet" th:href="@{~/css/creditManage/common.css}">
    <link rel="stylesheet" th:href="@{~/css/creditManage/reset.css}">
    <link rel="stylesheet" th:href="@{~/css/creditManage/index.css}">
    <link rel="stylesheet" th:href="@{~/css/creditManage/certificateScore.css}">
    <style>
        .main .con .c-item .lab {
            display: flex;
            height: 34px;
            margin-bottom: 24px;
        }

        .main .con .c-item .lab div {
            width: 250px;
            line-height: 34px;
            text-align: left;
            font-weight: 400;
            font-size: 14px;
            color: #1D2129;
        }
    </style>
    <script th:src="@{~/plugin/layui/layui.js}"></script>
</head>

<body>
<div class="main">
    <div class="top" th:if="${role==null||role!=3}">
        <div class="title">
            <div class="back">返回</div>
            <div class="levelone">按学生查询学分</div>
            <div class="icon"></div>
            <div class="leveltwo">按学期查询学分</div>
        </div>
    </div>

    <div class="con">
        <div class="c-item">
            <h3>学分详情</h3>
            <div class="lab">
                <div class="stuName">学生姓名：<span id="xsxm"></span></div>
                <div class="xh">学生学号：<span id="xsxh"></span></div>
                <div class="score">已修总学分：<span id="score"></span></div>
            </div>
            <div class="lab">
                <div class="name" style="width: 58px">学年学期</div>
                <div class="j-search-con single-box">
                    <input type="text" placeholder="请选择学年学期" class="schoolSel xnxq"
                           id="xnxq" autocomplete=off style="width:250px">
                    <span class="j-arrow"></span>
                    <div class="j-select-year">
                        <ul class="xnxqul"></ul>
                    </div>
                </div>
            </div>

        </div>
        <div class="c-item" id="cTime">
            <div class="c-table">
                <table class="layui-hide" id="creditDetail" lay-filter="creditDetail"></table>
            </div>
        </div>
    </div>
</div>
</body>
<script th:src="@{~/js/jquery-3.3.1.min.js}"></script>
<script th:src="@{~/js/creditManage/common.js}"></script>
<script th:src="@{~/js/my.util.js}"></script>
<script th:inline="javascript">
    var fid = [[${fid}]];
    var uid = [[${uid}]];

    layui.use(['jquery', 'table'], function () {
        var table = layui.table, $ = layui.jquery;

        $(function () {
            getXnxq();
            getBasicData()
        })

        function getXnxq() {
            $.ajax({
                type: 'get',
                dataType: 'json',
                url: '/basic/xnxq',
                data: {fid: fid},
                success: function (res) {
                    if (res.code == 200) {
                        var data = res.data;
                        var html = '';
                        var now = '';
                        for (let i = 0; i < data.length; i++) {
                            if (data[i].xnxq_sfdqxq == '是') {
                                now = data[i].xnxq_xnxqh;
                                html += "<li class='active'>" + data[i].xnxq_xnxqh + "</li>"
                            } else {
                                html += "<li>" + data[i].xnxq_xnxqh + "</li>"
                            }
                        }
                        $(".xnxq").val(now);
                        $(".xnxqul").html(html);
                        getCreditSet(now)
                    }
                }
            })
        }

        function getBasicData() {
            $.ajax({
                type: 'get',
                dataType: 'json',
                url: '/new/credit/search/getCreditSummary',
                data: {fid: fid, uid: uid},
                success: function (res) {
                    if (res.code == 200) {
                        $("#xsxm").html(res.data.name)
                        $("#xsxh").html(res.data.xgh)
                        $("#score").html(res.data.score)
                    } else {
                        U.fail(res.msg)
                    }
                }
            })
        }

        var cols =
            [{
                field: 'xnxq',
                title: '学年学期',
                align: "center",
            }, {
                field: 'nj',
                title: '年级',
                align: "center",
            }, {
                field: 'yx',
                title: '院系',
                align: "center",
            }, {
                field: 'zy',
                title: '专业',
                align: "center",
            }, {
                field: 'bj',
                title: '班级',
                align: "center",
            }, {
                field: 'bzrlxr',
                title: '班主任姓名',
                align: "center",
            }
            ]

        var xkxf = {
            field: 'xkxf',
            title: '学科学分',
            align: "center",
        }
        var lastCol = {
            title: '学期总学分',
            align: "center",
            templet: function (data) {
                let sum = 0;
                sum += parseFloat(data.kcxf);
                sum += parseFloat(data.jsxf);
                sum += parseFloat(data.zsxf);
                sum += parseFloat(data.sxxf);
                sum += parseFloat(data.qtxf);
                sum += parseFloat(data.dyxf);
                return sum.toFixed(2);
            }
        }
        var lastCol2 = {
            title: '学期实际学分',
            align: "center",
            templet: function (data) {
                let sum = 0;
                sum += parseFloat(data.xkxf);
                sum += parseFloat(data.jsxf);
                sum += parseFloat(data.zsxf);
                sum += parseFloat(data.sxxf);
                sum += parseFloat(data.qtxf);
                sum += parseFloat(data.dyxf);
                return sum.toFixed(2);
            }
        }
        $("ul.xnxqul").on("click", "li", function () {
            getCreditSet($(this).html());
        })

        var tempCols = [];

        function getCreditSet(xnxq) {
            $.get("/new/creditRuleSet/getRule", {fid: fid, xnxq: xnxq}, function (res) {
                if (res.code == 200) {
                    tempCols = cols.slice(0);
                    if (fid==389){
                        tempCols.push(xkxf);
                    }
                    var data = res.data;
                    var groupData = [];
                    var flag=true;
                    for (let i = 0; i < data.length; i++) {
                        if (data[i].status == 0) {
                            continue;
                        }

                        if (data[i].groupName == null || data[i].groupName == '') {
                            let field = '';
                            switch (data[i].creditType) {
                                case "证书类学分":
                                    field = "zsxf";
                                    break;
                                case "竞赛类学分":
                                    field = "jsxf";
                                    break;
                                case "实习类学分":
                                    field = "sxxf";
                                    break;
                                case "其他类学分":
                                    field = "qtxf";
                                    break;
                                case "课程类学分":
                                    field = "kcxf";
                                    break;
                                case "德育类学分":
                                    field = "dyxf";
                                    break;
                                default:
                                    break;
                            }
                            let title=""
                            if (data[i].creditType=="其他类学分"){
                                if (flag){
                                    title="其他类学分";
                                    flag=false;
                                }else {
                                    continue
                                }
                            }else {
                                title= data[i].creditName;
                            }
                            let json = {
                                field: field,
                                title:title,
                                align: "center",
                            }

                            tempCols.push(json);
                        } else {
                            if (groupData.length == 0) {
                                let tempData = [data[i]];
                                groupData.push(tempData);
                            } else {
                                let save = false;
                                for (let j = 0; j < groupData.length; j++) {
                                    if (groupData[j][0].groupId == data[i].groupId) {
                                        groupData[j].push(data[i]);
                                        save = true;
                                        break;
                                    }
                                }
                                if (!save) {
                                    let tempData = [data[i]];
                                    groupData.push(tempData);
                                }

                            }
                        }
                    }
                    if (groupData.length > 0) {
                        for (let i = 0; i < groupData.length; i++) {
                            let json = {
                                title: groupData[i][0].groupName,
                                align: "center",
                                templet: function (data) {
                                    let sum = 0;
                                    for (let j = 0; j < groupData[i].length; j++) {
                                        if (groupData[i][j].status == 0) {
                                            continue;
                                        }
                                        switch (groupData[i][j].creditType) {
                                            case "证书类学分":
                                                sum += parseFloat(data.zsxf);
                                                break;
                                            case "竞赛类学分":
                                                sum += parseFloat(data.jsxf);
                                                break;
                                            case "实习类学分":
                                                sum += parseFloat(data.sxxf);
                                                break;
                                            case "其他类学分":
                                                sum += parseFloat(data.qtxf);
                                                break;
                                            case "课程类学分":
                                                sum += parseFloat(data.kcxf);
                                                break;
                                            case "德育类学分":
                                                sum += parseFloat(data.dyxf);
                                                break;
                                            default:
                                                break;
                                        }
                                    }
                                    return sum.toFixed(2);
                                }
                            }
                            tempCols.push(json);
                        }
                    }
                    tempCols.push(lastCol);
                    if (fid == 389) {
                        tempCols.push(lastCol2);
                    }
                    updateTable(xnxq);
                } else {
                    U.fail("获取学分规则失败")
                }
            })
        }

        function updateTable(xnxq) {
            table.render({
                elem: '#creditDetail',
                url: '/new/credit/search/creditDetail',
                where: {fid: fid, uid: uid, xnxq: xnxq},
                parseData: function (res) {
                    if (res.code == 200) {
                        return {
                            "code": 0, //解析接口状态
                            "msg": res.msg, //解析提示文本
                            "count": res.data.length, //解析数据长度
                            "data": res.data //解析数据列表
                        }
                    } else {
                        return {
                            "code": 1, //解析接口状态
                            "msg": "无数据", //解析提示文本
                            "count": 0, //解析数据长度
                            "data": [] //解析数据列表
                        }
                    }
                },
                cols: [tempCols],
            });
        }

    });
    $(".back").click(function () {
        history.back();
    })
</script>

</html>