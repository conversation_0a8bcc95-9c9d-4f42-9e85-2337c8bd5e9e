<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1, user-scalable=no" />
	<meta name="format-detection" content="telephone=no,email=no,adress=no">
	<meta http-equiv="pragma" content="no-cache" />
	<meta http-equiv="cache-control" content="no-cache" />
	<meta http-equiv="expires" content="0" />
	<title>筛选可选课程</title>

	<link rel="stylesheet" th:href="${_CPR_}+'/elective/css/global.css'" />
	<link rel="stylesheet" th:href="${_CPR_}+'/elective/css/range.css'" />
	<script th:src="${_CPR_}+'/elective/js/responsive-1.0.js'"></script>

</head>

<body>
<div class="head-signal" style="width: 100%;"></div>
	<div class="tophead">
		<div class="head bottomLine">
			<div class="leftHead">
				<div class="back"></div>
			</div>
			<div class="centerHead">
				<div class="selectBox">
					<div class="selectWeek"><span class="week" week="1">筛选可选课程</span></div>
				</div>
			</div>

		</div>
	</div>
	<div class="list hislocationList">
		<div class="listItem ycenter">
			<div class="itemCon">
				<h1>所属选课计划</h1>
			</div>
			<div class="itemRight">
				<p class="arrowRight ycenter" data-id="0" id="selectPlan" style="height: 100%;"><span id="jhVal1">请选择</span><img
						th:src="${_CPR_}+'/elective/images/arrowgrayRight.png'" /></p>
			</div>
		</div>
		<div class="listItem ycenter">
			<div class="itemCon">
				<h1>开课年级</h1>
			</div>
			<div class="itemRight">
				<p class="arrowRight ycenter" data-id="1" id="selectGrade" style="height: 100%;"><span id="njVal1">请选择</span><img
						th:src="${_CPR_}+'/elective/images/arrowgrayRight.png'"  /></p>
			</div>
		</div>
		<div class="listItem ycenter">
			<div class="itemCon">
				<h1>开课院系</h1>
			</div>
			<div class="itemRight">
				<p class="arrowRight ycenter" data-id="2" id="selectDepart" style="height: 100%;"><span id="yxVal1">请选择</span><img
						th:src="${_CPR_}+'/elective/images/arrowgrayRight.png'" /></p>
			</div>
		</div>

		<div class="listItem ycenter">
			<div class="itemCon">
				<h1>开课专业</h1>
			</div>
			<div class="itemRight">
				<p class="arrowRight ycenter" data-id="3"  style="height: 100%;" id="selectMajor">
					<span id="zyVal1">请选择</span><img th:src="${_CPR_}+'/elective/images/arrowgrayRight.png'" />
				</p>
			</div>
		</div>
		<div class="listItem ycenter">
			<div class="itemCon">
				<h1>开设课程</h1>
			</div>
			<div class="itemRight">
				<p class="arrowRight ycenter" data-id="4" style="height: 100%;" id="selectCourse"><span id="kcVal1">请选择</span> <img
						th:src="${_CPR_}+'/elective/images/arrowgrayRight.png'" /></p>
			</div>
		</div>
		<div class="listItem ycenter">
			<div class="itemCon">
				<h1>开课校区</h1>
			</div>
			<div class="itemRight">
				<p class="arrowRight ycenter" data-id="5" style="height: 100%;" id="selectSqchool"><span id="xqVal1">请选择</span> <img
						th:src="${_CPR_}+'/elective/images/arrowgrayRight.png'" /></p>
			</div>
		</div>
		<div class="listItem ycenter">
			<div class="itemCon">
				<h1>课程类型</h1>
			</div>
			<div class="itemRight">
				<p class="arrowRight ycenter" data-id="6" style="height: 100%;" id="selectType"><span id="lxVal1">请选择</span> <img
						th:src="${_CPR_}+'/elective/images/arrowgrayRight.png'" /></p>
			</div>
		</div>
	</div>

	<div class="recall-bottom">
		<div class="refresh">重置</div>
		<div class="search">查询</div>
	</div>
<!-- 选择选课计划 -->
<div class="choosedepartment-dialog" data-id="0" id="chooseteaPlan">
	<div class="w-con">
		<div class="w-head">
			<div class="cancle">取消</div>
			<div class="name">所属选课计划</div>
			<div class="btns">

				<span class="save">保存</span>
			</div>
		</div>
		<div class="w-box">
			<div class="search-inputs">
				<div class="s-con">
					<img th:src="${_CPR_}+'/elective/images/search-icon1.png'" alt="">
					<input type="text" placeholder="请输入" class="mSearch">
					<span class="m-close" style="display: none;"></span>
				</div>
			</div>
			<ul id="jhDiv1">
				<li class="all">全部</li>
			</ul>
		</div>

	</div>
</div>
	<!-- 选择年级 -->
	<div class="choosedepartment-dialog" data-id="1" id="chooseteaGrade">
		<div class="w-con">
			<div class="w-head">
				<div class="cancle">取消</div>
				<div class="name">开课年级</div>
				<div class="btns">

					<span class="save">保存</span>
				</div>
			</div>
			<div class="w-box">
				<div class="search-inputs">
					<div class="s-con">
						<img th:src="${_CPR_}+'/elective/images/search-icon1.png'" alt="">
						<input type="text" placeholder="请输入" class="mSearch" >
						<span class="m-close" style="display: none;"></span>
					</div>
				</div>
				<ul id="njDiv1">
					<li class="all">全部</li>
				</ul>
			</div>

		</div>
	</div>
	<!-- 开课院系 -->
	<div class="choosedepartment-dialog" data-id="2" id="choosedepartment">
		<div class="w-con">
			<div class="w-head">
				<div class="cancle">取消</div>
				<div class="name">开课院系</div>
				<div class="btns">

					<span class="save">保存</span>
				</div>
			</div>
			<div class="w-box">
				<div class="search-inputs">
					<div class="s-con">
						<img th:src="${_CPR_}+'/elective/images/search-icon1.png'" alt="">
						<input type="text" placeholder="请输入" class="mSearch" >
						<span class="m-close" style="display: none;"></span>
					</div>
				</div>
				<ul id="yxDiv1">
					<li class="all">全部</li>
				</ul>
			</div>

		</div>
	</div>

	<!-- 选择专业 -->
	<div class="choosedepartment-dialog" data-id="3" id="chooseMajor">
		<div class="w-con">
			<div class="w-head">
				<div class="cancle">取消</div>
				<div class="name">开课专业</div>
				<div class="btns">

					<span class="save">保存</span>
				</div>
			</div>
			<div class="w-box">
				<div class="search-inputs">
					<div class="s-con">
						<img th:src="${_CPR_}+'/elective/images/search-icon1.png'" alt="">
						<input type="text" placeholder="请输入" class="mSearch" >
						<span class="m-close" style="display: none;"></span>
					</div>
				</div>
				<ul id="zyDiv1">
					<li class="all" >全部</li>
				</ul>
			</div>

		</div>
	</div>
	<!-- 选择课程 -->
	<div class="choosedepartment-dialog" data-id="4" id="chooseCourse">
		<div class="w-con">
			<div class="w-head">
				<div class="cancle">取消</div>
				<div class="name">开设课程</div>
				<div class="btns">

					<span class="save">保存</span>
				</div>
			</div>
			<div class="w-box">
				<div class="search-inputs">
					<div class="s-con">
						<img th:src="${_CPR_}+'/elective/images/search-icon1.png'" alt="">
						<input type="text" placeholder="请输入" class="mSearch" >
						<span class="m-close" style="display: none;"></span>
					</div>
				</div>
				<ul id="kcDiv1">
					<li class="all">全部</li>
				</ul>
			</div>

		</div>
	</div>
	<!-- 选择校区 -->
	<div class="choosedepartment-dialog" data-id="5" id="chooseSchoole">
		<div class="w-con">
			<div class="w-head">
				<div class="cancle">取消</div>
				<div class="name">开课校区</div>
				<div class="btns">

					<span class="save">保存</span>
				</div>
			</div>
			<div class="w-box">
				<div class="search-inputs">
					<div class="s-con">
						<img th:src="${_CPR_}+'/elective/images/search-icon1.png'" alt="">
						<input type="text" placeholder="请输入" class="mSearch" >
						<span class="m-close" style="display: none;"></span>
					</div>
				</div>
				<ul id="xqDiv1">
					<li class="all">全部</li>
				</ul>
			</div>

		</div>
	</div>
	<!-- 选择类类型 -->
	<div class="choosedepartment-dialog" data-id="6" id="chooseSchoole">
		<div class="w-con">
			<div class="w-head">
				<div class="cancle">取消</div>
				<div class="name">课程类型</div>
				<div class="btns">
					<span class="save">保存</span>
				</div>
			</div>
			<div class="w-box">
				<div class="search-inputs">
					<div class="s-con">
						<img th:src="${_CPR_}+'/elective/images/search-icon1.png'" alt="">
						<input type="text" placeholder="请输入" class="mSearch" >
						<span class="m-close" style="display: none;"></span>
					</div>
				</div>
				<ul id="lxDiv1">
					<li class="all">全部</li>
				</ul>
			</div>

		</div>
	</div>
	<script th:inline="javascript">
		const _VR_ = [[${_VR_}]] || '';
		var fid = [[${fid}]] || '';
	</script>
	<script th:src="${_CPR_}+'/elective/js/jquery-3.3.1.min.js'"></script>
<script th:src="${_CPR_}+'/elective/js/mobileVersion.js?a=1'"></script>
	<script type="text/javascript" src="https://kb.chaoxing.com/res/app/curriculum/js/CXJSBridge.js"></script>
	<script type="text/javascript" src="https://kb.chaoxing.com/res/app/curriculum/js/app.utils.js"></script>
	<script type="text/javascript" src="https://kb.chaoxing.com/res/app/curriculum/js/tools.js"></script>
	<script th:src="${_CPR_}+'/js/my.util.js'"></script>
<!--	<script src="https://cdn.bootcss.com/vConsole/3.2.0/vconsole.min.js"></script>-->


	<script>

		function _jsBridgeReady() {
			jsBridge.postNotification('CLIENT_TOOLBAR_TYPE', {
				'toolbarType': 0
			});
		}
		// var vConsole = new VConsole();
		$(".leftHead").on('click',function () {
			AppUtils.closeView();
		})

		addIphoneHeight();

		function addIphoneHeight() {
			var isIphone = /iphone/gi.test(navigator.userAgent);
			if (isIphone && (screen.height == 812 && screen.width == 375)) {
				$('body').addClass('iosxwrapMax');
			} else if (isIphone && (screen.width == 414)) {
				$('body').addClass('iospluswrapMax');

			} else if (isIphone) {
				$('body').addClass('ioswrapMax');
			}
		}


		var selEle;
		$(".ycenter").click(function () {
			selEle = $(this).find('span');
			var id = $(this).attr('data-id');
			var dialog = $(".choosedepartment-dialog[data-id=" + id + "]");
			dialog.show();
			dialog.find('.w-con').addClass("active");
		})

		// 检索
		$(".mSearch").on('input', function () {
			let val = $(this).val();
			var nextEle = $(this).parents('.search-inputs').next("ul");
			if (val != "") {
				nextEle.find("li").each(function (i, ele) {
					let txt = $(ele).text();
					if (txt.indexOf(val) >= 0) {
						$(ele).show();
					} else {
						$(ele).hide();
					}
				});
				nextEle.find("li.all").hide();
			} else {
				nextEle.find("li").show();
			}
		})
		// 选择
		$(".choosedepartment-dialog").on("click", ".w-con .w-box ul li", function () {
			$(this).toggleClass("cur");
			if ($(this).hasClass('all')) {
				// 全选
				if ($(this).hasClass('cur')) {
					$(this).nextAll().addClass('cur');
				} else {
					$(this).nextAll().removeClass('cur');
				}
			}
			let totalNus = $(this).parents(".choosedepartment-dialog").find(".w-con .w-box ul li").length - 1;
			let currentNus = $(this).parents(".choosedepartment-dialog").find(".w-con .w-box ul li.cur:not(.all)").length;
			if (totalNus == currentNus) {
				$(this).siblings('.all').addClass('cur');
			} else {
				$(this).siblings('.all').removeClass('cur');
			}

		})


		//取消
		$(".choosedepartment-dialog .w-con .w-head .cancle").click(function () {
			$(".choosedepartment-dialog").hide();
			$(".choosedepartment-dialog .w-con").removeClass("active");
		})

		//保存
		$(".choosedepartment-dialog .w-con .w-head .btns .save").click(function () {
			let cStr = [];
			let p = $(this).parents('.choosedepartment-dialog');
			var cur = p.find('li.cur');
			if (cur.length > 0) {
				if (p.find('li.all.cur').length > 0) {
					cStr.push('全部')
				} else {
					cur.each(function () {
						cStr.push($(this).text())
					})
				}

				selEle.text(cStr.join(',')).addClass("color1");
			} else {
				selEle.text('请选择').removeClass('color1');
			}
			p.hide();
			$("#choosedepartment .w-con").removeClass("active");

		})

		//重置
		$(".recall-bottom .refresh").click(function () {
			$(".ycenter span").text('请选择').removeClass();
			$('.choosedepartment-dialog li.cur').removeClass('cur');
		})

		U.ajax({
			type: "POST",
			url: "../elective/getNj",
			data: {fid: fid},
			dataType: 'json',
			success: function (result) {
				var html = "";
				if (result) {
					for (var i = 0; i < result.data.length; i++) {
						var name = result.data[i].name;
						var id = result.data[i].id;
						html += "<li data-id=\"" + id + "\">" + name + "</li>";
					}

				}
				$("#njDiv1").append(html);
			}
		});

		U.ajax({
			type: "POST",
			url: "../elective/getYX",
			data: {fid: fid},
			dataType: 'json',
			success: function (result) {
				var html = "";
				if (result) {
					for (var i = 0; i < result.data.length; i++) {
						var name = result.data[i].name;
						var id = result.data[i].id;
						html += "<li data-id=\"" + id + "\">" + name + "</li>";
					}

				}
				$("#yxDiv1").append(html);
			}
		});

		U.ajax({
			type: "POST",
			url: "../elective/getZY",
			data: {fid: fid},
			dataType: 'json',
			success: function (result) {
				var html = "";
				if (result) {
					for (var i = 0; i < result.data.length; i++) {
						var name = result.data[i].name;
						var id = result.data[i].id;
						html += "<li data-id=\"" + id + "\">" + name + "</li>";
					}

				}
				$("#zyDiv1").append(html);
			}
		});

		U.ajax({
			type: "POST",
			url: "../elective/getTaskKC",
			data: {fid: fid},
			dataType: 'json',
			success: function (result) {
				var html = "";
				if (result) {
					for (var i = 0; i < result.data.length; i++) {
						var name = result.data[i].name;
						var id = result.data[i].id;
						html += "<li data-id=\"" + id + "\">" + name + "</li>";
					}

				}
				$("#kcDiv1").append(html);
			}
		});

		U.ajax({
			type: "POST",
			url: "../elective/getXQ",
			data: {fid: fid},
			dataType: 'json',
			success: function (result) {
				var html = "";
				if (result) {
					for (var i = 0; i < result.data.length; i++) {
						var name = result.data[i].name;
						var id = result.data[i].id;
						html += "<li data-id=\"" + id + "\">" + name + "</li>";
					}

				}
				$("#xqDiv1").append(html);
			}
		});

		U.ajax({
			type: "POST",
			url: "../elective/getLX",
			data: {fid: fid},
			dataType: 'json',
			success: function (result) {
				var html = "";
				if (result) {
					for (var i = 0; i < result.data.length; i++) {
						var name = result.data[i].name;
						var id = result.data[i].id;
						html += "<li data-id=\"" + id + "\">" + name + "</li>";
					}

				}
				$("#lxDiv1").append(html);
			}
		});

		U.ajax({
			type: 'post',
			url: "../elective/task/getTask",
			data: {fid: fid},
			dataType: 'json',
			async: false,
			success: function (result) {
				var sxhtml = "";//筛选计划
				for (let i = 0; i < result.data.length; i++) {
					sxhtml += "<li data-id=\"" + result.data[i].taskBdid + "\">" + result.data[i].xkjhbJhmc + "</li>";
				}
				$("#jhDiv1").append(sxhtml);
			}
		})

		$(".search").on("click", function () {
			var para = {xxk_kknj:$("#njVal1").text().replace('请选择','')
				,xxk_kkyx:$("#yxVal1").text().replace('请选择','')
				,xxk_kkzy:$("#zyVal1").text().replace('请选择','')
				,kskc:$("#kcVal1").text().replace('请选择','')
				,xxk_kkxiaoqu:$("#xqVal1").text().replace('请选择','')
				,xxk_kclx:$("#lxVal1").text().replace('请选择','')
				,jh:$("#jhVal1").text().replace('请选择','')
			};
			jsBridge.postNotification('CLIENT_WEB_EXTRAINFO',para);
			AppUtils.closeView();
		})

	</script>
</body>

</html>