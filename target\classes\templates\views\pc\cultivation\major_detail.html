<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主修专业任务落实</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/global.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/plugin/layui-v2.9.13/layui/css/layui.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/slideCommon.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/layuiReset.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/majorTask.css'}">
    <script th:src="${_CPR_+_VR_+'/plugin/layui-v2.9.13/layui/layui.js'}"></script>

    <style>
        .j-search-con {
            width: 240px;
        }
    </style>
</head>

<body>
<div class="main">
    <div class="m-top">
        <div class="title">主修专业任务落实</div>
    </div>
    <div class="item">
        <div class="i-top" style="margin-top: 24px;">
            <h3>任务学年学期<span th:text="${term}"></span></h3>
        </div>
        <div class="search-con" style="margin: 0 0 8px 0;">
            <form>
                <div class="layui-inline">
                    <label class="layui-form-label" style="width: 56px;">课程名称</label>
                    <div class="layui-input-inline">
                        <div class="j-search-con multiple-box">
                            <input type="text" name="courseName" placeholder="请选择" readonly="" class="schoolSel">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul id="courseList">
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label mu" style="width: 56px;">开课学院</label>
                    <div class="layui-input-inline">
                        <div class="j-search-con multiple-box">
                            <input type="text" name="college" placeholder="请选择" readonly="" class="schoolSel">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul id="deptList">
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                    <button type="submit" class="layui-btn " lay-filter="form-search" id="searchAllCourseBtn">查询
                    </button>
                </div>
            </form>
        </div>
    </div>
    <div class="item">
        <div class="i-top" style="margin: 16px 0 8px">
            <h3>任务学期需落实课程</h3>
        </div>
        <ul class="tab-nav">
            <li class="active">未落实</li>
            <li>部分落实</li>
            <li>已落实</li>
        </ul>
        <div class="tab-box">
            <div class="table-box1">
                <table class="layui-hide" id="noImplemented" lay-filter="noImplemented"></table>

            </div>
            <div class="table-box1">
                <table class="layui-hide" id="partImplemented" lay-filter="noImplemented"></table>

            </div>
            <div class="table-box1">
                <table class="layui-hide" id="implemented" lay-filter="noImplemented"></table>

            </div>
        </div>
    </div>
    <div class="item" id="courseDetail" style="display: none">
        <div class="i-top" style="margin-top: 32px">
            <h3>课程落实详情</h3>
        </div>
        <div class="search-con" style="margin: 0 0 8px 0;">
            <form>
                <div class="layui-inline">
                    <label class="layui-form-label mu" style="width: 56px;">年级</label>
                    <div class="layui-input-inline">
                        <div class="j-search-con multiple-box">
                            <input type="text" name="grade" placeholder="请选择" readonly="" class="schoolSel">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul id="gradeList">

                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label mu" style="width: 56px;">学生专业</label>
                    <div class="layui-input-inline">
                        <div class="j-search-con multiple-box">
                            <input type="text" name="major" placeholder="请选择" readonly="" class="schoolSel">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul id="majorList">

                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-inline">
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                    <button type="submit" class="layui-btn " lay-filter="form-search" id="searchCourseBtn">查询</button>
                </div>
            </form>
        </div>
        <div class="table-box" style="margin: 0;">
            <table class="layui-hide" id="implementedDetail" lay-filter="implementedDetail"></table>
        </div>
    </div>

</div>
</body>
<!-- 删除 -->
<script th:src="@{~/js/jquery-3.3.1.min.js}"></script>
<script th:src="${_CPR_+_VR_+'/js/my.util.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/cultivation/slideCommon.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/cultivation/majorTask.js'}"></script>
<script type="text/html" id="courseSetToolBar">
    {{# if(d.noIm > 0) { }}
    <div class="opt-btn" lay-event="set">开课设置</div>
    {{# } else{ }}
    <div class="opt-btn">--</div>
    {{# } }}
</script>
<script th:inline="javascript">
    const _VR_ = [[${_VR_}]]
    var fid = [[${fid}]]
    var uid = [[${uid}]]
    var term = [[${term}]]
    $(function () {
        $.get("/cultivation/implement/selectData", {term: term}, function (res) {
            var data = res.data;
            var courseList = data.courseList;
            var deptList = data.deptList;
            var gradeList = data.gradeList;
            var majorList = data.majorList;

            if (courseList != undefined&&courseList!="") {
                var courseHtml = "";
                courseList.forEach(item => {
                    courseHtml += `<li value="` + item + `">` + item + `</li>`
                })
                $("#courseList").html(courseHtml)
            }
            if (deptList !=  undefined&&deptList!="") {
                var deptHtml = "";
                deptList.forEach(item => {
                    deptHtml += `<li value="` + item + `">` + item + `</li>`
                })
                $("#deptList").html(deptHtml)
            }

            if (gradeList !=  undefined&&gradeList!="") {
                var gradeHtml = "";
                gradeList.forEach(item => {
                    gradeHtml += `<li value="` + item + `">` + item + `</li>`
                })
                $("#gradeList").html(gradeHtml)
            }
            if (majorList !=  undefined&&majorList!="") {
                var majorHtml = "";
                majorList.forEach(item => {
                    majorHtml += `<li value="` + item + `">` + item + `</li>`
                })
                $("#majorList").html(majorHtml)
            }

        })
    })

</script>


</html>