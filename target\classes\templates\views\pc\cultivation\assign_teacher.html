<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>指定任课教师</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/global.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/plugin/layui/css/layui.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/reset.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/common.css'}"/>
    <link rel="stylesheet"
          th:href="@{${_CPR_+_VR_}+'/css/cultivation/teacher2.0.css'(v=${new java.util.Date().getTime()})}">
    <link rel="stylesheet"
          th:href="@{${_CPR_+_VR_}+'/css/cultivation/teacherPop.css'(v=${new java.util.Date().getTime()})}"/>
    <script th:src="${_CPR_+_VR_+'/plugin/layui/layui.js'}"></script>
    <style>
        body .layui-layer-btn .layui-layer-btn1 {
            color: #fff;
            background: #4d88ff;
            box-shadow: 0px 0px 8px rgba(77, 136, 255, 0.3);
            border: 1px solid #4d88ff;
            padding: 0 30px;
            height: 36px;
            font-size: 14px;
            line-height: 34px;
            border-radius: 18px;
            width: 40px !important;
        }

        body .layui-layer-btn .layui-layer-btn0 {
            border: 1px solid #E5E6EB;
            padding: 0 30px;
            height: 36px;
            font-size: 14px;
            border-radius: 18px;
            color: #4E5969;
            margin-right: 16px;
            line-height: 34px;
            background-color: #fff;
            width: 40px !important;
        }

        .layui-layer-btn {
            border-top: 1px solid #E5E6EB;
            text-align: center !important;
            display: flex;
            align-items: center;
            justify-content: flex-end;
        }

        .layui-table-view .layui-table tr th span i {
            color: red;
            padding-right: 3px;
        }

        .sel-item {
            margin-bottom: 20px; /* 统一垂直间距 */
        }

        .sel-title {
            font-size: 14px;
            color: #333;
            margin-bottom: 8px;
        }

    </style>
</head>
<body>
<div class="main">
    <div class="wrap-item">
        <div class="item">
            <div class="i-top" style="padding-top: 16px;">
                <h3>开课信息</h3>
                <span class="arrow slide"></span>
            </div>
            <div class="i-con">
                <div class="course-inform">
                    <ul>
                        <li>
                            <div class="name">开课学期：</div>
                            <div class="tit" th:text="${courseManageForm?.kkgl_kkxq}"
                                 th:title="${courseManageForm?.kkgl_kkxq}"></div>
                        </li>
                        <li>
                            <div class="name">开课校区：</div>
                            <div class="tit" th:text="${courseManageForm?.kkgl_kkxqxq}"
                                 th:title="${courseManageForm?.kkgl_kkxqxq}"></div>
                        </li>
                        <li>
                            <div class="name">年级：</div>
                            <div class="tit" th:text="${courseManageForm?.kkgl_nj}"
                                 th:title="${courseManageForm?.kkgl_nj}"></div>
                        </li>
                        <li>
                            <div class="name">专业名称：</div>
                            <div class="tit" th:text="${courseManageForm?.kkgl_zymc}"
                                 th:title="${courseManageForm?.kkgl_zymc}"></div>
                        </li>
                        <li>
                            <div class="name">专业编号：</div>
                            <div class="tit" th:text="${courseManageForm?.kkgl_zybh}"
                                 th:title="${courseManageForm?.kkgl_zybh}"></div>
                        </li>
                        <li>
                            <div class="name">教学班组成：</div>
                            <div class="tit" th:text="${#strings.listJoin(courseManageForm?.kkgl_jxbzc, ',')}"
                                 th:title="${#strings.listJoin(courseManageForm?.kkgl_jxbzc, ',')}"></div>
                        </li>
                        <li>
                            <div class="name">课程名称：</div>
                            <div class="tit" th:text="${courseManageForm?.kkgl_kcmc}"
                                 th:title="${courseManageForm?.kkgl_kcmc}"></div>
                        </li>
                        <li>
                            <div class="name">课程编号：</div>
                            <div class="tit" th:text="${courseManageForm?.kkgl_kcbh}"
                                 th:title="${courseManageForm?.kkgl_kcbh}"></div>
                        </li>
                        <li>
                            <div class="name">课程性质：</div>
                            <div class="tit" th:text="${courseManageForm?.kkgl_kcxz}"
                                 th:title="${courseManageForm?.kkgl_kcxz}"></div>
                        </li>
                        <li>
                            <div class="name">学分：</div>
                            <div class="tit" th:text="${courseManageForm?.kkgl_xf}"
                                 th:title="${courseManageForm?.kkgl_xf}"></div>
                        </li>
                        <li>
                            <div class="name">总学时：</div>
                            <div class="tit" th:text="${courseManageForm?.kkgl_zongxs}"
                                 th:title="${courseManageForm?.kkgl_zongxs}"></div>
                        </li>
                        <li class="textbook">
                            <div class="name">选用教材：</div>
                            <div class="tit "></div>
                            <div id="textbookTips">
                                <div class="textbook-con" th:each="material:${courseManageForm?.kkgl_jc}">
                                    <div class="textbook-item">
                                        <h5>教材名称：</h5>
                                        <p th:text="${material.kkgl_jcmc}"></p>
                                    </div>
                                    <div class="textbook-item">
                                        <h5>教材号：</h5>
                                        <p th:text="${material.kkgl_jch}"></p>
                                    </div>
                                    <div class="textbook-item">
                                        <h5>ISBN：</h5>
                                        <p th:text="${material.kkgl_isbn}"></p>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <!--<li>
                            <div class="name">周次：</div>
                            <div class="tit" th:unless="${#lists.isEmpty(courseManageForm?.kksz)}"
                                 th:text="${courseManageForm?.kksz.get(0).kkgl_zc}"></div>
                        </li>
                        <li>
                            <div class="name">周课时：</div>
                            <div class="tit" th:unless="${#lists.isEmpty(courseManageForm?.kksz)}"
                                 th:text="${courseManageForm?.kksz.get(0).kkgl_zks}"></div>
                        </li>-->
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <div class="item item-teacher">
        <div class="i-top">
        </div>
        <div class="i-search" style="justify-content: flex-end;">
            <div class="i-add" id="iAdd"><span>+</span>添加</div>
        </div>
        <div class="i-table">
            <table class="layui-table" id="classStartsTeacher" lay-filter="classStartsTeacher">
            </table>
        </div>
    </div>
    <div class="item item-teacher">
        <div class="i-top" style="margin: 56px 0 46px;justify-content: flex-end;">
            <button class="i-submit">提交</button>
        </div>
    </div>
    <div id="editPoups" class="popup" style="display: none;width: 840px;">
        <div class="title">
            <div class="name">指定授课教师</div>
        </div>
        <div class="popup-con">
            <div class="illustrate">
                <ul>
                    <li class="cur">使用说明：不勾选数据直接点击 [确定] 提交，即可清空已有授课教师。</li>
                </ul>
            </div>
            <div class="content">
                <div class="oprate">
                    <div class="invisible-switch">
                        <div class="name">是否同步修改成绩录入教师</div>
                        <div class="switc-con">
                            <div class="switch switch-open"><span></span></div>
                        </div>
                    </div>
                </div>
                <div class="screen-box">
                    <div class="sel-list">
                        <div class="sel-item">
                            <div class="sel-title">教师编号</div>
                            <div class="sel" style="margin-right:0;">
                                <div class="select-input">
                                    <input type="text" placeholder="请输入" id="jsbh" name="jsbh">
                                </div>
                            </div>
                        </div>
                        <div class="sel-item">
                            <div class="sel-title">教师姓名</div>
                            <div class="sel" style="margin-right:0;">
                                <div class="select-input">
                                    <input type="text" placeholder="请输入" id="xm" name="xm">
                                </div>
                            </div>
                        </div>
                        <div class="sel-item">
                            <div class="sel-title">系部</div>
                            <div class="sel" style="margin-right:0;">
                                <div class="select-input">
                                    <div class="name" data-name="请选择" formAlias = "yxsj" fieldAlias = "yxsj_yxmc">请选择</div>
                                    <i></i>
                                    <div class="select-dropdown">
                                        <div class="search">
                                            <input type="text" placeholder="搜索">
                                            <span></span>
                                        </div>
                                        <div class="all-selects">
                                            全选
                                        </div>
                                        <ul class="dropdown-lists" name="xb">
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="sel-item">
                            <div class="sel-title">教研组</div>
                            <div class="sel" style="margin-right:0;">
                                <div class="select-input">
                                    <div class="name" data-name="请选择" formAlias = "jyssj" fieldAlias = "jyssj_jysmc">请选择</div>
                                    <i></i>
                                    <div class="select-dropdown">
                                        <div class="search">
                                            <input type="text" placeholder="搜索">
                                            <span></span>
                                        </div>
                                        <div class="all-selects">
                                            全选
                                        </div>
                                        <ul class="dropdown-lists" name="jys">
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="btns" style="margin-bottom: 20px;">
                            <div class="reset">重置</div>
                            <div class="screen">筛选</div>
                        </div>
                    </div>
                </div>
                <div class="p-table">
                    <table id="teacherTable" lay-filter="teacherTable"></table>
                    <div class="fixed-bottom">
                        <div class="selectAll">
                            <span>选中所有数据</span>
                        </div>
                        <div class="selected">已选中 <i>0</i> 条</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
<!-- 教师姓名 -->
<script type="text/html" id="tmplName">
    <div class="tmplOpt">
        <h5 class="teacherName">{{d.teacherName}}</h5>
        <span class="set teacherName">选择</span>
    </div>
</script>
<!-- 成绩教师姓名 -->
<script type="text/html" id="tmplScoreName">
    <div class="tmplOpt">
        <h5 class="teacherName" no="{{d.scoreTeacherUid}}">{{d.scoreTeacher}}</h5>
        <span class="set scoreTeacher">选择</span>
    </div>
</script>
<!-- 周次 -->
<script type="text/html" id="tmplScoreEntry">
    <input type="radio" class="sfScoreTeacher" value="是" title="是">
    <input type="radio" class="sfScoreTeacher" value="否" title="否">
</script>
<!-- 周次 -->
<script type="text/html" id="tmplWeek">
    <div class="tmplOpt">
        <input type="text" lay-event="week_event" class="layui-input weekOpt"
               value="{{d.scheduleWeek && d.scheduleWeek != 'null:null' ? d.scheduleWeek : ''}}">
        <span class="set week">设置</span>
    </div>
</script>
<!-- 节次 -->
<script type="text/html" id="tmplSection">
    <div class="tmplOpt">
        <input type="text" class="layui-input" style="width: 90px;" value="{{d.lpSection ? d.lpSection : 2}}">
    </div>
</script>
<!-- 教室类型 -->
<script type="text/html" id="tmplClassRoomType">
    <select name="classRoomType" id="classRoomType" class="layui-border select-demo-primary" lay-ignore>
        <option value="">请选择</option>
        {{# layui.each(d.data, function(i, v){ }}
        <option value="{{= v }}">{{= v }}</option>
        {{# }); }}
    </select>
</script>
<!-- 指定教室 -->
<script type="text/html" id="tmplClassroom">
    <div class="tmplOpt">
        <h5 class="classroom">{{d.appointClassRoom ? d.appointClassRoom : ""}}</h5>
        <span class="set classroom">选择</span>
    </div>
</script>
<!-- 助教 -->
<script type="text/html" id="tmplAssistant">
    <div class="tmplOpt">
        <h5 class="asistant" no="{{d.teachAsistantUid? d.teachAsistantUid : ''}}">{{d.teachAsistant ? d.teachAsistant :
            ""}}</h5>
        <span class="set assistant">选择</span>
    </div>
</script>
<!-- 操作 -->
<script type="text/html" id="tmplOpt">
    <div class="delet" lay-event="del">删除</div>
</script>
<!-- 排考 -->
<script type="text/html" id="barOpt1">
    <input type="radio" name="is_promote_{{ d.teacherNo }}_1" data-id="{{ d.teacherNo }}_1" data-type="pk_1" value="是"
           title="是"
           lay-filter="promote_event1">
    <input type="radio" name="is_promote_{{ d.teacherNo }}_1" data-id="{{ d.teacherNo }}_1" data-type="pk_1" value="否"
           title="否"
           lay-filter="promote_event1">
</script>
<!-- 排课-->
<script type="text/html" id="barOpt2">
    <input type="radio" name="is_promote_{{ d.teacherNo }}_2" data-id="{{ d.teacherNo }}_2" data-type="pk_2" value="1"
           title="是"
           lay-filter="promote_event2">
    <input type="radio" name="is_promote_{{ d.teacherNo }}_2" data-id="{{ d.teacherNo }}_2" data-type="pk_2" value="2"
           title="否"
           lay-filter="promote_event2">
</script>
<!-- 成绩录入-->
<script type="text/html" id="barOpt3">
    <input type="radio" name="is_promote_{{ d.teacherNo }}_3" data-id="{{ d.teacherNo }}_3" data-type="pk_3" value="1"
           title="是"
           lay-filter="promote_event3">
    <input type="radio" name="is_promote_{{ d.teacherNo }}_3" data-id="{{ d.teacherNo }}_3" data-type="pk_3" value="2"
           title="否"
           lay-filter="promote_event3">
</script>
<script th:src="${_CPR_+_VR_+'/js/jquery-3.3.1.min.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/cultivation/common.js'}"></script>
<script th:inline="javascript">
    document.domain = "chaoxing.com";
    const _VR_ = [[${_VR_}]];
    let pageIndex = 1;
    let detailId = [[${detailId}]];
    let fid = [[${fid}]];
    let formUserId = [[${formUserId}]];
    let queryId = "";
    let insTb = "";
    let form = "";
    let textBook = [[${courseManageForm?.kkgl_jc}]];
    let endWeek = [[${semester?.xnxq_jsz}]];
    let selDataArray = [], uniqueArray = [];
    layui.use(["form", "table", "laytpl"], function () {
        form = layui.form;
        var table = layui.table;
        laytpl = layui.laytpl;
        // 点击搜索
        form.on('submit(searchSubmit)', function (data) {
            table.reload('classStartsTeacher', {
                page: {
                    curr: 1 //重新从第 1 页开始
                },
                where: data.field //传给后台数据并重载
            });
            return false;
        })
        if (textBook) {
            $("#textbookTips").prev().text(textBook.map(item => item.kkgl_jcmc).join("/"));
        }
        getClazzRoomByCode();
        // 点击提交
        $('.i-submit').on('click', function () {
            let hasEmptyFields = false;
            $('div[lay-id="classStartsTeacher"] .layui-table-main tr').each(function () {
                let teacherName = $(this).find("td[data-field=teacherName] h5").text();
                let scheduleWeek = $(this).find("td[data-field=scheduleWeek] .weekOpt").val();
                if (!teacherName || !scheduleWeek) {
                    hasEmptyFields = true;
                    layer.msg("未填写必填项，请填写后提交", {icon: 2, time: 2000});
                    return false;
                }
            })
            if (!hasEmptyFields && addTeacher()) {
                layer.msg("开课成功", {icon: 1, time: 2000});
                setTimeout(successFun, 2000);
                $('.btnSubmitHide').trigger('click');
            }
        });
        // table
        table = $.extend(table, {
            config: {
                checkName: 'sfScoreTeacher'
            }
        });
        insTb = table.render({
            elem: '#classStartsTeacher',
            url: '../processData/getClassStartsTeacherData', //数据接口
            cols: [
                [{
                    field: 'teacherUid',
                    title: '教师uid',
                    align: 'center',
                    hide: true,
                    minWidth: 225
                }, {
                    field: 'teacherName',
                    title: '<i>*</i>教师姓名',
                    align: 'center',
                    minWidth: 148,
                    templet: '#tmplName'
                }, {
                    type: 'radio',
                    title: '成绩录入',
                    width: 104,
                    hide: true
                }, {
                    field: 'scheduleWeek',
                    title: '<i>*</i>安排周次',
                    align: 'center',
                    minWidth: 317,
                    templet: '#tmplWeek'
                }, {
                    field: 'lpSection',
                    title: '连排节次',
                    align: 'center',
                    minWidth: 130,
                    templet: '#tmplSection'
                }, {
                    field: 'appointClassRoomType',
                    title: '教室类型',
                    align: 'center',
                    minWidth: 140,
                    templet: '#tmplClassRoomType'
                }, {
                    field: 'appointClassRoom',
                    title: '指定教室',
                    align: 'center',
                    minWidth: 232,
                    templet: '#tmplClassroom'
                }, {
                    field: 'teachAsistant',
                    title: '助教',
                    align: 'center',
                    minWidth: 176,
                    templet: '#tmplAssistant'
                }, {
                    field: 'sfExam',
                    title: '是否排考',
                    align: 'center',
                    minWidth: 176,
                    templet: '#barOpt1',
                    hide: true
                    /*templet:function(d){
                        return "<input type=\"radio\" name='sfExam' value='是' checked title=\"是\">\n" +
                               "<input type=\"radio\" name='sfExam' value='否' title=\"否\">";
                    }*/
                }, {
                    field: 'sfpk',
                    title: '是否排课',
                    align: 'center',
                    minWidth: 176,
                    templet: '#barOpt2',
                    hide: true
                    /*templet:function(d){
                        return "<input type=\"radio\" name='sfpk' value='是' title=\"是\">\n" +
                               "<input type=\"radio\" name='sfpk' value='否' title=\"否\">";
                    }*/
                }, {
                    field: 'enterScore',
                    title: '<i>*</i>是否录入成绩',
                    align: 'center',
                    minWidth: 176,
                    templet: '#barOpt3'
                    /*templet:function(d){
                        return "<input type=\"radio\" name='enterScore' value='是'  title=\"是\">\n" +
                               "<input type=\"radio\" name='enterScore' value='否' title=\"否\">";
                    }*/
                }, {
                    field: 'scoreTeacher',
                    title: '成绩录入老师',
                    align: 'center',
                    minWidth: 176,
                    templet: '#tmplScoreName'
                }, {
                    field: 'id',
                    title: 'id',
                    hide: true
                }, {
                    title: '操作',
                    width: 90,
                    align: 'center',
                    fixed: 'right',
                    toolbar: '#tmplOpt'
                }]
            ],
            where: {detailId: detailId, formUserId: [[${formUserId}]], fid: fid},
            limits: [14, 28, 42, 60, 100],
            limit: 14,
            page: true,
            done: function (res, curr, count) {
                //回显
                const dataArr = res.data;
                getClazzRoomType(dataArr);
                for (let i = 0; i < dataArr.length; i++) {
                    const sfExam = dataArr[i].sfExam && dataArr[i].sfExam === "否" ? 1 : 0;
                    const sfpk = dataArr[i].sfpk && dataArr[i].sfpk === "否" ? 1 : 0;
                    const enterScore = dataArr[i].enterScore && dataArr[i].enterScore === "否" ? 1 : 0;
                    $('tbody').find('tr[data-index="' + i + '"]').find('input[data-type="pk_1"]').eq(sfExam).prop('checked', true);
                    $('tbody').find('tr[data-index="' + i + '"]').find('input[data-type="pk_2"]').eq(sfpk).prop('checked', true);
                    $('tbody').find('tr[data-index=0]').find('input[data-type="pk_3"]').eq(enterScore).prop('checked', true);
                    form.render('radio');
                }
                $(".layui-table-main tr:not(:first-child) td[data-field=enterScore]").hide();
                $(".layui-table-main tr:not(:first-child) td[data-field=scoreTeacher]").hide();
                $(".layui-table-main tr:first-child td[data-field=enterScore]").show().attr("rowspan", 14);
                $(".layui-table-main tr:first-child td[data-field=scoreTeacher]").show().attr("rowspan", 14);
            }
        });
        let teacherTable = table.render({
            id: 'teacherTable',
            elem: '#teacherTable',
            url: _VR_ + "/processData/getTeacherInfoData",
            height: 480,
            page: true,
            cols: [
                [{
                    type: "checkbox",
                    field: 'id',
                    width: 80,
                },
                    {
                        field: 'jsjbxx_jsgh',
                        title: '工号',
                        width: 200,
                    },
                    {
                        field: 'jsjbxx_xm',
                        title: '姓名',
                        width: 199,
                    },
                    {
                        field: 'jsjbxx_zks',
                        title: '当前学期已开周学时',
                        align: "center",
                        width: 199
                    },
                    {
                        field: 'jsjbxx_ykzks',
                        title: '当前学期预开周学时',
                        align: "center",
                        width: 199
                    },
                    {
                        field: 'jsjbxx_jysks',
                        title: '教研组',
                        align: "center",
                        width: 230
                    },
                    {
                        field: 'jsjbxx_yx',
                        title: '系部',
                        align: "center",
                        width: 230
                    },
                    {
                        field: 'rowInfo',
                        hide: true
                    }
                ]
            ],
            done: function (res) {
                $(".selected").attr("count", res.count);
                if ($(".selectAll .cur").length > 0) {
                    res.data.forEach((item) => {
                        item.LAY_CHECKED = true;
                        const index = item.LAY_TABLE_INDEX;
                        $('.layui-table th input[type="checkbox"]').prop('checked', true).next().addClass('layui-form-checked');
                        $('.layui-table tr[data-index=' + index + '] input[type="checkbox"]').prop('checked', true).next().addClass('layui-form-checked');
                    })
                    return false;
                }
                uniqueArray = $.grep(selDataArray, function (item, index) {
                    return index === $.inArray(item.jsjbxx_jsgh, $.map(selDataArray, function (obj) {
                        return obj.jsjbxx_jsgh;
                    }));
                });
                for (let i = 0; i < res.data.length; i++) {
                    for (let j = 0; j < uniqueArray.length; j++) {
                        //数据id和要勾选的id相同时checkbox选中
                        if (res.data[i].jsjbxx_jsgh === uniqueArray[j].jsjbxx_jsgh) {
                            //这里才是真正的有效勾选
                            res.data[i]["LAY_CHECKED"] = 'true';
                            //找到对应数据改变勾选样式，呈现出选中效果
                            const index = res.data[i]['LAY_TABLE_INDEX'];
                            $('.layui-table tr[data-index=' + index + '] input[type="checkbox"]').prop('checked', true).next().addClass('layui-form-checked');
                        }
                    }
                    getTeacherWeekHour(res.data[i], i);
                }
            }
        });
        $(".screen").click(function () {
            const jsbh = $("#jsbh").val();
            const xm = $("#xm").val();
            const jys = $("ul[name='jys'] .cur").map(function() {
                return $(this).text();
            }).get().join(',');
            const xb = $("ul[name='xb'] .cur").map(function() {
                return $(this).text();
            }).get().join(',');
            const field = {jsjbxx_jsgh: jsbh, jsjbxx_xm: xm, jsjbxx_jysks: jys, jsjbxx_yx: xb};
            teacherTable.reload({where: field, page: {curr: 1}});
        })
        $(".reset").click(function () {
            $(".ckd").text("请选择").removeClass("ckd");
            $("#xm,#jsbh").val("");
            $(".dropdown-lists li").removeClass("cur");
            const field = {jsjbxx_jsgh: "", jsjbxx_xm: "", jsjbxx_jysks: "", jsjbxx_yx: ""};
            teacherTable.reload({where: field, page: {curr: 1}});
        })
        //监听工具条 （编辑|删除）
        table.on('tool(classStartsTeacher)', function (obj) {
            if (obj.event === 'del') {
                layer.confirm('确定删除？', {
                    title: "操作提示",
                    icon: 0,
                    btn: ['取消','确认'],
                    btnAlign: "r",
                }, function(index){
                    layer.close(index);
                },function (index){
                    obj.del();
                    layer.close(index);
                });
            } else if (obj.event === "week_event") {
                calculateWeek();
            }
        });
        $(".fixed-bottom .selectAll span").click(function () {
            $(this).toggleClass("cur");
            let dataArray = layui.table.cache["teacherTable"];
            if ($(this).hasClass("cur")) {
                $(".fixed-bottom .selected i").text($(".selected").attr("count"));
                dataArray.forEach((item) => {
                    item.LAY_CHECKED = true;
                    const index = item.LAY_TABLE_INDEX;
                    $('.layui-table th input[type="checkbox"]').prop('checked', true).next().addClass('layui-form-checked');
                    $('.layui-table tr[data-index=' + index + '] input[type="checkbox"]').prop('checked', true).next().addClass('layui-form-checked');
                })
            } else {
                $(".fixed-bottom .selected i").text(0);
                dataArray.forEach((item) => {
                    item.LAY_CHECKED = false;
                    const index = item.LAY_TABLE_INDEX;
                    $('.layui-table th input[type="checkbox"]').prop('checked', false).next().removeClass('layui-form-checked');
                    $('.layui-table tr[data-index=' + index + '] input[type="checkbox"]').prop('checked', false).next().removeClass('layui-form-checked');
                })
            }

        });

        table.on("checkbox(teacherTable)", function (obj) {
            const checkStatus = table.checkStatus('teacherTable');
            let dataArray = layui.table.cache["teacherTable"];
            checkStatus.data.forEach(function (obj) {
                if (!selDataArray.some(function (item) {
                    return item.jsjbxx_jsgh === obj.jsjbxx_jsgh;
                })) {
                    selDataArray.push(obj);
                }
            });
            if (!obj.checked) {
                selDataArray = selDataArray.filter(function (data) {
                    return data.jsjbxx_jsgh !== obj.data.jsjbxx_jsgh;
                });
                if (obj.type === "all") {
                    selDataArray = selDataArray.filter(function (item1) {
                        return !dataArray.some(function (item2) {
                            return item1.jsjbxx_jsgh === item2.jsjbxx_jsgh;
                        });
                    });
                }
            }
            $(".fixed-bottom .selected i").text(selDataArray.length);
        });
        table.on('radio(sfScoreTeacher)', function (data) {
            console.log(data.elem); //得到 radio 原始 DOM 对象
            console.log(data.value); //被点击的 radio 的 value 值
        });
        form.on('radio(promote_event3)', function (data) {
            let radioObj = $(".layui-table-main td[data-field=scoreTeacher] .tmplOpt");
            radioObj.show();
            radioObj.each(function () {
                let teacherNo = $(this).parents("tr").find("td").eq(0).find("div").text();
                let teacherName = $(this).parents("tr").find("td").eq(1).find("h5").text();
                $(this).find("h5").text(teacherName);
                $(this).find("h5").attr("no", teacherNo);
                if (data.value === "2") {
                    radioObj.hide();
                    $(this).find("h5").attr("no", "");
                    $(this).find("h5").text("");
                }
            })
            if (data.value === "2") {
                radioObj.hide();
            }
        });
        $("#iAdd").click(function () {
            let tData = [];
            const scoreTeacher = $(".layui-table-main tr:first-child td[data-field=scoreTeacher]").find("h5").text();
            const scoreTeacherUid = $(".layui-table-main tr:first-child td[data-field=scoreTeacher]").find("h5").attr("no");
            $("#classStartsTeacher").next().find(".layui-table-main tbody tr").each(function () {
                const teacherUid = $(this).find("td").eq(0).find("div").text();
                const teacherName = $(this).find("td").eq(1).find("h5").text();
                const scheduleWeek = $(this).find("td").eq(3).find(".weekOpt").val();
                const allocatedHours = $(this).find("td").eq(3).find(".weekOpt").attr("total");
                const lpSection = $(this).find("td").eq(4).find(".layui-input").val();
                const appointClassRoomType = $(this).find("td").eq(5).find("#classRoomType").val();
                const appointClassRoom = $(this).find("td").eq(6).find("h5").text();
                const teachAsistant = $(this).find("td").eq(7).find("h5").text();
                const teachAsistantUid = $(this).find("td").eq(7).find("h5").attr("no");
                const sfExam = $(this).find("td").eq(8).find(".layui-form-radioed div").text();
                const sfpk = $(this).find("td").eq(9).find(".layui-form-radioed div").text();
                const id = $(this).find("td").eq(12).find("div").text();
                tData.push({
                    id: id,
                    teacherUid: teacherUid,
                    teacherName: teacherName,
                    scheduleWeek: scheduleWeek,
                    lpSection: lpSection,
                    appointClassRoomType: appointClassRoomType,
                    appointClassRoom: appointClassRoom,
                    teachAsistant: teachAsistant,
                    teachAsistantUid: teachAsistantUid,
                    allocatedHours: allocatedHours,
                    sfExam: sfExam,
                    sfpk: sfpk,
                    scoreTeacher: scoreTeacher,
                    scoreTeacherUid: scoreTeacherUid
                })
            })
            tData.push({
                id: '',
                teacherNo: '',
                teacherName: '',
                scheduleWeek: '',
                lpSection: '',
                appointClassRoomType: '',
                appointClassRoom: clazzRoom,
                teachAsistant: '',
                teachAsistantNo: '',
                allocatedHours: '',
                sfExam: '',
                sfpk: '',
                scoreTeacher: '',
                scoreTeacherUid: ''
            })
            table.reload('classStartsTeacher', {data: tData, url: ''})
        })

    });

    //安排周次弹窗
    $(".i-table").on("click", ".week", function () {
        let _this = $(this);
        let semester = [[${courseManageForm?.kkgl_kkxq}]];
        let totalClassHour = [[${courseManageForm?.kkgl_zongxs}]];
        layer.open({
            type: 2,
            area: ["100%", "350px"],
            title: "安排周次",
            maxmin: false, //开启最大化最小化按钮
            content: "../process/weeksPop?week=" + _this.prev().val() + "&totalClassHour=" + totalClassHour + "&semester=" + semester,
            btn: ['确定', '关闭'],
            skin: 'teacher_layer',
            yes: function (index, layero) {
                // 提交 获取一个 按钮 进行 提交测试
                let iframeWin = layero.find('iframe')[0];
                let detail = iframeWin.contentWindow.getdata();// 调用子页面的函数
                if (!detail) {
                    layer.msg("请先设置周学时", {icon: 2, time: 2000});
                    return false;
                }
                _this.prev().val(detail.cstr);
                _this.prev().attr("total", detail.total);
                calculateWeek();
                layer.close(index);
            },
            cancel: function () {
                layer.closeAll();
            },
            end: function () {
                layer.closeAll();
            }
        });
    })

    //指定教室弹窗
    $(".i-table").on("click", ".classroom", function () {
        let _this = $(this);
        let classRoomType = _this.parents("td").prev().find("select").val();
        classRoomType = classRoomType ? classRoomType : "";
        layer.open({
            type: 2,
            area: ["840px", "600px"],
            title: "选择教室",
            maxmin: false, //开启最大化最小化按钮
            content: "../process/classRoomPop?classRoomType=" + classRoomType + "&majorCode=" + [[${courseManageForm?.kkgl_zybh}]],
            btn: ['确定', '关闭'],
            skin: 'teacher_layer',
            yes: function (index, layero) {
                // 提交 获取一个 按钮 进行 提交测试
                var iframeWin = layero.find('iframe')[0];
                var rowData = iframeWin.contentWindow.getSelectData();// 调用子页面的函数
                if (0 == rowData) {
                    layer.alert("请选择教室。");
                } else {
                    _this.parents("td").prev().find("select").val(rowData.jsxx_jslx);
                    _this.prev().text(rowData.jsxx_jsmc);
                    layer.close(index);
                }
            },
            cancel: function () {
                layer.closeAll();
            },
            end: function () {
                layer.closeAll();
            }
        });
    })

    //指定教师
    $(".i-table").on("click", ".teacherName", function () {
        let _this = $(this);
        openTeacherPop(_this, "teacher");
    });

    $(".back").click(function () {
        window.location.href = document.referrer;
    })

    function addClassStarts(teacherData) {
        if (teacherData && teacherData.length > 0) {
            $.post('/processData/courseSetting', {
                teacherData: JSON.stringify(teacherData),
                fid: fid,
                formUserId: formUserId
            }, function (res) {

            })
        } else {
            clearCourseSetting();
        }
    }

    function clearCourseSetting() {
        $.post('/processData/clearCourseSetting', {
            fid: fid,
            formUserId: formUserId
        }, function (res) {
        })
    }

    //提交
    function addTeacher() {
        let tData = [];
        let teacherArray = [];
        let flag = true;
        const enterScore = $(".layui-table-main tr:first-child td[data-field=enterScore]").find(".layui-form-radioed div").text();
        const scoreTeacher = $(".layui-table-main tr:first-child td[data-field=scoreTeacher]").find("h5").text();
        const scoreTeacherUid = $(".layui-table-main tr:first-child td[data-field=scoreTeacher]").find("h5").attr("no");
        $("#classStartsTeacher").next().find(".layui-table-main tbody tr").each(function () {
            const teacherUid = $(this).find("td").eq(0).find("div").text();
            const teacherName = $(this).find("td").eq(1).find("h5").text();
            let scheduleWeek = $(this).find("td").eq(3).find(".weekOpt").val();
            const allocatedHours = $(this).find("td").eq(3).find(".weekOpt").attr("total");
            const lpSection = $(this).find("td").eq(4).find(".layui-input").val();
            const appointClassRoomType = $(this).find("td").eq(5).find("#classRoomType").val();
            const appointClassRoom = $(this).find("td").eq(6).find("h5").text();
            const teachAsistant = $(this).find("td").eq(7).find("h5").text();
            const teachAsistantUid = $(this).find("td").eq(7).find("h5").attr("no");
            const sfExam = $(this).find("td").eq(8).find(".layui-form-radioed div").text();
            const sfpk = $(this).find("td").eq(9).find(".layui-form-radioed div").text();
            const id = $(this).find("td").eq(12).find("div").text();
            scheduleWeek = scheduleWeek.replace(/，/g, ',').replace(/：/g, ':');
            if (!/[0-9]+[-]{1}[0-9]+[:]{1}[0-9]+/.test(scheduleWeek)) {
                layer.msg("安排周次输入格式有误,请按照1-1:4,2-20:2的格式输入", {icon: 2, time: 2000});
                flag = false;
                return false;
            }
            const week = scheduleWeek.match(/\d+-\d+:\d+/g);
            const result = week.map(match => {
                const [start, end] = match.split(/[ :-]/).map(Number);
                return {start, end};
            });
            result.forEach(({start, end}) => {
                if (endWeek && end > endWeek) {
                    layer.msg("结束周次大于开课学年学期的结束周次，请重新设置。", {icon: 2, time: 2000});
                    flag = false;
                    return false;
                }
                if (start > end) {
                    layer.msg("开始周次不能大于结束周次，请重新设置。", {icon: 2, time: 2000});
                    flag = false;
                    return false;
                }
            });
            teacherArray.push(teacherName);
            tData.push({
                id: id,
                teacherUid: teacherUid,
                teacherName: teacherName,
                scheduleWeek: scheduleWeek,
                lpSection: lpSection,
                appointClassRoomType: appointClassRoomType,
                appointClassRoom: appointClassRoom,
                teachAsistant: teachAsistant,
                teachAsistantUid: teachAsistantUid,
                allocatedHours: allocatedHours,
                sfExam: sfExam,
                sfpk: sfpk,
                enterScore: enterScore,
                scoreTeacher: scoreTeacher,
                scoreTeacherUid: scoreTeacherUid
            })
        })
        if (flag) {
            $('.btnSubmit').attr('disabled', true);
            addClassStarts(tData);
        }
        return flag;
    }

    $(".i-table").on("change", "select[name=classRoomType]", function () {
        $(this).parents("td").next().find("h5").text("");
    })
    $(".i-table").on("blur", ".weekOpt", function () {
        let val = $(this).val().replace(/，/g, ',').replace(/：/g, ':');
        $(this).val(val);
    })
    //助教弹窗
    $(".i-table").on("click", ".assistant", function () {
        let _this = $(this);
        openTeacherPop(_this, "assistant");
    })

    //成绩录入教师弹窗
    $(".i-table").on("click", ".scoreTeacher", function () {
        let _this = $(this);
        openTeacherPop(_this, "scoreTeacher");
    })

    function openTeacherPop(_this, obj) {
        const titles = {
            teacher: "指定授课教师",
            assistant: "指定助教",
            scoreTeacher: "指定成绩录入教师"
        };
        const descs = {
            teacher: "使用说明：不勾选数据直接点击确定提交，即可清空已有授课教师。",
            assistant: "使用说明：不勾选数据直接点击确定提交，即可清空已有助教。",
            scoreTeacher: "使用说明：不勾选数据直接点击确定提交，即可清空已有成绩录入教师。"
        };
        const display = {
            teacher: 1,
            assistant: 2,
            scoreTeacher: 3
        };
        const title = titles[obj] || "";
        const desc = descs[obj] || "";
        const show = display[obj] || 0;
        $(".title .name").text(title);
        $(".illustrate li").text(desc);
        $(".oprate").toggle(show !== 3);
        $(".oprate .switch").toggleClass("switch-open", show === 1);
        layer.open({
            type: 1,
            title: false,
            closeBtn: false,
            shadeClose: true,
            isOutAnim: true,
            content: $("#editPoups"),
            area: ["auto", "auto"],
            btn: ['取消', '确定'],
            btn1: function () {
                layer.closeAll();
            },
            btn2: function (index) {
                let teacherName = [], teacherUid = [];
                for (let i = 0; i < selDataArray.length; i++) {
                    teacherName.push(selDataArray[i].jsjbxx_xm);
                    teacherUid.push(selDataArray[i].jsjbxx_uid);
                }
                if (_this.parents("td").index() === 1 && $(".switch-open").length > 0) {
                    _this.parents("tr").find("td").eq(11).find("h5").text(teacherName.join(","));
                    _this.parents("tr").find("td").eq(11).find("h5").attr("no", teacherUid.join(","));
                    _this.parents("td").prev().find("div").text(teacherUid.join(","));
                }
                _this.prev().attr("no", teacherUid.join(","));
                _this.prev().text(teacherName.join(","));
                _this.prev().attr("title", teacherName.join(","));
                layer.close(index);
            }
        });
    }

    function getClazzRoomType(dataArray) {
        $.post('/teacherIdle/getFormDistinctFiled', {
            formAlias: "jslx",
            fieldAlias: "zd_jslx"
        }, function (res) {
            // 将请求成功后返回的数据渲染到模板中
            const names = $.map(res.list, function (obj) {
                return obj.dictName;
            });
            let data = {code: 0, data: res.list};
            let result = laytpl($('#tmplClassRoomType').html()).render(data);
            $("select[name=classRoomType]").html(result);
            dataArray.forEach((data, index) => {
                let defaultVal = data["appointClassRoomType"]; // 默认选中项的值
                $("select[name=classRoomType]").eq(index).val(defaultVal); // 设置默认选中项的值
            });
        });
    }

    let clazzRoom = "", clazzCampus = "";

    function getClazzRoomByCode() {
        if ([[${courseManageForm?.kkgl_jxbzcbh}]]) {
            $.ajax({
                url: "../processData/getClazzRoomByCode",
                type: 'post',
                data: {
                    fid: fid, code: [[${courseManageForm?.kkgl_jxbzcbh}]].split(",")[0]
                },
                async: false,
                success: function (d) {
                    clazzRoom = d.classInfoForm.bjxx_gdjs;
                    clazzCampus = d.classInfoForm.bjxx_szxq;
                }
            })
        }
    }

    $(".select-input .name").click(function () {
        let _this = $(this);
        if(_this.parent().find(".dropdown-lists li").length > 0) {
            return;
        }
        $.post(_VR_ + '/teacherIdle/getFormDistinctFiled', {
            formAlias: _this.attr("formAlias"),
            fieldAlias: _this.attr("fieldAlias")
        }, function (res) {
            let optionHtml = "";
            for (let i = 0; i < res.list.length; i++) {
                let d = res.list[i];
                optionHtml += "<li><span>" + d + "</span></li>";
            }
            _this.parent().find(".dropdown-lists").html(optionHtml);
        })
    });


    function calculateWeek() {
        let total = 0;
        $("#classStartsTeacher").next().find(".layui-table-main tbody tr").each(function (index) {
            let val = $(this).find("td").eq(3).find("input[type='text']").val();
            let values = val.split(",").map(item => parseInt(item.split(":")[1]));
            total += values.reduce((sum, num) => sum + num, 0);
        })
        $("#allocatedHours_p").val(total ? total : 0);
    }

    function successFun() {
        window.parent.postMessage(JSON.stringify({action: 1}), "*");
    }

    // 展开/收起
    $(".i-top .arrow").click(function () {
        $(this).parent().next().slideToggle();
        $(this).toggleClass('slide');
    })

    function getTeacherWeekHour(teacher, idx) {
        $.post(_VR_ + "/cultivation/getTeacherWeekHour", {
            semester: [[${courseManageForm?.kkgl_kkxq}]],
            teacherNo: teacher.jsjbxx_jsgh,
            fid: fid
        }, function (result) {
            if (result.data) {
                let weekHour = result.data.weekHour ? result.data.weekHour : 0;
                let preWeekHour = result.data.preWeekHour ? result.data.preWeekHour : 0;
                $('div[lay-id="teacherTable"] tr[data-index=' + idx + '] td[data-field="jsjbxx_zks"] div').text(weekHour);
                $('div[lay-id="teacherTable"] tr[data-index=' + idx + '] td[data-field="jsjbxx_ykzks"] div').text(preWeekHour);
            }
        }, "json");
    }

</script>

</html>