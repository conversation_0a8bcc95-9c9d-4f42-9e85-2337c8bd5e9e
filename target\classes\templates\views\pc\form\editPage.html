<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <script src=""></script>
    <script th:src="@{~/js/form/web-office-sdk-solution-v1.1.27.umd.js}"></script>
    <script th:src="@{~/js/jquery-3.3.1.min.js}"></script>
    <link rel="stylesheet" th:href="@{~/plugin/layui/css/layui.css}">
    <link rel="stylesheet" th:href="@{~/css/form/select2.min.css}">
    <script th:src="@{~/plugin/layui/layui.js}"></script>
    <script th:src="@{~/js/form/select2.min.js}"></script>
    <script th:src="@{~/js/form/wpsLib.umd.js}"></script>
    <!-- <script src="js/wpsLib.js" type="module"></script> -->


    <style>
        .wps-wrapper,
        body,
        html {
            width: 100%;
            height: 100%;
        }
    </style>
</head>

<body>

    <div class="wps-wrapper">

    </div>
</body>
<script th:inline="javascript">
    let docUrl = [[${docUrl}]];
    let fid = [[${fid}]];
    let uid = [[${uid}]];
    let formAliases = [[${formAliases}]];
    // 引入 wpsLib.js
    /*   import { initWps } from './js/wpsLib.js'
      initWps({
          docUrl: "https://wps1.cldisk.com/weboffice/office/w/1013167764021030912?_w_appid=HZHBUAJBBNCZOQDF&_w_third_appid=HZHBUAJBBNCZOQDF&_w_third_file_id=1013167764021030912&_w_third_param_obj=h9uh7V3b5OHfoGtWPLmftGPt7vBGavH4m0Oq5m49vGAskTi8hnqYaLuP6ZeYFMQaThVcd8t2Orq77gDV1o94BMSeTzzouebBRZdOeAbkiSfErBQQWqUDqZ9Oyjg9l%2FiQmSj3lBTz7z9X976KAftvbKYQHgdz8y9N%2BpDlmO7hOAh2D6ma7bLapALma8NeTOSFYMCnKcwD4uPtlKoeUbdfYqyEjPCIppsCqc%2Be6dKrTr4Mk7A7YzC3lGz%2FpdJlw0cr8eM32djgslAt1xAB4BjOXRvnwDBlduOl6MmIk%2BTuXz%2FBTe58RXZsQHsN3mAYCe3VO%2FDdhDi5e%2FLxLOsxxXXTzQ%3D%3D&preview_mode=ordinary&route_key=0",
          elem: ".wps-wrapper",
          apiUrl: "./js/a.json"
      }) */
    // 引入 wpsLib.umd.js
    window.wpsLib.initWps({
        docUrl: docUrl,
        elem: ".wps-wrapper",
        apiUrl: "/api/form/info/upload/getFormConfig?fid="+fid+"&formAliases="+formAliases
    })


</script>

</html>