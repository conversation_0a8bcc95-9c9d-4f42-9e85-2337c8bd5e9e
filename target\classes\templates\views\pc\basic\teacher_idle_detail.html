<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>详情</title>
    <link rel="stylesheet" th:href="@{../css/basic/global.css}">
    <link rel="stylesheet" th:href="@{../plugin/layui/css/layui.css}">
    <link rel="stylesheet" th:href="@{../css/basic/noClassSearch.css}">
    <script th:src="@{../plugin/layui/layui.js}"></script>
</head>

<body>
    <div class="main">
        <div class="top">
            <div class="title">
                <div class="back">返回</div>
                <div class="levelone">详情</div>
            </div>

        </div>
        <div class="tableDetail">
            <table>
                <thead>
                    <tr>
                        <td>教师姓名</td>
                        <td>教师工号</td>
                        <td>所属部门</td>
                        <td>占用状态</td>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
        <div id="coursePage"></div>
    </div>
</body>
<script th:inline="javascript">
    let day = [[${day}]];
    let teacherNo = [[${teacherNo}]];
    let time = [[${time}]];
    let _VR_ = [[${_VR_}]];
    let festivals = [[${festivals}]];
</script>
<script th:src="@{../js/jquery-3.3.1.min.js}"></script>
<script th:src="@{../js/my.util.js}"></script>
<script th:src="@{../js/basic/teacher_detail.js(v=${new java.util.Date().getTime()})}"></script>
</html>