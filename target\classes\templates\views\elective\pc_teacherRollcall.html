<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" li-content="IE=edge">
    <meta name="viewport" li-content="width=device-width, initial-scale=1.0">
    <title>微点名</title>
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/global.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/layui/css/layui.css'">
    <link rel="stylesheet" href="css/commonPC.css">
    <link rel="stylesheet" href="css/reset.css">
    <link rel="stylesheet" href="css/poup.css">
    <link rel="stylesheet" href="css/microdot-name_pc_teacherRollcall.css">
    <script th:src="${_CPR_}+'/elective/layui/layui.js'"></script>

</head>

<body>
<div class="main">
    <div class="top">
        <div class="title">
            <div class="back" th:if="${!isForm}"><a href="#" onclick="javascript:window.history.back();">返回</a></div>
            <div class="levelone">微点名</div>
            <div class="icon"></div>
            <div class="leveltwo">点名</div>
        </div>
        <div class="btns-list">
            <div class="export updateStudent"  style="margin-right: 15px;display: none" onclick="updateStudent()">更新学生名单</div>
            <div class="export" onclick="addRollcall(false);">保存点名结果</div>
        </div>

    </div>
    <div class="con">
        <div class="filter-box">
            <div class="inform">
                <div class="i-item">
                    <div class="name">课程：</div>
                    <div class="text" th:inline="text"> [[${courseName}]]</div>
                </div>
                <div class="i-item">
                    <div class="name">教学班：</div>
                    <div class="text" th:inline="text">[[${teachingClassName}]]</div>
                </div>
                <div class="i-item">
                    <div class="name">上课时间：</div>
                    <div class="text" th:inline="text">
                        <span>第[[${zc}]]周</span><span>周[[${xq}]]</span><span>第[[${kj}]]节</span></div>
                </div>
            </div>
            <div class="search">
                <input type="text" placeholder="搜索学生姓名或学号" id="keywordIp">
                <span onclick="getList();"></span>
            </div>
        </div>
        <div class="person-list">
            <ul>
            </ul>
        </div>

    </div>
</div>


<div id="prompt" class="prompt popups" style="width:640px;">
    <div class="title">
        <div class="name">提示</div>
        <div class="close"></div>
    </div>
    <div class="popup-con">
        <div class="tips">
            <p>确定将<em>何颖琦</em>标记为 <span>迟到</span> ？</p>
        </div>
    </div>
    <div class="bottom">
        <div class="cancle">取消</div>
        <div class="confirm">确定</div>
    </div>
</div>


<div id="evaluate" class="evaluate-poup popups" style="width:640px;">
    <div class="title">
        <div class="name">提示</div>
        <div class="close"></div>
    </div>
    <div class="popup-con">
        <div class="appraise layui-form">
            <div class="name">评价内容</div>
            <div class="layui-input-block">
                <textarea name="eval-content" placeholder="请输入" class="layui-textarea"></textarea>
            </div>
        </div>
    </div>
    <div class="bottom">
        <div class="cancle">取消</div>
        <div class="confirm">确定</div>
    </div>
</div>


</body>

<script th:src="${_CPR_}+'/elective/js/jquery-3.3.1.min.js'"></script>
<script th:inline="javascript">
    var isForm = [[${isForm}]];
    var today = [[${today}]];
    var bldm = [[${bldm}]];
    var qjUid = [[${qjUid}]];
    var teachingClassName = [[${teachingClassName}]];
    var teachingClassCode = [[${teachingClassCode}]];
    var zc = [[${zc}]];
    var kj = [[${kj}]];
    var xq = [[${xq}]];
    var classDate = [[${classDate}]];
    var teacherUid = [[${teacherUid}]];
    var teacherName = [[${teacherName}]];
    var courseName = [[${courseName}]];
    var rollcallStateList = [[${rollcallStateList}]];

    var storage = window.localStorage;
    var storageKey = teachingClassCode + "_" + classDate + "_" + kj + "_";
    console.log(storage)
    if (bldm == '2' && today == '1') {
        alert("不允许对过去时间的课程补录点名");
    }
    if (today == '2') {
        alert("不允许对未来时间的课程补录点名");
    }
    $("#keywordIp").keyup(function (e) {
        if (e.keyCode == 13) {
            getList();
        }
    })
    var array = [];
    var json = {};
    layui.use(['table', 'jquery', 'laypage', 'laydate', 'form'], function () {

        var table = layui.table;
        var $ = layui.jquery;
        var layer = layui.layer;
        var laydate = layui.laydate;
        var form = layui.form;


        //收起展开
        $("body .main .con .person-list").on("click", "ul li .li-con .evaluate .text .state", function () {
            $(this).parent().toggleClass("launch");
            if ($(this).parent().hasClass("launch")) {
                $(this).find("em").text("收起");
            } else {
                $(this).find("em").text("展开");
            }
        })


        //评价

        $(".main .con .person-list").on("click", " ul li .inform .state", function () {
            if (bldm == '2' && today != '0') {
                return false;
            }
            if (today == '2') {
                return false;
            }
            // let state=$(this).parents("li").hasClass("disabled");
            // if(state){
            //     return false;
            // }
            var uid = $(this).parents(".li-con").attr("uuuid");
            $.each(array, function (index, item) {
                if (item != undefined && item.uid == uid) {
                    json = item;
                }
            });
            if (json.uid == undefined || json.uid != uid) {
                json = {};
                json.uid = uid;
            }

            $("#tagId").removeAttr("id");
            $(this).parents("li").attr("id", "tagId");
            let text = $("#tagId .li-con .evaluate .text p").text();
            $("#evaluate .popup-con .appraise .layui-input-block .layui-textarea").val(text);

            layer.open({
                type: 1,
                title: false,
                closeBtn: false,
                shadeClose: true,
                isOutAnim: true,
                content: $("#evaluate"),
                area: ["auto", "auto"],
                success: function () {
                },
            });
        })


        //隐藏弹窗
        $('.close,.cancle').on("click", function () {
            var index = $(this).parents(".layui-layer").attr("times");
            layer.close(index);
        })


        //标记状态

        $(".main .con .person-list").on("click", "ul li .li-con .lable-box .lab span", function () {
            // let state=$(this).parents("li").hasClass("disabled");
            // if(state){
            //     return false;
            // }
            if (bldm == '2' && today != '0') {
                return false;
            }
            if (today == '2') {
                return false;
            }
            var uid = $(this).parents(".li-con").attr("uuuid");
            $.each(array, function (index, item) {
                if (item != undefined && item.uid == uid) {
                    json = item;
                }
            });
            if (json.uid == undefined || json.uid != uid) {
                json = {};
                json.uid = uid;
            }
            var stuState = $(this).text();
            var thisstateid = $(this).attr("stateid");
            if (stuState != '迟到' && stuState != '早退') {
                if (json.kk == "1" || json.cd == "1" || json.zt == "1" || json.qj == "1" || (json.state != undefined && json.state != '0')) {
                    layer.msg('该状态不能与其他状态同时存在')
                    return false;
                }
            } else {
                if (json.kk == "1" ||  json.qj == "1" || (json.state != undefined && json.state != '0')) {
                    layer.msg('该状态不能与其他状态同时存在')
                    return false;
                }
            }
            // if (json.state!='0' && (json.state != thisstateid || json.kk == "1" || json.zt == "1" || json.cd == "1" || json.qj == "1")) {
            //     layer.msg('自定义状态与其他状态不能同时存在')
            //     return false;
            // }
            // if (((json.kk == "1" || json.zt == "1" || json.cd == "1" || (json.state != undefined && json.state != "0")) && stuState == '请假') || (json.qj == "1" && stuState != '请假')) {
            //     layer.msg('其他状态和请假状态不能同时存在')
            //     return false;
            // }
            // if (((json.qj == "1" || json.zt == "1" || json.cd == "1" || (json.state != undefined && json.state != "0")) && stuState == '旷课') || (json.kk == "1" && stuState != '旷课')) {
            //     layer.msg('其他状态和旷课状态不能同时存在')
            //     return false;
            // }
            $("#tagId").removeAttr("id");
            $(this).attr("id", "tagId");
            let text = $(this).text();
            let name = $(this).parents("li").find(".li-con .inform .person .text h3").text();
            $("#prompt").find(".popup-con .tips p span").text(text);
            $("#prompt").find(".popup-con .tips p em").text(name);
            layer.open({
                type: 1,
                title: false,
                closeBtn: false,
                shadeClose: true,
                isOutAnim: true,
                content: $("#prompt"),
                area: ["auto", "auto"],
                success: function () {

                },
            });


        })

        //标记弹窗确定

        $("#prompt .bottom div.confirm").click(function () {
            var statusStr = $("#tagId").text();
            var thisstateid = $("#tagId").attr("stateid");
            if (statusStr == '迟到') {
                json.cd = "1";
            } else if (statusStr == '旷课') {
                json.kk = "1";
            } else if (statusStr == '早退') {
                json.zt = "1";
            } else if (statusStr == '请假') {
                json.qj = "1";
            } else {
                json.state = thisstateid;
            }
            delArray(json.uid);
            array.push(json);
            if (storage) {
                storage.setItem(storageKey + json.uid, JSON.stringify(json));
            }
            $("#tagId").parent().addClass("active");
            let index = $(this).parents(".layui-layer").attr("times");
            layer.close(index);
        })

        //删除标记

        $(".main .con .person-list").on("click", " ul li .li-con .lable-box .lab.active em", function () {
            if (bldm == '2' && today != '0') {
                return false;
            }
            if (today == '2') {
                return false;
            }
            var uid = $(this).parents(".li-con").attr("uuuid");
            var statusStr = $(this).prev().text();
            $.each(array, function (index, item) {
                if (item != undefined && item.uid == uid) {
                    json = item;
                }
            });
            if (statusStr == '迟到') {
                json.cd = "0";
            } else if (statusStr == '旷课') {
                json.kk = "0";
            } else if (statusStr == '早退') {
                json.zt = "0";
            } else if (statusStr == '请假') {
                json.qj = "0";
            } else {
                json.state = '0';
            }
            delArray(json.uid);
            array.push(json);
            if (storage) {
                storage.setItem(storageKey + json.uid, JSON.stringify(json));
            }
            $(this).parent().removeClass("active");
        })

        //评论确定
        $("#evaluate .bottom div.confirm").click(function () {
            let text = $("#evaluate .popup-con .appraise .layui-input-block .layui-textarea").val().trim();
            json.val = text;
            delArray(json.uid);
            if (storage) {
                storage.setItem(storageKey + json.uid, JSON.stringify(json));
            }
            array.push(json);
            if (text.length == 0) {
                $("#tagId").find(".li-con .evaluate").remove();
                $("#tagId").find(".li-con .inform .state").text('待评价').addClass("wait-rate").removeClass("reviewed");
            } else {

                let obj = $("#tagId").find(".evaluate").length;

                if (obj > 0) {
                    $("#tagId .li-con .evaluate .text p").text(text);
                } else {
                    let khtml = '<div class="evaluate">' +
                        '<div class="name">评价：</div>' +
                        '<div class="text">' +
                        '<p>' + text + '</p>' +
                        '<div class="state">' +
                        '<em>展开</em>' +
                        '<i></i>' +
                        '</div>' +
                        '</div>' +
                        '</div>';

                    $("#tagId").find(".li-con").append(khtml);

                }

                textLimit($("#tagId"));

                $("#tagId").find(".li-con .inform .state").text('已评价').removeClass("wait-rate").addClass("reviewed");
            }


            let index = $(this).parents(".layui-layer").attr("times");
            layer.close(index);
        })


        function textLimit(obj) {

            let bwidth = obj.find(".li-con .evaluate .text p").width();
            let pwidth = obj.find(".li-con .evaluate .text").width();

            if (bwidth != pwidth) {
                obj.find(".evaluate .state").hide();
            } else {
                obj.find(".evaluate .state").show();
            }

        }

        getList();


    });

    function getList() {
        var keyword = $("#keywordIp").val();
        $.ajax({
            type: "POST",
            url: "/elective/rollcall/getStudents",
            data: {teachingClassCode: teachingClassCode, keyword: keyword, zc: zc, xq: xq, kj: kj},
            dataType: 'json',
            async: false,
            success: function (data) {
                var html = "";
                var text = "";

                if (data.status) {
                    if (data.dataFlag == "vo") {
                        for (var i = 0; i < data.list.length; i++) {
                            if (data.list[i].sjly != "0") {
                                continue
                            }
                            var thisJson = {};
                            if (storage) {
                                let item = storage.getItem(storageKey + data.list[i].jxbxsb_xslxr.puid);
                                if (item) {
                                    thisJson = JSON.parse(item);
                                }
                            }
                            var qj = false;
                            if (qjUid.indexOf(data.list[i].jxbxsb_xslxr.puid) != -1) {
                                html += "<li class='disabled'>";
                                qj = true;
                            } else {
                                html += "<li>";
                            }
                            html += "<div class='li-con' uuuid='" + data.list[i].jxbxsb_xslxr.puid + "'>";
                            html += "<div class='number'>" + (parseInt(i) + 1) + "</div>";
                            html += "<div class='inform'>";
                            html += "<div class='person'>";
                            html += "<img src='http://photo.chaoxing.com/p/" + data.list[i].jxbxsb_xslxr.puid + "_80' alt=''>";
                            html += "<div class='text'>";
                            html += "<h3>" + data.list[i].jxbxsb_xsxm + "</h3>";
                            html += "<p>" + data.list[i].jxbxsb_zzbmc + "</p>";
                            html += "</div>";
                            html += "</div>";
                            if (thisJson.val && thisJson.val != '') {
                                html += "<div class='state reviewed'>已评价</div>";
                            } else {
                                html += "<div class='state wait-rate'>待评价</div>";
                            }
                            html += "</div>";


                            html += "<div class='lable-box'>";
                            for (let j = 0; j < rollcallStateList.length; j++) {
                                html += getStateDiv(j, rollcallStateList[j], thisJson, qj);
                                html += "<span stateid = '" + rollcallStateList[j].id + "'>" + rollcallStateList[j].stateNamePc + "</span>";
                                html += "<em></em>";
                                html += "</div>";
                            }
                            html += "</div>";
                            if (thisJson.val && thisJson.val != '') {
                                html += "<div class='evaluate'>";
                                html += "<div class='name'>评价：</div>";
                                html += "<div class='text'>";
                                html += "<p>" + thisJson.val + "</p>";
                                html += "<div class='state'>";
                                html += "<em>展开</em>";
                                html += "<i></i>";
                                html += "</div>";
                                html += "</div>";
                                html += "</div>";
                            }
                            html += "</div>";
                            html += "</li>";

                            if (thisJson.uid) {
                                delArray(thisJson.uid);
                                thisJson.uid = data.list[i].jxbxsb_xslxr.puid;
                                thisJson.code = data.list[i].jxbxsb_xsxh;
                                thisJson.xsid = data.list[i].xsdataid;
                                thisJson.realname = data.list[i].jxbxsb_xsxm;
                                thisJson.zzbmc = data.list[i].jxbxsb_zzbmc;
                                thisJson.zy = data.list[i].jxbxsb_zy;
                                thisJson.classCode = data.list[i].jxbxsb_zzbbh;
                                if (qj) {
                                    thisJson.qj = "1";
                                }
                                array.push(thisJson);
                            } else {
                                var addJson = {};
                                addJson.uid = data.list[i].jxbxsb_xslxr.puid;
                                addJson.code = data.list[i].jxbxsb_xsxh;
                                addJson.xsid = data.list[i].xsdataid;
                                addJson.realname = data.list[i].jxbxsb_xsxm;
                                addJson.zzbmc = data.list[i].jxbxsb_zzbmc;
                                addJson.zy = data.list[i].jxbxsb_zy;
                                addJson.classCode = data.list[i].jxbxsb_zzbbh;
                                if (qj) {
                                    addJson.qj = "1";
                                }
                                delArray(addJson.uid);
                                array.push(addJson);
                            }
                        }
                    }
                    if (data.dataFlag == "po") {
                        $(".updateStudent").show();
                        for (var i = 0; i < data.list.length; i++) {
                            var qj = false;
                            if (qjUid.indexOf(data.list[i].studentUid) != -1) {
                                html += "<li class='disabled'>";
                                qj = true;
                            } else {
                                html += "<li>";
                            }
                            html += "<div class='li-con' uuuid='" + data.list[i].studentUid + "'>";
                            html += "<div class='number'>" + (parseInt(i) + 1) + "</div>";
                            html += "<div class='inform'>";
                            html += "<div class='person'>";
                            html += "<img src='http://photo.chaoxing.com/p/" + data.list[i].studentUid + "_80' alt=''>";
                            html += "<div class='text'>";
                            html += "<h3>" + data.list[i].studentRealname + "</h3>";
                            html += "<p>" + data.list[i].administrationClassName + "</p>";
                            html += "</div>";
                            html += "</div>";
                            if (data.list[i].val != undefined && data.list[i].val != '') {
                                html += "<div class='state reviewed'>已评价</div>";
                            } else {
                                html += "<div class='state wait-rate'>待评价</div>";
                            }

                            html += "</div>";

                            html += "<div class='lable-box'>";
                            for (let j = 0; j < rollcallStateList.length; j++) {
                                html += getStateDiv(j, rollcallStateList[j], data.list[i], false);
                                html += "<span  stateid = '" + rollcallStateList[j].id + "'>" + rollcallStateList[j].stateNamePc + "</span>";
                                html += "<em></em>";
                                html += "</div>";
                            }

                            html += "</div>";

                            if (data.list[i].val != undefined && data.list[i].val != '') {
                                html += "<div class='evaluate'>";
                                html += "<div class='name'>评价：</div>";
                                html += "<div class='text'>";
                                html += "<p>" + data.list[i].val + "</p>";
                                html += "<div class='state'>";
                                html += "<em>展开</em>";
                                html += "<i></i>";
                                html += "</div>";
                                html += "</div>";
                                html += "</div>";
                            }

                            html += "</div>";
                            html += "</li>";
                            var addJson = {};
                            addJson.uid = data.list[i].studentUid;
                            addJson.code = data.list[i].studentCodeDecrypt;
                            addJson.cd = data.list[i].cd == undefined ? 0 : data.list[i].cd;
                            addJson.kk = data.list[i].kk == undefined ? 0 : data.list[i].kk;
                            addJson.zt = data.list[i].zt == undefined ? 0 : data.list[i].zt;
                            addJson.xsid = data.list[i].xsid;
                            addJson.qj = data.list[i].qj == undefined ? 0 : data.list[i].qj;
                            addJson.state = data.list[i].state == undefined ? 0 : data.list[i].state;
                            addJson.val = data.list[i].val == undefined ? 0 : data.list[i].val;

                            addJson.realname = data.list[i].studentRealname;
                            addJson.zzbmc = data.list[i].administrationClassName;
                            addJson.zy = data.list[i].zy;
                            addJson.classCode = data.list[i].administrationClassCode;
                            delArray(addJson.uid);
                            array.push(addJson);
                        }
                    }
                }
                $(".person-list ul").html(html);
            }
        });
    }

    function delArray(uid) {
        $.each(array, function (index, item) {
            if (item != undefined && item.uid == uid) {
                array.splice(index, 1);
            }
        });
    }

    function getStateDiv(index, stateOb, thisOb, qj) {
        if ((stateOb.type == 0 && stateOb.stateAlias == 'cd' && thisOb.cd == 1) ||
            (stateOb.type == 0 && stateOb.stateAlias == 'kk' && thisOb.kk == 1) ||
            (stateOb.type == 0 && stateOb.stateAlias == 'zt' && thisOb.zt == 1) ||
            (stateOb.type == 0 && stateOb.stateAlias == 'qj' && thisOb.qj == 1) ||
            (stateOb.type == 0 && stateOb.stateAlias == 'qj' && qj) ||
            (stateOb.type == 1 && thisOb.state == stateOb.id)) {
            if (index == 0) {
                return " <div class='lab late active'>";
            } else if (index == 1) {
                return " <div class='lab absenteeism active'>";
            } else if (index == 2) {
                return " <div class='lab early-departure active'>";
            } else if (index == 3) {
                return " <div class='lab leave active'>";
            } else if (index == 4) {
                return " <div class='lab others active'>";
            }
            return " <div class='lab late active'>";
        } else {
            if (index == 0) {
                return " <div class='lab late'>";
            } else if (index == 1) {
                return " <div class='lab absenteeism'>";
            } else if (index == 2) {
                return " <div class='lab early-departure'>";
            } else if (index == 3) {
                return " <div class='lab leave'>";
            } else if (index == 4) {
                return " <div class='lab others'>";
            }
            return " <div class='lab late'>";
        }
    }

    function updateStudent(){
        var loading = layer.load(0, {
            shade: [0.5, '#c0c0c0']
        });
        $.ajax({
            type: "POST",
            url: "/elective/rollcall/updateRollcallData",
            data: {
                teachingClassCode: teachingClassCode,
                teachingClassName: teachingClassName,
                zc: zc,
                xq: xq,
                kj: kj,
                classDate: classDate,
                courseName: courseName,
                teacherUid: teacherUid,
                teacherName: teacherName
            },
            dataType: 'json',
            success: function (data) {
                layer.close(loading);
                if (!data.status) {
                    layer.msg(data.msg)
                    return;
                }
                layer.msg("更新成功！")
                setTimeout(function () {
                    window.location.reload();
                }, 1500)
            }
        })
    }

    function addRollcall(onlyAdd) {
        if (bldm == '2' && today != '0') {
            return false;
        }
        if (today == '2') {
            return false;
        }
        var loading = layer.load(0, {
            shade: [0.5, '#c0c0c0']
        });
        $.ajax({
            type: "POST",
            url: "/elective/rollcall/addRollcall",
            data: {
                array: JSON.stringify(array),
                teachingClassCode: teachingClassCode,
                teachingClassName: teachingClassName,
                zc: zc,
                xq: xq,
                kj: kj,
                onlyAdd: onlyAdd,
                classDate: classDate,
                courseName: courseName,
                teacherUid: teacherUid,
                teacherName: teacherName
            },
            dataType: 'json',
            success: function (data) {
                layer.close(loading);
                // if (!onlyAdd) {
                if (!data.status) {
                    layer.msg(data.msg)
                    return;
                }
                layer.msg("已保存点名结果！")
                if (isForm){
                    var wdm_chidao = 0;
                    var wdm_qingjia = 0;
                    var wdm_kuangke = 0;
                    var wdm_zaotui = 0;
                    var xkjl_sdxssl = 0;
                    for (let ob of array) {
                        xkjl_sdxssl++;
                        if (ob.zt && ob.zt =='1'){
                            wdm_zaotui++;
                        }
                        if (ob.kk && ob.kk =='1'){
                            wdm_kuangke++;
                        }
                        if (ob.qj && ob.qj =='1'){
                            wdm_qingjia++;
                        }
                        if (ob.cd && ob.cd =='1'){
                            wdm_chidao++;
                        }
                    }
                    window.parent.postMessage({
                        "type":"setFieldValue",
                        'data': [
                            {
                                'alias': 'wdm_chidao',
                                'val': [
                                    ''+wdm_chidao
                                ],
                                'compt': 'numberInput'
                            }, {
                                'alias': 'wdm_qingjia',
                                'val': [
                                    ''+wdm_qingjia
                                ],
                                'compt': 'numberInput'
                            },
                            {
                                'alias': 'wdm_kuangke',
                                'val': [
                                    ''+wdm_kuangke
                                ],
                                'compt': 'numberInput'
                            },
                            {
                                'alias': 'wdm_zaotui',
                                'val': [
                                    ''+wdm_zaotui
                                ],
                                'compt': 'numberInput'
                            },
                            {
                                'alias': 'xkjl_sdxssl',
                                'val': [
                                    ''+(xkjl_sdxssl - wdm_qingjia -wdm_kuangke)
                                ],
                                'compt': 'numberInput'
                            }
                        ]
                    },'*');
                    window.parent.postMessage({
                        type: "close"
                    }, '*');
                    return;
                }
                setTimeout(function () {
                    window.history.back();
                }, 1500)
                // }
            }
        });
    }
</script>

</html>