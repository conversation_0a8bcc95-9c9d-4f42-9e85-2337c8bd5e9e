<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>报表打印工具</title>
    <link rel="stylesheet" th:href="@{~/css/form/printUtil/global.css}">
    <link rel="stylesheet" th:href="@{~/plugin/layui/css/layui.css}">
    <link rel="stylesheet" th:href="@{~/css/form/printUtil/reset.css}">
    <link rel="stylesheet" th:href="@{~/css/form/printUtil/common.css}">
    <link rel="stylesheet" th:href="@{~/css/form/printUtil/index.css}">
    <script th:src="@{~/plugin/layui/layui.js}"></script>
    <style>
        @keyframes layui-upbit {
        }

        /* 打印模板设置-排序依据样式 */
        #subFormOrderRule {
            flex-wrap: wrap;
        }

        #subFormOrderRule .layui-form-label {
            float: left;
            text-align: center;
        }

        #subFormOrderRule .layui-input-block ul.order-select {
            width: 100%;
            height: 38px;
        }

        #subFormOrderRule .layui-input-block ul.order-select li {
            float: left;
            width: 40%;
            height: 38px;
            padding-left: 20px;
            background: url("/images/creditManage/radio-icon.png") no-repeat left center;
            line-height: 38px;
            cursor: pointer;
        }

        #subFormOrderRule .layui-input-block ul.order-select li.radio-checked {
            background: url("/images/creditManage/radio-cur-icon.png") no-repeat left center;
        }

        #subFormOrderRule .order-list {
            display: none;
            width: 100%;
            padding-left: 95px;
        }

        #subFormOrderRule .order-list .order-item {
            position: relative;
            height: 30px;
            margin-bottom: 10px;
        }

        #subFormOrderRule .order-list .order-item .title {
            float: left;
            height: 100%;
            margin-right: 5px;
            line-height: 30px;
        }

        #subFormOrderRule .order-list .order-item .subFormField {
            position: relative;
            float: left;
            width: 200px;
            height: 100%;
            margin-right: 10px;
        }

        #subFormOrderRule .order-list .order-item .subFormField input {
            box-sizing: border-box;
            position: relative;
            float: left;
            width: 100%;
            height: 100%;
            padding-left: 10px;
            border: 1px solid #D4D6D9;
            border-radius: 5px;
            line-height: 30px;
            cursor: pointer;
        }

        #subFormOrderRule .order-list .order-item .subFormField ul.field-name-list,
        #subFormOrderRule .order-list .order-item .order-rule ul.field-name-list {
            display: none;
            box-sizing: border-box;
            position: absolute;
            left: 0;
            top: 100%;
            z-index: 10;
            width: 100%;
            margin: 5px 0;
            padding: 6px 0;
            box-shadow: 0px 2px 12px rgba(175, 187, 204, 0.75);
            border-radius: 8px;
            background-color: #fff;
        }

        #subFormOrderRule .order-list .order-item .subFormField ul.field-name-list li,
        #subFormOrderRule .order-list .order-item .order-rule ul.field-name-list li {
            height: 30px;
            padding-left: 10px;
            line-height: 30px;
            cursor: pointer;
        }

        #subFormOrderRule .order-list .order-item .subFormField ul.field-name-list li:hover,
        #subFormOrderRule .order-list .order-item .order-rule ul.field-name-list li:hover {
            background-color: #F5F7FA;
        }

        #subFormOrderRule .order-list .order-item .order-rule {
            position: relative;
            float: left;
            width: 70px;
            height: 100%;
        }

        #subFormOrderRule .order-list .order-item .order-rule input {
            box-sizing: border-box;
            position: relative;
            float: left;
            width: 100%;
            height: 100%;
            padding-left: 10px;
            margin-right: 10px;
            border: 1px solid #D4D6D9;
            border-radius: 5px;
            line-height: 30px;
            cursor: pointer;
        }

        .select-click-before::after {
            display: block;
            display: -webkit-box;
            position: absolute;
            top: 9px;
            right: 9px;
            content: "";
            width: 12px;
            height: 12px;
            background: url("/images/form/printUtil/down-icon.png") no-repeat center;
        }

        .select-click-after::after {
            display: block;
            display: -webkit-box;
            position: absolute;
            top: 9px;
            right: 9px;
            content: "";
            width: 12px;
            height: 12px;
            transform: rotate(180deg);
            transition: all ease .3s;
            background: url("/images/form/printUtil/down-icon.png") no-repeat center;
        }

        #subFormOrderRule .order-list .order-item .add3 {
            float: left;
            width: 20px;
            height: 20px;
            background: url("/images/form/printUtil/add-icon.png") no-repeat center;
            padding-top: 5px;
            margin-left: 5px;
            cursor: pointer;
        }

        #subFormOrderRule .order-list .order-item .delete3 {
            float: left;
            width: 20px;
            height: 20px;
            background: url("/images/form/printUtil/delet-icon.png") no-repeat center;
            padding-top: 5px;
            margin-left: 5px;
            cursor: pointer;
        }
    </style>
</head>
<body>
<div class="yz-header">
    <div class="name">信息上传</div>
    <!--            <div class="login-after" style="display: flex">-->
    <!--                <div class="user-photo">-->
    <!--                    <img th:src="@{~/images/form/printUtil/person-img.png}" alt=""/>-->
    <!--                    <div class="login-opt">-->
    <!--                        <ul>-->
    <!--                            <li id="change-unit">-->
    <!--                                <span class="icon icon-h-unit1"></span><span>切换单位</span>-->
    <!--                            </li>-->
    <!--                            <li class="person-space">-->
    <!--                                <span class="icon icon-h-space"></span><span>个人空间</span>-->
    <!--                            </li>-->
    <!--                            <li id="exit">-->
    <!--                                <span class="icon icon-h-exit"></span><span>退出登录</span>-->
    <!--                            </li>-->
    <!--                        </ul>-->
    <!--                    </div>-->
    <!--                </div>-->
    <!--                <div class="user-mes">-->
    <!--                    <h3>钱佳沂</h3>-->
    <!--                </div>-->
    <!--                <div class="user-arrow"><span class="icon-h-arrow-l"></span></div>-->
    <!--            </div>-->
</div>
<div class="yz-con">
    <div class="yz-scroll layui-form">
        <div class="layui-input-inline" style="display: block; width: 160px; margin: 0 auto">
            <select name="typeCover" lay-filter="typeCover" lay-verify="required" id="formListSelect"
                    lay-search="">
                <!--                        <option value="0">竞赛规则管理</option>-->
            </select>
        </div>
        <div class="yz-menu">
            <div class="menu-list" id="menu-list">
                <input type="text" id="invite_code" value="testAlias" style="display: none;">
                <ul id="fieldListUl">
                    <!--                            <li>-->
                    <!--                                <div class="label-top">-->
                    <!--                                    <div class="name">竞赛类型</div>-->
                    <!--                                    <span class="slide-arrow"></span>-->
                    <!--                                </div>-->
                    <!--                                <div class="label-con">-->
                    <!--                                    <div class="item-list">-->
                    <!--                                        <div class="item">竞赛类型1</div>-->
                    <!--                                        <div class="item">竞赛类型2</div>-->
                    <!--                                        <div class="item">竞赛类型3</div>-->
                    <!--                                        <div class="item">竞赛类型4</div>-->
                    <!--                                    </div>-->
                    <!--                                </div>-->
                    <!--                            </li>-->
                    <!--                            <li>-->
                    <!--                                <div class="label-top no-children">-->
                    <!--                                    <div class="name">竞赛类型代码</div>-->
                    <!--                                </div>-->
                    <!--                            </li>-->
                </ul>
            </div>
        </div>
    </div>
    <div class="yz-main index_page" style="display: block">
        <div class="yz-cons">
            <div class="top">
                <div class="title">
                    <!--                            <div class="back">返回</div>-->
                    <div class="levelone">打印模板设置</div>
                </div>
            </div>
            <div class="cons" id="cons">
                <div class="c-item" id="cTime">
                    <div class="c-top">
                        <h3>上传记录</h3>
                        <div class="btns-list">
                            <span class="add" id="addBtn">添加模板</span>
                        </div>
                    </div>
                    <table class="layui-hide" id="templateTable" lay-filter="templateTable"></table>
                </div>
            </div>
        </div>
    </div>
    <div class="yz-main edit_page" style="display: none">
        <div class="yz-cons">
            <div class="top">
                <div class="title">
                    <div class="back">返回</div>
                    <div class="levelone">打印模板设置</div>
                </div>
                <div class="btn">
                    <div class="cancle">取消</div>
                    <div class="save" lay-submit lay-filter="demo-submit">保存</div>
                </div>
            </div>
            <div class="flex-cons">
                <div class="layui-form" lay-filter="secondaryEditForm">
                    <div class="layui-form-item">
                        <label class="layui-form-label"><em>*</em>模板名称</label>
                        <div class="layui-input-block w240">
                            <input type="text" name="tempName" lay-filter="tempName" placeholder="请输入"
                                   autocomplete="off" class="layui-input" id="templateName">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><em>*</em>上传模板</label>
                        <div class="layui-input-block" style="width: 476px;">
                            <div class="select-file">
                                <div class="btn" id='selectFile'></div>
                                <input type="hidden" id="objectId">
                                <div class="tip">上传格式仅限于doc、docx、pdf,小于20MB</div>
                            </div>
                            <div class="file-list">
                                <ul>
                                    <!--                                            <li>-->
                                    <!--                                                <span class="name">实验研究论文论文论文论文论文论文.pdf</span>-->
                                    <!--                                                <span class="del"></span>-->
                                    <!--                                            </li>-->
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item" style="width: 676px">
                        <label class="layui-form-label"><em>*</em>设置导出文件名</label>
                        <div class="layui-input-block">
                            <div class="sel file_name" style="margin-right:0;">
                                <div class="select-input w240">
                                    <div class="name">请选择</div>
                                    <i></i>
                                    <div class="select-dropdown" style="padding-top:11px;">
                                        <div class="all-selects">
                                            全选
                                        </div>
                                        <ul class="dropdown-lists person-drop" id="exportFileNameUl">
                                            <li>
                                                <span>姓名1</span>
                                                <input type="hidden" class="field-alias" value="aaa">
                                                <p>文本</p>
                                            </li>
                                            <li>
                                                <span>姓名2</span>
                                                <input type="hidden" class="field-alias" value="bbb">
                                                <p>文本</p>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="final" id="exportFileName"></div>
                        <input type="hidden" id="exportFileNameInput">
                        <div class="add file_name"></div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><em>*</em>导出子表单数量</label>
                        <div class="layui-input-block w240">
                            <input type="number" name="numbers" lay-filter="numbers" placeholder="请输入"
                                   autocomplete="off" class="layui-input" id="subFormCount">
                        </div>
                        <div class="tips">（上限值1000）</div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><em></em>数据关联标识</label>
                        <input type="hidden" id="dataAssociationInfo" value="">
                        <div id="dataAssociation">
                            <ul class="clearfix">
                                <li class="layui-input-block">
                                    <div class="sel dataAssociation">
                                        <div class="select-input w240">
                                            <div class="name">请选择关联表单</div>
                                            <i></i>
                                            <div class="select-dropdown" style="padding-top:11px;">
                                                <ul class="dropdown-lists person-drop single_select">
                                                    <li>
                                                        <span>员工信息</span>
                                                        <input type="hidden" class="form-alias" value="aaa">
                                                    </li>
                                                    <li>
                                                        <span>学生信息</span>
                                                        <input type="hidden" class="form-alias" value="bbb">
                                                    </li>
                                                </ul>
                                            </div>
                                            <input type="hidden" class="fieldHtml" value="">
                                        </div>
                                        <div class="reset-form-info">重置关联表单</div>
                                        <ul>
                                            <li>
                                                <div class="select-input w240">
                                                    <div class="name">选择关联字段</div>
                                                    <input type="hidden" class="title-name" value="选择关联字段">
                                                    <i></i>
                                                    <div class="select-dropdown" style="padding-top:11px;">
                                                        <ul class="dropdown-lists person-drop">
                                                            <li>
                                                                <span>请先选择表单</span>
                                                                <input type="hidden" class="field-alias"
                                                                       value="">
                                                                <input type="hidden" class="field-compt"
                                                                       value="">
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>
                                                <div class="condition">等于</div>
                                                <div class="select-input w240">
                                                    <div class="name">选择源字段</div>
                                                    <input type="hidden" class="title-name" value="选择源字段">
                                                    <i></i>
                                                    <div class="select-dropdown" style="padding-top:11px;">
                                                        <ul class="dropdown-lists person-drop">
                                                            <li>
                                                                <span>请先选择表单</span>
                                                                <input type="hidden" class="field-alias"
                                                                       value="">
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>
                                                <div class="add2"></div>
                                                <div class="delete2"></div>
                                            </li>
                                            <li>
                                                <div class="select-input w240">
                                                    <div class="name">选择关联字段</div>
                                                    <i></i>
                                                    <div class="select-dropdown" style="padding-top:11px;">
                                                        <ul class="dropdown-lists person-drop">
                                                            <li>
                                                                <span>请先选择表单</span>
                                                                <input type="hidden" class="field-alias"
                                                                       value="">
                                                                <input type="hidden" class="field-compt"
                                                                       value="">
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>
                                                <div class="condition">等于</div>
                                                <div class="select-input w240">
                                                    <div class="name">选择源字段</div>
                                                    <i></i>
                                                    <div class="select-dropdown" style="padding-top:11px;">
                                                        <ul class="dropdown-lists person-drop">
                                                            <li>
                                                                <span>请先选择表单</span>
                                                                <input type="hidden" class="field-alias"
                                                                       value="">
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>
                                                <div class="add2"></div>
                                                <div class="delete2"></div>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="add1"></div>
                                    <div class="delete1"></div>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="layui-form-item" id="subFormOrderRule">
                        <label class="layui-form-label">子表单-排序依据</label>
                        <div class="layui-input-block w240">
                            <ul class="order-select">
                                <li class="radio-checked">默认</li>
                                <li>自定义</li>
                            </ul>
                            <input type="hidden" id="isEnableOrder" value="0">
                        </div>
                        <input type="hidden" id="orderRuleSetInfo">
                        <div class="order-list">
                            <div class="order-item">
                                <div class="title">排序字段</div>
                                <div class="subFormField select-click-before">
                                    <input type="text" placeholder="请选择子表单字段" readonly class="fieldName">
                                    <input type="hidden" readonly class="fieldAlias">
                                    <ul class="field-name-list">
                                        <li>
                                            <input type="hidden" class="field-alias">
                                            <span class="field-name">成员信息-姓名</span>
                                        </li>
                                        <li>
                                            <input type="hidden" class="field-alias">
                                            <span class="field-name">成员信息-年龄</span>
                                        </li>
                                    </ul>
                                </div>
                                <div class="order-rule select-click-before">
                                    <input type="text" value="升序" readonly class="order">
                                    <ul class="field-name-list">
                                        <li><span class="field-name">升序</span></li>
                                        <li><span class="field-name">降序</span></li>
                                    </ul>
                                </div>
                                <div class="add3"></div>
                            </div>
                            <div class="order-item">
                                <div class="title">排序字段</div>
                                <div class="subFormField select-click-before">
                                    <input type="text" placeholder="请选择子表单字段" readonly class="fieldName">
                                    <input type="hidden" readonly class="fieldAlias">
                                    <ul class="field-name-list">
                                        <li>
                                            <input type="hidden" class="field-alias">
                                            <span class="field-name">成员信息-姓名</span>
                                        </li>
                                        <li>
                                            <input type="hidden" class="field-alias">
                                            <span class="field-name">成员信息-年龄</span>
                                        </li>
                                    </ul>
                                </div>
                                <div class="order-rule select-click-before">
                                    <input type="text" value="升序" readonly class="order">
                                    <ul class="field-name-list">
                                        <li><span class="field-name">升序</span></li>
                                        <li><span class="field-name">降序</span></li>
                                    </ul>
                                </div>
                                <div class="add3"></div>
                                <div class="delete3"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="preview" style="display: none">
                    <h4>预览</h4>
                    <div class="p-cons">
                        <div class="bg">
                            <img th:src="@{~/images/form/printUtil/preview-bg.png}" alt=""/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- 导出模板 -->
<div id="export-template" class="subitemadd popup" style="width: 458px;">
    <div class="title">
        <div class="name">导出模版</div>
        <div class="close"></div>
    </div>
    <div class="popup-con">
        <div class="layui-form" lay-filter="secondaryEditForm">
            <div class="layui-form-item">
                <label class="layui-form-label" style="width: auto;">选择模版样式</label>
                <div class="layui-input-block w240">
                    <select name="name1" lay-filter="name1" lay-verify="required">
                        <option value="">请选择</option>
                        <option value="0">样式一</option>
                        <option value="1">样式二</option>
                        <option value="2">样式三</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item" style="margin-bottom:0;">
                <label class="layui-form-label" style="width: auto;">选择导出格式</label>
                <div class="layui-input-block w275">
                    <input type="radio" name="formatType" lay-filter="formatType" checked value="0"
                           title="Word">
                    <input type="radio" name="formatType" lay-filter="formatType" value="1" title="Excel">
                    <input type="radio" name="formatType" lay-filter="formatType" value="2" title="PDF">
                </div>
            </div>
        </div>
    </div>
    <div class="bottom">
        <button type="reset" class="layui-btn layui-btn-primary exam-cancle">取消</button>
        <button class="layui-btn exam-sure">确定</button>
    </div>
</div>
<!-- 自定义文件名 -->
<div id="customFileName" class="subitemadd popup" style="width: 458px;">
    <div class="title">
        <div class="name">新增</div>
        <div class="close"></div>
    </div>
    <div class="popup-con">
        <div class="layui-form" lay-filter="secondaryEditForm">
            <div class="layui-form-item">
                <label class="layui-form-label" style="width: auto;">文件名</label>
                <div class="layui-input-block w240">
                    <input type="text" name="numbers" lay-filter="numbers" placeholder="请输入" autocomplete="off"
                           class="layui-input">
                </div>
            </div>
        </div>
    </div>
    <div class="bottom">
        <button type="reset" class="layui-btn layui-btn-primary exam-cancle">取消</button>
        <button class="layui-btn exam-sure">确定</button>
    </div>
</div>
<div >
    <a id="downloadHide" href="">下载模板</a>
</div>
<script type="text/html" id="barTable">
    {{#  if(d.isDefaultTemplate==0){ }}
    <a style="color: #4C88FF;cursor:pointer;margin-right:16px;" lay-event="cancelDefault">取消默认模板</a>
    {{#  } }}
    {{#  if(d.isDefaultTemplate==1){ }}
    <a style="color: #4C88FF;cursor:pointer;margin-right:16px;" lay-event="setDefault">设为默认模板</a>
    {{#  } }}
    {{#  if(d.template_file_type!='pdf' && d.resid!=null && d.resid!=''){ }}
    <a style="color: #4C88FF;cursor:pointer;margin-right:16px;" lay-event="editTemplate">编辑模板</a>
    {{#  } }}
    <span lay-event="download" style="color: #4C88FF;cursor: pointer;margin-right:16px;">下载</span>
    <span lay-event="edit" style="color: #4C88FF;cursor: pointer;margin-right:16px;">编辑</span>
    <span lay-event="del" style="color:#F33131;cursor: pointer;">删除</span>
</script>
<script th:src="@{~/js/jquery-3.3.1.min.js}"></script>
<script th:src="@{~/js/my.util.js}"></script>
<script>
    // 立即执行函数，进入页面即执行
    $(function () {
        // 记录选中表单的表单名称和接口标识
        let formNameAndAliasSelect = "---";
        // 记录选中表单的名称
        let formName = "---";
        // 记录选中表单的接口标识
        let formAlias = "---";
        // 记录当前编辑的模板数据id
        let templateId;
        // 该单位下所有的表单信息，初始化页面后加载
        let formListData = [];
        // 该单位下当前选择表单的属性，初始化页面后加载
        let formFieldListData = [];
        // 该单位下当前选择表单的子表单属性，初始化页面后加载
        let subFormFieldListData = [];

        // 左侧边栏，表单字段项点击事件
        $(".yz-con").on("click", ".yz-menu .menu-list ul li .label-top",
            function (e) {
                if ($(this).hasClass("no-children")) {
                    $(".yz-con .yz-menu .menu-list ul li .cur").removeClass("cur");
                    $(this).addClass("cur");
                    stopBubble(e);
                } else {
                    $(this).toggleClass("active");
                    $(this).parent().find(".label-con").stop().slideToggle();
                }
                var fieldAlias = $(this).find(".field-alias").val();
                $("#invite_code").val("${" + formAlias + ":" + fieldAlias + "}");
                copyToClipboard('invite_code');
            }
        );

        // 左侧边栏，表单字段项-二级分项的点击事件
        $(".yz-con").on("click", ".yz-menu .menu-list ul li .label-con .item-list .item",
            function (e) {
                $(".yz-con .yz-menu .menu-list ul li .cur").removeClass("cur");
                $(this).addClass("cur");
                var fieldAlias = $(this).find(".field-alias").val();
                $("#invite_code").val("${" + formAlias + ":" + fieldAlias + "}");
                copyToClipboard('invite_code');
                stopBubble(e);
            }
        );

        // 点击空白隐藏弹窗
        $(document).on("click", function (event) {
            var _con = $('.select-input');
            var _cons = $('.time-sel');
            if (!_con.is(event.target) && _con.has(event.target).length === 0) {
                $(".select-input").removeClass("clicked");
            }
            if (!_cons.is(event.target) && _cons.has(event.target).length === 0) {
                $(".time-sel").removeClass("clicked");
            }
        });

        // 设置导出文件名的下拉点击
        $(".select-input").on("click", ".name", function (e) {
            $(this).parents(".sel.file_name").siblings().find(".select-input").removeClass("clicked");
            $(this).parent().toggleClass("clicked");
            stopBubble(e);
        });

        // 数据关联标识下拉框点击事件
        $("#dataAssociation>ul").on("click", "li .name", function (e) {
            $(this).parents(".sel.file_name").siblings().find(".select-input").removeClass("clicked");
            $(this).parent().toggleClass("clicked");
            stopBubble(e);
        });

        // 设置导出文件名的多选下拉
        let txtarr = [];

        // 设置导出文件名的下拉li点击事件
        $(".sel.file_name .select-input").on("click", ".select-dropdown .dropdown-lists li", function () {
            $(this).toggleClass("cur");
            let words = $(this).find("span").text();
            let fieldAlias = $(this).find(".field-alias").val();
            let fieldType = $(this).find("p").text();
            if ($(this).hasClass("cur")) {
                txtarr.push(words);
                var htmls = '<span>' +
                    '<em>' + words + '</em>' +
                    '<i></i>' +
                    '<input type="hidden" class="field-alias" value="' + (fieldAlias ? fieldAlias : "") + '">' +
                    '<input type="hidden" class="field-type" value="' + (fieldType ? fieldType : "") + '">' +
                    '</span>';
                $(".final").append(htmls);
            } else {
                txtarr.forEach((item, index) => {
                    if (item === words) {
                        txtarr.splice(index, 1);
                    }
                });
                $(".final span").each(function () {
                    if ($(this).find("em").text() === words) {
                        $(this).remove();
                    }
                });
            }
            calcFn();
        });

        // 设置导出文件名下拉框-全选
        $(".yz-con").on("click", ".sel.file_name .select-input .select-dropdown .all-selects", function () {
            var selectObj = [];
            $(this).toggleClass("cur");
            if ($(this).hasClass("cur")) {
                $(this).text("取消全选");
                $(this).next().find("li").addClass("cur");
                $(".sel.file_name .select-input .select-dropdown .dropdown-lists.person-drop li").each(function () {
                    if ($(this).hasClass("cur")) {
                        txtarr.push($(this).find("span").text());
                        selectObj.push({
                            "name": $(this).find("span").text(),
                            "fieldAlias": $(this).find(".field-alias").val(),
                            "fieldType": $(this).find("p").text(),
                        });

                    }
                });
            } else {
                $(this).text("全选");
                $(this).next().find("li").removeClass("cur");
                txtarr = [];
            }
            $(".final span:not(.nadd)").remove();
            selectObj.forEach(item => {
                var htmls = '<span>' +
                    '<em>' + item.name + '</em>' +
                    '<i></i>' +
                    '<input type="hidden" class="field-alias" value="' + (item.fieldAlias ? item.fieldAlias : "") + '">' +
                    '<input type="hidden" class="field-type" value="' + (item.fieldType ? item.fieldType : "") + '">' +
                    '</span>'
                $(".final").append(htmls);
            });
            calcFn();
        });

        // 点击+号，弹窗可以自定义设置导出文件名
        $(".layui-form-item .add.file_name").click(function () {
            $("#customFileName input").val("");
            layer.open({
                type: 1,
                title: false,
                closeBtn: false,
                shadeClose: true,
                isOutAnim: true,
                content: $('#customFileName'),
                area: ['auto', 'auto'],
                success: function () {
                },
            }, function () {
            });
        });

        // 自定义文件名，点击确定
        $("#customFileName .exam-sure").click(function () {
            var index = $(this).parents(".layui-layer").attr("times");
            layer.close(index);
            var values = $("#customFileName input").val();
            var htmls = '<span class="nadd">' +
                '<em>' + values + '</em>' +
                '<i></i>' +
                '</span>';
            $(".final").append(htmls);
        });

        // 点击文件名预览框内分项右上方的x，删除
        $(".final").on("click", "span i", function () {
            let txts = $(this).parent().find("em").text();
            if (!$(this).parent().hasClass("nadd")) {
                removeSelect(txts);
            }
            $(this).parent().remove();
        });

        // 绑定数据关联标识字段的一些事件
        function bandDataAssociationEvent() {
            // 数据关联标识：点击请选择关联表单
            $("#dataAssociation>ul").on("click", ".dataAssociation>div:nth-child(1) .select-dropdown>ul li", function () {
                $(this).addClass("cur");
                $(this).siblings("li").removeClass("cur");
                var oldValue = $(this).parents(".select-input").find(".name").text();
                var value = $(this).find("span").text();
                // 重复点击同一个表单，不刷新下拉框内容
                if (oldValue === value) {
                    return;
                } else {
                    var tempName = $(this).parents("div.select-input").next("div.select-input").find(".name");
                    $(tempName).text("选择字段");
                    $(tempName).removeClass("ckd");
                }
                $(this).parents(".select-input").find(".name").addClass("ckd");
                $(this).parents(".select-input").find(".name").text(value);
                // 选择了表单之后，渲染表单字段到下拉框
                var formAlias = $(this).find(".form-alias").val();
                // 查找相邻的一个ul，即请选择字段下拉框
                var formFieldSelectUl = $(this).parents("li.layui-input-block")
                    .find("div.dataAssociation>ul>li")
                    .find("div.select-input:nth-child(1) ul.dropdown-lists");
                var nameDivEleList = $(this).parents("li.layui-input-block")
                    .find("div.dataAssociation>ul>li")
                    .find("div.name");
                var fieldHtmlEle = $(this).parents("li.layui-input-block")
                    .find("div.dataAssociation")
                    .find(".fieldHtml");
                $.ajax({
                    type: 'post',
                    dataType: 'json',
                    url: '/form/print/util/form/detail',
                    data: {"formAlias": formAlias},
                    success: function (res) {
                        if (res.code === 200) {
                            $(formFieldSelectUl).empty();
                            var tempLi = "";
                            var formDetailList = res.data.formDetailList;
                            for (var i = 0; i < formDetailList.length; i++) {
                                var singleFormField = formDetailList[i];
                                if ("normal" === singleFormField.type) {
                                    tempLi += '<li>' +
                                        '   <span>' + singleFormField.label + '</span>' +
                                        '   <input type="hidden" class="field-alias" value="' + singleFormField.fieldAlias + '">' +
                                        '   <input type="hidden" class="field-compt" value="' + singleFormField.compt + '">' +
                                        '</li>';
                                }
                            }
                            $(formFieldSelectUl).append(tempLi);
                            $(fieldHtmlEle).val(tempLi);
                            if (nameDivEleList && nameDivEleList.length > 0) {
                                for (var i = 0; i < nameDivEleList.length; i++) {
                                    var nameDivEle = nameDivEleList[i];
                                    $(nameDivEle).removeClass("ckd");
                                    $(nameDivEle).text($(nameDivEle).parent("div.select-input").find(".title-name").val());
                                }
                            }
                        } else {
                            layer.msg(res.msg, {icon: 2, time: 2000});
                        }
                    }
                });
            });

            // 数据关联标识：点击请选择关联字段 or 点击请选择源字段
            $("#dataAssociation>ul").on("click", ".dataAssociation>ul>li .select-dropdown>ul li", function () {
                $(this).addClass("cur");
                $(this).siblings("li").removeClass("cur");
                var textValue = $(this).find("span").text();
                if (textValue !== "") {
                    $(this).parents(".select-input").find(".name").addClass("ckd");
                    $(this).parents(".select-input").find(".name").text(textValue);
                } else {
                    $(this).parents(".select-input").find(".name").removeClass("ckd");
                    $(this).parents(".select-input").find(".name").text(
                        $(this).parents(".select-input").find(".title-name").val()
                    );
                }
            });

            // 点击重置关联表单按钮
            $("#dataAssociation>ul").on("click", "li .reset-form-info", function () {
                // 重置关联表单
                var fieldHtml = '<li>' +
                    '   <span>请选择关联表单</span>' +
                    '   <input type="hidden" class="field-alias" value="">' +
                    '   <input type="hidden" class="field-compt" value="">' +
                    '</li>';
                var divName = $(this).parents(".dataAssociation").find(">div.select-input .name");
                $(divName).text("选择关联表单");
                $(divName).removeClass("ckd");
                $(this).parents(".dataAssociation").find(">div.select-input ul.dropdown-lists>li").removeClass("cur");
                $(this).parents(".dataAssociation").find(">div.select-input input").val("");
                $(this).parents(".dataAssociation").find(">div.select-input .fieldHtml").val(fieldHtml);
                // 重置关联表单的字段信息
                var fieldListUl = $(this).parents(".dataAssociation").find(">ul");
                var fieldFirstLi = $(fieldListUl).find(">li:nth-child(1)");
                $(fieldFirstLi).siblings("li").remove();
                $(fieldFirstLi).find(">div.select-input:nth-child(1) .name").removeClass("ckd").text("选择关联字段");
                var fieldFirstLiLi = $(fieldFirstLi).find(">div.select-input:nth-child(1) ul.dropdown-lists").find(">li:nth-child(1)");
                $(fieldFirstLiLi).siblings("li").remove();
                $(fieldFirstLiLi).removeClass("cur").find("span").text("请选择关联表单");
                $(fieldFirstLiLi).find("input").val("");
                $(fieldFirstLi).find(">div.select-input:nth-child(3) .name").removeClass("ckd").text("选择源字段");
                fieldFirstLiLi = $(fieldFirstLi).find(">div.select-input:nth-child(3) ul.dropdown-lists").find(">li:nth-child(1)");
                $(fieldFirstLiLi).removeClass("cur").siblings("li").removeClass("cur");
            });

            // 点击添加数据关联标识
            $("#dataAssociation>ul").on("click", "li .add1", function () {
                addDataAssociationInfo();
            });

            // 点击删除数据关联标识
            $("#dataAssociation>ul").on("click", "li .delete1", function () {
                var _this = this;
                U.confirm({
                    title: "提示",
                    msg: "删除数据关联标识？",
                    sureBtnTxt: '确定',
                    cancelBtnTxt: '取消',
                    sure: function () {
                        // 点击 确定 按钮时执行的方法
                        $(_this).parent().remove();
                    },
                    cancel: function () {
                        U.closePop();
                    }
                });
            });

            // 点击添加字段关联
            $("#dataAssociation>ul").on("click", "li .dataAssociation>ul>li .add2", function () {
                addDataAssociationFieldInfo(this);
            });
            // 点击删除字段关联
            $("#dataAssociation>ul").on("click", "li .dataAssociation>ul>li .delete2", function () {
                var _this = this;
                U.confirm({
                    title: "提示",
                    msg: "删除字段关联标识？",
                    sureBtnTxt: '确定',
                    cancelBtnTxt: '取消',
                    sure: function () {
                        // 点击 确定 按钮时执行的方法
                        $(_this).parent().remove();
                    },
                    cancel: function () {
                        U.closePop();
                    }
                });
            });

            // 点击排序依据-默认或自定义
            $("#subFormOrderRule .layui-input-block ul.order-select").on("click", "li", function () {
                if (!$(this).hasClass("radio-checked")) {
                    $(this).addClass("radio-checked")
                    $(this).siblings("li").removeClass("radio-checked");
                }
                if ($(this).text() === "自定义") {
                    $("#subFormOrderRule .order-list").show();
                } else {
                    $("#subFormOrderRule .order-list").hide();
                }
            });

            // 点击子表单字段下拉框弹出或隐藏
            $("#subFormOrderRule .order-list").on("click", ".order-item .subFormField", function () {
                if ($(this).hasClass("select-click-before")) {
                    $(this).removeClass("select-click-before");
                    $(this).addClass("select-click-after");
                    $(this).find(".field-name-list").show();
                } else {
                    $(this).removeClass("select-click-after");
                    $(this).addClass("select-click-before");
                    $(this).find(".field-name-list").hide();
                }
            });

            // 点击排序下拉框弹出或隐藏
            $("#subFormOrderRule .order-list").on("click", ".order-item .order-rule", function () {
                if ($(this).hasClass("select-click-before")) {
                    $(this).removeClass("select-click-before");
                    $(this).addClass("select-click-after");
                    $(this).find(".field-name-list").show();
                } else {
                    $(this).removeClass("select-click-after");
                    $(this).addClass("select-click-before");
                    $(this).find(".field-name-list").hide();
                }
            });

            // 点击子表单字段下拉框选项
            $("#subFormOrderRule .order-list").on("click", ".order-item .subFormField .field-name-list li", function () {
                var fieldName = $(this).find("span.field-name").text();
                var fieldAlias = $(this).find(".field-alias").val();
                $(this).parents(".subFormField").find(".fieldName").val(fieldName);
                $(this).parents(".subFormField").find(".fieldAlias").val(fieldAlias);
            });

            // 点击排序下拉框选项
            $("#subFormOrderRule .order-list").on("click", ".order-item .order-rule .field-name-list li", function () {
                var fieldName = $(this).find("span.field-name").text();
                $(this).parents(".order-rule").find(".order").val(fieldName);
            });

            // 点击新增排序字段
            $("#subFormOrderRule .order-list").on("click", ".order-item .add3", function () {
                addOrderItemFieldInfo(this);
            });

            // 点击删除排序字段
            $("#subFormOrderRule .order-list").on("click", ".order-item .delete3", function () {
                $(this).parents(".order-item").remove();
            });
        }

        // 添加数据关联标识数据
        function addDataAssociationInfo(dataAssociationInfo, isFirst) {
            var bandFormName = "";
            var bandFormAlias = "";
            var bandFieldList = "";
            if (dataAssociationInfo) {
                bandFormName = dataAssociationInfo.bandFormName;
                bandFormAlias = dataAssociationInfo.bandFormAlias;
                bandFieldList = dataAssociationInfo.bandFieldList;
            }
            // 渲染关联表单下拉框内容
            var formLiHtml = "";
            var currentFormName = "";
            for (let i = 0; i < formListData.length; i++) {
                var formSingleData = formListData[i];
                formLiHtml += '<li ' + (bandFormAlias === formSingleData.formAlias ? 'class="cur"' : '') + '>' +
                    '   <span>' + formSingleData.name + '</span>' +
                    '   <input type="hidden" class="form-alias" value="' + formSingleData.formAlias + '">' +
                    '</li>';
                if (bandFormAlias === formSingleData.formAlias) {
                    currentFormName = formSingleData.name;
                }
            }
            // 渲染勾选的关联字段内容
            var fieldHtml = '<li>' +
                '   <span>请选择关联表单</span>' +
                '   <input type="hidden" class="field-alias" value="">' +
                '   <input type="hidden" class="field-compt" value="">' +
                '</li>';
            // 生成源字段下拉框内容
            var fieldSourceLiHtml = '';
            if (formFieldListData && formFieldListData.length > 0) {
                for (var i = 0; i < formFieldListData.length; i++) {
                    var singleFormField = formFieldListData[i];
                    if ("normal" === singleFormField.type) {
                        fieldSourceLiHtml += '<li>' +
                            '   <span>' + singleFormField.label + '</span>' +
                            '   <input type="hidden" class="field-alias" value="' + singleFormField.fieldAlias + '">' +
                            '</li>';
                    }
                }
            }
            // 关联属性的初始内容
            var bandFieldListHtml = '<li>' +
                '<div class="select-input w240">' +
                '    <div class="name">选择关联字段</div>' +
                '    <input type="hidden" class="title-name" value="选择关联字段">' +
                '    <i></i>' +
                '    <div class="select-dropdown" style="padding-top: 11px">' +
                '        <ul class="dropdown-lists person-drop">' +
                fieldHtml +
                '        </ul>' +
                '    </div>' +
                '</div>' +
                '<div class="condition">等于</div>' +
                '<div class="select-input w240">' +
                '    <div class="name">选择源字段</div>' +
                '    <input type="hidden" class="title-name" value="选择源字段">' +
                '    <i></i>' +
                '    <div class="select-dropdown" style="padding-top: 11px">' +
                '        <ul class="dropdown-lists person-drop">' +
                fieldSourceLiHtml +
                '        </ul>' +
                '    </div>' +
                '</div>' +
                '<div class="add2"></div>' +
                // '<div class="delete2"></div>' +
                '</li>';
            var fieldHtmlHidden = '';
            // 如果设置过数据关联标识，则需要额外渲染
            if (bandFieldList && bandFieldList.length) {
                bandFieldListHtml = '';
                // 同步请求关联的单个表单的属性结构
                var formFieldDataList = "";
                $.ajax({
                    type: 'post',
                    dataType: 'json',
                    url: '/form/print/util/form/detail',
                    data: {"formAlias": bandFormAlias},
                    async: false,
                    success: function (res) {
                        if (res.code === 200) {
                            formFieldDataList = res.data.formDetailList;
                            for (var j = 0; j < formFieldDataList.length; j++) {
                                var singleFormField = formFieldDataList[j];
                                if ("normal" === singleFormField.type) {
                                    fieldHtmlHidden += '<li>' +
                                        '   <span>' + singleFormField.label + '</span>' +
                                        '   <input type="hidden" class="field-alias" value="' + singleFormField.fieldAlias + '">' +
                                        '   <input type="hidden" class="field-compt" value="' + singleFormField.compt + '">' +
                                        '</li>';
                                }
                            }
                        } else {
                            layer.msg(res.msg, {icon: 2, time: 2000});
                        }
                    }
                });
                for (var i = 0; i < bandFieldList.length; i++) {
                    var bandField = bandFieldList[i];
                    // 生成关联字段下拉框内容
                    fieldHtml = "";
                    for (var j = 0; j < formFieldDataList.length; j++) {
                        var singleFormField = formFieldDataList[j];
                        if ("normal" === singleFormField.type) {
                            fieldHtml += '<li ' + (bandField.bandFieldAlias === singleFormField.fieldAlias ? 'class="cur"' : '') + '>' +
                                '   <span>' + singleFormField.label + '</span>' +
                                '   <input type="hidden" class="field-alias" value="' + singleFormField.fieldAlias + '">' +
                                '   <input type="hidden" class="field-compt" value="' + singleFormField.compt + '">' +
                                '</li>';
                        }
                    }
                    // 生成源字段下拉框内容
                    fieldSourceLiHtml = '';
                    if (formFieldListData && formFieldListData.length > 0) {
                        for (var j = 0; j < formFieldListData.length; j++) {
                            var singleFormField = formFieldListData[j];
                            if ("normal" === singleFormField.type) {
                                fieldSourceLiHtml += '<li ' + (bandField.sourceFieldAlias === singleFormField.fieldAlias ? 'class="cur"' : '') + '>' +
                                    '   <span>' + singleFormField.label + '</span>' +
                                    '   <input type="hidden" class="field-alias" value="' + singleFormField.fieldAlias + '">' +
                                    '</li>';
                            }
                        }
                    }
                    bandFieldListHtml += '<li>' +
                        '<div class="select-input w240">' +
                        '    <div class="name ckd">' + bandField.bandFieldName + '</div>' +
                        '    <input type="hidden" class="title-name" value="选择关联字段">' +
                        '    <i></i>' +
                        '    <div class="select-dropdown" style="padding-top: 11px">' +
                        '        <ul class="dropdown-lists person-drop">' +
                        fieldHtml +
                        '        </ul>' +
                        '    </div>' +
                        '</div>' +
                        '<div class="condition">等于</div>' +
                        '<div class="select-input w240">' +
                        '    <div class="name ckd">' + bandField.sourceFieldName + '</div>' +
                        '    <input type="hidden" class="title-name" value="选择源字段">' +
                        '    <i></i>' +
                        '    <div class="select-dropdown" style="padding-top: 11px">' +
                        '        <ul class="dropdown-lists person-drop">' +
                        fieldSourceLiHtml +
                        '        </ul>' +
                        '    </div>' +
                        '</div>' +
                        '<div class="add2"></div>' +
                        (i === 0 ? '' : '<div class="delete2"></div>') +
                        '</li>';
                }
            }
            var html = '<li class="layui-input-block">' +
                '   <div class="sel dataAssociation clearfix">' +
                '       <div class="select-input w240">' +
                '           <div class="name ' + (bandFormName ? 'ckd' : '') + '">' + (bandFormName ? bandFormName : '选择关联表单') + '</div>' +
                '           <i></i>' +
                '           <div class="select-dropdown" style="padding-top: 11px;">' +
                '               <ul class="dropdown-lists person-drop">' +
                formLiHtml +
                '               </ul>' +
                '           </div>' +
                '           <input type="hidden" class="fieldHtml" value="">' +
                '       </div>' +
                '<div class="reset-form-info">重置关联表单</div>' +
                '       <ul>' +
                bandFieldListHtml +
                '       </ul>' +
                '   </div>' +
                '   <div class="add1"></div>' +
                (isFirst ? '' : '   <div class="delete1"></div>') +
                '</li>';
            var ulEle = $("#dataAssociation>ul");
            var htmlEle = $(html);
            $(htmlEle).find(".fieldHtml").val(fieldHtmlHidden);
            ulEle.append(htmlEle);
        }

        // 添加排序依据-子表单关联排序字段
        function addSubFormFieldInfo(subFormFieldInfo, isFirst) {
            var subFormFieldListData = [];
            if (formFieldListData && formFieldListData.length > 0) {
                for (var i = 0; i < formFieldListData.length; i++) {
                    var singleFormField = formFieldListData[i];
                    if ("subForm" === singleFormField.type) {
                        subFormFieldListData = subFormFieldListData.concat(singleFormField.subFieldList);
                    }
                }
            }
            var liHtml = '';
            if (subFormFieldListData && subFormFieldListData.length) {
                for (let i = 0; i < subFormFieldListData.length; i++) {
                    var subFormField = subFormFieldListData[i];
                    liHtml += "<li>" +
                        "   <span class='field-name' title='" + subFormField.parentFieldLabel + "-" + subFormField.label + "' style='display:block; overflow: hidden;text-overflow: ellipsis;white-space: nowrap'>" + subFormField.parentFieldLabel + "-" + subFormField.label + "</span>" +
                        "   <input type='hidden' class='field-alias' value='" + subFormField.bandFieldAlias + "'>" +
                        "</li>";
                }
            }
            var htmlEle = "<div class='order-item'>" +
                "    <div class='title'>排序字段</div>" +
                "    <div class='subFormField select-click-before'>" +
                "        <input type='text' placeholder='请选择子表单字段' readonly class='fieldName' value='" + (subFormFieldInfo ? subFormFieldInfo.fieldName : "") + "'>" +
                "        <input type='hidden' class='fieldAlias' value='" + (subFormFieldInfo ? subFormFieldInfo.fieldAlias : "") + "'>" +
                "        <ul class='field-name-list'>" +
                liHtml +
                "        </ul>" +
                "    </div>" +
                "    <div class='order-rule select-click-before'>" +
                "        <input type='text' readonly class='order' value='" + (subFormFieldInfo ? subFormFieldInfo.order : "升序") + "'>" +
                "        <ul class='field-name-list'>" +
                "            <li><span class='field-name'>升序</span></li>" +
                "            <li><span class='field-name'>降序</span></li>" +
                "        </ul>" +
                "    </div>" +
                "    <div class='add3'></div>" +
                (isFirst ? "" : "    <div class='delete3'></div>") +
                "</div>";
            $("#subFormOrderRule .order-list").append(htmlEle);
        }

        // 添加字段关联标识数据
        function addDataAssociationFieldInfo(_this) {
            // 生成源字段下拉框值
            var fieldSourceLiHtml = '';
            if (formFieldListData && formFieldListData.length > 0) {
                for (var i = 0; i < formFieldListData.length; i++) {
                    var singleFormField = formFieldListData[i];
                    if ("normal" === singleFormField.type) {
                        fieldSourceLiHtml += '<li>' +
                            '   <span>' + singleFormField.label + '</span>' +
                            '   <input type="hidden" class="field-alias" value="' + singleFormField.fieldAlias + '">' +
                            '</li>';
                    }
                }
            }
            var fieldHtml = $(_this).parents("li.layui-input-block").find(".fieldHtml").val();
            fieldHtml = fieldHtml ? fieldHtml : '<li>' +
                '   <span>请选择关联表单</span>' +
                '   <input type="hidden" class="field-alias" value="">' +
                '   <input type="hidden" class="field-compt" value="">' +
                '</li>';

            var html = '<li>' +
                '   <div class="select-input w240">' +
                '       <div class="name">选择关联字段</div>' +
                '       <input type="hidden" class="title-name" value="选择关联字段">' +
                '       <i></i>' +
                '       <div class="select-dropdown" style="padding-top: 11px">' +
                '           <ul class="dropdown-lists person-drop">' +
                fieldHtml +
                '           </ul>' +
                '       </div>' +
                '   </div>' +
                '   <div class="condition">等于</div>' +
                '   <div class="select-input w240">' +
                '       <div class="name">选择源字段</div>' +
                '       <input type="hidden" class="title-name" value="选择源字段">' +
                '       <i></i>' +
                '       <div class="select-dropdown person-drop">' +
                '           <ul class="dropdown-lists person-drop">' +
                fieldSourceLiHtml +
                '           </ul>' +
                '       </div>' +
                '   </div>' +
                '   <div class="add2"></div>' +
                '   <div class="delete2"></div>' +
                '</li>';
            var ulEle = $(_this).parent().parent();
            ulEle.append(html);
        }

        // 添加字段关联标识数据
        function addOrderItemFieldInfo(_this) {
            var subFormFieldListData = [];
            if (formFieldListData && formFieldListData.length > 0) {
                for (var i = 0; i < formFieldListData.length; i++) {
                    var singleFormField = formFieldListData[i];
                    if ("subForm" === singleFormField.type) {
                        subFormFieldListData = subFormFieldListData.concat(singleFormField.subFieldList);
                    }
                }
            }
            var liHtml = '';
            if (subFormFieldListData && subFormFieldListData.length) {
                for (let i = 0; i < subFormFieldListData.length; i++) {
                    var subFormField = subFormFieldListData[i];
                    liHtml += "<li>" +
                        "   <span class='field-name'>" + subFormField.parentFieldLabel + "-" + subFormField.label + "</span>" +
                        "   <input type='hidden' class='field-alias' value='" + subFormField.bandFieldAlias + "'>" +
                        "</li>";
                }
            }
            var htmlEle = "<div class='order-item'>" +
                "    <div class='title'>排序字段</div>" +
                "    <div class='subFormField select-click-before'>" +
                "        <input type='text' placeholder='请选择子表单字段' readonly class='fieldName'>" +
                "        <input type='hidden' class='fieldAlias'>" +
                "        <ul class='field-name-list'>" +
                liHtml +
                "        </ul>" +
                "    </div>" +
                "    <div class='order-rule select-click-before'>" +
                "        <input type='text' value='升序' readonly class='order'>" +
                "        <ul class='field-name-list'>" +
                "            <li><span class='field-name'>升序</span></li>" +
                "            <li><span class='field-name'>降序</span></li>" +
                "        </ul>" +
                "    </div>" +
                "    <div class='add3'></div>" +
                "    <div class='delete3'></div>" +
                "</div>";
            $(_this).parents(".order-list").append(htmlEle);
        }

        // 从下拉框中删除指定文本的下拉选项
        function removeSelect(tt) {
            $(".sel.file_name .select-input .select-dropdown .dropdown-lists.person-drop li").each(function () {
                if ($(this).find("span").text() === tt) {
                    $(this).removeClass("cur");
                }
            });
            txtarr.forEach((item, index) => {
                if (item === tt) {
                    txtarr.splice(index, 1);
                }
            });
            calcFn();
        }

        // 设置页面各个模块的高度
        ifHH();

        function ifHH() {
            let iframe = $(window).height() - $(".yz-header").height() - 40;
            $(".yz-menu").css("height", iframe - 91);
            $(".yz-cons").css("height", iframe);
            $("#cons").css("height", iframe - 61);
        }

        // 当浏览器页面大小改变时，刷新各个模块的高度
        $(window).resize(function () {
            ifHH();
        });

        // 计算设置导出文件名的结果
        function calcFn() {
            if (txtarr.length === 0) {
                $(".sel.file_name .select-input .name").removeClass("ckd").text("请选择");
                $(".sel.file_name .select-input .select-dropdown .dropdown-lists.person-drop .cur").removeClass("cur");
            } else {
                $(".sel.file_name .select-input .name").addClass("ckd").text(txtarr.join(","));
            }
            let total = $(".sel.file_name .select-input .select-dropdown .dropdown-lists.person-drop li").length;
            let currents = $(".sel.file_name .select-input .select-dropdown .dropdown-lists.person-drop .cur").length;
            if (total === currents) {
                $(".sel.file_name .select-input .select-dropdown .all-selects").text("取消全选").addClass("cur");
            } else {
                $(".sel.file_name .select-input .select-dropdown .all-selects").text("全选").removeClass("cur");
            }
        }

        /**
         * 复制指定id的元素的内容到剪贴板
         */
        function copyToClipboard(elementId) {
            // 创建元素用于复制
            var aux = document.createElement("input");
            // 获取复制内容
            var content = document.getElementById(elementId).value;
            // 设置元素内容
            aux.setAttribute("value", content);
            // 将元素插入页面进行调用
            document.body.appendChild(aux);
            // 复制内容
            aux.select();
            //复制选中的文字到剪贴板;
            // document.execCommand('copy');
            try {
                document.execCommand('copy');
                // 删除创建元素
                document.body.removeChild(aux);
                layer.msg("复制成功", {icon: 1, time: 2000});
            } catch (exception) {
                layer.msg("复制失败", {icon: 2, time: 2000});
            }
        }

        // 停止冒泡事件
        function stopBubble(e) {
            if (e && e.stopPropagation) {
                e.stopPropagation();
            } else {
                window.event.cancelBubble = true;
            }
        }

        layui.use(["form", "upload", "table"], function () {
            var $ = layui.jquery;
            var form = layui.form;
            var upload = layui.upload;
            var table = layui.table;

            // 列表数据示例
            var tabData = [
                // {
                //     id: 0,
                //     templateName: "学生成绩单1.docx",
                //     isDefaultTemplate: 0,
                //     objectId: "123",
                // }, {
                //     id: 1,
                //     templateName: "学生成绩单2.docx",
                //     isDefaultTemplate: 1,
                //     objectId: "123",
                // }, {
                //     id: 2,
                //     templateName: "学生成绩单3.docx",
                //     isDefaultTemplate: 1,
                //     objectId: "123",
                // }, {
                //     id: 3,
                //     templateName: "学生成绩单4.docx",
                //     isDefaultTemplate: 1,
                //     objectId: "123",
                // }
            ];

            // 渲染上传记录的表格
            table.render({
                elem: '#templateTable',
                // url: '../../demo/table/user/-page=1&limit=20.js',
                data: tabData,
                cols: [
                    [
                        {
                            field: 'templateName',
                            title: '模板名称',
                            align: "center",
                        },
                        {
                            field: 'templateFileName',
                            title: '文件名称',
                            align: "center",
                        },
                        {
                            field: 'objectId',
                            title: '云盘objectId',
                            align: "center",
                            hide: true
                        },
                        {
                            title: '操作',
                            toolbar: '#barTable',
                            align: "center",
                        }
                    ]
                ],
                page: true,
            });

            // 工具条操作事件
            table.on("tool(templateTable)", function (obj) {
                var data = obj.data; //获得当前行数据
                let currentId = data.id;
                if (obj.event === "del") {
                    layer.confirm("确认删除【" + data.templateName + "】模版？", function (index) {
                        obj.del();
                        layer.close(index);
                        templateDelete([currentId]);
                    });
                } else if (obj.event === 'setDefault') {
                    layer.confirm("确认设置【" + data.templateName + "】为默认模版？", function (index) {
                        layer.close(index);
                        setTemplateDefaultStatus(currentId, 0);
                    });
                } else if (obj.event === 'cancelDefault') {
                    layer.confirm("确认取消【" + data.templateName + "】为默认模版？", function (index) {
                        layer.close(index);
                        setTemplateDefaultStatus(currentId, 1);
                    });
                } else if (obj.event === "edit") {
                    echoTemplateDetailInfo(currentId);
                    $(".yz-main.edit_page").css("display", "block");
                    $(".yz-main.index_page").css("display", "none");
                } else if (obj.event === "download") {
                    layer.confirm("确认下载【" + data.templateName + "】模版？", function (index) {
                        layer.close(index);
                        layer.msg("下载成功", {icon: 1, time: 2000});
                        console.log(data)
                        downloadTemplateFile(data.resid,data.objectId);
                    });
                } else if (obj.event === "editTemplate") {
                    window.open("/api/form/info/upload/editPage?id=" + data.id)
                }
            });

            // 绑定表单下拉框的列表项点击事件
            form.on('select(typeCover)', function (data) {
                var elem = data.elem; // 获得 select 原始 DOM 对象
                var value = data.value; // 获得被选中的值
                var othis = data.othis; // 获得 select 元素被替换后的 jQuery 对象
                // layer.msg(this.innerHTML + ' 的 value: ' + value); // this 为当前选中 <option> 元素对象
                // 渲染点击的表单字段到侧边栏
                renderSingleFormField(value);
                // 渲染点击表单的上传模板文件记录
                renderSingleFormTemplateList(value);
                $(".yz-main.edit_page").css("display", "none");
                $(".yz-main.index_page").css("display", "block");
                formName = this.innerHTML;
                formAlias = value;
                formNameAndAliasSelect = formName + "---" + formAlias;
            });

            // 点击添加模板或点击数据的编辑，显示新增模板页面
            $("#addBtn").on("click", function () {
                $(".yz-main.edit_page").css("display", "block");
                $(".yz-main.index_page").css("display", "none");
                // 打开导出模板的弹窗
                // exportTemplateOpen();
                echoTemplateDetailInfo();
            });

            // 编辑页面点击返回，隐藏新增模板页面
            $(".yz-main.edit_page").on("click", ".back", function () {
                renderSingleFormTemplateList(formAlias);
                $(".yz-main.edit_page").css("display", "none");
                $(".yz-main.index_page").css("display", "block");
                //删除数组文件中上传成功的图片，防止重复上传（重点）
                clearFile();
            });

            // 编辑页面点击删除文件
            $(".layui-form").on("click", ".layui-form-item .layui-input-block .file-list ul li .del", function () {
                $(this).parent().remove();
            });

            // 编辑页面点击保存
            $(".yz-cons .top .btn .save").click(function () {
                // layer.msg("保存成功");
            });

            // 编辑页面点击取消
            $(".yz-cons .top .btn .cancle").click(function () {
                layer.confirm("确认取消编辑吗？", function (index) {
                    renderSingleFormTemplateList(formAlias);
                    $(".yz-main.edit_page").css("display", "none");
                    $(".yz-main.index_page").css("display", "block");
                    layer.close(index);
                });
            });

            // 编辑页面点击取消，隐藏弹窗
            $('.close,.exam-cancle').on("click", function () {
                var index = $(this).parents(".layui-layer").attr("times");
                layer.close(index);
            });

            /**
             * 点击提交按钮触发
             */
            form.on('submit(demo-submit)', function (data) {
                // uploadListIns.upload();
                var templateName = $("#templateName").val();
                var exportFileName = getExportFileNameJson();
                var objectId = $("#objectId").val();
                var subFormCount = $("#subFormCount").val();
                if (!templateName) {
                    layer.msg("请输入模板名称", {icon: 2, time: 2000});
                    return;
                }
                if (!UPLOAD_FILES && !objectId) {
                    layer.msg("请选择模板文件", {icon: 2, time: 2000});
                    return;
                }
                if (!exportFileName) {
                    layer.msg("请输入导出文件名称", {icon: 2, time: 2000});
                    return;
                }
                $("#exportFileNameInput").val(exportFileName);
                if (subFormCount === "" || subFormCount === undefined) {
                    layer.msg("请输入导出子表单数量", {icon: 2, time: 2000});
                    return;
                } else if (subFormCount.indexOf(".") !== -1 || subFormCount <= 0) {
                    layer.msg("导出子表单数量需为正整数", {icon: 2, time: 2000});
                    return;
                } else if (subFormCount > 1000) {
                    layer.msg("导出子表单数量上限为1000", {icon: 2, time: 2000});
                    return;
                }
                // 获取排序依据
                var isEnableOrderText = $("#subFormOrderRule .order-select li.radio-checked").text();
                var isEnableOrder = 0;
                if (isEnableOrderText === "自定义") {
                    isEnableOrder = 1;
                }
                if (isEnableOrder === 1) {
                    var orderItemList = $("#subFormOrderRule .order-list").find(".order-item");
                    if (!orderItemList || !orderItemList.length) {
                        layer.msg("请设置自定义的排序依据", {icon: 2, time: 2000});
                        return;
                    }
                    var orderRuleSetInfoList = [];
                    for (var i = 0; i < orderItemList.length; i++) {
                        var orderRuleSetInfo = {};
                        var orderItem = orderItemList[i];
                        var fieldName = $(orderItem).find("input.fieldName").val();
                        var fieldAlias = $(orderItem).find("input.fieldAlias").val();
                        var order = $(orderItem).find("input.order").val();
                        if (!fieldName) {
                            layer.msg("请选择第" + (i + 1) + "个排序依据的子表单字段", {icon: 2, time: 2000});
                            return;
                        }
                        if (!order) {
                            layer.msg("请选择第" + (i + 1) + "个排序依据的排序方式", {icon: 2, time: 2000});
                            return;
                        }
                        orderRuleSetInfo.formType = "subForm";
                        orderRuleSetInfo.fieldName = fieldName;
                        orderRuleSetInfo.fieldAlias = fieldAlias;
                        orderRuleSetInfo.order = order;
                        orderRuleSetInfoList.push(orderRuleSetInfo);
                    }
                    $("#orderRuleSetInfo").val(JSON.stringify(orderRuleSetInfoList));
                }
                $("#isEnableOrder").val(isEnableOrder);

                // 检查关联的表单是否重复
                var formAliasList = [];
                // 封装数据关联标识
                var dataAssociationLiList = $("#dataAssociation>ul>li>.dataAssociation");
                var dataAssociationList = [];
                for (var i = 0; i < dataAssociationLiList.length; i++) {
                    var dataAssociationLi = dataAssociationLiList[i];
                    var bandFormLi = $(dataAssociationLi).find(">div.select-input ul li.cur");
                    var bandFormFieldLiList = $(dataAssociationLi).find(">ul>li");
                    var bandFormName = $(bandFormLi).find("span").text();
                    var bandFieldList = [];
                    if (bandFormFieldLiList && bandFormFieldLiList.length) {
                        for (var j = 0; j < bandFormFieldLiList.length; j++) {
                            var bandFormFieldLi = bandFormFieldLiList[j];
                            var bandFormFieldInfoLi = $(bandFormFieldLi).find("div.select-input:nth-child(1) div.select-dropdown ul>li.cur");
                            var bandFieldName = $(bandFormFieldInfoLi).find("span").text();
                            var bandFieldCompt = $(bandFormFieldInfoLi).find(".field-compt").val();
                            var bandFieldAlias = $(bandFormFieldInfoLi).find(".field-alias").val();
                            var condition = $(bandFormFieldLi).find("div.condition").text();
                            var sourceFormFieldInfoLi = $(bandFormFieldLi).find("div.select-input:nth-child(3) div.select-dropdown ul>li.cur");
                            var sourceFieldName = $(sourceFormFieldInfoLi).find("span").text();
                            var sourceFieldAlias = $(sourceFormFieldInfoLi).find(".field-alias").val();
                            if (bandFieldName && bandFieldCompt && bandFieldAlias && condition && sourceFieldName && sourceFieldAlias) {
                                var bandFieldObj = {
                                    "bandFieldName": bandFieldName,
                                    "bandFieldCompt": bandFieldCompt,
                                    "bandFieldAlias": bandFieldAlias,
                                    "condition": condition,
                                    "sourceFieldName": sourceFieldName,
                                    "sourceFieldAlias": sourceFieldAlias,
                                };
                                bandFieldList.push(bandFieldObj);
                            }
                        }
                    }
                    if (bandFieldList && bandFieldList.length) {
                        var bandFormAlias = $(bandFormLi).find(".form-alias").val();
                        if (formAlias === bandFormAlias) {
                            layer.msg("数据关联标识表单不能是自己：" + bandFormName, {icon: 2, time: 2000});
                            return;
                        }
                        if (formAliasList.indexOf(bandFormAlias) === -1) {
                            formAliasList.push(bandFormAlias);
                            var dataAssociation = {
                                "bandFormName": bandFormName,
                                "bandFormAlias": bandFormAlias,
                                "bandFieldList": bandFieldList
                            };
                            dataAssociationList.push(dataAssociation);
                        } else {
                            layer.msg("数据关联标识表单重复：" + bandFormName, {icon: 2, time: 2000});
                            return;
                        }
                    }
                }
                $("#dataAssociationInfo").val(JSON.stringify(dataAssociationList));
                layer.confirm('确认上传【' + formNameAndAliasSelect + "】的模板文件【" + $("#templateFileName").text() + "】？", {
                    btn: ['确定', '取消'] //按钮
                }, function () {
                    console.log(UPLOAD_FILES)
                    if (objectId) {
                        // 没有更新上传文件时，保存别的信息
                        saveTemplateInfoWithNoFile();
                    } else {
                        // 提交上传
                        uploadListIns.upload();
                    }
                }, function () {
                    layer.msg('已取消上传模板文件', {time: 2000,});

                });

            });

            var UPLOAD_FILES;
            var uploadListIns = upload.render({
                elem: "#selectFile",
                url: "/form/print/util/file/submit",
                auto: false,
                accept: "file", //普通文件
                size: 20 * 1024,//单位kb
                exts: "doc|docx|pdf",
                multiple: false,
                done: function (res, index) {
                    if (res.code === 200) {
                        layer.msg("保存成功");
                        templateId = res.data;
                    } else {
                        layer.msg(res.msg);
                    }
                },
                data: {
                    id: function () {
                        return templateId ? templateId : "";
                    },
                    formName: function () {
                        return formName;
                    },
                    formAlias: function () {
                        return formAlias;
                    },
                    objectId: function () {
                        return $("#objectId").val();
                    },
                    templateName: function () {
                        return $("#templateName").val();
                    },
                    exportFileName: function () {
                        return $("#exportFileNameInput").val();
                    },
                    subFormCount: function () {
                        return $("#subFormCount").val();
                    },
                    dataAssociationInfo: function () {
                        return $("#dataAssociationInfo").val();
                    },
                    isEnableOrder: function () {
                        return $("#isEnableOrder").val();
                    },
                    orderRuleSetInfo: function () {
                        return $("#orderRuleSetInfo").val();
                    }
                },
                error: function () {
                },
                choose: function (obj) {
                    $("#objectId").val("");
                    // 清空原来的文件，然后把新选择的文件填入
                    UPLOAD_FILES = obj.pushFile();
                    obj.preview(function (index, file, result) {
                        uploadListIns.config.elem.next()[0].value = '';
                        var html = '<li>' +
                            '<span class="name" id="templateFileName">' + file.name + '</span>' +
                            '<span class="del"></span>' +
                            '</li>';
                        $(".layui-form .layui-form-item .layui-input-block .file-list ul").text("");
                        $(".layui-form .layui-form-item .layui-input-block .file-list ul").append(html);
                    });
                },
            });

            // 清空文件队列
            function clearFile() {
                for (let x in UPLOAD_FILES) {
                    delete UPLOAD_FILES[x];
                }
                $("#objectId").val("")
            }

            // 立即执行函数，进入页面执行
            $(function () {
                // 初始化显示页面
                initPageFormInfo();
                // 绑定数据关联标识字段的一些事件
                bandDataAssociationEvent();
            });

            // 初始化显示页面
            function initPageFormInfo() {
                // 初始化表单下拉框列表值
                $.ajax({
                    type: 'post',
                    dataType: 'json',
                    url: '/form/print/util/form/list',
                    data: {},
                    success: function (res) {
                        if (res.code === 200) {
                            renderFormListSelect(res.data);
                            formListData = res.data;
                        } else {
                            layer.msg(res.msg, {icon: 3, time: 2000});
                        }
                    }
                });
            }

            // 没有选择文件时，更新别的信息
            function saveTemplateInfoWithNoFile() {
                $.ajax({
                    type: 'post',
                    dataType: 'json',
                    url: '/form/print/util/file/submit2',
                    data: {
                        id: function () {
                            return templateId ? templateId : "";
                        },
                        formName: function () {
                            return formName;
                        },
                        formAlias: function () {
                            return formAlias;
                        },
                        objectId: function () {
                            return $("#objectId").val();
                        },
                        templateName: function () {
                            return $("#templateName").val();
                        },
                        exportFileName: function () {
                            return $("#exportFileNameInput").val();
                        },
                        subFormCount: function () {
                            return $("#subFormCount").val();
                        },
                        dataAssociationInfo: function () {
                            return $("#dataAssociationInfo").val();
                        },
                        isEnableOrder: function () {
                            return $("#isEnableOrder").val();
                        },
                        orderRuleSetInfo: function () {
                            return $("#orderRuleSetInfo").val();
                        }
                    },
                    success: function (res) {
                        if (res.code === 200) {
                            layer.msg("保存成功", {icon: 1, time: 2000})
                        } else {
                            layer.msg(res.msg, {icon: 2, time: 2000});
                        }
                    }
                });
            }

            // 获取打印模板设置的"设置导出文件名"字段的json数据
            function getExportFileNameJson() {
                var spanList = $("#exportFileName").find("span");
                if (!spanList || spanList.length === 0) {
                    return "";
                }
                var fieldList = [];
                for (var i = 0; i < spanList.length; i++) {
                    var span = $(spanList[i]);
                    var obj = {};
                    obj.name = span.find("em").text();
                    // 手动加的名字有类nadd
                    if (span.hasClass("nadd")) {
                        obj.source = "define";
                    } else {
                        obj.source = "form";
                        obj.fieldAlias = span.find(".field-alias").val();
                        obj.fieldType = span.find(".field-type").val();
                    }
                    fieldList.push(obj);
                }
                return JSON.stringify(fieldList);
            }

            /**
             * 渲染表单下拉框
             */
            function renderFormListSelect(formList) {
                if (!formList || formList.length === 0) {
                    return;
                }
                // $("#formListSelect").empty();
                for (let i = 0; i < formList.length; i++) {
                    var single = formList[i];
                    $("#formListSelect").append("<option value='" + single.formAlias + "'>" + single.name + "</option>");
                }
                // 重新渲染下拉框，并渲染第一个表单的字段到侧边栏
                form.render('select');
                if (formList.length > 0) {
                    var firstForm = formList[0];
                    renderSingleFormField(firstForm.formAlias);
                    renderSingleFormTemplateList(firstForm.formAlias);
                    formName = firstForm.name;
                    formAlias = firstForm.formAlias;
                    formNameAndAliasSelect = formName + "---" + formAlias;
                }
                // 点击添加数据关联标识下拉框渲染
                var formLiHtml = "";
                for (let i = 0; i < formList.length; i++) {
                    var formSingleData = formList[i];
                    formLiHtml += '<li>' +
                        '   <span>' + formSingleData.name + '</span>' +
                        '   <input type="hidden" class="form-alias" value="' + formSingleData.formAlias + '">' +
                        '</li>';
                }
                $("#dataAssociation>ul li:nth-child(1) .single_select").empty();
                $("#dataAssociation>ul li:nth-child(1) .single_select").append(formLiHtml);
            }

            /**
             * 根据表单标识，渲染表单字段列表栏+模板设置页面的设置导出文件名下拉框
             */
            function renderSingleFormField(formAlias) {
                // 初始化表单下拉框列表值
                $.ajax({
                    type: 'post',
                    dataType: 'json',
                    url: '/form/print/util/form/detail',
                    data: {"formAlias": formAlias},
                    success: function (res) {
                        if (res.code === 200) {
                            var formDetailList = res.data.formDetailList;
                            renderFormDetailInfo(formDetailList);
                            renderTemplateFormFieldSelect(formDetailList);
                            formFieldListData = formDetailList;
                            subFormFieldListData = res.data.subFormDetailList;
                        } else {
                            layer.msg(res.msg, {icon: 2, time: 2000});
                        }
                    }
                });
            }

            /**
             * 渲染根据表单标识，渲染该表单对应的模板文件
             */
            function renderSingleFormTemplateList(formAlias) {
                // 初始化表单下拉框列表值
                $.ajax({
                    type: 'post',
                    dataType: 'json',
                    url: '/form/print/util/template/list',
                    data: {"formAlias": formAlias},
                    success: function (res) {
                        if (res.code === 200) {
                            tabData = res.data;
                            table.reload('templateTable', {data: tabData}, true);
                        } else {
                            layer.msg(res.msg, {icon: 2, time: 2000});
                        }
                    }
                });
            }

            /**
             * 渲染表单字段列表栏
             */
            function renderFormDetailInfo(formFieldList) {
                if (!formFieldList || formFieldList.length === 0) {
                    return;
                }
                $("#fieldListUl").empty();
                for (let i = 0; i < formFieldList.length; i++) {
                    var formField = formFieldList[i];
                    var html = "";
                    if (formField.type === "normal") {
                        html += "<li>" +
                            "   <div class='label-top no-children'>" +
                            "       <div class='name'>" + formField.label + "</div>" +
                            "       <input type='hidden' class='field-alias' value='" + formField.fieldAlias + "'>" +
                            "   </div>" +
                            "</li>";
                    } else if (formField.type === "subForm") {
                        html += "<li>" +
                            "   <div class='label-top'>" +
                            "       <div class='name'>" + formField.label + "" +
                            "           <input type='hidden' class='field-alias' value='" + formField.fieldAlias + "'>" +
                            "       </div>" +
                            "       <span class='slide-arrow'></span>" +
                            "   </div>" +
                            "   <div class='label-con'>" +
                            "       <div class='item-list'>";
                        var subFieldList = formField.subFieldList;
                        if (subFieldList && subFieldList.length !== 0) {
                            for (var j = 0; j < subFieldList.length; j++) {
                                var subField = subFieldList[j];
                                html += "<div class='item'>" + subField.label + "" +
                                    "       <input type='hidden' class='field-alias' value='" + subField.fieldAlias + "'>" +
                                    "</div>";
                            }
                        }
                        html += "   </div>" +
                            "   </div>" +
                            "</li>";
                    }
                    $("#fieldListUl").append(html);
                }
                // 将"打印时间_中文、打印时间_数字"这两个字段放到最后面
                var timeHtml = '<li>' +
                    '   <div class="label-top no-children">' +
                    '       <div class="name" title="示例:二零二三年十月二十七日">打印时间_中文</div>' +
                    '       <input type="hidden" class="field-alias" value="p_t-zh_cn">' +
                    '   </div>' +
                    '</li>' +
                    '<li>' +
                    '   <div class="label-top no-children">' +
                    '       <div class="name" title="示例:2023年10月27日">打印时间_数字</div>' +
                    '       <input type="hidden" class="field-alias" value="p_t-digit">' +
                    '   </div>' +
                    '</li>';
                $("#fieldListUl").append(timeHtml);
            }

            // 渲染模板设置页的设置导出文件名下拉框
            function renderTemplateFormFieldSelect(formFieldList) {
                $("#exportFileNameUl").empty();
                if (formFieldList && formFieldList.length > 0) {
                    for (let i = 0; i < formFieldList.length; i++) {
                        var formField = formFieldList[i];
                        if ("文本" === formField.fieldType) {
                            var html = "<li>" +
                                "   <span>" + formField.label + "</span>" +
                                "   <input type='hidden' class='field-alias' value='" + formField.fieldAlias + "'>" +
                                "   <p>文本</p>" +
                                "</li>";
                            $("#exportFileNameUl").append(html);
                        }
                    }
                }
            }

            // 打开导出模板的弹窗
            function exportTemplateOpen() {
                layer.open({
                    type: 1,
                    title: false,
                    closeBtn: false,
                    shadeClose: true,
                    isOutAnim: true,
                    content: $('#export-template'),
                    area: ['auto', 'auto'],
                    success: function () {
                    },
                }, function () {
                });
            }

            // 回显模板设置详情信息
            function echoTemplateDetailInfo(id) {
                if (id) {
                    templateId = id;
                    // 初始化表单下拉框列表值
                    $.ajax({
                        type: 'get',
                        dataType: 'json',
                        url: '/form/print/util/template/detail',
                        data: {"id": id},
                        success: function (res) {
                            if (res.code === 200) {
                                var detailInfo = res.data;
                                if (detailInfo) {
                                    $("#templateName").val(detailInfo.templateName);
                                    $("#objectId").val(detailInfo.objectId);
                                    var html = '<li>' +
                                        '<span class="name" id="templateFileName">' + detailInfo.templateFileName + '</span>' +
                                        '<span class="del"></span>' +
                                        '</li>';
                                    $(".layui-form .layui-form-item .layui-input-block .file-list ul").text("");
                                    $(".layui-form .layui-form-item .layui-input-block .file-list ul").append(html);
                                    // 回显设置导出文件名字段
                                    echoExportFileNameInfo(detailInfo.exportFileName);
                                    $("#subFormCount").val(detailInfo.subFormCount);
                                    // 回显数据关联标识下拉框
                                    echoDataAssociationInfo(detailInfo.dataAssociationInfo);
                                    // 回显排序依据数据
                                    var isEnableOrder = detailInfo.isEnableOrder;
                                    if (isEnableOrder === 1) {
                                        $("#subFormOrderRule .layui-input-block ul.order-select li").removeClass("radio-checked");
                                        $("#subFormOrderRule .layui-input-block ul.order-select li:nth-child(2)").addClass("radio-checked");
                                        $("#subFormOrderRule .order-list").show();
                                    } else {
                                        $("#subFormOrderRule .layui-input-block ul.order-select li").removeClass("radio-checked");
                                        $("#subFormOrderRule .layui-input-block ul.order-select li:nth-child(1)").addClass("radio-checked");
                                        $("#subFormOrderRule .order-list").hide();
                                    }
                                    echoOrderRuleSetInfo(detailInfo.orderRuleSetInfo);
                                }
                            } else {
                                layer.msg(res.msg, {icon: 2, time: 2000});
                            }
                        }
                    });
                } else {
                    templateId = undefined;
                    $("#templateName").val("");
                    $("#objectId").val("");
                    $(".layui-form .layui-form-item .layui-input-block .file-list ul").text("");
                    // 回显设置导出文件名字段
                    echoExportFileNameInfo();
                    $("#subFormCount").val("");
                    // 回显数据关联标识下拉框
                    echoDataAssociationInfo();
                }
            }

            // 回显数据关联标识下拉框
            function echoDataAssociationInfo(dataAssociationInfoJson) {
                var errorFlag = false;
                $("#dataAssociation>ul").empty();
                // 如果设置过数据关联标识，则回显
                if (dataAssociationInfoJson) {
                    try {
                        var dataAssociationInfoList = JSON.parse(dataAssociationInfoJson);
                        for (var i = 0; i < dataAssociationInfoList.length; i++) {
                            var dataAssociationInfo = dataAssociationInfoList[i];
                            if (dataAssociationInfo) {
                                addDataAssociationInfo(dataAssociationInfo, i === 0);
                            }
                        }
                    } catch (e) {
                        console.log(e);
                        errorFlag = true;
                    }
                }
                // 如果没有设置过数据关联标识或解析失败了，则清空下拉框选择的内容
                if (!dataAssociationInfo || errorFlag) {
                    addDataAssociationInfo(undefined, true);
                }
            }

            // 回显排序依据数据
            function echoOrderRuleSetInfo(orderRuleSetInfoJson) {
                var errorFlag = false;
                $("#subFormOrderRule .order-list").empty();
                // 如果设置过排序依据，则回显
                if (orderRuleSetInfoJson) {
                    try {
                        var orderRuleSetInfoList = JSON.parse(orderRuleSetInfoJson);
                        for (let i = 0; i < orderRuleSetInfoList.length; i++) {
                            var orderRuleSetInfo = orderRuleSetInfoList[i];
                            if (orderRuleSetInfo) {
                                addSubFormFieldInfo(orderRuleSetInfo, i === 0);
                            }
                        }
                    } catch (e) {
                        console.log(e)
                        errorFlag = true;
                    }
                }
                if (!orderRuleSetInfo || errorFlag) {
                    addSubFormFieldInfo(undefined, true);
                }
            }

            // 根据导出设置的文件名，回显导出文件名
            function echoExportFileNameInfo(exportFileNameJson) {
                // 如果exportFileNameJson有值，则是点击了编辑；否则是点击了添加模板或编辑(未设置导出文件名)
                if (exportFileNameJson) {
                    try {
                        var exportFileNameList = JSON.parse(exportFileNameJson);
                        $("#exportFileName").empty();
                        txtarr = [];
                        for (let i = 0; i < exportFileNameList.length; i++) {
                            var exportFileName = exportFileNameList[i];
                            var text = "";
                            var html = "";
                            if (exportFileName.source === "form") {
                                text = exportFileName.name;
                                html = "<span>" +
                                    "   <em>" + exportFileName.name + "</em>" +
                                    "   <i></i>" +
                                    "   <input type='hidden' class='field-alias' value='" + exportFileName.fieldAlias + "'>" +
                                    "   <input type='hidden' class='field-type' value='" + exportFileName.fieldType + "'>" +
                                    "</span>";
                                txtarr.push(exportFileName.name);
                            } else if (exportFileName.source === "define") {
                                text = exportFileName.name;
                                html = "<span>" +
                                    "   <em>" + exportFileName.name + "</em>" +
                                    "   <i></i>" +
                                    "</span>";
                            }
                            $("#exportFileName").append(html);
                        }
                        // 勾选指定的下拉框
                        $(".sel.file_name .select-input .select-dropdown .dropdown-lists.person-drop li").each(function () {
                            var tt = $(this).find("span").text();
                            if (txtarr.indexOf(tt) === -1) {
                                $(this).removeClass("cur");
                            } else {
                                $(this).toggleClass("cur", true);
                            }
                        });
                        calcFn();
                    } catch (e) {
                        console.log(e);
                    }
                } else {
                    txtarr = [];
                    calcFn();
                    $("#exportFileName").empty();
                }
            }

            // 将模板设置为默认模板
            function setTemplateDefaultStatus(id, isDefaultTemplate) {
                $.ajax({
                    type: 'post',
                    dataType: 'json',
                    url: '/form/print/util/template/setDefault',
                    data: {
                        "id": id,
                        "isDefaultTemplate": isDefaultTemplate,
                        "formAlias": formAlias
                    },
                    success: function (res) {
                        if (res.code === 200) {
                            layer.msg("设置成功", {icon: 1, time: 2000});
                            if (isDefaultTemplate === 0) {
                                tabData.forEach(item => {
                                    item.isDefaultTemplate = 1;
                                    if (item.id === id) {
                                        item.isDefaultTemplate = 0;
                                    }
                                });
                            } else {
                                for (let i = 0; i < tabData.length; i++) {
                                    var item = tabData[i];
                                    if (item.id === id) {
                                        item.isDefaultTemplate = 1;
                                        break;
                                    }
                                }
                            }
                            table.reload('templateTable', {data: tabData}, true);
                        } else {
                            layer.msg(res.msg, {icon: 2, time: 2000});
                        }
                    }
                });
            }

            // 下载模板文件
            function downloadTemplateFile(resId,objectId) {

                $.ajax({
                    type: 'post',
                    dataType: 'json',
                    url: '/form/print/util/template/download',
                    data: {
                        "resId": resId,
                        "objectId": objectId
                    },
                    success: function (res) {
                        if (res.code === 200) {
                            $('<a>')
                                .attr('href', res.data)
                                .appendTo('body')
                                .get(0).click(); // 触发点击事件
                            $('a').remove(); // 移除动态创建的 <a> 元素

                        } else {
                            layer.msg(res.msg, {icon: 2, time: 2000});
                        }
                    }
                });
            }

            // 删除模板文件
            function templateDelete(idList) {
                $.ajax({
                    type: 'post',
                    dataType: 'json',
                    url: '/form/print/util/template/delete',
                    data: JSON.stringify(idList),
                    headers: {'Content-Type': 'application/json'},
                    success: function (res) {
                        if (res.code === 200) {
                            layer.msg(res.msg, {icon: 1, time: 2000});
                        } else {
                            layer.msg(res.msg, {icon: 2, time: 2000});
                        }
                    }
                });
            }
        });
    });
</script>
</body>
</html>