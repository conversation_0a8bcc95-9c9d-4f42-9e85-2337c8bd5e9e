<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>开课总学时</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/global.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/classHour.css'}">
</head>

<body>
<div class="dialog-wrap dialog-hour">
    <div class="dialog">
        <div class="dialog-con">
            <div class="term">
                <h3>学年学期</h3>
                <input type="text" value="2024-2025-1" disabled>
            </div>
            <div class="hour-list">
                <div class="title">
                    <h3>授课教师已开总学时</h3><span></span>
                </div>
                <ul class="list teacher">
                </ul>
                <div class="title">
                    <h3>班级已开总学时</h3><span></span>
                </div>
                <ul class="list clazz">
                </ul>

            </div>
        </div>
        <div class="dialog-btn">
            <button class="cancel">取消</button>
            <button class="sure" id="planSel">确定</button>
        </div>
    </div>
</div>
</body>
<script th:src="${_CPR_+_VR_+'/js/jquery-3.3.1.min.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/my.util.js'}"></script>
<script th:inline="javascript">
    let _VR_ = [[${_VR_}]];
    $(document).ready(function () {
        $(".hour-list").on('click', '.title span', function () {
            $(this).toggleClass('slideUp');
            $(this).parent().next().slideToggle('fast')
        })
        U.ajax({
            type: 'post',
            url: "/cultivation/getTotalHoursStatisticsData",
            data: {
                formId: [[${formRightBtnVO.formId}]],
                fid: [[${formRightBtnVO.fid}]],
                uid: [[${formRightBtnVO.uid}]],
                formUserId: [[${formRightBtnVO.formUserId}]],
                roleid: [[${formRightBtnVO.roleid}]],
                appName: [[${formRightBtnVO.appName}]]
            },
            dataType: 'json',
            success: function (result) {
                if (!result.status) {
                    U.fail("获取数据异常");
                    return false;
                }
                let teacherNumber = 0;
                let clazzNumber = 0;
                $("input").val(result.data.term);
                let clazzList = result.data.clazzList;
                let teacherList = result.data.teacherList;
                let teacherHtml = "";
                let clazzHtml = "";
                Object.entries(clazzList).forEach(([key, val]) => {
                    clazzNumber++;
                    clazzHtml += "<li>";
                    clazzHtml += "<span>" + clazzNumber.toString().padStart(2, '0') + "</span>";
                    clazzHtml += "<dl><dt>班级名称：</dt><dd>" + key.split("|")[1] + "</dd></dl>";
                    clazzHtml += "<dl><dt>班级编号：</dt><dd>" + key.split("|")[0] + "</dd></dl>";
                    clazzHtml += "<dl><dt>已开总学时：</dt><dd>" + val + "</dd></dl>";
                    clazzHtml += "</li>";
                });
                Object.entries(teacherList).forEach(([key, val]) => {
                    teacherNumber++;
                    teacherHtml += "<li>";
                    teacherHtml += "<span>" + teacherNumber.toString().padStart(2, '0') + "</span>";
                    teacherHtml += "<dl><dt>教师姓名：</dt><dd>" + key.split("|")[1] + "</dd></dl>";
                    teacherHtml += "<dl><dt>教师工号：</dt><dd>" + key.split("|")[0] + "</dd></dl>";
                    teacherHtml += "<dl><dt>已开总学时：</dt><dd>" + val + "</dd></dl>";
                    teacherHtml += "</li>";
                });
                $(".teacher").html(teacherHtml);
                $(".clazz").html(clazzHtml);
            }
        })
    })
    $(".dialog-btn button").click(function () {
        window.parent.postMessage(JSON.stringify({action: 1}), "*");
    })
</script>

</html>