<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>开课</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/global.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/plugin/layui/css/layui2.8.2.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/reset.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/index4.0.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/dialogCourse.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/slideCommon.css'}">
    <script th:src="${_CPR_+_VR_+'/plugin/layui/layui2.8.2.js'}"></script>
    <style>
        #selTextbook .dialog-con .j-search-con, #selClass .dialog-con .j-search-con {
            width: 170px;
        }
    </style>
</head>

<body>
<div class="main">
    <div class="wrap-item">
        <div class="m-top">
            <div class="title">开课</div>
        </div>
        <div class="item">
            <div class="i-top" style="margin-top: 24px">
                <h3>开课信息</h3>
                <span class="arrow slide"></span>
            </div>
            <div class="i-con">
                <div class="course-inform">
                    <h4>课程信息</h4>
                    <ul>
                        <li>
                            <div class="name">开课学期：</div>
                            <div class="tit" th:title="${teachPlan.jxjhgl_xnxq}"
                                 th:text="${teachPlan.jxjhgl_xnxq}"></div>
                        </li>
                        <li>
                            <div class="name">开课院系：</div>
                            <div class="tit" th:title="${teachPlan.jxjhgl_kkxb}"
                                 th:text="${teachPlan.jxjhgl_kkxb}"></div>
                        </li>
                        <li>
                            <div class="name">所属专业：</div>
                            <div class="tit" th:title="${teachPlan.jxjhgl_zy}" th:text="${teachPlan.jxjhgl_zy}"></div>
                        </li>
                        <li>
                            <div class="name">培养层次：</div>
                            <div class="tit" th:title="${teachPlan.jxjhgl_pycc}"
                                 th:text="${teachPlan.jxjhgl_pycc}"></div>
                        </li>
                        <li>
                            <div class="name">课程名称：</div>
                            <div class="tit" th:title="${teachPlan.jxjhgl_kcmc}"
                                 th:text="${teachPlan.jxjhgl_kcmc}"></div>
                        </li>
                        <li>
                            <div class="name">课程性质：</div>
                            <div class="tit" th:title="${teachPlan.jxjhgl_kcxz}"
                                 th:text="${teachPlan.jxjhgl_kcxz}"></div>
                        </li>
                        <li>
                            <div class="name">是否为纯实践环节：</div>
                            <div class="tit" th:title="${teachPlan.jxjhgl_sfcsjhj}"
                                 th:text="${teachPlan.jxjhgl_sfcsjhj}"></div>
                        </li>
                        <li>
                            <div class="name">选必修：</div>
                            <div class="tit" th:title="${teachPlan.jxjhgl_xbx}" th:text="${teachPlan.jxjhgl_xbx}"></div>
                        </li>
                        <li>
                            <div class="name">总学分：</div>
                            <div class="tit" th:title="${teachPlan.jxjhgl_xf}" th:text="${teachPlan.jxjhgl_xf}"></div>
                        </li>
                        <li>
                            <div class="name">总学时：</div>
                            <div class="tit" th:title="${teachPlan.jxjhgl_zxs}" th:text="${teachPlan.jxjhgl_zxs}"></div>
                        </li>
                        <li>
                            <div class="name">理论学时：</div>
                            <div class="tit" th:title="${teachPlan.jxjhgl_llxs}"
                                 th:text="${teachPlan.jxjhgl_llxs}"></div>
                        </li>
                        <li>
                            <div class="name">实践学时：</div>
                            <div class="tit" th:title="${teachPlan.jxjhgl_sjxs}"
                                 th:text="${teachPlan.jxjhgl_sjxs}"></div>
                        </li>
                        <li>
                            <div class="name">上机学时：</div>
                            <div class="tit" th:title="${teachPlan.jxjhgl_sjxss}"
                                 th:text="${teachPlan.jxjhgl_sjxss}"></div>
                        </li>
                        <li>
                            <div class="name">实验学时：</div>
                            <div class="tit" th:title="${teachPlan.jxjhgl_syxs}"
                                 th:text="${teachPlan.jxjhgl_syxs}"></div>
                        </li>
                        <li>
                            <div class="name">其他学时：</div>
                            <div class="tit" th:title="${teachPlan.jxjhgl_qtxs}"
                                 th:text="${teachPlan.jxjhgl_qtxs}"></div>
                        </li>
                        <li>
                            <div class="name">计划人数：</div>
                            <div class="tit" th:title="${teachPlan.jxjhgl_zrs}" th:text="${teachPlan.jxjhgl_zrs}"></div>
                        </li>
                        <li>
                            <div class="name">分配人数：</div>
                            <div class="tit" id="yfprs"></div>
                        </li>
                        <li>
                            <div class="name">考试形式：</div>
                            <div class="tit" th:title="${teachPlan.jxjhgl_ksxs}"
                                 th:text="${teachPlan.jxjhgl_ksxs}"></div>
                        </li>
                    </ul>
                </div>
                <div class="class-box">
                    <div class="cb-top">
                        <div class="tit">已生成教学班</div>
                        <button class="button">保存</button>
                    </div>
                    <div class="j-table j-table-class" style="border: none">
                        <div class="j-table-con">
                            <table class="layui-table" id="materialTable" lay-filter="materialTable"></table>
                        </div>
                        <div class="j-table-con j-class-set">
                            <table>
                                <tr>
                                    <td class="td-name">
                                        <label>教学班名称</label>
                                    </td>
                                    <td>
                                        <input type="text" class="td-input-opt td-input" placeholder="请输入" disabled name="appellation" />
                                    </td>
                                    <td>
                                        <label>教学班编号</label>
                                    </td>
                                    <td>
                                        <input type="text" class="td-input-opt td-input" placeholder="请输入" disabled name="number" />
                                    </td>
                                    <td>
                                        <label>教学班组成</label>
                                    </td>
                                    <td>
                                        <input type="text" class="td-input-opt td-input" placeholder="请输入" disabled name="compose" />
                                    </td>
                                </tr>
                                <tr>
                                    <td class="td-name">
                                        <label class="td-name-require">学时类型</label>
                                    </td>
                                    <td>
                                        <div class="j-search-con single-box">
                                            <input type="text" isRequire="true" name="selHourType" placeholder="请选择" readonly="" class="td-input-opt schoolSel" />
                                            <span class="j-arrow"></span>
                                            <div class="j-select-year">
                                                <ul class="classType">
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <label class="td-name-require">教学班类型</label>
                                    </td>
                                    <td>
                                        <div class="j-search-con single-box">
                                            <input type="text" name="selTeachClassroomType" placeholder="请选择" readonly="" class="td-input-opt schoolSel" />
                                            <span class="j-arrow"></span>
                                            <div class="j-select-year">
                                                <ul>
                                                    <li value="父教学班">父教学班</li>
                                                    <li value="子教学班">子教学班</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <label>选择父教学班</label>
                                    </td>
                                    <td>
                                        <div class="j-search-con single-box">
                                            <input
                                                    type="text"
                                                    isRequire="true"
                                                    name="selParentClassroom"
                                                    placeholder="请选择"
                                                    readonly=""
                                                    class="td-input-opt schoolSel"
                                            />
                                            <span class="j-arrow"></span>
                                            <div class="j-select-year">
                                                <div class="search">
                                                    <input type="text" placeholder="搜索" />
                                                    <span></span>
                                                </div>
                                                <ul class="classType">
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="td-name">
                                        <label class="td-name-require">是否排课</label>
                                    </td>
                                    <td>
                                        <div class="j-search-con single-box">
                                            <input type="text" name="isArrange" isRequire="true" placeholder="请选择" readonly="" class="td-input-opt schoolSel" />
                                            <span class="j-arrow"></span>
                                            <div class="j-select-year">
                                                <ul class="classType">
                                                    <li value="是">是</li>
                                                    <li value="否">否</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <label class="td-name-require">是否选课</label>
                                    </td>
                                    <td>
                                        <div class="j-search-con single-box">
                                            <input type="text" name="isArrange1" isRequire="true" placeholder="请选择" readonly="" class="td-input-opt schoolSel" />
                                            <span class="j-arrow"></span>
                                            <div class="j-select-year">
                                                <ul class="classType">
                                                    <li value="是">是</li>
                                                    <li value="否">否</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <label class="td-name-require">是否排考</label>
                                    </td>
                                    <td>
                                        <div class="j-search-con single-box">
                                            <input type="text" name="isArrange2" isRequire="true" placeholder="请选择" readonly="" class="td-input-opt schoolSel" />
                                            <span class="j-arrow"></span>
                                            <div class="j-select-year">
                                                <ul class="classType">
                                                    <li value="是">是</li>
                                                    <li value="否">否</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="td-name">
                                        <label class="td-name-require">是否录入成绩</label>
                                    </td>
                                    <td>
                                        <div class="j-search-con single-box">
                                            <input type="text" name="isArrange3" isRequire="true" placeholder="请选择" readonly="" class="td-input-opt schoolSel" />
                                            <span class="j-arrow"></span>
                                            <div class="j-select-year">
                                                <ul class="classType">
                                                    <li value="是">是</li>
                                                    <li value="否">否</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <label>选择教师</label>
                                    </td>
                                    <td>
                                        <div class="setBox">
                                            <input type="text" readonly name="selTeacher" class="td-input-opt td-input" placeholder="请选择" />
                                            <span class="set set-teacher" data-type="1">设置</span>
                                        </div>
                                    </td>
                                    <td>
                                        <label>选择助教</label>
                                    </td>
                                    <td>
                                        <div class="setBox">
                                            <input type="text" readonly name="selAssistant" class="td-input-opt td-input" placeholder="请选择" />
                                            <span class="set set-teacher" data-type="2">设置</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="td-name">
                                        <label>指定教室</label>
                                    </td>
                                    <td>
                                        <div class="setBox">
                                            <input type="text" readonly name="classroom" class="td-input-opt td-input" placeholder="请选择" />
                                            <span class="set set-classroom" lay-event="set">设置</span>
                                        </div>
                                    </td>
                                    <td>
                                        <label>教室类型</label>
                                    </td>
                                    <td>
                                        <div class="j-search-con single-box">
                                            <input type="text" name="selClassroomType" placeholder="请选择" readonly="" class="td-input-opt schoolSel" />
                                            <span class="j-arrow"></span>
                                            <div class="j-select-year">
                                                <ul class="classType">
                                                    <li value="1">教室类型1</li>
                                                    <li value="2">教室类型2</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <label>周学时安排</label>
                                    </td>
                                    <td>
                                        <div class="setBox">
                                            <input type="text" readonly name="weekHour" class="td-input-opt td-input" placeholder="请输入" />
                                            <span class="set set-weekHour">设置</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="td-name">
                                        <label>联排节次</label>
                                    </td>
                                    <td colspan="5">
                                        <input type="text" class="td-input-opt td-input" placeholder="请输入" name="festivals" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="item" style="background: #ffffff; border-radius: 8px; padding-top: 16px; overflow: hidden">
        <div class="i-top">
            <h3>开班</h3>
            <span class="arrow slide"></span>
        </div>
        <div class="i-con" style="display: flex; overflow: unset">
            <div class="class-box" style="width: 47.5%">
                <div class="cb-top">
                    <div class="tit" style="margin-right: 24px">可选行政班</div>
                    <div class="i-mes">
                        <button class="singleClassTeaching">行政班单班开课</button>
                        <button id="selOtherClass">选择其他教学计划班级</button>
                    </div>
                </div>
                <div class="j-table" style="border: none">
                    <table class="layui-table" id="classTable" lay-filter="classTable"></table>
                </div>
            </div>

            <div class="mutate">
                <div class="up" id="up"></div>
                <div class="down" id="down"></div>
            </div>

            <div class="class-box" style="width: 47.5%; overflow: unset">
                <div class="cb-top" style="justify-content: space-between">
                    <div class="tit">已生成教学班</div>
                    <div class="i-mes">
                        <h5>教学班人数：<em id = "classStuNum">0</em>人</h5>
                        <button id="confirmClass">确认生成教学班</button>
                    </div>
                </div>

                <div class="j-table" style="border: none">
                    <table class="layui-table" id="teachingClassForm" lay-filter="teachingClassForm"></table>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- 选择教师、选择助教 -->
<div class="dialog" id="selTeacher">
    <div class="dialog-title">选择教师<span class="dialog-close"></span></div>
    <div class="dialog-con">
        <form action="" class="layui-form form-teacher">
            <div class="layui-inline">
                <label class="layui-form-label">教师工号</label>
                <div class="layui-input-inline">
                    <div class="j-search-con single-box" formAlias = "jsjbxx" fieldAlias = "jsjbxx_jsgh">
                        <input type="text" name="jsjbxx_jsgh" placeholder="请输入" autocomplete="off" class="schoolSel" />
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索" />
                                <span></span>
                            </div>
                            <ul>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">教师姓名</label>
                <div class="layui-input-inline">
                    <div class="j-search-con single-box" formAlias = "jsjbxx" fieldAlias = "jsjbxx_xm">
                        <input type="text" name="jsjbxx_xm" placeholder="请输入" autocomplete="off" class="schoolSel" />
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索" />
                                <span></span>
                            </div>
                            <ul>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label" style="width: 56px">系部</label>
                <div class="layui-input-inline" style="margin-right: 0">
                    <div class="j-search-con single-box" formAlias = "jsjbxx" fieldAlias = "jsjbxx_yx">
                        <input type="text" name="jsjbxx_yx" placeholder="请选择" readonly="" class="schoolSel" />
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索" />
                                <span></span>
                            </div>
                            <ul>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-inline layui-inline-btns">
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                <button type="submit" class="layui-btn" lay-submit lay-filter="selTeacherTable">查询</button>
            </div>
        </form>
        <div class="courseList">
            <table class="layui-hide" id="teacherList" lay-filter="teacherList"></table>
            <div class="z-check"><span class="check" id="checkAllTeacher"></span>选择全部数据</div>
            <div class="selCourse" id="selTeacherCount">已选中<em>0</em>条</div>
        </div>
    </div>
    <div class="dialog-footer">
        <button class="btnCancel">取消</button>
        <button id="teacherSureBtn">确定</button>
    </div>
</div>

<!-- 选择其他专业班级 -->
<div id="selClass" class="dialog" style="display: none">
    <div class="dialog-title">选择其他专业班级</div>
    <div class="tips">只能选择同一开课学年学期、开课课程一致、周学时一致的教学计划</div>
    <div class="dialog-con">
        <form action="" class="layui-form form-textbook" style="margin-top: 24px">
            <div class="layui-inline">
                <label class="layui-form-label" style="width: 56px">专业名称</label>
                <div class="layui-input-inline">
                    <div class="j-search-con single-box">
                        <input type="text" placeholder="请选择" readonly="" class="schoolSel" />
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索" />
                                <span></span>
                            </div>
                            <ul class="majorList">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label" style="width: 36px">年级</label>
                <div class="layui-input-inline">
                    <div class="j-search-con single-box">
                        <input type="text" placeholder="请选择" readonly="" class="schoolSel" />
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索" />
                                <span></span>
                            </div>
                            <ul class = "gradeList">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label" style="width: 36px">系部</label>
                <div class="layui-input-inline">
                    <div class="j-search-con single-box">
                        <input type="text" placeholder="请选择" readonly="" class="schoolSel" />
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索" />
                                <span></span>
                            </div>
                            <ul class = "deptList">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-inline">
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                <button type="submit" class="layui-btn" lay-submit lay-filter="selTextbookTable">查询</button>
            </div>
        </form>
        <div class="courseList">
            <table class="layui-hide" id="classList" lay-filter="classList"></table>
            <div class="z-check"><span class="check" id="checkAllClass"></span>选择全部数据</div>
            <div class="selCourse" id="selTextbookCount">已选中<em>0</em>条</div>
        </div>
    </div>
    <div class="dialog-footer">
        <button class="btnCancel">取消</button>
        <button id="classSureBtn">确定</button>
    </div>
</div>
<!-- 选择学生 -->
<div id="selTextbook" class="dialog" style="display: none">
    <div class="dialog-title">选择学生</div>
    <div class="tips">当前可选学生[23]位，若不指定学生，系统则按照分配人数随机进行学生拆分</div>
    <div class="dialog-con">
        <form action="" class="layui-form form-textbook" style="margin-top: 24px">
            <div class="layui-inline">
                <label class="layui-form-label">学生编号</label>
                <div class="layui-input-inline">
                    <div class="j-search-con single-box single-search" formAlias = "xsjbxx" fieldAlias = "xsjbxx_xh">
                        <input type="text" name="xh" placeholder="请输入" autocomplete="off" class="schoolSel" />
                        <div class="j-select-year">
                            <ul>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">学生姓名</label>
                <div class="layui-input-inline">
                    <div class="j-search-con single-box single-search" formAlias = "xsjbxx" fieldAlias = "xsjbxx_xm">
                        <input type="text" name="xm" placeholder="请输入" autocomplete="off" class="schoolSel" />
                        <div class="j-select-year">
                            <ul>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label mu" style="width: 56px">学生性别</label>
                <div class="layui-input-inline">
                    <div class="j-search-con multiple-box">
                        <input type="text" name="xb" placeholder="请选择" readonly="" class="schoolSel" />
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="all-selects" style="margin-top: 6px">全选</div>
                            <ul>
                                <li>男</li>
                                <li>女</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label" style="width: 56px">培养层次</label>
                <div class="layui-input-inline">
                    <div class="j-search-con single-box" formAlias = "xsjbxx" fieldAlias = "xsjbxx_pycc">
                        <input type="text" name="pycc" placeholder="请选择" readonly="" class="schoolSel" />
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索" />
                                <span></span>
                            </div>
                            <ul>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-inline">
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                <button type="submit" class="layui-btn" lay-submit lay-filter="selTextbookTable">查询</button>
            </div>
        </form>
        <div class="courseList">
            <table class="layui-hide" id="TextbookList" lay-filter="TextbookList"></table>
            <div class="z-check"><span class="check" id="checkAllStu"></span>选择全部数据</div>
            <div class="selCourse" id="selTextbookCount">已选中<em>0</em>条</div>
        </div>
    </div>
    <div class="dialog-footer">
        <button class="btnCancel">取消</button>
        <button id="TextbookSureBtn">确定</button>
    </div>
</div>
<!-- 错误提示 -->
<div class="dialog" id="dialogTip" style="display: none">
    <div class="dialog-title">
        <h5>错误提示</h5>
        <span class="close"></span>
    </div>
    <div class="dialog-con">
        <div class="error-con">
            <img src="../images/error-tip1.png" alt="" />
            <p class="error-title">指定学生人数大于分配人数，请重新指定学生</p>
        </div>
    </div>
    <div class="dialog-footer">
        <button class="btnTipCancel">取消</button>
        <button class="btnTipCancel">确定</button>
    </div>
</div>
<!-- 选择教室 -->
<div class="dialog" id="selClassroom" style="display: none">
    <div class="dialog-title">选择教室<span class="dialog-close"></span></div>
    <div class="dialog-con">
        <form action="" class="layui-form form-classroom">
            <div class="layui-inline">
                <label class="layui-form-label" style="width: 72px">教学楼名称</label>
                <div class="layui-input-inline">
                    <div class="j-search-con single-box" formAlias = "jsxx" fieldAlias = "jsxx_jxl">
                        <input type="text" name="jsxx_jxl" placeholder="请选择" autocomplete="off" class="schoolSel" />
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索" />
                                <span></span>
                            </div>
                            <ul>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">教室类型</label>
                <div class="layui-input-inline">
                    <div class="j-search-con single-box" formAlias = "jsxx" fieldAlias = "jsxx_jslx">
                        <input type="text" name="jsxx_jslx" placeholder="请选择" autocomplete="off" class="schoolSel" />
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索" />
                                <span></span>
                            </div>
                            <ul>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label" style="width: 56px">教室名称</label>
                <div class="layui-input-inline" style="margin-right: 0">
                    <div class="j-search-con single-box" formAlias = "jsxx" fieldAlias = "jsxx_jsmc">
                        <input type="text" name="jsxx_jsmc" placeholder="请选择" readonly="" class="schoolSel" />
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索" />
                                <span></span>
                            </div>
                            <ul>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label" style="width: 72px">是否启用</label>
                <div class="layui-input-inline" style="margin-right: 0">
                    <div class="j-search-con single-box">
                        <input type="text" name="jsxx_sfqy" placeholder="请选择" readonly="" class="schoolSel" />
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <ul>
                                <li value="是">是</li>
                                <li value="否">否</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-inline layui-inline-btns">
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                <button type="submit" class="layui-btn" lay-submit lay-filter="selClassroomTable">查询</button>
            </div>
        </form>
        <div class="courseList">
            <table class="layui-hide" id="selClassroomList" lay-filter="selClassroomList"></table>
        </div>
    </div>
    <div class="dialog-footer">
        <button class="btnCancel">取消</button>
        <button id="classroomSureBtn">确定</button>
    </div>
</div>
<!-- 添加教学班 -->
<div class="dialog" id="addClass" style="display: none">
    <div class="dialog-title">添加<span class="dialog-close"></span></div>
    <div class="dialog-con">
        <form action="" class="layui-form form-class">
            <div class="layui-form-item">
                <label class="layui-form-label layui-label-require">教学班类型</label>
                <div class="layui-input-inline">
                    <div class="j-search-con single-box">
                        <input type="text" name="teachClassroomType" placeholder="请选择" readonly="" class="schoolSel" />
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <ul>
                                <li value="父教学班">父教学班</li>
                                <li value="子教学班">子教学班</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">学时类型</label>
                <div class="layui-input-inline">
                    <div class="j-search-con single-box">
                        <input type="text" name="hourType" placeholder="请选择" readonly="" class="schoolSel" />
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <ul>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label layui-label-require">教学班组成</label>
                <div class="layui-input-inline">
                    <div class="j-search-con multiple-box">
                        <input type="text" name="compose" placeholder="请选择" readonly="" class="schoolSel" />
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索" />
                                <span></span>
                            </div>
                            <ul>

                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="dialog-footer">
        <button class="btnCancel">取消</button>
        <button id="addClassSureBtn">确定</button>
    </div>
</div>
</body>

<!-- 删除 -->
<script type="text/html" id="tmplToolBar2">
    <!-- <div class="delet" lay-event="clear" style="margin-right: 8px;">清除</div> -->
    {{# if(d.level ==0 ){ }}
    <div class="add-node" lay-event="addChild">添加</div>
    {{# } }}
    <div class="delet" lay-event="del">删除</div>
</script>

<!-- 已安排人数 -->
<script type="text/html" id="titleTpl">
    <div class="stu">
        {{# if(d.arrangedNumber !=0 && d.arrangedNumber!=undefined){ }}
        <span lay-event="sel-stu">已指定{{d.arrangedNumber}}人</span>
        {{# } else { }}
        <span class="selStu" lay-event="sel-stu">点击选择人员</span>
        {{# } }}
    </div>
</script>

<script th:src="${_CPR_+_VR_+'/js/jquery-3.3.1.min.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/my.util.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/cultivation/slideCommon2.js'}"></script>
<script th:src="@{${_CPR_+_VR_}+'/js/cultivation/courseNew.js'(v=${new java.util.Date().getTime()})}"></script>
<script th:inline="javascript">
    document.domain = "chaoxing.com";
    const _VR_ = [[${_VR_}]];
    var fid = [[${fid}]];
    var uid = [[${uid}]];
    var formUserId = [[${formUserId}]];
    var semester = [[${teachPlan?.jxjhgl_xnxq}]];
    var totalClassHour = [[${teachPlan?.jxjhgl_zxs}]];
    var teachPlan = [[${teachPlan}]];
    let startWeek = [[${semester?.xnxq_qsz}]];
    let endWeek = [[${semester?.xnxq_jsz}]];
    $("body")
        .off("click")
        .on("click", ".j-table-class .j-search-con.single-box .schoolSel", function (e) {
            let parent = $(this).parent();
            var offsetObj = $(this).offset();
            var slideH = $(this).outerHeight(true);
            var scrollTop = $(document).scrollTop();
            parent.find(".j-select-year").css({
                position: "fixed",
                width: "180px",
                top: offsetObj.top + slideH - scrollTop + 4 + "px",
                left: offsetObj.left + "px",
            });
            e.stopPropagation();
        });
</script>
</html>
