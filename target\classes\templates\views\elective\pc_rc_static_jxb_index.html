<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" li-content="IE=edge">
    <meta name="viewport" li-content="width=device-width, initial-scale=1.0">
    <title>微点名统计</title>
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/global.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/layui1/css/layui.css'">
    <link rel="stylesheet" href="css/commonPC.css">
    <link rel="stylesheet" href="css/reset.css">
    <link rel="stylesheet" href="css/microdot-name.css">
    <script th:src="${_CPR_}+'/elective/layui1/layui.js'"></script>
    <style>
        .course-list ul li .text p {
            margin-bottom: 5px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .course-list ul li .text p span {
            width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /*//2025.3.19 start*/
        .w240 {
            width: 230px !important;
        }

        body .main .con .filter-box {
            margin-bottom: 12px;
            display: block;
        }

        body .main .con .table .course-list {
            min-height: calc(100vh - 330px)
        }

        /*//2025.3.19 end*/
    </style>

</head>

<body>
<div class="main">
    <div class="top">
        <div class="title">
            <div class="back" style="display: none"><a href="#" onclick="javascript:window.history.back();">返回</a></div>
            <div class="level-name">班级点名统计</div>
        </div>
        <!--        <div class="btns-list">-->
        <!--            <div class="export">查看点名记录</div>-->
        <!--        </div>-->
    </div>
    <div class="con">
        <div class="filter-box">
            <div class="layui-form" action="">
                <div class="layui-form-item">
                    <label class="layui-form-label">课程名称</label>
                    <div class="layui-input-block w240">
                        <select name="typeCover1" lay-filter="typeCover1" id="typeCover1" lay-search="">
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">教学班名称</label>
                    <div class="layui-input-block w240">
                        <select name="typeCover2" lay-filter="typeCover2" id="typeCover2" lay-verify="required"
                                lay-search="">
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">班级</label>
                    <div class="layui-input-block w240">
                        <select name="class" lay-filter="class" id="class" lay-verify="required" lay-search="">
                        </select>
                    </div>
                </div>
            </div>
            <div class="layui-form" action="" lay-filter="myFormFilter">
                <div class="layui-form-item">
                    <label class="layui-form-label">周次</label>
                    <div class="layui-input-block" style="width: 258px;">
                        <select name="weekbyweek" lay-filter="weekbyweek" id="weekbyweek" lay-verify="required"
                                lay-search="">
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">星期</label>
                    <div class="layui-input-block" style="width: 272px;">
                        <select name="weeks" lay-filter="weeks" id="weeks" lay-verify="required" lay-search="">
                            <option value="">请选择</option>
                            <option value="一">星期一</option>
                            <option value="二">星期二</option>
                            <option value="三">星期三</option>
                            <option value="四">星期四</option>
                            <option value="五">星期五</option>
                            <option value="六">星期六</option>
                            <option value="日">星期日</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">课节</label>
                    <div class="layui-input-block w240">
                        <select name="lessonSchedule" lay-filter="lessonSchedule" id="lessonSchedule"
                                lay-verify="required"
                                lay-search="">
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">日期选择</label>
                    <div class="layui-input-block ">
                        <div class="times w240">
                            <input type="text" name="time" readonly="" placeholder="请选择" class="layui-input"
                                   id="startTime" lay-key="1">
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-input-block w240">
                        <div class="btns">
                            <div class="searchs">查询</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="table" style="display:block;">
            <div class="course-list">
                <ul>
                </ul>
            </div>
            <div id="coursePage"></div>

        </div>
        <div class="no-data" style="display:none;">
            <img src="images/no-datas.png" alt="">
            <p>暂无统计数据</p>
        </div>
    </div>
</div>


</body>

<script th:src="${_CPR_}+'/elective/js/jquery-3.3.1.min.js'"></script>
<script>
    let pageIndex = 1;
    let limit = 10;
    var js = '[[${js}]]';
    if (js) {
        $(".back").show();
    }
    layui.use(['table', 'jquery', 'laypage', 'laydate', 'form'], function () {

        var table = layui.table;
        var laypage = layui.laypage;
        var $ = layui.jquery;
        var layer = layui.layer;
        var laydate = layui.laydate;
        var form = layui.form;

        var now = new Date();
        var year = now.getFullYear();
        var month = now.getMonth() + 1;
        var day = now.getDate();

        var formattedDate = year + '-' + (month < 10 ? '0' + month : month) + '-' + (day < 10 ? '0' + day : day);

        //日期选择
        laydate.render({
            elem: '#startTime',
            format: 'yyyy-MM-dd',
            type: 'date',
            range: '~',
            value: formattedDate +' ~ ' +formattedDate,
            done: function(value, date) {
                // 在选择日期后执行的操作
                // 这里将值置空
                // this.config.value = ''; // 或者使用 this.config({value: ''});
                form.val("myFormFilter", { // myFormFilter 是表单定义的lay-filter的值
                    "weekbyweek": '',
                    "weeks": '',
                    "lessonSchedule": '',
                });
            }
        });
        // $(".export").click(function (){
        //     location.href = "/elective/rollcall/staticIndex?model=pc&js=1";
        // })
        form.on('select(weekbyweek)', function(data){
            console.log(data.elem); // 获取当前select原始DOM对象
            console.log(data.value); // 获取被选中的值
            console.log(data.othis); // 获取美化后的DOM对象，如果第二个参数为'bar'，则表示bar容器中的select原始DOM对象
            // 这里可以执行一些操作，比如根据选中的值动态加载数据等
            // form.val("myFormFilter", {
            //     "startTime": '',
            // });
            $('#startTime').val('');
        });
        form.on('select(weeks)', function(data){
            $('#startTime').val('');

        });
        form.on('select(lessonSchedule)', function(data){
            $('#startTime').val('');

        });



        $(".searchs").click(function () {
            pageIndex = 1;
            getList();
        })

        var height = $(window).height() - 200;
        $("#iframepage").attr("height", height);


        $(".tab").on("click", "ul li", function () {
            $(this).addClass("cur").siblings().removeClass("cur");
        })

        //返回

        $(".main .top .title .back").click(function () {
            $(".details-main").hide();
            $(".table-main").show();
        })

        getList();

        function getList() {
            var courseTime = $("#startTime").val();
            var course = $('select[name="typeCover1"] option:selected').val();
            var jxbCode = $('select[name="typeCover2"] option:selected').val();
            var clazzCode = $('select[name="class"] option:selected').val();
            var zc = $('select[name="weekbyweek"] option:selected').val();
            var xq = $('select[name="weeks"] option:selected').val();
            var kj = $('select[name="lessonSchedule"] option:selected').val();
            var teachingClassName = "";
            $.ajax({
                type: "POST",
                url: "/elective/rollcall/getRollcallstaticJXBPage",
                data: {
                    courseTime: courseTime,
                    jxbCode: jxbCode,
                    course: course,
                    pageSize: limit,
                    curPage: pageIndex,
                    clazzCode: clazzCode,
                    zc: zc,
                    xq: xq,
                    kj: kj,
                    js: js
                },
                dataType: 'json',
                success: function (data) {
                    var html = "";
                    if (data.count == 0) {
                        $(".table").hide();
                        $(".no-data").show();
                        return;
                    } else {
                        $(".table").show();
                        $(".no-data").hide();
                    }
                    laypage.render({
                        elem: 'coursePage',
                        groups: 5,
                        limit: limit,
                        limits: [10, 20, 30],
                        count: data.count, //数据总数，从服务端得到
                        curr: pageIndex,
                        layout: ['count', 'prev', 'page', 'next', 'limit', 'limits', 'skip'],
                        jump: function (obj, first) {
                            if (!first) {
                                pageIndex = obj.curr;
                                limit = obj.limit;
                                getList();
                            }
                        }
                    });
                    for (var i = 0; i < data.data.length; i++) {
                        html += "<li style='width:20%;'>";
                        html += "<div class='li-con'>";
                        html += "<div class='img'>";
                        html += "<img src='images/image01.png' alt=''>";
                        html += "</div>";
                        html += "<div class='text' style='height:auto;'>";
                        html += "<h3>" + data.data[i].course + "</h3>";
                        html += "<p>";
                        html += "<span>" + data.data[i].teachingClassName + "</span>";
                        html += "</p>";
                        html += "<p>";
                        html += "<span>" + data.data[i].teacherName + "</span>";
                        html += "</p>";
                        html += "<div class='state edit' onclick=\"toJxbDetail('" + data.data[i].teachingClassCode + "','" + data.data[i].teachingClassName + "','" + data.data[i].course + "','" + data.data[i].teacherName + "')\" >";
                        // html += "<div class='state edit'>";
                        html += "<span>查看</span>";
                        html += "</div>";
                        html += "</div>";
                        html += "</div>";
                        html += "</li>";
                        // html+="<a href=\"javascript:toRollcall('/elective/rollcall/teacherRollcall?teachingClassCode="+data.list[i].pkjgsj_jxbbh+"&teachingClassName="+data.list[i].pkjgsj_jxbmc+"&courseName="+data.list[i].pkjgsj_kcmc+"&zc="+data.list[i].pkjgsj_zc+"&kj="+data.list[i].pkjgsj_kj+"&xq="+data.list[i].pkjgsj_xq+"&classDate="+data.courseTime+"')\">";
                    }
                    $(".course-list ul").html(html);
                }
            });
        }


        $.ajax({
            type: "POST",
            url: "/elective/rollcall/getCourseListV1",
            dataType: 'json',
            success: function (data) {
                var html = "<option value=''>请选择</option>";
                if (data.status) {
                    for (var i = 0; i < data.list.length; i++) {
                        html += "<option value='" + data.list[i].mc + "'>" + data.list[i].mc + "</option>";
                    }
                }
                $("#typeCover1").html(html);
                layui.form.render("select");
                form.render();
            }
        });

        $.ajax({
            type: "POST",
            url: "/elective/rollcall/getJxbNameList",
            dataType: 'json',
            success: function (data) {
                var html = "<option value=''>请选择</option>";
                if (data.status) {
                    for (var i = 0; i < data.data.length; i++) {
                        html += "<option value='" + data.data[i].teachingClassCode + "'>" + data.data[i].teachingClassName + "</option>";
                    }
                }
                $("#typeCover2").html(html);
                layui.form.render("select");
            }
        });

        $.ajax({
            type: "POST",
            url: "/elective/rollcall/getXzbList",
            dataType: 'json',
            success: function (data) {
                var html = "<option value=''>请选择</option>";
                if (data.status) {
                    for (var i = 0; i < data.data.length; i++) {
                        html += "<option value='" + data.data[i].administrationClassCode + "'>" + data.data[i].administrationClassName + "</option>";
                    }
                }
                $("#class").html(html);
                layui.form.render("select");
            }
        });

        $.ajax({
            type: "POST",
            url: "/elective/rollcall/getZcXqKjList",
            data: {column: "zc"},
            dataType: 'json',
            success: function (data) {
                var html = "<option value=''>请选择</option>";
                if (data.status) {
                    for (var i = 0; i < data.data.length; i++) {
                        html += "<option value='" + data.data[i].zc + "'>" + getZcVal(data.data[i].zc) + "</option>";
                    }
                }
                $("#weekbyweek").html(html);
                layui.form.render("select");
            }
        });

        // $.ajax({
        //     type: "POST",
        //     url: "/elective/rollcall/getZcXqKjList",
        //     data: {column: "xq"},
        //     dataType: 'json',
        //     success: function (data) {
        //         var html = "<option value=''>请选择</option>";
        //         if (data.status) {
        //             for (var i = 0; i < data.data.length; i++) {
        //                 html += "<option value='" + data.data[i].xq + "'>" + getXqVal(data.data[i].xq) + "</option>";
        //             }
        //         }
        //         $("#weeks").html(html);
        //         layui.form.render("select");
        //     }
        // });

        $.ajax({
            type: "POST",
            url: "/elective/rollcall/getZcXqKjList",
            data: {column: "kj"},
            dataType: 'json',
            success: function (data) {
                var html = "<option value=''>请选择</option>";
                if (data.status) {
                    for (var i = 0; i < data.data.length; i++) {
                        html += "<option value='" + data.data[i].kj + "'>" + getKjVal(data.data[i].kj) + "</option>";
                    }
                }
                $("#lessonSchedule").html(html);
                layui.form.render("select");
            }
        });


    });
    var ZcArr = ["", "一", "二", "三", "四", "五", "六", "七", "八", "九", "十", "十一", "十二", "十三", "十四", "十五", "十六",
        "十七", "十八", "十九", "二十", "二十一", "二十二", "二十三", "二十四", "二十五", "二十六", "二十七", "二十八", "二十九", "三十",
        "三十一"];

    function getZcVal(val) {
        return "第"+ZcArr[val]+"周";
    }

    function getXqVal(val) {
        return "星期"+ val;
    }

    function getKjVal(val) {
        return "第"+ZcArr[val]+"节";
    }

    function toJxbDetail(jxbbh, jxbmc,course,teacherName) {
        var courseTime = $("#startTime").val();
        var clazzCode = $('select[name="class"] option:selected').val();
        var zc = $('select[name="weekbyweek"] option:selected').val();
        var xq = $('select[name="weeks"] option:selected').val();
        var kj = $('select[name="lessonSchedule"] option:selected').val();
        var clazzName = $('select[name="class"] option:selected').text().replaceAll("请选择","");
        window.location.href = "/elective/rollcall/toJxbStaticDetail?teachingClassCode=" + jxbbh +
            "&teachingClassName=" + jxbmc + "&courseTime=" + courseTime+ "&course=" + course
            + "&clazzCode=" + clazzCode+ "&zc=" + zc+ "&xq=" + xq+ "&kj=" + kj+ "&teacherName=" + teacherName+ "&clazzName=" + clazzName;
    }

</script>

</html>