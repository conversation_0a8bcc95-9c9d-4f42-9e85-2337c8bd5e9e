<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>课程校验</title>
    <th:block th:include="common :: header('课程校验')"/>
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/global.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/layui/css/layui.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/reset.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/selCourseSet.css'">
</head>
<body>
	<input type="hidden" id="jhids">
    <div class="z-main">
        <div class="z-title">
            <h3>课程校验</h3>
            <!-- <div id="saveBth"> 保存设置</div> -->
        </div>
        <div class="z-tab">
            <ul>
                <li class="active">跨学期选课</li>
                <li>先修课程校验</li>
            </ul>
        </div>
        <div class="z-box">
            <!-- 跨学期选课 -->
            <div class="box-con box-common" style="display: block;">
                <div class="z-search">
                    <form action="" class="layui-form">
                        <div class="layui-form-item">
                            <label class="layui-form-label" style="width: 88px;">历史选课计划</label>
                            <div class="layui-input-block " style="margin-left: 103px;">
                                <div class="j-search-con multiple-box">
                                    <input type="text" name="teacherName" placeholder="请选择" readonly=""
                                        class="schoolSel">
                                    <span class="j-arrow"></span>
                                    <div class="j-select-year">
                                        <div class="search">
                                            <input type="text" placeholder="搜索">
                                            <span></span>
                                        </div>
                                        <ul name="teacherName" id="searchJhUl">
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="z-btns" style="margin-bottom: 18px;">
                    <div class="addRule" id="addTermCourse"><img th:src="${_CPR_}+'/elective/images/add-icon.png'">添加跨学期课程</div>
                    <div class="del">删除</div>
                </div>
                <div class="z-table">
                    <table class="layui-hide materialTable6" id="materialTable6" lay-filter="materialTable6">
                    </table>
                </div>
            </div>
            <!-- 先修课程校验 -->
            <div class="box-con box-common" style="display: none;">
                <div class="z-search">
                    <form action="" class="layui-form" onsubmit="return false">
                    	<div class="layui-form-item">
                            <label class="layui-form-label">所在校区 </label>
                            <div class="layui-input-block">
                                <div class="j-search-con multiple-box">
                                    <input type="text" name="teacherName" placeholder="请选择" readonly=""
                                        class="schoolSel" id="beforeCourseXqIp">
                                    <span class="j-arrow"></span>
                                    <div class="j-select-year">
                                        <div class="search">
                                            <input type="text" placeholder="搜索">
                                            <span></span>
                                        </div>
                                        <ul name="teacherName" id="beformCourseXqUl">
                                            <li data-id="0" class="" th:each="data:${addXq}" th:text="${data}"></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">所属年级</label>
                            <div class="layui-input-block">
                                <div class="j-search-con multiple-box">
                                    <input type="text" name="teacherName" placeholder="请选择" readonly=""
                                        class="schoolSel" id="beforeCourseNjIp">
                                    <span class="j-arrow"></span>
                                    <div class="j-select-year">
                                        <div class="search">
                                            <input type="text" placeholder="搜索">
                                            <span></span>
                                        </div>
                                        <ul name="teacherName" id="beformCourseNjUl">
                                            <li data-id="0" class="" th:each="data:${addNj}" th:text="${data}"></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">所属院系 </label>
                            <div class="layui-input-block">
                                <div class="j-search-con multiple-box">
                                    <input type="text" name="teacherName" placeholder="请选择" readonly=""
                                        class="schoolSel" id="beforeCourseYxIp">
                                    <span class="j-arrow"></span>
                                    <div class="j-select-year">
                                        <div class="search">
                                            <input type="text" placeholder="搜索">
                                            <span></span>
                                        </div>
                                        <ul name="teacherName" id="beformCourseYxUl">
                                            <li data-id="0" class="" th:each="data:${addYx}" th:text="${data}"></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">所属专业 </label>
                            <div class="layui-input-block">
                                <div class="j-search-con multiple-box">
                                    <input type="text" name="teacherName" placeholder="请选择" readonly=""
                                        class="schoolSel" id="beforeCourseZyIp">
                                    <span class="j-arrow"></span>
                                    <div class="j-select-year">
                                        <div class="search">
                                            <input type="text" placeholder="搜索">
                                            <span></span>
                                        </div>
                                        <ul name="teacherName" id="beformCourseZyUl">
                                            <li data-id="0" class="" th:each="data:${addZy}" th:text="${data}"></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label"> 所在班级 </label>
                            <div class="layui-input-block">
                                <div class="j-search-con multiple-box">
                                    <input type="text" name="teacherName" placeholder="请选择" readonly=""
                                        class="schoolSel" id="beforeCourseBjIp">
                                    <span class="j-arrow"></span>
                                    <div class="j-select-year">
                                        <div class="search">
                                            <input type="text" placeholder="搜索">
                                            <span></span>
                                        </div>
                                        <ul name="teacherName" id="beformCourseBjUl">
                                            <li data-id="0" class="" th:each="data:${addBj}" th:text="${data}"></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button class="z-btn">查询</button>
                        <div class="clear"></div>
                    </form>
                </div>
                <div class="z-btns" style="margin-bottom: 18px;">
                    <div class="addRule" id="addCourseCheck"><img th:src="${_CPR_}+'/elective/images/add-icon.png'">添加需校验的课程</div>
                    <div class="del" id="delBeforeCoursebtn">删除</div>
                </div>
                <div class="z-table">
                    <table class="layui-hide materialTable7" id="materialTable7" lay-filter="materialTable7">
                    </table>
                </div>
            </div>
        </div>
    </div>
    <!-- 跨学期课程 -->
    <div id="termCourse" class="dialog">
        <div class="dialog-title">
            <h3>跨学期课程 </h3><span class="pu-close"></span>
        </div>
        <div class="tips" style="margin-bottom: 20px;">限制同一个编组内选修课的可选数量</div>
        <div class="dialog-con">
            <form action="" class="layui-form layui-form-left">
                <div class="layui-form-item">
                    <label class="layui-form-label">已选选课计划 </label>
                    <div class="layui-input-block">
                        <div class="j-search-con single-box">
                            <input type="text" name="teacherName" placeholder="请选择" readonly="" class="schoolSel" id="addJhIp">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul name="teacherName" id="addJhUl">
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">已修选修课 </label>
                    <div class="layui-input-block">
                        <div class="j-search-con single-box">
                            <input type="text" name="teacherName" placeholder="请选择" readonly="" class="schoolSel" id="addKcIp">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul name="teacherName" id="addKcUl">
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </form>

        </div>
        <div class="dialog-btn"><button class="pu-cancel">取消</button><button class="pu-sure"
                id="termCourseSure">确定</button>
        </div>
    </div>
    <!-- 跨学期课程-查看 -->
    <div id="termCourseCheck" class="dialog dialogCheck">
        <div class="dialog-title">
            <h3>跨学期课程详情</h3><span class="pu-close"></span>
        </div>
        <div class="dialog-con">
            <dl>
                <dt>已选修课名称：</dt>
                <dd>计算机</dd>
            </dl>
            <dl>
                <dt>已选修课计划：</dt>
                <dd>计算机</dd>
            </dl>
            <dl>
                <dt>所属年级：</dt>
                <dd>1年级</dd>
            </dl>
            <dl>
                <dt>所属院系：</dt>
                <dd>计算机学院</dd>
            </dl>
            <dl>
                <dt>开课专业：</dt>
                <dd>计算机技术</dd>
            </dl>
            <dl>
                <dt>开课校区：</dt>
                <dd>南校区</dd>
            </dl>

        </div>
    </div>
    <!-- 先修课程校验 -->
    <div id="courseCheck" class="dialog">
        <div class="dialog-title">
            <h3>先修课程校验</h3><span class="pu-close"></span>
        </div>
        <div class="dialog-con">
            <form action="" class="layui-form" onsubmit="return false">
                <div class="layui-form-item">
                    <label class="layui-form-label">所属年级 </label>
                    <div class="layui-input-block">
                        <div class="j-search-con multiple-box">
                            <input type="text" value="全部" name="teacherName" placeholder="请选择" readonly=""
                                class="schoolSel" id="addBeforeCourseNjIp">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul name="teacherName" id="addBeforeCourseNjUl">
                                    <li data-id="" class="all">全部</li>
                                    <li data-id="0" class="" th:each="data:${addNj}" th:text="${data}"></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">所在校区 </label>
                    <div class="layui-input-block">
                        <div class="j-search-con multiple-box">
                            <input type="text" value="全部" name="teacherName" placeholder="请选择" readonly=""
                                class="schoolSel" id="addBeforeCourseXqIp">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul name="teacherName" id="addBeforeCourseXqUl">
                                    <li data-id="" class="all">全部</li>
                                    <li data-id="0" class="" th:each="data:${addXq}" th:text="${data}"></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">所属院系 </label>
                    <div class="layui-input-block">
                        <div class="j-search-con multiple-box">
                            <input type="text" value="全部" name="teacherName" placeholder="请选择" readonly=""
                                class="schoolSel" id="addBeforeCourseYxIp">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul name="teacherName" id="addBeforeCourseYxUl">
                                    <li data-id="" class="all">全部</li>
                                    <li data-id="0" class="" th:each="data:${addYx}" th:text="${data}"></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">所属专业 </label>
                    <div class="layui-input-block">
                        <div class="j-search-con multiple-box">
                            <input type="text" value="全部" name="teacherName" placeholder="请选择" readonly=""
                                class="schoolSel" id="addBeforeCourseZyIp">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul name="teacherName" id="addBeforeCourseZyUl">
                                    <li data-id="" class="all">全部</li>
                                    <li data-id="0" class="" th:each="data:${addZy}" th:text="${data}"></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">所在班级 </label>
                    <div class="layui-input-block">
                        <div class="j-search-con multiple-box">
                            <input type="text" value="全部" name="teacherName" placeholder="请选择" readonly=""
                                class="schoolSel" id="addBeforeCourseBjIp">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul name="teacherName" id="addBeforeCourseBjUl">
                                    <li data-id="" class="all">全部</li>
                                    <li data-id="0" class="" th:each="data:${addBj}" th:text="${data}"></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">需校验的课程 </label>
                    <div class="layui-input-block">
                        <div class="j-search-con single-box">
                            <input type="text" value="全部" name="teacherName" placeholder="请选择需要校验的课程" readonly="readonly"
                                class="schoolSel" id="addBeforeCourseCIp">
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">先修课程名称 </label>
                    <div class="layui-input-block">
                        <div class="j-search-con multiple-box">
                            <input type="text" value="全部" name="teacherName" placeholder="请选择需要校验的课程" readonly=""
                                class="schoolSel" id="addBeforeCourseBeforeIp">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul name="teacherName" id="addBeforeCourseBeforeUl">
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">先修课程分数</label>
                    <div class="layui-input-block set-score">
                        <input type="number" min="0" name="relationName" lay-verify="required" placeholder="请输入"
                            autocomplete="off" class="layui-input" id="addBeforeCourseScoreUl">
                    </div>
                </div>
            </form>
        </div>
        <div class="dialog-btn"><button class="pu-cancel">取消</button><button class="pu-sure"
                id="courseCheckSure">确定</button>
        </div>
    </div>
    <!-- 先修课程校验-查看 -->
    <div id="courseFirstCheck" class="dialog dialogCheck">
        <div class="dialog-title">
            <h3>先修课程校验详情</h3><span class="pu-close"></span>
        </div>
        <div class="dialog-con">
            <dl>
                <dt>名称：</dt>
                <dd>计算机</dd>
            </dl>
            <dl>
                <dt>所属年级：</dt>
                <dd>1年级</dd>
            </dl>
            <dl>
                <dt>所属院系：</dt>
                <dd>计算机学院</dd>
            </dl>
            <dl>
                <dt>所属专业：</dt>
                <dd>计算机技术</dd>
            </dl>
            <dl>
                <dt>所在班级：</dt>
                <dd>1班</dd>
            </dl>

            <dl>
                <dt>所在校区：</dt>
                <dd>南校区</dd>
            </dl>
            <dl>
                <dt>先修课程名称：</dt>
                <dd>xx</dd>
            </dl>
            <dl>
                <dt>校检分数：</dt>
                <dd>5</dd>
            </dl>
        </div>
    </div>
</body>
<script type="text/html" id="tmplToolBar">
            <div class="oprate-table">
                <span class="edit" lay-event="check">查看</span>
                <span class="delete" lay-event="delete">删除</span>
            </div>
        </script>
<script type="text/html" id="tmplToolBar2">
            <div class="oprate-table">
                <span class="edit" lay-event="check">查看</span>
                <span class="delete" lay-event="delete">删除</span>
            </div>
        </script>
<script type="text/javascript">
	var fid = [[${fid}]];
	var formId = [[${formId}]];
	var formUserId = [[${formUserId}]];
	var bjh = '[[${bjh}]]';
	var bid = [[${bid}]];
	// var addNj = '[[${addNj}]]';
	// var addYx = '[[${addYx}]]';
	// var addXq = '[[${addXq}]]';
	var thisCourse = '[[${thisCourse}]]';
	var thiskskc = '[[${thiskskc}]]';
	$("#addBeforeCourseCIp").val(thisCourse);
	// var addNjs = addNj.split(",");
	// var h = "<li data-id=\"\" class=\"all\">全部</li>";
	// for(var i = 0;i<addNjs.length;i++){
	// 	h += "<li data-id=\""+i+"\">"+addNjs[i]+"</li>";
	// }
    // $("#addBeforeCourseNjUl").html(h);
    // var addYxs = addYx.split(",");
    // var h2 = "<li data-id=\"\" class=\"all\">全部</li>";
	// for(var i = 0;i<addYxs.length;i++){
	// 	h2 += "<li data-id=\""+i+"\">"+addYxs[i]+"</li>";
	// }
    // $("#addBeforeCourseYxUl").html(h2);
	$.ajax({
  		type: "POST",
  		url: "/elective/course/rule/getHistoryPlan",
  		data:{fid:fid},
  		dataType: 'json',
  		success: function (result) {
  	    	var html = "";
  	    	if (result) {
  	        	for(var i = 0;i<result.list.length;i++){
  	        		html += "<li data-id=\""+result.list[i].id+"\" >"+result.list[i].xkjhbJhmc+"</li>";
  	        	}
  	    	}
  	    	$("#searchJhUl").html(html);
  	    	$("#addJhUl").html(html);
  		}
  	});
  	// $.ajax({
    // 	type: "POST",
    // 	url: "/elective/getNj",
    // 	data:{fid:fid},
    // 	dataType: 'json',
    // 	success: function (result) {
    //     	var html = "";
    //       	var html2 = "<li data-id=\"\" class=\"all\">全部</li>";
    //     	if (result) {
    //         	for(var i = 0;i<result.data.length;i++){
    //         		var name = result.data[i].name;
    //         		var id = result.data[i].id;
    //         		html += "<li data-id=\""+id+"\">"+name+"</li>";
    //         		html2 += "<li data-id=\""+id+"\">"+name+"</li>";
    //         	}
    //     	}
    //         $("#beformCourseNjUl").html(html);
   	// 	}
	// });
	// $.ajax({
    // 	type: "POST",
    //     url: "/elective/getYX",
    //     data:{fid:fid},
    // 	dataType: 'json',
    // 	success: function (result) {
    //     	var html = "";
    //       	var html2 = "<li data-id=\"\" class=\"all\">全部</li>";
    //     	if (result) {
    //         	for(var i = 0;i<result.data.length;i++){
    //         		var name = result.data[i].name;
    //         		var id = result.data[i].id;
    //         		html += "<li data-id=\""+id+"\">"+name+"</li>";
    //         		html2 += "<li data-id=\""+id+"\">"+name+"</li>";
    //         	}
    //     	}
    //         $("#beformCourseYxUl").html(html);
   	// 	}
	// });
	// $.ajax({
    //      type: "POST",
    //      url: "/elective/getZY",
    //      data:{fid:fid},
    //      dataType: 'json',
    //      success: function (result) {
    //      	var html = "";
    //      	var html2 = "<li data-id=\"\" class=\"all\">全部</li>";
    //         if (result) {
	//            	for(var i = 0;i<result.data.length;i++){
	//            		var name = result.data[i].name;
	//            	 	var id = result.data[i].id;
	//            	    html += "<li data-id=\""+id+"\">"+name+"</li>";
	//            	    html2 += "<li data-id=\""+id+"\">"+name+"</li>";
	//            	}
	//          }
	//          $("#beformCourseZyUl").html(html);
	//          $("#addBeforeCourseZyUl").html(html2);
   	// 	}
	// });
	// $.ajax({
    // 	type: "POST",
    // 	url: "/elective/getBj",
    //     data:{fid:fid},
    //     dataType: 'json',
    //     success: function (result) {
    //     	var html = "";
    //       	var html2 = "<li data-id=\"\" class=\"all\">全部</li>";
    //         if (result) {
    //         	for(var i = 0;i<result.data.length;i++){
    //         		var name = result.data[i].name;
    //         	    var id = result.data[i].id;
    //         	    html += "<li data-id=\""+id+"\">"+name+"</li>";
    //         	   	html2 += "<li data-id=\""+id+"\">"+name+"</li>";
    //         	}
    //         }
    //         $("#beformCourseBjUl").html(html);
    //    	}
	// });
	// $.ajax({
    // 	type: "POST",
    //     url: "/elective/getXQ",
    //     data:{fid:fid},
    //     dataType: 'json',
    //     success: function (result) {
    //     	var html = "";
    //       	var html2 = "<li data-id=\"\" class=\"all\">全部</li>";
    //         if (result) {
    //         	for(var i = 0;i<result.data.length;i++){
    //         		var name = result.data[i].name;
    //         	    var id = result.data[i].id;
    //         	    html += "<li data-id=\""+id+"\">"+name+"</li>";
    //         	    html2 += "<li data-id=\""+id+"\">"+name+"</li>";
    //         	}
    //         }
    //         $("#beformCourseXqUl").html(html);
    //         if (addXq ==''){
    //             $("#addBeforeCourseXqUl").html(html2);
    //         }else {
    //             var addXqs = addXq.split(",");
    //             var h3 = "<li data-id=\"\" class=\"all\">全部</li>";
    //             for(var i = 0;i<addXqs.length;i++){
    //                 h3 += "<li data-id=\""+i+"\">"+addXqs[i]+"</li>";
    //             }
    //             $("#addBeforeCourseXqUl").html(h3);
    //         }
    //    }
	// });
	// var addBeforeCourseYx = $("#addBeforeCourseYxIp").val();
	// var addBeforeCourseZy = $("#addBeforeCourseZyIp").val();
	// var addBeforeCourseNj = $("#addBeforeCourseNjIp").val();
	// var addBeforeCourseXq = $("#addBeforeCourseXqIp").val();
	// if(addBeforeCourseNj == '全部'){
    // 	addBeforeCourseNj = addNj;
	// }
	// $.ajax({
	// 	type: "POST",
	// 	url: "/elective/getBj",
	// 	data:{fid:fid,addScoreZy:addBeforeCourseZy,addScoreYx:addBeforeCourseYx,addYx:addYx,addScoreXq:addBeforeCourseXq,addScoreNj:addBeforeCourseNj},
	// 	dataType: 'json',
	// 	success: function (result) {
	// 		var html2 = "";
	// 		if (result.data.length>0) {
	// 	    	html2 += "<li data-id=\"\" class=\"all\">全部</li>";
	// 	    	for(var i = 0;i<result.data.length;i++){
	// 	        	var name = result.data[i].name;
	// 	        	var id = result.data[i].id;
	// 	        	html2 += "<li data-id=\""+id+"\">"+name+"</li>";
	// 	    	}
	// 		}else{
	// 	    	$("#addBeforeCourseBjIp").val("");
	// 	 	}
	// 		$("#addBeforeCourseBjUl").html(html2);
	// 	}
	// });
	// $("#addBeforeCourseBjIp").click(function () {
    //   	var addScoreYx = $("#addBeforeCourseYxIp").val();
    //   	var addScoreZy = $("#addBeforeCourseZyIp").val();
    //   	var addScoreNj = $("#addBeforeCourseNjIp").val();
    //   	var addScoreXq = $("#addBeforeCourseXqIp").val();
    //   	if(addScoreNj == '全部'){
    //   		addScoreNj = addNj;
    //   	}
	// 	$.ajax({
	//     	type: "POST",
	//     	url: "/elective/getBj",
	//     	data:{fid:fid,addScoreZy:addScoreZy,addScoreYx:addScoreYx,addYx:addYx,addScoreXq:addScoreXq,addScoreNj:addScoreNj},
	//     	dataType: 'json',
	//     	success: function (result) {
	//         	var html2 = "";
	//         	if (result.data.length>0) {
	//             	html2 += "<li data-id=\"\" class=\"all\">全部</li>";
	//             	for(var i = 0;i<result.data.length;i++){
	//             		var name = result.data[i].name;
	//             		var id = result.data[i].id;
	//             		html2 += "<li data-id=\""+id+"\">"+name+"</li>";
	//             	}
	//         	}else{
	//             	$("#addBeforeCourseBjIp").val("");
	//          	}
	//            	$("#addBeforeCourseBjUl").html(html2);
	//    		}
	//  	});
	// });
	// $("#addBeforeCourseZyIp").click(function () {
    // 	var addScoreYx = $("#addBeforeCourseYxIp").val();
    //   	if(addScoreYx == '全部'){
    //   		addScoreYx = addYx;
    //   	}
	// 	$.ajax({
	//     	type: "POST",
	//     	url: "/elective/getZY",
	//      	data:{fid:fid,addScoreYx:addScoreYx},
	//       	dataType: 'json',
	//       	success: function (result) {
	//        		var html2 = "<li data-id=\"\" class=\"all\">全部</li>";
	//          	if (result) {
	//             	for(var i = 0;i<result.data.length;i++){
	//             		var name = result.data[i].name;
	//             		var id = result.data[i].id;
	//             	 	html2 += "<li data-id=\""+id+"\">"+name+"</li>";
	//             	}
	//            	}
	//             $("#addBeforeCourseZyUl").html(html2);
	//      	}
	//  	});
	// });
	$("#addBeforeCourseBeforeIp").click(function () {
    	var addBeforeCourseCIp = $("#addBeforeCourseCIp").val();
      	if(addBeforeCourseCIp == ''){
      		return false;
      	}
	   	$.ajax({
	    	type: "POST",
	    	url: "/elective/course/rule/getBeforeCourse",
	    	data:{fid:fid,addBeforeCourseCIp:thiskskc},
	     	dataType: 'json',
	     	success: function (result) {
	        	var html2 = "<li data-id=\"\" class=\"all\">全部</li>";
	         	if (result) {
	            	for(var i = 0;i<result.list.length;i++){
	            		var name = result.list[i].kck_xxkcmc;
	            		html2 += "<li data-id=\""+i+"\">"+name+"</li>";
	        		}
	     		}
	      		$("#addBeforeCourseBeforeUl").html(html2);
	  		}
		});
	});
</script>
<script th:src="${_CPR_}+'/elective/js/jquery-3.3.1.min.js'"></script>
<script th:src="${_CPR_}+'/elective/js/common.js'"></script>
<script th:src="${_CPR_}+'/elective/layui/layui.js'"></script>
<script th:src="${_CPR_}+'/elective/js/courseCheck.js?v=1'"></script>
</html>