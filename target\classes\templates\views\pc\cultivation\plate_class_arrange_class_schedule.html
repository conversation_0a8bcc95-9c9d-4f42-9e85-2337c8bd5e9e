<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上课时间安排</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/global.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/plugin/layui-v2.8.18/layui/css/layui.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/plateClass.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/slideCommon.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/layuiReset.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/plateType.css'}">
    <script th:src="${_CPR_+_VR_+'/plugin/layui-v2.8.18/layui/layui.js'}"></script>
    <style>
        div[lay-id=plateTime] .layui-table-fixed-r .layui-table-body .layui-table-cell {
            height: 34px;
        }

        .cell-blue {
            background-color: #4c85fa;
            color: #fff;
        }

        .main .m-top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-right: 32px;
        }
    </style>
</head>
<body>
<div class="main" style="background-color: #fff;margin: 0 auto;">
    <div class="m-top">
        <div class="title">上课时间</div>
        <div class="top-opt">
            <button type="submit" class="layui-btn layui-btn-primary" id="plateTimeCancle">取消</button>
            <button type="submit" class="layui-btn" id="plateTimeSure">确定</button>
        </div>
    </div>
    <div class="table-opt" style="justify-content:flex-start">
        <button type="submit" class="layui-btn" id="addPlateTime">添加</button>
    </div>
    <div class="table-box">
        <table class="layui-hide" id="plateTime" lay-filter="plateTime"></table>
    </div>
</div>
</body>
<!-- 星期 -->
<script type="text/html" id="selectWeek">
    <div class="j-search-con multiple-box" style="width: 150px;">
        <input type="text" placeholder="请选择" name="week" readonly="" class="schoolSel">
        <span class="j-arrow"></span>
        <div class="j-select-year">
            <ul>
                {{# layui.each(['星期一','星期二','星期三','星期四','星期五'], function(i, v){ }}
                <li value="{{=  }}">{{= v }}</li>
                {{# }); }}
            </ul>
        </div>
    </div>
</script>
<!-- 节次 -->
<script type="text/html" id="selectFestivals">
    <div class="festivals layui-table-cell" style="display: flex;align-items: center;">
        <div class="j-search-con single-box" style="width: 110px;">
            <input type="text" placeholder="请选择" name="startFestivals" readonly="" class="schoolSel">
            <span class="j-arrow"></span>
            <div class="j-select-year">
                <ul>
                </ul>
            </div>
        </div>
        <div style="margin: 0 10px;">-</div>
        <div class="j-search-con single-box" style="width: 110px;">
            <input type="text" placeholder="请选择" name="endFestivals" readonly="" class="schoolSel">
            <span class="j-arrow"></span>
            <div class="j-select-year">
                <ul>
                </ul>
            </div>
        </div>

    </div>
</script>
<!-- 操作 -->
<script type="text/html" id="toolBarOpt">
    <div class="opt-btn-wrap">
        <span class="opt-btn" lay-event="oddWeek">单周</span>
        <span class="opt-btn" lay-event="evenWeek">双周</span>
        <span class="opt-btn" lay-event="allWeek">全选</span>
        <span class="opt-btn color-red" lay-event="clearWeek">清除选择</span>
        <span class="opt-btn color-red" lay-event="del">删除行</span>
    </div>
</script>
<script th:inline="javascript">
    const _VR_ = [[${_VR_}]];
    const fid = [[${fid}]];
    let weeks = [[${term.xnxq_jsz}]]-[[${term.xnxq_qsz}]];
    const detailId = [[${id}]];
</script>
<script th:src="${_CPR_+_VR_+'/js/cultivation/plateTime.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/cultivation/plateCommon.js'}"></script>
</html>