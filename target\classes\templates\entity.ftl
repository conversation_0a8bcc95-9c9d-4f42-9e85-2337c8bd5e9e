package ${pkg};

import com.chaoxing.academic.design.template.bo.TargetPoint;
import com.chaoxing.form.annotation.*;
import com.chaoxing.form.symbol.FormEntity;
import com.chaoxing.form.constant.FormComponentConstants;
import lombok.Data;
import lombok.experimental.Accessors;
import java.io.Serializable;
<#if !subFormId??>
<#if type == 1>
import com.chaoxing.form.pojo.RowInfo;
<#elseif type == 2>
import com.chaoxing.form.pojo.AprvRowInfo;
</#if>
import com.chaoxing.form.annotation.Form;
</#if>
<#list  imports as impt>
import ${impt};
</#list>

/**
 * <#if formAlias??>@formAlias: ${formAlias}</#if>
<#if subFormId??>
 * 子表单对应的组件, id: ${subFormId}, alias: ${subFormAlias}
</#if>
 */
<#if !subFormId??>
@Form(<#if formAlias??>formAlias = "${formAlias}"</#if> )
</#if>
@Data
@Accessors(chain = true)
public class ${entityName} implements FormEntity, Serializable {

    private static final long serialVersionUID = 1L;

<#if !subFormId??>
    public static final String ALIAS = ${entityName}.class.getAnnotation(Form.class).formAlias();

    public static final TargetPoint POINT = new TargetPoint(ALIAS);
</#if>


<#if !subFormId??>
    <#if type == 1>
    /**
     * 表单行信息
     */
    private RowInfo rowInfo;
    <#elseif type == 2>

    /**
     * 审批单行信息
     */
    private AprvRowInfo rowInfo;
    </#if>

</#if>
<#list  fields as field>
    /**
     * ${field.label} ${field.text}
     */
    @FormComponent(id = ${field.formComponentInfo.id}, alias = "${field.formComponentInfo.alias}", componentName = ${field.formComponentInfo.componentName})
    <#if field.datePattern??>
    @DateFormat(value = "${field.datePattern}")
    </#if>
    private ${field.fieldType}<#if field.listFanXing??><${field.listFanXing}></#if> ${field.fieldName};

</#list>
}