<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
        content="width=device-width, user-scalable=no,initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <title>微点名</title>
    <script src="js/responsive.js"></script>
    <link rel="stylesheet" href="css/global.css">
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/record.css?v=1">
</head>
<body>
    <div class="record-list" id="dataBox">
        
    </div>
</body>
<script src="js/zepto.min.js"></script>
<script src="js/iosSelect.js"></script>
<script src="js/CXJSBridge.js"></script>
<script th:inline="javascript">
	var teachingClassCode = [[${teachingClassCode}]];
	var courseName = [[${courseName}]];
	var teachingClassName = [[${teachingClassName}]];
	var fid = [[${fid}]];
    var rollcallStateList = [[${rollcallStateList}]];
	var thisZC = "";
	var thisXQ = "";
	var thisKJ = "";
	var thisDate = "";
	var num = "";
	var num1 = "";
	var num2 = "";
	var num3 = "";
	var num4 = "";
    $(document).ready(function () {
        var stuH = $(window).height() - $(".record-list").offset().top;
        $(".record-list").css("height", stuH + "px");
        // 点击

        // 点击其他地方消失
        // $(document).click(function (e) {
        //     if ($(e.target).closest("#optMenu").length == 0 && $(e.target).closest(".record-btn").length == 0) {
        //         $("#optMenu").hide();
        //     }
        // })
    })
    var dataList = [];
    var kjList = [];
    getList();
    function getList(){
    	$.ajax({
        	type: "POST",
        	url: "/elective/rollcall/getRollcallLog",
        	data:{teachingClassCode:teachingClassCode},
        	dataType:'json',
        	async:false,
        	success: function (data) {
        		var html = "";
        		html += "<div id=\"optMenu\">";
            	html += "<ul>";
                html += "<li onclick=\"toEdit();\">编辑</li>";
                html += "<li onclick=\"exportLog();\">导出</li>";
            	html += "</ul>";
        		html += "</div>";
            	if(data.status){
            		for(var i = 0;i<data.list.length;i++){
                        dataList.push(data.list[i].classDate);
            			kjList.push(data.list[i].kj);
            			html += "<div class=\"record-con\"  kj = '"+data.list[i].kj+"' classDate = '"+data.list[i].classDate+"'>";
            			html += "<div class=\"record-btn\"  uuzc=\""+data.list[i].zc+"\" uuxq=\""+data.list[i].xq+"\" uukj=\""+data.list[i].kj+"\" uudate=\""+data.list[i].classDate+"\"></div>";
            			html += "<h1><span>第"+data.list[i].zc+"周</span><span>周"+data.list[i].xq+"</span><span>第"+data.list[i].kj+"节</span><span>"+data.list[i].classDate+"</span></h1>";
            			html += "<p>"+data.list[i].teachingClassName+"</p>";
            			html += "<ul id=\"ul"+data.list[i].classDate+data.list[i].kj+"\">";
                		html += "<li>正常：<i>0</i></li>";
                        for (let j = 0; j < rollcallStateList.length; j++) {
                            html += "<li>"+rollcallStateList[j].stateNamePc+"：<i>0</i></li>";
                        }
            			html += "</ul>";
        				html += "</div>";
            		}
            	}
            	$("#dataBox").html(html);
       		}
		});
    }
    for(var i = 0;i<dataList.length;i++){
    	getDetail(dataList[i],kjList[i]);
    }
    function getDetail(classDate,kj){
    	$.ajax({
        	type: "POST",
        	url: "/elective/rollcall/getRollcallLogDetail",
        	data:{teachingClassCode:teachingClassCode,classDate:classDate,kj:kj},
        	dataType:'json',
        	success: function (data) {
                for (let i = 0; i < data.data.length; i++) {
                    $("#ul"+classDate+kj).find("li").eq(i).find("i").text(data.data[i]);
                }
       		}
		});
    }
    function toEdit(){
    	var url = "/elective/rollcall/teacherRollcall?teachingClassCode="+teachingClassCode+"&teachingClassName="+teachingClassName+"&courseName="+courseName+"&zc="+thisZC+"&kj="+thisKJ+"&xq="+thisXQ+"&classDate="+thisDate;
    	var webUrl = window.location.origin+url;
        jsBridge.postNotification('CLIENT_OPEN_URL', { "toolbarType": 2, "webUrl": webUrl, "loadType": 1 });
    }

    $(".record-list").on('click', '.record-con', function (e) {
        if(e.target.className=="record-btn"){
            if ($("#optMenu").css("display")=="block") {
                $("#optMenu").hide();
                return false;
            }
            var offTop = $(this).find(".record-btn").offset().top + parseInt($(this).find(".record-btn").height()) + $(".record-list").scrollTop() + 8;
            $("#optMenu").css({ top: offTop }).show();
            thisZC = $(this).find(".record-btn").attr("uuzc");
            thisKJ = $(this).find(".record-btn").attr("uukj");
            thisXQ = $(this).find(".record-btn").attr("uuxq");
            thisDate = $(this).find(".record-btn").attr("uudate");
        }else{
            var webUrl = window.location.origin+"/elective/rollcall/jxbIndex?jxb="+teachingClassCode+"&courseTime="+$(this).attr("classDate")+"&jxbmc="+teachingClassName+"&kj="+$(this).attr("kj");
            jsBridge.postNotification('CLIENT_OPEN_URL', { "toolbarType": 2, "webUrl": webUrl, "loadType": 1 });
            return false;
        }
    })

    function stopBubble(e) {
        if (e && e.stopPropagation)
            e.stopPropagation();
        else {
            window.event.cancelBubble = true;
        }
    }
    function exportLog(){
        jsBridge.postNotification('CLIENT_DISPLAY_MESSAGE', {'message': '正在导出！！', 'gravity': '1'});
        $.ajax({
            type: "POST",
            url: "/elective/rollcall/exportRollcallLogOb",
            dataType:'json',
            data:{teachingClassCode:teachingClassCode,teachingClassName:teachingClassName,courseName:courseName,zc:thisZC
                ,kj:thisKJ,xq:thisXQ,classDate:thisDate,fid:fid},
            async:false,
            success: function (data) {
                if (data.status){
                    var objectId = data.data.objectid;
                    var content = {"downPath":"http://d0.ananas.chaoxing.com/download/"+objectId+"","suffix":"xlsx","puid":""+data.data.uid+"","isfile":true,"name": data.data.fileName,"id":objectId,"size":data.data.fileSize};
                    jsBridge.postNotification('CLIENT_OPEN_RES',  {"cataid":100000019,"key":objectId,"content": content,"cataName":"云盘文件"});
                }else {
                    jsBridge.postNotification('CLIENT_DISPLAY_MESSAGE', {'message': data.msg, 'gravity': '1'});
                }
            }
        });
    }
</script>
</html>