<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">

<head>
    <th:block th:include="common :: header('转入补考科目')"/>
    <th:block th:include="common :: jquery-mCustomScrollbar-css"/>

    <link rel="stylesheet" type="text/css" th:href="@{~/css/examination/global.css}" href=""/>
    <link rel="stylesheet" type="text/css" th:href="@{~/css/examination/chosen.css}"/>
    <link rel="stylesheet" type="text/css" th:href="@{~/plugin/layui/css/layui.css}"/>
    <link rel="stylesheet" type="text/css" th:href="@{~/css/examination/style.css}"/>
    <style>
        .inputs input {
            margin: 0;
            outline: 0;
            -webkit-appearance: none;
            border-radius: 0;
            line-height: 30px;
            padding-left: 10px;
            border: none;
            background: transparent;
            height: 29px;
            float: left;
            color: rgb(51, 51, 51);
            font-size: 14px;
        }

        .inputs input::placeholder {
            color: #8F97A8;
            font-size: 14px;
        }

    </style>
</head>

<body class="bodyColor">

<div class="zy_box">
    <div class="zy_main">
        <div class="zy_title">
            <h2 class="zy_title_h2 fl">添加课程</h2>
        </div>
        <div class="xaScreen clearAfter">
            <div class="xaScreen_row fl">
                批次所属学年学期：
                <input type="hidden" class="thisBatchYear"
                       th:each="info : ${academicYearSemesterFormList}"
                       th:attr="value=${info.xnxq_xnxqh}"

                       th:if="${info.xnxq_sfdqxq eq '是'}">

                <select name="yearSemester" id="batchYearSemester" data-placeholder="请选择学年学期" style="width:200px;"
                        class="dept_select" onchange="switchYearSemester();">
                    <option value="">请选择学年学期</option>
                    <option th:each="info : ${academicYearSemesterFormList}" th:attr="value=${info.xnxq_xnxqh}"
                            th:text="${info.xnxq_xnxqh}"></option>
                </select>
            </div>
            <div class="xaScreen_row fl">
                考试批次：
                <select name="testBatch" id="batchName" data-placeholder="请选择考试批次" style="width:200px;"
                        class="dept_select" onchange="switchBatch();">
                    <option value="" selected="selected">请选择考试批次</option>
                </select>
            </div>
        </div>
        <div class="xaScreen clearAfter">
            <div class="xaScreen_row fl">
                开课学期：
                <select name="courseYearSemester" id="courseYearSemester" data-placeholder="请选择学年学期"
                        style="width:200px;" class="dept_select">
                    <option value="" selected="selected">请选择学年学期</option>
                    <option th:each="info : ${academicYearSemesterFormList}" th:attr="value=${info.xnxq_xnxqh}"
                            th:text="${info.xnxq_xnxqh}"></option>
                </select>
            </div>
            <div class="xaScreen_row fl">
                年级：
                <select name="grade" id="gradeName" data-placeholder="请选择年级" style="width:200px;" class="dept_select">
                    <option value="" selected="selected">请选择年级</option>
                </select>

            </div>
            <div class="xaScreen_row fl">
                院系名称：
                <select name="dept" id="dept" data-placeholder="请选择系部" style="width:200px;" class="dept_select">
                    <option value="" selected="selected">请选择系部</option>
                </select>

            </div>
            <div class="xaScreen_row fl">
                课程名称：
                <select name="course" id="courseName" data-placeholder="请选择课程" style="width:200px;" class="dept_select">
                    <option value="" selected="selected">请选择课程</option>
                </select>

            </div>
            <div class="xaScreen_row fl">
                教学班名称：
                <select name="courseClass" id="courseClassName" data-placeholder="请选择教学班名称" style="width:200px;" class="dept_select">
                    <option value="" selected="selected">请选择教学班名称</option>
                    <option th:each="info : ${courseClassList}" th:attr="value=${info}"
                            th:text="${info}" th:if="${info != ''}" ></option>
                </select>

            </div>
            <div class="xaScreen_row fl">
                班级名称：
                <select name="class" id="className" data-placeholder="请选择班级名称" style="width:200px;" class="dept_select">
                    <option value="" selected="selected">请选择班级名称</option>
                    <option th:each="info : ${classList}" th:attr="value=${info}"
                            th:text="${info}" th:if="${info != ''}" ></option>
                </select>
            </div>


            <!-- <div class="xaScreen_row fl">

            </div>-->
            <div class="xaScreen_row fl"><a class="xaScreen_bnt" href="javascript:beforeInitTab();">查询</a></div>
            <div class="xaScreen_row fl"><a class="xaScreen_bnt" href="javascript:reSetParam();">重置</a></div>
        </div>
        <div class="xaScreen clearAfter">
            <div class="xaScreen_row fl"><a class="xaScreen_add" href="javascript:syncAll();">一键添加</a></div>
        </div>
        <div class="xaTable_box">
            <div class="xaTable_container">
                <table class="layui-table" lay-filter="courseTable" id="courseTable"></table>
            </div>
        </div>
    </div>
</div>

<script th:src="@{~/js/jquery-3.6.0.min.js}" type="text/javascript" charset="utf-8"></script>

<script th:src="@{~/js/jquery.nicescroll.min.js}" type="text/javascript" charset="utf-8"></script>
<script th:src="@{~/js/examination/fyPublic.js}" type="text/javascript" charset="utf-8"></script>
<script th:src="@{~/js/examination/chosen.jquery.js}" type="text/javascript" charset="utf-8"></script>
<script th:src="@{~/js/base.js}" type="text/javascript" charset="utf-8"></script>
<script type="text/html" id="toolBar">
    <a class="colorBlue" href="javascript:void(0);" lay-event="open">添加</a>
    <a class="colorGreen" href="javascript:void(0);" style="display: none;" lay-event="none">已转入</a>
</script>
<script>
    $('.dept_select').chosen({
        no_results_text: "没有结果匹配",
        hide_results_on_select: false,
        max_shown_results: '2',
        //显示搜索框之前所需的最少选项数
        disable_search_threshold: 10,
        allow_single_deselect: true,
    });
</script>
<script th:src="@{~/plugin/layui/layui.js}" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript">
    const a = '[[${collageList}]]';
    const collageList = '[[${collageList}]]'.replaceAll("[", "").replaceAll("]", "").split(",");
    const fid = '[[${fid}]]';
    const uid = '[[${uid}]]';
    let subFlag = false;
    let batchNumber = "";
    let batchYearSemester = "";
    let table = "", insTb = "";
    $(function (){
        $('#batchYearSemester').val($(".thisBatchYear").val());
        $('#batchYearSemester').trigger("chosen:updated");

        switchBatch();
        getData4Select("1,2,3,4", false);
    })
    function reSetParam() {
        $('#batchName').val("");
        $('#batchName').trigger("chosen:updated");
        $('#courseYearSemester').val("");
        $('#courseYearSemester').trigger("chosen:updated");
        $('#gradeName').val("");
        $('#gradeName').trigger("chosen:updated");
        $('#dept').val("");
        $('#dept').trigger("chosen:updated");
        $('#courseName').val("");
        $('#courseName').trigger("chosen:updated");
        $('#className').val("");
        $('#className').trigger("chosen:updated");
        $('#courseClassName').val("");
        $('#courseClassName').trigger("chosen:updated");
        switchBatch();
    }
    function beforeInitTab() {
        batchNumber = $("#batchName").val();
        batchYearSemester = $("#batchYearSemester").val();
        if (batchNumber === '') {
            U.fail("请先选择考试批次")
            return false;
        }
        initTab();
    }

    function initTab() {
        layui.use('table', function () {
            table = layui.table;
            let data = {};
            data["batchNumber"] = batchNumber;
            data["batchYearSemester"] = batchYearSemester;
            data["courseYearSemester"] = $("#courseYearSemester").val();
            data["gradeName"] = $("#gradeName").val();
            data["courseName"] = $("#courseName").val();
            data["courseName"] = $("#courseName").val();
            data["dept"] = $("#dept").val();
            data["courseClassName"] = $("#courseClassName").val();
            data["className"] = $("#className").val();
            let url = "/exam/make_up/getNoTaskCourseByBatch";
            insTb = table.render({
                elem: '#courseTable',
                method: "POST",
                where: data,
                url: url,
                page: true,
                limits: [10, 20, 100],
                cellMinWidth: 100,
                cols: [
                    [
                        {type: 'checkbox', field: 'rowInfo.formUserId', fixed: 'left'},
                        {field: 'kkxxb_kcmc', title: '课程名称'},
                        {field: 'kkxxb_njnj', title: '年级'},
                        {field: 'kkxxb_kkyxyx', title: '院系'},
                        {field: 'kkxxb_zymc', title: '专业'},
                        {field: 'kkxxb_jxbzc', title: '班级名称'},
                        {field: 'kkxxb_jxbmc', title: '教学班名称'},
                        {
                            title: '授课教师', templet: function (data) {
                                let jsxm = "";
                                for (let i = 0; i < data.kkxxb_skjsxm.length; i++) {
                                    if (i == data.kkxxb_skjsxm.length - 1) {
                                        jsxm += data.kkxxb_skjsxm[i].uname;
                                    } else {
                                        jsxm += data.kkxxb_skjsxm[i].uname + ",";
                                    }
                                }

                                return jsxm;
                            }
                        },
                        {
                            title: '教师uid', templet: function (data) {
                                let jsxm = "";
                                for (let i = 0; i < data.kkxxb_skjsxm.length; i++) {
                                    if (i == data.kkxxb_skjsxm.length - 1) {
                                        jsxm += data.kkxxb_skjsxm[i].puid;
                                    } else {
                                        jsxm += data.kkxxb_skjsxm[i].puid + ",";
                                    }
                                }

                                return jsxm;
                            }
                        },
                        {field: 'kkxxb_kcbh', title: '课程编号'},
                        {field: 'kkxxb_jxbbh', title: '教学班编号'},
                        {field: 'kkxxb_byzd1', title: '补考人数'},
                        {title: '操作', toolbar: '#toolBar', field: 'tool', width: 80, fixed: 'right'}
                    ]
                ],
                done: function (res, curr, count) {
                    $(".opt_data_num #totalSpan").text("共 " + count + " 条");
                }
            });

            table.on('tool(courseTable)', function (obj) {
                if (obj.event !== 'open') {
                    return;
                }
                if (subFlag) {
                    return;
                }
                syncByBase(obj.data.kkxxb_jxbbh);

            })
        });
    }


    function syncAll() {
        var checkStatus = table.checkStatus('courseTable');
        if (subFlag) {
            return;
        }
        if (checkStatus.data.length === 0) {
            U.fail("请先勾选行政班")
            return;
        }
        let jxbbh = "";
        for (let i in checkStatus.data) {
            if (jxbbh.length > 0) {
                jxbbh += ";";
            }
            jxbbh += checkStatus.data[i].kkxxb_jxbbh;
        }
        syncByBase(jxbbh);
    }

    function switchYearSemester() {
        getData4Select("1", true);
    }

    function switchBatch() {
        batchNumber = $("#batchName").val();
        batchYearSemester = $("#batchYearSemester").val();
        initTab();
    }

    function initBatchSel(list, needReload) {
        $("#batchName").html('<option value="" selected="selected">请选择考试批次</option>');
        for (let i in list) {
            $("#batchName").append('<option value="' + list[i].kspcgl_kspcbh + '" >' + list[i].kspcgl_kspcmc + '</option>');
        }
        $('#batchName').trigger("chosen:updated");
        if (needReload) {
            switchBatch();
        }
    }

    function getData4Select(types, needReload) {
        batchYearSemester = $("#batchYearSemester").val();

        $.ajax({
            url: "/exam/make_up/getData4Select",
            data: {yearSemester: batchYearSemester, type: types},
            dataType: 'json',
            type: 'post',
            success: function (res) {
                if (res.code!=200){
                    console.log("数据加载失败");
                }
                let data = res.data;
                if (types.indexOf("1") > -1) {
                    initBatchSel(data.batch.list, needReload);
                }
                if (types.indexOf("2") > -1) {
                    let list = data.grade.list;
                    $("#gradeName").html('<option value="" selected="selected">请选择年级</option>');
                    for (let i in list) {
                        let val = list[i].nj_njmc;
                        $("#gradeName").append('<option value="' + val + '" >' + val + '</option>');
                    }
                    $('#gradeName').trigger("chosen:updated");
                }
                if (types.indexOf("3") > -1) {
                    let list = data.course.list;
                    $("#courseName").html('<option value="" selected="selected">请选择课程</option>');
                    for (let i in list) {
                        let val = list[i];
                        $("#courseName").append('<option value="' + val + '" >' + val + '</option>');
                    }
                    $('#courseName').trigger("chosen:updated");
                }
                if (types.indexOf("4") > -1) {
                    let list = data.college.list;
                    if (getCookie("fid") == 250119 || getCookie("fid") == 299360){
                        if (a != '' && collageList !== undefined && collageList !== ''){
                            for (let i in collageList) {
                                if (i == 0){
                                    $("#dept").html('<option value="'+collageList[i]+'" selected="selected">'+collageList[i]+'</option>');
                                }else {
                                    $("#dept").append('<option value="'+collageList[i]+'" >'+collageList[i]+'</option>');
                                }
                            }
                        }else {
                            $("#dept").html('<option value="" selected="selected">请选择系部</option>');
                            for (let i in list) {
                                let val = list[i];
                                $("#dept").append('<option value="' + val + '" >' + val + '</option>');
                            }
                        }
                    }else {
                        $("#dept").html('<option value="" selected="selected">请选择系部</option>');
                        for (let i in list) {
                            let val = list[i];
                            $("#dept").append('<option value="' + val + '" >' + val + '</option>');
                        }
                    }
                    $('#dept').trigger("chosen:updated");
                }
            }
        });

    }

    function syncByBase(jxbbh) {
        U.success("正在添加，请稍等",2000);
        subFlag = true;
        let bhArray = jxbbh.split(";");
        U.ajax({
            url: "/exam/make_up/syncByBase",
            data: {jxbbhs: jxbbh, batchNumber: batchNumber, yearSemester: batchYearSemester,fid :fid, uid: uid},
            dataType: 'json',
            type: 'post',
            success: function (data) {
                if (data.status) {
                    U.success("添加排考任务成功");
                    $(".layui-table-main tbody tr").each(function () {
                        let thisBh = $(this).find("td[data-field='kkxxb_jxbbh'] .layui-table-cell")[0].innerText
                        if (bhArray.indexOf(thisBh) > -1) {
                            $(this).find("td[data-field='tool'] .layui-table-cell .colorBlue").hide();
                            $(this).find("td[data-field='tool'] .layui-table-cell .colorGreen").show();
                        }
                    })
                } else {
                    U.fail( data.msg,3000);
                }
                subFlag = false;
            },
            error: function () {
                U.fail("访问异常！")
                subFlag = false;
            }
        });
    }
</script>


</body>

</html>