<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width,initial-scale=1.0, maximum-scale=1.0, user-scalable=0, viewport-fit=cover">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta content="telephone=no" name="format-detection"/>
    <title>校历</title>
    <script th:src="@{/js/responsive-1.0.js}"></script>
    <link rel="stylesheet" th:href="@{/css/global.css}">
    <link rel="stylesheet" th:href="@{/css/calendar/header.css}">
    <link rel="stylesheet" th:href="@{/css/basic/iosSelect.css}">
    <link rel="stylesheet" th:href="@{/css/calendar/index.css}">
</head>

<body>
<div class="header">
    <div class="head-signal"></div>
    <div class="head-con">
        <a href="javascript:void(0)" class="head-return">
            <img th:src="@{/images/calendar/back-icon.png}" alt="">
            <span>2023年5月</span>
        </a>
        <span class="head-title">校历</span>
        <span class="head-right">
                <img th:src="@{/images/calendar/add-icons.png}" alt="">
            </span>
    </div>
</div>
<div class="main">
    <div class="change-box">
        <div class="current-semester">
            <div class="name">当前学期：</div>
            <div class="time">2022-2023-2</div>
        </div>
        <div class="types">
            <span class="cur">月份</span>
            <span>学期</span>
        </div>

    </div>
    <div class="calendar-box">
        <div class="cal-top">
            <span>周序</span>
            <span>一</span>
            <span>二</span>
            <span>三</span>
            <span>四</span>
            <span>五</span>
            <span>六</span>
            <span>日</span>
        </div>
        <div class="cla-con">
            <div class="lable">
                <div class="row">
                    <div class="week">01</div>
                    <div class="unit disable">
                        <span>31</span>
                    </div>
                    <div class="unit m-start" lay-evnt="0">
                        <span>五月</span>
                        <p>劳动节</p>
                    </div>
                    <div class="unit current" lay-evnt="1">
                        <span>2</span>
                    </div>
                    <div class="unit events" lay-evnt="2">
                        <span>3</span>
                    </div>
                    <div class="unit">
                        <span>4</span>
                    </div>
                    <div class="unit">
                        <span>5</span>
                    </div>
                    <div class="unit">
                        <span>6</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="notes-list">
            <div class="lable">
                <span class="no-data">暂时未添加备注</span>
            </div>
            <div class="lable active">
                <span class="no-data">暂时未添加备注</span>
            </div>
            <div class="lable">
                <h4>备注：</h4>
                <div class="tts">
                    09月01日： 机关、行政人员上班;<br>
                    09月02日至03日： 老生返校;<br>
                    09月16日： 教研活动;<br>
                    09月17日： 大学生英语。
                </div>
            </div>
        </div>
    </div>

</div>

<div class="school-bottom">
    <div class="handle">
        <div class="btn generate">生成校历</div>
        <div class="btn export">导出校历</div>
    </div>

</div>

<div class="marker" style="display: none;"></div>
<div class="markers" style="display: none;"></div>
<div class="editNotes">
    <div class="en-top">
        <span>编辑备注</span>
    </div>
    <div class="en-con">
        <div class="lable">
            <div class="title">学年学期号</div>
            <div class="inputs">
                <span>2022-2023-2</span>
            </div>
        </div>
        <div class="lable">
            <div class="title">年月</div>
            <div class="inputs">
                <span>2023-05</span>
            </div>
        </div>
        <div class="lable">
            <div class="title">备注</div>
            <div class="texatarea">
                <div class="kalamu-area" id="textareaaddress" contenteditable="true" placeholder="请输入"></div>
            </div>
        </div>
    </div>
    <div class="btns">
        <div class="handle">
            <div class="btn generate">取消</div>
            <div class="btn export">确定</div>
        </div>
    </div>
</div>

<div class="bot-window category" id="semester">
    <div class="bot-list">
        <ul>
            <li>2022-2023-1</li>
            <li>2022-2023-2</li>
            <li>2023-2024-1</li>
            <li>2023-2024-2</li>
            <li>2024-2025-1</li>
            <li>2024-2025-2</li>
        </ul>
    </div>
    <div class="bot-close">取消</div>
</div>

<div class="bot-window category" id="weekly">
    <div class="bot-list">
        <ul>
            <li>第一周</li>
            <li>第二周</li>
            <li>第三周</li>
            <li>第四周</li>
            <li>第五周</li>
            <li>第六周</li>
            <li>第七周</li>
            <li>第八周</li>
            <li>第九周</li>
            <li>第十周</li>
            <li>第十一周</li>
        </ul>
    </div>
    <div class="bot-close">取消</div>
</div>

<div class="geschool-calendar">
    <div class="en-top">
        <span>生成校历</span>
    </div>
    <div class="en-con">
        <div class="lable">
            <div class="title">学年学期号</div>
            <div class="inputs" id="session">
                <span>请选择</span>
                <em></em>
            </div>
        </div>
        <div class="lable">
            <div class="title">学期开始时间</div>
            <div class="inputs" data-year="" data-month="" data-date="" id="selectDate">
                <span>请选择</span>
                <em></em>
            </div>
        </div>
        <div class="lable">
            <div class="title">开始周次</div>
            <div class="inputs selectWeeks">
                <span>请选择</span>
                <em></em>
            </div>
        </div>
        <div class="lable">
            <div class="title">结束周次</div>
            <div class="inputs selectWeeks">
                <span>请选择</span>
                <em></em>
            </div>
        </div>
        <div class="lable">
            <div class="title">校历备注</div>
            <div class="texatarea">
                <div class="kalamu-area" id="textareaaddress" contenteditable="true" placeholder="请输入"></div>
            </div>
        </div>
    </div>
    <div class="btns">
        <div class="handle">
            <div class="btn generate">取消</div>
            <div class="btn export">确定</div>
        </div>
    </div>
</div>

<div class="poup save-dialog" style="display: none;">
    <div class="p-con">
        <p>保存成功</p>
    </div>
    <div class="p-botm">
        <div class="confirm">确定</div>
    </div>
</div>
<script th:src="@{/js/jquery-3.3.1.min.js}"></script>
<script th:src="@{/js/CXJSBridge.js}"></script>
<script th:src="@{/js/iosSelect.js}"></script>
<script th:src="@{/js/mobileVersion.js}"></script>
<script th:src="@{/js/calendar/school_calendar_month.js}"></script>
</body>

</html>