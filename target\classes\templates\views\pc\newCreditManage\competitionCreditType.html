<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
    <head>
        <meta charset="UTF-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>竞赛学分</title>
        <link rel="stylesheet" th:href="@{~/css/creditManage/global.css}">
        <link rel="stylesheet" th:href="@{~/plugin/layui/css/layui.css}">
        <link rel="stylesheet" th:href="@{~/css/creditManage/common.css}">
        <link rel="stylesheet" th:href="@{~/css/creditManage/reset.css}">
        <link rel="stylesheet" th:href="@{~/css/creditManage/index.css}">
        <link rel="stylesheet" th:href="@{~/css/creditManage/certificateScore.css}">
        <script th:src="@{~/plugin/layui/layui.js}"></script>
    </head>
    <body>
        <div class="main">
            <div class="top">
                <div class="title">
                    <div class="back">返回</div>
                    <div class="levelone">学分参数设置</div>
                    <div class="icon"></div>
                    <div class="leveltwo">竞赛学分类型</div>
                </div>
                <div class="btn" id="saveBth">保存设置</div>
            </div>
            <div class="con">
                <div class="c-item">
                    <h3>学分数据来源设置</h3>
                    <input type="hidden" id="creditDataSourceSetId">
                    <div class="lab">
                        <div class="name" style="width:auto;">成绩数据类型</div>
                        <div id="grade_data_type" class="j-search-con single-box">
                            <input type="text" placeholder="请选择" readonly class="schoolSel" th:value="等级"
                                   id="gradeDataType">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <ul>
                                    <li class="active">等级</li>
                                    <!--                                    <li>等级2</li>-->
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="lab">
                        <div class="name" style="width:154px;">是否启用绩点</div>
                        <div class="switc-con">
                            <div class="switch"><span></span></div>
                            <div class="switch-con" id="isEnableGradePoint">否</div>
                        </div>
                    </div>
                </div>
                <div class="c-item" id="cTime">
                    <div class="c-top">
                        <h3>学分换算规则设置</h3>
                        <div class="btns-list">
                            <span class="set">等级管理</span>
                            <span class="add">新增换算规则</span>
                        </div>
                    </div>
                    <div class="c-table">
                        <table class="layui-hide" id="competitionTable" lay-filter="competitionTable"></table>
                    </div>
                </div>
                <!--                <div class="save-btn" id="saveBth">保存设置</div>-->
            </div>
        </div>
        <div class="dialog" id="dialogRule" style="display: none;">
            <div class="dialog-rule">
                <div class="dialog-title">
                    换成规则管理
                </div>
                <div class="dialog-con">
                    <form action="">
                        <div class="form-item form-item-type">
                            <h3 class="item-title">类目类型</h3>
                            <input type="text" value="技能证书" disabled>
                        </div>
                        <div class="form-item form-item-score">
                            <h3 class="item-title"><span>*</span>学分得分</h3>
                        </div>
                        <table class="tabEdit">
                            <thead>
                            <tr>
                                <td width="267">等级名称</td>
                                <td width="267">学分</td>
                                <td width="267">操作</td>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td>高级</td>
                                <td><span>6</span>
                                    <input type="text" value="6" data-old="6"></td>
                                <td>
                                    <span class="edit">编辑</span>
                                    <div class="opt">
                                        <span class="save">保存</span>
                                        <span class="cancel">取消</span>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>中级</td>
                                <td><span>6</span>
                                    <input type="text" value="6" data-old="6"></td>
                                <td>
                                    <span class="edit">编辑</span>
                                    <div class="opt">
                                        <span class="save">保存</span>
                                        <span class="cancel">取消</span>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>初级</td>
                                <td>
                                    <span>6</span>
                                    <input type="text" value="6" data-old="6">
                                </td>
                                <td>
                                    <span class="edit">编辑</span>
                                    <div class="opt">
                                        <span class="save">保存</span>
                                        <span class="cancel">取消</span>
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </form>
                </div>
                <div class="dialog-btn">
                    <button class="btn-cancel">取消</button>
                    <button class="btn-sure">确定</button>
                </div>
            </div>
        </div>
    </body>
    <script type="text/html" id="barTable">
        <span lay-event="edit" style="color: #4C88FF;cursor: pointer;">编辑</span>
    </script>
    <script th:src="@{~/js/jquery-3.3.1.min.js}"></script>
    <script th:src="@{~/js/creditManage/common.js}"></script>
    <script th:inline="javascript">
        layui.use(['jquery', 'table'], function () {

            // 从页面中取得csId和学年学期参数值
            var csId = [[${csId}]];
            var term = [[${term}]];

            var table = layui.table,
                $ = layui.jquery;
            var tabData = [{
                typeCode: "hih4t97",
                type: "技能证书",
                level: "一等奖",
                score: "2",
            }, {
                typeCode: "hih4t97",
                type: "技能证书",
                level: "二等奖",
                score: "4",
            }, {
                typeCode: "hih4t97",
                type: "技能证书",
                level: "三等奖",
                score: "6",
            }, {
                typeCode: "hih4t98",
                type: "英语考级证书",
                level: "一等奖",
                score: "2",
            }, {
                typeCode: "hih4t98",
                type: "英语考级证书",
                level: "二等奖",
                score: "4",
            }, {
                typeCode: "hih4t98",
                type: "英语考级证书",
                level: "三等奖",
                score: "6",
            }];
            tabData = [];

            // 点击编辑
            $(".tabEdit tbody").on('click', '.edit', function () {
                $(this).hide();
                $(this).next().show();
                var prev = $(this).parent().prev();
                var val = prev.find("input").val();
                prev.find("input").val("").show().focus().val(val);
                prev.find('span').hide();
            });
            // 保存
            $(".tabEdit tbody").on('click', '.opt .save', function () {
                $(this).parent().hide();
                $(this).parent().prev().show();
                var prev = $(this).parents('td').prev();
                var val = prev.find("input").val();
                prev.find('input').hide();
                prev.find('input').attr('data-old', val)
                prev.find('span').text(val).show();
            });
            // 取消
            $(".tabEdit tbody").on('click', '.opt .cancel', function () {
                $(this).parent().hide();
                $(this).parent().prev().show();
                var prev = $(this).parents('td').prev();
                var val = prev.find("input").attr('data-old');
                prev.find('input').hide();
                prev.find('input').val(val);
                prev.find('span').text(val).show();
            });
            // 确定
            $("#dialogRule .btn-sure").click(function () {
                $("#dialogRule").hide();
            });
            // 取消
            $("#dialogRule .btn-cancel").click(function () {
                $("#dialogRule").hide();
            });

            // 点击学分数据来源类型下拉框
            $('.single-box .j-select-year').on('click', "ul li", function () {
                let gradeDataType = $("#grade_data_type").find(".schoolSel").val();
                // 学分数据来源类型默认选择等级，选择【等级】才出现【等级管理按钮】，主要后续可能存在成绩类型为分数的情况
                if ("等级" == gradeDataType) {
                    $("#cTime").find(".c-top .btns-list .set").show();
                } else {
                    $("#cTime").find(".c-top .btns-list .set").hide();
                }
            });

            // 点击【等级管理】跳转至竞赛等级管理页面
            $("#cTime").find(".c-top .btns-list .set").on("click", function () {
                window.location.href = "/new/credit/competition/grade/manager/index";
            });

            // 点击【新增换算规则】跳转至竞赛规则管理页面
            $("#cTime").find(".c-top .btns-list .add").on("click", function () {
                window.location.href = "/new/credit/competition/conversion/rule/index";
            });

            // 是否启用绩点的开关
            $(".switc-con .switch").click(function () {
                if ($(this).hasClass("switch-open")) {
                    $(this).removeClass("switch-open");
                    $(this).next().text('否');
                } else {
                    $(this).addClass("switch-open");
                    $(this).next().text('是');
                }
            });

            // 点击保存按钮
            $("#saveBth").click(function () {
                let creditDataSourceSetId = $("#creditDataSourceSetId").val();
                let gradeDataType = $("#gradeDataType").val();
                if (!gradeDataType) {
                    layer.confirm('请选择学分数据来源设置-成绩数据类型', {
                        btn: ['取消', '确定'] //按钮
                    });
                    return;
                }
                if (!tabData.length) {
                    layer.confirm('学分换算规则未设置，请设置后保存', {
                        btn: ['取消', '确定'] //按钮
                    });
                    return;
                }
                $.ajax({
                    type: 'post',
                    dataType: 'json',
                    url: '/new/credit/competition/saveCreditDataSourceSet',
                    data: {
                        "id": creditDataSourceSetId,
                        "creditSetId": csId,
                        "term": term,
                        "gradeDataType": gradeDataType,
                        "isEnableGradePoint": $("#isEnableGradePoint").text() === "是" ? 0 : 1
                    },
                    success: function (res) {
                        if (res.code === 200) {
                            layer.msg("保存成功", {icon: 1, time: 2000});
                            // 加载学分数据来源设置
                            loadCreditDataSourceSetData();
                            // 加载学分换算规则设置
                            loadCreditConversionRuleData();
                        } else {
                            layer.msg(res.msg, {icon: 2, time: 2000});
                        }
                    }
                });
            });

            // 点击返回按钮，后退
            $(".back").click(function () {
                history.back();
            });

            // 给是否启用绩点开关绑定点击事件
            $(".switch").click(function () {
                tableRender();
            });

            // 初始化页面
            $(function () {
                // 加载学分数据来源设置
                loadCreditDataSourceSetData();
                // 加载学分换算规则设置
                loadCreditConversionRuleData();
            });

            /**
             * 加载学分数据来源设置
             */
            function loadCreditDataSourceSetData() {
                // 获取学分数据来源设置
                $.ajax({
                    type: 'get',
                    dataType: 'json',
                    url: '/new/credit/competition/getCreditDataSourceSet',
                    success: function (res) {
                        if (res.code == 200) {
                            let data = res.data;
                            if (data) {
                                $("#creditDataSourceSetId").val(data.id);
                                if (data.gradeDataType) {
                                    $("#gradeDataType").val(data.gradeDataType);
                                    // 学分数据来源类型默认选择等级，选择【等级】才出现【等级管理按钮】，主要后续可能存在成绩类型为分数的情况
                                    if ("等级" === data.gradeDataType) {
                                        $("#cTime").find(".c-top .btns-list .set").show();
                                    } else {
                                        $("#cTime").find(".c-top .btns-list .set").hide();
                                    }
                                }
                                let isSetUnifyStandardCreditEle = $(".switc-con .switch");
                                // 如果开启了绩点，即isEnableGradePoint === 0，则渲染开关和文本值；其他情况为否，没有开启
                                if (data.isEnableGradePoint === 0) {
                                    $(isSetUnifyStandardCreditEle).next().text('是');
                                    if (!$(isSetUnifyStandardCreditEle).hasClass("switch-open")) {
                                        $(isSetUnifyStandardCreditEle).addClass("switch-open");
                                    }
                                } else {
                                    $(isSetUnifyStandardCreditEle).next().text('否');
                                    if ($(isSetUnifyStandardCreditEle).hasClass("switch-open")) {
                                        $(isSetUnifyStandardCreditEle).removeClass("switch-open");
                                    }
                                }
                            }
                        } else {
                            layer.msg(res.msg, {icon: 2, time: 2000});
                        }
                    }
                });
            }

            /**
             * 加载学分换算规则设置
             */
            function loadCreditConversionRuleData() {
                // 异步请求，获取竞赛规则管理表单数据，然后渲染到页面上
                $.ajax({
                    type: 'get',
                    dataType: 'json',
                    url: '/new/credit/competition/getCreditCalRuleSetData',
                    success: function (res) {
                        if (res.code == 200) {
                            tabData = res.data;
                            tableRender();
                            // table.reload("competitionTable", {data: tabData, cols: getTableColdObject()});
                        } else {
                            layer.msg(res.msg, {icon: 2, time: 2000});
                        }
                    }
                });
            }

            /**
             *  渲染表格
             */
            function tableRender() {
                // table
                table.render({
                    elem: '#competitionTable',
                    url: '',
                    data: tabData,
                    cols: getTableColdObject(),
                    page: false,
                    limit: Number.MAX_VALUE,
                    done: function (res, curr, count) {
                        /* 需要合并栏目的数组信息 */
                        var merge_column_infos = [{
                            field: "typeCode",
                            index: 0
                        }, {
                            field: "type",
                            index: 1
                        }];
                        /* 从第几行开始遍历合并 去除表头就是 1 咯 */
                        var start_tr_index = 1;
                        /* 查找到需要合并表格所有的 tr */
                        var tr_s = $("#cTime table").find("tr");
                        /* 开始遍历需要合并的栏目 */
                        for (var merge_item of merge_column_infos) {
                            var field = merge_item.field;
                            /* 需要合并栏目的索引 */
                            var index = merge_item.index;
                            /* 需要合并栏目的数量 */
                            var merge_num = 0;
                            /* 需要合并栏目的 td */
                            var merge_tds = [],
                                merge_tds1 = [];
                            /* 开始遍历需要合并表格的所有 tr */
                            for (var i = start_tr_index; i < tr_s.length; i++) {
                                /* 当前 td */
                                var cur_td = tr_s.eq(i).find("td").eq(index); /* 下一个 td */
                                if (index == 2) {
                                    var cur_td1 = tr_s.eq(i).find("td").eq(6);
                                }
                                /* 下一个 td */
                                var next_td = tr_s.eq(i + 1).find("td").eq(index);
                                /*当前 td 的 text */
                                var cur_text = $(cur_td).text(); /* 下一个 td 的 text 当遍历到最后默认为空 */
                                var next_text = $(next_td).text(); /* 如果当前 td=下一个 td */
                                if (cur_text == next_text) {
                                    /* 放入到合并 td 的集合中 */
                                    merge_tds.push(cur_td); /* 需要合并的 td 数量加 1 */
                                    if (index == 2) {
                                        merge_tds1.push(cur_td1)
                                    }

                                    merge_num++;
                                } else {
                                    /* 如果 如果当前 td !=下一个 td 且要合并的 td 数量不等于 0 */
                                    if (merge_num != 0) {
                                        /* 第一个 td 合并 因为动态添加 rowspan 属性是向下延申 */
                                        $(merge_tds[0]).attr("rowspan", merge_num + 1);
                                        if (index == 2) {
                                            $(merge_tds1[0]).attr("rowspan", merge_num + 1);
                                        }
                                        /* 遍历所有的需要合并的 td 将他们的属性设置为 不可见 */
                                        for (var d = 1; d < merge_tds.length; d++) {
                                            $(merge_tds[d]).addClass("layui-hide");
                                            if (index == 2) {
                                                $(merge_tds1[d]).addClass("layui-hide");
                                            }
                                        }
                                        /* 当前 td 属性也需要设置为不可见 */
                                        $(cur_td).addClass("layui-hide");
                                        if (index == 2) {
                                            $(cur_td1).addClass("layui-hide");
                                        }
                                    }
                                    /*重置合并 td 数据 */
                                    merge_num = 0;
                                    merge_tds = [], merge_tds1 = [];;
                                }
                            }
                        }
                    }
                });
                table.on('tool(competitionTable)', function (obj) {
                    var data = obj.data;
                    if (obj.event === 'edit') {
                        $("#dialogRule").show();
                    }
                });
            }

            /**
             * 获取表格属性列cols的对象
             */
            function getTableColdObject() {
                var isEnableGradePoint = $("#isEnableGradePoint").text() === "是";
                var col = [
                    {
                        field: 'typeCode',
                        title: '类目类型代码',
                        align: "center",
                    }, {
                        field: 'type',
                        title: '类目类型',
                        align: "center",
                    }, {
                        field: 'level',
                        title: '等级名称',
                        align: "center",
                    }, {
                        field: 'score',
                        title: '学分',
                        align: "center",
                    }
                ];
                if (isEnableGradePoint) {
                    col.push({
                        field: 'gradePoint',
                        title: '绩点',
                        align: "center",
                    });
                }
                return [col];
            }
        });
    </script>

</html>