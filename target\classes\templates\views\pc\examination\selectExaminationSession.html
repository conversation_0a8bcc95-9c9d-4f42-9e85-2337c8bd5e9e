<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>手动安排考试弹窗</title>
    <link rel="stylesheet" th:href="@{~/css/examination/global2.css}">
    <link rel="stylesheet" th:href="@{~/css/examination/dialog.css}">
    <link rel="stylesheet" type="text/css" th:href="@{~/plugin/layui-v2.8.18/layui/css/layui.css}"/>
    <script th:src="@{~/plugin/layui-v2.8.18/layui/layui.js}" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" th:href="@{~/css/examination/poup.css}">
    <style>
        #batch {
            /*margin-left: 30px;*/
            /*font-size: 15px;*/
            color: #8f8f8f;
            width: 21em; /*保证文字不会被半汉字截断,显示10个文字*/
            overflow: hidden; /*超出长度的文字隐藏*/
            text-overflow: ellipsis; /*文字隐藏以后添加省略号*/
            white-space: nowrap; /*强制不换行*/
            /*border:1px solid red;*/
        }

        .layui-table-header table {
            background-color: #F8F8F8;
        }

        .layui-table-body {
            height: 390px !important;;
        }

        .layui-table-pageview {
            position: absolute;
            left: 52%;
        }

        #exportRecord {
            width: 1000px;
            position: fixed;
            left: 0;
            top: 0;
            transform: translate(0, 0);
        }

        #invigilateMax{
            width: 1000px;
            position: fixed;
            left: 0;
            top: 0;
            transform: translate(0, 0);
        }

        #invigilateMax .dialog-con{
            height:459px;
        }

    </style>
</head>

<body>

<div class="dialog" id="invigilateMax" style="display: none">
    <div class="dialog-title" style="display: flex">选择考试场次
        <div id="batch"></div>

    </div>

    <div class="dialog-con">
        <div class="item">
            <div class="label">选择场次</div>
            <div class="j-search-con single-box">
                <input type="text" name="teacherName" placeholder="请选择" readonly="" class="schoolSel" id="session">
                <span class="j-arrow"></span>
                <div class="j-select-year ">
                    <ul id="sessions">

                    </ul>
                </div>
            </div>
        </div>
        <input type="hidden" id="ccbh">
        <div class="item">
            <div class="label">开始时间</div>
            <div class="j-search-con single-box">
                <input type="text" name="teacherName" readonly="" class="schoolSel" id="kssj"
                       style="background: #ebebeb">
            </div>
        </div>
        <div class="item">
            <div class="label">结束时间</div>
            <div class="j-search-con single-box">
                <input type="text" name="teacherName" readonly="" class="schoolSel" id="jssj"
                       style="background: #ebebeb">
            </div>
        </div>

    </div>
    <div class="dialog-btn">
        <button class="pu-sure" id="export">确定</button>
    </div>
</div>
<div class="dialog" id="exportRecord" style="display: none;width: 1000px">

    <div  class="exam-popup popups" style="display: block; margin: 0 auto; width: 940px;padding-top:15px;">
        <div  style="font-size: 16px; line-height: 40px;">安排场次</div>
        <div  style="font-size: 14px; line-height: 40px; background: url(../../../images/examination/tips-icon.png) no-repeat left center; padding-left:22px;">检测到下列班级学生存在同一场次多门考试的冲突，请确认是否忽略冲突继续排考。</div>
    </div>
    <div class="dialog-con" style="margin:0 30px;" >
<!--        <div style="color: #5f5f5f"> 重复学生名单</div>-->
        <br>
        <div style="height: 470px">
            <table class="layui-hide materialTable" id="materialTable" lay-filter="materialTable"></table>
        </div>
    </div>
    <div  class="exam-popup popups" style="display: block; margin: 0 auto; width: 940px;">
        <div class="bottom" style="border:none;text-align: center !important;justify-content: flex-end; padding: 0;">
            <div onclick="confirm()" class="confirm">是</div>
            &nbsp;&nbsp;&nbsp;
            <div onclick="cancle()" class="cancle" style="margin-right:0;">否</div>
        </div>
    </div>
</div>

</body>
<script th:src="@{~/js/jquery-3.3.1.min.js}"></script>
<script th:src="@{~/js/my.util.js}"></script>
<script>
    var queryId = '[[${queryId}]]'
    const _VR_ = '';
    $(function () {
        layui.use(['jquery', 'table' , 'layer'], function () {
            var table = layui.table;
            $ = layui.jquery;
            layer = layui.layer
            let loading = layer.load(2, {
                shade: [0.5, '#000']
            });
        U.ajax({
            type: 'post',
            loading: false,
            url: '/examination/result/checkSessionStudent',
            data: {
                queryId: queryId
            },
            success: function (r1) {
                r = r1
                layer.close(loading);
                if (r1.code == 500) {
                    var data = {};
                    try {
                        data = JSON.parse(r.msg);
                    } catch (e) {
                        U.fail(r.msg,10000)
                        console.log(e)
                        return false;
                    }

                    $("#invigilateMax").hide();
                    $("#exportRecord").show();
                    //判断返回值是不是json
                    table.render({
                        elem: '#materialTable',
                        data: data,
                        height: '470',
                        even: true,
                        cols: [
                            [
                                {
                                    field: "xm",
                                    title: "姓名",
                                    align: "center",
                                    width: 120,
                                },
                                {
                                    field: "xh",
                                    title: "学号",
                                    align: "center",
                                    width: 130
                                },
                                {
                                    field: "nj",
                                    title: "年级",
                                    align: "center",
                                },
                                {
                                    field: "yx",
                                    title: "院系",
                                    align: "center",
                                },
                                {
                                    field: "zy",
                                    title: "专业",
                                    align: "center",
                                },
                                {
                                    field: "bjmc",
                                    title: "班级名称",
                                    align: "center",
                                },
                                {
                                    field: "bjbh",
                                    title: "班级编号",
                                    align: "center",
                                },
                                {
                                    field: "kcmc",
                                    title: "课程名称",
                                    align: "center",
                                },
                                {
                                    field: "kcbh",
                                    title: "课程编号",
                                    align: "center",
                                },
                            ]
                        ],
                        done: function (res, curr, count) {
                            $("#total").html(count)
                            $('td[data-field=fileName] div').each(function (index, element) {
                                $(element).attr('title', $(element).text());
                            });
                        },
                        page: {
                            layout: ['prev', 'page', 'next', 'count', 'limit', 'skip']
                        }
                    });


                }

                if (r1.code == 200) {
                    $("#invigilateMax").show();
                    var html = '';
                    var data = r1.data.data;
                    $("#batch").html("当前批次：" + data[0].ksccgl_kspc)
                    $("#batch").attr("title", data[0].ksccgl_kspc)
                    $("#batch").attr("data-term", data[0].ksccgl_xnxq);
                    for (let i = 0; i < data.length; i++) {
                        html += '<li data-value="' + data[i].ksccgl_ccbh + '" data-kssj="' + format(data[i].ksccgl_kssj) + '" data-jssj="' + format(data[i].ksccgl_jssj) + '">' + data[i].ksccgl_ccmc + '</li>'
                    }
                    $("#sessions").html(html);
                }
            }
        });
        })
    })


    $(document).ready(function () {
        // 下拉多选
        $(".j-search-con").on("click", ".schoolSel", function (e) {
            $(".j-select").hide();
            let parent = $(this).parent();
            parent.find('.j-arrow').toggleClass('j-arrow-slide');
            parent.find('.j-select-year').toggleClass('slideShow')
            stopBubble(e)
        })

        // 选择-单选
        $(".j-search-con.single-box").on("click",
            ".j-select-year li ",
            function (e) {
                $(this).addClass('active').siblings().removeClass();
                var parents = $(this).parents('.j-search-con');
                var schoolSelEle = parents.find('.schoolSel');
                var txt = $(this).text();
                schoolSelEle.val(txt);
                parents.find('.j-arrow').removeClass('j-arrow-slide');
                parents.find('.j-select-year').removeClass('slideShow')
                stopBubble(e);
            })
        // 搜索
        $(".j-search-con").on('click', '.search input', function (e) {
            stopBubble(e);
        })
        $(".j-search-con").on('input', '.search input', function (e) {
            let val = $(this).val();
            var nextEle = $(this).parents('.j-search-con').find('ul')
            if (val != "") {

                nextEle.find('li').each(function (i, ele) {
                    let txt = $(ele).text();
                    if (txt.indexOf(val) >= 0) {
                        $(ele).show();
                    } else {
                        $(ele).hide();
                    }
                })
            } else {
                nextEle.find('li').show();
            }
            stopBubble(e)
        })

        function stopBubble(e) {
            if (e && e.stopPropagation)
                e.stopPropagation();
            else {
                window.event.cancelBubble = true;
            }

        }

        $(document).on("click", function (event) {
            var _con = $('.select-input');
            if (!_con.is(event.target) && _con.has(event.target).length === 0) {
                $(".j-search-con").find('.j-arrow').removeClass('j-arrow-slide');
                $(".j-search-con").find('.j-select-year').removeClass('slideShow')
            }
        })

    })
</script>
<script th:inline="javascript">
    var fid = [[${fid}]];
    var uid = [[${uid}]];
    var r = ''


    $(".j-select-year").on("click", "ul li", function () {
        var kssj = $(this).attr("data-kssj");
        var jssj = $(this).attr("data-jssj");
        var ccbh = $(this).attr("data-value");
        $("#kssj").val(kssj)
        $("#jssj").val(jssj)
        $("#ccbh").val(ccbh)
    })

    $(".pu-sure").click(function () {
        var data = {
            fid: fid,
            uid: uid,
            term: $("#batch").attr("data-term"),
            formUserId: JSON.stringify(r.data.formUserId).replaceAll("\"",""),
            sessionName: $("#session").val(),
            kssj: new Date($("#kssj").val()),
            jssj: new Date($("#jssj").val()),
            ccbh: $("#ccbh").val(),
            qp: isqp
        }
        $.post("/examination/result/update/session", data, function (res) {
            if (res.code == 200) {
                U.success("保存成功", 2000)
                setTimeout(U.closePop, 2000)

            } else {
                U.fail("保存失败", 2000)
            }
        })
    })

    function format(date) {
        if (date == undefined || date == null || date == '') {
            return '';
        }
        var dateTime = new Date(date);
        var year = dateTime.getFullYear();
        var month = dateTime.getMonth() + 1;
        var day = dateTime.getDate();
        var hours = dateTime.getHours();
        var minutes = dateTime.getMinutes();
        if (month < 10) {
            month = '0' + month;
        }
        if (day < 10) {
            day = '0' + day;
        }
        if (hours < 10) {
            hours = '0' + hours;
        }
        if (minutes < 10) {
            minutes = '0' + minutes;
        }
        return year + "-" + month + "-" + day + " " + hours + ":" + minutes;
    }

    var isqp = 'false'
    function confirm() {
        U.ajax({
            type: 'post',
            loading: false,
            url: '/examination/result/checkSessionStudent',
            data: {
                queryId: queryId,
                qp: 'true'
            },
            success: function (r1) {
                isqp = 'true'
                r = r1
                if (r1.code == 200) {
                    $("#exportRecord").hide();
                    $("#invigilateMax").show();
                    var html = '';
                    var data = r1.data.data;
                    $("#batch").html("当前批次：" + data[0].ksccgl_kspc)
                    $("#batch").attr("title", data[0].ksccgl_kspc)
                    $("#batch").attr("data-term", data[0].ksccgl_xnxq);
                    for (let i = 0; i < data.length; i++) {
                        html += '<li data-value="' + data[i].ksccgl_ccbh + '" data-kssj="' + format(data[i].ksccgl_kssj) + '" data-jssj="' + format(data[i].ksccgl_jssj) + '">' + data[i].ksccgl_ccmc + '</li>'
                    }
                    $("#sessions").html(html);
                }
            }
        });
        
    }
    function cancle() {
        window.parent.postMessage(JSON.stringify({action: 1}), "*");
        // window.parent.postMessage({type: "close"}, '*');
    }
</script>

</html>