<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>班课速配</title>
    <link rel="stylesheet" th:href="@{${_CPR_}+'/css/cultivation/global.css'}">
    <link rel="stylesheet" th:href="@{${_CPR_}+'/plugin/layui/css/layui.css'}">
    <link rel="stylesheet" th:href="@{${_CPR_}+'/css/cultivation/reset.css'}">
    <link rel="stylesheet" th:href="@{${_CPR_}+'/css/cultivation/classCourse.css'}">
    <script th:src="@{${_CPR_}+'/plugin/layui/layui.js'}"></script>
</head>

<body>
<div class="main">
    <div class="m-top">
        <div class="title">开课管理</div>
        <span></span>
        <h3>班课速配</h3>
        <div class="btns"><span>下一步</span></div>
    </div>
    <div class="form1">
        <form class="layui-form" action="">
            <div class="layui-inline">
                <label class="layui-form-label">开课学期</label>
                <div class="layui-input-inline">
                    <select name="xnxq_xnxqh">
                    </select>
                </div>
            </div>
        </form>
    </div>
    <div class="item">
        <div class="i-top">
            <span>筛选班级</span>
        </div>
        <div class="form-con">
            <form class="layui-form layui-form-stu" action="" lay-filter="courseForm">
                <div class="layui-inline">
                    <label class="layui-form-label">年级</label>
                    <div class="layui-input-inline">
                        <div class="j-search-con single-box">
                            <input type="text" name="bjxx_rxnf" placeholder="请选择" readonly="" class="schoolSel"/>
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">所属系部</label>
                    <div class="layui-input-inline">
                        <div class="j-search-con single-box">
                            <input type="text" name="bjxx_ssyx" placeholder="请选择" readonly="" class="schoolSel">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">专业</label>
                    <div class="layui-input-inline">
                        <div class="j-search-con single-box">
                            <input type="text" name="bjxx_zy" placeholder="请选择" readonly="" class="schoolSel"/>
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">班级名称</label>
                    <div class="layui-input-inline">
                        <div class="j-search-con single-box">
                            <input type="text" name="bjxx_bjmc" placeholder="请选择" readonly="" class="schoolSel"/>
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">班级编号</label>
                    <div class="layui-input-inline">
                        <div class="j-search-con single-box">
                            <input type="text" name="bjxx_bjbh" placeholder="请选择" readonly="" class="schoolSel"/>
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">所属教研室</label>
                    <div class="layui-input-inline">
                        <div class="j-search-con single-box">
                            <input type="text" name="bjxx_ssjys" placeholder="请选择" readonly="" class="schoolSel">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline" style="display: none;">
                        <button type="reset" class="layui-btn layui-btn-primary layui-btn-reset">重置</button>
                    </div>
                </div>
            </form>
            <div class="form-btn">
                <button class="btn btn-reset">重置</button>
                <button class="btn btn-search">筛选</button>
            </div>
        </div>
        <div class="course-table">
            <table class="layui-hide" id="courseTable" lay-filter="courseTable"></table>
        </div>
    </div>
</div>
<div class="main1">
    <div class="title">
        <h1><span>“筛选班级” </span>设置结果</h1>
        <div class="clear">清除</div>
    </div>
    <div class="course-table">
        <table class="layui-hide" lay-filter="courseTable1" id="courseTable1"></table>
    </div>
</div>


</body>
<script type="text/html" id="tmplToolBar">
    <div class="oprate-table">
        <div class="delet" lay-event="delet">删除</div>
    </div>
</script>
<script th:src="@{${_CPR_}+'/js/jquery-3.3.1.min.js'}"></script>
<script th:src="@{${_CPR_}+'/js/cultivation/select.js'}"></script>
<script>
    const _VR_ = "[[${_VR_}]]";
    const fid = "[[${clazzSpeedMatchVO.fid}]]";
    const uid = "[[${clazzSpeedMatchVO.uid}]]";
    let tabData = [];
    layui.use(['jquery', "form", "table"], function () {
        const $ = layui.jquery;
        const form = layui.form;
        const table = layui.table;
        // 清空 localStorage
        let clearFlag = new URLSearchParams(window.location.search).get("clear");
        if (!clearFlag) {
            localStorage.clear();
        }

        // 查询
        $(".btn-search").click(function () {
            const bjxx_rxnf = $("input[name='bjxx_rxnf']").val();
            const bjxx_ssyx = $("input[name='bjxx_ssyx']").val();
            const bjxx_zy = $("input[name='bjxx_zy']").val();
            const bjxx_bjmc = $("input[name='bjxx_bjmc']").val();
            const bjxx_bjbh = $("input[name='bjxx_bjbh']").val();
            const bjxx_ssjys = $("input[name='bjxx_ssjys']").val();
            const field = {
                grade: bjxx_rxnf,
                dept: bjxx_ssyx,
                major: bjxx_zy,
                clazzName: bjxx_bjmc,
                clazzNo: bjxx_bjbh,
                teachSection: bjxx_ssjys
            };
            layui.table.reload("courseTable", {where: field, page: {curr: 1}});
        })
        // 重置
        $(".btn-reset").click(function () {
            $(".layui-btn-reset").click();
            const field = {grade: "", dept: "", major: "", clazzName: "", clazzNo: "", teachSection: ""};
            layui.table.reload("courseTable", {where: field, page: {curr: 1}});
        });
        // table
        table.render({
            elem: '#courseTable',
            url: _VR_ + `/pc/cultivation/clazzSpeedMatch/clazzData`,
            where: {fid: fid},
            cols: [
                [{
                    type: 'checkbox',
                    width: 86,
                    fixed: 'left'
                }, {
                    field: 'bjxx_bjmc',
                    title: '班级名称',
                    align: "center",
                    minWidth: 190
                }, {
                    field: 'bjxx_bjbh',
                    title: '班级编号',
                    align: "center",
                    minWidth: 190
                }, {
                    field: 'bjxx_rxnf',
                    title: '入学年份',
                    align: "center",
                    minWidth: 190
                }, {
                    field: 'bjxx_ssyx',
                    title: '所属系部',
                    align: "center",
                    minWidth: 190
                }, {
                    field: 'bjxx_zy',
                    title: '专业',
                    align: "center",
                    minWidth: 190
                }, {
                    field: 'bjxx_njrs',
                    title: '班级人数',
                    align: "center",
                    minWidth: 190
                }, {
                    field: 'bjxx_bzr',
                    title: '班主任',
                    align: "center",
                    minWidth: 190
                }, {
                    field: 'bjxx_bzrgh',
                    title: '班主任工号',
                    align: "center",
                    minWidth: 190,

                }, {
                    field: 'bjxx_ssjys',
                    title: '所属教研室',
                    align: "center",
                    minWidth: 190,

                }]
            ],
            page: {
                layout: ['count', 'prev', 'page', 'next', 'limit', 'skip']
            },
            done: function (res, curr, count) {
                //选中了某一行，使不同管理员的行不可选
                tabData = res.data;
                res.data.forEach((data, i) => {
                    const match = selTable.some(item => item.bjxx_bjbh === data.bjxx_bjbh);
                    if (match) {
                        tabData[i].isSel = true;
                        disabledRow(i);
                    } else {
                        abledRow(i);
                    }
                });
                getFormDistinctFiled();
            }
        });
        // 获取table选择的数据
        const selectData = table.checkStatus('courseTable').data;
        // 获取table选择的数据
        let selTable = window.localStorage.getItem("clazzData") ? JSON.parse(window.localStorage.getItem("clazzData")) : [];
        table.render({
            elem: '#courseTable1',
            data: selTable,
            cols: [
                [{
                    field: 'bjxx_bjmc',
                    title: '班级名称',
                    align: "center",
                    minWidth: 190
                }, {
                    field: 'bjxx_bjbh',
                    title: '班级编号',
                    minWidth: 190,
                    align: "center",
                }, {
                    field: 'bjxx_rxnf',
                    title: '入学年份',
                    minWidth: 190,
                    align: "center",
                }, {
                    field: 'bjxx_ssyx',
                    title: '所属系部',
                    align: "center",
                    minWidth: 190
                }, {
                    field: 'bjxx_zy',
                    title: '专业',
                    align: "center",
                    minWidth: 190
                }, {
                    field: 'bjxx_njrs',
                    title: '班级人数',
                    align: "center",
                    minWidth: 190
                }, {
                    title: '操作',
                    align: "center",
                    minWidth: 190, toolbar: "#tmplToolBar",
                    fixed: "right"

                }]
            ],
            page: {
                layout: ['count', 'prev', 'page', 'next', 'limit', 'skip']
            }
        });
        // 删除
        table.on('tool(courseTable1)', function (obj) {
            const data = obj.data;
            if (obj.event === 'delet') {
                layer.confirm('确定删除吗？', function (index) {
                    obj.del();
                    const idx1 = selTable.findIndex(item => item.id === data.id);
                    selTable.splice(idx1, 1);
                    table.reload('courseTable1', {data: selTable, page: {curr: 1}});
                    layer.close(index);
                    //如果勾选为0，则重置
                    const idx = tabData.findIndex(item => item.id === data.id);
                    tabData[idx].isSel = false;
                    abledRow(idx);
                    window.localStorage.setItem("clazzData", JSON.stringify(selTable));
                });
            }
        });
        // 选择
        //监听表格复选框
        table.on('checkbox(courseTable)', function (obj) {
            let tableArray = layui.table.cache['courseTable'];
            if (obj.type === "all") {
                if (obj.checked && !$(this).hasClass("layui-btn-disabled")) {
                    //选中了某一行，使不同管理员的行不可选
                    for (let i = 0; i < tableArray.length; i++) {
                        if (!tableArray[i].isSel) {
                            tableArray[i].isSel = true;
                            selTable.push(tableArray[i]);
                            disabledRow(i);
                        }
                    }
                    table.reload('courseTable1', {data: selTable})
                    // let t = $(obj.tr).find('input[type=checkbox]');
                    $(this).prop('disabled', true);
                    $(this).addClass('layui-btn-disabled');
                    $(this).next().css("cursor", "not-allowed");
                }
            } else {
                if (obj.checked && !$(this).hasClass("layui-btn-disabled")) {
                    //选中了某一行，使不同管理员的行不可选
                    if (!obj.data.isSel) {
                        obj.data.isSel = true;
                        obj.update(obj.data);
                        selTable.push(obj.data);
                        disabledRow($(obj.tr).index());
                        table.reload('courseTable1', {data: selTable});
                        let th = $("div[lay-id=courseTable] .layui-table-header input[type='checkbox']")
                        let thCheck = th.prop('checked');
                        if (thCheck) {
                            th.prop('disabled', true);
                            th.addClass('layui-btn-disabled');
                            th.next().css("cursor", "not-allowed");
                        }
                    }

                }
            }
        })

        function abledRow(index) {
            //取消行不可选
            let t = $("div[lay-id=courseTable] .layui-table tr[data-index=" + index + "] input[type='checkbox']");
            $("div[lay-id=courseTable] th[data-field='0'] input[type='checkbox']").prop('disabled', false).prop('checked', false).removeClass('layui-btn-disabled');
            t.prop('disabled', false);
            t.prop('checked', false);
            t.removeClass('layui-btn-disabled');
            t.next().css("cursor", "unset").removeClass('layui-form-checked');
            $("div[lay-id=courseTable] .layui-table tr[data-index=" + index + "]").css("background-color", "");
            $("div[lay-id=courseTable] .layui-table tr[data-index=" + index + "]").css("color", "#666");

            let th = $("div[lay-id=courseTable] .layui-table-header input[type='checkbox']");
            th.prop('disabled', false);
            th.prop('checked', false);
            th.removeClass('layui-btn-disabled');
            th.next().css("cursor", "unset").removeClass('layui-form-checked');
        }

        function disabledRow(index) {
            //第index行复选框不可选
            let t = $("div[lay-id=courseTable] .layui-table tr[data-index=" + index + "] input[type='checkbox']");
            $("div[lay-id=courseTable] th[data-field='0'] input[type='checkbox']").prop('disabled', true).addClass('layui-btn-disabled');
            t.prop('disabled', true);
            t.addClass('layui-btn-disabled');
            t.next().css("cursor", "not-allowed");
            //置灰
            $("div[lay-id=courseTable] .layui-table tr[data-index=" + index + "]").css("background-color", "#E0E0E0");
            $(" div[lay-id=courseTable] .layui-table tr[data-index=" + index + "]").css("color", "#9f9696");
        }

        // 清除
        $(".clear").click(function () {
            table.reload('courseTable1', {data: []});
            tabData.map(item => {
                item.isSel = false
            })
            table.reload('courseTable');
            selTable = [];
            window.localStorage.removeItem("clazzData");
        })

        function getFormDistinctFiled() {
            const fieldAliasArray = ["bjxx_rxnf", "bjxx_ssyx", "bjxx_zy", "bjxx_bjmc", "bjxx_bjbh", "bjxx_ssjys", "xnxq_xnxqh"];
            fieldAliasArray.forEach(fieldAlias => {
                let formAlias = fieldAlias === "xnxq_xnxqh" ? "xnxq" : "bjxx";
                $.post(_VR_ + `/teacherIdle/getFormDistinctFiled`, {
                    formAlias: formAlias,
                    fieldAlias,
                    fid: fid
                }, function (data) {
                    if (data.list) {
                        let inputElement = $(`input[name="${fieldAlias}"]`).parent().find("ul");
                        let selectElement = $(`select[name="${fieldAlias}"]`);
                        selectElement.append(new Option("请选择", ""));
                        data.list.forEach(item => {
                            inputElement.append("<li>" + item + "</li>");
                            selectElement.append(new Option(item, item));
                        });
                        let termData = window.localStorage.getItem("termData");
                        if (termData) {
                            $("select[name='xnxq_xnxqh']").val(termData);
                        }
                        layui.form.render('select');
                    }
                }, 'json');
            });
        }

        $(".btns span").click(function () {
            let term = $("select[name='xnxq_xnxqh']").val();
            if (!term) {
                layer.msg("请选择开课学期", {icon: 2, time: 2000});
                return false;
            }
            if (selTable.length === 0) {
                layer.msg("请选择班级", {icon: 2, time: 2000});
                return false;
            }
            location.href = "/pc/cultivation/clazzSpeedMatch/courseIndex?fid=" + fid + "&uid=" + uid + "&term=" + term;
            window.localStorage.setItem("clazzData", JSON.stringify(selTable));
            window.localStorage.setItem("termData", term);
        })
    })
</script>

</html>