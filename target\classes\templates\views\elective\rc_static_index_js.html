<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no,initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <title>微点名</title>
    <script src="js/responsive.js"></script>
    <link rel="stylesheet" href="css/global.css">
    <link rel="stylesheet" href="css/iosSelect.css">
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/slide.css?v=1">
    <link rel="stylesheet" href="css/sta.css?v=1">
</head>
<body>
<!--<div class="sta-nav">-->
<!--</div>-->
<div class="sta-box-wrap">
    <div class="sta-box">
        <div class="search-wrap">
            <div class="search flex">
                <img src="images/search.png" alt="">
                <input type="text" placeholder="搜索教学班" id="nameIp2" onkeyup="getjxbList();">
            </div>
        </div>
        <div class="con-select">
            <ul>
                <li data-cla="course" id="selKc"><span>课程</span> <i></i></li>
                <li data-cla="date" class="selDateRange1" id="selDate2"><span>日期</span> <i></i></li>
                <!--                    <li data-cla="jxbzclx" id="jxbzclx"><span>教学班组成类型</span> <i></i></li>-->
            </ul>
        </div>
        <div class="teach-class-list">
        </div>
    </div>
</div>
<div class="marker" style="display: none;"></div>
<div class="slide-sel course-sel" data-cla="course" style="top: 1.90rem">
    <div class="course-list">
        <ul id="kcBox">
        </ul>
    </div>
</div>
</body>
<script type="text/javascript">
    var fid = '[[${fid}]]';
</script>
<script src="js/CXJSBridge.js"></script>
<script src="js/zepto.min.js"></script>
<script src="js/iosSelect.js"></script>
<!--<script src="js/slide.js"></script>-->
<!--<script src="js/export.js?v=1.1"></script>-->
<script>
    function _jsBridgeReady() {
        jsBridge.postNotification('CLIENT_WEB_BOUNCES', {"forbiddenFlag": 1});
    }

    $(document).ready(function () {
        // 列表高度
        var boxH = $(window).height() - $(".teach-class-list").offset().top;
        $(".teach-class-list").height(boxH);
    })
    getKc();
    getjxbList();

    function getKc() {
        $.ajax({
            type: "POST",
            url: "/elective/rollcall/getRollcallLogKC",
            dataType: 'json',
            success: function (data) {
                var html = "";
                html += "<li class=\"active\">";
                html += "<h1>全部</h1>";
                html += "</li>";
                for (var i = 0; i < data.kcs.length; i++) {
                    html += "<li>";
                    html += "<h1>" + data.kcs[i].course + "</h1>";
                    html += "</li>";
                }
                $("#kcBox").html(html);
            }
        });
    }

    function getjxbList() {
        var name = $("#nameIp2").val();
        var classDate = $("#selDate2").find("span").text();
        // var jxbzclx = $("#jxbzclx").find("span").text();
        var kc = $("#selKc").find("span").text();
        $.ajax({
            type: "POST",
            url: "/elective/rollcall/getRollcallstaticJXB",
            data: {name: name, courseTime: classDate, kc: kc,js:1},
            dataType: 'json',
            success: function (data) {
                var html = "";
                for (var i = 0; i < data.data.length; i++) {
                    html += "<a href=\"javascript:jxbIndex('" + data.data[i].teachingClassCode + "','" + classDate + "','" + data.data[i].teachingClassName + "');\">";
                    html += "<div class=\"class-name\">";
                    html += "<h1>" + data.data[i].course + "</h1>";
                    html += "<span></span>";
                    html += "</div>";
                    html += "<div class=\"class-course\">";
                    html += "<span>" + data.data[i].teachingClassName + "</span>";
                    html += "</div>";
                    html += "<ul class=\"class-state\">";
                    html += "<li>" + data.data[i].teacherName + "</li>";
                    html += "</ul></a>";
                }
                $(".teach-class-list").html(html);
            }
        });
    }

    function jxbIndex(jxb, courseTime, jxbmc) {
        var webUrl = window.location.origin + "/elective/rollcall/jxbStaticIndex?jxbCode=" + jxb + "&courseTime=" + courseTime + "&jxbmc=" + jxbmc;
        jsBridge.postNotification('CLIENT_OPEN_URL', {"toolbarType": 2, "webUrl": webUrl, "loadType": 1});
    }


    // 初始化时间
    var now = new Date();
    var nowYear = now.getFullYear();
    var nowMonth = now.getMonth() + 1;
    var nowDate = now.getDate();
    // 数据初始化
    let dataArr = [];
    for (var y = nowYear - 1; y <= nowYear; y++) {
        for (var m = 1; m <= 12; m++) {
            if (y == nowYear && m > nowMonth) {
                break;
            }
            dateDataE(y, m)
        }
    }

    function dateDataE(year, month, callback) {
        var dataLen = 0;
        if (/^(1|3|5|7|8|10|12)$/.test(month)) {
            dataLen = 31;
        } else if (/^(4|6|9|11)$/.test(month)) {
            dataLen = 30;
        } else if (/^2$/.test(month)) {
            if (year % 4 === 0 && year % 100 !== 0 || year % 400 === 0) {
                dataLen = 29;
            } else {
                dataLen = 28;
            }
        } else {
            throw new Error('month is illegal');
        }
        if (year == nowYear && month == nowMonth) {
            for (var i = 1; i <= nowDate; i++) {
                dataArr.push({
                    id: '' + year + month + i,
                    value: year + '年' + month + '月' + i + '日'
                });
            }
            return false;
        }
        for (var i = 1; i <= dataLen; i++) {
            dataArr.push({
                id: '' + year + month + i,
                value: year + '年' + month + '月' + i + '日'
            });
        }
    };

    $(".selDateRange1").click(function () {
        var _this = $(this);
        let text = _this.find("span").text();
        var oneLevelId = "" + nowYear + nowMonth + nowDate;
        var twoLevelId = "" + nowYear + nowMonth + nowDate;
        if (text != "日期") {
            let dataArr = text.split("-");
            oneLevelId = getYearMonthDate(dataArr[0]);
            twoLevelId = getYearMonthDate(dataArr[1]);
        }
        new IosSelect(2,
            [dataArr, dataArr],
            {
                title: '选择日期范围',
                itemHeight: 35,
                oneLevelId: oneLevelId,
                twoLevelId: twoLevelId,
                showLoading: true,
                callback: function (selectOneObj, selectTwoObj) {
                    _this.find("span").text(selectOneObj.value + "-" + selectTwoObj.value);
                    getjxbList();
                }
            });
    })

    //    下拉选择
    $(".con-select").on('click', 'ul li', function () {
        let dataAttr = $(this).attr('data-cla');
        $(this).toggleClass("active").siblings().removeClass("active");
        let isHasActive = $(this).hasClass("active");
        if (isHasActive) {
            $(".marker").show()
            $("." + dataAttr + '-sel').show();
            $(".slide-sel:not(." + dataAttr + "-sel)").hide();
        } else {
            $(".marker").hide()
            $("." + dataAttr + '-sel').hide();
        }
    });


    // 选择
    $(".slide-sel ").on('click', 'ul li', function () {
//$(".slide-sel ul li").click(function () {
        $(this).addClass("active").siblings().removeClass("active");
        $(this).parents(".slide-sel").hide();
        $(".marker").hide();
        var dataCla = $(this).parents(".slide-sel").attr("data-cla");
        var text = $(this).find('h1').text();
        var liEle = $(".con-select ul li[data-cla=" + dataCla + "]");
        liEle.find('span').text(text);
        liEle.removeClass("active");
        if ($(this).parents(".slide-sel").hasClass("course-sel")){
            getjxbList()
        }
    });


    //点击遮罩
    $(".marker").click(function () {
        $(".marker,.slide-sel").hide();
        $(".con-select ul li").removeClass("active");
    })

    function getYearMonthDate(date) {
        let strYearsplit = date.split("年");
        let strMonthsplit = strYearsplit[1].split("月");
        return strYearsplit[0] + strMonthsplit[0] + strMonthsplit[1].replace("日", "");
    }

</script>
</html>