<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分流管理</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/basic/global.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/plugin/layui-v2.9.13/layui/css/layui.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/basic/layuiReset.css'}">
<!--    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/basic/index3.0.css'}">-->
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/basic/slideCommon.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/basic/shuntManage.css'}">

    <style>
        .j-search-con .j-select-year {
            width: 200px;
        }

        .main .item .i-con .class-box {
            overflow: unset;
        }

        .form-search-mes.form-right .layui-inline {
            width: 25%;
        }
    </style>
</head>
<body>
<div class="main" style="background-color: #fff;margin: 0 auto;">
    <div class="m-top" style="justify-content: space-between;">
        <div class="title">分流管理</div>
        <button class="layui-btn" style="margin-right: 30px;" id="saveBtn">确定</button>
    </div>

    <div class="item" style="background: #ffffff;border-radius: 8px;padding-top: 16px;overflow: hidden;">
        <div class="i-con" style="display: flex;overflow: unset;">
            <div class="class-box" style="width: 47.5%;">
                <div class="cb-top">
                    <div class="tit" style="margin-right:24px;">待分流学生</div>
                </div>
                <form id="leftForm" action="" class="layui-form form-search-mes form-left" style="margin-top: 24px;">
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: 56px;">批次名称</label>
                        <div class="layui-input-inline">
                            <div class="j-search-con single-box">
                                <input type="text" placeholder="请选择" name="batchName" readonly="" class="schoolSel">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <ul id="batch">
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: 56px;">分流前系部</label>
                        <div class="layui-input-inline">
                            <div class="j-search-con single-box">
                                <input type="text" placeholder="请选择" name="dept" readonly="" class="schoolSel">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <ul class="deptList">

                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: 56px;">分流前专业</label>
                        <div class="layui-input-inline">
                            <div class="j-search-con single-box">
                                <input type="text" placeholder="请选择" name="major" readonly="" class="schoolSel">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <ul class="majorList">

                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: 56px;">分流前班级</label>
                        <div class="layui-input-inline">
                            <div class="j-search-con single-box">
                                <input type="text" placeholder="请选择" name="className" readonly="" class="schoolSel">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <ul class="classList">

                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button type="reset" class="layui-btn layui-btn-primary clear">重置</button>
                        <button type="submit" class="layui-btn " lay-submit id="searchBtnLeft">查询</button>
                    </div>
                </form>

                <div class="j-table" style="border:none;">
                    <table class="layui-table" id="classTable" lay-filter="classTable">
                    </table>
                </div>
            </div>

            <div class="mutate">
                <div class="up" id="up"></div>
                <div class="down" id="down"></div>
            </div>

            <div class="class-box" style="width: 47.5%;overflow: unset;">
                <div class="cb-top" style="justify-content: space-between;">
                    <div class="tit">学生分流去向</div>
                </div>
                <form id="rightForm" action="" class="layui-form form-search-mes form-right" style="margin-top: 24px;">
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: 56px;">所属系部</label>
                        <div class="layui-input-inline">
                            <div class="j-search-con single-box">
                                <input type="text" placeholder="请选择" name="dept" readonly="" class="schoolSel">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <ul class="deptList">

                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: 56px;">专业</label>
                        <div class="layui-input-inline">
                            <div class="j-search-con single-box">
                                <input type="text" placeholder="请选择" name="major" readonly="" class="schoolSel">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <ul class="majorList2">

                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: 56px;">班级</label>
                        <div class="layui-input-inline">
                            <div class="j-search-con single-box">
                                <input type="text" placeholder="请选择" name="className" readonly="" class="schoolSel">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <ul class="classList">

                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-inline">
                        <button type="reset" class="layui-btn layui-btn-primary clear">重置</button>
                        <button type="submit" class="layui-btn " lay-submit id="searchBtnRight">查询</button>
                    </div>
                </form>
                <div class="j-table" style="border:none;">
                    <table class="layui-table" id="teachingClassForm" lay-filter="teachingClassForm">
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>


</body>
<script th:src="${_CPR_+_VR_+'/js/jquery-3.3.1.min.js'}"></script>
<script th:src="${_CPR_+_VR_+'/plugin/layui-v2.9.13/layui/layui.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/my.util.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/basic/slideCommon2.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/basic/shuntManage.js'}"></script>
<script th:inline="javascript">
    const _VR_ = [[${_VR_}]]
    var fid = [[${fid}]]
    var uid = [[${uid}]]
    $(function (){
        $.get("/basic/shunt/batch",function (res){
            if (U.su(res)){
                var batchLi = "";
                res.data.forEach(item=>{
                    batchLi+='<li data-value="'+item.dlflcssz_pcbh+'" data-term="'+item.dlflcssz_sxxq+'">'+item.dlflcssz_pcmc+'</li>'
                })
                $("#batch").html(batchLi);
            }else {
                U.fail(res.msg)
            }

        })
    })

</script>
</html>