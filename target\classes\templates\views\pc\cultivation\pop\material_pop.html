<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=r, initial-scale=1.0">
    <title>选择教材</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/global.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/layui/css/layui.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/materialPop.css'}">
    <script th:src="${_CPR_+_VR_+'/layui/layui.js'}"></script>
</head>
<body>
<div id="selTextbook" class="dialog">
    <div class="tips">使用说明：不勾选数据直接点击 [确定] 提交，即可清空已有教材。</div>
    <div class="dialog-con">
        <form action="" class="layui-form form-textbook">
            <div class="layui-inline">
                <label class="layui-form-label">教材名称</label>
                <div class="layui-input-inline">
                    <input type="text" name="jc_jcmc" placeholder="请输入" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">教材号</label>
                <div class="layui-input-inline">
                    <input type="text" name="jc_jcbh" placeholder="请输入" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label" style="width: 76px;">出版号ISBN</label>
                <div class="layui-input-inline">
                    <input type="text" name="jc_isbn" placeholder="请输入" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                <button type="submit" class="layui-btn " lay-submit lay-filter="selTextbookTable">筛选</button>
            </div>
        </form>
        <div class="courseList">
            <table class="layui-hide" id="textbookList" lay-filter="textbookList"></table>
            <div class="z-check">
                <span class="check" id="checkAll"></span>选择全部数据
            </div>
            <div class="selCourse" id="selTextbookCount">已选中<em>0</em>条</div>
        </div>
    </div>
    <div class="dialog-footer">
        <button id="textbookCancelBtn">取消</button>
        <button id="textbookSureBtn">确定</button>
    </div>
</div>
</body>
<script>
    let selDataArray = [], uniqueArray = [];
    layui.use(['table', 'jquery', 'util', 'form', 'layer'], function () {
        var util = layui.util,
            form = layui.form,
            layer = layui.layer,
            table = layui.table,
            $ = layui.jquery;
        /******************************************** 指定教材 ************************************************/
        // table渲染
        table.render({
            elem: '#textbookList',
            url: '/cultivation/majorCourseSet/getMaterialInfoData',
            where: {deptId: "[[${deptId}]]"},
            height: '400px',
            page: true,
            cols: [
                [{
                    type: 'checkbox',
                    width: 80,
                }, {
                    field: 'jc_jcmc',
                    title: '教材名称',
                    align: "center",
                    width: 256,
                }, {
                    field: 'jc_jcbh',
                    title: '教材号',
                    align: "center",
                    width: 199
                }, {
                    field: 'jc_isbn',
                    title: '出版号ISBN',
                    align: "center",
                    width: 230
                }
                ],
            ],
            done: function (res, curr, count) {
                $("#selTextbookCount").attr("count", res.count);
                if ($("#selTextbook .checked").length > 0) {
                    $('div[lay-id="textbookList"] input[type=checkbox]').prop("checked", true);
                    $('div[lay-id="textbookList"] .layui-form-checkbox').addClass("layui-form-checked");
                    return false;
                }
                uniqueArray = $.grep(selDataArray, function (item, index) {
                    return index === $.inArray(item.jc_jcbh, $.map(selDataArray, function (obj) {
                        return obj.jc_jcbh;
                    }));
                });
                for (let i = 0; i < res.data.length; i++) {
                    for (let j = 0; j < uniqueArray.length; j++) {
                        //数据id和要勾选的id相同时checkbox选中
                        if (res.data[i].jc_jcbh === uniqueArray[j].jc_jcbh) {
                            //这里才是真正的有效勾选
                            res.data[i]["LAY_CHECKED"] = 'true';
                            //找到对应数据改变勾选样式，呈现出选中效果
                            const index = res.data[i]['LAY_TABLE_INDEX'];
                            $('div[lay-id="textbookList"] tr[data-index=' + index + '] input[type="checkbox"]').prop('checked', true).next().addClass('layui-form-checked');
                        }
                    }
                }
            }
        });
        // 筛选
        // 提交事件
        form.on('submit(selTextbookTable)', function (data) {
            const field = data.field; // 获取表单字段值
            field.deptId = "[[${deptId}]]";
            // 重新加载table
            table.reload('textbookList', {where: field, page: {curr: 1}});
            return false; // 阻止默认 form 跳转
        });
        $("#selTextbook button[type='reset']").click(function () {
            const field = {};
            field.jc_jcmc = "";
            field.jc_jcbh = "";
            field.jc_isbn = "";
            field.deptId = "[[${fid}]]";
            table.reload('textbookList', {where: field, page: {curr: 1}});
        })
        // 确定
        $("#textbookSureBtn").click(function () {
            let selectedData = table.checkStatus('textbookList').data;
            window.parent.postMessage({
                "type": "setFieldValue",
                "data": [
                    {
                        "alias": "zyksz_jcmc",
                        "val": [selectedData.map(item => item.jc_jcmc).join(",")],
                        "compt": "editinput"
                    },
                    {
                        "alias": "zyksz_jch",
                        "val": [selectedData.map(item => item.jc_jcbh).join(",")],
                        "compt": "editinput"
                    },
                    {
                        "alias": "zyksz_isbn",
                        "val": [selectedData.map(item => item.jc_isbn).join(",")],
                        "compt": "editinput"
                    }
                ]
            }, '*');
            window.parent.postMessage({type: "close"}, '*');
        })
        // 取消
        $("#textbookCancelBtn").click(function () {
            layer.closeAll();
        })
        //操作每行数据复选框
        table.on('checkbox(textbookList)', function (obj) {
            const checkStatus = table.checkStatus('textbookList');
            let dataArray = layui.table.cache["textbookList"];
            checkStatus.data.forEach(function (obj) {
                if (!selDataArray.some(function (item) {
                    return item.jc_jcbh === obj.jc_jcbh;
                })) {
                    selDataArray.push(obj);
                }
            });
            if (!obj.checked) {
                selDataArray = selDataArray.filter(function (data) {
                    return data.jc_jcbh !== obj.data.jc_jcbh;
                });
                if (obj.type === "all") {
                    selDataArray = selDataArray.filter(function (item1) {
                        return !dataArray.some(function (item2) {
                            return item1.jc_jcbh === item2.jc_jcbh;
                        });
                    });
                }
            }
            $("#selTextbookCount em").text(selDataArray.length);
        });
        $("#checkAll").click(function () {
            $(this).toggleClass("checked");
            if ($(this).hasClass("checked")) {
                $('div[lay-id="textbookList"] input[type=checkbox]').prop("checked", true);
                $('div[lay-id="textbookList"] .layui-form-checkbox').addClass("layui-form-checked");
                $("#selTextbookCount em").text($("#selTextbookCount").attr("count"));
            } else {
                $('div[lay-id="textbookList"] input[type=checkbox]').prop("checked", false);
                $('div[lay-id="textbookList"] .layui-form-checkbox').removeClass("layui-form-checked");
                $("#selTextbookCount em").text(0);
            }
        });
    });
</script>

</html>