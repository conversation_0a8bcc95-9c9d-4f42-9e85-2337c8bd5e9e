<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>维护班级</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/global.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/plugin/layui/css/layui.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/layuiReset.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/plateClass.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/slideCommon.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/dialogCourse.css'}">
    <script th:src="${_CPR_+_VR_+'/plugin/layui/layui.js'}"></script>
</head>
<body>
<div class="main" style="background-color: #fff;margin: 0 auto;">
    <div class="m-top" style="justify-content: space-between;">
        <div class="title">班级维护</div>
        <button class="layui-btn" style="margin-right: 30px;" id="btnPlateMaintainSure">确定</button>
    </div>
    <div class="item">
        <div class="i-con">
            <div class="course-inform">
                <h4 style="margin-top: 24px;">板块信息</h4>
                <ul>
                    <li>
                        <div class="name">开课学期：</div>
                        <div class="tit" th:text="${info?.semester}"></div>
                    </li>
                    <li>
                        <div class="name">板块名称：</div>
                        <div class="tit" th:text="${info?.plateTypeName}"></div>
                    </li>
                    <li>
                        <div class="name">板块项目名称：</div>
                        <div class="tit" th:text="${info?.classNameArr}"></div>
                    </li>

                </ul>
            </div>
        </div>
    </div>
    <div class="item" style="background: #ffffff;border-radius: 8px;padding-top: 16px;overflow: hidden;">
        <div class="i-con" style="display: flex;overflow: unset;">
            <div class="class-box" style="width: 47.5%;">
                <div class="cb-top">
                    <div class="tit" style="margin-right:24px;">选择班级</div>
                </div>
                <form action="" class="layui-form form-search-mes" style="margin-top: 24px;"
                      lay-filter="classTableFilter">
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: 56px;">年级</label>
                        <div class="layui-input-inline">
                            <div class="j-search-con multiple-box">
                                <input type="text" placeholder="请选择" name="bjxx_rxnf" readonly="" class="schoolSel"
                                       formAlias="bjxx" fieldAlias="bjxx_rxnf">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <ul>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: 56px;">院系</label>
                        <div class="layui-input-inline">
                            <div class="j-search-con multiple-box">
                                <input type="text" placeholder="请选择" name="bjxx_ssyx" readonly="" class="schoolSel"
                                       formAlias="bjxx" fieldAlias="bjxx_ssyx">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <ul>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: 56px;">专业</label>
                        <div class="layui-input-inline">
                            <div class="j-search-con multiple-box">
                                <input type="text" placeholder="请选择" name="bjxx_zy" readonly="" class="schoolSel"
                                       formAlias="bjxx" fieldAlias="bjxx_zy">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <ul>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: 56px;">班级</label>
                        <div class="layui-input-inline">
                            <div class="j-search-con multiple-box">
                                <input type="text" placeholder="请选择" name="bjxx_bjmc" readonly="" class="schoolSel"
                                       formAlias="bjxx" fieldAlias="bjxx_bjmc">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <ul>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        <button type="submit" class="layui-btn " lay-submit lay-filter="classTableFilter">查询</button>
                    </div>
                </form>
                <div class="stu-static"><span>人数总计：已选<i>0</i>人</span></div>
                <div class="j-table" style="border:none;">
                    <table class="layui-table" id="classTable" lay-filter="classTable">
                    </table>
                </div>
            </div>

            <div class="mutate">
                <div class="up" id="up"></div>
                <div class="down" id="down"></div>
            </div>
            <div class="class-box" style="width: 47.5%;overflow: unset;">
                <div class="cb-top" style="justify-content: space-between;">
                    <div class="tit">已选班级</div>
                </div>
                <form action="" class="layui-form form-search-mes" style="margin-top: 24px;"
                      lay-filter="teachingClassFilter">
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: 56px;">年级</label>
                        <div class="layui-input-inline">
                            <div class="j-search-con multiple-box">
                                <input type="text" placeholder="请选择" name="grade" readonly="" class="schoolSel"
                                       formAlias="plateClassMaintain" fieldAlias="grade" source="table">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <ul>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: 56px;">院系</label>
                        <div class="layui-input-inline">
                            <div class="j-search-con multiple-box">
                                <input type="text" placeholder="请选择" name="colleges" readonly="" class="schoolSel"
                                       formAlias="plateClassMaintain" fieldAlias="colleges" source="table">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <ul>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: 56px;">专业</label>
                        <div class="layui-input-inline">
                            <div class="j-search-con multiple-box">
                                <input type="text" placeholder="请选择" name="majorName" readonly="" class="schoolSel"
                                       formAlias="plateClassMaintain" fieldAlias="majorName" source="table">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <ul>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: 56px;">班级</label>
                        <div class="layui-input-inline">
                            <div class="j-search-con multiple-box">
                                <input type="text" placeholder="请选择" name="className" readonly="" class="schoolSel"
                                       formAlias="plateClassMaintain" fieldAlias="className" source="table">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <ul>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        <button type="submit" class="layui-btn " lay-submit lay-filter="teachingClassFilter">查询
                        </button>
                    </div>
                </form>
                <div class="stu-static"><span>人数总计：已选<i th:utext="${info?.number}">0</i>人</span></div>
                <div class="j-table" style="border:none;">
                    <table class="layui-table" id="teachingClassForm" lay-filter="teachingClassForm">
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- 成功弹窗 -->
<div class="dialog" id="dialogTip" style="display: none;">
    <div class="dialog-con">
        <img src="/images/cultivation/mooc/success-icon.png" alt="">
        <p>保存成功</p>
        <button id="tipSuccessBtn">确定</button>
    </div>
</div>
</body>
<script th:inline="javascript">
    const _VR_ = [[${_VR_}]];
    const fid = [[${fid}]];
    const id = [[${info?.id}]];
</script>
<script th:src="@{${_CPR_+_VR_}+'/js/cultivation/plateClassService.js'(v=${new java.util.Date().getTime()})}"></script>
<script th:src="@{${_CPR_+_VR_}+'/js/cultivation/plateCommon.js'(v=${new java.util.Date().getTime()})}"></script>
</html>