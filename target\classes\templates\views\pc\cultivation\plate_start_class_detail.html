<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>板块开课</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/global.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/plugin/layui-v2.8.18/layui/css/layui.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/slideCommon.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/index3.0.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/layuiReset.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/dialogCourse.css'}">
    <script th:src="${_CPR_+_VR_+'/plugin/layui-v2.8.18/layui/layui.js'}"></script>
    <style>
        .titleColor {
            color: #F76560;
            padding-right: 3px
        }

        .main .item .btn-con {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            margin: 16px 0 0;
        }
        .layui-table tr.tr-blue {
            background-color: #ccdff8 !important;
        }
        .layui-table-cell .gray {
            color: #999;
            cursor: not-allowed;
        }
    </style>
</head>

<body>
<div class="main" style="background-color: #fff;margin: 0;">
    <div class="m-top">
        <div class="title">板块开课</div>
    </div>
    <div class="item">
        <div class="i-con">
            <div class="course-inform">
                <h4 style="margin-top: 24px;">课程信息</h4>
                <ul>
                    <li>
                        <div class="name">开课学期：</div>
                        <div class="tit" th:utext="${info?.semester}"></div>
                    </li>
                    <li>
                        <div class="name">板块类型：</div>
                        <div class="tit" th:utext="${info?.plateTypeName}"></div>
                    </li>
                    <li>
                        <div class="name">板块级别：</div>
                        <div class="tit" th:utext="${info?.plateTypeLevelName}"></div>
                    </li>
                    <li>
                        <div class="name">课程数：</div>
                        <div class="tit"></div>
                    </li>
                </ul>
            </div>
            <table class="layui-hide" id="plateItem" lay-filter="plateItem"></table>
        </div>
    </div>
    <div class="item">
        <h4 style="margin-top:32px;font-size: 16px;">开班</h4>
        <div class="btn-con">
            <button class="layui-btn layui-btn-normal add-btn">保存</button>
        </div>
        <div class="i-con">
            <div class="j-table" style="border:none;">
                <table class="layui-table" id="materialTable" lay-filter="materialTable">
                </table>
            </div>
        </div>
    </div>
</div>
<!-- 成功弹窗 -->
<div class="dialog" id="dialogTip" style="display: none;">
    <div class="dialog-con">
        <img src="/images/cultivation/mooc/success-icon.png" alt="">
        <p>保存成功</p>
        <button id="tipSuccessBtn">确定</button>
    </div>
</div>
</body>
</html>
<!-- 添加教学班 -->
<script type="text/html" id="addTeachClass">
    {{# if(d.notArrangedCount<=0) { }}
    <span class="gray">添加教学班</span>
    {{# }else{ }}
    <span class="edit" lay-event="addClass" >添加教学班</span>
    {{# } }}
</script>
<!-- 任课教师 -->
<script type="text/html" id="selectTeacher">
    <div class="j-search-con multiple-box" style="width: 150px;">
        <input type="text" name="teacher" placeholder="请选择" readonly="" class="schoolSel">
        <span class="j-arrow"></span>
        <div class="j-select-year">
            <div class="search">
                <input type="text" placeholder="搜索" class = "teacherSearch">
                <span></span>
            </div>
            <ul class = "teacherList">
                {{#  layui.each(d.teacherList, function(i, v){ }}
                <li uid="{{= v.uid }}" xgh = "{{= v.xgh }}" uname = "{{= v.uname }}">{{= v.uname }} </li>
                {{#  }); }}
            </ul>
        </div>
    </div>
</script>
<!-- 是否选课 -->
<script type="text/html" id="isSelectCourse">
    <div class="j-search-con single-box " style="width: 150px;">
        <input type="text" name="courseSelectionFlag" placeholder="请选择" readonly="" class="schoolSel">
        <span class="j-arrow"></span>
        <div class="j-select-year">
            <ul>
                <li>是</li>
                <li>否</li>
            </ul>
        </div>
    </div>
</script>
<!-- 性别 -->
<script type="text/html" id="isGender">
    <div class="j-search-con single-box " style="width: 150px;">
        <input type="text" name="optionalGender" placeholder="请选择" readonly="" class="schoolSel" value="全部">
        <span class="j-arrow"></span>
        <div class="j-select-year">
            <ul>
                <li>全部</li>
                <li>男</li>
                <li>女</li>
            </ul>
        </div>
    </div>
</script>
<!-- 指定教室 -->
<script type="text/html" id="selectClassroom">
    <div class="j-search-con single-box" style="width: 150px;">
        <input type="text" name="classRoom" placeholder="请选择" readonly="" class="schoolSel">
        <span class="j-arrow"></span>
        <div class="j-select-year">
            <ul>
            </ul>
        </div>
    </div>
</script>
<!-- 指定教室类型 -->
<script type="text/html" id="selectClassroomType">
    <div class="j-search-con single-box" style="width: 150px;">
        <input type="text" name="classRoomType" placeholder="请选择" readonly="" class="schoolSel" formAlias="jslx" fieldAlias="zd_jslx">
        <span class="j-arrow"></span>
        <div class="j-select-year">
            <div class="search">
                <input type="text" placeholder="搜索">
                <span></span>
            </div>
            <ul>
            </ul>
        </div>
    </div>
</script>
<!-- 授课方式 -->
<script type="text/html" id="teachMethod">
    <div class="j-search-con single-box" style="width: 150px;">
        <input type="text" name="teachMethod" placeholder="请选择" readonly="" class="schoolSel" formAlias="218612" fieldAlias="teacher_type">
        <span class="j-arrow"></span>
        <div class="j-select-year">
            <ul>
            </ul>
        </div>
    </div>
</script>
<!-- 人数 -->
<script type="text/html" id="stuCount">
    <input type="number" name="number" style="width: 150px;" class="layui-input classIpt stuCount" min="1"
           placeholder="请输入" value="{{d.number}}">
</script>
<!-- 助教 -->
<script type="text/html" id="selectAssistant">
    <div class="j-search-con  multiple-box" style="width: 150px;">
        <input type="text" name="assistant" placeholder="请选择" readonly="" class="schoolSel">
        <span class="j-arrow"></span>
        <div class="j-select-year">
            <div class="search">
                <input type="text" placeholder="搜索" class = "teacherSearch">
                <span></span>
            </div>
            <ul class = "assistantList">
                {{#  layui.each(d.teacherList, function(i, v){ }}
                <li uid="{{= v.uid }}" xgh = "{{= v.xgh }}" uname = "{{= v.uname }}">{{= v.uname }} </li>
                {{#  }); }}
            </ul>
        </div>
    </div>
</script>
<!-- 删除 -->
<script type="text/html" id="classToolBar">
    <span class="delet" lay-event="del">删除</span>
</script>
<script th:inline="javascript">
    const _VR_ = [[${_VR_}]];
    const fid = [[${info.fid}]];
    const id = [[${info.id}]];
    let courseNo = "";
</script>
<script th:src="${_CPR_+_VR_+'/js/cultivation/plateCommon.js'}"></script>
<script th:src="@{${_CPR_+_VR_}+'/js/cultivation/plateSubItemCourses.js'(v=${new java.util.Date().getTime()})}"></script>