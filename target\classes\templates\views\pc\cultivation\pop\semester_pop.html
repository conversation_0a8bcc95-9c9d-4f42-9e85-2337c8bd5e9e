<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生成教学计划</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/global.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/plugin/layui/css/layui.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/slideCommon.css'}"/>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/teachPlan.css'}"/>
</head>

<body>
<div class="marker"></div>
<div class="dialog" id="teachPlanDialog">
    <div class="dialog-con">
        <div class="item">
            <div class="label">选择目标学年学期<span class="layui-tips"
                                                     data-tip="批量编辑已选数据，如需修改所有数据请先勾选表格下方“选中所有数据”"></span>
            </div>
            <div class="j-search-con multiple-box">
                <input type="text" name="termSel" placeholder="请选择" readonly="" class="schoolSel">
                <span class="j-arrow"></span>
                <div class="j-select-year">
                    <div class="search">
                        <input type="text" placeholder="搜索">
                        <span></span>
                    </div>
                    <div class="all-selects">全选</div>
                    <ul class="dropdown-list">
                    </ul>
                </div>
            </div>
        </div>
        <p class="error-tips">请选择学年学期</p>
    </div>
    <div class="dialog-footer">
        <button class="btn-cancel">取消</button>
        <button class="btn-sure">确定</button>
    </div>
</div>
</body>
<script th:src="${_CPR_+_VR_+'/plugin/layui/layui.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/cultivation/slideCommon.js'}"></script>
<script th:src="@{${_CPR_+_VR_}+'/js/cultivation/teachPlan.js'(v=${new java.util.Date().getTime()})}"></script>
<script th:inline="javascript">
    const formUserId = [[${formUserId}]];
    const formId = [[${formTopBtnBO.formId}]];
    const fid = [[${formTopBtnBO.fid}]];
    const uid = [[${formTopBtnBO.uid}]];
    const selectTotal = [[${formTopBtnBO.selectTotal}]];
    const queryId = [[${formTopBtnBO.queryId}]];
    let dataType = [[${dataType}]];
    let unitType = [[${unitType}]];
    const _VR_ = [[${_VR_}]];
</script>
</html>