<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>班教材签收单</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/material/global.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/plugin/layui/css/layui.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/material/common.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/material/receipt_form_new.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/material/newPrints.css'}" media="print">
    <script th:src="${_CPR_+_VR_+'/plugin/layui/layui.js'}"></script>
    <style type="text/css" media=print>
        .noprint {
            display: none
        }

        .lab-box {
            overflow: hidden;
            margin: 0 auto;
        }

        .tableDetail table {
            table-layout: fixed;
        }
    </style>

</head>

<body>
<div class="main">
    <div class="top">
        <h4>班教材签收单</h4>
        <div class="r-title">
            <span class="export">导出</span>
            <span class="export-record">导出记录</span>
            <span class="print" id="prints">打印</span>
        </div>
    </div>
    <div class="form-con">
        <div class="sel-item">
            <div class="sel-title">学年学期</div>
            <div class="sel" style="margin-right:0;">
                <div class="select-input">
                    <div th:class="${term != null && term != '' ? 'name ckd' : 'name'}" data-name="请选择学期"
                         th:text="${term != null && term != '' ? term : '请选择学期'}"></div>
                    <i></i>
                    <div class="select-dropdown">
                        <div class="search">
                            <input type="text" placeholder="搜索">
                            <span></span>
                        </div>
                        <ul class="dropdown-lists dropdown-lists-single">
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="sel-item">
            <div class="sel-title">供应商</div>
            <div class="sel" style="margin-right:0;">
                <div class="select-input">
                    <div class="name" data-name="请选择供应商">请选择供应商</div>
                    <i></i>
                    <div class="select-dropdown">
                        <div class="search">
                            <input type="text" placeholder="搜索">
                            <span></span>
                        </div>
                        <ul class="dropdown-lists">
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="sel-item">
            <div class="sel-title">年级</div>
            <div class="sel" style="margin-right:0;">
                <div class="select-input">
                    <div class="name" data-name="请选择年级">请选择年级</div>
                    <i></i>
                    <div class="select-dropdown">
                        <div class="search">
                            <input type="text" placeholder="搜索">
                            <span></span>
                        </div>
                        <ul class="dropdown-lists">
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="sel-item">
            <div class="sel-title">班级</div>
            <div class="sel" style="margin-right:0;">
                <div class="select-input">
                    <div class="name" data-name="请选择班级">请选择班级</div>
                    <i></i>
                    <div class="select-dropdown">
                        <div class="search">
                            <input type="text" placeholder="搜索">
                            <span></span>
                        </div>
                        <div class="all-selects">
                            全选
                        </div>
                        <ul class="dropdown-lists">
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="st-scroll">
        <div class="lab-box">
            <div class="tableDetail print-box" id="printArea">
                <table>
                    <thead>
                    <tr>
                        <th width="16.66%">学年学期</th>
                        <th width="16.66%">年级</th>
                        <th width="16.66%">院系</th>
                        <th width="16.66%">专业</th>
                        <th width="16.66%">班级</th>
                        <th width="16.66%">供应商</th>
                    </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div id="coursePage"></div>
</div>


</body>
<script th:src="${_CPR_+_VR_+'/js/jquery-3.3.1.min.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/print.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/unitChange.js'}"></script>
<script th:src="@{${_CPR_+_VR_}+'/js/material/receipt_form_new.js'(v=${new java.util.Date().getTime()})}"></script>
<script th:src="${_CPR_+_VR_+'/js/my.util.js'}"></script>
<script th:inline="javascript">
    let fid = [[${fid}]];
    let term = [[${term}]];
    let uid = [[${uid}]];
    const _VR_ = [[${_VR_}]] || '';
</script>

</html>