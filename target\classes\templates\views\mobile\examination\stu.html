<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no"/>
    <title>考生名单</title>
    <script th:src="@{~/js/responsive-1.0.js}"></script>
    <link rel="stylesheet" th:href="@{~/css/examination/mobile/iosSelect.css}">
    <link rel="stylesheet" th:href="@{~/css/examination/mobile/common.css}"/>
    <link rel="stylesheet" th:href="@{~/css/examination/mobile/schedule.css}"/>
    <link rel="stylesheet" th:href="@{~/css/examination/mobile/newStyle.css}"/>

</head>

<body>
<!--    <div class="tophead">-->
<!--        <div class="head bottomLine">-->
<!--            <div class="leftHead">-->
<!--                <div class="back"></div>-->
<!--            </div>-->
<!--            <div class="centerHead">-->
<!--                <div class="selectBox">-->
<!--                    <div class="selectWeek"><span class="week" week='1'>考生名单</span></div>-->
<!--                </div>-->
<!--            </div>-->

<!--        </div>-->
<!--    </div>-->
<div class="search">
    <div class="search-text"><img th:src="@{~/images/examination/search-icons.png}" alt="">搜索</div>
    <input type="text" placeholder="请输入">
</div>
<div class="studentCount">
    <p>总人数：<span id="count"></span></p>
    <p>
        <span id="missExam"></span>
        <span id="cheat"></span>
        <span id="warning"></span>
    </p>
</div>
<div class="teacherList">

</div>
<div class="notesList" style="display: none">
    <div class="arrowTop"></div>
    <ul>
        <li>缺考</li>
        <li>作弊</li>
        <li>警告</li>
    </ul>
    <div class="arrowBotton"></div>
</div>
</body>
<script th:src="@{~/js/jquery-3.3.1.min.js}"></script>
<script th:src="@{~/js/iosSelect.js}"></script>
<script type="text/javascript" th:src="@{~/js/CXJSBridge.js}"></script>
<script type="text/javascript" th:src="@{~/js/app.utils.js}"></script>
<script type="text/javascript" th:src="@{~/js/tools.js}"></script>
<script type="text/javascript" th:src="@{~/js/base.js}"></script>
<script type="text/javascript" th:src="@{~/js/my.util.js}"></script>

<script th:inline="javascript">
    var fid = [[${fid}]];
    var uid = [[${uid}]];
    var pkjgbh = [[${pkjgbh}]];
    $(function () {
        //适配iphone头部
        addIphoneHeight();
        getStudent();
    })

    function addIphoneHeight() {
        var isIphone = /iphone/gi.test(navigator.userAgent);
        console.log(isIphone);
        if (isIphone && (screen.height == 812 && screen.width == 375)) {
            $('body').addClass('iosxwrapMax');
        } else if (isIphone && (screen.width == 414)) {
            $('body').addClass('iospluswrapMax');
        } else if (isIphone) {
            $('body').addClass('ioswrapMax');
        }
    }

    function getStudent() {
        $.get("/examination/basic/student", {fid: fid, pkjgbh: pkjgbh}, function (res) {
            if (res.code == 200) {
                let html = '';
                let miss=0;
                let cheat=0;
                let warning=0;
                for (let i = 0; i < res.data.length; i++) {
                    let data = res.data[i];
                    html += '<div class="teacher" data-value="'+data.rowInfo.formUserId+'">'
                    html += '<img src="https://photo.chaoxing.com/photo_80.jpg" alt="">'
                    html += '<div class="teacherMes">'
                    html += '<h3>' + data.pkjgxsmx_xm+ '</h3>'
                    if (data.pkjgxsmx_kszt=="正常"){

                        html += '<span class="normal">正常</span>'
                    }
                    if (data.pkjgxsmx_kszt=="作弊"){
                        cheat+=1;
                        html += '<span class="cheat">作弊</span>'
                    }
                    if (data.pkjgxsmx_kszt=="缺考"){
                        miss+=1;
                        html += '<span class="missExam">缺考</span>'
                    }
                    if (data.pkjgxsmx_kszt=="警告"){
                        warning+=1;
                        html += '<span class="warning">警告</span>'
                    }
                    html += '</div>'
                    if (data.pkjgxsmx_kszt=="正常"){
                        html += '<div class="notes"><span></span>备注</div>'
                        html += '<div class="cancel hide" style="display: none">取消</div>'
                    }else {
                        html += '<div class="notes" style="display: none"><span></span>备注</div>'
                        html += '<div class="cancel hide">取消</div>'
                    }


                    html += '</div>'
                }
                $("#count").html(res.data.length+"人")
                $("#missExam").html("缺考："+miss+"人")
                $("#cheat").html("作弊："+cheat+"人")
                $("#warning").html("警告："+warning+"人")
                $(".teacherList").html(html);
            }else {
                U.fail("当前场次无学生")
            }
        })
    }

    // 头部检索
    $(".search-text").click(function () {
        $(this).hide().next().show().focus();
    })
    $(".search input").blur(function () {
        if ($(this).val() == "") {
            $(this).hide().prev().show();
        }
    })

    $(".search input").change(function (res) {
        $(".teacherList h3").each((idx, value) => {
            if (res.currentTarget.value == undefined || res.currentTarget.value == "") {
                $(value).parents(".teacher").show();
                return;
            }
            var uname = $(value).html();
            if (uname.indexOf(res.currentTarget.value) != -1) {
                $(value).parents(".teacher").show()
            } else {
                $(value).parents(".teacher").hide()
            }
        })
    })


    setTimeout(function () {
        var tabHeight = $(window).height() - $(".teacherList").offset().top;
        $(".teacherList").css({
            'height': tabHeight,
            "overflow-y": "auto"
        })
    }, 300)

    // 备注
    var noteEle = "";
    $(".teacherList").on("click",".notes",function (e) {
        noteEle = $(this).parent().find('.teacherMes').find('span');
        var noteH = $(this).height() + 6;
        var X = $(this).offset().top; //元素在当前视窗距离顶部的位置
        var Y = $(this).offset().left;
        var totalH = $(".notesList").height() + X;
        var winH = $(window).height();
        if (totalH > winH) {
            $(".notesList .arrowBotton").show();
            $(".notesList .arrowTop").hide();
            $(".notesList").show().css({
                'bottom': winH - X + 6
            })
        } else {
            $(".notesList .arrowBotton").hide();
            $(".notesList .arrowTop").show();
            $(".notesList").show().css({
                'top': X + noteH
            })
        }
        e.stopPropagation();
    })
    $(".notesList ul li").click(function () {
        var idx = $(this).index();
        var txt = $(this).text();
        if (txt==noteEle.text()){
            return;
        }
        var formUserId = noteEle.parents(".teacher").attr("data-value");
        $.get("/mobile/examination/updateStatus",{fid:fid,formUserId:formUserId,status:idx},function (res){
            if (res.code==200){
                noteEle.removeClass();
                noteEle.text(txt);
                switch (idx) {
                    case 0:
                        noteEle.addClass('missExam');
                        break;
                    case 1:
                        noteEle.addClass('cheat');
                        break;
                    case 2:
                        noteEle.addClass('warning');
                        break;
                }
            }else {
                U.fail("修改失败",2000)
            }
        })
        $(".notesList").hide();
        var p = noteEle.parents('.teacher');
        p.find('.notes').hide();
        p.find('.cancel').show();
    })
    // 取消
    $(".teacherList").on("click",".cancel",function () {
        var p = $(this).parents('.teacher');
        var formUserId = p.attr("data-value");
        $.get("/mobile/examination/updateStatus",{fid:fid,formUserId:formUserId,status:"-1"},function (res){
            if (res.code==200){
                p.find('.teacherMes').find('span').text('正常').removeClass().addClass("normal");
                p.find('.notes').show();
                p.find('.cancel').hide();
            }
        })
    })
    //
    $(".teacherList").scroll(function () {
        $(".notesList").hide();
    })
</script>

</html>