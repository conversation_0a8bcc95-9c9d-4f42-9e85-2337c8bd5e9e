<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <title>开课信息管理列表</title>
    <meta name="decorator" content="list"/>
    <link rel="stylesheet" th:href="@{../css/cultivation/jquery.searchableSelect.css}"/>
    <link rel="stylesheet" th:href="@{../css/cultivation/newStyle.css}"/>
    <link rel="stylesheet" th:href="@{../plugin/layui/css/layui.css}"/>
</head>
<body>
<div class="popDiv popMove wid840">
    <div class="popBody" id="popScroll">
        <div class="popSearch clearAfter">
            <div class="popSearch_row fl">
                <div class="popSearch_name">
                    <div class="popSearch_table">
                        <div class="popSearch_cell"><span>校区</span></div>
                    </div>
                </div>
                <select id="xq" name='kkxqdm' class="qselect">
                    <option value="">请选择</option>
                    <c:forEach items="${xqList}" var="item">
                        <option value="${item.id}">${item.xqmc}</option>
                    </c:forEach>
                </select>
            </div>
            <div class="popSearch_row fl">
                <div class="popSearch_name">
                    <div class="popSearch_table">
                        <div class="popSearch_cell"><span>年级</span></div>
                    </div>
                </div>
                <select id="nj" name='nj' class="qselect">
                    <option value="">请选择</option>
                    <c:forEach items="${njList}" var="item">
                        <option value="${item.njdm}">${item.njmc}</option>
                    </c:forEach>
                </select>
            </div>
            <div class="popSearch_row fl">
                <div class="popSearch_name">
                    <div class="popSearch_table">
                        <div class="popSearch_cell"><span>院系</span></div>
                    </div>
                </div>
                <select id="kkdwdm" name="skyx" class="qselect">
                    <option value="">请选择</option>
                    <c:forEach items="${yxList}" var="item">
                        <option value="${item.id}">${item.name}</option>
                    </c:forEach>
                </select>
            </div>
            <div class="popSearch_row fl">
                <div class="popSearch_name">
                    <div class="popSearch_table">
                        <div class="popSearch_cell"><span>专业</span></div>
                    </div>
                </div>
                <select id="zydm" name="zyid" class="qselect">
                    <option value="">请选择</option>
                    <c:forEach items="${zyList}" var="item">
                        <option value="${item.zybh}">${item.zymc}</option>
                    </c:forEach>
                </select>
            </div>
            <div class="popSearch_row fl">
                <div class="popSearch_name">
                    <div class="popSearch_table">
                        <div class="popSearch_cell"><span>班级</span></div>
                    </div>
                </div>
                <select name="bjdm" id="bjdm" class="qselect">
                    <option value="">请选择</option>
                    <c:forEach items="${bjList}" var="item">
                        <option value="${item.bjbh}">${item.bjmc}</option>
                    </c:forEach>
                </select>
            </div>
            <div class="popSearch_row fl">
                <div class="popSearch_name">
                    <div class="popSearch_table">
                        <div class="popSearch_cell"><span>学生</span></div>
                    </div>
                </div>
                <div class="popSearch_per"><input id="xs" class="popSearch_input" name="xs"/></div>
            </div>
        </div>
        <div class="popSearch_btns">
            <div class="popSearch_search_btn fl" onclick="setdel();">删除</div>
            <div class="popSearch_search_btn fr" onclick="setxz();">限制</div>
            <div class="popSearch_search_btn fr" lay-submit onclick="setmx();">面向</div>
        </div>

        <div class="popSearch_cont">
            <table lay-filter="mxTable" class="layui-table" id="mxTable">
            </table>
        </div>
    </div>
</div>
<script th:src="@{../js/jquery-3.3.1.min.js}"></script>
<script th:src="@{../js/jquery.nicescroll.min.js}"></script>
<script th:src="@{../js/cultivation/jquery.searchableSelect.js}"></script>
<script th:src="@{../js/cultivation/sweetalert.min.js}"></script>
<script th:src="@{../js/cultivation/select_data.js}"></script>
<script th:src="@{../plugin/layui/layui.js}"></script>
<script>
    document.domain = document.domain.split('.').slice(-2).join('.');

    //弹窗居中
    function MoveFixed(userClass) {
        $(userClass).css({
            top: function () {
                return 0;
            }, left: function () {
                return ($(window).width() - $(this).width()) / 2;
            }
        });

    }

    window.onresize = function () {
        MoveFixed('.popMove')
    }
    MoveFixed('.popMove')
    $('.qselect').click();
    var table = "", insTb = "", rowIndex = "";
    var mxdatas = [], mxdxdata = [];
    layui.use('table', function () {
        table = layui.table;
        insTb = table.render({
            elem: '#mxTable',
            data: mxdatas,
            cols: [
                [
                    {field: 'sz', title: '设置', width: 80},
                    {field: 'detail1', title: '详细信息'}
                ]
            ]
        });
        table.on('row(mxTable)', function (obj) {
            obj.tr.addClass('layui-table-click').siblings().removeClass('layui-table-click');
            rowIndex = $(obj.tr).attr("data-index");
        })
    });

    // 检查班级是否已经被选择
    function setmx() {
        // 面向
        var data = {};
        var detail1 = '';

        // 拼接详情
        var xq = $("#xq").val();
        var nj = $("#nj").val();
        var kkdwdm = $("#kkdwdm").val();
        var zydm = $("#zydm").val();
        var bjdm = $("#bjdm").val();
        var pycc = $("#pycc").val();
        var xs = $("#xs").val();
        data.xq = xq;
        data.pycc = pycc;
        data.nj = nj;
        data.kkdwdm = kkdwdm;
        data.zydm = zydm;
        data.bjdm = bjdm;
        data.xs = xs;
        if ($("#xq").find("option:selected").text() != undefined && $("#xq").find("option:selected").text() != '请选择') {
            detail1 = '校区:' + $("#xq").find("option:selected").text();
        }
        if ($("#pycc").find("option:selected").text() != undefined && $("#pycc").find("option:selected").text() != '请选择') {
            detail1 += ',培养层次:' + $("#pycc").find("option:selected").text();
        }
        if ($("#nj").find("option:selected").text() != undefined && $("#nj").find("option:selected").text() != '请选择') {
            detail1 += ',年级:' + $("#nj").find("option:selected").text();
        }
        if ($("#kkdwdm").find("option:selected").text() != undefined && $("#kkdwdm").find("option:selected").text() != '请选择') {
            detail1 += ',院系:' + $("#kkdwdm").find("option:selected").text();
        }
        if ($("#zydm").find("option:selected").text() != undefined && $("#zydm").find("option:selected").text() != '请选择') {
            detail1 += ',专业:' + $("#zydm").find("option:selected").text();
        }
        if ($("#bjdm").find("option:selected").text() != undefined && $("#bjdm").find("option:selected").text() != '请选择') {
            detail1 += ',班级:' + $("#bjdm").find("option:selected").text();
        }
        if (xs != undefined && xs != '') {
            detail1 += ',学生:' + xs;
        }
        if (detail1 == '') {
            detail1 = '全部';
        }
        if (detail1.startsWith(",")) {
            detail1 = detail1.substring(1, detail1.length)
        }
        data.sz = '面向';
        data.detail = detail1;
        data.type = '1';
        data.detail1 = detail1;
        var flag = true;
        for (var i = 0; i < mxdxdata.length; i++) {
            if (mxdxdata[i].detail1 == data.detail1 && mxdxdata[i].sz == data.sz) {
                flag = false;
                break;
            }
        }

        if (flag) {
            mxdxdata.unshift(data);
            table.reload('mxTable', {
                data: mxdxdata
            });
        }
    }

    function setxz() {
        // 面向
        var data = {};
        var detail1 = '';
        // 拼接详情
        var xq = $("#xq").val();
        var pycc = $("#pycc").val();
        var nj = $("#nj").val();
        var kkdwdm = $("#kkdwdm").val();
        var zydm = $("#zydm").val();
        var bjdm = $("#bjdm").val();
        var xs = $("#xs").val();
        data.xq = xq;
        data.pycc = pycc;
        data.nj = nj;
        data.kkdwdm = kkdwdm;
        data.zydm = zydm;
        data.bjdm = bjdm;
        data.xs = xs;
        if ($("#xq").find("option:selected").text() != undefined && $("#xq").find("option:selected").text() != '请选择') {
            detail1 = '校区:' + $("#xq").find("option:selected").text();
        }
        if ($("#pycc").find("option:selected").text() != undefined && $("#pycc").find("option:selected").text() != '请选择') {
            detail1 += ',培养层次:' + $("#pycc").find("option:selected").text();
        }
        if ($("#nj").find("option:selected").text() != undefined && $("#nj").find("option:selected").text() != '请选择') {
            detail1 += ',年级:' + $("#nj").find("option:selected").text();
        }
        if ($("#kkdwdm").find("option:selected").text() != undefined && $("#kkdwdm").find("option:selected").text() != '请选择') {
            detail1 += ',院系:' + $("#kkdwdm").find("option:selected").text();
        }
        if ($("#zydm").find("option:selected").text() != undefined && $("#zydm").find("option:selected").text() != '请选择') {
            detail1 += ',专业:' + $("#zydm").find("option:selected").text();
        }
        if ($("#bjdm").find("option:selected").text() != undefined && $("#bjdm").find("option:selected").text() != '请选择') {
            detail1 += ',班级:' + $("#bjdm").find("option:selected").text();
        }
        if (xs != undefined && xs != '') {
            detail1 += ',学生:' + xs;
        }
        if (detail1 == '') {
            detail1 = '全部';
        }
        if (detail1.startsWith(",")) {
            detail1 = detail1.substring(1, detail1.length)
        }
        data.sz = '限制';
        data.detail = detail1;
        data.detail1 = detail1;
        data.type = '2';
        var flag = true;
        for (var i = 0; i < mxdxdata.length; i++) {
            if (mxdxdata[i].sz == data.sz && mxdxdata[i].detail1 == data.detail1) {
                flag = false;
                break;
            }
        }
        if (flag) {
            mxdxdata.unshift(data);
            table.reload('mxTable', {
                data: mxdxdata
            });
        }
    }

    function setdel() {
        // 删除选择的记录
        if (undefined === rowIndex || rowIndex.length <= 0) {
            top.layer.alert("您没有选择记录。");
            return false;
        }
        if (mxdxdata.length > 0 && mxdxdata[0].nj == undefined) {
            mxdxdata.splice(mxdxdata[0], 1);
        }

        //获取当前行号
        mxdxdata.splice(rowIndex, 1);
        table.reload('mxTable', {
            data: mxdxdata
        });
    }
</script>
</body>
</html>