<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <title>添加教师</title>
    <link rel="stylesheet" th:href="@{../css/cultivation/jquery.searchableSelect.css}"/>
    <link rel="stylesheet" th:href="@{../css/cultivation/newStyle.css}"/>
    <link rel="stylesheet" th:href="@{../plugin/layui/css/layui.css}"/>
    <style>
        html, body {
            height: 100%;
        }

        .layui-laypage .layui-laypage-curr .layui-laypage-em {
            background-color: #4C88FF !important;
        }

        body .layui-layer-btn .layui-layer-btn0 {
            background: #4C88FF;
            border: 1px solid #4C88FF;
            border-radius: 20px;
            display: inline-block;
            width: 60px;
            height: 34px;
            text-align: center;
            line-height: 34px;
            font-size: 14px;
            color: #FFFFFF;
        }

        body .layui-layer-btn .layui-layer-btn1 {
            background: #FFFFFF;
            border: 1px solid #94C1FF;
            border-radius: 20px;
            display: inline-block;
            width: 60px;
            height: 34px;
            text-align: center;
            line-height: 34px;
            font-size: 14px;
            color: #4C88FF;
        }

        body .layui-layer-btn {
            padding: 32px 15px 12px;
        }
    </style>
</head>
<body>
<div class="popDiv popMove wid840">
    <div class="popBody" id="popScroll">
        <div class="popSearch clearAfter">
            <div class="popSearch_row fl">
                <div class="popSearch_name">
                    <div class="popSearch_table">
                        <div class="popSearch_cell"><span>教师编号</span></div>
                    </div>
                </div>
                <div class="popSearch_per"><input type="text" placeholder="请输入" id="jsbh" name="jsbh"
                                                  class="popSearch_input"></div>
            </div>
            <div class="popSearch_row fl">
                <div class="popSearch_name">
                    <div class="popSearch_table">
                        <div class="popSearch_cell"><span>教师姓名</span></div>
                    </div>
                </div>
                <div class="popSearch_per"><input type="text" placeholder="请输入" id="xm" name="xm"
                                                  class="popSearch_input"></div>
            </div>
            <div class="popSearch_row fl">
                <div class="popSearch_name">
                    <div class="popSearch_table">
                        <div class="popSearch_cell"><span>教师院系</span></div>
                    </div>
                </div>
                <select class="qselect" name="skyx" id="yxbmm">
                    <option value="">请选择</option>
                </select>
            </div>
        </div>
        <div class="popSearch_btns">
            <div class="popSearch_clear fr">清空筛选</div>
            <div class="popSearch_search_btn fr" lay-filter="search" lay-submit>筛选</div>
        </div>

        <div class="popSearch_cont">
            <table lay-filter="teacherTable" class="layui-table" id="teacherTable">
            </table>
        </div>
    </div>
    <div class="layui-layer-btn"><a class="layui-layer-btn0">确定</a><a class="layui-layer-btn1">关闭</a></div>
</div>
<script th:src="@{../js/jquery-3.3.1.min.js}"></script>
<script th:src="@{../js/jquery.nicescroll.min.js}"></script>
<script th:src="@{../js/cultivation/jquery.searchableSelect.js}"></script>
<script th:src="@{../js/cultivation/select_data.js}"></script>
<script th:src="@{../plugin/layui/layui.js}"></script>
<script th:inline="javascript">
    //弹窗居中
    document.domain = "chaoxing.com";
    let queryId = [[${queryId}]];
    let fid = [[${fid}]];
    let uid = [[${uid}]];

    function MoveFixed(userClass) {
        $(userClass).css({
            top: function () {
                return 0;
            }, left: function () {
                return ($(window).width() - $(this).width()) / 2;
            }
        });

    }

    window.onresize = function () {
        MoveFixed('.popMove')
    }
    MoveFixed('.popMove')

    function scrollBox(userID) {
        $(userID).niceScroll({
            cursorborder: "",
            cursorwidth: 8,
            cursorcolor: "#CFD8E6",
            boxzoom: false,
            autohidemode: true
        });
        $(userID).getNiceScroll().resize(); //检测滚动条是否重置大小（当窗口改变大小时）
    }

    scrollBox("#popScroll");
    document.domain = document.domain.split('.').slice(-2).join('.');
    $('.qselect').click();
    var names = "";
    var ids = "";

    function getname() {
        names = "";
        ids = "";
        var rowsData = table.checkStatus('teacherTable').data;
        if (rowsData.length > 6) {
            top.layer.alert('最多选择六名辅讲教师!', {icon: 0, title: '警告'});
            return;
        }
        for (var i = 0; i < rowsData.length; i++) {
            var obj = rowsData[i];
            names += obj.xm + ",";
            ids += obj.id + ",";
        }
        names = names.substring(0, names.length - 1);
        return names;
    }

    function getTeachers() {
        var tableData = [];
        var checkRows = table.checkStatus('teacherTable');
        for (var i = 0; i < checkRows.data.length; i++) {
            var rowData = checkRows.data[i];
            const row = {teacherNo: rowData.uName, teacherName: rowData.name, sfScoreTeacher: false};
            tableData.push(row);
        }
        return tableData;
    }

    $(".popSearch_search_btn").click(function () {
        var jsbh = $("#jsbh").val();
        var xm = $("#xm").val();
        var yxbmm = $("#yxbmm").val();
        var field = {jsjbxx_jsgh: jsbh, jsjbxx_xm: xm, jsjbxx_yx: yxbmm};
        insTb.reload({where: field, page: {curr: 1}});
    })

    $(".popSearch_clear").click(function () {
        location.reload();
    })

    var table = "", insTb = "";
    layui.use('table', function () {
        table = layui.table;
        insTb = table.render({
            elem: '#teacherTable',
            url: '../processData/getTeacherInfoData',
            page: true,
            cols: [
                [
                    {type: 'checkbox', field: 'id', fixed: 'left'},
                    {field: 'jsjbxx_jsgh', title: '工号'},
                    {field: 'jsjbxx_xm', title: '姓名'},
                    {field: 'jsjbxx_pyzc', title: '职称'},
                    {field: 'jsjbxx_sfwp', title: '外聘'}
                ]
            ],
            done: function () {
                scrollBox("#popScroll");
            }
        });
        //监听表格复选框选择
        table.on('checkbox(teacherTable)', function (obj) {
            //全选时
            if (obj.type == "all") {
                if (obj.checked) {//全选中';l
                    var checkStatus = table.checkStatus('teacherTable');
                    var sdata = checkStatus.data;
                    $(".opt_data_num span em").text(sdata.length);
                    if (sdata.length > 0) {//渲染背景颜色
                        $("#delBtn").removeClass("noClick");
                        $(".layui-table-body .layui-table tr").each(function () {
                            $(this).addClass("tr_bj_color");
                        })
                    }
                } else {//全部不选
                    $(".opt_data_num span em").text(0);
                    $("#delBtn").addClass("noClick");
                    $(".layui-table-body .layui-table tr").each(function () {
                        $(this).removeClass("tr_bj_color");
                    })
                }
            } else {//单选
                var checkStatus = table.checkStatus('teacherTable');
                var sdata = checkStatus.data;
                $(".opt_data_num span em").text(sdata.length);
                if (obj.checked) {//选中
                    $("#delBtn").removeClass("noClick");
                    obj.tr.addClass('tr_bj_color');
                } else {//取消选中
                    $("#delBtn").addClass("noClick");
                    obj.tr.removeClass('tr_bj_color');
                }
            }
        });
    });

    $(".layui-layer-btn1").click(function () {
        window.parent.postMessage(JSON.stringify({action: 1}), "*");
    })

    $(".layui-layer-btn0").click(function () {
        let teacherArray = getTeachers();
        if (teacherArray.length === 0) {
            layer.msg("请先选择教师", {icon: 2, time: 2000});
            return false;
        }
        $.post("../processData/appointTeacher",
            {
                teacherArray: JSON.stringify(teacherArray),
                queryId: queryId,
                fid: fid,
                uid: uid
            }, function (result) {
                if (!result.success) {
                    layer.msg(result.message, {icon: 2, time: 2000});
                } else {
                    layer.msg("操作成功", {icon: 1, time: 2000});
                    setTimeout(function () {
                        window.parent.postMessage(JSON.stringify({action: 1}), "*");
                    }, 2000)
                }
            }, "json");
    });
</script>
</body>
</html>
