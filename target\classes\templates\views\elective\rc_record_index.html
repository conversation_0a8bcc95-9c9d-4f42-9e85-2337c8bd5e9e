<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
        content="width=device-width, user-scalable=no,initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <title>微点名</title>
    <script src="js/responsive.js"></script>
    <link rel="stylesheet" href="css/global.css">
    <link rel="stylesheet" href="css/iosSelect.css">
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/slide.css">
    <link rel="stylesheet" href="css/sta.css?v=1">
</head>
<body>
<!--    <div class="sta-nav">-->
<!--        <ul>-->
<!--            <li class="active">按行政班统计</li>-->
<!--            <li>按教学班统计</li>-->
<!--        </ul>-->
<!--    </div>-->
    <div class="sta-box-wrap">
<!--        <div class="sta-box">-->
<!--            <div class="search-wrap">-->
<!--                <div class="search flex">-->
<!--                    <img src="images/search.png" alt="">-->
<!--                    <input type="text" placeholder="搜索班级" id="nameIp" onkeyup="getList();">-->
<!--                </div>-->
<!--            </div>-->
<!--            <div class="con-select">-->
<!--                <ul>-->
<!--                    <li data-cla="major" id="selZy"><span onchange="getList();">专业</span> <i></i></li>-->
<!--                    <li data-cla="date" class="selDate" id="selDate"><span>日期</span> <i></i></li>-->
<!--                </ul>-->
<!--            </div>-->
<!--            <div class="class-list">-->
<!--            </div>-->
<!--        </div>-->
        <div class="sta-box">
            <div class="search-wrap">
                <div class="search flex">
                    <img src="images/search.png" alt="">
                    <input type="text" placeholder="搜索教学班" id="nameIp2"  onkeyup="getjxbList();">
                </div>
            </div>
            <div class="con-select">
                <ul>
                    <li data-cla="course" id="selKc"><span>课程</span> <i></i></li>
                    <li data-cla="date" class="selDate" id="selDate2"><span>日期</span> <i></i></li>
<!--                    <li data-cla="jxbzclx" id="jxbzclx"><span>教学班组成类型</span> <i></i></li>-->
                </ul>
            </div>
            <div class="teach-class-list">
            </div>
        </div>
    </div>
    <div class="marker" style="display: none;"></div>
    <div class="slide-sel course-sel" data-cla="course" style="top: 1.90rem">
        <div class="course-list">
            <ul id="kcBox">
            </ul>
        </div>
    </div>
<!--    <div class="slide-sel course-sel" data-cla="jxbzclx">-->
<!--        <div class="course-list">-->
<!--            <ul id="jxbzclxBox">-->
<!--                <li class="active"><h1>全部</h1></li>-->
<!--                <li class=""><h1>纯行政班</h1></li>-->
<!--                <li class=""><h1>行政班合班</h1></li>-->
<!--                <li class=""><h1>其他（拆班，拆合班，选修课）</h1></li>-->
<!--            </ul>-->
<!--        </div>-->
<!--    </div>-->
</body>
<script th:inline="javascript">
	var fid = [[${fid}]];
</script>
<script src="js/CXJSBridge.js"></script>
<script src="js/zepto.min.js"></script>
<script src="js/iosSelect.js"></script>
<script src="js/export.js?v=1.1"></script>
<script th:inline="javascript">
    var rollcallStateList = [[${rollcallStateList}]];
    function _jsBridgeReady() {
        jsBridge.postNotification('CLIENT_WEB_BOUNCES', {"forbiddenFlag":1});
        // jsBridge.postNotification('CLIENT_CUSTOM_MENU', { show: 1, menu: "导出", option: 'exportData()' });
//         $(".class-list").on('click', "a", function () {
//             // 以实际地址为准，此地址为本地测试
//             var webUrl = "http://" + document.domain + ":5500/教师端-行政班详情.html";
//             var title = $(this).find('.cla-name').text();
//             jsBridge.postNotification('CLIENT_OPEN_URL', { "title": title, "toolbarType": 2, "webUrl": webUrl, "loadType": 1 });
//             return false;
//         });
//         $(".teach-class-list").on('click', "a", function () {
//             // 以实际地址为准，此地址为本地测试
//             var webUrl = "http://" + document.domain + ":5500/教师端-教学班详情.html";
//             var title = $(this).find('.class-name').find('h1').text();
//             jsBridge.postNotification('CLIENT_OPEN_URL', { "title": title, "toolbarType": 2, "webUrl": webUrl, "loadType": 1 });
//             return false;
//         });
    }
    $(document).ready(function () {
        // 列表高度
        var boxH = $(window).height() - $(".teach-class-list").offset().top;
        $(".teach-class-list").height(boxH);
        // tab切换
        $(".sta-nav ul li").click(function () {
            $(this).addClass("active").siblings().removeClass("active");
            var idx = $(this).index();
            if(idx=='0'){
            	// getList();
            }
            if(idx=='0'){
            
            }
            $(".sta-box-wrap .sta-box").eq(idx).show().siblings().hide();
        })
    })
    getKc();
    function getKc(){
    	$.ajax({
        	type: "POST",
        	url: "/elective/rollcall/getRollcallLogKC",
        	dataType:'json',
        	success: function (data) {
        		var html = "";
        		html += "<li class=\"active\">";
                html += "<h1>全部</h1>";
                html += "</li>";
        		for(var i = 0;i<data.kcs.length;i++){
                	html += "<li>";
                    html += "<h1>"+data.kcs[i].course+"</h1>";
                    html += "</li>";
        		}
        		$("#kcBox").html(html);
       		}
		});
    }
    getjxbList();
    function getjxbList(){
    	var name = $("#nameIp2").val();
    	var classDate = $("#selDate2").find("span").text();
    	// var jxbzclx = $("#jxbzclx").find("span").text();
    	var kc = $("#selKc").find("span").text();
    	$.ajax({
        	type: "POST",
        	url: "/elective/rollcall/getRollcallLogJXB",
        	data:{name:name,courseTime:classDate,kc:kc,jxbzclx:""},
        	dataType:'json',
        	success: function (data) {
        		var html = "";
        		for(var i = 0;i<data.list.length;i++){
                	html += "<a href=\"javascript:jxbIndex('"+data.list[i].teachingClassCode+"','"+data.courseTime+"','"+data.list[i].teachingClassName+"','"+data.list[i].kj+"');\">";
                    html += "<div class=\"class-name\">";
                    html += "<h1>"+data.list[i].teachingClassName+"</h1>";
                    html += "<span></span>";
                    html += "</div>";
                    html += "<div class=\"class-course\">";
                    html += "<span>第"+data.list[i].zc+"周</span>";
                    html += "<span>周"+data.list[i].xq+"</span>";
                    html += "<span>第"+data.list[i].kj+"节</span>";
                    html += "</div>";
                    html += "<ul class=\"class-state\">";
                    html += "<li>正常：<i>"+data.list[i].zcNum+"</i></li>";
                    for (let j = 0; j < rollcallStateList.length; j++) {
                        html += "<li>"+rollcallStateList[j].stateNamePc+"：<i>"+data.list[i].stateNum[j]+"</i></li>";
                    }
                    html += "</ul></a>";
        		}
        		$(".teach-class-list").html(html);
       		}
		});
    }
    // function getJxbNum(str,code,kj){
    // 	var classDate = $("#selDate2").find("span").text();
    // 	var num = "";
    // 	$.ajax({
    //     	type: "POST",
    //     	url: "/elective/rollcall/getJxbNum",
    //     	dataType:'json',
    //     	data:{str:str,jxbcode:code,courseTime:classDate,kj:kj},
    //     	async:false,
    //     	success: function (data) {
    //     		num = data.num;
    //    		}
	// 	});
	// 	return num;
    // }
    function jxbIndex(jxb,courseTime,jxbmc,kj){
    	var webUrl = window.location.origin+"/elective/rollcall/jxbIndex?jxb="+jxb+"&courseTime="+courseTime+"&jxbmc="+jxbmc+"&kj="+kj;
        jsBridge.postNotification('CLIENT_OPEN_URL', { "toolbarType": 2, "webUrl": webUrl, "loadType": 1 });
    }

    //    下拉选择
    $(".con-select").on('click', 'ul li', function () {
        let dataAttr = $(this).attr('data-cla');
        $(this).toggleClass("active").siblings().removeClass("active");
        let isHasActive = $(this).hasClass("active");
        if (isHasActive) {
            $(".marker").show()
            $("." + dataAttr + '-sel').show();
            $(".slide-sel:not(." + dataAttr + "-sel)").hide();
        } else {
            $(".marker").hide()
            $("." + dataAttr + '-sel').hide();
        }
    });


    // 选择
    $(".slide-sel ").on('click', 'ul li', function () {
//$(".slide-sel ul li").click(function () {
        $(this).addClass("active").siblings().removeClass("active");
        $(this).parents(".slide-sel").hide();
        $(".marker").hide();
        var dataCla = $(this).parents(".slide-sel").attr("data-cla");
        var text = $(this).find('h1').text();
        var liEle = $(".con-select ul li[data-cla=" + dataCla + "]");
        liEle.find('span').text(text);
        liEle.removeClass("active");
        getjxbList()
    });


    //点击遮罩
    $(".marker").click(function () {
        $(".marker,.slide-sel").hide();
        $(".con-select ul li").removeClass("active");
    })

    var showDateDom = $('.selDate');
    // 初始化时间
    var now = new Date();
    var nowYear = now.getFullYear();
    var nowMonth = now.getMonth() + 1;
    var nowDate = now.getDate();
    showDateDom.attr('data-year', nowYear);
    showDateDom.attr('data-month', nowMonth);
    showDateDom.attr('data-date', nowDate);

    // 数据初始化
    function formatYear(nowYear) {
        var arr = [];
        for (var i = nowYear - 5; i <= nowYear + 5; i++) {
            arr.push({
                id: i + '',
                value: i + '年'
            });
        }
        return arr;
    }

    function formatMonth() {
        var arr = [];
        for (var i = 1; i <= 12; i++) {
            arr.push({
                id: i + '',
                value: i + '月'
            });
        }
        return arr;
    }

    function formatDate(count) {
        var arr = [];
        for (var i = 1; i <= count; i++) {
            arr.push({
                id: i + '',
                value: i + '日'
            });
        }
        return arr;
    }

    var yearData = function (callback) {

        callback(formatYear(nowYear))
    };
    var monthData = function (year, callback) {
        callback(formatMonth());
    };
    var dateData = function (year, month, callback) {
        if (/^(1|3|5|7|8|10|12)$/.test(month)) {
            callback(formatDate(31));
        } else if (/^(4|6|9|11)$/.test(month)) {
            callback(formatDate(30));
        } else if (/^2$/.test(month)) {
            if (year % 4 === 0 && year % 100 !== 0 || year % 400 === 0) {
                callback(formatDate(29));
            } else {
                callback(formatDate(28));
            }
        } else {
            throw new Error('month is illegal');
        }

    };
    showDateDom.each(function () {
        $(this).bind('click', function () {
            var oneLevelId = $(this).attr('data-year');
            var twoLevelId = $(this).attr('data-month');
            var threeLevelId = $(this).attr('data-date');
            var _this = $(this);
            var iosSelect = new IosSelect(3,
                [yearData, monthData, dateData],
                {
                    title: '选择时间',
                    itemHeight: 35,
                    oneLevelId: oneLevelId,
                    twoLevelId: twoLevelId,
                    threeLevelId: threeLevelId,
                    showLoading: true,
                    callback: function (selectOneObj, selectTwoObj, selectThreeObj) {
                        _this.attr('data-year', selectOneObj.id);
                        _this.attr('data-month', selectTwoObj.id);
                        _this.attr('data-date', selectThreeObj.id);
                        _this.find('span').text(selectOneObj.value + selectTwoObj.value + selectThreeObj.value);
                        getjxbList();
                        var liEle = $(".con-select ul li[data-cla=date]");
                        liEle.removeClass("active");
                    }
                });
        });
    })


</script>
</html>