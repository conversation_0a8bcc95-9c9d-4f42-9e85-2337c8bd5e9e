<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>成绩管理</title>
    <link rel="stylesheet" href="/score/css/global.css">
    <link rel="stylesheet" href="/score/layui/css/layui.css">
    <link rel="stylesheet" href="/score/css/common.css">
    <link rel="stylesheet" href="/score/css/reset.css">
    <link rel="stylesheet" href="/score/css/indexRule.css">

</head>

<body>

<div class="main">
    <div class="content calculRuleSet">
        <form id="savegrdcalcuRuleForm">
            <input type="hidden" id="grdotherRule_id" name="id" th:value="${otherruleBase?.id}">
            <input type="hidden" id="grdotherRule_fid" name="fid" th:value="${fid}">
            <input type="hidden" id="grdotherRule_xnxq" name="xnxq" th:value="${xnxq}">
            <input type="hidden" id="grdotherRule_createUid" name="createUid" th:value="${uid}">
        </form>
        <div class="c-top">
            <h2>特殊成绩标识设置</h2>
        </div>

        <div class="add-lab cjadd-lab">
            <span>添加</span>
        </div>
        <div class="tabWrap">
            <table class="layui-table" id="main-table1" lay-filter="dataTable1" lay-data='{page: true}'>
            </table>
        </div>
        <div class="c-top" style="margin-top:20px;">
            <h2>成绩分析设置</h2>
        </div>

        <div class="flex-area">
            <div class="items">
                <h4>质量分析规则设置</h4>
                <div class="add-lab add-lab1">
                    <span>添加</span>
                </div>
                <div class="table">
                    <table class="layui-table" id="main-table2" lay-filter="dataTable2">
                    </table>
                </div>
                <div class="form-box" style="margin-top:20px;">
                    <form class="layui-form">
                        <div class="layui-form-item">
                            <label class="layui-form-label">是否允许教师自定义质量分析规则：</label>
                            <div class="layui-input-block ">
                                <div class="limit-switch">
                                    <input type="checkbox" name="switchTeacherFilter1" th:checked="${otherruleBase?.teacherRuleType == 1}" lay-skin="switch"
                                           lay-filter="switchTeacherFilter1">
                                    <span th:text="${otherruleBase?.teacherRuleType == 1} ? '是':'否'">否</span>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="items">
                <h4>质量分析标题设置</h4>
                <div class="add-lab add-lab2">
                    <span>添加</span>
                </div>
                <div class="table">
                    <table class="layui-table" id="main-table3" lay-filter="dataTable3">
                    </table>
                </div>
                <div class="form-box" style="margin-top:20px;">
                    <form class="layui-form">
                        <div class="layui-form-item">
                            <label class="layui-form-label">是否允许教师自定义质量分析标题：</label>
                            <div class="layui-input-block ">
                                <div class="limit-switch">
                                    <input type="checkbox" name="switchTeacherFilter2" th:checked="${otherruleBase?.teacherTitleType == 1}" lay-skin="switch"
                                           lay-filter="switchTeacherFilter2">
                                    <span th:text="${otherruleBase?.teacherTitleType == 1} ? '是':'否'">否</span>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="c-top" style="margin-top:20px;">
            <h2>成绩查询设置</h2>
        </div>
        <form class="layui-form">
            <div class="layui-form-item">
                <label class="layui-form-label">若成绩不合格，则最终成绩显示为：</label>
                <div class="layui-input-block">
                    <input type="radio" name="gradeShow" value="1" title="最后一次成绩" th:checked="${otherruleBase?.gradeShow == 1}" >
                    <input type="radio" name="gradeShow" value="2" title="正考成绩" th:checked="${otherruleBase?.gradeShow == 2}">
                    <input type="radio" name="gradeShow" value="3" title="所有类型成绩中的最高分" th:checked="${otherruleBase?.gradeShow == 3}">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">学生端成绩查询显示内容：</label>
                <div class="layui-input-block w240">
                        <span class="edit studentEdit"
                              style="display: inline-block; color:rgba(89, 144, 255, 1);line-height: 34px; cursor: pointer;">设置</span>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">教师端成绩查询显示内容：</label>
                <div class="layui-input-block">
                        <span class="edit teacherEdit"
                              style="display: inline-block; color:rgba(89, 144, 255, 1);line-height: 34px; cursor: pointer;">设置</span>
                </div>
            </div>
        </form>


        <div class="save-settings">
            <span>保存设置</span>
        </div>

    </div>
</div>

<!-- 设置 -->
<div id="contentDisplay" class="addPoups popup">
    <div class="title">
        <div class="name">查询内容显示</div>
        <div class="close"></div>
    </div>
    <div class="popup-con">
        <div class="queryContent">
            <ul>
                <li class="all-select" id="cxnr">
                    <div class="name">查询内容</div>
                    <div class="checkbox-list">
                        <span class="checkAll" type="checkAll">全选</span>
                    </div>
                </li>
                <li id="fxcj">
                    <div class="name">分项成绩</div>
                    <div class="checkbox-list">
                        <span type="ps">平时</span>
                        <span type="qz">期中</span>
                        <span type="qm">期末</span>
                    </div>
                </li>
                <li id="glxcj">
                    <div class="name">各类型成绩</div>
                    <div class="checkbox-list">
                        <span type="zk">正考</span>
                        <span type="bk">补考</span>
                        <span type="cx">重修</span>
                        <span type="qk">清考</span>
                    </div>
                </li>
                <li id="cjdj">
                    <div class="name">成绩等级</div>
                    <div class="checkbox-list">
                        <span type="cjdj"></span>
                    </div>
                </li>
                <li id="kcxf">
                    <div class="name">课程学分</div>
                    <div class="checkbox-list">
                        <span type="kcxf"></span>
                    </div>
                </li>
                <li id="kcjd">
                    <div class="name">课程绩点</div>
                    <div class="checkbox-list">
                        <span type="kcjd"></span>
                    </div>
                </li>
            </ul>
        </div>
    </div>
    <div class="bottom">
        <button type="reset" class="layui-btn layui-btn-primary exam-cancle">取消</button>
        <button class="layui-btn exam-sure" id="surecontentDisplay">确定</button>
    </div>

</div>

<div id="scontentDisplay" class="addPoups popup">
    <div class="title">
        <div class="name">查询内容显示</div>
        <div class="close"></div>
    </div>
    <div class="popup-con">
        <div class="queryContent">
            <ul>
                <li class="all-select" id="scxnr">
                    <div class="name">查询内容</div>
                    <div class="checkbox-list">
                        <span class="checkAll" type="checkAll">全选</span>
                    </div>
                </li>
                <li id="sfxcj">
                    <div class="name">分项成绩</div>
                    <div class="checkbox-list">
                        <span type="ps">平时</span>
                        <span type="qz">期中</span>
                        <span type="qm">期末</span>
                    </div>
                </li>
                <li id="sglxcj">
                    <div class="name">各类型成绩</div>
                    <div class="checkbox-list">
                        <span type="zk">正考</span>
                        <span type="bk">补考</span>
                        <span type="cx">重修</span>
                        <span type="qk">清考</span>
                    </div>
                </li>
                <li id="scjdj">
                    <div class="name">成绩等级</div>
                    <div class="checkbox-list">
                        <span type="cjdj"></span>
                    </div>
                </li>
                <li id="skcxf">
                    <div class="name">课程学分</div>
                    <div class="checkbox-list">
                        <span type="kcxf"></span>
                    </div>
                </li>
                <li id="skcjd">
                    <div class="name">课程绩点</div>
                    <div class="checkbox-list">
                        <span type="kcjd"></span>
                    </div>
                </li>
            </ul>
        </div>
    </div>
    <div class="bottom">
        <button type="reset" class="layui-btn layui-btn-primary exam-cancle">取消</button>
        <button class="layui-btn exam-sure" id="surescontentDisplay">确定</button>
    </div>

</div>

<!-- 添加 -->
<div id="addInput" class="addPoups popup">
    <div class="title">
        <div class="name">质量分析标题</div>
        <div class="close"></div>
    </div>
    <div class="popup-con">
        <div class="form-box">
            <form class="layui-form">
                <input type="hidden" id="qualityAnalysisTitleId" value="">
                <div class="layui-form-item">
                    <label class="layui-form-label" style="width: 100px;"><em>*</em> 标题名称：</label>
                    <div class="layui-input-block w240">
                        <input type="text" name="qualityAnalysisTitle" id="qualityAnalysisTitle" placeholder="输入内容" autocomplete="off"
                               class="layui-input">
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="bottom">
        <button type="reset" class="layui-btn layui-btn-primary exam-cancle">取消</button>
        <button class="layui-btn exam-sure" id="addQualityAnalysisTitle">确定</button>
    </div>

</div>

<!-- 添加 -->
<div id="addInputs" class="addPoups popup" style="width: 640px;">
    <div class="title">
        <div class="name">添加</div>
        <div class="close"></div>
    </div>
    <div class="popup-con">
        <div class="form-box">
            <form class="layui-form" lay-filter="addeditForm">
                <input type="hidden" id="specialresultid" value="">
                <div class="layui-form-item">
                    <label class="layui-form-label" style="width: 134px;"><em
                            style="visibility: hidden;">*</em>特殊成绩代码：</label>
                    <div class="layui-input-block w240">
                        <input type="text" lay-filter="templateName1" id="templateName1" name="templateName1" placeholder="FX001"
                               autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="width: 134px;"><em>*</em>特殊成绩名称：</label>
                    <div class="layui-input-block w240">
                        <input type="text" lay-filter="templateName2" id="templateName2" name="templateName2" placeholder="输入内容"
                               autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="width: 134px;"><em>*</em>对应分数：</label>
                    <div class="layui-input-block w240">
                        <input type="text" lay-filter="templateName3" id="templateName3" name="templateName3" placeholder="输入内容"
                               autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="width: 134px;"><em>*</em>是否启用：</label>
                    <div class="layui-input-block w240">
                        <div class="limit-switch">
                            <input type="checkbox" id="enableFlag" name="switchTeacherFilter3" lay-skin="switch"
                                   lay-filter="switchTeacherFilter3">
                            <span>否</span>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="bottom">
        <button type="reset" class="layui-btn layui-btn-primary exam-cancle">取消</button>
        <button class="layui-btn exam-sure" id="add-special-flag">确定</button>
    </div>

</div>


<!-- 添加 -->
<div id="achievementAddInput" class="addPoups popup">
    <input type="hidden" id="otherruleResultanlyRuleId" value="">
    <div class="title">
        <div class="name">质量分析标题</div>
        <div class="close"></div>
    </div>
    <div class="popup-con">
        <div class="p-boxs">
            <div class="b-top">
                <em>*</em>
                <div class="name">成绩分项：</div>
                <span class="introIcon">
                        <div class="bubble">
                            <span>请勾选要配置质量分析规则的成绩分项</span>
                        </div>
                    </span>
            </div>
            <div class="check-box">
                <ul id="itemList">
                    <li th:each="info : ${itemDictionaryList}" th:attr="value=${info.id},name=${info.itemName}"
                            th:text="${info.itemName}"></li>
                </ul>
            </div>
            <div class="b-top">
                <em>*</em>
                <div class="name">统计内容：</div>
            </div>
            <div class="check-box">
                <ul id="statistics-content">
                    <li value="1">成绩统计</li>
                    <li value="2">成绩分布频数</li>
                    <li value="3">成绩分布直方图</li>
                </ul>
            </div>

            <div class="b-top level-interval-content" style="display: none">
                <div class="name">成绩等级百分率划分</div>
            </div>
            <div class="level-interval level-interval-content" id="level-interval-content" style="display: none">
                <div class="name">等级区间：</div>
                <div class="li-box" >
                    <div class="lab">
                        <span id="add-grade-level-html">添加成绩等级</span>
                    </div>
                </div>
            </div>
            <div class="b-top statistics-interval-content" style="display: none">
                <div class="name">统计区间设置</div>
            </div>
            <div class="level-interval statistics-interval-content" id="statistics-interval-content" style="display: none">
                <div class="name">分数段：</div>
                <div class="li-box">
                    <div class="lab">
                        <span id="add-statistics-interval-html">添加统计区间</span>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <div class="bottom">
        <button type="reset" class="layui-btn layui-btn-primary exam-cancle">取消</button>
        <button class="layui-btn exam-sure" id="add-otherruleResultanlyRule">确定</button>
    </div>
</div>

<script type="text/html" id="barDemo1">
    <a style="color:#3A8BFF;;cursor:pointer;margin-right:8px;" lay-event="edit">编辑</a>
    <a style="color:#F33131;cursor:pointer;" lay-event="del">删除</a>
</script>
<script type="text/html" id="barDemo4">
    <a style="color:#3A8BFF;;cursor:pointer;margin-right:8px;" lay-event="edit">编辑</a>
    <a style="color:#F33131;cursor:pointer;" lay-event="del">删除</a>
</script>
<script src="/js/jquery-3.3.1.min.js"></script>
<script src="/js/jquery.mCustomScrollbar.concat.min.js"></script>
<script src="/score/layui/layui.js"></script>
<script src="/score/js/common.js"></script>
<script type="text/javascript" th:src="@{~/js/my.util.js}"></script>
<script th:inline="javascript">
    var studentShowContent = [[${otherruleBase?.studentShowContent}]];
    var teacherShowContent = [[${otherruleBase?.teacherShowContent}]];
    if(!U.isEmpty(studentShowContent)){
        var content = JSON.parse(studentShowContent);
        for(var i in content){
            var obj = content[i];
            for(var j in obj){
                var json = obj[j];
                for(var k in json){
                    if(json[k] == "1"){
                        $("#" + j).find(".checkbox-list").find("[type='"+k+"']").addClass("cur");
                    } else {
                        $("#" + j).find(".checkbox-list").find("[type='"+k+"']").removeClass("cur");
                    }
                }
            }
        }
    }
    if(!U.isEmpty(teacherShowContent)){
        var content = JSON.parse(teacherShowContent);
        for(var i in content){
            var obj = content[i];
            for(var j in obj){
                var json = obj[j];
                for(var k in json){
                    if(json[k] == "1"){
                        $("#" + j).find(".checkbox-list").find("[type='"+k+"']").addClass("cur");
                    } else {
                        $("#" + j).find(".checkbox-list").find("[type='"+k+"']").removeClass("cur");
                    }
                }
            }
        }
    }

    var addInputsindex;
    var addInputindex;
    var contentDisplayindex;
    var scontentDisplayindex
    $(function () {
        var form, table, laydate, layer;
        layui.use(['form', 'table', 'laydate'],
            function () {
                var $ = layui.jquery;
                form = layui.form;
                table = layui.table;
                laydate = layui.laydate;
                layer = layui.layer;

                form.on('switch(switchTeacherFilter1)', function (data) {
                    if (data.elem.checked) {
                        $(this).parent().find("span").text('是');
                    } else {
                        $(this).parent().find("span").text('否');
                    }
                });

                form.on('switch(switchTeacherFilter2)', function (data) {
                    if (data.elem.checked) {
                        $(this).parent().find("span").text('是');
                    } else {
                        $(this).parent().find("span").text('否');
                    }
                });

                form.on('switch(switchTeacherFilter3)', function (data) {
                    if (data.elem.checked) {
                        $(this).parent().find("span").text('是');
                    } else {
                        $(this).parent().find("span").text('否');
                    }
                });

                var table1 = table.render({
                    elem: "#main-table1",
                    id: 'main-table1',
                    url: '/api/score/otherRule/getOtherruleSpecialresltList',
                    page: {
                        limit: 10,
                        limits: [10, 20, 30, 40, 50],
                        layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                    },
                    where: {
                        'fid': $("#grdotherRule_fid").val(),
                        "xnxq": $("#grdotherRule_xnxq").val()
                    },
                    request: {
                        pageName: "curPage",
                        limitName: "pageSize"
                    },
                    parseData: function (res) {
                        return {
                            "count": res.data.count,
                            "data": res.data.dataList,
                            "code": 0
                        }
                    },
                    cols: [
                        [{
                            field: "specialResultCode",
                            align: "center",
                            title: "特殊成绩代码",
                            minWidth: 120,
                        },
                            {
                                field: "specialResultName",
                                align: "center",
                                title: "特殊成绩名称",
                                minWidth: 120
                            },
                            {
                                field: "specialResultScore",
                                align: "center",
                                title: "对应分数",
                                minWidth: 120
                            },
                            {
                                field: "enableFlag",
                                align: "center",
                                title: "是否启用",
                                minWidth: 120,
                                templet: function (d) {
                                    if (d.enableFlag == "1") {
                                        return '<span class="wstatus correct">是</span>';
                                    } else {
                                        return '<span class="wstatus deny">否</span>';
                                    }
                                }
                            },
                            {
                                field: "options",
                                align: "center",
                                title: "操作",
                                toolbar: "#barDemo1",
                                minWidth: 120
                            },
                        ]
                    ],
                    done: function (res) {}
                })

                /* 监听工具条 */
                table.on("tool(dataTable1)", function (obj) {
                    var data = obj.data; //获得当前行数据
                    if (obj.event === "del") {
                        layer.confirm('是否删除？', function (index) {
                            $.ajax({
                                type: "POST",
                                url: "/api/score/otherRule/deleteOtherruleSpecialreslt",
                                data: {id: data.id},
                                dataType: 'json',
                                success: function (result) {
                                    if(result.code == 200){
                                        layer.msg("删除成功");
                                        layui.table.reload('main-table1', {
                                            where: {
                                                'fid': $("#grdotherRule_fid").val(),
                                                "xnxq": $("#grdotherRule_xnxq").val()
                                            },
                                        }, 'data');
                                    } else {
                                        layer.msg(result.msg);
                                    }
                                }
                            });
                        });
                    } else if (obj.event === "edit") {
                        // layer.msg("编辑")
                        $("#addInputs .title .name").html("编辑");
                        form.val("addeditForm", {
                            "templateName1": data.specialResultCode,
                            "templateName2": data.specialResultName,
                            "templateName3": data.specialResultScore,
                            "switchTeacherFilter3": data.enableFlag
                        });
                        $("#addInputs .layui-form .layui-form-item .limit-switch span").text("是");
                        addInputsindex = layer.open({
                            type: 1,
                            title: false,
                            closeBtn: false,
                            shadeClose: true,
                            isOutAnim: true,
                            content: $('#addInputs'),
                            area: ['auto', 'auto'],
                            success: function () {
                                $("#specialresultid").val(data.id);
                            },
                        }, function () {});
                    }
                })

                table.on("tool(dataTable2)", function (obj) {
                    var data = obj.data; //获得当前行数据
                    if (obj.event === "del") {
                        layer.confirm('是否删除？', function (index) {
                            $.ajax({
                                type: "POST",
                                url: "/api/score/otherRule/deleteOtherruleResultanlyRule",
                                data: {id: data.id},
                                dataType: 'json',
                                success: function (result) {
                                    if(result.code == 200){
                                        layer.msg("删除成功");
                                        layui.table.reload('main-table2', {
                                            where: {
                                                'fid': $("#grdotherRule_fid").val(),
                                                "xnxq": $("#grdotherRule_xnxq").val()
                                            },
                                        }, 'data');
                                    } else {
                                        layer.msg(result.msg);
                                    }
                                }
                            });
                        });
                    } else if (obj.event === "edit") {
                        achievementAddInputindex = layer.open({
                            type: 1,
                            title: false,
                            closeBtn: false,
                            shadeClose: true,
                            isOutAnim: true,
                            content: $('#achievementAddInput'),
                            offset:'15%',
                            area: ['auto', 'auto'],
                            success: function () {
                                $("#otherruleResultanlyRuleId").val("");
                                $("#itemList li").removeClass("cur");
                                $("#statistics-content li").removeClass("cur");
                                $("#level-interval-content .li-box .grade-level-html").remove();
                                $("#statistics-interval-content .li-box .statistics-interval-html").remove();

                                $("#otherruleResultanlyRuleId").val(data.id);
                                var itemid = data.itemid.toString();
                                var statisticsType = data.statisticsType.toString();
                                if(!U.isEmpty(itemid)){
                                    var itemidArr = itemid.split(",");
                                    $("#itemList li").each(function(){
                                        if(contains(itemidArr, $(this).attr("value"))){
                                            $(this).addClass("cur");
                                        }
                                    })
                                }
                                if(!U.isEmpty(statisticsType)){
                                    var statisticsTypeArr = statisticsType.split(",");
                                    $("#statistics-content li").each(function(){
                                        if(contains(statisticsTypeArr, $(this).attr("value"))){
                                            $(this).addClass("cur");
                                        }
                                    })
                                    if(statisticsType.indexOf("2") != -1 || statisticsType.indexOf("3") != -1){
                                        $(".statistics-interval-content").show();
                                    }
                                    if(statisticsType.indexOf("1") != -1){
                                        $(".level-interval-content").show();
                                    }
                                }
                                if(!U.isEmpty(data.levelInterval)){
                                    var levelInterval = JSON.parse(data.levelInterval);
                                    for(var i in levelInterval){
                                        var obj = levelInterval[i];
                                        var html = "<div class=\"lab grade-level-html\">\n" +
                                            "                                <input value=\""+obj['title']+"\" class=\"level\" placeholder=\"例:优分率\">\n" +
                                            "                                    <input value=\""+obj['start']+"\" type=\"text\" name=\"addpNames\" value=\"\" autocomplete=\"off\" class=\"layui-input w84\">\n" +
                                            "                                        <div class=\"sign\">一</div>\n" +
                                            "                                        <input value=\""+obj['end']+"\" type=\"text\" name=\"addpNamee\" value=\"\" autocomplete=\"off\" class=\"layui-input w84\">\n" +
                                            "                                            <div class=\"delet\"></div>\n" +
                                            "                            </div>";
                                            $("#add-grade-level-html").parent(".lab").before(html);
                                    }
                                }
                                if(!U.isEmpty(data.gradeInterval)){
                                    var gradeInterval = JSON.parse(data.gradeInterval);
                                    for(var i in gradeInterval){
                                        var obj = gradeInterval[i];
                                        var html = "<div class=\"lab statistics-interval-html\">\n" +
                                            "            <input value=\""+obj['start']+"\" type=\"text\" name=\"addpNames\" value=\"\" autocomplete=\"off\" class=\"layui-input w84\">\n" +
                                            "            <div class=\"sign\">一</div>\n" +
                                            "            <input value=\""+obj['end']+"\" type=\"text\" name=\"addpNamee\" value=\"\" autocomplete=\"off\" class=\"layui-input w84\">\n" +
                                            "            <div class=\"delet\"></div>\n" +
                                            "            </div>";
                                        $("#add-statistics-interval-html").parent(".lab").before(html);
                                    }
                                }
                            },
                        }, function () {});
                    }
                })
                table.on("tool(dataTable3)", function (obj) {
                    var data = obj.data; //获得当前行数据
                    if (obj.event === "del") {
                        layer.confirm('是否删除？', function (index) {
                            $.ajax({
                                type: "POST",
                                url: "/api/score/otherRule/deleteOtherruleResultanly",
                                data: {id: data.id},
                                dataType: 'json',
                                success: function (result) {
                                    if(result.code == 200){
                                        layer.msg("删除成功");
                                        layui.table.reload('main-table3', {
                                            where: {
                                                'fid': $("#grdotherRule_fid").val(),
                                                "xnxq": $("#grdotherRule_xnxq").val()
                                            },
                                        }, 'data');
                                    } else {
                                        layer.msg(result.msg);
                                    }
                                }
                            });
                        });
                    } else if (obj.event === "edit") {
                        $("#qualityAnalysisTitleId").val(data.id);
                        $("#qualityAnalysisTitle").val(data.qualityAnalysisTitle);
                        addInputindex = layer.open({
                            type: 1,
                            title: false,
                            closeBtn: false,
                            shadeClose: true,
                            isOutAnim: true,
                            content: $('#addInput'),
                            area: ['auto', 'auto'],
                            success: function () {
                            },
                        }, function () {});
                    }
                })

                var table2 = table.render({
                    elem: "#main-table2",
                    id: 'main-table2',
                    url: "/api/score/otherRule/getOtherruleResultanlyRuleList",
                    page: {
                        limit: 10,
                        limits: [10, 20, 30, 40, 50],
                        layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                    },
                    where: {
                        'fid': $("#grdotherRule_fid").val(),
                        "xnxq": $("#grdotherRule_xnxq").val()
                    },
                    request: {
                        pageName: "curPage",
                        limitName: "pageSize"
                    },
                    parseData: function (res) {
                        return {
                            "count": res.data.count,
                            "data": res.data.dataList,
                            "code": 0
                        }
                    },
                    cols: [
                        [{
                            field: "itemname",
                            align: "center",
                            title: "成绩分项",
                            minWidth: 120,
                        },
                            {
                                field: "options",
                                align: "center",
                                title: "操作",
                                toolbar: "#barDemo4",
                                minWidth: 120
                            },
                        ]
                    ],
                    done: function (res) {}
                })


                var table3 = table.render({
                    elem: "#main-table3",
                    id: 'main-table3',
                    url: "/api/score/otherRule/getOtherruleResultanlyList",
                    page: {
                        limit: 10,
                        limits: [10, 20, 30, 40, 50],
                        layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                    },
                    where: {
                        'fid': $("#grdotherRule_fid").val(),
                        "xnxq": $("#grdotherRule_xnxq").val()
                    },
                    request: {
                        pageName: "curPage",
                        limitName: "pageSize"
                    },
                    parseData: function (res) {
                        return {
                            "count": res.data.count,
                            "data": res.data.dataList,
                            "code": 0
                        }
                    },
                    cols: [
                        [{
                            field: "qualityAnalysisTitle",
                            align: "center",
                            title: "质量分析标题",
                            minWidth: 120,
                        },
                            {
                                field: "options",
                                align: "center",
                                title: "操作",
                                toolbar: "#barDemo4",
                                minWidth: 120
                            },
                        ]
                    ],
                    done: function (res) {}
                })

                $(".save-settings span").click(function(){
                    var id = $("#grdotherRule_id").val();
                    var fid = $("#grdotherRule_fid").val();
                    var xnxq = $("#grdotherRule_xnxq").val();
                    var createUid = $("#grdotherRule_createUid").val();
                    var teacherRuleType = $("input[name='switchTeacherFilter1']").is(":checked") ? "1" : "0";
                    var teacherTitleType = $("input[name='switchTeacherFilter2']").is(":checked") ? "1" : "0";
                    var gradeShow = $("input:radio[name='gradeShow']:checked").val();
                    var studentShowContentArr = [];
                    $("#scontentDisplay .queryContent ul li").each(function(){
                        var pname = $(this).prop("id");
                        var pobj = {};
                        var obj = {};
                        $(this).find(".checkbox-list span").each(function(){
                            if($(this).hasClass("cur")){
                                obj["" + $(this).attr("type")] = "1";
                            } else {
                                obj["" + $(this).attr("type")] = "0";
                            }
                        })
                        pobj[pname] = obj;
                        studentShowContentArr.push(pobj);
                    })

                    var teacherShowContentArr = [];
                    $("#contentDisplay .queryContent ul li").each(function(){
                        var pname = $(this).prop("id");
                        var pobj = {};
                        var obj = {};
                        $(this).find(".checkbox-list span").each(function(){
                            if($(this).hasClass("cur")){
                                obj["" + $(this).attr("type")] = "1";
                            } else {
                                obj["" + $(this).attr("type")] = "0";
                            }
                        })
                        pobj[pname] = obj;
                        teacherShowContentArr.push(pobj);
                    })
                    var studentShowContent = JSON.stringify(studentShowContentArr);
                    var teacherShowContent = JSON.stringify(teacherShowContentArr);
                    $.ajax({
                        type: "POST",
                        url: "/api/score/otherRule/updateOtherruleBase",
                        data: {id: id, fid: fid, xnxq: xnxq, createUid: createUid,
                            teacherRuleType: teacherRuleType, teacherTitleType: teacherTitleType,
                            'gradeShow': gradeShow, "studentShowContent": studentShowContent,
                            "teacherShowContent": teacherShowContent},
                        dataType: 'json',
                        success: function (result) {
                            if(result.code == 200){
                                layer.msg("保存成功");
                                window.location.reload();
                            } else {
                                layer.msg(result.msg);
                            }
                        }
                    });

                })

                $("#add-otherruleResultanlyRule").click(function(){
                    var id = $("#otherruleResultanlyRuleId").val();
                    var itemidArr = [];
                    var itemnameArr = [];
                    $("#itemList li").each(function(){
                        if($(this).hasClass("cur")){
                            itemidArr.push($(this).attr("value"));
                            itemnameArr.push($(this).attr("name"));
                        }
                    })
                    var itemid = itemidArr.join(",");
                    var itemname = itemnameArr.join(",");
                    if(U.isEmpty(itemid)){
                        layer.msg("成绩分项不得为空");
                        return;
                    }

                    var statisticsTypeArr = [];
                    $("#statistics-content li").each(function(){
                        if($(this).hasClass("cur")){
                            statisticsTypeArr.push($(this).attr("value"));
                        }
                    })
                    var statisticsType = statisticsTypeArr.join(",");
                    if(U.isEmpty(statisticsType)){
                        layer.msg("统计内容不得为空");
                        return;
                    }

                    var levelIntervalArr = [];
                    if(statisticsType.indexOf("1") != -1){
                        var falg1 = true;
                        $("#level-interval-content .li-box .grade-level-html").each(function(){
                            var obj = {};
                            var title = $(this).find(".level").val();
                            var start = $(this).find("input[name='addpNames']").val();
                            var end = $(this).find("input[name='addpNamee']").val();
                            if(U.isEmpty(title) || U.isEmpty(start) || U.isEmpty(end)){
                                falg1 = false;
                            }
                            obj["title"] = title;
                            obj["start"] = start;
                            obj["end"] = end;
                            levelIntervalArr.push(obj);
                        })
                        if(!falg1){
                            layer.msg("等级区间填写不完整");
                            return;
                        }
                    }

                    var gradeIntervalArr = [];
                    if(statisticsType.indexOf("2") != -1 || statisticsType.indexOf("3") != -1){
                        var falg2 = true;
                        $("#statistics-interval-content .li-box .statistics-interval-html").each(function(){
                            var obj = {};
                            var start = $(this).find("input[name='addpNames']").val();
                            var end = $(this).find("input[name='addpNamee']").val();
                            if(U.isEmpty(start) || U.isEmpty(end)){
                                falg2 = false;
                            }
                            obj["start"] = start;
                            obj["end"] = end;
                            gradeIntervalArr.push(obj);
                        })
                        if(!falg2){
                            layer.msg("分数段填写不完整");
                            return;
                        }
                    }
                    var gradeIntervalStr = ""
                    if(gradeIntervalArr.length != 0){
                        gradeIntervalStr = JSON.stringify(gradeIntervalArr);
                    }

                    var levelIntervalStr = ""
                    if(levelIntervalArr.length != 0){
                        levelIntervalStr = JSON.stringify(levelIntervalArr);
                    }

                    $.ajax({
                        type: "POST",
                        url: "/api/score/otherRule/saveOtherruleResultanlyRule",
                        data: {id: id, itemid: itemid, itemname: itemname, statisticsType: statisticsType,
                            gradeInterval: gradeIntervalStr, levelInterval: levelIntervalStr,
                            'fid': $("#grdotherRule_fid").val(), "xnxq": $("#grdotherRule_xnxq").val(),
                            "createUid": $("#grdotherRule_createUid").val()},
                        dataType: 'json',
                        success: function (result) {
                            if(result.code == 200){
                                layer.close(achievementAddInputindex);
                                layui.table.reload('main-table2', {
                                    where: {
                                        'fid': $("#grdotherRule_fid").val(),
                                        "xnxq": $("#grdotherRule_xnxq").val()
                                    },
                                }, 'data');
                            } else {
                                layer.msg(result.msg);
                            }
                        }
                    });

                })

                $("#addQualityAnalysisTitle").click(function(){
                    var id = $("#qualityAnalysisTitleId").val();
                    var title = $("#qualityAnalysisTitle").val();
                    $.ajax({
                        type: "POST",
                        url: "/api/score/otherRule/saveOtherruleResultanly",
                        data: {id: id, qualityAnalysisTitle: title, 'fid': $("#grdotherRule_fid").val(),
                            "xnxq": $("#grdotherRule_xnxq").val(), "createUid": $("#grdotherRule_createUid").val()},
                        dataType: 'json',
                        success: function (result) {
                            if(result.code == 200){
                                layer.close(addInputindex);
                                layui.table.reload('main-table3', {
                                    where: {
                                        'fid': $("#grdotherRule_fid").val(),
                                        "xnxq": $("#grdotherRule_xnxq").val()
                                    },
                                }, 'data');
                            } else {
                                layer.msg(result.msg);
                            }
                        }
                    });
                })

                $("#add-special-flag").click(function (){
                    var specialResultCode = $("#templateName1").val();
                    var specialResultName = $("#templateName2").val();
                    var specialResultScore = $("#templateName3").val();
                    var enableFlag = $("#enableFlag").is(":checked") ? "1" : "0";
                    if(U.isEmpty(specialResultName)){
                        layer.msg("特殊成绩名称不得为空")
                        return;
                    }
                    if(U.isEmpty(specialResultScore)){
                        layer.msg("对应分数不得为空")
                        return;
                    }
                    let formdata = new FormData();
                    formdata.append("specialResultCode", specialResultCode);
                    formdata.append("specialResultName", specialResultName);
                    formdata.append("specialResultScore", specialResultScore);
                    formdata.append("enableFlag", enableFlag);
                    formdata.append("fid", $("#grdotherRule_fid").val());
                    formdata.append("xnxq", $("#grdotherRule_xnxq").val());
                    formdata.append("id", $("#specialresultid").val());
                    formdata.append("createUid", $("#grdotherRule_createUid").val());
                    $.ajax({
                        type: "POST",
                        url: "/api/score/otherRule/saveOtherruleSpecialreslt",
                        data: formdata,
                        contentType : false,
                        processData : false,
                        dataType: 'json',
                        success: function (result) {
                            if(result.code == "200"){
                                layer.close(addInputsindex);
                                table.reload('main-table1');
                            }
                        },error: function (){
                            layer.msg("系统错误");
                        }
                    });
                })

                //设置
                $(".layui-form .layui-form-item .layui-input-block .teacherEdit").click(function () {
                    contentDisplayindex = layer.open({
                        type: 1,
                        title: false,
                        closeBtn: false,
                        shadeClose: true,
                        isOutAnim: true,
                        content: $('#contentDisplay'),
                        area: ['auto', 'auto'],
                        success: function () {},

                    }, function () {});
                })

                //设置
                $(".layui-form .layui-form-item .layui-input-block .studentEdit").click(function () {
                    scontentDisplayindex = layer.open({
                        type: 1,
                        title: false,
                        closeBtn: false,
                        shadeClose: true,
                        isOutAnim: true,
                        content: $('#scontentDisplay'),
                        area: ['auto', 'auto'],
                        success: function () {},

                    }, function () {});
                })

                //选中

                $("#contentDisplay .popup-con").on("click", ".queryContent ul li .checkbox-list span",
                    function () {
                        $(this).toggleClass("cur");
                        let pstate = $(this).parents("li").hasClass("all-select");
                        if (pstate) {
                            if ($(this).hasClass("cur")) {
                                $("#contentDisplay .popup-con .queryContent ul li .checkbox-list span")
                                    .addClass("cur");
                            } else {
                                $("#contentDisplay .popup-con .queryContent ul li .checkbox-list span")
                                    .removeClass("cur");
                            }
                        } else {
                            let nums = $(
                                "#contentDisplay .popup-con .queryContent ul li:not(.all-select) .checkbox-list span"
                            ).length;
                            let currentnums = $(
                                "#contentDisplay .popup-con .queryContent ul li:not(.all-select) .checkbox-list .cur"
                            ).length;
                            if (nums == currentnums) {
                                $("#contentDisplay .popup-con .queryContent ul li.all-select .checkbox-list span")
                                    .addClass("cur");
                            } else {
                                $("#contentDisplay .popup-con .queryContent ul li.all-select .checkbox-list span")
                                    .removeClass("cur");
                            }
                        }
                    })

                $("#scontentDisplay .popup-con").on("click", ".queryContent ul li .checkbox-list span",
                    function () {
                        $(this).toggleClass("cur");
                        let pstate = $(this).parents("li").hasClass("all-select");
                        if (pstate) {
                            if ($(this).hasClass("cur")) {
                                $("#scontentDisplay .popup-con .queryContent ul li .checkbox-list span")
                                    .addClass("cur");
                            } else {
                                $("#scontentDisplay .popup-con .queryContent ul li .checkbox-list span")
                                    .removeClass("cur");
                            }
                        } else {
                            let nums = $(
                                "#scontentDisplay .popup-con .queryContent ul li:not(.all-select) .checkbox-list span"
                            ).length;
                            let currentnums = $(
                                "#scontentDisplay .popup-con .queryContent ul li:not(.all-select) .checkbox-list .cur"
                            ).length;
                            if (nums == currentnums) {
                                $("#scontentDisplay .popup-con .queryContent ul li.all-select .checkbox-list span")
                                    .addClass("cur");
                            } else {
                                $("#scontentDisplay .popup-con .queryContent ul li.all-select .checkbox-list span")
                                    .removeClass("cur");
                            }
                        }
                    })


                //隐藏弹窗
                $('.close,.exam-cancle').on("click", function () {
                    var index = $(this).parents(".layui-layer").attr("times");
                    layer.close(index);
                })

                //添加
                $(".flex-area .add-lab2 span").click(function () {

                    addInputindex = layer.open({
                        type: 1,
                        title: false,
                        closeBtn: false,
                        shadeClose: true,
                        isOutAnim: true,
                        content: $('#addInput'),
                        area: ['auto', 'auto'],
                        success: function () {
                            $("#qualityAnalysisTitleId").val("");
                            $("#qualityAnalysisTitle").val("");
                        },
                    }, function () {});
                })

                //添加
                $(".flex-area .add-lab1 span").click(function () {

                    achievementAddInputindex = layer.open({
                        type: 1,
                        title: false,
                        closeBtn: false,
                        shadeClose: true,
                        isOutAnim: true,
                        content: $('#achievementAddInput'),
                        offset:'15%',
                        area: ['auto', 'auto'],
                        success: function () {
                            $("#otherruleResultanlyRuleId").val("");
                            $("#itemList li").removeClass("cur");
                            $("#statistics-content li").removeClass("cur");
                            $("#level-interval-content .li-box .grade-level-html").remove();
                            $("#statistics-interval-content .li-box .statistics-interval-html").remove();
                        },
                    }, function () {});
                })

                //添加
                $(".cjadd-lab span").click(function () {
                    $("#addInputs .title .name").html("添加");
                    form.val("addeditForm", {
                        "templateName1": '',
                        "templateName2": '',
                        "templateName3": '',
                        "switchTeacherFilter3": 0
                    });
                    $("#addInputs .layui-form .layui-form-item .limit-switch span").text("否");

                    addInputsindex = layer.open({
                        type: 1,
                        title: false,
                        closeBtn: false,
                        shadeClose: true,
                        isOutAnim: true,
                        content: $('#addInputs'),
                        area: ['auto', 'auto'],
                        success: function () {
                            $("#specialresultid").val("");
                        },
                    }, function () {});
                })


                $("#achievementAddInput .popup-con .p-boxs .check-box ul li").click(function () {
                    $(this).toggleClass("cur");
                    var selectId = [];
                    $("#statistics-content li").each(function(){
                        if($(this).hasClass("cur")){
                            selectId.push($(this).attr("value"));
                        }
                    })
                    $(".level-interval-content").hide();
                    $(".statistics-interval-content").hide();
                    for(var i in selectId){
                        if(selectId[i] == "1"){
                            $(".level-interval-content").show();
                        } else if(selectId[i] == "2" || selectId[i] == "3"){
                            $(".statistics-interval-content").show();
                        }
                    }
                })

                //删除

                $("#achievementAddInput .popup-con").on("click",
                    ".p-boxs .level-interval .li-box .lab .delet",
                    function () {
                        $(this).parent().remove();
                    })

                $("#add-grade-level-html").click(function(){
                    $("#add-grade-level-html").parent(".lab").before("<div class=\"lab grade-level-html\">\n" +
                        "                                <input class=\"level\" placeholder=\"例:优分率\">\n" +
                        "                                    <input type=\"text\" name=\"addpNames\" value=\"\" autocomplete=\"off\" class=\"layui-input w84\">\n" +
                        "                                        <div class=\"sign\">一</div>\n" +
                        "                                        <input type=\"text\" name=\"addpNamee\" value=\"\" autocomplete=\"off\" class=\"layui-input w84\">\n" +
                        "                                            <div class=\"delet\"></div>\n" +
                        "                            </div>");
                })

                $("#add-statistics-interval-html").click(function(){
                    $("#add-statistics-interval-html").parent(".lab").before("<div class=\"lab statistics-interval-html\">\n" +
                        "            <input type=\"text\" name=\"addpNames\" value=\"\" autocomplete=\"off\" class=\"layui-input w84\">\n" +
                        "            <div class=\"sign\">一</div>\n" +
                        "            <input type=\"text\" name=\"addpNamee\" value=\"\" autocomplete=\"off\" class=\"layui-input w84\">\n" +
                        "            <div class=\"delet\"></div>\n" +
                        "            </div>");
                })

                $("#surecontentDisplay").click(function(){
                    layer.close(contentDisplayindex);
                })

                $("#surescontentDisplay").click(function(){
                    layer.close(scontentDisplayindex);
                })
            })
    })

    function contains(arr, str){
        var flag = false;
        for(var i in arr){
            if(arr[i] == str){
                flag = true;
                break;
            }
        }
        return flag;
    }
</script>

</body>

</html>