<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">

<head>
    <th:block th:include="common :: header('从开课信息添加')"/>
    <th:block th:include="common :: jquery-mCustomScrollbar-css"/>

    <link rel="stylesheet" type="text/css" th:href="@{../css/examination/global.css}" href=""/>
    <link rel="stylesheet" type="text/css" th:href="@{../css/examination/chosen.css}"/>
    <link rel="stylesheet" type="text/css" th:href="@{../plugin/layui-v2.8.18/layui/css/layui.css}"/>
    <link rel="stylesheet" type="text/css" th:href="@{../css/examination/style.css}"/>
    <link rel="stylesheet" type="text/css" th:href="@{../css/examination/sort.css}"/>
    <link rel="stylesheet" type="text/css" th:href="@{../css/examination/font.css}"/>
    <style>
        .inputs input {
            margin: 0;
            outline: 0;
            -webkit-appearance: none;
            border-radius: 0;
            line-height: 30px;
            padding-left: 10px;
            border: none;
            background: transparent;
            height: 29px;
            float: left;
            color: rgb(51, 51, 51);
            font-size: 14px;
        }

        input::placeholder {
            color: #8F97A8;
            font-size: 14px !important;
        }

        .layui-form-checkbox[lay-skin=primary]>i{
            border:none;
        }

        .all-select{
            width: 100%;
            height: auto;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .all-select .layui-form{
            position: relative;
        }

    </style>
</head>

<body class="bodyColor">

<div class="zy_box">
    <div class="zy_main">
        <div class="zy_title">
            <h2 class="zy_title_h2 fl">添加课程</h2>
        </div>
        <div class="xaScreen clearAfter">
            <div class="xaScreen_row fl">
                批次所属学年学期：
                <input type="hidden" class="thisBatchYear"
                       th:each="info : ${academicYearSemesterFormList}"
                       th:attr="value=${info.xnxq_xnxqh}"

                       th:if="${info.xnxq_sfdqxq eq '是'}">

                <select name="yearSemester" id="batchYearSemester" data-placeholder="请选择学年学期" style="width:200px;"
                        class="dept_select">
                    <option value="">请选择学年学期</option>
                    <!--<option th:each="info : ${academicYearSemesterFormList}" th:attr="value=${info.xnxq_xnxqh}"
                            th:text="${info.xnxq_xnxqh}" th:if="${info.xnxq_sfdqxq eq '是'}"
                            selected="selected"></option>
                    <option th:each="info : ${academicYearSemesterFormList}" th:attr="value=${info.xnxq_xnxqh}"
                            th:text="${info.xnxq_xnxqh}" th:if="${info.xnxq_sfdqxq != '是'}"></option>-->
                    <option th:each="info : ${academicYearSemesterFormList}" th:attr="value=${info.xnxq_xnxqh}"
                            th:text="${info.xnxq_xnxqh}"></option>
                </select>
            </div>
            <div class="xaScreen_row fl">
                考试批次：
                <select name="testBatch" id="batchName" data-placeholder="请选择考试批次" style="width:200px;"
                        class="dept_select">
                    <option value="" selected="selected">请选择考试批次</option>
                </select>
            </div>
        </div>
        <div class="xaScreen clearAfter">
            <div class="xaScreen_row fl">
                开课学期：
                <select name="courseYearSemester" id="courseYearSemester" data-placeholder="请选择学年学期"
                        style="width:200px;" class="dept_select" onchange="getSelectByParentParam(1)">
                    <option value="" selected="selected">请选择学年学期</option>
                    <option th:each="info : ${academicYearSemesterFormList}" th:attr="value=${info.xnxq_xnxqh}"
                            th:text="${info.xnxq_xnxqh}"></option>
                </select>
            </div>
            <div class="xaScreen_row fl">
                考试方式：
                <select name="class" id="examMethod" data-placeholder="请选择考试方式" style="width:200px;"
                        class="dept_select" onchange="getSelectByParentParam(2)">
                    <option value="" selected="selected">请选择考试方式</option>
                    <option th:each="info : ${examMethon}" th:attr="value=${info}"
                            th:text="${info}" th:if="${info != ''}"></option>
                </select>
            </div>
            <div class="xaScreen_row fl">
                课程名称：
                <select name="course" id="courseName" data-placeholder="请选择课程" style="width:200px;" class="dept_select" onchange="getSelectByParentParam(3)">
                    <option value="" selected="selected">请选择课程</option>
                </select>

            </div>
            <div class="xaScreen_row fl">
                教学班名称：
                <select name="courseClass" id="courseClassName" data-placeholder="请选择教学班名称" style="width:200px;"
                        class="dept_select" onchange="getSelectByParentParam(4)">
                    <option value="" selected="selected">请选择教学班名称</option>
                    <option th:each="info : ${courseClassList}" th:attr="value=${info}"
                            th:text="${info}" th:if="${info != ''}"></option>
                </select>

            </div>
            <div class="xaScreen_row fl">
                教学班编号：
                <select name="courseClassCode" id="courseClassCode" data-placeholder="请选择教学班编号" style="width:200px;"
                        class="dept_select" onchange="getSelectByParentParam(9)">
                    <option value="" selected="selected">请选择教学班编号</option>
                    <option th:each="info : ${courseClassCodeList}" th:attr="value=${info}"
                            th:text="${info}" th:if="${info != ''}"></option>
                </select>

            </div>
            <div class="xaScreen_row fl">
                开课部门：
                <select name="kkbm" id="kkbm" data-placeholder="请选择开课部门" style="width:200px;"
                        class="dept_select">
                    <option value="" selected="selected">请选择开课部门</option>
                    <option th:each="info : ${kkbmList}" th:attr="value=${info}"
                            th:text="${info}" th:if="${info != ''}"></option>
                </select>

            </div>
            <div class="xaScreen_row fl">
                年级：
                <select name="grade" id="gradeName" data-placeholder="请选择年级" style="width:200px;" class="dept_select" onchange="getClassName()">
                    <option value="" selected="selected" >请选择年级</option>
                </select>

            </div>
            <div class="xaScreen_row fl">
                校区：
                <select name="class" id="campus" data-placeholder="请选择校区" style="width:200px;"
                        class="dept_select" onchange="getClassName()">
                    <option value="" selected="selected">请选择校区</option>
                    <option th:each="info : ${campus}" th:attr="value=${info}"
                            th:text="${info}" th:if="${info != ''}"></option>
                </select>
            </div>
            <div class="xaScreen_row fl">
                院系名称：
                <select name="dept" id="dept" data-placeholder="请选择系部" style="width:200px;" class="dept_select" onchange="getClassName()">
                    <option value="" selected="selected" >请选择系部</option>
                </select>

            </div>
            <div class="xaScreen_row fl">
                专业名称：
                <select name="zy" id="zy" data-placeholder="请选择专业" style="width:200px;" class="dept_select" onchange="getClassName()">
                    <option value="" selected="selected" >请选择专业</option>
                </select>

            </div>

            <div class="xaScreen_row fl">
                班级名称：
                <select name="class" id="className" data-placeholder="请选择班级名称" style="width:200px;" class="dept_select">
                    <option value="" selected="selected">请选择班级名称</option>
                    <option th:each="info : ${classList}" th:attr="value=${info}"
                            th:text="${info}" th:if="${info != ''}"></option>
                </select>
            </div>
            <div class="xaScreen_row fl"><a class="xaScreen_bnt" href="javascript:aaa();" id="search">查询</a></div>
            <div class="xaScreen_row fl"><a class="xaScreen_bnt" href="javascript:aaa();" id="reSetParam">重置</a></div>
        </div>
        <div class="xaScreen clearAfter">
            <div class="xaScreen_row fl"><a class="xaScreen_add" href="javascript:aaa();" >一键添加</a></div>
            <div class="opt_data_top fr">
                <div id="fieldsSortRuleBtn" class="opt_data_btn opt_data_order fr">
                    <div class="order_btn"><i class="icomoon icon-order"></i> <span>排序</span></div>
                    <div class="opt-order-popup" style="display: none">
                        <div class="order-popup-body">
                            <div id="fieldSortRuleList" class="order-ul">
                                <div class="order-lis">
                                    <div class="select-line-ctrl width120 mr8">
                                        <div class="select-line-box">
                                            <span class="icon-select-down"></span>
                                            <input type="text" readonly="readonly" placeholder="请选择" class="owt1">
                                        </div>
                                        <div class="select-list-box">
                                            <div class="sel-search-box">
                                                <input type="text" placeholder="搜索"
                                                       class="sel-search-val"> <span class="icon-search"></span>
                                                <span class="icon-clean-input" style="display: none;"><span
                                                        class="path1"></span><span class="path2"></span></span></div>
                                            <div class="sel-search-tip" style="display: none;">未搜索到数据</div>
                                            <ul class="select-list">
                                                <li class="select-option" value="kkxxb_kcmc"><span title="课程名称" class="owt1" >课程名称</span></li>
                                                <li class="select-option" value="kkxxb_njnj"><span title="年级" class="owt1" >年级</span></li>
                                                <li class="select-option" value="kkxxb_kkxqxq"><span title="校区" class="owt1" >校区</span></li>
                                                <li class="select-option" value="kkxxb_kkyxyx"><span title="院系" class="owt1">院系</span></li>
                                                <li class="select-option" value="kkxxb_zymc"><span title="专业" class="owt1" >专业</span></li>
                                                <li class="select-option" value="kkxxb_jxbzc"><span title="班级名称" class="owt1" >班级名称</span></li>
                                                <li class="select-option" value="kkxxb_jxbmc"><span title="教学班名称" class="owt1" >教学班名称</span></li>
                                                <li class="select-option" value="kkxxb_kcbh"><span title="课程编号" class="owt1" >课程编号</span></li>
                                                <li class="select-option" value="kkxxb_jxbbh"><span title="教学班编号" class="owt1" >教学班编号</span></li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="select-line-ctrl width120 mr10">
                                        <div class="select-line-box">
                                            <span class="icon-select-down"></span>
                                            <input type="text" readonly="readonly" placeholder="请选择" class="owt1" value="降序">
                                        </div>
                                        <div class="select-list-box">
                                            <ul class="select-list">
                                                <li class="select-option"><span class="owt1">升序</span></li>
                                                <li class="select-option s-selected"><span class="owt1">降序</span></li>
                                            </ul>
                                        </div>
                                    </div>

                                    <span class="icon-delete"></span>
                                </div>
                            </div>
                            <div class="x-add-icon-txt-btn"><span class="icon-add"></span> <span>添加条件</span></div>
                        </div>
                        <div class="order-popup-btm">
                            <div class="clean-btn"><span class="icon-clean"></span> <span>清空</span></div>
                            <div class="sure-btn">排序</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="xaTable_box">
            <div class="xaTable_container">
                <table class="layui-table" lay-filter="courseTable" id="courseTable"></table>
            </div>
            <div class="all-select">
                <div class="layui-form" lay-filter="quanxuan">
                    <div class="layui-form-item" style="margin-bottom:0;">
                        <div class="layui-input-inline" style="width: 67px;">
                            <input type="checkbox" name="isQuanxuan" id="myCheckbox" lay-filter="isQuanxuans" title="全选"
                                   lay-skin="primary" />
                        </div>
                    </div>
                </div>
                <div id="test1"></div>
            </div>
        </div>
    </div>
</div>


<script th:src="@{../js/jquery-3.6.0.min.js}" type="text/javascript" charset="utf-8"></script>

<script th:src="@{../js/jquery.nicescroll.min.js}" type="text/javascript" charset="utf-8"></script>
<script th:src="@{../js/examination/fyPublic.js}" type="text/javascript" charset="utf-8"></script>
<script th:src="@{../js/examination/chosen.jquery.js}" type="text/javascript" charset="utf-8"></script>
<script th:src="@{../js/base.js}" type="text/javascript" charset="utf-8"></script>
<script type="text/html" id="toolBar">
    <a class="colorBlue" href="javascript:void(0);" lay-event="open">添加</a>
    <a class="colorGreen" href="javascript:void(0);" style="display: none;" lay-event="none">已转入</a>
</script>
<script>
    $('.dept_select').chosen({
        no_results_text: "没有结果匹配",
        hide_results_on_select: false,
        max_shown_results: '2',
        //显示搜索框之前所需的最少选项数
        disable_search_threshold: 10,
        allow_single_deselect: true,
    });
</script>
<script th:src="@{../plugin/layui-v2.8.18/layui/layui.js}" type="text/javascript" charset="utf-8"></script>
<script>
    $(function () {
        $(".order_btn").click(function () {
            if (batchNumber === '') {
                U.fail("请先选择考试批次")
                return false;
            }

            $(this).toggleClass("active");
            if ($(this).hasClass("active")) {
                $(".opt-order-popup").show();
            } else {
                $(".opt-order-popup").hide();
                $("div.s-selected").removeClass("s-selected");
            }
        })

        //删除
        $(".opt_data_top").on("click", ".opt-order-popup .order-popup-body .icon-delete", function () {
            $(this).parents(".order-lis").remove();
        })

        //下拉
        $(".opt_data_top .opt-order-popup").on("click", ".order-popup-body .order-lis .select-line-ctrl .select-line-box", function () {
            $(this).parent().toggleClass("s-selected");
        })

        //下拉点击
        $(".opt_data_top").on("click", ".order-lis .select-line-ctrl .select-list-box .select-list .select-option", function () {
            let tts = $(this).find("span").text();
            $(this).parents(".select-line-ctrl").find(".select-line-box input").val(tts);
            $(this).parent().children().each((index,item)=>{
                $(item).removeClass("s-selected");
            });
            $(this).toggleClass("s-selected");
        })

        //添加条件

        $(".opt_data_top .opt-order-popup .order-popup-body .x-add-icon-txt-btn").click(function () {
            let aoderHtml = ' <div class="order-lis">' +
                '<div class="select-line-ctrl width120 mr8">' +
                '<div class="select-line-box">' +
                '<span class="icon-select-down"></span>' +
                '<input type="text" readonly="readonly" placeholder="请选择" class="owt1">' +
                '</div>' +
                '<div class="select-list-box">' +
                '<div class="sel-search-box">' +
                '<input type="text" placeholder="搜索"' +
                'class="sel-search-val"> <span class="icon-search"></span>' +
                '<span class="icon-clean-input" style="display: none;"><span' +
                'class="path1"></span><span class="path2"></span></span></div>' +
                '<div class="sel-search-tip" style="display: none;">未搜索到数据</div>' +
                '<ul class="select-list">' +
                '<li class="select-option" value="kkxxb_kcmc"><span title="课程名称" class="owt1" >课程名称</span></li>' +
                '<li class="select-option" value="kkxxb_njnj"><span title="年级" class="owt1"  >年级</span></li>' +
                '<li class="select-option" value="kkxxb_kkxqxq"><span title="校区" class="owt1"  >校区</span></li>' +
                '<li class="select-option" value="kkxxb_kkyxyx"><span title="院系" class="owt1"  >院系</span></li>' +
                '<li class="select-option" value="kkxxb_zymc"><span title="专业" class="owt1"  >专业</span></li>' +
                '<li class="select-option" value="kkxxb_jxbzc"><span title="班级名称" class="owt1"  >班级名称</span></li>' +
                '<li class="select-option" value="kkxxb_jxbmc"><span title="教学班名称" class="owt1"  >教学班名称</span></li>' +
                '<li class="select-option" value="kkxxb_kcbh"><span title="课程编号" class="owt1" >课程编号</span></li>' +
                '<li class="select-option" value="kkxxb_jxbbh"><span title="教学班编号" class="owt1"  >教学班编号</span></li>' +
                '</ul>' +
                '</div>' +
                '</div>' +
                '<div class="select-line-ctrl width120 mr10">' +
                '<div class="select-line-box">' +
                '<span class="icon-select-down"></span>' +
                '<input type="text" readonly="readonly" placeholder="请选择" class="owt1" value="降序">' +
                '</div>' +
                '<div class="select-list-box">' +
                '<ul class="select-list">' +
                '<li class="select-option"><span class="owt1">升序</span></li>' +
                '<li class="select-option s-selected"><span class="owt1">降序</span></li>' +
                '</ul>' +
                '</div>' +
                '</div> ' +
                '<span class="icon-delete"></span>' +
                '</div>'

            $("#fieldSortRuleList").append(aoderHtml);

        })

        //清空

        $(".opt_data_top .opt-order-popup .order-popup-btm .clean-btn").click(function () {
            $("#fieldSortRuleList").empty();
        })


    })
</script>
<script type="text/javascript">
    const a = '[[${collageList}]]';
    const collageList = '[[${collageList}]]'.replaceAll("[", "").replaceAll("]", "").split(",");
    const fid = '[[${fid}]] ';
    let subFlag = false;
    let batchNumber = "";
    let batchYearSemester = "";
    let table2 = "", insTb = "";
    $(function () {
        $('#batchYearSemester').val($(".thisBatchYear").val());
        $('#batchYearSemester').trigger("chosen:updated");

        layui.use(['table', 'form', 'laypage'], function () {
            var table = layui.table;
            var form = layui.form;
            var laypage = layui.laypage;
            let dataList = [];
            let selectArr = [];
            let currnums = 1;
            let limit = 10;
            let pages=0;
            let otbale;
            //排序参数
            var sortData=[];
            table2 = layui.table;
            function initBatchSel(list, needReload) {
                $("#batchName").html('<option value="" selected="selected">请选择考试批次</option>');
                for (let i in list) {
                    $("#batchName").append('<option value="' + list[i].kspcgl_kspcbh + '" >' + list[i].kspcgl_kspcmc + '</option>');
                }
                $('#batchName').trigger("chosen:updated");
                if (needReload) {
                    switchBatch();
                }
            }

            function getData4Select(types, needReload) {
                batchYearSemester = $("#batchYearSemester").val();
                $.ajax({
                    url: "../examination/getData4Select",
                    data: {yearSemester: batchYearSemester, type: types},
                    dataType: 'json',
                    type: 'post',
                    success: function (res) {
                        if (res.code != 200) {
                            console.log("数据加载失败");
                        }
                        let data = res.data;
                        if (types.indexOf("1") > -1) {
                            initBatchSel(data.batch.list, needReload);
                        }
                        if (types.indexOf("2") > -1) {
                            let list = data.grade.list;
                            $("#gradeName").html('<option value="" selected="selected">请选择年级</option>');
                            for (let i in list) {
                                let val = list[i].nj_njmc;
                                $("#gradeName").append('<option value="' + val + '" >' + val + '</option>');
                            }
                            $('#gradeName').trigger("chosen:updated");
                        }
                        if (types.indexOf("3") > -1) {
                            let list = data.course.list;
                            $("#courseName").html('<option value="" selected="selected">请选择课程</option>');
                            for (let i in list) {
                                let val = list[i].kck_kcmc;
                                $("#courseName").append('<option value="' + val + '" >' + val + '</option>');
                            }
                            $('#courseName').trigger("chosen:updated");
                        }
                        if (types.indexOf("4") > -1) {
                            let list = data.college.list;
                            if (getCookie("fid") == 250119 || getCookie("fid") == 299360){
                                if (a != '' && collageList !== undefined && collageList !== ''){
                                    for (let i in collageList) {
                                        if (i == 0){
                                            $("#dept").html('<option value="'+collageList[i]+'" selected="selected">'+collageList[i]+'</option>');
                                        }else {
                                            $("#dept").append('<option value="'+collageList[i]+'" >'+collageList[i]+'</option>');
                                        }
                                    }
                                }else {
                                    $("#dept").html('<option value="" selected="selected">请选择系部</option>');
                                    for (let i in list) {
                                        let val = list[i].yxsj_yxmc;
                                        $("#dept").append('<option value="' + val + '" >' + val + '</option>');
                                    }
                                }
                            }else {
                                $("#dept").html('<option value="" selected="selected">请选择系部</option>');
                                for (let i in list) {
                                    let val = list[i].yxsj_yxmc;
                                    $("#dept").append('<option value="' + val + '" >' + val + '</option>');
                                }
                            }
                            $('#dept').trigger("chosen:updated");
                        }

                        if (types.indexOf("5") > -1) {
                            let list = data.zy.list;

                            $("#zy").html('<option value="" selected="selected">请选择专业</option>');
                            for (let i in list) {
                                let val = list[i].zysj_zymc;
                                $("#zy").append('<option value="' + val + '" >' + val + '</option>');
                            }

                            $('#zy').trigger("chosen:updated");
                        }

                    }
                });

            }

            //排序确定
            $(".opt_data_top .opt-order-popup .order-popup-btm .sure-btn").click(function () {
                $(".order_btn").toggleClass("active");
                if ($(".order_btn").hasClass("active")) {
                    $(".opt-order-popup").show();
                } else {
                    $(".opt-order-popup").hide();
                    $("div.s-selected").removeClass("s-selected");
                }
                var lis = $("#fieldSortRuleList").find(".order-lis");
                if (lis.length<=0){
                    return;
                }

                lis.each((index,item)=>{
                    var data={};
                    var field = $($(item).find("li.s-selected")[0]).attr("value");
                    var orderType =$( $(item).find("li.s-selected")[1]).find("span").text()
                    if (field==undefined){
                        return
                    }
                    data["field"]=field
                    data["orderType"]=orderType=="升序"?"asc":"desc";
                    sortData.push(data);
                })
                if (sortData.length>0){
                    initTab()

                }
            })
            function initTab() {
                getData()
            }
            //查询
            $("#search").click(function(){
                beforeInitTab();
                currnums = 1
                selectArr = []
                form.val('quanxuan', {
                    'isQuanxuan': false
                })
            })
            //重置
            $("#reSetParam").click(function(){
                selectArr = []
                form.val('quanxuan', {
                    'isQuanxuan': false
                })
                reSetParam();
            })
            //一键同步
            $(".xaScreen_add").click(function(){
                if (selectArr.length > 1){
                    batchSync()
                }else {
                    syncAll()
                }
            })
            function batchSync() {
                let data = {};
                data["fid"] = getCookie("fid");
                data["uid"] = getCookie("_uid");
                data["batchName"] = batchNumber;
                data["batchYearSemester"] = batchYearSemester;
                data["courseYearSemester"] = $("#courseYearSemester").val()
                data["gradeName"] = $("#gradeName").val();
                data["courseName"] = $("#courseName").val();
                data["dept"] = $("#dept").val();
                data["courseClassName"] = $("#courseClassName").val();
                data["className"] = $("#className").val();
                data["examMethod"] = $("#examMethod").val();
                data["campus"] = $("#campus").val();
                data["courseClassCode"] = $("#courseClassCode").val();
                data["zy"] = $("#zy").val();
                data["kkbm"] = $("#kkbm").val();
                data["pageSize"] = limit;
                if (sortData != undefined){
                    data["sort"]=JSON.stringify(sortData);
                }
                data["selectArr"] = JSON.stringify(selectArr);
                $.ajax({
                    url: "../examination/batchSync",
                    data: data,
                    dataType: 'json',
                    type: 'post',
                    success: function (res) {
                        if (res.status == true){
                            U.success(res.msg)
                        }else {
                            U.success(res.msg)
                        }
                    }
                });

            }
            $('#batchYearSemester').on('change', function() {
                batchNumber = ''
                switchYearSemester();

            });

            // 监听变化事件
            $('#batchName').on('change', function() {
                switchBatch();
            });

            getData4Select("1,2,4,5", false);
            getData4Select("3", false);
            function syncAll() {
                var checkStatus = table2.checkStatus('courseTable');
                if (subFlag) {
                    return;
                }
                if (checkStatus.data.length === 0) {
                    U.fail("请先勾选行政班")
                    return;
                }
                let jxbbh = "";
                for (let i in checkStatus.data) {
                    if (jxbbh.length > 0) {
                        jxbbh += ";";
                    }
                    jxbbh += checkStatus.data[i].kkxxb_jxbbh;
                }
                syncByBase(jxbbh);
            }

            function syncByBase(jxbbh) {
                var fid = getCookie("fid");
                var uid = getCookie("_uid");
                subFlag = true;
                let bhArray = jxbbh.split(";");
                $.ajax({
                    url: "../examination/syncByBase",
                    data: {jxbbhs: jxbbh, batchNumber: batchNumber, yearSemester: batchYearSemester, fid: fid, uid: uid},
                    dataType: 'json',
                    type: 'post',
                    success: function (data) {
                        if (data.status) {
                            U.success(data.msg);

                            $(".layui-table-main tbody tr").each(function () {
                                let thisBh = $(this).find("td[data-field='kkxxb_jxbbh'] .layui-table-cell")[0].innerText
                                if (bhArray.indexOf(thisBh) > -1) {
                                    let newIndexs = Math.floor($(this).index()) + 1;
                                    $(".xaTable_container .layui-table tbody tr:nth-child(" + newIndexs + ") .colorBlue").hide();
                                    $(".xaTable_container .layui-table tbody tr:nth-child(" + newIndexs + ") .colorGreen").show();

                                }
                            })
                        } else {
                            U.fail(data.msg);
                        }
                        subFlag = false;
                    },
                    error: function () {
                        U.fail("访问异常！")
                        subFlag = false;
                    }
                });
            }
            function switchBatch() {
                batchNumber = $("#batchName").val();
                batchYearSemester = $("#batchYearSemester").val();
                initTab();
            }
            function switchYearSemester() {
                getData4Select("1", true);
            }
            function reSetParam() {
                $('#batchName').val("");
                reSetParam2()
            }
            function reSetParam2() {
                $('#batchName').trigger("chosen:updated");
                $('#courseYearSemester').val("");
                $('#courseYearSemester').trigger("chosen:updated");
                $('#gradeName').val("");
                $('#gradeName').trigger("chosen:updated");
                $('#dept').val("");
                $('#dept').trigger("chosen:updated");
                $('#courseName').val("");
                $('#courseName').trigger("chosen:updated");
                $('#className').val("");
                $('#className').trigger("chosen:updated");
                $('#courseClassName').val("");
                $('#courseClassName').trigger("chosen:updated");
                $('#examMethod').val("");
                $('#examMethod').trigger("chosen:updated");
                $('#campus').val("");
                $('#campus').trigger("chosen:updated");
                $('#courseClassCode').val("");
                $('#courseClassCode').trigger("chosen:updated");
                $('#zy').val("");
                $('#zy').trigger("chosen:updated");
                $('#kkbm').val("");
                $('#kkbm').trigger("chosen:updated");
                switchBatch();
            }


            function beforeInitTab() {
                batchNumber = $("#batchName").val();
                batchYearSemester = $("#batchYearSemester").val();
                if (batchNumber === '') {
                    U.fail("请先选择考试批次")
                    return false;
                }
                initTab();
            }
            function getData() {
                let data = {};
                data["batchNumber"] = batchNumber;
                data["batchYearSemester"] = batchYearSemester;
                data["courseYearSemester"] = $("#courseYearSemester").val()
                data["gradeName"] = $("#gradeName").val();
                data["courseName"] = $("#courseName").val();
                data["dept"] = $("#dept").val();
                data["courseClassName"] = $("#courseClassName").val();
                data["className"] = $("#className").val();
                data["examMethod"] = $("#examMethod").val();
                data["kkbm"] = $("#kkbm").val();
                data["zy"] = $("#zy").val();
                data["courseClassCode"] = $("#courseClassCode").val();
                data["campus"] = $("#campus").val();
                if (sortData != undefined){
                    data["sort"]=JSON.stringify(sortData);
                }
                data["page"] = currnums
                data["limit"] = limit
                $.ajax({
                    url: "../examination/getNoTaskCourseByBatch",
                    data: data,
                    dataType: 'json',
                    type: 'post',
                    success: function (res) {
                        dataList = res.data;
                        renderPage(res.count)
                        if ( res.data == undefined || res.data == ''){
                            $('#courseTable').parent('.xaTable_container').children('.layui-table-view').hide()
                            return
                        }

                        if(selectArr.length==0){

                            dataList.forEach(function (item, index) {
                                item.LAY_CHECKED = false;
                            })


                        }else{

                            if(selectArr[currnums-1]==undefined){
                                selectArr[currnums-1]=[];
                                for(let i=0;i<limit;i++){
                                    selectArr[currnums-1].push(0);
                                }
                            }

                            dataList.forEach(function (item, index) {
                                let number= selectArr[currnums-1][index];
                                item.LAY_CHECKED = number==1?true:false;
                            })

                        }

                        if(otbale==undefined){
                            init();
                        }else{
                            table.reload('courseTable', {
                                data: dataList
                            });
                        }
                    }
                });
            }


            // 封装渲染函数
            function renderPage(count) {
                // 清空容器内容（避免重复）
                document.getElementById('test1').innerHTML = '';

                laypage.render({
                    elem: 'test1',
                    count: count,
                    limit: limit, // 每页显示的条数
                    curr: currnums, // 当前页，默认为1
                    layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                    jump: function (obj, first) {
                        if (obj.limit != limit){
                            form.val('quanxuan', {
                                'isQuanxuan': false
                            })
                            arrInit()
                        }
                        currnums = obj.curr;
                        limit = obj.limit;
                        pages=obj.pages;
                        if(!first){
                            getData();
                        }

                    }
                });
            }






            function init() {

                let data = {};
                data["batchNumber"] = batchNumber;
                data["batchYearSemester"] = batchYearSemester;
                data["courseYearSemester"] = $("#courseYearSemester").val()
                data["gradeName"] = $("#gradeName").val();
                data["courseName"] = $("#courseName").val();
                data["dept"] = $("#dept").val();
                data["courseClassName"] = $("#courseClassName").val();
                data["className"] = $("#className").val();
                data["examMethod"] = $("#examMethod").val();
                data["campus"] = $("#campus").val();
                data["courseClassCode"] = $("#courseClassCode").val();
                data["zy"] = $("#y").val();
                data["kkbm"] = $("#kkbm").val();
                if (sortData != undefined){
                    data["sort"]=JSON.stringify(sortData);
                }
                data["page"] = currnums
                data["limit"] = limit
                otbale= table.render({
                    elem: '#courseTable',
                    data: dataList,
                    page: false,
                    cellMinWidth: 100,
                    cols: [
                        [
                            {type: 'checkbox', fixed: 'left'},
                            {field: 'kkxxb_kcmc', title: '课程名称'},
                            {field: 'kkxxb_njnj', title: '年级'},
                            {field: 'kkxxb_kkxqxq', title: '校区'},
                            {field: 'kkxxb_kkyxyx', title: '院系'},
                            {field: 'kkxxb_zymc', title: '专业'},
                            {field: 'kkxxb_jxbzc', title: '班级名称'},
                            {field: 'kkxxb_jxbmc', title: '教学班名称'},
                            {
                                title: '授课教师', templet: function (data) {
                                    let jsxm = "";
                                    for (let i = 0; i < data.kkxxb_skjsxm.length; i++) {
                                        if (i == data.kkxxb_skjsxm.length - 1) {
                                            jsxm += data.kkxxb_skjsxm[i].uname;
                                        } else {
                                            jsxm += data.kkxxb_skjsxm[i].uname + ",";
                                        }
                                    }

                                    return jsxm;
                                }
                            },
                            {
                                title: '教师uid', templet: function (data) {
                                    let jsxm = "";
                                    for (let i = 0; i < data.kkxxb_skjsxm.length; i++) {
                                        if (i == data.kkxxb_skjsxm.length - 1) {
                                            jsxm += data.kkxxb_skjsxm[i].puid;
                                        } else {
                                            jsxm += data.kkxxb_skjsxm[i].puid + ",";
                                        }
                                    }

                                    return jsxm;
                                }
                            },
                            {field: 'kkxxb_kcbh', title: '课程编号'},
                            {field: 'kkxxb_jxbbh', title: '教学班编号'},
                            {field: 'kkxxb_jxbrs', title: '教学班人数'},
                            {title: '操作', toolbar: '#toolBar', field: 'tool', width: 80, fixed: 'right'}
                        ]
                    ],
                    done: function (res, curr, count) {
                        $(".opt_data_num #totalSpan").text("共 " + count + " 条");
                    }
                });
                table.on('tool(courseTable)', function (obj) {
                    if (obj.event !== 'open') {
                        return;
                    }
                    if (subFlag) {
                        return;
                    }

                    syncByBase(obj.data.kkxxb_jxbbh);
                })
            }

            // form.val('quanxuan', {
            //           'isQuanxuan': obj.checked
            //         })

            table.on("checkbox(courseTable)", function (obj) { //监听表格，tableQuanxuan是表格的lay-filter
                if (obj.type == "one") {
                    if (obj.checked) {
                        let sort=obj.index;

                        if(selectArr[currnums-1]==undefined){
                            selectArr[currnums-1]=[];
                            for(let i=0;i<limit;i++){
                                selectArr[currnums-1].push(0);
                            }
                        }

                        selectArr[currnums-1][sort]=1;

                    } else {

                        let sort=obj.index;
                        if(selectArr[currnums-1]==undefined){
                            selectArr[currnums-1]=[];
                            for(let i=0;i<limit;i++){
                                selectArr[currnums-1].push(0);
                            }
                        }

                        selectArr[currnums-1][sort]=0;


                    }
                } else {
                    if (obj.checked) {
                        if(selectArr[currnums-1]==undefined){
                            selectArr[currnums-1]=[];
                            for(let i=0;i<limit;i++){
                                selectArr[currnums-1].push(1);
                            }
                        }else{
                            selectArr[currnums-1].forEach(function(item,index){
                                selectArr[currnums-1][index]=1;
                            })
                        }

                    } else {

                        if(selectArr[currnums-1]==undefined){
                            selectArr[currnums-1]=[];
                            for(let i=0;i<limit;i++){
                                selectArr[currnums-1].push(0);
                            }
                        }else{
                            selectArr[currnums-1].forEach(function(item,index){
                                selectArr[currnums-1][index]=0;
                            })
                        }

                    }

                }

                pdform();

            });


            function pdform(){
                let states=true;

                if(selectArr.length!=pages){
                    states=false;
                }else{
                    for(let i=0;i<pages;i++){
                        for(let j=0;j<limit;j++){
                            if(selectArr[i] == undefined || selectArr[i][j] == undefined || selectArr[i][j]==0){
                                states=false;
                                break;
                            }
                        }
                    }
                }



                form.val('quanxuan', {
                    'isQuanxuan': states
                })
            }

            form.on('checkbox(isQuanxuans)', function (data) { //监听自定义的全选checkbox事件，

                if (data.elem.checked) {

                    selectArr=[];

                    for (let i = 0; i < pages; i++) {
                        selectArr.push(new Array(limit).fill(1));
                    }


                    dataList.forEach(function (item, index) {
                        item.LAY_CHECKED=true;
                    })
                } else {
                    arrInit();

                }

                table.reload('courseTable', {
                    data: dataList
                });

            })

            function arrInit(){
                selectArr=[];
                // for (let i = 0; i < pages; i++) {
                //     selectArr.push(new Array(limit).fill(0));
                // }

                dataList.forEach(function (item, index) {
                    item.LAY_CHECKED=false;
                })
            }





        });

    })



    function getSelectByParentParam(type) {
        let courseYearSemester;
        let examMethod;
        let courseName;
        let courseClassName;
        let courseClassCode;
        //用某学年学期查
        if (type == '1'){
            courseYearSemester = $("#courseYearSemester").val();
            if (courseYearSemester==''){
                type = '5';
                $.ajax({
                    url: "../examination/getSelectByParentParam",
                    data: {courseYearSemester: courseYearSemester, examMethod: examMethod, courseName: courseName, courseClassName: courseClassName, type: type},
                    dataType: 'json',
                    type: 'post',
                    success: function (res) {
                        if (res.code != 200) {
                            console.log("数据加载失败");
                        }
                        let data = res.data;

                        let ksfslist = data.ksfs.list;
                        $("#examMethod").html('<option value="" selected="selected">请选择考试方式</option>');
                        for (let i in ksfslist) {
                            let val = ksfslist[i];
                            $("#examMethod").append('<option value="' + val + '" >' + val + '</option>');
                        }
                        $('#examMethod').trigger("chosen:updated");

                        let kcmclist = data.kcmc.list;
                        $("#courseName").html('<option value="" selected="selected">请选择课程</option>');
                        for (let i in kcmclist) {
                            let val = kcmclist[i];
                            $("#courseName").append('<option value="' + val + '" >' + val + '</option>');
                        }
                        $('#courseName').trigger("chosen:updated");

                        let jxbmclist = data.jxbmc.list;
                        $("#courseClassName").html('<option value="" selected="selected">请选择教学班名称</option>');
                        for (let i in jxbmclist) {
                            let val = jxbmclist[i];
                            $("#courseClassName").append('<option value="' + val + '" >' + val + '</option>');
                        }
                        $('#courseClassName').trigger("chosen:updated");

                        let jxbbhlist = data.jxbbh.list;
                        $("#courseClassCode").html('<option value="" selected="selected">请选择教学班名称</option>');
                        for (let i in jxbbhlist) {
                            let val = jxbbhlist[i];
                            $("#courseClassCode").append('<option value="' + val + '" >' + val + '</option>');
                        }
                        $('#courseClassCode').trigger("chosen:updated");

                    }
                });
                return;
            }
        }
        //用某学年学期、考试方式查
        if (type == '2'){
            courseYearSemester = $("#courseYearSemester").val();
            examMethod = $("#examMethod").val();
            if (courseYearSemester!=''&&examMethod==''){
                type = '6';
                $.ajax({
                    url: "../examination/getSelectByParentParam",
                    data: {courseYearSemester: courseYearSemester, examMethod: examMethod, courseName: courseName, courseClassName: courseClassName, type: type},
                    dataType: 'json',
                    type: 'post',
                    success: function (res) {
                        if (res.code != 200) {
                            console.log("数据加载失败");
                        }
                        let data = res.data;

                        let kcmclist = data.kcmc.list;
                        $("#courseName").html('<option value="" selected="selected">请选择课程</option>');
                        for (let i in kcmclist) {
                            let val = kcmclist[i];
                            $("#courseName").append('<option value="' + val + '" >' + val + '</option>');
                        }
                        $('#courseName').trigger("chosen:updated");

                        let jxbmclist = data.jxbmc.list;
                        $("#courseClassName").html('<option value="" selected="selected">请选择教学班名称</option>');
                        for (let i in jxbmclist) {
                            let val = jxbmclist[i];
                            $("#courseClassName").append('<option value="' + val + '" >' + val + '</option>');
                        }
                        $('#courseClassName').trigger("chosen:updated");

                    }
                });
                return;
            }
        }
        //用某学年学期、考试方式、课程名称查
        if (type == '3'){
            courseYearSemester = $("#courseYearSemester").val();
            examMethod = $("#examMethod").val();
            courseName = $("#courseName").val();
            courseClassName = $("#courseClassName").val();
            if (courseYearSemester!=''&&examMethod!=''&&courseName=='') {
                type = '7';
                $.ajax({
                    url: "../examination/getSelectByParentParam",
                    data: {courseYearSemester: courseYearSemester, examMethod: examMethod, courseName: courseName, courseClassName: courseClassName, type: type},
                    dataType: 'json',
                    type: 'post',
                    success: function (res) {
                        if (res.code != 200) {
                            console.log("数据加载失败");
                        }
                        let data = res.data;

                        let jxbmclist = data.jxbmc.list;
                        $("#courseClassName").html('<option value="" selected="selected">请选择教学班名称</option>');
                        for (let i in jxbmclist) {
                            let val = jxbmclist[i];
                            $("#courseClassName").append('<option value="' + val + '" >' + val + '</option>');
                        }
                        $('#courseClassName').trigger("chosen:updated");

                        let kcmclist = data.kcmc.list;
                        $("#courseName").html('<option value="" selected="selected">请选择课程</option>');
                        for (let i in kcmclist) {
                            let val = kcmclist[i];
                            $("#courseName").append('<option value="' + val + '" >' + val + '</option>');
                        }
                        $('#courseName').trigger("chosen:updated");
                    }
                });
                return;
            }
        }
        //用某学年学期、考试方式、教学班名称查
        if (type == '4'){
            courseYearSemester = $("#courseYearSemester").val();
            examMethod = $("#examMethod").val();
            courseClassName = $("#courseClassName").val();
            courseName = $("#courseName").val();
            if (courseYearSemester!=''&&examMethod!=''&&courseClassName=='') {
                type = '7';
                $.ajax({
                    url: "../examination/getSelectByParentParam",
                    data: {courseYearSemester: courseYearSemester, examMethod: examMethod, courseName: courseName, courseClassName: courseClassName, type: type},
                    dataType: 'json',
                    type: 'post',
                    success: function (res) {
                        if (res.code != 200) {
                            console.log("数据加载失败");
                        }
                        let data = res.data;

                        let kcmclist = data.kcmc.list;
                        $("#courseName").html('<option value="" selected="selected">请选择课程</option>');
                        for (let i in kcmclist) {
                            let val = kcmclist[i];
                            $("#courseName").append('<option value="' + val + '" >' + val + '</option>');
                        }
                        $('#courseName').trigger("chosen:updated");

                        let jxbmclist = data.jxbmc.list;
                        $("#courseClassName").html('<option value="" selected="selected">请选择教学班名称</option>');
                        for (let i in jxbmclist) {
                            let val = jxbmclist[i];
                            $("#courseClassName").append('<option value="' + val + '" >' + val + '</option>');
                        }
                        $('#courseClassName').trigger("chosen:updated");
                    }
                });
                return;
            }
        }
        //用某学年学期、考试方式、教学班编号查
        if (type == '9'){
            courseYearSemester = $("#courseYearSemester").val();
            examMethod = $("#examMethod").val();
            courseClassCode = $("#courseClassCode").val();
            courseName = $("#courseName").val();
            if (courseYearSemester!=''&&examMethod!=''&&courseClassCode=='') {
                type = '7';
                $.ajax({
                    url: "../examination/getSelectByParentParam",
                    data: {courseYearSemester: courseYearSemester, examMethod: examMethod, courseName: courseName, courseClassCode: courseClassCode, type: type},
                    dataType: 'json',
                    type: 'post',
                    success: function (res) {
                        if (res.code != 200) {
                            console.log("数据加载失败");
                        }
                        let data = res.data;

                        let kcmclist = data.kcmc.list;
                        $("#courseName").html('<option value="" selected="selected">请选择课程</option>');
                        for (let i in kcmclist) {
                            let val = kcmclist[i];
                            $("#courseName").append('<option value="' + val + '" >' + val + '</option>');
                        }
                        $('#courseName').trigger("chosen:updated");

                        let jxbmclist = data.jxbmc.list;
                        $("#courseClassName").html('<option value="" selected="selected">请选择教学班名称</option>');
                        for (let i in jxbmclist) {
                            let val = jxbmclist[i];
                            $("#courseClassName").append('<option value="' + val + '" >' + val + '</option>');
                        }
                        $('#courseClassName').trigger("chosen:updated");
                    }
                });
                return;
            }
        }
        $.ajax({
            url: "../examination/getSelectByParentParam",
            data: {courseYearSemester: courseYearSemester, examMethod: examMethod, courseName: courseName, courseClassName: courseClassName, type: type},
            dataType: 'json',
            type: 'post',
            success: function (res) {
                if (res.code != 200) {
                    console.log("数据加载失败");
                }
                let data = res.data;
                if (type=='1' ) {
                    let ksfslist = data.ksfs.list;
                    $("#examMethod").html('<option value="" selected="selected">请选择考试方式</option>');
                    for (let i in ksfslist) {
                        let val = ksfslist[i];
                        $("#examMethod").append('<option value="' + val + '" >' + val + '</option>');
                    }
                    $('#examMethod').trigger("chosen:updated");

                    let kcmclist = data.kcmc.list;
                    $("#courseName").html('<option value="" selected="selected">请选择课程</option>');
                    for (let i in kcmclist) {
                        let val = kcmclist[i];
                        $("#courseName").append('<option value="' + val + '" >' + val + '</option>');
                    }
                    $('#courseName').trigger("chosen:updated");

                    let jxbmclist = data.jxbmc.list;
                    $("#courseClassName").html('<option value="" selected="selected">请选择教学班名称</option>');
                    for (let i in jxbmclist) {
                        let val = jxbmclist[i];
                        $("#courseClassName").append('<option value="' + val + '" >' + val + '</option>');
                    }
                    $('#courseClassName').trigger("chosen:updated");


                    let jxbbhlist = data.jxbbh.list;
                    $("#courseClassCode").html('<option value="" selected="selected">请选择教学班名称</option>');
                    for (let i in jxbbhlist) {
                        let val = jxbbhlist[i];
                        $("#courseClassCode").append('<option value="' + val + '" >' + val + '</option>');
                    }
                    $('#courseClassCode').trigger("chosen:updated");
                }
                if (type=='2' ) {
                    let kcmclist = data.kcmc.list;
                    $("#courseName").html('<option value="" selected="selected">请选择课程</option>');
                    for (let i in kcmclist) {
                        let val = kcmclist[i];
                        $("#courseName").append('<option value="' + val + '" >' + val + '</option>');
                    }
                    $('#courseName').trigger("chosen:updated");

                    let jxbmclist = data.jxbmc.list;
                    $("#courseClassName").html('<option value="" selected="selected">请选择教学班名称</option>');
                    for (let i in jxbmclist) {
                        let val = jxbmclist[i];
                        $("#courseClassName").append('<option value="' + val + '" >' + val + '</option>');
                    }
                    $('#courseClassName').trigger("chosen:updated");


                    let jxbbhlist = data.jxbbh.list;
                    $("#courseClassCode").html('<option value="" selected="selected">请选择教学班名称</option>');
                    for (let i in jxbbhlist) {
                        let val = jxbbhlist[i];
                        $("#courseClassCode").append('<option value="' + val + '" >' + val + '</option>');
                    }
                    $('#courseClassCode').trigger("chosen:updated");
                }

                if (type=='3' && courseClassName == '') {

                    let jxbmclist = data.jxbmc.list;
                    $("#courseClassName").html('<option value="" selected="selected">请选择教学班名称</option>');
                    for (let i in jxbmclist) {
                        let val = jxbmclist[i];
                        $("#courseClassName").append('<option value="' + val + '" >' + val + '</option>');
                    }
                    $('#courseClassName').trigger("chosen:updated");


                    let jxbbhlist = data.jxbbh.list;
                    $("#courseClassCode").html('<option value="" selected="selected">请选择教学班名称</option>');
                    for (let i in jxbbhlist) {
                        let val = jxbbhlist[i];
                        $("#courseClassCode").append('<option value="' + val + '" >' + val + '</option>');
                    }
                    $('#courseClassCode').trigger("chosen:updated");
                }

                if (type=='4' && courseName == '' ) {

                    let kcmclist = data.kcmc.list;
                    $("#courseName").html('<option value="" selected="selected">请选择课程</option>');
                    for (let i in kcmclist) {
                        let val = kcmclist[i];
                        $("#courseName").append('<option value="' + val + '" >' + val + '</option>');
                    }
                    $('#courseName').trigger("chosen:updated");
                }
            }
        });

    }

    function getClassName() {
        let gradeName;
        let campus;
        let dept;
        let zy;
        let kkbm;
        gradeName = $("#gradeName").val();
        campus = $("#campus").val();
        dept = $("#dept").val();
        zy = $("#zy").val();
        kkbm = $("#kkbm").val();
        $.ajax({
            url: "../examination/getClassName",
            data: {gradeName: gradeName, campus: campus, dept: dept, zy: zy, kkbm},
            dataType: 'json',
            type: 'post',
            success: function (res) {
                if (res.code != 200) {
                    console.log("数据加载失败");
                }
                let data = res.data;


                let bjmclist = data.bjmc.list;
                $("#className").html('<option value="" selected="selected">请选择班级名称</option>');
                for (let i in bjmclist) {
                    let val = bjmclist[i];
                    $("#className").append('<option value="' + val + '" >' + val + '</option>');
                }
                $('#className').trigger("chosen:updated");


            }
        });

    }

    function aaa() {

    }

</script>


</body>

</html>