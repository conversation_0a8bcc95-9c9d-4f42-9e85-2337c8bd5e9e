<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" li-content="IE=edge">
    <meta name="viewport" li-content="width=device-width, initial-scale=1.0">
    <title>微点名</title>
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/global.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/layui/css/layui.css'">
    <link rel="stylesheet" href="css/commonPC.css">
    <link rel="stylesheet" href="css/reset.css">
    <link rel="stylesheet" href="css/poup.css">
    <link rel="stylesheet" href="css/microdot-name.css">
    <link rel="stylesheet" href="css/new-microdot-name.css">
    <script th:src="${_CPR_}+'/elective/layui/layui.js'"></script>

</head>

<body>
<div class="main">
    <div class="top">
        <div class="title">
            <div class="sel" style="margin-right:0;">
                <div class="select-input">
                    <div class="name ckd">[[${xzb}]]</div>
                    <em></em>
                    <div class="select-dropdown">
                        <ul class="dropdown-lists dropdown-lists-single">
                            <li th:each="entity,list:${classInfoFormList}" th:class="${list.count} ==1?'cur':''"
                                th:text="${entity?.bjxx_bjmc}" th:attr="xzbCode=${entity?.bjxx_bjbh}"></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="r-title">
            <span class="export" id="export">导出</span>
            <span class="register-for-leave" id="registerQJ">登记请假</span>
        </div>
    </div>
    <div class="con">
        <div class="filter-box">
            <div class="layui-form" action="">
                <div class="layui-form-item">
                    <label class="layui-form-label">姓名</label>
                    <div class="layui-input-block w240">
                        <input type="text" class="layui-input" id="xm" placeholder="请输入学生姓名">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">学号</label>
                    <div class="layui-input-block w240">
                        <input type="text" class="layui-input" id="xh" placeholder="请输入学号">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">日期选择</label>
                    <div class="layui-input-block ">
                        <div class="times w240">
                            <input type="text" name="time" readonly="" placeholder="请选择" class="layui-input"
                                   id="startTime" lay-key="1">
                        </div>
                    </div>
                </div>

            </div>
            <div class="export" id="search">查询</div>
        </div>
        <div class="table" style="display:block;">
            <div class="detail">
                <span><em>正常：</em><i class="color1">0</i></span>
                <span th:each="state,userStat:${rollcallStateList}"><em
                        th:inline="text">[[${state.stateNamePc}]]：</em><i
                        th:class="'color'+${userStat.count+1}">0</i></span>
            </div>
            <div class="tab-cons">
                <table class="layui-table" id="materialTable" lay-filter="materialTable"></table>
            </div>

            <div class="bottom">
                <div class="total">
                    <em>共</em><i>0</i><em>条</em>
                </div>
                <div id="coursePage"></div>
                <div class="refresh"><i></i>刷新</div>
            </div>
        </div>

        <div class="no-data" style="display:none;">
            <img src="images/no-datas.png" alt="">
            <p>当前时间无点名记录</p>
        </div>


    </div>
</div>


</body>
<script type="text/html" id="tmplToolBar">
    <div class="oprate-table">
        <div class="seek" lay-event="seek" style="color:#3A8BFF;;cursor:pointer;">查看详情</div>
    </div>
</script>
<script th:src="${_CPR_}+'/elective/js/jquery-3.3.1.min.js'"></script>
<script th:inline="javascript">
    var xzbCode = [[${xzbCode}]];
    let pageIndex = 1;
    let pageSize = 10;
    var tempcourseTime = [[${courseTime}]];
    var rollcallStateList = [[${rollcallStateList}]];

    layui.use(['table', 'jquery', 'laypage', 'laydate', 'form'], function () {
        var laypage = layui.laypage;
        var table = layui.table;
        var $ = layui.jquery;
        var layer = layui.layer;
        var laydate = layui.laydate;
        var form = layui.form;
        //日期选择
        laydate.render({
            elem: '#startTime',
            format: 'yyyy-MM-dd',
            type: 'date',
            value: [[${courseTime}]],
        });
        $("#search").click(function () {
            pageIndex = 1;
            tableReload()
        })
        getAllNum();
        $("#export").click(function () {
            $.ajax({
                type: "POST",
                url: "/elective/rollcall/isRollCall",
                data: {courseTime: $("#startTime").val(), xzbCode: xzbCode},
                dataType: 'json',
                async: false,
                success: function (data) {
                    if (data.data) {
                        window.open("/elective/rollcall/exportXzbRollcall?xzbCode=" + xzbCode + "&classDate=" + $("#startTime").val())
                    } else {
                        layer.msg("今天还没有点名记录可导出！")
                    }
                }
            });
        })
        $("#registerQJ").click(function () {
            $.ajax({
                type: "get",
                url: "/elective/rollcall/getLeaveUrl",
                dataType: 'json',
                async: false,
                success: function (data) {
                    if (data.data) {
                        window.open(data.data)
                    }
                }
            });
        })

        function tableReload() {
            if (tempcourseTime != $("#startTime").val()) {
                tempcourseTime = $("#startTime").val();
                getAllNum();
            }
            table.reload('materialTable', {
                where: {
                    xzbCode: xzbCode,
                    xm: $("#xm").val(),
                    xh: $("#xh").val(),
                    curPage: pageIndex,
                    pageSize: pageSize
                }
            });
        }

        var titleArr = [{
            field: "xsjbxx_xm",
            align: "center",
            title: "姓名",
            width: 193
        }, {
            field: "xsjbxx_xh",
            align: "center",
            title: "学号",
            width: 360
        }, {
            field: "normal",
            align: "center",
            title: "正常",
            width: 100,
            templet: function (d) {
                return "<span id='stuzc" + d.xsjbxx_uid + "'>0</span>";
            }
        }];

        for (let i = 0; i < rollcallStateList.length; i++) {
            let stateName = "stu_";
            if (rollcallStateList[i].type == 1) {
                stateName += rollcallStateList[i].id;
            } else {
                stateName += rollcallStateList[i].stateAlias;
            }
            titleArr.push({
                field: "options",
                align: "center",
                title: rollcallStateList[i].stateNamePc,
                minWidth: 100,
                templet: function (d) {
                    return "<span id='" + stateName + "_" + d.xsjbxx_uid + "'>0</span>";
                }
            });
        }
        titleArr.push({
            field: "options",
            align: "center",
            title: "操作",
            toolbar: "#tmplToolBar",
            width: 88,
            fixed: "right",
        });
        var table1 = table.render({
            elem: "#materialTable",
            id: 'materialTable',
            url: '/elective/rollcall/pcGetXzbStudents', //数据接口
            where: {
                xzbCode: xzbCode,
                xm: $("#xm").val(),
                xh: $("#xh").val(),
                curPage: pageIndex,
                pageSize: pageSize
            },
            cols: [titleArr],
            done: function (res) {
                $(".total i").text(res.count)
                //分页
                laypage.render({
                    elem: 'coursePage',
                    curr: pageIndex,
                    groups: 5,
                    limit: pageSize,
                    limits: [10, 20, 30],
                    count: res.count,
                    layout: ['prev', 'page', 'next', 'skip', 'limit'],
                    jump: function (obj, first) {
                        if (!first) {
                            pageIndex = obj.curr;
                            pageSize = obj.limit;
                            tableReload()
                        }
                    }
                });
                $.ajax({
                    type: "POST",
                    url: "/elective/rollcall/isRollCall",
                    data: {courseTime: $("#startTime").val(), xzbCode: xzbCode},
                    dataType: 'json',
                    async: false,
                    success: function (data) {
                        if (data.data && res.data) {
                            for (let i = 0; i < res.data.length; i++) {
                                if (res.data[i].xsjbxx_uid) {
                                    getStuNum(res.data[i].xsjbxx_uid);
                                }
                            }
                        }
                    }
                });
            }
        })


        table.on("tool(materialTable)", function (obj) {
            var data = obj.data; //获得当前行数据
            if (obj.event === "seek") {
                window.open("/elective/rollcall/pcXzbDetail?courseTime=" + $("#startTime").val() + "&uid=" + data.xsjbxx_uid + "&xzb=" + $(".ckd").text() + "&uname=" + data.xsjbxx_xm, "_self")
                // location.href = "/elective/rollcall/pcXzbDetail?courseTime="+$("#startTime").val()+"&uid="+data.xsjbxx_uid+"&xzb="+$(".ckd").text()+"&uname="+data.xsjbxx_xm;
            }
        })


        $(".bottom .refresh").click(function () {
            $(this).toggleClass("clicked");
            tableReload();
        })

        computedHh();//she

        var kohh;

        function computedHh() {
            let wrapperTop = $(".tab-cons").offset().top;
            kohh = $(window).height() - wrapperTop - 106;
            $("body .main .con .table .tab-cons").css("min-height", kohh);
        }

        $(window).resize(function () {
            computedHh();
        })


        $(document).on("click", function (event) {
            var _con = $('.select-input');
            if (!_con.is(event.target) && _con.has(event.target).length === 0) {
                $(".select-input").removeClass("clicked");
            }

        })

        //下拉点击
        $(".select-input .name").click(function (e) {
            $(this).parent().toggleClass("clicked");
            stopBubble(e);
        })


        $(".sel .select-input").on("click", ".select-dropdown .dropdown-lists li", function () {
            if ($(this).parent().hasClass("dropdown-lists-single")) {
                let txt = $(this).text();
                $(this).parents(".select-input").find(".name").addClass("ckd").text(txt);
                $(this).addClass('cur').siblings().removeClass('cur');
                $(this).parents(".clicked").toggleClass("clicked");
                xzbCode = $(this).attr("xzbCode");
                getAllNum();
                tableReload();
            } else {
                let txtarr = [];
                $(this).toggleClass("cur");
                $(this).parent().find("li").each(function () {
                    if ($(this).hasClass("cur")) {
                        txtarr.push($(this).find("span").text());
                    }
                })
                let totals = $(this).parents(".dropdown-lists").find("li").length;
                if (txtarr.length == 0) {
                    let nameText = $(this).parents(".select-input").find(".name").attr('data-name');
                    $(this).parents(".select-input").find(".name").removeClass("ckd").text(nameText)
                } else if (totals == txtarr.length) {
                    $(this).parents(".select-dropdown").find(".all-selects").addClass("cur").text("取消全选");
                    $(this).parents(".select-input").find(".name").addClass("ckd").text(txtarr.join(","));
                } else {
                    $(this).parents(".select-dropdown").find(".all-selects").removeClass("cur").text("全选");
                    $(this).parents(".select-input").find(".name").addClass("ckd").text(txtarr.join(","));
                }
            }
        })

        function stopBubble(e) {
            if (e && e.stopPropagation)
                e.stopPropagation();
            else {
                window.event.cancelBubble = true;
            }
        }

        function getStuNum(uid) {
            $.ajax({
                type: "POST",
                url: "/elective/rollcall/getStuNumV2",
                data: {courseTime: $("#startTime").val(), uid: uid},
                dataType: 'json',
                success: function (data) {
                    $("#stuzc" + uid).text(data.data[0]);
                    for (let i = 0; i < rollcallStateList.length; i++) {
                        let stateName = "stu_";
                        if (rollcallStateList[i].type == 1) {
                            stateName += rollcallStateList[i].id;
                        } else {
                            stateName += rollcallStateList[i].stateAlias;
                        }
                        var index3 = parseInt(i) + 1;
                        $("#" + stateName + "_" + uid).text(data.data[index3]);
                    }
                }
            });
        }
    });
    function getAllNum() {
        $.ajax({
            type: "POST",
            url: "/elective/rollcall/getXzbNumV2",
            data: {courseTime: $("#startTime").val(), xzbCode: xzbCode},
            dataType: 'json',
            success: function (data) {
                for (let i = 0; i < data.data.length; i++) {
                    $(".detail").find("span").eq(i).find("i").text(data.data[i]);
                }
            }
        });
    }
</script>

</html>