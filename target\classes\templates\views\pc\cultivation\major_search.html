<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主修专业任务查询</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/global.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/plugin/layui-v2.9.13/layui/css/layui.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/slideCommon.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/layuiReset.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/majorTask.css'}">
    <script th:src="${_CPR_+_VR_+'/plugin/layui-v2.9.13/layui/layui.js'}"></script>
</head>

<body>
<div class="main">
    <div class="m-top">
        <div class="title">主修专业任务落实查询</div>
    </div>
    <div class="search-con">
        <div>
            <div class="layui-inline">
                <label class="layui-form-label mu" style="width: 56px;">任务学年学期</label>
                <div class="layui-input-inline">
                    <div class="j-search-con multiple-box">
                        <input type="text" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">

                            <div class="all-selects" style="margin-top: 8px;">全选</div>
                            <ul id="termList">

                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-inline">
                <!-- <button type="reset" class="layui-btn layui-btn-primary">重置</button> -->
                <button type="submit" class="layui-btn searchBtn" lay-submit="" lay-filter="form-search">查询</button>
            </div>
        </div>
        <button type="submit" class="layui-btn" id="updateData">更新任务落实</button>

    </div>
    <div class="table-box">
        <table class="layui-hide" id="taskList" lay-filter="taskList"></table>
        <!--        <div class="z-check">-->
        <!--            <span class="check" id="checkAllTask"></span>选择全部数据-->
        <!--        </div>-->
        <!--        <div class="selCourse" id="selTextbookCount">已选中<em>0</em>条</div>-->
    </div>
</div>
</body>
<!-- 删除 -->
<script type="text/html" id="detailToolBar">
    <div class="opt-btn" lay-event="detail">查看落实详情</div>
</script>
<script th:src="@{~/js/jquery-3.3.1.min.js}"></script>
<script th:src="${_CPR_+_VR_+'/js/my.util.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/cultivation/slideCommon.js'}"></script>
<script th:inline="javascript">
    const _VR_ = [[${_VR_}]];
    var fid = [[${fid}]]
    var uid = [[${uid}]]
    $(function () {
        $.get("/basic/xnxq", function (res) {
            if (U.su(res)) {
                console.log(res)
                var termHtml = "";
                res.data.forEach(item => {
                    termHtml += '<li>' + item.xnxq_xnxqh + '</li>'
                })
                $("#termList").html(termHtml);
            }
        })
    })
    layui.use(['table', 'jquery', 'laypage', 'form', 'layer'], function () {
        var table = layui.table
        var laypage = layui.laypage
        var $ = layui.jquery,
            form = layui.form,
            layer = layui.layer

        $("#updateData").click(function (res) {
            var checkStatus = table.checkStatus("taskList");
            // var data = checkStatus.data;
            var termList = []
            // data.forEach(item => {
            //     termList.push(item.term)
            // })
            if (checkStatus.data.length > 0) {
                checkStatus.data.forEach(item => {
                    termList.push(item.term)
                })
            } else {
                $("#termList").find("li.active").each((idx, item) => {
                    termList.push($(item).html())
                })
            }
            $.get("/cultivation/implement/updateData", {term: termList.join(","), fid: fid, uid: uid}, function (res) {
                if (U.su(res)) {
                    U.success("正在更新，请稍等")
                } else {
                    U.fail(res.msg)
                }
            })

        })
        /******************************* 专修专业任务落实查询 start ********************************************* */
        var taskTotal = 0
        //选中的数据
        var selTaskAry = []
        $(".searchBtn ").click(function () {
            var termList = []
            $("#termList").find("li.active").each((idx, item) => {
                termList.push($(item).html())
            })
            table.reload("taskList", {where: {term: termList.join(",")}})
        })
        // table渲染
        table.render({
            elem: '#taskList',
            url: '/cultivation/implement/list',
            parseData: function (res) {
                if (U.su(res)) {
                    return {
                        "code": 0, //解析接口状态
                        "msg": res.msg, //解析提示文本
                        "count": res.count, //解析数据长度
                        "data": res.data //解析数据列表
                    }
                } else {
                    return {
                        "code": 1, //解析接口状态
                        "msg": res.msg, //解析提示文本
                        "count": 0, //解析数据长度
                        "data": [] //解析数据列表
                    }
                }
            },
            height: 'full-163',
            cols: [
                [
                    {
                        type: 'checkbox',
                        // fixed: 'left',
                        width: 60
                    },
                    {
                        field: 'term',
                        title: '任务学年学期'
                    },
                    {
                        field: 'needImplement',
                        title: '需落实全部课程数'
                    },
                    {
                        field: 'implemented',
                        title: '已落实课程数'
                    },
                    {
                        field: 'implementHalf',
                        title: '部分落实课程数'
                    },
                    {
                        field: 'noimplement',
                        title: '未落实课程数'
                    }
                    ,
                    {
                        field: 'opt',
                        title: '操作',
                        fixed: 'right',
                        toolbar: '#detailToolBar'
                    }
                ]
            ],
            page: false,
            done: function (res, curr, count) {
                taskTotal = 2
                let table_data = res.data
                setPageCheckAll('classList', table_data, selTaskAry)
            }
        })
        // 详情
        table.on('tool(taskList)', function (obj) {
            if (obj.event == 'detail') {
                window.open('detail?term=' + obj.data.term)
            }
        })
        //操作每行数据复选框
        getSelData('taskList', selTaskAry, true, taskTotal)
        // 选择全部
        $('#checkAllTask').click(function () {
            setCheckedAll('taskList', $(this), taskTotal)
            selTaskAry = []
            getSelData('taskList', selTaskAry, true, taskTotal)
            form.render('checkbox')
        })
        // 确定
        $('#classSureBtn').click(function () {
            //    选择的内容
            layer.closeAll()
            $('#selClass').hide()
            if ($('#checkAllTask').attr('data-sel') == 1) {
                // selTaskAry 为取消选中的数据 （选中了“选择全部数据”，取消了部分选中数据）
            } else {
                // selTaskAry 为选中的数据
            }
        })
        /******************************* 专修专业任务落实查询 end ********************************************* */
        // 设置全选，切换分页选中数据
        /*
         * @param {string} tableId 表格id
         * @param {json} pageData 当前页数据
         * @param {Array} cancelSelTextbookAry 取消选中的数据
         */
        function setPageCheckAll(tableId, pageData, cancelSelTextbookAry) {
            var isSelAll = $('#' + tableId)
                .parent()
                .find('.check')
                .attr('data-sel')
            if (isSelAll == 1) {
                $('div[lay-table-id="' + tableId + '"] input[type=checkbox]').prop(
                    'checked',
                    true
                )
                $('div[lay-table-id="' + tableId + '"] .layui-form-checkbox').addClass(
                    'layui-form-checked'
                )
                if (cancelSelTextbookAry.length > 0) {
                    cancelSelTextbookAry.forEach((item, index) => {
                        var selIndex = pageData.findIndex(item1 => item1.id == item.id)
                        $('div[lay-table-id="' + tableId + '"] thead input[type=checkbox]').prop(
                            'checked',
                            false
                        )
                        $(
                            'div[lay-table-id="' + tableId + '"] thead .layui-form-checkbox'
                        ).removeClass('layui-form-checked')
                        if (selIndex > -1) {
                            var tr = $(
                                'div[lay-table-id="' + tableId + '"] .layui-table-body tbody tr'
                            ).eq(selIndex)
                            tr.find('input[type=checkbox]').prop('checked', false)
                            tr.find('.layui-form-checkbox').removeClass('layui-form-checked')
                        }
                    })
                }
            }
        }

        // 跨页选择数据
        /*
         * @param {string} tableId 表格id
         * @param {Array} selData 选中数据集合
         * @param {number} showLen 显示长度
         * @param {number} totalCount 总数
         */
        function getSelData(tableId, selData, showLen, totalCount) {
            table.on('checkbox(' + tableId + ')', function (obj) {
                if (obj) {
                    var dataSel = $('#' + tableId)
                        .parent()
                        .find('.check')
                        .attr('data-sel')
                    if (showLen && dataSel == 1) {
                        // 选择全部数据，selData为取消选中的数据
                        var checkedStatus = obj.checked
                        if (obj.type == 'one') {
                            if (!checkedStatus) {
                                selData.push(obj.data)
                            } else {
                                var selIndex = selData.findIndex(item => item.id == obj.data.id)
                                if (selIndex > -1) {
                                    selData.splice(selIndex, 1)
                                }
                            }
                        } else if (obj.type == 'all') {
                            var checkStatus = table.checkStatus(tableId).data
                            checkStatus.forEach(element => {
                                var selIndex = selData.findIndex(item => element.id == item.id)
                                if (!checkedStatus) {
                                    if (selIndex < 0) {
                                        selData.push(element)
                                    }
                                } else {
                                    if (selIndex > -1) {
                                        selData.splice(selIndex, 1)
                                    }
                                }
                            })
                        }
                    } else {
                        // 未选择全部数据，selData为选中的数据
                        var checkedStatus = obj.checked
                        if (obj.type == 'one') {
                            if (checkedStatus) {
                                selData.push(obj.data)
                            } else {
                                var selIndex = selData.findIndex(item => item.id == obj.data.id)
                                if (selIndex > -1) {
                                    selData.splice(selIndex, 1)
                                }
                            }
                        } else if (obj.type == 'all') {
                            var checkStatus = table.checkStatus(tableId).data
                            checkStatus.forEach(element => {
                                var selIndex = selData.findIndex(item => element.id == item.id)
                                if (checkedStatus) {
                                    if (selIndex < 0) {
                                        selData.push(element)
                                    }
                                } else {
                                    if (selIndex > -1) {
                                        selData.splice(selIndex, 1)
                                    }
                                }
                            })
                        }
                    }
                    if (showLen) {
                        var tableParent = $('#' + tableId).parent()
                        var selLen = selData.length
                        if (dataSel == 1) {
                            tableParent.find('.selCourse em').text(totalCount - selLen)
                            selLen == 0
                                ? tableParent.find('.check').addClass('checked')
                                : tableParent.find('.check').removeClass('checked')
                        } else {
                            tableParent.find('.selCourse em').text(selLen)
                            totalCount == selLen
                                ? tableParent.find('.check').addClass('checked')
                                : tableParent.find('.check').removeClass('checked')
                        }
                    }
                }
            })
        }

        /* 设置选中全部数据 */

        /*
         * @param {string} tableId 表格id
         * @param {html} checkEle 选中元素
         */
        function setCheckedAll(tableId, checkEle, totalCount) {
            checkEle.toggleClass('checked')
            if (checkEle.hasClass('checked')) {
                checkEle.attr('data-sel', '1')
                $('div[lay-table-id="' + tableId + '"] input[type=checkbox]').prop(
                    'checked',
                    true
                )
                $('div[lay-table-id="' + tableId + '"] .layui-form-checkbox').addClass(
                    'layui-form-checked'
                )
                checkEle.parent().next().find('em').text(totalCount)
            } else {
                checkEle.attr('data-sel', '0')
                $('div[lay-table-id="' + tableId + '"] input[type=checkbox]').prop(
                    'checked',
                    false
                )
                $('div[lay-table-id="' + tableId + '"] .layui-form-checkbox').removeClass(
                    'layui-form-checked'
                )
                checkEle.parent().next().find('em').text(0)
            }
        }
    })
</script>

</html>