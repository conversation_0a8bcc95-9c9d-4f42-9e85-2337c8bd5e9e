<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>操作日志</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/reset.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/slideCommon.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/optLog.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/plugin/layui/css/layui.css'}">
    <script th:src="${_CPR_+_VR_+'/plugin/layui/layui.js'}"></script>
</head>
<body>
<div class="container">
    <div class="top">
        <div class="title">
            <div class="back" id="goBack">返回</div>
            <div class="levelone">培养方案管理</div>
            <div class="icon"></div>
            <div class="leveltwo">专业课设置</div>
            <div class="icon"></div>
            <div class="leveltwo">操作日志</div>
        </div>
    </div>
    <form action="" class="layui-form form-log">
        <div class="layui-inline-wrap">
            <div class="layui-inline">
                <label class="layui-form-label">操作人</label>
                <div class="j-search-con single-box">
                    <input type="text" class="fuzzy-query-input" name="optName" placeholder="请输入" autocomplete="off">
                    <div class="j-select-search">
                        <ul>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">操作时间</label>
                <div class="layui-input-block">
                    <input type="text" placeholder="请选择" id="selDate" autocomplete="off" name="optTime" readonly>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">调整类型</label>
                <div class="j-search-con multiple-box">
                    <input type="text" name="optType" placeholder="请选择" readonly="" class="schoolSel">
                    <span class="j-arrow"></span>
                    <div class="j-select-year">
                        <div class="all-selects">全选</div>
                        <ul name="classroom">
                            <li data-id="1" class="">新增课程</li>
                            <li data-id="2" class="">修改课程</li>
                            <li data-id="3" class="">删除课程</li>
                            <li data-id="4" class="">发布课程</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="layui-inline layui-inline-button">
                <button type="submit" class="layui-btn " lay-submit lay-filter="selTable" id="selTable">筛选</button>
                <button type="reset" class="layui-btn layui-btn-primary clear"> 重置</button>
            </div>
        </div>

    </form>
    <div class="logList">
        <table class="layui-hide" id="logListTable" lay-filter="logList"></table>
    </div>
</div>
</body>
<script th:src="${_CPR_+_VR_+'/js/cultivation/slideCommon.js'}"></script>
<script>
    const _VR_ = "[[${_VR_}]]";
    layui.use(['table', 'jquery', 'form', 'layer', 'laydate'], function () {
        var form = layui.form,
            layer = layui.layer,
            table = layui.table,
            $ = layui.jquery,
            laydate = layui.laydate;
        laydate.render({
            elem: '#selDate' //指定元素
            , type: 'datetime'
            , range: true
            , format: "yyyy-MM-dd HH:mm:ss"
        });
        table.render({
            elem: '#logListTable',
            url: _VR_ + '/cultivation/majorCourseSet/searchOperationLog',
            parseData: function (data) {
                if (data.status) {
                    return {
                        "code": 0, //解析接口状态
                        "msg": data.msg, //解析提示文本
                        "count": data.data.count, //解析数据长度
                        "data": data.data.list //解析数据列表
                    }
                } else {
                    return {
                        "code": 1, //解析接口状态
                        "msg": "数据获取失败", //解析提示文本
                        "count": 0, //解析数据长度
                        "data": [] //解析数据列表
                    }
                }
            },
            height: 'full-210',
            page: {
                prev: '上一页',
                next: '下一页',
                limit: 10,
                layout: ['prev', 'page', 'next', 'skip', 'limit']
            },
            cols: [
                [
                    {
                        field: 'handleName',
                        title: '操作人',
                        align: 'center',
                        minWidth: 144
                    },
                    {
                        field: 'createTime',
                        title: '操作时间',
                        align: 'center'
                    },
                    {
                        field: 'opType',
                        title: '操作类型',
                        align: 'center',
                        minWidth: 154,
                        templet: function (d) {
                            let name = '';
                            if (d.opType) {
                                switch (d.opType) {
                                    case "1":
                                        name = '新增课程';
                                        break;
                                    case "2":
                                        name = '修改课程';
                                        break;
                                    case "3":
                                        name = '删除课程';
                                        break;
                                    case "4":
                                        name = '发布课程';
                                        break;
                                }
                            }
                            return name;
                        }
                    },
                    {
                        field: 'detail',
                        title: '操作内容',
                        align: 'center',
                        minWidth: 90
                    }
                ]
            ],
            done: function (res, curr, count) {

            }
        })
        // 点击筛选
        $("#selTable").click(function () {
            const selParams = {};
            let time = $("#selDate").val();
            if (time) {
                selParams['startTime'] = time.split(' - ')[0];
                selParams['endTime'] = time.split(' - ')[1];
            }
            selParams['handleName'] = $("input[name='optName']").val();
            selParams['opType'] = $(".multiple-box .active").map(function () {
                return $(this).attr("data-id");
            }).get().join(",");
            table.reload('logListTable', {
                where: selParams // 设定新的搜索条件
                , page: {
                    curr: 1 // 重新从第一页开始
                }
            });
            return false;
        })
        $(".clear").click(function () {
            const selParams = {
                startTime: "",
                endTime: "",
                handleName: "",
                opType: ""
            };
            table.reload('logListTable', {where: selParams, page: {curr: 1}});
        })
        // 点击返回
        $("#goBack").click(function () {
            window.history.back()
        })
        $.post(_VR_ + "/cultivation/majorCourseSet/getOperatorList", {}, function (result) {
            if (result.data && result.data.length > 0) {
                let html = '';
                for (let i = 0; i < result.data.length; i++) {
                    let d = result.data[i];
                    html += "<li>" + d.name + "</li>"
                }
                $(".j-select-search ul").html(html);
            }
        }, "json");
    })
</script>

</html>