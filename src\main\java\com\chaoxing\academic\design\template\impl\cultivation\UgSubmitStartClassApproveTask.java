package com.chaoxing.academic.design.template.impl.cultivation;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.chaoxing.academic.constant.RedisKey;
import com.chaoxing.academic.design.template.AbstractUniqueTopBtnTask;
import com.chaoxing.academic.design.template.bo.Bvr;
import com.chaoxing.academic.design.template.bo.FormTopBtnBO;
import com.chaoxing.academic.entity.approve.cultivation.CourseManageApprove;
import com.chaoxing.academic.entity.approve.cultivation.subform.Kkszsp_kksz;
import com.chaoxing.academic.entity.form.basic.ClassInfoForm;
import com.chaoxing.academic.entity.form.basic.CollegesInfoForm;
import com.chaoxing.academic.entity.form.cultivation.TeachPlanForm;
import com.chaoxing.academic.entity.po.cultivation.StartClassApprove;
import com.chaoxing.academic.enums.SearchStrBodyType;
import com.chaoxing.academic.service.cultivation.IStartClassApproveService;
import com.chaoxing.academic.utils.MyUtils;
import com.chaoxing.academic.utils.form.FormUtils;
import com.chaoxing.form.pojo.AprvRowInfo;
import com.chaoxing.form.pojo.IdName;
import com.chaoxing.form.pojo.SearchStrBody;
import com.chaoxing.office.app.entity.forms.dto.data.FormsData;
import com.chaoxing.office.app.entity.forms.vo.response.ApiApproveFlowResponse;
import com.chaoxing.office.app.entity.forms.vo.response.ApiModifyResponse;
import com.chaoxing.office.app.service.api.OfficeApproveApiInvokeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @description 本科提交开课审批任务
 * @date 2025/7/16 10:14
 */
@Slf4j
@Component
public class UgSubmitStartClassApproveTask extends AbstractUniqueTopBtnTask<FormTopBtnBO> {

    // ==================== 常量定义 ====================
    private static final String TEACHING_CLASS_NAME = "teachingClassName";
    private static final String TEACHING_CLASS_TYPE = "teachingClassType";
    private static final String STUDENT_COMPOSITION = "studentComposition";
    private static final String NEED_ARRANGE_COURSE = "needArrangeCourse";
    private static final String NEED_ARRANGE_EXAM = "needArrangeExam";
    private static final String NEED_COURSE_SELECTION = "needCourseSelection";
    private static final String NEED_RECORD_SCORE = "needRecordScore";
    private static final String CLASS_COMPOSE_TYPE = "classComposeType";
    private static final String CLASS_COMPOSITION = "classComposition";
    private static final String CLASS_COMPOSITION_NUMBER = "classCompositionNumber";
    private static final String PARENT_TEACHING_CLASS_INFO = "parentTeachingClassInfo";
    private static final String TEACHING_WEEKS = "teachingWeeks";
    private static final String TEACHING_HOURS = "teachingHours";

    // ==================== 依赖注入 ====================
    @Resource
    private IStartClassApproveService startClassApproveService;

    // ==================== 核心方法 ====================

    /**
     * 验证参数合法性
     */
    @Override
    public Bvr verify(FormTopBtnBO args) {
        try {
            if (args == null || args.getFid() == null || args.getUid() == null) {
                return Bvr.noPass("参数错误");
            }

            List<TeachPlanForm> teachPlans = FormUtils.listAllByQueryId(args.getFid(), args.getUid(), args.getQueryId(),
                    TeachPlanForm.class);
            if (CollectionUtils.isEmpty(teachPlans)) {
                return Bvr.noPass("未找到需要提交的教学计划");
            }

            return Bvr.passAndWait();
        } catch (Exception e) {
            log.error("验证参数失败: {}", e.getMessage(), e);
            return Bvr.noPass("系统异常，请稍后重试");
        }
    }

    /**
     * 获取Redis锁名称
     */
    @Override
    public String lockName(FormTopBtnBO args) {
        return String.format(RedisKey.SUBMIT_START_CLASS_APPROVE_LOCK, args.getFid());
    }

    /**
     * 执行主要业务逻辑
     */
    @Override
    public void handle(FormTopBtnBO args) {
        List<TeachPlanForm> teachPlanList = FormUtils.listAllByQueryId(args.getFid(), args.getUid(), args.getQueryId(),
                TeachPlanForm.class);

        for (TeachPlanForm teachPlan : teachPlanList) {
            if (!isValidTeachPlan(teachPlan)) {
                continue;
            }

            // 处理教学计划
            processTeachPlan(args, teachPlan);

            // 更新审核状态
            updateExamineStatus(args, teachPlan);
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 验证教学计划数据完整性
     */
    private boolean isValidTeachPlan(TeachPlanForm teachPlan) {
        return teachPlan != null &&
                teachPlan.getRowInfo() != null &&
                StringUtils.isNotBlank(teachPlan.getRowInfo().getFormUserId());
    }

    /**
     * 处理单个教学计划
     */
    private void processTeachPlan(FormTopBtnBO args, TeachPlanForm teachPlan) {
        // 查找开班审批记录
        List<CourseManageApprove> approveList = startClassApproveService.getApproveWithStudentCompose(args.getFid(), teachPlan.getRowInfo().getFormUserId());

        if (CollectionUtils.isEmpty(approveList)) {
            return;
        }
        // 处理每条开班审批记录
        for (CourseManageApprove courseManageApprove : approveList) {
            submitApproval(args.getFid(), args.getUid(), courseManageApprove);
        }
    }

    /**
     * 查询关联的开班审批记录
     */
    private List<StartClassApprove> findStartClassApproves(Integer fid, TeachPlanForm teachPlan) {
        try {
            String formUserId = teachPlan.getRowInfo().getFormUserId();

            return startClassApproveService
                    .lambdaQuery()
                    .eq(StartClassApprove::getFid, fid)
                    .eq(StartClassApprove::getFormUserId, formUserId)
                    .list();
        } catch (Exception e) {
            log.error("查询开班审批记录失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 将StartClassApprove转换为CourseManageApprove
     */
    private CourseManageApprove convertToApprove(StartClassApprove startClassApprove, TeachPlanForm teachPlanForm, FormTopBtnBO args) {
        if (startClassApprove == null || teachPlanForm == null) {
            return null;
        }
        CourseManageApprove approve = new CourseManageApprove();
        setBasicProperties(approve, startClassApprove, teachPlanForm);
        if (StringUtils.isNotBlank(startClassApprove.getDetail())) {
            try {
                setDetailProperties(approve, startClassApprove, teachPlanForm);
            } catch (Exception e) {
                log.error("解析StartClassApprove的detail字段失败", e);
            }
        }
        CollegesInfoForm one = FormUtils.getOne(SearchStrBodyType.AND, args.getFid(), CollegesInfoForm.ALIAS, searchStrBody -> {
            searchStrBody.createAndAdd(CollegesInfoForm::getYxsj_yxmc).eq(teachPlanForm.getJxjhgl_ssxb());
        }, CollegesInfoForm.class);
        approve.setKkszsp_kkxq(one != null ? one.getYxsj_ssxq() : null);
        AprvRowInfo rowInfo = new AprvRowInfo();
        rowInfo.setFormUserId(String.valueOf(startClassApprove.getApprovalId()));
        approve.setRowInfo(rowInfo);
        return approve;
    }

    /**
     * 设置基本属性
     */
    private void setBasicProperties(CourseManageApprove approve, StartClassApprove startClassApprove,
                                    TeachPlanForm teachPlanForm) {
        approve.setKkszsp_jxbbh(startClassApprove.getTeachingClassNumber());
        approve.setNumberpeople(startClassApprove.getNumberPeople());
        approve.setStudentCompose(startClassApprove.getStuCompose());
        approve.setKkszsp_jsjhid(teachPlanForm.getRowInfo().getFormUserId());
        approve.setKkszsp_kkxnxq(teachPlanForm.getJxjhgl_xnxq());
        approve.setKkszsp_kkjys(teachPlanForm.getJxjhgl_fasskys());
        approve.setKkszsp_kkbm(teachPlanForm.getJxjhgl_fassxb());
        approve.setKkszsp_nj(teachPlanForm.getJxjhgl_nj());
        approve.setKkszsp_zy(teachPlanForm.getJxjhgl_zy());
        approve.setKkszsp_zybh(teachPlanForm.getJxjhgl_zybh());
        approve.setKkszsp_skxb(teachPlanForm.getJxjhgl_ssxb());
        approve.setKkszsp_kcbh(teachPlanForm.getJxjhgl_kcid());
        approve.setKkszsp_kcmc(teachPlanForm.getJxjhgl_kcmc());
        approve.setKkszsp_kkbm1(teachPlanForm.getJxjhgl_kkxb());
        approve.setKkszsp_kcxz(teachPlanForm.getJxjhgl_kcxz());
        approve.setKkszsp_xbx(teachPlanForm.getJxjhgl_xbx());
        approve.setKkszsp_ksfs(teachPlanForm.getJxjhgl_ksxs());
        approve.setKkszsp_pycc(teachPlanForm.getJxjhgl_pycc());
        approve.setKkszsp_xf(teachPlanForm.getJxjhgl_xf());
    }

    /**
     * 设置详细属性
     */
    private void setDetailProperties(CourseManageApprove approve, StartClassApprove startClassApprove,
                                     TeachPlanForm teachPlanForm) {
        JSONObject detailObj = JSONObject.parseObject(startClassApprove.getDetail());
        // 设置基本信息
        approve.setKkszsp_jxbmc(detailObj.getString(TEACHING_CLASS_NAME));
        approve.setKkszsp_sfpk(detailObj.getString(NEED_ARRANGE_COURSE));
        approve.setKkszsp_sfpaik(detailObj.getString(NEED_ARRANGE_EXAM));
        approve.setKkszsp_sfxk(detailObj.getString(NEED_COURSE_SELECTION));

        // 设置学生组成
        approve.setStudentCompose(detailObj.getString(STUDENT_COMPOSITION));

        // 设置班级组成
        setClassComposition(approve, detailObj, startClassApprove.getFid(), teachPlanForm);

        // 设置父教学班信息
        setParentTeachingClassInfo(approve, detailObj);

        // 创建开课设置子表单
        List<Kkszsp_kksz> kkszList = createKkszList(detailObj);
        if (!kkszList.isEmpty()) {
            approve.setKkszsp_kksz(kkszList);
        }
    }

    /**
     * 设置班级组成信息
     */
    private void setClassComposition(CourseManageApprove approve,
                                     JSONObject detailObj,
                                     Integer fid,
                                     TeachPlanForm teachPlanForm) {
        try {
            setBasicClassComposition(approve, detailObj);
            String compositionNumber = detailObj.getString(CLASS_COMPOSITION_NUMBER);
            if (StringUtils.isNotBlank(compositionNumber)) {
                processClassCompositionNumbers(approve, fid, teachPlanForm, compositionNumber);
            }
        } catch (Exception e) {
            log.error("设置班级组成信息失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 设置基本班级组成信息
     * 优化点：
     * 1. 增加空值检查，避免NPE
     * 2. 优化JSON数组处理逻辑
     * 3. 增加日志记录便于调试
     */
    private void setBasicClassComposition(CourseManageApprove approve, JSONObject detailObj) {
        approve.setKkszsp_jxbzclx(detailObj.getString(CLASS_COMPOSE_TYPE));
        JSONArray compositionArray = detailObj.getJSONArray(CLASS_COMPOSITION);
        if (compositionArray != null && !compositionArray.isEmpty()) {
            approve.setKkszsp_jxbzc(compositionArray.toJavaList(String.class));
        }
        approve.setKkszsp_jxbzcbh(detailObj.getString(CLASS_COMPOSITION_NUMBER));
    }

    /**
     * 处理班级组成编号相关信息
     */
    private void processClassCompositionNumbers(CourseManageApprove approve,
                                                Integer fid,
                                                TeachPlanForm teachPlanForm,
                                                String compositionNumber) {
        String[] compositionNumbers = parseCompositionNumbers(compositionNumber);
        List<ClassInfoForm> classInfoForms = queryClassInfoForms(fid, compositionNumbers);
        List<String> majorCodes = extractAndSetMajorInfo(classInfoForms, approve);
        if (!majorCodes.isEmpty()) {
            queryAndSetTeachPlanDepartments(fid, teachPlanForm, majorCodes, approve);
        }
    }

    /**
     * 解析班级组成编号
     */
    private String[] parseCompositionNumbers(String compositionNumber) {
        if (StringUtils.isBlank(compositionNumber)) {
            return new String[0];
        }
        return Arrays.stream(compositionNumber.split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .toArray(String[]::new);
    }

    /**
     * 提取专业信息并设置到审批对象中
     * 优化点：
     * 1. 增加参数校验
     * 2. 使用Stream API优化代码可读性
     * 3. 增加详细日志记录
     * 4. 优化集合操作性能
     */
    private List<String> extractAndSetMajorInfo(List<ClassInfoForm> classInfoForms, CourseManageApprove approve) {
        if (MyUtils.isEmpty(classInfoForms) || approve == null) {
            return Collections.emptyList();
        }
        Set<String> majorCodeSet = new LinkedHashSet<>();
        Set<String> majorNameSet = new LinkedHashSet<>();
        classInfoForms.forEach(classInfo -> {
            addIfNotBlank(classInfo.getBjxx_zybh(), majorCodeSet);
            addIfNotBlank(classInfo.getBjxx_zy(), majorNameSet);
        });
        List<String> majorCodes = new ArrayList<>(majorCodeSet);
        List<String> majorNames = new ArrayList<>(majorNameSet);
        approve.setKkszsp_zybh1(majorCodes);
        approve.setKkszsp_zy1(majorNames);
        return majorCodes;
    }

    /**
     * 添加非空字符串到集合中
     */
    private void addIfNotBlank(String value, Set<String> set) {
        if (StringUtils.isNotBlank(value)) {
            set.add(value);
        }
    }

    /**
     * 查询班级信息表单
     */
    private List<ClassInfoForm> queryClassInfoForms(Integer fid, String[] compositionNumbers) {
        if (fid == null || compositionNumbers == null || compositionNumbers.length == 0) {
            return Collections.emptyList();
        }
        try {
            return FormUtils.listAll(SearchStrBodyType.AND, fid, ClassInfoForm.ALIAS, searchStrBody -> {
                SearchStrBody searchStrBodyOr = searchStrBody.nestedOr();
                Arrays.stream(compositionNumbers)
                        .filter(StringUtils::isNotBlank)
                        .forEach(code -> searchStrBodyOr.createAndAdd(ClassInfoForm::getBjxx_bjbh).eq(code));
            }, ClassInfoForm.class);
        } catch (Exception e) {
            return Collections.emptyList();
        }
    }

    /**
     * 查询并设置教学计划相关部门信息
     */
    private void queryAndSetTeachPlanDepartments(Integer fid,
                                                 TeachPlanForm teachPlanForm,
                                                 List<String> majorCodes,
                                                 CourseManageApprove approve) {
        try {
            List<TeachPlanForm> teachPlanForms = queryRelatedTeachPlans(fid, teachPlanForm, majorCodes);
            extractAndSetDepartmentInfo(teachPlanForms, approve);
        } catch (Exception e) {
            log.error("查询教学计划部门信息失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 查询相关教学计划
     */
    private List<TeachPlanForm> queryRelatedTeachPlans(Integer fid,
                                                       TeachPlanForm teachPlanForm,
                                                       List<String> majorCodes) {
        return FormUtils.listAll(SearchStrBodyType.AND, fid, TeachPlanForm.ALIAS, searchStrBody -> {
            searchStrBody.createAndAdd(TeachPlanForm::getJxjhgl_xnxq).eq(teachPlanForm.getJxjhgl_xnxq());
            searchStrBody.createAndAdd(TeachPlanForm::getJxjhgl_kcid).eq(teachPlanForm.getJxjhgl_kcid());
            searchStrBody.createAndAdd(TeachPlanForm::getJxjhgl_mzxs).eq(teachPlanForm.getJxjhgl_mzxs());
            searchStrBody.createAndAdd(TeachPlanForm::getJxjhgl_zybh).match(majorCodes.toArray(new String[0]));
        }, TeachPlanForm.class);
    }

    /**
     * 提取并设置部门信息
     */
    private void extractAndSetDepartmentInfo(List<TeachPlanForm> teachPlanForms, CourseManageApprove approve) {
        if (MyUtils.isNotEmpty(teachPlanForms)) {
            Set<String> gradeSet = new LinkedHashSet<>();
            Set<String> deptSet = new LinkedHashSet<>();
            teachPlanForms.forEach(planForm -> {
                addIfNotBlank(planForm.getJxjhgl_nj(), gradeSet);
                addIfNotBlank(planForm.getJxjhgl_ssxb(), deptSet);
            });
            approve.setKkszsp_nj1(new ArrayList<>(gradeSet));
            approve.setKkszsp_skxb1(new ArrayList<>(deptSet));
        }
    }

    /**
     * 设置父教学班信息
     */
    private void setParentTeachingClassInfo(CourseManageApprove approve, JSONObject detailObj) {
        JSONObject parentInfo = detailObj.getJSONObject(PARENT_TEACHING_CLASS_INFO);
        if (parentInfo != null) {
            approve.setCompose(parentInfo.getString("compose"));
            approve.setComposeNo(parentInfo.getString("composeNo"));
        }
    }

    /**
     * 创建开课设置子表单列表（本科版本不按学时分组）
     */
    private List<Kkszsp_kksz> createKkszList(JSONObject detailObj) {
        List<Kkszsp_kksz> kkszList = new ArrayList<>();

        // 创建单个开课设置对象
        Kkszsp_kksz kksz = new Kkszsp_kksz();

        // 设置教师信息
        setTeacherInfo(kksz, detailObj);

        // 设置教室信息
        setClassroomInfo(kksz, detailObj);

        // 设置教学班类型信息
        setTeachingClassInfo(kksz, detailObj);

        // 设置学时信息
        setTeachingHourInfo(kksz, detailObj);

        kkszList.add(kksz);
        return kkszList;
    }

    /**
     * 设置教师信息
     */
    private void setTeacherInfo(Kkszsp_kksz kksz, JSONObject detailObj) {
        // 设置主讲教师信息
        JSONObject teacherInfo = detailObj.getJSONObject("teacherInfo");
        if (teacherInfo != null) {
            kksz.setKkszsp_skjsgh(teacherInfo.getString("teacherNumbers"));
            setIdNameList(teacherInfo.getJSONArray("teacherNames"), kksz::setKkszsp_skjs);
        }

        // 设置助教信息
        JSONObject assistantInfo = detailObj.getJSONObject("assistantInfo");
        if (assistantInfo != null) {
            kksz.setKkszsp_zjjsgh(assistantInfo.getString("assistantNumbers"));
            setIdNameList(assistantInfo.getJSONArray("assistantNames"), kksz::setKkszsp_zj);
        }

        // 设置成绩录入教师信息
        JSONObject scoreTeacherInfo = detailObj.getJSONObject("scoreTeacherInfo");
        if (scoreTeacherInfo != null) {
            setIdNameList(scoreTeacherInfo.getJSONArray("scoreTeacherNames"), kksz::setKkszsp_cjlrls);
        }
    }

    /**
     * 设置IdName列表
     */
    private void setIdNameList(JSONArray array, java.util.function.Consumer<List<IdName>> setter) {
        if (array != null && !array.isEmpty()) {
            List<IdName> idNameList = new ArrayList<>();
            for (int i = 0; i < array.size(); i++) {
                JSONObject object = array.getJSONObject(i);
                idNameList.add(new IdName(object.getInteger("puid"), object.getString("uname")));
            }
            setter.accept(idNameList);
        }
    }

    /**
     * 设置教室信息
     */
    private void setClassroomInfo(Kkszsp_kksz kksz, JSONObject detailObj) {
        JSONObject classroomInfo = detailObj.getJSONObject("classroomInfo");
        if (classroomInfo != null) {
            kksz.setKkszsp_jslx(classroomInfo.getString("classroomType"));
            kksz.setKkszsp_jsmc(classroomInfo.getString("classroomName"));
            kksz.setKkszsp_jsbh(classroomInfo.getString("classroomNumber"));
        }
    }

    /**
     * 设置教学班类型信息
     */
    private void setTeachingClassInfo(Kkszsp_kksz kksz, JSONObject detailObj) {
        // 设置教学班类型
        String teachingClassType = detailObj.getString(TEACHING_CLASS_TYPE);
        kksz.setKkszsp_jxblx(teachingClassType);

        // 设置父教学班信息
        JSONObject parentInfo = detailObj.getJSONObject(PARENT_TEACHING_CLASS_INFO);
        if (parentInfo != null) {
            kksz.setKkszsp_fjxbbh(parentInfo.getString("number"));
            kksz.setKkszsp_fjxbmc(parentInfo.getString("name"));
        }

        // 设置是否录入成绩
        kksz.setKkszsp_sflrcj(detailObj.getString(NEED_RECORD_SCORE));
    }

    /**
     * 设置学时信息
     */
    private void setTeachingHourInfo(Kkszsp_kksz kksz, JSONObject detailObj) {
        JSONObject hourInfo = detailObj.getJSONObject(TEACHING_HOURS);
        if (hourInfo == null) {
            kksz.setKkszsp_xszj(detailObj.getInteger("totalHours"));
            return;
        }

        // 设置基本学时信息
        String weeklyHours = hourInfo.getString("weeklyHours");
        kksz.setKkszsp_zxsap(weeklyHours);
        kksz.setKkszsp_xslx(hourInfo.getString("hourType"));
        kksz.setKkszsp_lpjc(parseInteger(hourInfo, "continuousPeriods"));
        kksz.setKkszsp_llxs(parseInteger(hourInfo, "theoryHours"));
        kksz.setKkszsp_sjxs(parseInteger(hourInfo, "practiceHours"));
        kksz.setKkszsp_syxs(parseInteger(hourInfo, "experimentHours"));
        kksz.setKkszsp_sjjsx(parseInteger(hourInfo, "computerHours"));
        kksz.setKkszsp_qtsh(parseInteger(hourInfo, "otherHours"));

        // 计算教学周数和总学时
        if (StringUtils.isNotBlank(weeklyHours)) {
            int[] result = calculateWeeksAndHours(weeklyHours);
            kksz.setKkszsp_jxzs(String.valueOf(result[0]));
            kksz.setKkszsp_xszj(result[1]);
        } else {
            kksz.setKkszsp_jxzs(detailObj.getString(TEACHING_WEEKS));
            kksz.setKkszsp_xszj(detailObj.getInteger("totalHours"));
        }
    }

    /**
     * 计算教学周数和总学时
     *
     * @param weeklyHours 格式如: "1-1:1,2-2:2"
     * @return [教学周数, 总学时]
     */
    private int[] calculateWeeksAndHours(String weeklyHours) {
        if (StringUtils.isBlank(weeklyHours)) {
            return new int[]{0, 0};
        }

        int totalWeeks = 0;
        int totalHours = 0;

        for (String entry : weeklyHours.split(",")) {
            if (!entry.contains(":")) {
                continue;
            }

            String[] parts = entry.split(":");
            if (parts.length != 2) {
                continue;
            }

            // 获取周学时
            int hourValue;
            try {
                hourValue = Integer.parseInt(parts[1].trim());
            } catch (Exception e) {
                continue;
            }
            String weekRange = parts[0].trim();
            int weekCount = 1;
            if (weekRange.contains("-")) {
                try {
                    String[] range = weekRange.split("-");
                    int start = Integer.parseInt(range[0].trim());
                    int end = Integer.parseInt(range[1].trim());
                    weekCount = end - start + 1;
                } catch (Exception e) {
                    continue;
                }
            }
            totalWeeks += weekCount;
            totalHours += weekCount * hourValue;
        }
        return new int[]{totalWeeks, totalHours};
    }

    /**
     * 解析整数值
     */
    private Integer parseInteger(JSONObject json, String key) {
        try {
            return json.getInteger(key);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 提交审批
     */
    private void submitApproval(Integer fid, Integer uid, CourseManageApprove approve) {
        // 检查参数
        if (fid == null || uid == null || approve == null) {
            log.warn("提交审批参数不完整");
            return;
        }

        try {
            // 检查审批状态
            if (isApprovalAlreadySubmitted(approve.getApprovalId())) {
                log.warn("教学计划对应的开课审批记录[{}]已存在", approve.getApprovalId());
                return;
            }

            // 转换为表单数据
            FormsData formsData = FormUtils.getFormsData(approve);
            if (formsData == null) {
                log.error("转换FormsData失败，教学班: {}", approve.getKkszsp_jxbmc());
                return;
            }

            // 调用审批API
            ApiModifyResponse apiResponse = callApproveApi(fid, uid, formsData);

            // 处理API响应
            if (apiResponse != null && apiResponse.getSuccess()) {
                log.info("成功提交教学班[{}]的开课审批", approve.getKkszsp_jxbmc());
                updateApprovalId(apiResponse, startClassApprove);
            } else {
                String errorMsg = apiResponse != null ? apiResponse.getMsg() : "未知错误";
                log.error("提交教学班[{}]的开课审批失败: {}", approve.getKkszsp_jxbmc(), errorMsg);
            }
        } catch (Exception e) {
            log.error("提交开课审批失败，教学班: {}, 错误: {}", approve.getKkszsp_jxbmc(), e.getMessage(), e);
        }
    }

    /**
     * 检查审批是否已提交
     */
    private boolean isApprovalAlreadySubmitted(Long approvalId) {
        if (approvalId == null) {
            return false;
        }

        try {
            ApiApproveFlowResponse approveFlowDetail = OfficeApproveApiInvokeService.getApproveFlowDetail(approvalId);
            return approveFlowDetail != null &&
                    approveFlowDetail.getData() != null &&
                    approveFlowDetail.getData().getStatusTypeId() != 2;
        } catch (Exception e) {
            log.error("检查审批状态失败, approvalId: {}, 错误: {}", approvalId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 调用审批API
     */
    private ApiModifyResponse callApproveApi(Integer fid, Integer uid, FormsData formsData) {
        try {
            return OfficeApproveApiInvokeService.saveApproveFormsData(
                    fid, null, CourseManageApprove.ALIAS, Long.valueOf(uid),
                    false, formsData, null, null, null, null, null, 1);
        } catch (Exception e) {
            log.error("调用审批API失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 更新审批ID
     */
    private void updateApprovalId(ApiModifyResponse apiResponse, StartClassApprove startClassApprove) {
        if (apiResponse.getData() == null || apiResponse.getData().getFormUserId() == null) {
            log.warn("更新审批ID失败，响应数据为空");
            return;
        }

        Long formUserId = apiResponse.getData().getFormUserId();
        try {
            startClassApproveService.lambdaUpdate()
                    .eq(StartClassApprove::getId, startClassApprove.getId())
                    .set(StartClassApprove::getApprovalId, formUserId)
                    .set(StartClassApprove::getStatus, 0)
                    .update();
        } catch (Exception e) {
            log.error("更新审批ID失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 更新审批状态
     */
    private void updateExamineStatus(FormTopBtnBO args, TeachPlanForm teachPlanForm) {
        if (StringUtils.isNotBlank(teachPlanForm.getJxjhgl_jsapzt())) {
            return;
        }

        FormUtils.update(param -> {
            param.setFid(args.getFid());
            param.setFormAlias(TeachPlanForm.ALIAS);
            param.setFormUserId(teachPlanForm.getRowInfo().getFormUserId());
            param.setOperatorUid(args.getUid());
        }, () -> new TeachPlanForm().setJxjhgl_jsapzt("待审核"));
    }
}
