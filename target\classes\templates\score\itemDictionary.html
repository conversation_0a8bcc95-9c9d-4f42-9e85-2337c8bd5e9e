<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>成绩管理-分项字典表</title>
    <link rel="stylesheet" href="/score/css/global.css">
    <link rel="stylesheet" href="/layui/css/layui.css">
    <link rel="stylesheet" href="/score/css/common.css">
    <link rel="stylesheet" href="/score/css/reset.css">
    <link rel="stylesheet" href="/css/jquery.mCustomScrollbar.css">
    <link rel="stylesheet" href="/score/css/indexRule.css">

</head>

<body>

    <div class="main">
        <div class="content">
            <div class="c-top">
                <h2>成绩分项设置</h2>
            </div>
            <div class="add-lab">
                <span>添加</span>
            </div>
            <div class="tabWrap">
                <table class="layui-table" id="main-table1" lay-filter="dataTable1" lay-data='{page: true}'>
                </table>
            </div>
        </div>
    </div>

    <!-- 添加 -->
    <div id="addPoups" class="addPoups popup">
        <div class="title">
            <div class="name">添加</div>
            <div class="close"></div>
        </div>
        <div class="popup-con">
            <form class="layui-form "  lay-filter="editForm">
            	<input type="hidden" name="id">
            	<input type="hidden" name="pid" value="0">
                <div class="layui-form-item">
                    <label class="layui-form-label">成绩分项代码</label>
                    <div class="layui-input-block w275">
                        <input type="text" name="itemCode" id="addpCode" placeholder="FX001" autocomplete="off" class="layui-input" readonly="readonly">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>成绩分项名称</label>
                    <div class="layui-input-block w275">
                        <input type="text" name="itemName" id="addpName" placeholder="输入内容" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>是否启用成绩分项</label>
                    <div class="layui-input-block" id="addpEnable1">
                        <input type="radio" name="deleteFlag" value="0" title="是" checked="">
                        <input type="radio" name="deleteFlag" value="1" title="否">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>是否启用二级分项</label>
                    <div class="layui-input-block" id="addpEnable2">
                        <input type="radio" name="secondEnable"  value="1" title="是" checked="">
                        <input type="radio" name="secondEnable" value="0" title="否">
                    </div>
                </div>

            </form>
        </div>
        <div class="bottom">
            <button type="reset" class="layui-btn layui-btn-primary exam-cancle">取消</button>
            <button class="layui-btn exam-sure">确定</button>
        </div>

    </div>

    <!-- 二级分项 -->
    <div id="subitem" class="subitem popup">
        <div class="title">
            <div class="name">二级分项</div>
            <div class="close"></div>
        </div>
        <div class="popup-con">
            <div class="add-lab">
                <span>添加</span>
            </div>
            <div class="table">
                <table class="layui-table" id="main-table2" lay-filter="dataTable2">
                </table>
            </div>
        </div>
        <div class="bottom">
            <button type="reset" class="layui-btn layui-btn-primary exam-cancle">取消</button>
            <button class="layui-btn exam-sure">确定</button>
        </div>

    </div>

    <!-- 二级分项添加 -->
    <div id="subitemadd" class="subitemadd popup">
        <div class="title">
            <div class="name">添加</div>
            <div class="close"></div>
        </div>
        <div class="popup-con">
            <form class="layui-form" lay-filter="secondaryEditForm">
            	<input type="hidden" name="pid">
            	<input type="hidden" name="id">
                <div class="layui-form-item">
                    <label class="layui-form-label">二级分项代码</label>
                    <div class="layui-input-block w275">
                        <input type="text" name="itemCode" id="seconCode" placeholder="FX001001" autocomplete="off" class="layui-input" readonly="readonly">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>二级分项名称</label>
                    <div class="layui-input-block w275">
                        <input type="text" name="itemName" id="seconName" placeholder="输入内容" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </form>
        </div>
        <div class="bottom">
            <button type="reset" class="layui-btn layui-btn-primary exam-cancle">取消</button>
            <button class="layui-btn exam-sure">确定</button>
        </div>
    </div>


    <script type="text/html" id="barDemo1">
        {{#  if(d.secondEnable==1){ }}
        <a style="color: #4A7CFE;cursor:pointer;margin-right:8px;" lay-event="itemize">二级分项</a>
        {{#  } }}
        <a style="color:#3A8BFF;;cursor:pointer;margin-right:8px;" lay-event="edit">编辑</a>
        <a style="color:#F33131;cursor:pointer;" lay-event="del">删除</a>
    </script>
    <script type="text/html" id="barDemo3">
        <a style="color:#3A8BFF;;cursor:pointer;margin-right:8px;" lay-event="edit">编辑</a>
        <a style="color:#F33131;cursor:pointer;" lay-event="del">删除</a>
    </script>
    <script type="text/html" id="barDemo4">
        <a style="color:#3A8BFF;;cursor:pointer;margin-right:8px;" lay-event="edit">编辑</a>
        <a style="color:#F33131;cursor:pointer;" lay-event="del">删除</a>
    </script>
    <script src="/js/jquery1.11.1.min.js"></script>
    <script src="/js/jquery.mCustomScrollbar.concat.min.js"></script>
    <script src="/layui/layui.js"></script>
    <script th:inline="javascript">
    	var fid = [[${fid}]];
        $(function () {
            var form, table, laydate, layer;
            layui.use(['form', 'table', 'laydate'],
                function () {
                    var $ = layui.jquery;
                    form = layui.form;
                    table = layui.table;
                    laydate = layui.laydate;
                    layer = layui.layer;

                    //日期选择
                    laydate.render({
                        elem: '#startTimes',
                        format: 'yyyy-MM-dd HH:mm:ss',
                        type: 'datetime',
                    });

                    //日期选择
                    laydate.render({
                        elem: '#endTimes',
                        format: 'yyyy-MM-dd HH:mm:ss',
                        type: 'datetime',
                    });


                    var table2 = table.render({
                        elem: "#main-table1",
                        id: 'main-table1',
                        url:"/api/score/itemDictionary/getList?pid=0&fid="+fid,
                        page: {
                            limit: 10,
                            limits: [10, 20, 30, 40, 50],
                            layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                        },
                        cols: [
                            [{
                                    field: "itemCode",
                                    align: "center",
                                    title: "成绩分项代码",
                                    minWidth: 120
                                },
                                {
                                    field: "itemName",
                                    align: "center",
                                    title: "成绩分项名称",
                                    minWidth: 120
                                },
                                {
                                    field: "deleteFlag",
                                    align: "center",
                                    title: "是否启用成绩分项",
                                    minWidth: 120,
                                    templet: function (d) {
                                        if (d.deleteFlag == "0") {
                                            return '<span class="wstatus correct">是</span>';
                                        }else {
                                            return '<span class="wstatus deny">否</span>';
                                        }
                                    }
                                },
                                {
                                    field: "secondEnable",
                                    align: "center",
                                    title: "是否启用二级分项",
                                    minWidth: 120,
                                    templet: function (d) {
                                        if (d.secondEnable == "1") {
                                            return '<span class="wstatus correct">是</span>';
                                        }else {
                                            return '<span class="wstatus deny">否</span>';
                                        }
                                    }
                                },
                                {
                                    field: "options",
                                    align: "center",
                                    title: "操作",
                                    toolbar: "#barDemo1",
                                    minWidth: 120
                                },
                            ]
                        ],
                        done: function (res) {}
                    })



                    /* 监听工具条 */
                    table.on("tool(dataTable1)", function (obj) {
                        var data = obj.data; //获得当前行数据
                        if (obj.event === "del") {
                            obj.del();
                            layer.msg("删除")
                            $.ajax({
					            type: 'post',
					            dataType: 'json',
					            url: '/api/score/itemDictionary/deleteById?',
					            data: {id:data.id},
					            success: function (res){
					                if (res.code == 200){
					                    table.reload("main-table1",{page: {curr: 1}});
					                }
					            }
					        });
                        } else if (obj.event === "edit") {
                            console.log(data);
                            $("#addPoups .title .name").html("编辑");
                            form.val("editForm", {
                                "itemCode": data.itemCode,
                                "itemName": data.itemName,
                                "deleteFlag":data.deleteFlag,
                                "secondEnable":data.secondEnable,
                                "id":data.id
                            });
                            layer.open({
                                type: 1,
                                title: false,
                                closeBtn: false,
                                shadeClose: true,
                                isOutAnim: true,
                                content: $('#addPoups'),
                                area: ['auto', 'auto'],
                                success: function () {
                                },
                                
                            }, function () {
                            });
                        }else if(obj.event === "itemize"){
                        	$("#subitemadd input[name='pid']").val(data.id);
                            secondaryFn(data.id);
                        }
                    })

                    table.on("tool(dataTable2)", function (obj) {
                        var data = obj.data; //获得当前行数据
                        if (obj.event === "del") {
                            obj.del();
                            layer.msg("删除")
                            $.ajax({
					            type: 'post',
					            dataType: 'json',
					            url: '/api/score/itemDictionary/deleteById?',
					            data: {id:data.id},
					            success: function (res){
					                if (res.code == 200){
					                    table.reload("main-table1",{page: {curr: 1}});
					                }
					            }
					        });
                        } else if (obj.event === "edit") {
                            $("#subitemadd .title .name").text("编辑");
                            $("#subitemadd input[name='id']").val(data.id);
                            console.log(data);
							form.val("secondaryEditForm", {
                                "itemCode": data.itemCode,
                                "itemName": data.itemName,
                                "id":data.id
                            });
                            layer.open({
                                type: 1,
                                title: false,
                                closeBtn: false,
                                shadeClose: true,
                                isOutAnim: true,
                                content: $('#subitemadd'),
                                area: ['auto', 'auto'],
                                success: function () {
                                },
                            }, function () {
                            });
                          
                        }
                    })

                    var table3 = table.render({
                        elem: "#main-table2",
                        id: 'main-table2',
                        url:"/api/score/itemDictionary/getList?fid="+fid,
                        cols: [
                            [{
                                    field: "itemCode",
                                    align: "center",
                                    title: "二级分项代码",
                                },
                                {
                                    field: "itemName",
                                    align: "center",
                                    title: "二级分项名称",
                                },
                                {
                                    field: "options",
                                    align: "center",
                                    title: "操作",
                                    toolbar: "#barDemo4",
                                },
                            ]
                        ],
                        done: function (res) {}
                    })

                    //添加
                    $(".content .add-lab span").on("click", function () {
                        $("#addPoups .title .name").html("添加");
                        var itemCode = "";
                        $.ajax({
				            type: 'get',
				            dataType: 'json',
				            url: '/api/score/itemDictionary/newCode?pid=0',
				            async:false,
				            success: function (res){
				                if (res.code == 200){
				                	itemCode = res.data.itemCode;
				                }
				            }
				        });
                        form.val("editForm", {
                            "itemCode": itemCode,
                            "itemName": '',
                            "deleteFlag":0,
                            "secondEnable":1,
                            "id":''
                        });
                        layer.open({
                            type: 1,
                            title: false,
                            closeBtn: false,
                            shadeClose: true,
                            isOutAnim: true,
                            content: $('#addPoups'),
                            area: ['auto', 'auto'],
                            success: function () {
                            },
                            
                        }, function () {

                        });
                    });
                    //添加确认
					$("#addPoups .bottom .exam-sure").click(function (obj){
						var data = form.val("editForm");
						data['fid'] = fid;
						$.ajax({
				            type: 'get',
				            dataType: 'json',
				            url: '/api/score/itemDictionary/saveEntity',
				            data: data,
				            success: function (res){
				                if (res.code == 200){
				                	$("#addPoups .close").click();
				                    table.reload("main-table1",{page: {curr: 1}});
				                }
				            }
				        });
					});

                    //二级分项
                    function secondaryFn(id){
                        layer.open({
                            type: 1,
                            title: false,
                            closeBtn: false,
                            shadeClose: true,
                            isOutAnim: true,
                            content: $('#subitem'),
                            area: ['auto', 'auto'],
                            success: function () {
                                table.reload('main-table2',{where:{pid:id}});
                            },
                        }, function () {
                           
                        });
                    }

                    //隐藏弹窗
					$('.close,.exam-cancle').on("click", function () {
                        var index = $(this).parents(".layui-layer").attr("times");
                        layer.close(index);
                    })

                    //弹窗确定
                    $("#addPoup .exam-sure").on("click", function () {
                        $("#extra").addClass('wrong');
                    })
					$("#subitem .exam-sure").on("click",function (){
						$("#subitem .close").click();
					});
                    //组件切换
                    $(".main").on("click", ".m-tab ul li", function () {
                        $(this).addClass("cur").siblings().removeClass("cur");
                    })

                    //二级分项弹窗
                    $("#subitem .add-lab span").click(function(){
                        $("#subitemadd .title .name").text("添加");
                        var pid = $("#subitemadd input[name='pid']").val();
                        var itemCode = "";
                        $.ajax({
				            type: 'get',
				            dataType: 'json',
				            url: '/api/score/itemDictionary/newCode?pid='+pid,
				            async:false,
				            success: function (res){
				                if (res.code == 200){
				                	itemCode = res.data.itemCode;
				                }
				            }
				        });
                        form.val("secondaryEditForm", {
                            "itemCode": itemCode,
                            "itemName": '',
                            "id":''
                        });
                        layer.open({
                            type: 1,
                            title: false,
                            closeBtn: false,
                            shadeClose: true,
                            isOutAnim: true,
                            content: $('#subitemadd'),
                            area: ['auto', 'auto'],
                            success: function () {
                            },
                        }, function () {
                        });
                    })
                    //添加确认
					$("#subitemadd .bottom .exam-sure").click(function (obj){
						var data = form.val("secondaryEditForm");
						data['fid'] = fid;
						data['deleteFlag'] = 0;
						$.ajax({
				            type: 'get',
				            dataType: 'json',
				            url: '/api/score/itemDictionary/saveEntity',
				            data: data,
				            success: function (res){
				                if (res.code == 200){
				                	$("#subitemadd .close").click();
				                    table.reload("main-table2",{page: {curr: 1}});
				                }
				            }
				        });
					});
                })
        })
    </script>

</body>

</html>