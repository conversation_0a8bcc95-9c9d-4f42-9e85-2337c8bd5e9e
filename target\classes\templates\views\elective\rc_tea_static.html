<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no,initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <title>教师端-统计</title>
    <script src="/elective/rollcall/js/responsive.js"></script>
    <link rel="stylesheet" href="/elective/rollcall/css/global.css">
    <link rel="stylesheet" href="/elective/rollcall/css/iosSelect.css">
    <link rel="stylesheet" href="/elective/rollcall/css/common.css">
    <link rel="stylesheet" href="/elective/rollcall/css/head.css">
    <link rel="stylesheet" href="/elective/rollcall/css/mescroll.min.css">
    <link rel="stylesheet" href="/elective/rollcall/css/student-statistics.css?v=1">
    <style>
        .stu-list-wrap .class-list .stu .stu-top .inform .name {
            margin-right: 0.28rem;
        }

        .stu-list-wrap .class-list .stu .stu-top .attendance-rate {
            padding-right: 0.42rem;
        }
    </style>
</head>

<body>
<div class="header">
    <div class="head-signal"></div>
    <div class="head-con">
        <a href="javascript:void(0)" onclick="AppUtils.closeView();" class="head-return">
            <img src="/elective/rollcall/images/back-icon1.png" alt="">
        </a>
        <span class="head-title">
        <span th:text="${current.xnxq_xnxqh}"></span>
        <i></i>
        </span>
    </div>
</div>

<div class="search-wrap">
    <div class="search flex">
        <img src="/elective/rollcall/images/search.png" alt="">
        <input type="search" id="keyword" placeholder="搜索教师姓名">
    </div>
</div>
<div class="stu-list-wrap mescroll" id="mescroll">

    <div class="class-list" id="classList">
    </div>
</div>

<!-- 选择学期-->
<div class="choose-semester-dialog dialog-box">
    <div class="w-con active">
        <div class="w-head">
            <h3>点击选择学期</h3>
        </div>
        <div class="w-box">
            <ul id="xnxqdiv">
            </ul>
        </div>

    </div>
</div>

</body>
<script src="/elective/rollcall/js/CXJSBridge.js"></script>
<script src="/elective/rollcall/js/jquery-3.3.1.min.js"></script>
<script src="/elective/rollcall/js/headHeight.js"></script>
<script src="/elective/rollcall/js/mescroll.min.js"></script>
<script type="text/javascript" src="/js/app.utils.js"></script>
<script th:inline="javascript">
    var current = [[${current}]];

    function _jsBridgeReady() {

        jsBridge.postNotification('CLIENT_TOOLBAR_TYPE', {
            'toolbarType': 0
        });

        jsBridge.postNotification('CLIENT_WEB_BOUNCES', {
            "forbiddenFlag": 1
        });

    }

    $.ajax({
        type: "get",
        url: "/elective/rollcall/getSemesters",
        dataType: 'json',
        async: false,
        success: function (data) {
            var html = "";
            if (data.status) {
                for (var i = 0; i < data.data.length; i++) {
                    if (current.xnxq_xnxqh == data.data[i].xnxq_xnxqh) {
                        html += "<li data-id='" + data.data[i].xnxq_xnxqh + "' class='cur'>" + data.data[i].xnxq_xnxqh + "</li>";
                    } else {
                        html += "<li data-id='" + data.data[i].xnxq_xnxqh + "'>" + data.data[i].xnxq_xnxqh + "</li>";
                    }
                }
            }
            $("#xnxqdiv").html(html);
        }
    })

    var search = false;
    $('#keyword').on({
        'compositionstart': function () {
            search = true;
        },
        'compositionend': function () {
            search = false;
            if (!search) {
                mescroll.setPageNum(1);
                mescroll.triggerDownScroll();
            }
        },
        'input propertychange': function () {
            if (!search) {
                mescroll.setPageNum(1);
                mescroll.triggerDownScroll();
            }
        }
    });
    var mescroll = new MeScroll("mescroll", {
        // down: {
        //   auto: true, //是否在初始化完毕之后自动执行下拉回调callback; 默认true
        //   callback: getList //下拉刷新的回调
        // },
        up: {
            auto: true, //是否在初始化时以上拉加载的方式自动加载第一页数据; 默认false
            isBounce: true, //此处禁止ios回弹,解析(务必认真阅读,特别是最后一点): http://www.mescroll.com/qa.html#q10
            callback: getList, //上拉回调,此处可简写; 相当于 callback: function (page) { upCallback(page); }
            htmlNodata: '<p class="upwarp-nodata">-- 没有更多数据了 --</p>',
        }

    });

    function getList(page) {
        $.ajax({
            type: "get",
            url: "/elective/rollcall/getStaticTeaPage",
            data: {
                xnxqh: $(".head-title").find("span").text(),
                teaName: $('#keyword').val(),
                page: page.num
            },
            dataType: 'json',
            success: function (data) {
                var html = "";
                for (let i = 0; i < data.data.length; i++) {
                    html += '<div class="stu" teaUid="'+data.data[i].jsjbxx_uid+'">';
                    html += '<div class="stu-top">';
                    html += '<div class="inform">';
                    html += '<div class="name">'+data.data[i].jsjbxx_xm+'</div>';
                    html += '<div class="studentid">'+data.data[i].jsjbxx_jsgh+'</div>';
                    html += '</div>';
                    html += '<div class="attendance-rate">';
                    html += '<span>提交率：</span>';
                    html += '<em>'+(((parseInt(data.data[i].hasSubRCNum) / parseInt(data.data[i].subRCNum)) * 100).toFixed(1) + "%")+'</em>';
                    html += '</div>';
                    html += '</div>';
                    html += '<div class="stu-con">';
                    html += '<div class="grade">'+data.data[i].jsjbxx_yx+' </div>';
                    html += '</div>';
                    html += '<div class="stu-bottom">';
                    html += '<ul>';
                    html += '<li>应提交点名课程数：<i>'+data.data[i].subRCNum+'</i></li>';
                    html += '<li>已提交点名课程数：<i>'+data.data[i].hasSubRCNum+'</i></li>';
                    html += '</ul>';
                    html += '</div>';
                    html += '</div>';
                }
                if (page.num == 1) {
                    $("#classList").html(html);
                } else {
                    $("#classList").append(html);
                }
                mescroll.endSuccess(data.data.length, data.count);
            }
        })
    }

    $(document).ready(function () {

        setTimeout(function () {
            var stuH = $(window).height() - $(".stu-list-wrap").offset().top;
            $(".stu-list-wrap").css({
                height: stuH + "px"
            });
        }, 500)

        //学期选择
        $(".choose-semester-dialog").on("click", ".w-con .w-box ul li", function (event) {

            $(this).addClass("cur").siblings().removeClass("cur");
            let text = $(this).text();
            $(".head-con .head-title span").text(text);
            $(".choose-semester-dialog").hide();
            $(".head-con .head-title").removeClass("active");
            mescroll.setPageNum(1);
            mescroll.triggerDownScroll();
            event.stopPropagation();
        })


        $(".choose-semester-dialog").on("click", function (event) {
            var _con = $(".w-con");
            if (!_con.is(event.target) && _con.has(event.target).length === 0) {
                $(".choose-semester-dialog").hide();
                $(".head-con .head-title").removeClass("active");
            }
        })

        //跳转

        $(".stu-list-wrap").on('click', ".class-list .stu", function () {
            var webUrl = window.location.origin+"/elective/rollcall/staticIndex/tea/detail?xnxqh="+$(".head-title").find("span").text()
                +"&teaUid="+$(this).attr("teaUid")+"&teaName="+$(this).find(".stu-top .inform .name").text()
                +"&teaCode="+$(this).find(".stu-top .inform .studentid").text()
            // location.href = webUrl;
            jsBridge.postNotification('CLIENT_OPEN_URL', { "toolbarType": 2, "webUrl": webUrl, "loadType": 1 });
        });


        $(".header").on("click", ".head-title span", function (event) {
            $(this).parent().addClass("active");
            $(".choose-semester-dialog").fadeIn();
            event.stopPropagation();
        })


    })
</script>

</html>