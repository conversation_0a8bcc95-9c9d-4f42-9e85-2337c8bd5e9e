<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
    <head>
        <meta charset="UTF-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>竞赛学分-等级管理</title>
        <link rel="stylesheet" th:href="@{~/css/creditManage/global.css}">
        <link rel="stylesheet" th:href="@{~/plugin/layui/css/layui.css}">
        <link rel="stylesheet" th:href="@{~/css/creditManage/common.css}">
        <link rel="stylesheet" th:href="@{~/css/creditManage/reset.css}">
        <link rel="stylesheet" th:href="@{~/css/creditManage/index.css}">
        <link rel="stylesheet" th:href="@{~/css/creditManage/certificateScore.css}">
        <script th:src="@{~/plugin/layui/layui.js}"></script>
    </head>
    <body>
        <div class="main">
            <div class="top">
                <div class="title">
                    <div class="back">返回</div>
                    <div class="levelone">学分参数设置</div>
                    <div class="icon"></div>
                    <div class="leveltwo">竞赛学分类型</div>
                    <div class="icon"></div>
                    <div class="leveltwo">等级管理</div>
                </div>
            </div>
            <div class="con" style="height: 1000px">
                <iframe id="gradeManagerIframe" style="width: 100%;height: 100%;border: none;">
                </iframe>
            </div>
        </div>
    </body>
    <script th:src="@{~/js/jquery-3.3.1.min.js}"></script>
    <script th:src="@{~/js/creditManage/common.js}"></script>
    <script>
        layui.use(['jquery', 'table'], function () {

            // 点击返回按钮，后退
            $(".back").click(function () {
                history.back();
            });

            // 立即执行函数，初始化加载页面数据
            $(function () {
                loadGradeManagerFormInfo();
            });

            // 加载等级管理表单信息
            function loadGradeManagerFormInfo() {
                $.ajax({
                    type: 'get',
                    dataType: 'json',
                    url: '/new/credit/competition/getCompetitionGradeManageFormUrl',
                    success: function (res) {
                        if (res.code == 200) {
                            // window.open("http://" + res.data);
                            let formUrl = window.location.protocol + "//" + res.data;
                            $("#gradeManagerIframe").attr("src", formUrl);
                        } else {
                            layer.msg(res.msg, {icon: 2, time: 2000});
                        }
                    }
                });
            }
        });
    </script>
</html>