<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学生学分查询</title>
    <link rel="stylesheet" th:href="@{~/css/creditManage/global.css}">
    <link rel="stylesheet" th:href="@{~/plugin/layui/css/layui.css}">
    <link rel="stylesheet" th:href="@{~/css/creditManage/common.css}">
    <link rel="stylesheet" th:href="@{~/css/creditManage/reset.css}">
    <link rel="stylesheet" th:href="@{~/css/creditManage/courseScore.css}">
    <link rel="stylesheet" th:href="@{~/css/creditManage/poup.css}">
    <style>
        .layui-table-page {
            text-align: right;
        }
    </style>
</head>

<body>
<div class="main">
    <div class="top">
        <h4>按学生查询学分</h4>
    </div>
    <div class="form-con">
        <form class="layui-form layui-form-stu" action="" lay-filter="courseForm">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">年级</label>
                    <div class="layui-input-inline">
                        <select name="grade" class="grade" lay-search="">
                            <option value="">请选择</option>
                            <option value="1">1年级</option>
                            <option value="2">2年级</option>
                            <option value="3">3年级</option>
                            <option value="4">4年级</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">系部</label>
                    <div class="layui-input-inline">
                        <select name="depth" class="depth" lay-search="">
                            <option value="">请选择</option>
                            <option value="1">系部1</option>
                            <option value="2">系部2</option>
                            <option value="3">系部3</option>
                            <option value="4">系部4</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">专业</label>
                    <div class="layui-input-inline">
                        <select name="stuMajor" class="stuMajor" lay-search="">
                            <option value="">请选择</option>
                            <option value="1">专业1</option>
                            <option value="2">专业2</option>
                            <option value="3">专业3</option>
                            <option value="4">专业4</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">班级</label>
                    <div class="layui-input-inline">
                        <select name="stuClass" class="stuClass" lay-search="">
                            <option value="">请选择</option>
                            <option value="1">1班</option>
                            <option value="2">2班</option>
                            <option value="3">3班</option>
                            <option value="4">4班</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">学生姓名</label>
                    <div class="layui-input-inline">
                        <select name="xsxm" class="stuName" lay-search="">
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">学生学号</label>
                    <div class="layui-input-inline">
                        <select name="xsxh" class="stuXh" lay-search="">
                        </select>
                    </div>
                </div>
                <div class="layui-inline" style="display: none;">
                    <button type="reset" class="layui-btn layui-btn-primary layui-btn-reset">重置</button>
                </div>
            </div>
        </form>
        <div class="form-btn">
            <button class="btn btn-search">查询</button>
            <button class="btn btn-reset">重置</button>
        </div>
    </div>
    <div class="tab-con">
        <div class="tab-btn">
            <div class="opt-btn">
                <div class="btn btn-export">同步学生基本信息</div>
                <div class="btn btn-del" id="record">同步记录</div>
                <div class="btn export" th:if="${fid == 24700}">导出</div>
                <div class="btn exportRecord" th:if="${fid == 24700}">导出记录</div>
            </div>
        </div>
        <div class="course-table">
            <table class="layui-hide" id="courseTable" lay-filter="courseTable"></table>
        </div>
        <div class="no-data" style="display: none;">
            <img th:src="@{~/images/creditManage/no-data.png}" alt="">
        </div>
    </div>
</div>
<div id="prompt" class="prompt popups">
    <div class="popup-con">
        <div class="err-tips">
            <p>请选择要导出的学生数据</p>
            <div class="btn">确定</div>
        </div>
    </div>
</div>

<div id="loading" class="loading popups">
    <div class="popup-con">
        <div class="close"></div>
        <div class="exporting">
            <img class="loading" src="/images/creditManage/loading.png" alt="">
            <p>导出中...</p>
            <p>请稍后至导出记录中查看进度</p>
        </div>
    </div>
</div>

<div id="creditCart" class="schedule-export popups">
    <div class="title">
      <div class="name">请选择学分统计卡导出内容</div>
      <div class="close"></div>
    </div>
    <div class="popup-con">
      <div class="export-cons">
        <div class="sel-row">
          <div class="name">选择学分</div>
          <div class="sel-item" style="margin-bottom:0;">
            <div class="sel select-grade" style="width: 360px;">
              <div class="select-input">
                <div class="name" data-name="请选择">请选择</div>
                <em></em>
                <div class="select-dropdown"  style="width: 360px;">
                  <div class="search">
                      <input type="text" placeholder="搜索">
                      <span></span>
                  </div>
                  <div class="all-selects">全选</div>
                  <ul class="dropdown-lists">
                      
                  </ul>
              </div>
              </div>
            </div>

          </div>

        </div>
      </div>
    </div>
    <div class="bottom">
      <div class="cancle">取消</div>
      <div class="confirm">确定</div>
    </div>
  </div>
</body>
<script th:src="@{~/js/jquery-3.3.1.min.js}"></script>
<script th:src="@{~/plugin/layui/layui.js}"></script>
<script th:src="@{~/js/creditManage/common.js}"></script>
<script th:src="@{~/js/creditManage/ruleSetCommon.js}"></script>
<script type="text/html" id="tmplToolBar">
    <div class="oprates-table">
        <div lay-event="detail">按学期查询学分</div>
    </div>
</script>
<script th:inline="javascript">
    var role = [[${role}]];
    var fid = [[${fid}]];
    var uid = [[${uid}]];
    var xnxq = [[${xnxq}]];
    var table = "";

    var managerCols = [
        {
             type: 'checkbox',
             width: 86,
             fixed: 'left'
         },
        {
            field: 'xm',
            title: '学生姓名',
            align: "center",
            minWidth: 117,
            fixed: 'left',
        }, {
            field: 'xh',
            title: '学生学号',
            align: "center",
            minWidth: 117
        }
    ];

    var adminCols = [ {
        field: 'bj',
        title: '班级',
        align: "center",
        minWidth: 117
    }, {
        field: 'bzrlxr',
        title: '班主任姓名',
        align: "center",
        minWidth: 117
    }];

    function getCols() {
        var clos = managerCols.slice(0)
        // if (role == 1) {
            adminCols.forEach(function (value) {
                clos.push(value)
            })
        // }

        var lastJson = [
            {
                field: 'nj',
                title: '年级',
                align: "center",
                minWidth: 117
            }, {
                field: 'yx',
                title: '院系',
                align: "center",
                minWidth: 117
            }, {
                field: 'zy',
                title: '专业',
                align: "center",
                minWidth: 117
            },
            {
                field: 'xssfzx',
                title: '学生是否在校',
                align: "center",
                minWidth: 117
            }
            , {
                field: 'xsdqzt',
                title: '学生当前状态',
                align: "center",
                minWidth: 117
            },
            {
                field: 'sum',
                title: '已修总学分',
                align: "center",
                minWidth: 117,
            },
            {
                title: "操作",
                width: 150,
                align: "center",
                toolbar: "#tmplToolBar",
                fixed: 'right',
            }]
        lastJson.forEach(function (value) {
            clos.push(value)
        })
        return clos;
    }

    layui.use(['form', 'layedit', 'jquery', 'table', 'upload'], function () {
        table = layui.table;
        var form = layui.form,
            layer = layui.layer,
            layedit = layui.layedit,
            upload = layui.upload,
            $ = layui.jquery;
        $(function () {
            getSearchData();
            getRuleSet();
        })
		

        function getSearchData() {
            let loading = layer.load();
            var data = form.val('courseForm');
            if (role == 0) {
                data["bzruid"] = uid;
            }
            $.get("/new/credit/search/bar2", data, function (res) {
                if (res.code == 200) {
                    var result = res.data;
                    var grade = result.grade == null ? [] : result.grade;
                    var depth = result.depth == null ? [] : result.depth;
                    var stuMajor = result.stuMajor == null ? [] : result.stuMajor;
                    var stuClass = result.stuClass == null ? [] : result.stuClass;
                    var stuName = result.stuName == null ? [] : result.stuName;
                    var stuXh = result.stuXh == null ? [] : result.stuXh;
                    var gradeHtml = "<option value=\"\">请选择</option>";
                    for (let i = 0; i < grade.length; i++) {
                        gradeHtml += "<option value=\"" + grade[i] + "\">" + grade[i] + "</option>";
                    }
                    var depthHtml = "<option value=\"\">请选择</option>";
                    for (let i = 0; i < depth.length; i++) {
                        depthHtml += "<option value=\"" + depth[i] + "\">" + depth[i] + "</option>";
                    }
                    var majorHtml = "<option value=\"\">请选择</option>";
                    for (let i = 0; i < stuMajor.length; i++) {
                        majorHtml += "<option value=\"" + stuMajor[i] + "\">" + stuMajor[i] + "</option>";
                    }
                    var classHtml = "<option value=\"\">请选择</option>";
                    for (let i = 0; i < stuClass.length; i++) {
                        classHtml += "<option value=\"" + stuClass[i] + "\">" + stuClass[i] + "</option>";
                    }

                    var nameHtml = "<option value=\"\">请选择</option>";
                    for (let i = 0; i < stuName.length; i++) {
                        nameHtml += "<option value=\"" + stuName[i] + "\">" + stuName[i] + "</option>";
                    }
                    var xhHtml = "<option value=\"\">请选择</option>";
                    for (let i = 0; i < stuXh.length; i++) {
                        xhHtml += "<option value=\"" + stuXh[i] + "\">" + stuXh[i] + "</option>";
                    }

                    $(".grade").html(gradeHtml);
                    $(".depth").html(depthHtml);
                    $(".stuMajor").html(majorHtml);
                    $(".stuClass").html(classHtml);
                    $(".stuName").html(nameHtml);
                    $(".stuXh").html(xhHtml);

                    updateForm("courseForm")
                    layer.close(loading);
                }
            })
        }
		function getRuleSet() {
            $.get("/new/creditRuleSet/getRule", {xnxq:xnxq}, function (res) {
                if (res.code == 200) {
                    var result = res.data;
					result.forEach(function(info) {
	                    $(".dropdown-lists").append("<li data-id='"+info.id+"'><span>"+info.creditName+"</span></li>");
					});
                }
            })
        }
        function updateForm(name) {
            form.render("select", name);
        }

        // 查询
        $(".btn-search").click(function () {
            updateTable();
        })

        // 重置
        $(".btn-reset").click(function () {
            $(".layui-btn-reset").click();
            updateTable();
        });

        function searchData() {
            var data = form.val('courseForm');
            if (role == 0) {
                data["bzruid"] = uid;
            }
            return data;
        }

        // table 
        table.render({
            elem: '#courseTable',
            url: '/new/credit/search/getCreditData',
            where: searchData(),
            parseData: function (res) {
                if (res.code == 200) {
                    return {
                        "code": 0, //解析接口状态
                        "msg": res.msg, //解析提示文本
                        "count": res.data.total, //解析数据长度
                        "data": res.data.records //解析数据列表
                    }
                } else {
                    return {
                        "code": 1, //解析接口状态
                        "msg": "数据获取失败", //解析提示文本
                        "count": 0, //解析数据长度
                        "data": [] //解析数据列表
                    }
                }
            },
            height: 'full-350',
            cols: [getCols()],
            page: {
                layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                curr: 1
            }
        });

        table.on('tool(courseTable)', function (obj) {
            var data = obj.data;
            if (obj.event === 'detail') {
                window.location.href = "/new/credit/search/detail.html?fid=" + data.fid + "&uid=" + data.uid;
            }
        })

        function updateTable() {
            table.reload('courseTable', {
                elem: '#courseTable',
                url: '/new/credit/search/getCreditData',
                where: searchData(),
                parseData: function (res) {
                    if (res.code == 200) {
                        return {
                            "code": 0, //解析接口状态
                            "msg": res.msg, //解析提示文本
                            "count": res.data.total, //解析数据长度
                            "data": res.data.records //解析数据列表
                        }
                    } else {
                        return {
                            "code": 1, //解析接口状态
                            "msg": "数据获取失败", //解析提示文本
                            "count": 0, //解析数据长度
                            "data": [] //解析数据列表
                        }
                    }

                },
                height: 'full-350',
                cols: [getCols()],
                page: {
                    layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                    curr: 1
                }
            })
        }

        $(".btn-export").click(function () {
            $.get("/new/credit/search/sync/xsjbxx", {fid:fid,uid:uid}, function (res) {
                if (res.code == 200) {
                    layer.msg(res.msg, {icon: 1, time: 2000});
                } else {
                    layer.msg(res.msg, {icon: 2, time: 2000});
                }
            })
        })
        /* 导出 */
        let selState=false;
        table.on('checkbox(courseTable)', function (obj) {
            var checkStatus = table.checkStatus('courseTable').data.length; 
             if(checkStatus>0){
                selState=true;
             }else{
                selState=false;
             }
        });
        //导出提示
        $(".main .tab-con .opt-btn .btn.export").click(function(){
            if(!selState){
                layer.open({
	                type: 1,
	                title: false,
	                closeBtn: false,
	                shadeClose: true,
	                isOutAnim: true,
	                content: $("#prompt"),
	                area: ["auto", "auto"],
	                success: function () {
	                  
	                },
	            });
            }else{
            	layer.open({
	                type: 1,
	                title: false,
	                closeBtn: false,
	                shadeClose: true,
	                isOutAnim: true,
	                content: $("#creditCart"),
	                area: ["auto", "auto"],
	                success: function () {},
	            });
            }
        });
        
        /* $("body .main .con .btn-list .btn.synchronize-records").click(function(){
            layer.open({
                type: 1,
                title: false,
                closeBtn: false,
                shadeClose: true,
                isOutAnim: true,
                content: $("#creditCart"),
                area: ["auto", "auto"],
                success: function () {},
            });
        }) */
        //错误确定
        $("#prompt .popup-con .err-tips .btn").click(function(){
            let index = $(this).parents(".layui-layer").attr("times");
            layer.close(index);
        })
        //选择学分统计卡确定
        $("#creditCart .bottom div.confirm").click(function(){
            let state=$("#creditCart .popup-con .export-cons  .select-input .name").hasClass("ckd");
            if(!state){
                layer.msg("请选择学分类型");
                return false;
            }
            let index = $(this).parents(".layui-layer").attr("times");
            layer.close(index);
            var queryId = "";
            var creditIds = "";
           	table.checkStatus('courseTable').data.forEach(function(info) {
           		if(queryId == ''){
           			queryId = info.id;
           		}else{
           			queryId += "," + info.id;
           		}
           	});
           	$("#creditCart .dropdown-lists li.cur").each(function(){
           		if(creditIds == ''){
           			creditIds = $(this).attr("data-id");
           		}else{
           			creditIds += "," + $(this).attr("data-id");
           		}
           	});
           	$.get("/new/credit/search/expStuCard", {fid: fid, uid: uid,queryId:queryId,creditIds:creditIds}, function (res) {
            });
            layer.open({
               type: 1,
               title: false,
               closeBtn: false,
               shadeClose: true,
               isOutAnim: true,
               content: $("#loading"),
               area: ["auto", "auto"],
               success: function () {
                 
               },
           });
        })
        $(".popups .popup-con .close,.popups .bottom div.cancle,.popups .title .close").click(function(){
            let index = $(this).parents(".layui-layer").attr("times");
            layer.close(index);
        })
        /* 导出  */
    })
    $("#record").click(function () {
        layui.use(["form", "table"], function () {
            layer.open({
                type: 2,  // 2表示弹出的是iframe，1表示弹出的是层
                offset: 'auto',
                title: false,
                area: ['858px', '560px'],
                scrollbar: true,
                content: "/sync/record/list.html?fid="+fid+"&tableName=t_credit_summary",   // 弹出iframe的页面链接
                btn: '',
                shade: 0.3 //显示遮罩
            });
        })
    })
    $(".exportRecord").click(function () {
        layui.use(["form", "table"], function () {
            layer.open({
                type: 2,  // 2表示弹出的是iframe，1表示弹出的是层
                offset: 'auto',
                title: false,
                area: ['858px', '560px'],
                scrollbar: true,
                content: "/downloadCenter/list.html?fid="+fid+"&uid="+uid+"&formId=8",   // 弹出iframe的页面链接
                btn: '',
                shade: 0.3 //显示遮罩
            });
        })
    })
</script>

</html>