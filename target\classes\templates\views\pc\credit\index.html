<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学分管理</title>
    <link rel="stylesheet" th:href="@{../css/credit/global1.css}">
    <link rel="stylesheet" th:href="@{../plugin/layui/css/layui.css}">
    <link rel="stylesheet" th:href="@{../css/credit/reset.css}">
    <link rel="stylesheet" th:href="@{../css/credit/index.css}">
    <script th:src="@{../plugin/layui/layui.js}"></script>

</head>

<body>
<form class="layui-form" action="" method="post">
    <input type="hidden" name="id" value=""/>
    <div class="j-material-wrap">
        <div class="j-material">
            <div class="j-title">
                <h4>学分基础规则设置</h4>
                <div class="j-btns">
                    <button class="btn-complate" lay-filter="btn-complate" lay-submit="">保存设置</button>
                </div>
            </div>

            <div class="j-material-lable">
                <h4>标准学分规则设置</h4>
                <div class="row">
                    <div class="tit">标准过程学分=</div>
                    <div class="input">
                        <input min="0" style="width:80px;" value="" class="layui-input" name="standardProcessCredit"
                               lay-verify="required|num" lay-verType="tips" placeholder="请输入" autocomplete="off">
                    </div>
                    <div class="inform">%标准学分</div>
                </div>
                <div class="row">
                    <div class="tit">标准考核学分=</div>
                    <div class="input">
                        <input min="0" style="width:80px;" value="" class="layui-input" name="standardExamineCredit"
                               lay-verify="required|num" lay-verType="tips" placeholder="请输入" autocomplete="off">
                    </div>
                    <div class="inform">%标准学分</div>
                </div>
            </div>

            <div class="j-material-lable">
                <h4>学业积分规则设置</h4>
                <div class="j-lable">
                    <div class="lab">
                        <div class="name">学业积分初始化分数</div>
                        <div class="input">
                            <input min="0" style="width:240px;" value="" class="layui-input"
                                   name="academicInitCredit"
                                   lay-verify="required|num" lay-verType="tips" placeholder="请输入" autocomplete="off">
                        </div>
                    </div>
                    <div class="lab">
                        <div class="name">学业积分评价开始时间</div>
                        <div class="input time">
                            <input type="text" style="width:240px;" readonly=""
                                   placeholder="请选择"
                                   th:value="${academicYearSemesterForm?.xnxq_xqkssj}==null?'':${#dates.format(academicYearSemesterForm.xnxq_xqkssj, 'yyyy-MM-dd')}"
                                   class="layui-input" id="startTime" name="startTime" lay-verType="tips"
                                   lay-verify="required">
                        </div>
                    </div>
                    <div class="lab">
                        <div class="name">学业积分评价截止时间</div>
                        <div class="input time">
                            <input type="text" style="width:240px;" readonly=""
                                   placeholder="请选择"
                                   th:value="${academicYearSemesterForm?.xnxq_xqjssj}==null?'':${#dates.format(academicYearSemesterForm.xnxq_xqjssj, 'yyyy-MM-dd')}"
                                   class="layui-input" id="endTime" name="endTime" lay-verType="tips"
                                   lay-verify="required">
                        </div>
                    </div>
                </div>
            </div>

            <div class="j-material-lable" type="1">
                <h4>正考学分规则设置</h4>
                <div class="add-oprate">
                    <h5>过程学分计算规则</h5>
                    <div class="add add1">添加</div>
                </div>

                <div class="j-table" type="1">
                    <div class="j-head">
                        <div class="j-th">成绩区间</div>
                        <div class="j-th">正考过程学分得分</div>
                        <div class="j-th">操作</div>
                    </div>
                    <div class="j-body">
                        <div class="j-tr">
                            <div class="j-td">
                                <div class="oprate">
                                    <div class="inputs left-inp">
                                        <input class="input inp layui-input" placeholder="请输入分值">
                                        <div class="error">请重新输入</div>
                                        <div class="select-input">
                                            <div class="name ckd"><=</div>
                                            <em></em>
                                            <div class="select-dropdown">
                                                <ul class="dropdown-list">
                                                    <li>></li>
                                                    <li>>=</li>
                                                    <li>=</li>
                                                    <li>
                                                        <
                                                    </li>
                                                    <li class="cur"><=
                                                    </li>
                                                    <li>为空</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="select-input score-sel">
                                        <div class="name">请选择</div>
                                        <em></em>
                                        <div class="select-dropdown ">
                                            <ul class="dropdown-list">
                                                <li class="cur">学业积分</li>
                                            </ul>
                                        </div>
                                        <div class="errorMsg">请重新输入</div>
                                    </div>
                                    <div class="inputs right-inp">

                                        <div class="select-input">
                                            <div class="name"><=</div>
                                            <em></em>
                                            <div class="select-dropdown ">
                                                <ul class="dropdown-list">
                                                    <li>></li>
                                                    <li>>=</li>
                                                    <li>=</li>
                                                    <li>
                                                        <
                                                    </li>
                                                    <li class="cur"><=
                                                    </li>
                                                    <li>为空</li>
                                                </ul>
                                            </div>
                                        </div>
                                        <input class="input inp layui-input" placeholder="请输入分值">
                                        <div class="error">请重新输入</div>

                                    </div>
                                </div>
                                <div class="total">学业积分 >= 80</div>
                            </div>
                            <div class="j-td">
                                <div class="oprate">
                                    <div class="txt">正考过程学分=</div>
                                    <div class="input inp-lay">
                                        <input class="input inp layui-input" placeholder="请输入系数值">
                                        <div class="error">请重新输入</div>
                                    </div>
                                    <div class="symbol">*</div>
                                    <div class="select-input score-sel">
                                        <div class="name">请选择</div>
                                        <em></em>
                                        <div class="select-dropdown ">
                                            <ul class="dropdown-list">
                                                <li class="cur">标准学分</li>
                                                <li>标准过程学分</li>
                                            </ul>
                                        </div>
                                        <div class="errorMsg">请重新输入</div>
                                    </div>
                                </div>
                                <div class="total">正考过程学分=2*标准过程学分</div>
                            </div>

                            <div class="j-td">
                                <div class="oprate">
                                    <span class="preservation">保存</span>
                                </div>
                                <div class="total">
                                    <span class="edit">编辑</span>
                                    <span class="delet">删除</span>
                                </div>

                            </div>

                        </div>

                    </div>
                </div>
                <div class="add-oprate">
                    <h5>考核学分计算规则</h5>
                    <div class="add add2">添加</div>
                </div>

                <div class="j-table" type="2" style="margin-bottom:0;">
                    <div class="j-head">
                        <div class="j-th">成绩区间</div>
                        <div class="j-th">正考考核学分得分</div>
                        <div class="j-th">操作</div>
                    </div>
                    <div class="j-body">

                        <div class="j-tr">
                            <div class="j-td">
                                <div class="oprate">
                                    <div class="inputs left-inp">
                                        <input class="input inp layui-input" placeholder="请输入分值">
                                        <div class="error">请重新输入</div>
                                        <div class="select-input">
                                            <div class="name ckd"><=</div>
                                            <em></em>
                                            <div class="select-dropdown">
                                                <ul class="dropdown-list">
                                                    <li>></li>
                                                    <li>>=</li>
                                                    <li>=</li>
                                                    <li>
                                                        <
                                                    </li>
                                                    <li class="cur"><=
                                                    </li>
                                                    <li>为空</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="select-input score-sel">
                                        <div class="name">请选择</div>
                                        <em></em>
                                        <div class="select-dropdown ">
                                            <ul class="dropdown-list">
                                                <li class="cur">正考考核成绩</li>
                                            </ul>
                                        </div>
                                        <div class="errorMsg">请重新输入</div>
                                    </div>
                                    <div class="inputs right-inp">

                                        <div class="select-input">
                                            <div class="name"><=</div>
                                            <em></em>
                                            <div class="select-dropdown ">
                                                <ul class="dropdown-list">
                                                    <li>></li>
                                                    <li>>=</li>
                                                    <li>=</li>
                                                    <li>
                                                        <
                                                    </li>
                                                    <li class="cur"><=
                                                    </li>
                                                    <li>为空</li>
                                                </ul>
                                            </div>
                                        </div>
                                        <input class="input inp layui-input" placeholder="请输入分值">
                                        <div class="error">请重新输入</div>

                                    </div>
                                </div>
                                <div class="total">80 <= 正考考核成绩 ＜ 90</div>
                            </div>
                            <div class="j-td">
                                <div class="oprate">
                                    <div class="txt">正考考核学分=</div>
                                    <div class="input inp-lay">
                                        <input class="input inp layui-input" placeholder="请输入系数值">
                                        <div class="error">请重新输入</div>
                                    </div>
                                    <div class="symbol">*</div>
                                    <div class="select-input score-sel">
                                        <div class="name">请选择</div>
                                        <em></em>
                                        <div class="select-dropdown ">
                                            <ul class="dropdown-list">
                                                <li class="cur">标准学分</li>
                                                <li>标准考核学分</li>
                                            </ul>
                                        </div>
                                        <div class="errorMsg">请重新输入</div>
                                    </div>
                                </div>
                                <div class="total">正考考核学分=2*标准考核学分</div>
                            </div>

                            <div class="j-td">
                                <div class="oprate">
                                    <span class="preservation">保存</span>
                                    <span class="delet">删除</span>
                                </div>
                                <div class="total">
                                    <span class="edit">编辑</span>
                                    <span class="delet">删除</span>
                                </div>

                            </div>

                        </div>
                    </div>
                </div>
            </div>
            <div class="j-material-lable" type="2">
                <h4>重修学分规则设置</h4>
                <div class="add-oprate">
                    <h5>过程学分计算规则</h5>
                    <!--<div class="add add3">添加</div>-->
                </div>

                <div class="j-table" type="1">
                    <div class="j-head">
                        <div class="j-th">成绩区间</div>
                        <div class="j-th">重修过程学分得分</div>
                        <div class="j-th">操作</div>
                    </div>
                    <div class="j-body">
                        <div class="j-tr">
                            <div class="j-td">
                                <div class="oprate">
                                    <div class="txt level">及格</div>
                                </div>
                                <div class="total">及格</div>
                            </div>
                            <div class="j-td">
                                <div class="oprate">
                                    <div class="txt">重修过程学分=</div>
                                    <div class="input inp-lay">
                                        <input class="input inp layui-input" placeholder="请输入系数值">
                                        <div class="error">请重新输入</div>
                                    </div>
                                    <div class="symbol">*</div>
                                    <div class="select-input score-sel">
                                        <div class="name">请选择</div>
                                        <em></em>
                                        <div class="select-dropdown ">
                                            <ul class="dropdown-list">
                                                <li class="cur">标准学分</li>
                                                <li>标准考核学分</li>
                                            </ul>
                                        </div>
                                        <div class="errorMsg">请重新输入</div>
                                    </div>
                                </div>
                                <div class="total">重修考核学分=2*标准考核学分</div>
                            </div>

                            <div class="j-td">
                                <div class="oprate">
                                    <span class="preservation">保存</span>
                                </div>
                                <div class="total">
                                    <span class="edit">编辑</span>
                                    <span class="delet">删除</span>
                                </div>
                            </div>
                        </div>
                        <div class="j-tr">
                            <div class="j-td">
                                <div class="oprate">
                                    <div class="txt level">不及格</div>
                                </div>
                                <div class="total">不及格</div>
                            </div>
                            <div class="j-td">
                                <div class="oprate">
                                    <div class="txt">重修过程学分=</div>
                                    <div class="input inp-lay">
                                        <input class="input inp layui-input" placeholder="请输入系数值">
                                        <div class="error">请重新输入</div>
                                    </div>
                                    <div class="symbol">*</div>
                                    <div class="select-input score-sel">
                                        <div class="name">请选择</div>
                                        <em></em>
                                        <div class="select-dropdown ">
                                            <ul class="dropdown-list">
                                                <li class="cur">标准学分</li>
                                                <li>标准考核学分</li>
                                            </ul>
                                        </div>
                                        <div class="errorMsg">请重新输入</div>
                                    </div>
                                </div>
                                <div class="total">重修考核学分=2*标准考核学分</div>
                            </div>

                            <div class="j-td">
                                <div class="oprate">
                                    <span class="preservation">保存</span>
                                </div>
                                <div class="total">
                                    <span class="edit">编辑</span>
                                    <span class="delet">删除</span>
                                </div>

                            </div>

                        </div>
                        <div class="j-tr">
                            <div class="j-td">
                                <div class="oprate">
                                    <div class="txt level">重修替代及格</div>
                                </div>
                                <div class="total">重修替代及格</div>
                            </div>
                            <div class="j-td">
                                <div class="oprate">
                                    <div class="txt">重修过程学分=</div>
                                    <div class="input inp-lay">
                                        <input class="input inp layui-input" placeholder="请输入系数值">
                                        <div class="error">请重新输入</div>
                                    </div>
                                    <div class="symbol">*</div>
                                    <div class="select-input score-sel">
                                        <div class="name">请选择</div>
                                        <em></em>
                                        <div class="select-dropdown ">
                                            <ul class="dropdown-list">
                                                <li class="cur">标准学分</li>
                                                <li>标准考核学分</li>
                                            </ul>
                                        </div>
                                        <div class="errorMsg">请重新输入</div>
                                    </div>
                                </div>
                                <div class="total">重修考核学分=2*标准考核学分</div>
                            </div>

                            <div class="j-td">
                                <div class="oprate">
                                    <span class="preservation">保存</span>
                                </div>
                                <div class="total">
                                    <span class="edit">编辑</span>
                                    <span class="delet">删除</span>
                                </div>

                            </div>

                        </div>
                    </div>
                </div>
                <div class="add-oprate">
                    <h5>考核学分计算规则</h5>
                    <!--<div class="add add4">添加</div>-->
                </div>

                <div class="j-table" type="2" style="margin-bottom:0;">
                    <div class="j-head">
                        <div class="j-th">成绩区间</div>
                        <div class="j-th">重修考核学分得分</div>
                        <div class="j-th">操作</div>
                    </div>
                    <div class="j-body">
                        <div class="j-tr">
                            <div class="j-td">
                                <div class="oprate">
                                    <div class="txt level">及格</div>
                                </div>
                                <div class="total">及格</div>
                            </div>
                            <div class="j-td">
                                <div class="oprate">
                                    <div class="txt">重修考核学分=</div>
                                    <div class="input inp-lay">
                                        <input class="input inp layui-input" placeholder="请输入系数值">
                                        <div class="error">请重新输入</div>
                                    </div>
                                    <div class="symbol">*</div>
                                    <div class="select-input score-sel">
                                        <div class="name">请选择</div>
                                        <em></em>
                                        <div class="select-dropdown ">
                                            <ul class="dropdown-list">
                                                <li class="cur">标准学分</li>
                                                <li>标准考核学分</li>
                                            </ul>
                                        </div>
                                        <div class="errorMsg">请重新输入</div>
                                    </div>
                                </div>
                                <div class="total">重修考核学分=2*标准考核学分</div>
                            </div>

                            <div class="j-td">
                                <div class="oprate">
                                    <span class="preservation">保存</span>
                                </div>
                                <div class="total">
                                    <span class="edit">编辑</span>
                                    <span class="delet">删除</span>
                                </div>

                            </div>

                        </div>
                        <div class="j-tr">
                            <div class="j-td">
                                <div class="oprate">
                                    <div class="txt level">不及格</div>
                                </div>
                                <div class="total">不及格</div>
                            </div>
                            <div class="j-td">
                                <div class="oprate">
                                    <div class="txt">重修考核学分=</div>
                                    <div class="input inp-lay">
                                        <input class="input inp layui-input" placeholder="请输入系数值">
                                        <div class="error">请重新输入</div>
                                    </div>
                                    <div class="symbol">*</div>
                                    <div class="select-input score-sel">
                                        <div class="name">请选择</div>
                                        <em></em>
                                        <div class="select-dropdown ">
                                            <ul class="dropdown-list">
                                                <li class="cur">标准学分</li>
                                                <li>标准考核学分</li>
                                            </ul>
                                        </div>
                                        <div class="errorMsg">请重新输入</div>
                                    </div>
                                </div>
                                <div class="total">重修考核学分=2*标准考核学分</div>
                            </div>

                            <div class="j-td">
                                <div class="oprate">
                                    <span class="preservation">保存</span>
                                </div>
                                <div class="total">
                                    <span class="edit">编辑</span>
                                    <span class="delet">删除</span>
                                </div>

                            </div>

                        </div>
                        <div class="j-tr">
                            <div class="j-td">
                                <div class="oprate">
                                    <div class="txt level">重修替代及格</div>
                                </div>
                                <div class="total">重修替代及格</div>
                            </div>
                            <div class="j-td">
                                <div class="oprate">
                                    <div class="txt">重修考核学分=</div>
                                    <div class="input inp-lay">
                                        <input class="input inp layui-input" placeholder="请输入系数值">
                                        <div class="error">请重新输入</div>
                                    </div>
                                    <div class="symbol">*</div>
                                    <div class="select-input score-sel">
                                        <div class="name">请选择</div>
                                        <em></em>
                                        <div class="select-dropdown ">
                                            <ul class="dropdown-list">
                                                <li class="cur">标准学分</li>
                                                <li>标准考核学分</li>
                                            </ul>
                                        </div>
                                        <div class="errorMsg">请重新输入</div>
                                    </div>
                                </div>
                                <div class="total">重修考核学分=2*标准考核学分</div>
                            </div>

                            <div class="j-td">
                                <div class="oprate">
                                    <span class="preservation">保存</span>
                                </div>
                                <div class="total">
                                    <span class="edit">编辑</span>
                                    <span class="delet">删除</span>
                                </div>

                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
</body>
<script th:src="@{../js/jquery-3.3.1.min.js}"></script>

<script>
    //下拉
    $(".j-material").on("click", ".j-material-lable .j-table .j-body .j-tr .j-td .select-input .name", function (e) {
        $(this).parent().toggleClass("clicked");
        stopBubble(e);
    })
    $(".j-material").on("click", ".j-material-lable .j-table .j-body .j-tr .j-td .select-input .select-dropdown .dropdown-list li", function (e) {
        $(this).addClass("cur").siblings().removeClass("cur");
        $(this).parents(".select-input").parent().find(".layui-input").removeClass("layui-disabled").removeAttr("disabled");
        let kosl = '';
        kosl = $(this).text();
        if (kosl == '为空') {
            kosl = '';
            $(this).parents(".select-input").parent().find(".layui-input").addClass("layui-disabled").attr("disabled", true);
        }
        $(this).parents(".select-input").find(".name").addClass("ckd");
        $(this).parents(".select-input").find(".name").parent().removeClass("error");
        $(this).parents(".select-input").find(".name").parent().find(".errorMsg").hide();
        $(this).parents(".select-input").find(".name").text(kosl);
        $(this).parents(".select-input").removeClass("clicked");
    })


    function stopBubble(e) {
        if (e && e.stopPropagation)
            e.stopPropagation();
        else {
            window.event.cancelBubble = true;
        }

    }

    $(document).on("click", function (event) {
        var _con = $('.select-input');
        if (!_con.is(event.target) && _con.has(event.target).length === 0) {
            $(".select-input").removeClass("clicked");
        }
    })

    var ktml = '<div class="j-tr">' +
        '<div class="j-td">' +
        '<div class="oprate">' +
        '<div class="inputs left-inp">' +
        '<input class="input inp layui-input" placeholder="请输入分值">' +
        '<div class="error">请重新输入</div>' +
        '<div class="select-input">' +
        '<div class="name ckd"><=</div>' +
        '<em></em>' +
        '<div class="select-dropdown">' +
        '<ul class="dropdown-list">' +
        '<li>&gt;</li>' +
        '<li>>=</li>' +
        '<li>=</li>' +
        '<li>&lt;</li>' +
        '<li class="cur"><=</li>' +
        '<li>为空</li>' +
        '</ul>' +
        '</div>' +
        '</div>' +
        '</div>' +
        '<div class="select-input score-sel">' +
        '<div class="name">请选择</div>' +
        '<em></em>' +
        '<div class="select-dropdown ">' +
        '<ul class="dropdown-list">' +
        '<li class="cur">学业积分</li>' +
        '</ul>' +
        '</div>' +
        '<div class="errorMsg">请重新输入</div>' +
        '</div>' +
        '<div class="inputs right-inp">' +
        '<div class="select-input">' +
        '<div class="name"><=</div>' +
        '<em></em>' +
        '<div class="select-dropdown ">' +
        '<ul class="dropdown-list">' +
        '<li>&gt;</li>' +
        '<li>>=</li>' +
        '<li>=</li>' +
        '<li>&lt;</li>' +
        '<li class="cur"><=</li>' +
        '<li>为空</li>' +
        '</ul>' +
        '</div>' +
        '</div>' +
        '<input class="input layui-input" placeholder="请输入分值">' +
        '<div class="error">请重新输入</div>' +
        '</div>' +
        '</div>' +
        '<div class="total">学业积分 >= 80</div>' +
        '</div>' +
        '<div class="j-td">' +
        '<div class="oprate">' +
        '<div class="txt">正考过程学分=</div>' +
        '<div class="input inp-lay">' +
        '<input class="layui-input" placeholder="请输入系数值">' +
        '<div class="error">请重新输入</div>' +
        '</div>' +
        '<div class="symbol">*</div>' +
        '<div class="select-input score-sel">' +
        '<div class="name">请选择</div>' +
        '<em></em>' +
        '<div class="select-dropdown ">' +
        '<ul class="dropdown-list">' +
        '<li class="cur">标准学分</li>' +
        '<li>标准过程学分</li>' +
        '</ul>' +
        '</div>' +
        '<div class="errorMsg">请重新输入</div>' +
        '</div>' +
        '</div>' +
        '<div class="total">正考过程学分=2*标准过程学分</div>' +
        '</div>' +
        '<div class="j-td">' +
        '<div class="oprate">' +
        '<span class="preservation">保存</span>' +
        '<span class="delet">删除</span>' +
        '</div>' +
        '<div class="total">' +
        '<span class="edit">编辑</span>' +
        '<span class="delet">删除</span>' +
        '</div>' +
        '</div>' +
        '</div>';

    let ntml = '<div class="j-tr">' +
        '<div class="j-td">' +
        '<div class="oprate">' +
        '<div class="txt level">正考过程学分=</div></div>' +
        '<div class="total">学业积分 >= 80</div>' +
        '</div>' +
        '<div class="j-td">' +
        '<div class="oprate">' +
        '<div class="txt">正考过程学分=</div>' +
        '<div class="input inp-lay">' +
        '<input class="layui-input" placeholder="请输入系数值">' +
        '<div class="error">请重新输入</div>' +
        '</div>' +
        '<div class="symbol">*</div>' +
        '<div class="select-input score-sel">' +
        '<div class="name">请选择</div>' +
        '<em></em>' +
        '<div class="select-dropdown ">' +
        '<ul class="dropdown-list">' +
        '<li class="cur">标准学分</li>' +
        '<li>标准过程学分</li>' +
        '</ul>' +
        '</div>' +
        '<div class="errorMsg">请重新输入</div>' +
        '</div>' +
        '</div>' +
        '<div class="total">正考过程学分=2*标准过程学分</div>' +
        '</div>' +
        '<div class="j-td">' +
        '<div class="oprate">' +
        '<span class="preservation">保存</span>' +
        '</div>' +
        '<div class="total">' +
        '<span class="edit">编辑</span>' +
        '</div>' +
        '</div>' +
        '</div>';

    //保存
    $(".j-material").on("click", ".j-material-lable .j-table .j-body .j-tr .j-td .preservation", function () {
        var _this = $(this);
        var id = $(this).parents(".j-tr").attr("id");
        var creditCalcuRule = $(this).parents(".j-table").attr("type");
        var ruleSetType = $(this).parents(".j-material-lable").attr("type");
        $(this).parents(".j-tr").find(".j-td").eq(0).attr("style", "");
        $(this).parents(".j-tr").find(".j-td").eq(0).find(".left-inp").removeClass("errors");
        $(this).parents(".j-tr").find(".j-td").eq(0).find(".right-inp").removeClass("errors");
        $(this).parents(".j-tr").find(".j-td").eq(1).find(".inp-lay").removeClass("errors");
        //成绩区间
        var selTxt = $(this).parents(".j-tr").find(".j-td").eq(0).find(".score-sel .name").text();
        var leftVal = $(this).parents(".j-tr").find(".j-td").eq(0).find(".left-inp .layui-input").val();
        var leftOperator = $(this).parents(".j-tr").find(".j-td").eq(0).find(".left-inp .name").text();
        var rightVal = $(this).parents(".j-tr").find(".j-td").eq(0).find(".right-inp .layui-input").val();
        var rightOperator = $(this).parents(".j-tr").find(".j-td").eq(0).find(".right-inp .name").text();
        //得分
        var scoreVal = $(this).parents(".j-tr").find(".j-td").eq(1).find(".oprate .layui-input").val();
        var scoreTxt = $(this).parents(".j-tr").find(".j-td").eq(1).find(".oprate .score-sel .name").text();
        var score = scoreVal + "*" + scoreTxt;
        var achievementSection = "";
        if (leftVal) {
            achievementSection = leftVal + leftOperator;
        }
        achievementSection += selTxt;
        if (rightVal) {
            achievementSection += rightOperator + rightVal;
        }
        if (ruleSetType === "2") {
            achievementSection = $(this).parents(".j-tr").find(".j-td").eq(0).find(".level").text();
        }
        if (!scoreVal) {
            $(this).parents(".j-tr").find(".j-td").eq(1).find(".inp-lay").addClass("errors");
            $(this).parents(".j-tr").find(".j-td").eq(1).find(".inp-lay .error").text("不能为空");
            return false;
        }
        if (scoreVal != 0 && !/^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(scoreVal)) {
            $(this).parents(".j-tr").find(".j-td").eq(1).find(".inp-lay").addClass("errors");
            $(this).parents(".j-tr").find(".j-td").eq(1).find(".inp-lay .error").text("只能输入数字，最多两位小数");
            return false;
        }
        if (scoreTxt == "请选择") {
            $(this).parents(".j-tr").find(".j-td").eq(1).find(".score-sel").addClass("error");
            $(this).parents(".j-tr").find(".j-td").eq(1).find(".score-sel .errorMsg").show().text("选项不能为空");
            return false;
        }
        if (ruleSetType === "1") {
            if (leftOperator == '' && leftVal) {
                $(this).parents(".j-tr").find(".j-td").eq(0).find(".left-inp").addClass("errors");
                $(this).parents(".j-tr").find(".j-td").eq(0).find(".left-inp .error").text("有分数设置时，符号下拉框选择不能为空");
                return false;
            }
            if (rightOperator == '' && rightVal) {
                $(this).parents(".j-tr").find(".j-td").eq(0).find(".right-inp").addClass("errors");
                $(this).parents(".j-tr").find(".j-td").eq(0).find(".right-inp .error").text("有分数设置时，符号下拉框选择不能为空");
                return false;
            }
            if (!leftVal && !rightVal) {
                if (!leftVal) {
                    $(this).parents(".j-tr").find(".j-td").eq(0).find(".left-inp").addClass("errors");
                    $(this).parents(".j-tr").find(".j-td").eq(0).find(".left-inp .error").text("不能为空");
                }
                return false;
            }
            if (selTxt == "请选择") {
                $(this).parents(".j-tr").find(".j-td").eq(0).find(".score-sel").addClass("error");
                $(this).parents(".j-tr").find(".j-td").eq(0).find(".score-sel .errorMsg").show().text("选项不能为空");
                return false;
            }
            if (rightVal && !/^[0-9]*$/.test(rightVal)) {
                $(this).parents(".j-tr").find(".j-td").eq(0).find(".left-inp").addClass("errors");
                $(this).parents(".j-tr").find(".j-td").eq(0).find(".left-inp .error").text("只能输入数字");
                return false;
            }
            if (leftVal && rightVal && parseInt(leftVal) >= parseInt(rightVal)) {
                $(this).parents(".j-tr").find(".j-td").eq(0).find(".left-inp").addClass("errors");
                $(this).parents(".j-tr").find(".j-td").eq(0).find(".left-inp .error").text("区间值设置错误");
                flag = false;
                return false;
            }
            if (leftVal && !/^[0-9]*$/.test(leftVal)) {
                $(this).parents(".j-tr").find(".j-td").eq(0).find(".left-inp").addClass("errors");
                $(this).parents(".j-tr").find(".j-td").eq(0).find(".left-inp .error").text("只能输入数字");
                return false;
            }
            var array = [];
            $(this).parents(".j-body").find(".opafter").each(function () {
                var selTxt = $(this).find(".j-td").eq(0).find(".score-sel .name").text();
                var leftVal = $(this).find(".j-td").eq(0).find(".left-inp .layui-input").val();
                var leftOperator = $(this).find(".j-td").eq(0).find(".left-inp .name").text();
                var rightVal = $(this).find(".j-td").eq(0).find(".right-inp .layui-input").val();
                var rightOperator = $(this).find(".j-td").eq(0).find(".right-inp .name").text();
                leftVal = leftVal ? leftVal : 0;
                rightVal = rightVal ? rightVal : 0;
                var obj = {
                    selTxt: selTxt,
                    leftVal: leftVal,
                    rightVal: rightVal,
                    leftOperator: leftOperator,
                    rightOperator: rightOperator
                };
                array.push(obj);
            })
            leftVal = leftVal ? leftVal : 0;
            rightVal = rightVal ? rightVal : 0;
            array.push({
                selTxt: selTxt,
                leftVal: leftVal,
                rightVal: rightVal,
                leftOperator: leftOperator,
                rightOperator: rightOperator
            });
            var array2 = new Array();
            var errorInfo = "";
            for (var i = 0; i < array.length; i++) {//重叠判断，-1代表最后一个不进行判断
                /* if(parseInt(array[i].rightVal)-parseInt(array[i+1].leftVal)>0 && array[i].selTxt == array[i+1].selTxt){
                    $(this).parents(".j-tr").find(".j-td").eq(0).attr("style","border: 1px solid #F98981;");
                    $(this).parents(".j-tr").find(".j-td").eq(0).find(".left-inp .error").show().text("成绩区间设置不合理");
                    return false;
                } */
                var info = array[i];
                if (info.leftOperator.indexOf(">") > -1 || (info.leftVal == "" && info.rightOperator.indexOf(">") > -1)) {//设置颠倒了
                    if (info.rightVal == '' && array2[0] != undefined) {//只能设置一个起始
                        errorInfo = info;
                        break;
                    } else if (info.rightVal == '') {
                        array2[0] = info;
                    } else {
                        array2[info.rightVal] = info;
                    }
                } else {
                    if (info.leftVal == '' && array2[0] != undefined) {//只能设置一个起始
                        errorInfo = info;
                        break;
                    } else if (info.leftVal == '') {
                        array2[0] = info;
                    } else {
                        array2[info.leftVal] = info;
                    }
                }

            }
            array2 = array2.sort(arrSort);
            for (var j = 0; j < array2.length - 1; j++) {
                if (array2[j + 1] != undefined) {
                    var max = array2[j + 1].leftVal ? parseInt(array2[j + 1].leftVal) : 0;
                    var min = array2[j].rightVal ? parseInt(array2[j].rightVal) : 0;
                    if (max > min && min == 0) {
                        //errorInfo = array2[j];
                        //break;
                    } else if (max == 0 && parseInt(array2[j + 1].rightVal) < min) {
                        errorInfo = array2[j];
                        break;
                    } else if (max == 0 && parseInt(array2[j + 1].rightVal) == min && array2[j + 1].rightOperator.indexOf("=") > -1
                        && array2[j].rightOperator.indexOf("=") > -1) {
                        errorInfo = array2[j];
                        break;
                    } else if (max < min || (max == min && array2[j + 1].leftOperator.indexOf("=") > -1
                        && array2[j].rightOperator.indexOf("=") > -1)) {//成绩区间设置重叠
                        errorInfo = array2[j];
                        break;
                    }
                }
            }
            if (errorInfo != '') {
                $(this).parents(".j-tr").find(".j-td").eq(0).attr("style", "border: 1px solid #F98981;");
                $(this).parents(".j-tr").find(".j-td").eq(0).find(".left-inp .error").show().text("成绩区间设置不合理");
                return false;
            }
        }
        var explain = "";
        if (ruleSetType == 1 && creditCalcuRule == 1) {
            explain = "正考过程学分=";
        } else if (ruleSetType == 1 && creditCalcuRule == 2) {
            explain = "正考考核学分=";
        } else if (ruleSetType == 2 && creditCalcuRule == 1) {
            explain = "标准过程学分=";
        } else if (ruleSetType == 2 && creditCalcuRule == 2) {
            explain = "标准考核学分=";
        }
        $.post("../credit/basic/rule/customization/data/saveCalcuRuleSet", {
            score: score,
            achievementSection: achievementSection,
            creditCalcuRule: creditCalcuRule,
            ruleSetType: ruleSetType,
            id: id
        }, function (data) {
            if (data.status) {
                _this.parents(".j-tr").addClass("opafter");
                _this.parents(".j-tr").attr("id", data.id);
                _this.parents(".j-tr").find(".j-td").eq(0).find(".total").text(achievementSection);
                _this.parents(".j-tr").find(".j-td").eq(1).find(".total").text(explain + score);
            } else {
                layer.msg("保存失败", {icon: 2, time: 2000});
            }
        }, "json");
    })

    //编辑
    $(".j-material").on("click", ".j-material-lable .j-table .j-body .j-tr .j-td .edit", function () {
        $(this).parents(".j-tr").removeClass("opafter");
    })
    //删除
    $(".j-material").on("click", ".j-material-lable .j-table .j-body .j-tr .j-td .delet", function () {
        var id = $(this).parents(".j-tr").attr("id");
        var _this = $(this);
        if (!id) {
            _this.parents(".j-tr").remove();
            return false;
        }
        layer.confirm('确定要删除吗？', function (index) {
            $.post("../credit/basic/rule/customization/data/delCalcuRuleSet", {
                id: id
            }, function (data) {
                if (data.status) {
                    _this.parents(".j-tr").remove();
                } else {
                    layer.msg("删除失败", {icon: 2, time: 2000});
                }
            }, "json");
            layer.close(index);
        })
    })

    //添加
    $(".j-material").on("click", ".j-material-lable .add-oprate .add1", function () {
        $(this).parent(".add-oprate").next().find(".j-body").append(ktml);
        $(this).parent(".add-oprate").next().find(".j-body .txt").text("正考过程学分=");
        $(this).parent(".add-oprate").next().find(".j-body .j-tr").last().find(".j-td").eq(0).find(".score-sel .name").text("学业积分");
        $(this).parent(".add-oprate").next().find(".j-body .j-tr").last().find(".j-td").eq(0).find(".score-sel .cur").text("学业积分");
        $(this).parent(".add-oprate").next().find(".j-body .j-tr").last().find(".j-td").eq(1).find("ul li").eq(1).text("标准过程学分");
    })
    $(".j-material").on("click", ".j-material-lable .add-oprate .add2", function () {
        $(this).parent(".add-oprate").next().find(".j-body").append(ktml);
        $(this).parent(".add-oprate").next().find(".j-body .txt").text("正考考核学分=");
        $(this).parent(".add-oprate").next().find(".j-body .j-tr").last().find(".j-td").eq(0).find(".score-sel .name").text("正考考核成绩");
        $(this).parent(".add-oprate").next().find(".j-body .j-tr").last().find(".j-td").eq(0).find(".score-sel  .cur").text("正考考核成绩");
        $(this).parent(".add-oprate").next().find(".j-body .j-tr").last().find(".j-td").eq(1).find("ul li").eq(1).text("标准考核学分");
    })
    $(".j-material").on("click", ".j-material-lable .add-oprate .add3", function () {
        $(this).parent(".add-oprate").next().find(".j-body").append(ktml);
        $(this).parent(".add-oprate").next().find(".j-body .txt").text("重修过程学分=");
        $(this).parent(".add-oprate").next().find(".j-body .j-tr").last().find(".j-td").eq(0).find(".score-sel .name").text("重修成绩");
        $(this).parent(".add-oprate").next().find(".j-body .j-tr").last().find(".j-td").eq(0).find(".score-sel .cur").text("重修成绩");
        $(this).parent(".add-oprate").next().find(".j-body .j-tr").last().find(".j-td").eq(1).find("ul li").eq(1).text("标准过程学分");
    })
    $(".j-material").on("click", ".j-material-lable .add-oprate .add4", function () {
        $(this).parent(".add-oprate").next().find(".j-body").append(ktml);
        $(this).parent(".add-oprate").next().find(".j-body .txt").text("重修考核学分=");
        $(this).parent(".add-oprate").next().find(".j-body .j-tr").last().find(".j-td").eq(0).find(".score-sel .name").text("重修成绩");
        $(this).parent(".add-oprate").next().find(".j-body .j-tr").last().find(".j-td").eq(0).find(".score-sel .cur").text("重修成绩");
        $(this).parent(".add-oprate").next().find(".j-body .j-tr").last().find(".j-td").eq(1).find("ul li").eq(1).text("标准考核学分");
    })

    $(document).ready(function () {
        //学分基础规则设置
        $.post("../credit/basic/rule/customization/data/getBasicRuleSet", {}, function (data) {
            if (data.status && data.creditBasicsRuleSet) {
                var creditBasicsRuleSet = data.creditBasicsRuleSet;
                $("input[name='id']").val(creditBasicsRuleSet.id);
                $("input[name='standardProcessCredit']").val(creditBasicsRuleSet.standardProcessCredit);
                $("input[name='standardExamineCredit']").val(creditBasicsRuleSet.standardExamineCredit);
                $("input[name='academicInitCredit']").val(creditBasicsRuleSet.academicInitCredit);
                if (creditBasicsRuleSet.startTime) {
                    $("input[name='startTime']").val(creditBasicsRuleSet.startTime);
                }
                if (creditBasicsRuleSet.endTime) {
                    $("input[name='endTime']").val(creditBasicsRuleSet.endTime);
                }
            }
        }, "json");
        //学分计算规则
        getCalcuRuleSet();
    })

    function getCalcuRuleSet() {
        $.post("../credit/basic/rule/customization/data/getCalcuRuleSet", {}, function (data) {
            if (data.status) {
                if (data.list.length > 0) {
                    $(".j-body").empty();
                    var tr1 = 0, tr2 = 0, tr3 = 0, tr4 = 0, tridx = 0;
                    for (var i = 0; i < data.list.length; i++) {
                        var calcu = data.list[i];
                        var index = 0;
                        var explain = [];
                        if (calcu.ruleSetType == "1" && calcu.creditCalcuRule == "1") {
                            index = 0;
                            tridx = tr1;
                            tr1++;
                            explain = ["正考过程学分=", "学业积分", "标准过程学分"];
                        } else if (calcu.ruleSetType == "1" && calcu.creditCalcuRule == "2") {
                            index = 1;
                            tridx = tr2;
                            tr2++;
                            explain = ["正考考核学分=", "正考考核成绩", "标准考核学分"];
                        } else if (calcu.ruleSetType == "2" && calcu.creditCalcuRule == "1") {
                            index = 2;
                            tridx = tr3;
                            tr3++;
                            explain = ["重修过程学分=", "重修成绩", "标准过程学分"];
                        } else if (calcu.ruleSetType == "2" && calcu.creditCalcuRule == "2") {
                            index = 3;
                            tridx = tr4;
                            tr4++;
                            explain = ["重修考核学分=", "重修成绩", "标准考核学分"];
                        }
                        //解析区间表达式
                        const achievementSection = calcu.achievementSection;
                        let html = achievementSection !== "不及格" && achievementSection !== "及格" && achievementSection !== "重修替代及格" ? ktml : ntml;
                        $(".j-body").eq(index).append(html);
                        $(".j-body").eq(index).find(".j-tr").addClass("opafter")
                        $(".j-body").eq(index).find(".j-tr").eq(tridx).attr("id", calcu.id);
                        $(".j-body").eq(index).find(".j-tr").eq(tridx).find(".j-td").eq(0).find(".level").text(achievementSection);
                        $(".j-body").eq(index).find(".j-tr").eq(tridx).find(".j-td").eq(0).find(".total").text(achievementSection);
                        $(".j-body").eq(index).find(".j-tr").eq(tridx).find(".j-td").eq(1).find(".txt").text(explain[0]);
                        $(".j-body").eq(index).find(".j-tr").eq(tridx).find(".j-td").eq(1).find(".total").text(explain[0] + calcu.score);
                        if (achievementSection !== "不及格" && achievementSection !== "及格" && achievementSection !== "重修替代及格") {
                            var reg = /[\u4e00-\u9fa5]/g;
                            var options = achievementSection.match(reg);
                            options = options.join("");
                            var sectionScore = achievementSection.match(/\d+/g).join(",");
                            var expression = achievementSection.replace(options, ",");
                            expression = expression.replace(/\d+/g, "");
                            var leftExpress = expression.split(",")[0] ? expression.split(",")[0] : "<=";
                            var rightExpress = expression.split(",")[1] ? expression.split(",")[1] : "<=";
                            if (sectionScore.indexOf(",") == -1) {
                                var idx = achievementSection.indexOf(options);
                                if (idx == 0) {
                                    sectionScore = "," + sectionScore;
                                }
                            }
                            $(".j-body").eq(index).find(".j-tr").eq(tridx).find(".j-td").eq(0).find(".score-sel .cur").text(explain[1]);
                            $(".j-body").eq(index).find(".j-tr").eq(tridx).find(".j-td").eq(0).find(".left-inp .name").text(leftExpress);
                            $(".j-body").eq(index).find(".j-tr").eq(tridx).find(".j-td").eq(0).find(".left-inp .layui-input").val(sectionScore.split(",")[0]);
                            $(".j-body").eq(index).find(".j-tr").eq(tridx).find(".j-td").eq(0).find(".score-sel .name").text(options);
                            $(".j-body").eq(index).find(".j-tr").eq(tridx).find(".j-td").eq(0).find(".right-inp .layui-input").val(sectionScore.split(",")[1]);
                            $(".j-body").eq(index).find(".j-tr").eq(tridx).find(".j-td").eq(0).find(".right-inp .name").text(rightExpress);
                        }

                        //解析得分表达式
                        var score = calcu.score;
                        $(".j-body").eq(index).find(".j-tr").eq(tridx).find(".j-td").eq(1).find("ul li").eq(1).text(explain[2]);
                        $(".j-body").eq(index).find(".j-tr").eq(tridx).find(".j-td").eq(1).find(".oprate .layui-input").val(score.split("*")[0]);
                        $(".j-body").eq(index).find(".j-tr").eq(tridx).find(".j-td").eq(1).find(".oprate .score-sel .name").text(score.split("*")[1]);

                    }
                }
            }
        }, "json");
    }

    layui.use(['form', 'layer', 'laydate'], function () {
        var form = layui.form;
        var layer = layui.layer;
        var laydate = layui.laydate;
        laydate.render({
            elem: '#startTime',
            format: 'yyyy-MM-dd', //设置日期格式
        })
        laydate.render({
            elem: '#endTime',
            format: 'yyyy-MM-dd', //设置日期格式
        })
        //自定义验证规则
        form.verify({
            num: [/^[0-9]*$/, "只能输入数字"]
        });
        //监听提交
        form.on('submit(btn-complate)', function (data) {
            var standardProcessCredit = parseInt(data.field.standardProcessCredit);
            var standardExamineCredit = parseInt(data.field.standardExamineCredit);
            var id = data.field.id;
            var academicInitCredit = data.field.academicInitCredit;
            if (standardProcessCredit + standardExamineCredit != 100) {
                layer.msg("标准过程学分、标准考核学分占比之和必须为100", {icon: 2, time: 2000});
                return false;
            }
            var flag = true;
            var settingArray = [];
            $(".j-table .j-tr").each(function () {
                let ruleType = $(this).parents(".j-material-lable").attr("type");
                $(this).find(".j-td").eq(0).find(".left-inp").removeClass("errors");
                $(this).find(".j-td").eq(0).find(".right-inp").removeClass("errors");
                $(this).find(".j-td").eq(1).find(".score-sel").removeClass("error");
                $(this).find(".j-td").eq(1).find(".inp-lay").removeClass("error");
                var id = $(this).attr("id");
                var type = $(this).parents(".j-material-lable").attr("type");
                var creditCalcuRule = $(this).parents(".j-table").attr("type");
                //成绩区间
                var selTxt = $(this).find(".j-td").eq(0).find(".score-sel .name").text();
                var leftVal = $(this).find(".j-td").eq(0).find(".left-inp .layui-input").val();
                var leftOperator = $(this).find(".j-td").eq(0).find(".left-inp .name").text();
                var rightVal = $(this).find(".j-td").eq(0).find(".right-inp .layui-input").val();
                var rightOperator = $(this).find(".j-td").eq(0).find(".right-inp .name").text();
                var achievementSection = $(this).find(".j-td").eq(0).find(".total").text();
                //得分
                var scoreVal = $(this).find(".j-td").eq(1).find(".inp-lay .layui-input").val();
                var scoreTxt = $(this).find(".j-td").eq(1).find(".score-sel .name").text();


                var sectionSetting = {
                    leftVal: leftVal,
                    leftOperator: leftOperator,
                    selTxt: selTxt,
                    rightVal: rightVal,
                    rightOperator: rightOperator,
                    scoreVal: scoreVal,
                    scoreTxt: scoreTxt,
                    ruleSetType: type,
                    creditCalcuRule: creditCalcuRule,
                    achievementSection: achievementSection,
                    id: id
                };
                if (ruleType === "1") {
                    if (!leftVal && !rightVal) {
                        if (!leftVal) {
                            $(this).find(".j-td").eq(0).find(".left-inp").addClass("errors");
                            $(this).find(".j-td").eq(0).find(".left-inp .error").text("不能为空");
                        }
                        flag = false;
                        return false;
                    }
                    if (leftVal && !/^[0-9]*$/.test(leftVal)) {
                        $(this).find(".j-td").eq(0).find(".left-inp").addClass("errors");
                        $(this).find(".j-td").eq(0).find(".left-inp .error").text("只能输入数字");
                        flag = false;
                        return false;
                    }
                    if (selTxt == "请选择") {
                        $(this).find(".j-td").eq(0).find(".score-sel").addClass("error");
                        $(this).find(".j-td").eq(0).find(".score-sel .errorMsg").show().text("选项不能为空");
                        flag = false;
                        return false;
                    }
                    if (rightVal && !/^[0-9]*$/.test(rightVal)) {
                        $(this).find(".j-td").eq(1).find(".left-inp").addClass("errors");
                        $(this).find(".j-td").eq(1).find(".left-inp .error").text("只能输入数字");
                        flag = false;
                        return false;
                    }
                    if (leftVal && rightVal && parseInt(leftVal) >= parseInt(rightVal)) {
                        $(this).find(".j-td").eq(0).find(".left-inp").addClass("errors");
                        $(this).find(".j-td").eq(0).find(".left-inp .error").text("区间值设置错误");
                        flag = false;
                        return false;
                    }
                }
                if (!scoreVal) {
                    $(this).find(".j-td").eq(1).find(".inp-lay").addClass("error");
                    $(this).find(".j-td").eq(1).find(".inp-lay .error").show().text("不能为空");
                    flag = false;
                    return false;
                }
                if (scoreVal != 0 && !/^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(scoreVal)) {
                    $(this).find(".j-td").eq(1).find(".inp-lay").addClass("error");
                    $(this).find(".j-td").eq(1).find(".inp-lay .error").show().text("只能输入数字，最多两位小数");
                    flag = false;
                    return false;
                }
                if (scoreTxt == "请选择") {
                    $(this).find(".j-td").eq(1).find(".score-sel").addClass("error");
                    $(this).find(".j-td").eq(1).find(".score-sel .errorMsg").show().text("选项不能为空");
                    flag = false;
                    return false;
                }
                settingArray.push(sectionSetting);
            })
            var errorInfo = "";
            var errorInfo2 = "";
            for (var n = 1; n < 3; n++) {
                for (var m = 1; m < 3; m++) {
                    var array = new Array();
                    for (var i = 0; i < settingArray.length; i++) {
                        var info = settingArray[i];
                        if (info.ruleSetType == n && info.creditCalcuRule == m) {
                            if (info.leftOperator.indexOf(">") > -1 || (info.leftVal == "" && info.rightOperator.indexOf(">") > -1)) {//设置颠倒了
                                if (info.rightVal == '' && array[0] != undefined) {//只能设置一个起始
                                    errorInfo = info;
                                    errorInfo2 = array[0];
                                    break;
                                } else if (info.rightVal == '') {
                                    array[0] = info;
                                } else {
                                    array[info.rightVal] = info;
                                }
                            } else {
                                if (info.leftVal == '' && array[0] != undefined) {//只能设置一个起始
                                    errorInfo = info;
                                    errorInfo2 = array[0];
                                    break;
                                } else if (info.leftVal == '') {
                                    array[0] = info;
                                } else {
                                    array[info.leftVal] = info;
                                }
                            }
                        }
                    }
                    array.sort(arrSort);
                    for (var j = 0; j < array.length - 1; j++) {
                        if (array[j + 1] != undefined) {
                            var max = array[j + 1].leftVal ? parseInt(array[j + 1].leftVal) : 0;
                            var min = array[j].rightVal ? parseInt(array[j].rightVal) : 0;
                            if (max > min && min == 0) {
                                //errorInfo = array[j];
                                //errorInfo2 = array[j+1];
                                //break;
                            } else if (max == 0 && parseInt(array[j + 1].rightVal) < min) {
                                errorInfo = array[j];
                                errorInfo2 = array[j + 1];
                                break;
                            } else if (max == 0 && parseInt(array[j + 1].rightVal) == min && array[j + 1].rightOperator.indexOf("=") > -1
                                && array[j].rightOperator.indexOf("=") > -1) {
                                errorInfo = array[j];
                                errorInfo2 = array[j + 1];
                                break;
                            } else if (max < min || (max == min && array[j + 1].leftOperator.indexOf("=") > -1
                                && array[j].rightOperator.indexOf("=") > -1)) {//成绩区间设置重叠
                                errorInfo = array[j];
                                errorInfo2 = array[j + 1];
                                break;
                            }
                        }
                    }
                }
            }
            if (errorInfo != '') {
                var before = "", after = "";
                if (errorInfo.leftVal) {
                    before = errorInfo.leftVal + errorInfo.leftOperator;
                }
                before += errorInfo.selTxt;
                if (errorInfo.rightVal) {
                    before += errorInfo.rightOperator + errorInfo.rightVal;
                }
                if (errorInfo2.leftVal) {
                    after = errorInfo2.leftVal + errorInfo2.leftOperator;
                }
                after += errorInfo2.selTxt;
                if (errorInfo2.rightVal) {
                    after += errorInfo2.rightOperator + errorInfo2.rightVal;
                }
                var explain = "";
                if (errorInfo.ruleSetType == 1 && errorInfo.creditCalcuRule == 1) {
                    explain = "正考过程";
                } else if (errorInfo.ruleSetType == 1 && errorInfo.creditCalcuRule == 2) {
                    explain = "正考考核";
                } else if (errorInfo.ruleSetType == 2 && errorInfo.creditCalcuRule == 1) {
                    explain = "重修过程";
                } else if (errorInfo.ruleSetType == 2 && errorInfo.creditCalcuRule == 2) {
                    explain = "重修考核";
                }
                layer.msg(explain + ":成绩区间" + before + "与"
                    + after + "有重叠", {icon: 2, time: 5000});
                flag = false;
                return false;
            }
            if (flag) {
                $.ajaxSettings.async = false;
                $.post("../credit/basic/rule/customization/data/saveBasicRuleSet", {
                    settingData: JSON.stringify(settingArray),
                    standardProcessCredit: standardProcessCredit,
                    standardExamineCredit: standardExamineCredit,
                    academicInitCredit: academicInitCredit,
                    startTime: data.field.startTime,
                    endTime: data.field.endTime,
                    id: id
                }, function (data) {
                    if (data.status) {
                        layer.msg("保存成功", {icon: 1, time: 2000});
                    } else {
                        layer.msg("保存失败", {icon: 2, time: 2000});
                    }
                }, "json");
                $.ajaxSettings.async = true;
            }
            return false;
        });
    });

    function arrSort(a, b) {
        return a - b;
    }
</script>

</html>