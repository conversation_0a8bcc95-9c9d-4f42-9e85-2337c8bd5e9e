<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>更新成绩数据</title>
    <link rel="stylesheet" th:href="@{~/css/examination/global2.css}">
    <link rel="stylesheet" th:href="@{~/css/examination/dialog.css}">
    <link rel="stylesheet" type="text/css" th:href="@{~/plugin/layui-v2.8.18/layui/css/layui.css}"/>
    <script th:src="@{~/plugin/layui-v2.8.18/layui/layui.js}" type="text/javascript" charset="utf-8"></script>

<body>
<div class="masker"></div>
<div class="dialog" id="invigilateMax">
    <div class="dialog-title" style="display: flex">更新成绩数据</div>

    <div class="dialog-con">
        <div class="item">
            <div class="label">学年学期</div>
            <div class="j-search-con single-box">
                <input type="text" name="term" placeholder="请选择" readonly="" class="schoolSel" id="term">
                <span class="j-arrow"></span>
                <div class="j-select-year ">
                    <ul id="terms">

                    </ul>
                </div>
            </div>
        </div>
    </div>
    <div class="dialog-btn">
        <button class="pu-sure" id="btnSure">确定</button>
    </div>
</div>
</body>
<script th:src="@{~/js/jquery-3.3.1.min.js}"></script>
<script th:src="@{~/js/apimanage/common.js}"></script>
<script th:src="@{~/js/my.util.js}"></script>
<script type="text/javascript">
    // const _VR_ = '';
    var fid=[[${fid}]]
    var uid=[[${uid}]]
    var formId=[[${formId}]]
    $(function () {
        $.get("/basic/xnxq",function (res){
            if(U.su(res)){
                res.data.forEach(function (item){
                    $('#terms').append('<li data-id="'+item.id+'">'+item.xnxq_xnxqh+'</li>')
                })
            }
        })

        $("#btnSure").bind("click",function (){
            var term = $('#term').val();
            if (term==undefined||term==''){
                U.fail("请选择学年学期")
                return false;
            }
            $.get("/api/score/updateScore",{
                fid:fid,
                uid:uid,
                formId:formId,
                term:term
            },function (res){
                if(U.su(res)){
                    U.success(res.data);
                }else {
                    U.fail(res.msg)
                }
                setTimeout(function (){
                    U.closePop()
                },3_000)

            })


        })

    })
</script>

</html>