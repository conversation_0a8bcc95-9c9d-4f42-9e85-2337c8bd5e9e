<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta http-equiv='X-UA-Compatible' content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <title>设置学分</title>
    <link rel="stylesheet" th:href="@{~/css/graduate/global.css}">
    <link rel="stylesheet" th:href="@{~/plugin/layui/css/layui.css}">
    <link rel="stylesheet" th:href="@{~/css/graduate/reset.css}">
    <link rel="stylesheet" th:href="@{~/css/graduate/dialog.css}">
    <script th:src="@{~/layui/layui.js}"></script>
    <script th:src="@{~/js/jquery-3.6.0.min.js}" type="text/javascript" charset="utf-8"></script>
    <style>
        #creditConfig {
            width: 800px;
            position: fixed;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%)
        }
    </style>
</head>
<body>
<div class="masker"></div>
<div class="dialog" id="creditConfig">
    <div class="dialog-title">设置学分</div>
    <div class="dialog-con">
        <div class="item">
            <div class="label">课程标签</div>
            <div class="j-search-con single-box">
                <input type="text" name="teacherName" placeholder="请选择" readonly="" class="schoolSel" value="课程性质">
                <span class="j-arrow"></span>
                <div class="j-select-year">
                    <ul>
                        <li data-value="xt_kcxz" class="active">课程性质</li>
                        <li data-value="218606">课程类别</li>
                        <li data-value="kcsx">课程属性</li>
                        <li data-value="519478">课程分类</li>
                        <li data-value="xbx">选必修</li>
                    </ul>
                </div>
            </div>
        </div>
        <div style="height: 440px">
            <table class="layui-hide materialTable" id="materialTable" lay-filter="materialTable">
            </table>
        </div>
    </div>
    <div class="dialog-btn">
        <button class="pu-cancel">取消</button>
        <button class="pu-sure"
                id="invigilateSure">确定
        </button>
    </div>
    <div>
    </div>
</div>
</body>
<script th:src="@{~/js/my.util.js}"></script>
<script th:inline="javascript">
    var zybh=[[${zybh}]];
    var nj=[[${nj}]];
    var queryId=[[${queryId}]]||'';
    const _VR_ = [[${_VR_}]] || '';
    const fid = [[${fid}]] || '';
    const uid = [[${uid}]] || '';
    layui.use(['jquery', 'table'], function () {
        var table = layui.table;
        $ = layui.jquery;

        $(function (){
            var value = $("li.active").attr("data-value");
            var type = $("li.active").html();
            $.get("/graduate/rule/credit/list", {alias: value,zybh:zybh,nj:nj}, function (res) {
                tableReload(type, res.data)
            })
        })

        function tableReload(type, data) {
            if (data == undefined) {
                data = [];
            }

            var cols = [
                [{
                    field: "type",
                    title: type,
                    align: "center",
                },
                    {
                        field: "score",
                        title: "应修学分",
                        align: "center",
                        edit: 'textarea'
                    },
                ]
            ];

            table.reload("materialTable", {cols: cols, data: data});
        }

        table.render({
            elem: '#materialTable',
            // url: '../../demo/table/user/-page=1&limit=20.js',
            data: [],
            height: '440',
            cols: [
                [{
                    field: "type",
                    title: "课程性质",
                    align: "center",
                },
                    {
                        field: "score",
                        title: "应修学分",
                        align: "center",
                    },
                ]
            ],
            done: function (res, curr, count) {
            }
        });

        /* ***************** 下拉 **************************** */
        // 下拉多选
        $(".j-search-con").on("click", ".schoolSel", function (e) {
            $(".j-select").hide();
            let parent = $(this).parent();
            parent.find('.j-arrow').toggleClass('j-arrow-slide');
            parent.find('.j-select-year').toggleClass('slideShow');
            var sibling = $(this).parents('.item').siblings();
            sibling.find('.j-arrow').removeClass('j-arrow-slide');
            sibling.find('.j-select-year').removeClass('slideShow');

            stopBubble(e)
        })

        //  选择-单选
        $(".j-search-con.single-box").on("click", ".j-select-year li ", function (e) {
            $(this).addClass('active').siblings().removeClass();
            var parents = $(this).parents('.j-search-con');
            var schoolSelEle = parents.find('.schoolSel');
            var txt = $(this).text();
            schoolSelEle.val(txt);
            parents.find('.j-arrow').removeClass('j-arrow-slide');
            parents.find('.j-select-year').removeClass('slideShow')

            var type = $(this).html();
            var value = $(this).attr("data-value");
            $.get("/graduate/rule/credit/list", {alias: value,zybh:zybh,nj:nj}, function (res) {
                tableReload(type, res.data)
            })

            stopBubble(e);
        })
        $("#invigilateSure").click(function () {
            var data = table.cache["materialTable"];
            if (data==undefined||data.length==0){
                U.fail("无可保存数据")
                return  false;
            }
            for (let i = 0; i < data.length; i++) {
                data[i]["zybh"] = zybh;
                data[i]["grade"] = nj;
                if (data[i].score===undefined||data[i].score===""){
                    U.fail("请填写【" + data[i].type+ "】应修学分")
                    return false;
                }
            }
            U.ajax({
                type: 'post',
                url: "/graduate/rule/credit/save?queryId="+queryId,
                data: JSON.stringify(data),
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success: function (res) {
                    if (res.code == 200) {
                        U.success("保存成功")
                        setTimeout(function () {
                            U.closePop()
                        }, 2000)
                    } else {
                        U.fail(res.msg)
                    }
                },
                error: function (res) {
                    U.fail("系统繁忙")
                }
            })
        })

        // 搜索
        $(".j-search-con").on('click', '.search input', function (e) {
            stopBubble(e);
        })
        $(".j-search-con").on('input', '.search input', function (e) {
            let val = $(this).val();
            var nextEle = $(this).parents('.j-search-con').find('ul')
            if (val != "") {
                nextEle.find('li').each(function (i, ele) {
                    let txt = $(ele).text();
                    if (txt.indexOf(val) >= 0) {
                        $(ele).show();
                    } else {
                        $(ele).hide();
                    }
                })
            } else {
                nextEle.find('li').show();
            }
            stopBubble(e)
        })

        $(".pu-cancel").click(function (){
            U.closePop()
        })

    })

    function stopBubble(e) {
        if (e && e.stopPropagation)
            e.stopPropagation();
        else {
            window.event.cancelBubble = true;
        }
    }
</script>
</html>