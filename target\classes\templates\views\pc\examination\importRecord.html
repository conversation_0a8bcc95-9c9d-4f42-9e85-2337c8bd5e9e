<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导入记录</title>
    <link rel="stylesheet" th:href="@{~/css/examination/global2.css}">
    <link rel="stylesheet" th:href="@{~/css/examination/dialog.css}">
    <link rel="stylesheet" th:href="@{~/css/examination/reset.css}">
    <link rel="stylesheet" th:href="@{~/plugin/layui/css/layui.css}">
    <script th:src="@{~/plugin/layui/layui.js}"></script>
    <script th:src="@{~/js/jquery-1.11.3.min.js}"></script>
    <script th:src="@{~/js/my.util.js}"></script>
    <style>
        .layui-table-view .layui-table{
            width: 100%;
        }
    </style>
</head>

<body>
<div class="masker"></div>
<div class="dialog" id="exportRecord">
    <div class="dialog-con" >
        <div style="height: 470px">
            <table class="layui-hide materialTable" id="materialTable" lay-filter="materialTable"></table>
        </div>
        <div class="tab-mes">
            <div class="total">共<em id="total"></em>条数据</div>
            <div class="refresh" id="refresh">刷新</div>
        </div>
    </div>
</div>
</body>
<script type="text/html" id="status">
    {{#  if(d.status==0){ }}
    <span>正在导入</span>
    {{#  } else if(d.status==1){ }}
    <span style="color: #00B368">导入成功</span>
    {{#  } else{ }}
    <span style="color: #F33131">导入失败</span>
    {{#  } }}
</script>
<script th:inline="javascript">
    var fid = [[${fid}]];
    var uid = [[${uid}]];
    layui.use(['jquery', 'table'], function () {
        var table = layui.table;
        $ = layui.jquery;
        table.render({
            elem: '#materialTable',
            url: '/examination/result/importRecord',
            where:{
                fid:fid
            },
            parseData: function (res) {
                return {
                    "code": res.code == 200 ? 0 : 1, //解析接口状态
                    "msg": res.msg, //解析提示文本
                    "count": res.data.total, //解析数据长度
                    "data": res.data.records //解析数据列表
                }
            },
            height: '470',
            cols: [
                [
                    {
                        field: "createTime",
                        title: "导入时间",
                        align: "center",
                    },
                    {
                        field: "name",
                        title: "导入人姓名",
                        align: "center",
                    },
                    {
                        field: "status",
                        title: "导入状态",
                        align: "center",
                        templet: "#status",
                    },
                    {
                        field: "msg",
                        title: "提示信息",
                        align: "center"
                    },
                ]
            ],
            done: function (res, curr, count) {
                $("#total").html(count)
                $('td[data-field=fileName] div').each(function(index,element){
                    $(element).attr('title',$(element).text());
                });
            },
            page: {
                layout: ['prev', 'page', 'next', 'limit', 'skip']
            }
        });
        // 刷新
        $("#refresh").click(function () {
            table.reload('materialTable');
        })
    })
</script>

</html>