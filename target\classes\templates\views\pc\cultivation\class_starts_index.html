<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <title>教学任务查询</title>
    <link rel="stylesheet" th:href="@{../css/cultivation/jquery.searchableSelect.css}"/>
    <link rel="stylesheet" th:href="@{../css/cultivation/newStyle.css}"/>
    <link rel="stylesheet" th:href="@{../css/cultivation/sweetalert.css}"/>
    <link rel="stylesheet" th:href="@{../plugin/layui/css/layui.css}"/>
    <style>
        html, body {
            height: 100%;
        }

        .layui-laypage .layui-laypage-curr .layui-laypage-em {
            background-color: #4C88FF !important;
        }
    </style>
</head>
<body>
<div class="jwMain">
    <div class="opt_home_top"><label class="opt_form_name">开课信息管理</label></div>
    <div class="opt_index">
        <div class="opt_top_btns">
            <ul class="opt_top_uls">
                <li class="opt_top_lis fl opt_add_lis"
                    onclick="deleteplkkxx('清空开课信息','../processData/deleteplkkxx')"><span></span>清空开课信息
                </li>
                <li class="opt_top_lis fl" onclick="exportData('导出','../processData/exportData')"><span></span>导出
                </li>
                <li class="opt_top_lis fl noClick" id="delBtn"><span></span>删除</li>
            </ul>
        </div>
        <div class="opt_search">
            <div class="opt_search_right clearAfter">
                <div class="opt_search_temp fl">
                    <div class="opt_search_name">
                        <div class="opt_search_table">
                            <div class="opt_search_cell"><span>学年学期</span></div>
                        </div>
                    </div>
                    <select id="xnxq" name="xnxq" class="qselect">
                    </select>
                </div>
                <div class="opt_search_temp fl">
                    <div class="opt_search_name">
                        <div class="opt_search_table">
                            <div class="opt_search_cell"><span>年级</span></div>
                        </div>
                    </div>
                    <select id="nj" name="nj" class="qselect">
                        <option value="">请选择</option>
                    </select>
                </div>
                <div class="opt_search_temp fl">
                    <div class="opt_search_name">
                        <div class="opt_search_table">
                            <div class="opt_search_cell"><span>院系</span></div>
                        </div>
                    </div>
                    <select id="skyx" name="skyx" class="qselect">
                        <option value="">请选择</option>
                    </select>
                </div>
                <div class="opt_search_temp fl optShowHide" style="display:none;">
                    <div class="opt_search_name">
                        <div class="opt_search_table">
                            <div class="opt_search_cell"><span>专业名称</span></div>
                        </div>
                    </div>
                    <select id="zyid" style="display:inline-block;" name="zyid" class="qselect">
                        <option value=''>请选择</option>
                    </select>
                </div>
                <div class="opt_search_temp fl optShowHide" style="display:none;">
                    <div class="opt_search_name">
                        <div class="opt_search_table">
                            <div class="opt_search_cell"><span>班级名称</span></div>
                        </div>
                    </div>
                    <select id="bjdm" style="display:inline-block;" name="bjdm" class="qselect">
                        <option value=''>请选择</option>
                    </select>
                </div>
                <div class="opt_search_temp fl optShowHide" style="display:none;">
                    <div class="opt_search_name">
                        <div class="opt_search_table">
                            <div class="opt_search_cell"><span>课程性质</span></div>
                        </div>
                    </div>
                    <select name="kcxz" id="kcxz" class="qselect">
                        <option value="">请选择</option>
                    </select>
                </div>
                <div class="opt_search_temp fl optShowHide" style="display:none;">
                    <div class="opt_search_name">
                        <div class="opt_search_table">
                            <div class="opt_search_cell"><span>选/必修</span></div>
                        </div>
                    </div>
                    <select id="sfbx" style="display:inline-block" name="sfbxdm" class="qselect">
                        <option value="" selected="selected">请选择</option>
                        <option value="选修">选修</option>
                        <option value="必修">必修</option>
                    </select>
                </div>
                <div class="opt_search_temp fl optShowHide" style="display:none;">
                    <div class="opt_search_name">
                        <div class="opt_search_table">
                            <div class="opt_search_cell"><span>课程名称</span></div>
                        </div>
                    </div>
                    <div class="opt_search_per"><input type="text" id="kcmc" placeholder="课程名称" name="kcmc"
                                                       class="opt_txt_input"></div>
                </div>
                <div class="opt_search_temp fl optShowHide" style="display:none;">
                    <div class="opt_search_name">
                        <div class="opt_search_table">
                            <div class="opt_search_cell"><span>安排状态</span></div>
                        </div>
                    </div>
                    <select name="apzt" id="apzt" class="qselect">
                        <option value="">请选择</option>
                        <option value="0">未安排</option>
                        <option value="1">部分安排</option>
                        <option value="2">已安排</option>
                    </select>
                </div>
                <div class="opt_search_temp fl optShowHide" style="display:none;">
                    <div class="opt_search_name">
                        <div class="opt_search_table">
                            <div class="opt_search_cell"><span>教师指定状态</span></div>
                        </div>
                    </div>
                    <select name="jszdzt" id="jszdzt" class="qselect">
                        <option value="">请选择</option>
                        <option value="2">已指定</option>
                        <option value="1">部分指定</option>
                        <option value="0">未指定</option>
                    </select>
                </div>
                <div class="opt_search_temp fl optShowHide" style="display:none;">
                    <div class="opt_search_name">
                        <div class="opt_search_table">
                            <div class="opt_search_cell"><span>总学时范围</span></div>
                        </div>
                    </div>
                    <div class="opt_search_per">
                        <input type="text" name="bgzxs" id="bgzxs" maxlength="4" onblur="isnum('bgzxs')"
                               class="opt_txt_input fl" style="width: 50px;">
                        <span class="popSearch_line fl">—</span>
                        <input type="text" name="ndzxs" id="ndzxs" maxlength="4" onblur="isnum('ndzxs')"
                               class="opt_txt_input fl" style="width: 50px;">
                    </div>
                </div>
                <div class="opt_btns">
                    <div class="opt_down fr"><label style="font-weight: 100;">展开</label><span></span></div>
                    <div class="opt_clear fr">清空筛选</div>
                    <div class="opt_search_btn fr">筛选</div>
                </div>
            </div>
        </div>
        <div class="opt_data">
            <div class="opt_data_top"><label class="opt_data_num"><span
                    id="totalSpan"></span><span>，已选<em>0</em>条</span></label></div>
            <div class="opt_data_cont">
                <table lay-filter="courseTable" class="layui-table" id="courseTable"></table>
            </div>
        </div>
    </div>
</div>
<script th:src="@{../js/jquery-3.3.1.min.js}"></script>
<script th:src="@{../js/jquery.nicescroll.min.js}"></script>
<script th:src="@{../js/cultivation/jquery.searchableSelect.js}"></script>
<script th:src="@{../js/sweetalert.min.js}"></script>
<!--<script th:src="@{../process/js/select_data.js}"></script>-->
<script th:src="@{../plugin/layui/layui.js}"></script>
<script type="text/html" id="toolBar">
    <a class="layui-btn layui-btn-xs" lay-event="open">开课</a>
</script>
<script>
    $('.qselect').click();
    $('.opt_down').click(function () {
        var _this = $(this).parents('.opt_search').find('.optShowHide');
        if (_this.is(':visible')) {
            _this.hide();
            $(this).removeClass('opt_up')
            $(this).find('label').text('展开')
        } else {
            _this.show();
            $(this).addClass('opt_up')
            $(this).find('label').text('收起')
        }
    })

    function generatePlan(title, url, gridId, width, height) {
        if (navigator.userAgent.match(/(iPhone|iPod|Android|ios)/i)) {//如果是移动端，就使用自适应大小弹窗
            width = 'auto';
            height = 'auto';
        } else {//如果是PC端，根据用户设置的width和height显示。
            width = '100%';
            height = '100%';
        }
        layer.open({
            skin: 'btn-class',
            type: 2,
            area: [width, height],
            title: title,
            maxmin: true, //开启最大化最小化按钮
            content: url,
            btn: ['提交', '取消'],
            yes: function (index, layero) {//确定按钮
                var iframeWin = layero.find('iframe')[0];
                iframeWin.contentWindow.savejxb('保存', '', 'jxblistGridIdGrid', '800px', '500px');
            },
            cancel: function (index) {
                layer.closeAll();
            },
            end: function () {
                layer.closeAll();
            }
        });
    }

    function deleteplkkxx(title, url) {
        var ids = [];
        var rows = table.checkStatus('courseTable').data;
        if (rows.length > 0) {
            swal({
                title: "提示",
                text: "您确定要删除这些信息么，请谨慎操作！",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#DD6B55",
                confirmButtonText: "确定",
                closeOnConfirm: false,
                cancelButtonText: "取消",
            }, function () {
                for (var i = 0; i < rows.length; i++) {
                    ids.push(rows[i]);
                }
                $.ajax({
                    url: url,
                    type: 'post',
                    data: {
                        kkxxid: ids.join('%')
                    },
                    cache: false,
                    success: function (d) {
                        if (d.ret == 1) {
                            var msg = d.msg;
                            swal("提示！", msg, "success");
                            $(".layui-laypage-btn")[0].click();
                        } else {
                            var msg = d.msg;
                            swal("提示！", msg, "error");
                        }
                    }
                });
            });
        } else {
            layer.alert('请选择需要删除的数据!', {icon: 0, title: '警告'});
        }
    }

    $(".opt_search_btn").click(function () {
        var xnxq = $("select[name='xnxq']").val();
        var nj = $("select[name='nj']").val();
        var skyx = $("select[name='skyx']").val();
        var zyid = $("select[name='zyid']").val();
        var bjdm = $("select[name='bjdm']").val();
        var kcxz = $("select[name='kcxz']").val();
        var sfbxdm = $("select[name='sfbxdm']").val();
        var kcmc = $("input[name='kcmc']").val();
        var apzt = $("select[name='apzt']").val();
        var jszdzt = $("select[name='jszdzt']").val();
        var bgzxs = $("input[name='bgzxs']").val();
        var ndzxs = $("input[name='ndzxs']").val();
        var field = {
            semester: xnxq,
            gradeName: nj,
            collegeName: skyx,
            majorName: zyid,
            className: bjdm,
            courseNature: kcxz,
            courseType: sfbxdm,
            courseName: kcmc,
            apzt: apzt,
            jszdzt: jszdzt,
            bgzxs: bgzxs,
            ndzxs: ndzxs
        };
        insTb.reload({where: field, page: {curr: 1}});
    })

    $(".opt_clear").click(function () {
        location.reload();
    })

    var table = "", insTb = "";
    layui.use('table', function () {
        table = layui.table;
        insTb = table.render({
            elem: '#courseTable',
            url: '../processData/getClassStartsData',
            page: true,
            cellMinWidth: 100,
            cols: [
                [
                    {type: 'checkbox', field: 'id', fixed: 'left'},
                    {field: 'semester', title: '学年学期号'},
                    {field: 'courseName', title: '课程名称'},
                    {field: 'courseId', title: '课程编号'},
                    {field: 'teacher', title: '教师'},
                    {field: 'className', title: '行政班'},
                    {field: 'gradeName', title: '年级'},
                    {field: 'collegeName', title: '院系'},
                    {field: 'majorName', title: '专业'},
                    {field: 'courseNature', title: '课程性质'},
                    {field: 'classNum', title: '班级人数'},
                    {field: 'notScheduledNum', title: '未安排人数'},
                    {field: 'kkTotalClassHours', title: '开课总学时'},
                    {field: 'credit', title: '学分'},
                    {field: 'totalClassHours', title: '总学时'},
                    {field: 'llClassHours', title: '理论学时'},
                    {field: 'sjClassHours', title: '上机学时'},
                    {field: 'syClassHours', title: '实验学时'},
                    {field: 'practiceClassHours', title: '实践学时'},
                    {field: 'weekClassHours', title: '周学时'},
                    {field: 'practiceWeeks', title: '实践周数'},
                    {title: '选/必修', field: "courseType"},
                    {title: '操作', toolbar: '#toolBar', width: 150, fixed: 'right'}
                ]
            ],
            done: function (res, curr, count) {
                $(".opt_data_num #totalSpan").text("共 " + count + " 条");
            }
        });
        //监听表格复选框选择
        table.on('checkbox(courseTable)', function (obj) {
            //全选时
            if (obj.type == "all") {
                if (obj.checked) {//全选中
                    var checkStatus = table.checkStatus('courseTable');
                    var sdata = checkStatus.data;
                    $(".opt_data_num span em").text(sdata.length);
                    if (sdata.length > 0) {//渲染背景颜色
                        $("#delBtn").removeClass("noClick");
                        $(".layui-table-body .layui-table tr").each(function () {
                            $(this).addClass("tr_bj_color");
                        })
                    }
                } else {//全部不选
                    $(".opt_data_num span em").text(0);
                    $("#delBtn").addClass("noClick");
                    $(".layui-table-body .layui-table tr").each(function () {
                        $(this).removeClass("tr_bj_color");
                    })
                }
            } else {//单选
                var checkStatus = table.checkStatus('courseTable');
                var sdata = checkStatus.data;
                $(".opt_data_num span em").text(sdata.length);
                if (obj.checked) {//选中
                    $("#delBtn").removeClass("noClick");
                    obj.tr.addClass('tr_bj_color');
                } else {//取消选中
                    $("#delBtn").addClass("noClick");
                    obj.tr.removeClass('tr_bj_color');
                }
            }
        });

        table.on('tool(courseTable)', function (obj) {
            var data = obj.data;
            var id = data.id;
            if (obj.event === 'open') {
                var url = "../process/classStartsDetail?infoId=" + id;
                generatePlan('开课', url, '', '1000px', '500px');
            }
        })
    });

    function isnum(id) {
        var str = $("#" + id).val();
        if (str.length != 0) {
            var reg = /^[0-9]+$/;
            var r = str.match(reg);
            if (r == null) {
                $("#" + id).val("");
            }
        }
    }
</script>
</body>
</html>