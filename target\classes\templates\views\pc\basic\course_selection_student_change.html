<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学籍异动-选课处理</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/basic/global.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/plugin/layui-v2.8.18/layui/css/layui.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/basic/layuiReset.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/basic/dialog.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/basic/studentStatusChange.css'}">
    <style>
        .layui-border-red {
            border-width: 1px;
            border-style: solid;
            border-color: #ff5722 !important;
            color: #ff5722 !important;
        }
    </style>
    <script th:src="${_CPR_+_VR_+'/plugin/layui-v2.8.18/layui/layui.js'}"></script>
</head>

<body>
<div class="main">
    <div class="m-top">
        <div class="title">选课处理</div>
        <div>
            <button class="layui-btn layui-btn-primary layui-border-red" id="closePage">关闭</button>
            <button class="layui-btn" style="margin-right: 30px;" id="saveBtn">确定</button>
        </div>
    </div>
    <div class="item" style="align-items: flex-end;">
        <div class="change-stu-con">
            <dl>
                <dt>异动生效学期：</dt>
                <dd th:text="${detail?.spb_ydsxxq}"></dd>
            </dl>
            <dl>
                <dt>学生姓名：</dt>
                <dd th:text="${detail?.spb_sqrxm}"></dd>
            </dl>
            <dl>
                <dt>学号：</dt>
                <dd th:text="${detail?.spb_sqrxh}"></dd>
            </dl>
            <dl>
                <dt>异动申请时间:</dt>
                <dd th:text="${#dates.format(detail?.spb_ydtjsj, 'yyyy-MM-dd')}"></dd>
            </dl>
            <dl>
                <dt>异动前年级：</dt>
                <dd th:text="${detail?.spb_ydqsznj}"></dd>
            </dl>
            <dl>
                <dt>系部：</dt>
                <dd th:text="${detail?.spb_ydqszyx}"></dd>
            </dl>
            <dl>
                <dt>专业：</dt>
                <dd th:text="${detail?.spb_ydqszzy}"></dd>
            </dl>
            <dl>
                <dt>班级：</dt>
                <dd th:text="${detail?.spb_ydqszbj}"></dd>
            </dl>
        </div>
        <div class="change-stu-con">
            <dl>
                <dt>异动后年级：</dt>
                <dd th:text="${detail?.spb_ydhsznj}"></dd>
            </dl>
            <dl>
                <dt>系部：</dt>
                <dd th:text="${detail?.spb_ydhszyx}"></dd>
            </dl>
            <dl>
                <dt>专业：</dt>
                <dd th:text="${detail?.spb_ydhszzy}"></dd>
            </dl>
            <dl>
                <dt>班级：</dt>
                <dd th:text="${detail?.spb_ydhszbj}"></dd>
            </dl>
        </div>
    </div>
    <div class="item">
        <div class="i-con">
            <div class="class-box">
                <div class="cb-top">
                    <div class="tit" style="margin-right:24px;">学生现选课程</div>
                    <button class="layui-btn" id="addStuClass">增加</button>
                </div>
                <div class="j-table">
                    <table class="layui-table layui-hide" id="stuCourse" lay-filter="stuCourse">
                    </table>
                </div>
            </div>
        </div>
        <div class="i-con">
            <div class="class-box">
                <div class="cb-top">
                    <div class="tit" style="margin-right:24px;">新班级课程</div>
                    <button class="layui-btn" id="addClasCourse">增加</button>
                </div>
                <div class="j-table">
                    <table class="layui-table layui-hide" id="newClassCourse" lay-filter="newClassCourse">
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="item">
        <div class="i-con" style="width: 100%;">
            <div class="class-box">
                <div class="cb-top">
                    <div class="tit" style="margin-right:24px;">异动后选课处理区</div>
                    <button class="layui-btn layui-btn-primary layui-border-red" id="deleteTrData">删除</button>
                </div>
                <div class="j-table" style="width: 100%;">
                    <table class="layui-table layui-hide" id="changeTable" lay-filter="changeTable">
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="dialog" id="tipDialog">
    <div class="dialog-title">
        <h5>提示</h5><span class="close"></span>
    </div>
    <div class="dialog-con">
        <span></span> 异动后选课处理未保存，确定是否关闭
    </div>
    <div class="dialog-footer">
        <button class="btnCancel" id="tipCancel">取消</button>
        <button class="btnSure" id="tipSure">确定</button>
    </div>
</div>
<div class="dialog" id="handleSuccessfulDialog">
    <div class="dialog-con">
        <div class="img">
            <img src="/images/basic/success-icon.png" alt="">
            <p>处理成功！</p>
        </div>
    </div>
    <div class="dialog-footer">
        <button class="btnSure">确定</button>
    </div>
</div>
<div class="dialog" id="failureRecordDialog">
    <div class="dialog-title">
        <h5>选课处理失败记录</h5>
    </div>
    <div class="dialog-con">
        <div class="total-data">共<i>0</i>条数据</div>
        <table class="layui-table layui-hide" id="failTable" lay-filter="failTable">
        </table>
    </div>
    <div class="dialog-footer">
        <button class="btnSure">确定</button>
    </div>
</div>
</body>
<script th:inline="javascript">
    let _VR_ = [[${_VR_}]];
    let detail = [[${detail}]];
</script>
<script th:src="@{${_CPR_+_VR_}+'/js/basic/studentStatusChange.js'(v=${new java.util.Date().getTime()})}"></script>
</html>