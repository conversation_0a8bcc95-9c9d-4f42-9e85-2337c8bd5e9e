<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>继承</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/basic/global.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/basic/slideCommon.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/basic/dialog.css'}">
</head>

<body>

    <div class="mask"></div>
    <div class="dialog" id="inheritDialog">
        <div class="dialog-title">
            <h5>继承</h5><span class="close"></span>
        </div>
        <div class="dialog-con" style="height: 355px;">
            <div class="dialog-con-title">继承来源设置</div>
            <div class="set-con">
                <div class="set-item">
                    <h4>数据来源</h4>
                    <div class="set-block">
                        <div class="j-search-con single-box">
                            <input type="text" name="dataOrigin" placeholder="请选择" readonly="" class="schoolSel">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <ul data-name="dataOrigin">
                                    <li value="0">学生基本信息</li>
                                    <li value="1">学期学生数据 </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="set-item" style="display: none;">
                    <h4>来源学期</h4>
                    <div class="set-block">
                        <div class="j-search-con single-box">
                            <input type="text" name="originTerm" placeholder="请选择"  readonly=""
                                   class="schoolSel"
                                   formAlias="xqxssj" fieldAlias="xqxssj_xnxq">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <ul data-name="originTerm">
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="set-item">
                    <h4>目标学期</h4>
                    <div class="set-block">
                        <div class="j-search-con single-box">
                            <input type="text" name="term" placeholder="请选择" th:value="${semester}" readonly=""
                                   class="schoolSel" formAlias="xqjssj" fieldAlias="xqjssj_xnxq">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <ul data-name="term">
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="set-item">
                    <h4>继承学生当前状态</h4>
                    <div class="set-block">
                        <div class="j-search-con multiple-box">
                            <input type="text" name="stuStatus" placeholder="请选择" readonly=""
                                   class="schoolSel" formAlias="218599" fieldAlias="student_state">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="all-selects" style="margin-top: 8px;">全选</div>
                                <ul data-name="stuStatus">
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="set-item">
                    <h4>继承方式</h4>
                    <div class="set-block">
                        <div class="j-search-con single-box">
                            <input type="text" name="inheritType" placeholder="请选择" readonly="" class="schoolSel">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <ul data-name="inheritType">
                                    <li value="0">追加</li>
                                    <li value="1">覆盖</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="dialog-footer">
            <button class="btnCancel">取消</button>
            <button class="btnSure" id="btnInheritSure">确定</button>
        </div>
    </div>
</body>
<script th:src="${_CPR_+_VR_+'/js/jquery-3.3.1.min.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/basic/slideCommon.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/my.util.js'}"></script>
<script th:inline="javascript">
    const _VR_ = [[${_VR_}]];
    $(function () {
        $("ul[data-name=dataOrigin]").on('click', 'li', function () {
            $(this).text().trim() == '学期学生数据' ? $(this).parents('.set-item').next().show() : $(this).parents('.set-item').next().hide()
        })

        $("#btnInheritSure").click(function () {
            // 设置内容
            var selParams = {}
            $("#inheritDialog ul").each(function () {
                const key = $(this).attr("data-name");
                let val = $(this).find(".active").attr('value');
                let ipt = $(this).parents(".j-search-con").find("input").val();
                selParams[key] = val ? val : ipt;
            })
            const requiredFields = [
                {key: 'dataOrigin', message: '请选择数据来源'},
                {key: 'originTerm', message: '请选择来源学期'},
                {key: 'term', message: '请选择目标学期'},
                {key: 'stuStatus', message: '请选择继承教师状态'},
                {key: 'inheritType', message: '请选择继承方式'}
            ];
            console.log(selParams);
            for (const field of requiredFields) {
                if (selParams["dataOrigin"] === "0" && field.key === "originTerm") {
                    continue;
                }
                if (!selParams[field.key]) {
                    U.fail(field.message);
                    return false;
                }
            }
            U.ajax({
                type: 'post',
                url: "/basic/pop/inheritSemesterStudent",
                data: $.param(selParams),
                dataType: 'json',
                success: function (result) {
                    if (!result.status) {
                        U.fail(result.msg);
                        return false;
                    }
                    U.success("继承成功");
                    setTimeout(function () {
                        window.parent.postMessage(JSON.stringify({action: 1}), "*");
                    }, 2000);
                }
            })
        })
        $("#inheritDialog .close,.btnCancel").click(function () {
            window.parent.postMessage(JSON.stringify({action: 1}), "*");
            $("#inheritDialog,.mask").hide();
        })
    })
</script>

</html>