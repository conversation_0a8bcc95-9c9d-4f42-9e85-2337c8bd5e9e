<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>通知提醒框</title>
  <style>
    * {
      margin: 0;
      padding: 0;
    }

    .dialog-wrap {
      position: fixed;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      z-index: 9999;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
    }

    .dialog-wrap .dialog {
      position: fixed;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      background-color: #ffffff;
      width: 560px;
      padding: 40px 0;
      border-radius: 10px;
      overflow: hidden;
    }

    .dialog-wrap .dialog img {
      display: block;
      margin: 0 auto;
    }

    .dialog-wrap .dialog p {
      text-align: center;
      font-size: 16px;
      color: #1d2129;
      margin: 24px 0;
    }

    .dialog-wrap .dialog button {
      border-radius: 18px;
      background: var(--unnamed, #4d88ff);
      box-shadow: 0px 0px 10px 0px rgba(0, 108, 226, 0.4);
      width: 88px;
      height: 36px;
      margin: 0 auto;
      display: block;
      outline: none;
      border: 1px solid #4d88ff;
      color: #ffffff;
      cursor: pointer;
    }
  </style>
</head>

<body>
  <div class="dialog-wrap dialog-tips">
    <div class="dialog">
      <img th:src="@{~/images/creditManage/error-icon.png}" alt="">
      <p th:text="${tips}"></p>
    </div>
  </div>
</body>

</html>