<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分配教材</title>
    <link rel="stylesheet" href="/distribution/css/global.css">
    <link rel="stylesheet" href="/distribution/layui/css/layui.css">
    <link rel="stylesheet" href="/distribution/css/reset.css">
    <link rel="stylesheet" href="/distribution/css/fpjc.css">
    <script src="/distribution/layui/layui.js"></script>
</head>

<body>
    <div class="j-material-wrap">
        <div class="j-material">
            <div class="j-title">
                <h4>分配教材</h4>
                <div class="j-btns">
                    <!--<button class="btn-cancel">取消</button>-->
                    <button class="btn-complate">完成</button>
                </div>
            </div>
            <h4 class="j-title-s">已选课程</h4>
            <div class="j-table">
                <table class="layui-table" id="courseTable" lay-filter="courseTable" lay-data='{page: true}'>
                </table>
            </div>
        </div>
        <div class="j-material">

            <div class="j-title-wrap">
                <h4 class="j-title-s">为<em id = "selectCourseNames">“”</em>指定教材</h4>
                <div class="j-checkbox">
                    <ul>
                        <li><i class="layui-icon layui-icon-square layui-icon-ok" id="student-type"></i>指定为学生教材</li>
                        <li><i class="layui-icon layui-icon-square layui-icon-ok" id="teacher-type"></i> 指定为老师教材</li>
                    </ul>
                </div>
            </div>

            <div class="j-opt">
                <div class="j-search">
                    <!-- <h5>筛选延用课程</h5> -->
                    <input type="text" placeholder="输入教材名称" id="courseSearch">

                    <button id="searchBtn">搜索</button>
                    <!--<div class="j-select">
                        <ul>
                            <li>
                                <h3>大学<em>英语</em>第二版</h3>
                                <p><span>电子科技大学出版社</span><span>赵野编</span></p>
                            </li>
                            <li>
                                <h3>大学<em>英语</em>第二版</h3>
                                <p><span>电子科技大学出版社</span><span>赵野编</span></p>
                            </li>
                            <li>
                                <h3>大学<em>英语</em>第二版</h3>
                                <p><span>电子科技大学出版社</span><span>赵野编</span></p>
                            </li>

                        </ul>
                    </div>-->


                </div>

            </div>
            <div class="j-table">
                <table class="layui-table" id="materialTable" lay-filter="materialTable">
                </table>
            </div>
        </div>
    </div>

</body>
<script th:inline="javascript">
    var selectCourseNames = "";
    var fid = [[${fid}]];
    var uid = [[${uid}]];
    layui.use(['table', 'jquery', 'laydate', 'layer'], function () {
        var table = layui.table,
            laydate = layui.laydate,
            $ = layui.jquery;
        /* ********************* 已选课程 ************************ */

        table = $.extend(table, {
            config: {
                checkName: 'checked'
            }
        })

        var table1fieldArray = []
        table1fieldArray.push({field: "id", type: 'checkbox', width: 100});
        table1fieldArray.push({field: "jcxnxq", align: "center", title: "学年学期"});
        table1fieldArray.push({field: "jckcmc", align: "center", title: "课程名称"});
        table1fieldArray.push({field: "jckclb", align: "center", title: "课程类别"});
        table1fieldArray.push({field: "jcskjs", align: "center", title: "课程老师"});
        table1fieldArray.push({field: "jcjxbm", align: "center", title: "教学班名"});
        table.render({
            id: "courseTable",
            elem: "#courseTable",
            type: "post",
            url: "/api/distribution/getDataList",
            where: {
                fid: fid,
                formalias: "jc_xzbkcxx",
                keyword: ""
            },
            request: {
                pageName: "currentPage",
                limitName: "pageSize"
            },
            limits: [10, 20, 30, 40, 50, 60, 70, 80, 90],
            page: {curr: 1},
            limit: 10,
            cols: [table1fieldArray],
            parseData: function (res) {
                return {
                    "count": res.count,
                    "data": res.dataList,
                    "code": "0"
                }
            },
        });
        // 监听table复选框
        // 默认选中第一行
        $('.layui-table-box').find("tr[data-index=0]").find(
            "td div.laytable-cell-checkbox div.layui-form-checkbox I").click();
        $(".layui-table-body table.layui-table tbody tr").eq(0).addClass('trSel');
        $(document).on("click", ".layui-table-body table.layui-table tbody tr", function () {
            var index = $(this).attr('data-index');
            var tableBox = $(this).parents('.layui-table-box');
            //存在固定列
            if (tableBox.find(".layui-table-fixed.layui-table-fixed-l").length > 0) {
                tableDiv = tableBox.find(".layui-table-fixed.layui-table-fixed-l");
            } else {
                tableDiv = tableBox.find(".layui-table-body.layui-table-main");
            }
            var checkCell = tableDiv.find("tr[data-index=" + index + "]").find(
                "td div.laytable-cell-checkbox div.layui-form-checkbox I");
            var checkCellSel = tableDiv.find("tr[data-index=" + index + "]").find(
                "td div.laytable-cell-checkbox div.layui-form-checkbox");
            if (checkCell.length > 0) {
                checkCell.click();
                if (checkCellSel.hasClass('layui-form-checked')) {
                    //  选中
                    $(this).addClass("trSel");
                } else {
                    // 取消选中
                    $(this).removeClass("trSel");
                }
            }
        });
        //对td的单击事件进行拦截停止，防止事件冒泡再次触发上述的单击事件（Table的单击行事件不会拦截，依然有效）
        $(".j-table").on("click", "tr td:first-child div.laytable-cell-checkbox div.layui-form-checkbox",
            function (e) {
                e.stopPropagation();
            });

        table.on('checkbox(courseTable)', function (obj) {
            var data = obj.data;
            // 当前行数据
            console.log(obj);
            // 所有选中数据
            let selData = layui.table.checkStatus('courseTable');
            console.log(selData);

            //判断是否被选中
            if (obj.tr.find('.layui-form-checkbox').hasClass('layui-form-checked')) {
                obj.tr.addClass("trSel");
                if(selectCourseNames.indexOf(data.jckcmc + "，") == -1){
                    selectCourseNames += data.jckcmc + "，";
                }
            } else {
                // 取消选中
                obj.tr.removeClass("trSel");
                selectCourseNames = selectCourseNames.replaceAll(data.jckcmc + "，", "");
            }
            $("#selectCourseNames").html("“" + selectCourseNames + "”");
        });


        /* ********************* 指定教材 ************************ */
        var table1fieldArray = []
        table1fieldArray.push({field: "id", type: 'checkbox', width: 100});
        table1fieldArray.push({field: "jcmc", align: "center", title: "教材名称"});
        table1fieldArray.push({field: "jcbh", align: "center", title: "教材编号"});
        table1fieldArray.push({field: "isbn", align: "center", title: "ISBN"});
        table1fieldArray.push({field: "cbs", align: "center", title: "出版社"});
        table1fieldArray.push({field: "jczz", align: "center", title: "教材作者"});
        table1fieldArray.push({field: "cbny", align: "center", title: "出版年月"});

        //table1fieldArray.push({field: "10", align: "center", title: "教材名称"});
        //table1fieldArray.push({field: "19", align: "center", title: "教材编号"});
        //table1fieldArray.push({field: "20", align: "center", title: "ISBN"});
        //table1fieldArray.push({field: "12", align: "center", title: "出版社"});
        //table1fieldArray.push({field: "13", align: "center", title: "教材作者"});
        //table1fieldArray.push({field: "14", align: "center", title: "出版年月"});
        table.render({
            id: "materialTable",
            elem: "#materialTable",
            type: "post",
            url: "/api/distribution/getDataList",
            where: {
                fid: fid,
                formalias: "jc_jcxx",
                keyword: $("#courseSearch").val()
            },
            request: {
                pageName: "currentPage",
                limitName: "pageSize"
            },
            limits: [10, 20, 30, 40, 50, 60, 70, 80, 90],
            page: {curr: 1},
            limit: 10,
            cols: [table1fieldArray],
            parseData: function (res) {
                return {
                    "count": res.count,
                    "data": res.dataList,
                    "code": "0"
                }
            },
        });
        // 搜索
        $("#courseSearch").on('keyup', function () {
            //$(this).parent().find(".j-select").show();
        });

        $("#searchBtn").on('click', function () {
            layui.table.reload('materialTable', {
                page: {curr: 1}
                , where: {
                    fid: fid,
                    formalias: "jc_jcxx",
                    keyword: $("#courseSearch").val()
                },
            }, 'data');
        });

        // 多选
        $(".j-checkbox ul li").click(function () {
            $(this).find('.layui-icon').toggleClass('layui-icon-ok');
        })
        // 选择
        $(".j-select").on('click', "ul li", function () {
            $(this).addClass('active').siblings().removeClass('active');
            $(this).parents('.j-select').hide();

        })
        // 点击页面其他地方小时
        $(document).on('click', function (e) {
            if ($(e.target).closest('.j-select').length > 0) {
                // alert('弹出框内部被点击了');
            } else {
                $(".j-select").hide();
            }
        })

        // 添加表单数据
        $(".btn-complate").click(function () {
            var type = "";
            var selectCourseCells = layui.table.checkStatus('courseTable'),
                coursedata = selectCourseCells.data;
            var selectjiaocaiCells = layui.table.checkStatus('materialTable'),
                jiaocaidata = selectjiaocaiCells.data;
            if($("#student-type").hasClass("layui-icon-ok")){
                type += "1,";
            }
            if($("#teacher-type").hasClass("layui-icon-ok")){
                type += "2,";
            }
            if(coursedata.length == 0){
                layer.msg("请选择课程数据");
                return;
            }
            if(jiaocaidata.length == 0){
                layer.msg("请选择教材数据");
                return;
            }
            if(type == ""){
                layer.msg("请选择教材类型");
                return;
            }
            var formId = getQueryString("formId");
            if(formId == null){
                formId = "268361";
            }
            $.ajax({
                type: 'get',
                dataType: 'json',
                url: '/api/distribution/insertFormData',
                async: false,
                data: {"coursejson": JSON.stringify(coursedata),
                    "jiaocaijson": JSON.stringify(jiaocaidata),
                    "type": type, "uid": uid, "fid": fid, formId: formId},
                success: function (data){
                    console.log(data);
                    if(data.success){
                        layer.msg("添加成功");
                    } else {
                        layer.msg(data.msg);
                    }
                }
            });
        })
    })

    function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]); return null;
    }
</script>

</html>