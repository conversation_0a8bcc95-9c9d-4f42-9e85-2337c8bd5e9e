<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>教材变更登记</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/material/global.css'}"/>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/plugin/layui/css/layui.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/material/material_change_register_pop.css'}"/>
</head>

<body class="style1">
<div id="textbookChanges" class="textbook-changes-popup popups">
    <div class="illustrate">
        <ul>
            <li>注意：进行此操作会将本学期已征订的所有原教材替换为变更后的教材</li>
        </ul>
    </div>
    <div class="popup-con">
        <div class="filter-options">
            <div class="lable">
                <div class="name">学年学期</div>
                <div class="input">
                    <input type="text" disabled class="layui-input layui-disabled" th:value="${detail.jc_xnxq}"
                           placeholder="请输入学学年学期">
                </div>
            </div>
            <div class="lable">
                <div class="name"></div>
                <div class="input"></div>
            </div>
            <div class="lable" th:attr="bh=${detail.jc_jcbh}">
                <div class="name">原教材</div>
                <div class="input">
                    <input type="text" disabled class="layui-input layui-disabled" th:value="${detail.jc_jcmc}"
                           placeholder="请输入原教材">
                </div>
            </div>
            <div class="lable">
                <div class="name">ISBN号</div>
                <div class="input">
                    <input type="text" disabled class="layui-input layui-disabled" th:value="${detail.jc_isbn}"
                           placeholder="请输入ISBN号">
                </div>
            </div>
            <div class="lable sel">
                <div class="name">变更教材</div>
                <div class="select-input">
                    <div class="name">请选择</div>
                    <em></em>
                    <div class="select-dropdown">
                        <div class="search">
                            <input type="text" placeholder="搜索">
                            <span></span>
                        </div>
                        <ul class="dropdown-lists dropdown-lists-single">
                        </ul>
                    </div>
                </div>
            </div>
            <div class="lable">
                <div class="name">ISBN号</div>
                <div class="input">
                    <input type="text" disabled class="layui-input layui-disabled" value=""
                           placeholder="请输入ISBN号">
                </div>
            </div>
        </div>
        <div class="change-remarks">
            <div class="name">变更备注</div>
        </div>
        <div class="textarea">
            <textarea rows=5 class="layui-textarea" style="resize: none;" placeholder="请输入变更备注"
                      name=""></textarea>
        </div>
    </div>
    <div class="bottom">
        <div class="cancle">取消</div>
        <div class="confirm">确定</div>
    </div>
</div>
</body>
<script th:src="${_CPR_+_VR_+'/js/jquery-1.11.3.min.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/material/common.js'}"></script>
<script th:src="${_CPR_+_VR_+'/plugin/layui/layui.js'}"></script>
<script th:inline="javascript">
    const _VR_ = [[${_VR_}]];
    $(function () {
        layui.use(["layer"], function () {
            const $ = layui.jquery,
                layer = layui.layer;
            //隐藏弹窗
            $('.close,.cancle').on("click", function () {
                closePop();
            })
            $(".confirm").click(function () {
                if ($(".ckd").length === 0) {
                    layer.msg("请选择变更教材", {icon: 2, time: 2000});
                    return false;
                }
                if ($(".lable").eq(2).attr("bh") === $(".sel").attr("bh")) {
                    layer.msg("原教材与变更教材相同不符合触发条件", {icon: 2, time: 2000});
                    return false;
                }
                layer.load(1);
                $.post(_VR_ + "/material/pop/materialChangeRegister", {
                    fid: [[${formRightBtnBO.fid}]],
                    uid: [[${formRightBtnBO.uid}]],
                    jc_yjcmc: $(".lable").eq(2).find("input").val(),
                    jc_yjcisbn: $(".lable").eq(3).find("input").val(),
                    jc_bghjcmc: $(".ckd").text(),
                    jc_bghjcisbn: $(".sel").next().find("input").val(),
                    jc_bgyy: $(".layui-textarea").val(),
                    jc_xnxq: $(".lable").eq(0).find("input").val(),
                    original_jc_bh: [[${detail.jc_jcbh}]],
                    jc_bh: $(".sel").attr("bh"),
                    jc_cbs: $(".sel").attr("cbs"),
                    jc_zz: $(".sel").attr("zz"),
                    jc_sy: $(".sel").attr("sy"),
                    jc_zk: $(".sel").attr("zk"),
                    jc_my: $(".sel").attr("my")
                }, function (result) {
                    layer.msg("变更教材同步中", {icon: 1, time: 3000});
                    setTimeout(closePop, 1000);
                });
            })

            $(".select-input .name").click(function () {
                let _this = $(this);
                if (_this.parent().find("ul li").length > 0) {
                    return false;
                }
                $.post(_VR_ + "/material/pop/getMaterialDataList", {fid: [[${formRightBtnBO.fid}]]}, function (result) {
                        let html = "";
                        $.each(result.data, function (i, item) {
                            html += "<li " +
                                "bh = \"" + item.jc_jcbh + "\"" +
                                "cbs = \"" + item.jc_cbs + "\"" +
                                "zz= \"" + item.jc_zz + "\"" +
                                "sy= \"" + item.jc_sy + "\"" +
                                "zk= \"" + item.jc_zk + "\"" +
                                "my= \"" + item.jc_my + "\"" +
                                "isbn=\"" + item.jc_isbn + "\">" + item.jc_jcmc + "</li>";
                        });
                        _this.parent().find("ul").html(html);
                    }
                )
            });

            $(".sel .select-input").on("click", ".select-dropdown .dropdown-lists li", function () {
                $(this).parents(".sel")
                    .attr("bh", $(this).attr("bh"))
                    .attr("cbs", $(this).attr("cbs"))
                    .attr("zz", $(this).attr("zz"))
                    .attr("sy", $(this).attr("sy"))
                    .attr("zk", $(this).attr("zk"))
                    .attr("my", $(this).attr("my"));
                $(this).parents(".sel").next().find("input").val($(this).attr("isbn"));
            })

            function closePop() {
                window.parent.postMessage(JSON.stringify({action: 1}), "*");
            }
        });
    })
    ;
</script>
</html>