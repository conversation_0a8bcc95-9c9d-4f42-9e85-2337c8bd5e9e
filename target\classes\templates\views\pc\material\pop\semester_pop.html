<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>学年学期</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/plugin/layui/css/layui.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/dialog.css'}">
    <script th:src="${_CPR_+_VR_+'/plugin/layui/layui.js'}"></script>
</head>
<body>
<div class="dialog" id="invigilateMax">
    <div class="dialog-con" style="margin: -24px 58px 149px 33px;height: 120px;">
        <div class="item">
            <div class="label">目标学年学期</div>
            <div class="j-search-con multiple-box">
                <input type="text" name="term" placeholder="请选择" readonly="" class="schoolSel">
                <span class="j-arrow"></span>
                <div class="j-select-year">
                    <div class="search">
                        <input type="text" placeholder="搜索">
                        <span></span>
                    </div>
                    <div class="all-selects">全选</div>
                    <ul class="dropdown-list" style="max-height: 130px;">
                        <li th:each="term:${list}"><span th:text="${term.jc_xnxq}"></span></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <div class="dialog-btn">
        <button class="pu-cancel">取消</button>
        <button class="pu-sure" id="invigilateSure" style="width: 88px;">确定</button>
    </div>
</div>
</body>
<script th:src="${_CPR_+_VR_+'/js/jquery-1.11.3.min.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/my.util.js'}"></script>
<script th:inline="javascript">
    document.domain = "chaoxing.com";
    var formId = [[${formTopBtnBO.formId}]];
    var fid = [[${formTopBtnBO.fid}]];
    var uid = [[${formTopBtnBO.uid}]];
    var queryId = [[${formTopBtnBO.queryId}]];
    let dataType = [[${dataType}]];
    const _VR_ = [[${_VR_}]];
    let layer = "";
    layui.use(['layer'], function () {
        layer = layui.layer;
    })

    $(".pu-sure").click(function () {
        let semester = $("input[name='term']").val();
        if (!semester) {
            U.fail("请选择学年学期！");
            return false;
        }
        location.href = "/api/form-btn/gm/outpost.popup?code=8T10000&semester=" + semester + "&formId=" + formId + "&fid=" + fid + "&queryId=" + queryId + "&uid=" + uid;
        return false;
    })

    $(".pu-cancel").click(function () {
        window.parent.postMessage(JSON.stringify({action: 1}), "*");
    })

    $(document).ready(function () {
        /* ***************** 下拉 **************************** */
        // 下拉多选
        $(".j-search-con").on("click", ".schoolSel", function (e) {
            $(".j-select").hide();
            let parent = $(this).parent();
            parent.find('.j-arrow').toggleClass('j-arrow-slide');
            parent.find('.j-select-year').toggleClass('slideShow');
            var sibling = $(this).parents('.item').siblings();
            sibling.find('.j-arrow').removeClass('j-arrow-slide');
            sibling.find('.j-select-year').removeClass('slideShow');
            stopBubble(e)
        })
        // 全选/取消全选
        $(".j-search-con").on('click', '.j-select-year .all-selects', function (e) {
            $(this).toggleClass("active");
            if ($(this).hasClass("active")) {
                $(this).text('取消全选');
                let selCon = [];
                let curEles = $(this).parent().find("li");
                curEles.each((index, ele) => {
                    if ($(ele).is(':hidden')) {
                        return true;
                    }
                    $(ele).addClass("active");
                    let txt = $(ele).text();
                    selCon.push(txt);
                });
                $(this).parents('.j-search-con').find('.schoolSel').val(selCon.join(','))

            } else {
                $(this).next().find("li").removeClass("active");
                $(this).text('全选');
                $(this).parents('.j-search-con').find('.schoolSel').val('');
            }
            stopBubble(e)
        })
        // 选择-多选
        $(".j-search-con").on("click",
            ".j-select-year li ",
            function (e) {
                $(this).toggleClass("active");
                var parentEle = $(this).parent();
                var totallis = parentEle.find(".active").length;
                var curlis = parentEle.find("li:not(.active)").length;
                var prev = parentEle.prev(".all-selects")
                if (totallis == curlis) {
                    prev.addClass("active");
                    prev.text('取消全选')
                } else {
                    prev.removeClass("active");
                    prev.text('全选')
                }

                let selCon = [];
                let curEles = parentEle.find(".active");

                curEles.each((index, ele) => {
                    let txt = $(ele).text();
                    selCon.push(txt);
                });
                var schoolSelEle = $(this).parents('.j-search-con').find('.schoolSel')
                if (curEles.length > 0) {
                    schoolSelEle.val(selCon.join(','))
                } else {
                    schoolSelEle.val('');
                }
                stopBubble(e);
            })
        //  选择-单选
        $(".j-search-con.single-box").on("click",
            ".j-select-year li ",
            function (e) {
                $(this).addClass('active').siblings().removeClass();
                var parents = $(this).parents('.j-search-con');
                var schoolSelEle = parents.find('.schoolSel');
                var txt = $(this).text();
                schoolSelEle.val(txt);
                parents.find('.j-arrow').removeClass('j-arrow-slide');
                parents.find('.j-select-year').removeClass('slideShow')
                stopBubble(e);
            })
        // 搜索
        $(".j-search-con").on('click', '.search input', function (e) {
            stopBubble(e);
        })
        $(".j-search-con").on('input', '.search input', function (e) {
            let val = $(this).val();
            var nextEle = $(this).parents('.j-search-con').find('ul')
            if (val != "") {

                nextEle.find('li').each(function (i, ele) {
                    let txt = $(ele).text();
                    if (txt.indexOf(val) >= 0) {
                        $(ele).show();
                    } else {
                        $(ele).hide();
                    }
                })
            } else {
                nextEle.find('li').show();
            }
            stopBubble(e)
        })

        function stopBubble(e) {
            if (e && e.stopPropagation)
                e.stopPropagation();
            else {
                window.event.cancelBubble = true;
            }

        }

        $(document).on("click", function (event) {
            var _con = $('.select-input');
            if (!_con.is(event.target) && _con.has(event.target).length === 0) {
                $(".j-search-con").find('.j-arrow').removeClass('j-arrow-slide');
                $(".j-search-con").find('.j-select-year').removeClass('slideShow')
            }
        })
    })
</script>
</html>