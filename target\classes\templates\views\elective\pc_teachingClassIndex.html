<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" li-content="IE=edge">
    <meta name="viewport" li-content="width=device-width, initial-scale=1.0">
    <title>微点名</title>
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/global.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/layui/css/layui.css'">
    <link rel="stylesheet" href="css/commonPC.css">
    <link rel="stylesheet" href="css/reset.css">
    <link rel="stylesheet" href="css/microdot-name.css">
    <link rel="stylesheet" href="/elective/rollcall/css/poup.css">
    <link rel="stylesheet" href="/elective/rollcall/css/name-calling-end.css">
    <script th:src="${_CPR_}+'/elective/layui/layui.js'"></script>


</head>

<body>
<div class="main">
    <div class="top">
        <div class="title">
            <div class="level-name">微点名</div>
        </div>
        <div class="btns-list">
            <div class="export view-statistics">
                <span>查看点名统计</span>
                <div class="type-box">
                    <ul>
                        <li>按学生查看</li>
                        <li>按班级查看</li>
                        <li>按教师查看</li>
                    </ul>
                </div>
            </div>
            <!--            <div class="export export1" style="margin-right: 20px;">查看学生点名统计</div>-->
            <div class="export export0">查看点名记录</div>
        </div>
    </div>
    <div class="con">
        <div class="filter-box">
            <div class="layui-form" action="">
                <div class="layui-form-item">
                    <label class="layui-form-label">课程名称</label>
                    <div class="layui-input-block w240">
                        <select name="typeCover1" lay-filter="typeCover1" id="typeCover1" lay-verify="required"
                                lay-search="">
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">日期选择</label>
                    <div class="layui-input-block ">
                        <div class="times w240">
                            <input type="text" name="time" readonly="" placeholder="请选择" class="layui-input"
                                   id="startTime" lay-key="1">
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">点名状态</label>
                    <div class="layui-input-block w240">
                        <select name="isRc" lay-filter="isRc" id="isRc" lay-verify="required"
                                lay-search="">
                            <option value=''>请选择</option>
                            <option value='false'>未点名</option>
                            <option value='true'>已点名</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-input-block w240">
                        <div class="btns">
                            <div class="searchs">查询</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="send-notifications">
                发送通知
            </div>
        </div>
        <div class="table" style="display:block;">
            <div class="course-list">
                <ul>
                </ul>
            </div>
            <div id="coursePage"></div>

        </div>
        <div class="no-data" style="display:none;">
            <img src="images/no-datas.png" alt="">
            <p>当前时间无课程</p>
        </div>
    </div>
</div>
<div id="sendNotifications" class="send-notifications-poup popups" style="width:1074px;">
    <div class="title">
        <div class="name">发送通知</div>
        <div class="close"></div>
    </div>
    <div class="popup-con">
        <div class="tips">请选择需要发送通知的课程和老师</div>
        <div class="table">
            <table class="layui-table" id="materialTable" lay-filter="materialTable"></table>
        </div>
    </div>
    <div class="bottom">
        <div class="cancle">取消</div>
        <div class="confirm">确定</div>
    </div>
</div>

</body>

<script th:src="${_CPR_}+'/elective/js/jquery-3.3.1.min.js'"></script>
<script>
    let pageIndex = 1;
    let limit = 10;
    layui.use(['table', 'jquery', 'laypage', 'laydate', 'form'], function () {

        var table = layui.table;
        var laypage = layui.laypage;
        var $ = layui.jquery;
        var layer = layui.layer;
        var laydate = layui.laydate;
        var form = layui.form;

        //日期选择
        laydate.render({
            elem: '#startTime',
            format: 'yyyy-MM-dd',
            type: 'date',
            value: new Date(),
        });
        $(".export0").click(function () {
            location.href = "/elective/rollcall/recordIndex?model=pc&js=1";
        })
        $(".main .top").on("click", ".btns-list .view-statistics", function () {
            $(this).toggleClass("clicked");
        })

        $(document).on("click", function (event) {
            var _con = $(".view-statistics");
            if (!_con.is(event.target) && _con.has(event.target).length === 0) {
                $(".main .top .btns-list .view-statistics").removeClass("clicked");
            }
        })


        $(".main .top .btns-list").on("click", " .view-statistics .type-box ul li", function (e) {
            $(this).addClass("cur").siblings().removeClass("cur");
            $(".main .top .btns-list .view-statistics").removeClass("clicked");
            let inx = $(".view-statistics .type-box ul li").index(this);
            if (inx == 1) {
                window.open("/elective/rollcall/staticIndex?model=pc&js=1", "_self");
            } else if (inx == 0) {
                window.open("/elective/rollcall/staticIndex/stu?model=pc&back=1", "_self");
            } else if (inx == 2) {
                window.open("/elective/rollcall/staticIndex/tea?model=pc&back=1", "_self");
            }
            e.stopPropagation();
        })
        $(".searchs").click(function () {
            pageIndex = 1;
            getList();
        })

        var height = $(window).height() - 200;
        $("#iframepage").attr("height", height);


        $(".tab").on("click", "ul li", function () {
            $(this).addClass("cur").siblings().removeClass("cur");
        })

        //返回

        $(".main .top .title .back").click(function () {
            $(".details-main").hide();
            $(".table-main").show();
        })

        getList();

        function getList() {
            var loading = layer.load(0, {
                shade: 0.1
            });
            var courseTime = $("#startTime").val();
            var courseCode = $('select[name="typeCover1"] option:selected').val();
            var teachingClassName = "";
            $.ajax({
                type: "POST",
                url: "/elective/rollcall/getTeachingClassList",
                data: {
                    courseTime: courseTime,
                    teachingClassName: teachingClassName,
                    courseCode: courseCode,
                    pageSize: limit,
                    curPage: pageIndex,
                    rollCallColumn: "true",
                    isRc:$('select[name="isRc"] option:selected').val(),
                },
                dataType: 'json',
                success: function (data) {
                    layer.close(loading);
                    var html = "";
                    if (data.status) {
                        if (data.count == 0) {
                            $(".table").hide();
                            $(".no-data").show();
                            return;
                        } else {
                            $(".table").show();
                            $(".no-data").hide();
                        }
                        laypage.render({
                            elem: 'coursePage',
                            groups: 5,
                            limit: limit,
                            limits: [10, 20, 30],
                            count: data.count, //数据总数，从服务端得到
                            curr: pageIndex,
                            layout: ['count', 'prev', 'page', 'next', 'limit', 'limits', 'skip'],
                            jump: function (obj, first) {
                                if (!first) {
                                    pageIndex = obj.curr;
                                    limit = obj.limit;
                                    getList();
                                }
                            }
                        });
                        for (var i = 0; i < data.list.length; i++) {
                            html += "<li>";
                            html += "<div class='li-con'>";
                            html += "<div class='img'>";
                            html += "<img src='images/image01.png' alt=''>";
                            html += "</div>";
                            html += "<div class='text'>";
                            html += "<h3>" + data.list[i].pkjgsj_kcmc + "</h3>";
                            html += "<p>";
                            html += "<span>第" + data.list[i].pkjgsj_zc + "周</span>";
                            html += "<span>周" + data.list[i].pkjgsj_xq + "</span>";
                            html += "<span>第" + data.list[i].pkjgsj_kj + "节</span>";
                            html += "</p>";
                            if (data.isRollCall[i]) {
                                // html += "<div class='state edit' onclick='toRollcall('/elective/rollcall/teacherRollcall?teachingClassCode="+data.list[i].pkjgsj_jxbbh+"&teachingClassName="+data.list[i].pkjgsj_jxbmc+"&courseName="+data.list[i].pkjgsj_kcmc+"&zc="+data.list[i].pkjgsj_zc+"&kj="+data.list[i].pkjgsj_kj+"&xq="+data.list[i].pkjgsj_xq+"&classDate="+data.courseTime+"')' >";
                                html += "<div class='state edit' onclick=\"toRollcall('" + data.list[i].pkjgsj_jxbbh + "','" + data.list[i].pkjgsj_jxbmc + "','" + data.list[i].pkjgsj_kcmc + "','" + data.list[i].pkjgsj_zc + "','" + data.list[i].pkjgsj_kj + "','" + data.list[i].pkjgsj_xq + "','" + data.courseTime + "')\" >";
                                html += "<span>已点名</span>";
                            } else {
                                html += "<div class='state roll-call' onclick=\"toRollcall('" + data.list[i].pkjgsj_jxbbh + "','" + data.list[i].pkjgsj_jxbmc + "','" + data.list[i].pkjgsj_kcmc + "','" + data.list[i].pkjgsj_zc + "','" + data.list[i].pkjgsj_kj + "','" + data.list[i].pkjgsj_xq + "','" + data.courseTime + "')\" >";
                                html += "<span>未点名</span>";
                            }
                            // html += "<div class='state roll-call'>";
                            // html+="<div class='state edit'>";
                            // html += "<span>点名</span>";
                            html += "</div>";
                            html += "</div>";
                            html += "</div>";
                            html += "</li>";

                            // html+="<a href=\"javascript:toRollcall('/elective/rollcall/teacherRollcall?teachingClassCode="+data.list[i].pkjgsj_jxbbh+"&teachingClassName="+data.list[i].pkjgsj_jxbmc+"&courseName="+data.list[i].pkjgsj_kcmc+"&zc="+data.list[i].pkjgsj_zc+"&kj="+data.list[i].pkjgsj_kj+"&xq="+data.list[i].pkjgsj_xq+"&classDate="+data.courseTime+"')\">";
                        }
                    }
                    $(".course-list ul").html(html);
                }
            });
        }


        $.ajax({
            type: "POST",
            url: "/elective/rollcall/getCourseList",
            dataType: 'json',
            success: function (data) {
                var html = "<option value=''>请选择</option>";
                if (data.status) {
                    for (var i = 0; i < data.list.length; i++) {
                        html += "<option value='" + data.list[i].bh + "'>" + data.list[i].mc + "</option>";
                    }
                }
                $("#typeCover1").html(html);
                layui.form.render("select");
            }
        });

//发送通知

        var layerIndex;

        $(".main .con .filter-box .send-notifications").click(function () {
            layerIndex = layer.open({
                type: 1,
                title: false,
                closeBtn: false,
                shadeClose: true,
                isOutAnim: true,
                content: $("#sendNotifications"),
                area: ["auto", "auto"],
                success: function () {
                    table.reload("materialTable", {
                        where: {
                            courseTime: $("#startTime").val(),
                            isRc:$('select[name="isRc"] option:selected').val()
                        }
                    });
                },
            });
        })

        $("#sendNotifications .title .close").click(function () {
            layer.close(layerIndex);
        })

        $("#sendNotifications .bottom div.cancle").click(function () {
            layer.close(layerIndex);
        })

        $("#sendNotifications .bottom div.confirm").click(function () {
            var checkStatus = table.checkStatus('materialTable');
            var data = checkStatus.data.filter(function (item) {
                return !item.disabled; // 过滤掉被禁用的行
            });
            var sendNoticeIndexId = [];
            if (data.length > 0) {
                for (let i = 0; i < data.length; i++) {
                    sendNoticeIndexId.push(data[i].rowInfo.formUserId);
                }
            }
            if (sendNoticeIndexId.length>0){
                var loading = layer.load(0, {
                    shade: [0.5, '#c0c0c0']
                });
                $.ajax({
                    type: "POST",
                    url: "/elective/rollcall/sendNoticeTeachingClassList",
                    data: {sendNoticeIndexId:sendNoticeIndexId.join(",")},
                    dataType: 'json',
                    async:false,
                    success: function (data) {
                        if (!data.status){
                            layer.msg(data.msg);
                        }else {
                            layer.msg("发送成功");
                        }
                        layer.close(loading);
                    }
                })
            }
            layer.close(layerIndex);
        })
        var zcArr = ["周一"];

        var table1 = table.render({
            elem: "#materialTable",
            id: 'materialTable',
            height: 265,
            url: "/elective/rollcall/getNoticeTeachingClassList",
            where: {
                courseTime: $("#startTime").val(),
                isRc:$('select[name="isRc"] option:selected').val()
            },
            cols: [
                [{
                    type: 'checkbox',
                    width: 60,
                }, {
                    field: "pkjgsj_kcmc",
                    align: "center",
                    title: "课程名称",
                    minWidth: 120
                },
                    {
                        field: "pkjgsj_jsxm",
                        align: "center",
                        title: "授课教师",
                        minWidth: 120
                    },
                    {
                        field: "pkjgsj_jxbmc",
                        align: "center",
                        title: "教学班名称",
                        minWidth: 120,
                    },
                    {
                        field: "classTime",
                        align: "center",
                        title: "上课时间",
                        minWidth: 120,
                        templet: function (d) {
                            return "第" + d.pkjgsj_zc + "周 周" + d.pkjgsj_xq + " 第" + d.pkjgsj_kj + "节";
                        }
                    },
                ]
            ],
            done: function (res) {
            }
        })

        //发通知end

    });

    function toRollcall(jxbbh, jxbmc, kcmc, zc, kj, xq, classDate) {
        window.location.href = "/elective/rollcall/teacherRollcall?teachingClassCode=" + jxbbh + "&teachingClassName=" + jxbmc + "&courseName=" + kcmc + "&zc=" + zc + "&kj=" + kj + "&xq=" + xq + "&classDate=" + classDate + "&model=pc";
    }

</script>

</html>