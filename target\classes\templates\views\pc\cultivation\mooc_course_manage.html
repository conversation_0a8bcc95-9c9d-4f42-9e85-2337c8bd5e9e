<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <title>网络课程查询</title>
    <link rel="stylesheet" th:href="@{../css/cultivation/jquery.searchableSelect.css}"/>
    <link rel="stylesheet" th:href="@{../css/cultivation/newStyle.css}"/>
    <link rel="stylesheet" th:href="@{../plugin/layui/css/layui.css}"/>
    <style>
        html, body {
            height: 100%;
        }

        .layui-laypage .layui-laypage-curr .layui-laypage-em {
            background-color: #4C88FF !important;
        }
    </style>
</head>
<body>
<div class="jwMain">
    <div class="opt_home_top"><label class="opt_form_name">网络课程管理</label></div>
    <div class="opt_index">
        <div class="opt_search">
            <div class="opt_search_right clearAfter">
                <div class="opt_search_temp fl">
                    <div class="opt_search_name">
                        <div class="opt_search_table">
                            <div class="opt_search_cell"><span>网络课程id</span></div>
                        </div>
                    </div>
                    <div class="opt_search_per"><input type="text" placeholder="网络课程id" name="moocId"
                                                       class="opt_txt_input"></div>
                </div>
                <div class="opt_search_temp fl">
                    <div class="opt_search_name">
                        <div class="opt_search_table">
                            <div class="opt_search_cell"><span>网络课程名称</span></div>
                        </div>
                    </div>
                    <div class="opt_search_per"><input type="text" placeholder="网络课程名称" name="moocName"
                                                       class="opt_txt_input"></div>
                </div>
                <div class="opt_search_temp fl">
                    <div class="opt_search_name">
                        <div class="opt_search_table">
                            <div class="opt_search_cell"><span>创建教师</span></div>
                        </div>
                    </div>
                    <div class="opt_search_per"><input type="text" placeholder="创建教师" name="moocTeacher"
                                                       class="opt_txt_input"></div>
                </div>
                <div class="opt_btns">
                    <div class="opt_clear fr" onclick="resetQueryCondition('zxjhGridIdGrid')">清空筛选</div>
                    <div class="opt_search_btn fr search">筛选</div>
                    <div class="opt_search_btn fr" style="margin-right: 157px;width: 84px;margin-top: -34px;"
                         onclick="syncMooc();">同步网络课程
                    </div>
                </div>
            </div>
        </div>
        <div class="opt_data">
            <div class="opt_data_cont">
                <table lay-filter="courseTable" class="layui-table" id="courseTable"></table>
            </div>
        </div>
    </div>
</div>
<script th:src="@{../js/jquery-3.3.1.min.js}"></script>
<script th:src="@{../js/cultivation/jquery.searchableSelect.js}"></script>
<script th:src="@{../js/jquery.nicescroll.min.js}"></script>
<script th:src="@{../plugin/layui/layui.js}"></script>
<script type="text/html" id="toolBar">
    <a class="layui-btn layui-btn-xs" lay-event="manage">管理</a>
</script>

<script>
    $(".search").click(function () {
        var moocTeacher = $("input[name='moocTeacher']").val();
        var moocName = $("input[name='moocName']").val();
        var moocId = $("input[name='moocId']").val();
        var field = {moocTeacher: moocTeacher, moocName: moocName, moocId: moocId};
        insTb.reload({where: field, page: {curr: 1}});
    })

    $(".opt_clear").click(function () {
        location.reload();
    })

    var table = "", insTb = "";
    layui.use('table', function () {
        table = layui.table;
        insTb = table.render({
            elem: '#courseTable',
            url: '../processData/getMoocCourseData',
            page: true,
            cellMinWidth: 100,
            cols: [
                [
                    {field: 'moocId', title: '网络课程编号'},
                    {field: 'moocName', title: '网络课程名称'},
                    {field: 'kcId', title: '课程代码'},
                    {field: 'moocTeacher', title: '网络课程创建教师'},
                    {field: 'moocCreateDate', title: '创建时间'}
                ]
            ],
            parseData: function (res) {
                if (res.code === 0) {
                    return {
                        "code": 0, //解析接口状态
                        "msg": res.msg, //解析提示文本
                        "count": res.list.total, //解析数据长度
                        "data": res.list.records //解析数据列表
                    }
                } else {
                    return {
                        "code": 1, //解析接口状态
                        "msg": "服务异常", //解析提示文本
                    }
                }
            },
            done: function (res, curr, count) {
                $(".opt_data_num #totalSpan").text("共 " + count + " 条");
            }
        });

        table.on('tool(courseTable)', function (obj) {
            var data = obj.data;
            var moocId = data.moocId;
            if (obj.event === 'manage') {
                var url = "../process/moocCourseManage?moocId=" + moocId;
                window.open(url);
            }
        })
    });

    function syncMooc() {
        alert("已执行");
        $.post("../processData/doMoocCourse", function (data) {
        }, "json");
        $.post("../processData/clearDelCourse", function (data) {
        }, "json");
    }
</script>
</body>
</html>