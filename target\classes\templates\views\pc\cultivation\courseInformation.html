<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>开课信息</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/plugin/layui/css/layui.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/courseInformation.css'}">
    <style>
        .layui-form-select dl dd.layui-this {
            background-color: #4c88ff !important;
        }

        .layui-form-select dl {
            max-height: 200px !important;
        }
    </style>
    <script th:src="${_CPR_+_VR_+'/plugin/layui/layui.js'}"></script>
</head>
<body>
<div class="main">
    <div id="courseInformation">
        <div class="popup-con">
            <div class="content">
                <div class="sel-item">
                    <div class="sel-title"><span></span>开课学期</div>
                    <div class="sel" style="margin-right:0;">
                        <div class="select-input">
                            <div class="name ckd" data-name="请选择">请选择</div>
                            <i></i>
                            <div class="select-dropdown">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul class="dropdown-lists dropdown-lists-single">
                                    <li>2021-01</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="sel-item">
                    <div class="sel-title"><span></span>年级</div>
                    <div class="sel" style="margin-right:0;">
                        <div class="select-input">
                            <div class="name ckd" data-name="请选择" th:text="${info?.kkxxb_njnj}?${info?.kkxxb_njnj}:'请选择'">请选择</div>
                            <i></i>
                            <div class="select-dropdown">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul class="dropdown-lists dropdown-lists-single">
                                    <li>一年级</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="sel-item">
                    <div class="sel-title"><span></span>培养层次</div>
                    <div class="sel" style="margin-right:0;">
                        <div class="select-input">
                            <div class="name ckd" data-name="请选择" th:text="${info?.kkxxb_pycc}?${info?.kkxxb_pycc}:'请选择'">请选择</div>
                            <i></i>
                            <div class="select-dropdown">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul class="dropdown-lists dropdown-lists-single">
                                    <li>培养层次1</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="sel-item">
                    <div class="sel-title"><span></span>专业</div>
                    <div class="sel" style="margin-right:0;">
                        <div class="select-input">
                            <div class="name ckd" data-name="请选择" th:text="${info?.kkxxb_zymc}?${info?.kkxxb_zymc}:'请选择'">请选择</div>
                            <i></i>
                            <div class="select-dropdown">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul class="dropdown-lists dropdown-lists-single">
                                    <li>专业1</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="sel-item" th:if="${queryId == null}">
                    <div class="sel-title"><span></span>教学班编号</div>
                    <div class="sel" style="margin-right:0;">
                        <div class="select-input">
                            <input type="text" placeholder="请输入教学班编号（非必填，不填则复制后生成随机编号）">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bottom">
            <button type="reset" class="layui-btn exam-cancle">
                取消
            </button>
            <button class="layui-btn exam-sure">确定</button>
        </div>
    </div>
</div>

</body>
<script th:src="${_CPR_+_VR_+'/js/jquery-1.11.3.min.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/cultivation/common.js'}"></script>
<script th:inline="javascript">
    document.domain = "chaoxing.com";
    const _VR_ = [[${_VR_}]];
    const formUserId = [[${formUserId}]];
    let queryId = [[${queryId}]];
    const formId = [[${formId}]];
    const fid = [[${fid}]];
    const uid = [[${uid}]];
    const formSelects = "";
    layui.use(['form', 'layer'], function () {
        getSemesterData();
        getGradeData();
        getMajorData();
        getLevelData();
    })
    $(".exam-cancle").click(function () {
        window.parent.postMessage(JSON.stringify({action: 1}), "*");
    })

    $(".exam-sure").click(function () {
        const layer = layui.layer;
        const inputs = [$(".ckd").eq(0).text(), $(".ckd").eq(1).text(), $(".ckd").eq(2).text(), $(".ckd").eq(3).text()];
        const jxbbh = $(".sel-item").eq(4).find("input").val();
        for (let i = 0; i < inputs.length; i++) {
            if (!inputs[i] || inputs[i] === "请选择") {
                const errorMsg = ["开课学期不能为空", "年级不能为空", "培养层次不能为空", "专业不能为空"];
                layer.msg(errorMsg[i], { icon: 2, time: 2000 });
                return false;
            }
        }
        let loading = layer.load(1);
        $.post(_VR_ + "/api/pygc/courseInformationCopy", {
            formUserId: formUserId,
            formId: formId,
            queryId: queryId,
            fid: fid,
            uid: uid,
            semester: inputs[0],
            grade: inputs[1],
            level: inputs[2],
            major: inputs[3],
            jxbbh: jxbbh
        }, function (result) {
            if (result.success) {
                window.parent.postMessage(JSON.stringify({action: 1}), "*");
            } else {
                layer.msg(result.message, {icon: 2, time: 2000});
            }
            layer.close(loading);
        }, "json");
    });

    //学年学期数据
    function getSemesterData() {
        $.post(_VR_ + "/api/pygc/getSemesterData", function (result) {
            if (result.status) {
                let html = "";
                for (let i = 0; i < result.list.length; i++) {
                    const semester = result.list[i];
                    html += "<li>" + semester.xnxq_xnxqh + "</li>";
                }
                $(".sel-item").eq(0).find(".dropdown-lists-single").html(html);
            }
        }, "json");
    }

    //年级数据
    function getGradeData() {
        $.post(_VR_ + "/api/pygc/getGradeData", function (result) {
            if (result.status) {
                result.list.sort((a, b) => b.nj_njmc.localeCompare(a.nj_njmc));
                let html = "";
                for (let i = 0; i < result.list.length; i++) {
                    const grade = result.list[i];
                    html += "<li>" + grade.nj_njmc + "</li>";
                }
                $(".sel-item").eq(1).find(".dropdown-lists-single").html(html);
            }
        }, "json");
    }

    //培养过程数据
    function getLevelData() {
        $.post(_VR_ + "/api/pygc/getLevelData", function (result) {
            if (result.status) {
                let html = "";
                for (let i = 0; i < result.list.length; i++) {
                    const level = result.list[i];
                    html += "<li>" + level.education_level + "</li>";
                }
                $(".sel-item").eq(2).find(".dropdown-lists-single").html(html);
            }
        }, "json");
    }

    //专业数据
    function getMajorData() {
        $.post(_VR_ + "/api/pygc/getMajorData", function (result) {
            if (result.status) {
                let html = "";
                for (let i = 0; i < result.list.length; i++) {
                    const major = result.list[i];
                    html += "<li>" + major.zysj_zymc + "</li>";
                }
                $(".sel-item").eq(3).find(".dropdown-lists-single").html(html);
            }
        }, "json");
    }
</script>
</html>