<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta http-equiv='X-UA-Compatible' content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <title>毕业条件达成度</title>
    <link rel="stylesheet" th:href="@{~/css/graduate/global.css}">
    <link rel="stylesheet" th:href="@{~/plugin/layui/css/layui.css}">
    <link rel="stylesheet" th:href="@{~/css/graduate/reset.css}">
    <link rel="stylesheet" th:href="@{~/css/graduate/dialog.css}">
    <script th:src="@{~/layui/layui.js}"></script>
    <script th:src="@{~/js/jquery-3.6.0.min.js}" type="text/javascript" charset="utf-8"></script>
    <style>
        #graduateProgress {
            width: 1000px;
            position: fixed;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);

        }

        #graduateProgress .student-info-label {
            font-weight: bold;
        }

        #graduateProgress .student-info-value {
            color: #4E5969;
            margin: 0px 10px 0px 3px;
        }

        #graduateProgress progress {
            width: 150px;
            height: 5px;
            color: #4d88ff;;
            border-radius: 14px;

        }

        /* 总长度背景色 */
        #graduateProgress progress::-webkit-progress-bar {
            background-color: #e5e6eb;
            border-radius: 14px;
        }

        /*已完成进度背景色 */
        #graduateProgress progress::-webkit-progress-value {
            background-color: #4d88ff;
            border-radius: 14px;
        }

        #graduateProgress .graduate-condition {
            color: #4080ff;
            font-weight: 600;

        }

        .conform-yes {
            color: #fff;
            padding: 5px 10px;
            background-color: #3eb35a;
            border-radius: 10px;
        }

        .conform-no {
            color: #fff;
            padding: 5px 10px;
            background-color: #ffb026;
            border-radius: 10px;
        }
    </style>
</head>
<body>
<div class="masker"></div>

<div class="dialog" id="graduateProgress">
    <div class="dialog-title">毕业条件达成度</div>
    <div class="dialog-con">
        <div class="item">
					<span id="studentInfo">
						<span class="student-info-label">年级:</span>
						<span class="student-info-value" th:text="${graduateQualifications.byzgsc_nj}"></span>
						<span class="student-info-label">系部:</span>
						<span class="student-info-value" th:text="${graduateQualifications.byzgsc_xb}"></span>
						<span class="student-info-label">专业:</span>
						<span class="student-info-value" th:text="${graduateQualifications.byzgsc_zy}"></span>
						<span class="student-info-label">班级:</span>
						<span class="student-info-value" th:text="${graduateQualifications.byzgsc_bj}"></span>
						<span class="student-info-label">姓名:</span>
						<span class="student-info-value" th:text="${graduateQualifications.byzgsc_xm}"></span>
						<span class="student-info-label">学号:</span>
						<span class="student-info-value"
                              th:text="${graduateQualifications.byzgsc_xh}"></span>
					</span>
        </div>
        <div class="item" style="justify-content:space-between">
            <div class="graduate-condition">
                毕业条件符合情况
            </div>
            <div style="display: flex;align-items: center;">
                <span class="student-info-label" style="padding-right: 10px;">完成进度:</span>
                <progress id="progress" max="100" value="0"></progress>
                <span class="student-info-value"
                      style="margin-left: 10px;"></span>
            </div>
        </div>
        <div style="height: 440px">
            <table class="layui-hide materialTable" id="materialTable" lay-filter="materialTable">
            </table>
        </div>
        <div class="dialog-btn">
            <button class="pu-cancel">取消</button>
            <button class="pu-sure"
                    id="invigilateSure">确定
            </button>
        </div>
        <div>
        </div>
    </div>
</div>
</body>
<script th:inline="javascript">
    var graduateQualifications = [[${graduateQualifications}]]
    layui.use(['jquery', 'table'], function () {
        var table = layui.table;

        $ = layui.jquery;
        var exportData = []

        //符合条件 数量
        var sum = 0;
        for (let i = 0; i < graduateQualifications.byzgsc_bytjfhqk.length; i++) {
            var conformSwitch = "";
            if (graduateQualifications.byzgsc_bytjfhqk[i].byzgsc_sffh == "是") {
                sum++;
                conformSwitch = "符合条件";
            } else {
                conformSwitch = "不符合";
            }
            var json = {
                graduationRequirement: graduateQualifications.byzgsc_bytjfhqk[i].byzgsc_bytj2,
                conformSwitch: conformSwitch
            }
            exportData.push(json)
        }

        var percent = (sum*100/graduateQualifications.byzgsc_bytjfhqk.length).toFixed(2);
        $("#progress").val(percent)
        $("#progress").next().html(percent+"%")


        table.render({
            elem: '#materialTable',
            data: exportData,
            height: '440',
            cols: [
                [{
                    field: "graduationRequirement",
                    title: "毕业条件",
                    align: "center"
                },
                    {
                        field: "conformSwitch",
                        title: "是否符合",
                        align: "center",
                        templet: function (d) {
                            return `<span class="${d.conformSwitch === '符合条件' ? 'conform-yes' : 'conform-no'}">${d.conformSwitch}</span>`;
                        }
                    }
                ]
            ],

            done: function (res, curr, count) {
            }
        });
    })
</script>
</html>