<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width,initial-scale=1.0, maximum-scale=1.0, user-scalable=0, viewport-fit=cover">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta content="telephone=no" name="format-detection"/>
    <title>校历</title>
    <script th:src="@{/js/responsive-1.0.js}"></script>
    <link rel="stylesheet" th:href="@{/css/global.css}">
    <link rel="stylesheet" th:href="@{/css/calendar/header.css}">
    <link rel="stylesheet" th:href="@{/css/calendar/index.css}">
</head>

<body>
<div class="header">
    <div class="head-signal"></div>
    <div class="head-con">
        <a href="javascript:void(0)" class="head-return">
            <img th:src="@{/images/calendar/back-icon.png}" alt="">
            <span>2023年</span>
        </a>
        <span class="head-title">校历</span>
        <span class="head-right" style="display:none;">
                <img th:src="@{/images/calendar/add-icons.png}" alt="">
        </span>
    </div>
</div>
<div class="main">
    <div class="change-box">
        <div class="current-semester">
            <div class="name">当前学期：</div>
            <div class="time">2022-2023-2</div>
        </div>
        <div class="types">
            <span>月份</span>
            <span class="cur">学期</span>
        </div>

    </div>
    <div class="calendar-box">
        <div class="cal-top">
            <span>周序</span>
            <span>一</span>
            <span>二</span>
            <span>三</span>
            <span>四</span>
            <span>五</span>
            <span>六</span>
            <span>日</span>
        </div>
        <div class="cla-con">
            <div class="lable">
                <div class="row">
                    <div class="week">01</div>
                    <div class="unit disable">
                        <span>31</span>
                    </div>
                    <div class="unit m-start">
                        <span>五月</span>
                        <p>劳动节</p>
                    </div>
                    <div class="unit current">
                        <span>2</span>
                    </div>
                    <div class="unit events">
                        <span>3</span>
                    </div>
                    <div class="unit">
                        <span>4</span>
                    </div>
                    <div class="unit">
                        <span>5</span>
                    </div>
                    <div class="unit">
                        <span>6</span>
                    </div>
                </div>
                <div class="row">
                    <div class="week">01</div>
                    <div class="unit disable">
                        <span>-</span>
                    </div>
                    <div class="unit disable">
                        <span>-</span>
                    </div>
                    <div class="unit disable">
                        <span>-</span>
                    </div>
                    <div class="unit disable">
                        <span>-</span>
                    </div>
                    <div class="unit disable">
                        <span>-</span>
                    </div>
                    <div class="unit disable">
                        <span>-</span>
                    </div>
                    <div class="unit disable">
                        <span>-</span>
                    </div>
                </div>

            </div>

        </div>
    </div>

</div>

<div class="school-bottom">
    <div class="handle">
        <div class="btn generate disable">生成校历</div>
        <div class="btn export disable">导出校历</div>
    </div>

</div>
<script th:src="@{/js/jquery-3.3.1.min.js}"></script>
<script th:src="@{/js/CXJSBridge.js}"></script>
<script th:src="@{/js/mobileVersion.js}"></script>
<script>
    $(function () {
        // 隐藏学习通头
        jsBridge.postNotification('CLIENT_TOOLBAR_TYPE', {
            'toolbarType': 0
        });

        //日期切换
        $(".main .change-box .types span").click(function () {
            $(this).addClass("cur").siblings().removeClass("cur");
        })
    })
</script>
</body>
</html>