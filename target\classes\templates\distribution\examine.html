<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>审核征订计划</title>
    <link rel="stylesheet" href="/distribution/css/global.css">
    <link rel="stylesheet" href="/distribution/layui/css/layui.css">
    <link rel="stylesheet" href="/distribution/css/reset.css">
    <link rel="stylesheet" href="/distribution/css/reviewSub.css">
    <script src="/distribution/layui/layui.js"></script>
</head>

<body>
<div class="j-material-wrap">
    <div class="j-material">
        <div class="j-title">
            <h4>审核征订计划</h4>
            <div class="j-btns">
                <!--                <button class="btn-cancel">保存修改</button>-->
                <button class="btn-complate" onclick="pass()" id="pass">审核通过</button>
            </div>
        </div>
        <div class="j-review-con ">
            <div class="j-top-inform clearfix">
                <div class="semester">
                    <div class="name">学年学期：</div>
                    <div class="txt" th:text="${order.xnxq}"></div>
                </div>
                <div class="semester">
                    <div class="name">征订计划批次：</div>
                    <div class="txt" th:text="${order.orderNum}"></div>
                </div>

                <div class="total">
                    <div class="name">批次总计：</div>
                    <div class="principal sumMoney" th:text="${order.money}">3600元</div>
                </div>
                <form action="/api/material/pass" method="post" id="order">
                    <input type="hidden" name="type" value="order">
                    <input type="hidden" name="fid" th:value="${fid}">
                    <input type="hidden" name="uid" th:value="${uid}">
                    <input type="hidden" name="formId" th:value="${formId}">
                    <input type="hidden" name="xnxq" th:value="${order.xnxq}">
                    <input type="hidden" name="orderNum" th:value="${order.orderNum}">
                    <input type="hidden" name="college" th:value="${order.college}">
                    <input type="hidden" name="zy" th:value="${order.zy}">
                    <input type="hidden" name="sum" th:value="${#arrays.length(jc)}">
                    <input type="hidden" class="sumMoney" name="money" th:value="${order.money}">

                </form>
            </div>
            <div class="j-search">
                <div class="j-search-item">
                    <h5>批量设置供应商</h5>
                    <div class="j-search-con">
                        <input type="text" placeholder="选择供应商" readonly class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <ul class="main gys">

                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="j-table">
                <div class="j-table-thead">
                    <div class="cell checked" id="allChecked">
                        <span>全选</span>
                    </div>
                    <div class="cell">教材编号</div>
                    <div class="cell">提交人</div>
                    <div class="cell">院系</div>
                    <div class="cell">教材类型</div>
                    <div class="cell">教材名称</div>
                    <div class="cell">ISBN</div>
                    <div class="cell">出版社</div>
                    <div class="cell">教材作者</div>
                    <div class="cell">出版年月</div>
                    <div class="cell">单价</div>
                </div>

                <div class="j-table-tr">
                    <form action="/api/material/pass" method="post" class="jc" th:each="data,dataStat:${jc}"
                          th:styleappend="${dataStat.index<6?'':'display:none'}" onsubmit="return false;">
                        <div class="cell">
                            <div class="lable">
                                <span></span>
                            </div>

                            <div class="c-main">
                                <div class="c-inform">
                                    <input type="hidden" name="type" value="summary">
                                    <input type="hidden" name="fid" th:value="${fid}">
                                    <input type="hidden" name="uid" th:value="${uid}">
                                    <input type="hidden" name="formId" th:value="${formId}">
                                    <input type="hidden" name="xnxq" th:value="${order.xnxq}">
                                    <input type="hidden" name="orderNum" th:value="${order.orderNum}">
                                    <input type="hidden" name="jcbh" th:value="${data.jcbh}">
                                    <input type="hidden" name="creator" th:value="${data.creator}">
                                    <input type="hidden" name="faculty" th:value="${data.faculty}">
                                    <input type="hidden" name="bookType" th:value="${data.bookType}">
                                    <input type="hidden" name="jcmc" th:value="${data.jcmc}">
                                    <input type="hidden" name="isbn" th:value="${data.isbn}">
                                    <input type="hidden" name="cbs" th:value="${data.cbs}">
                                    <input type="hidden" name="jczz" th:value="${data.jczz}">
                                    <input type="hidden" name="cbny" th:value="${data.cbny}">
                                    <input type="hidden" name="dj" th:value="${data.dj}">
                                    <input type="hidden" name="zk" th:value="${data.zk==null?1:data.zk}">
                                    <input type="hidden" name="money" th:value="${data.money}">

                                    <div class="item" th:title="${data.jcbh}" th:text="${data.jcbh}"></div>
                                    <div class="item" th:title="${data.creator}" th:text="${data.creator}"></div>
                                    <div class="item" th:title="${data.faculty}" th:text="${data.faculty}"></div>
                                    <div class="item" th:title="${data.bookType}" th:text="${data.bookType}"></div>
                                    <div class="item" th:title="${data.jcmc}" th:text="${data.jcmc}"></div>
                                    <div class="item" th:title="${data.isbn}" th:text="${data.isbn}"></div>
                                    <div class="item" th:title="${data.cbs}" th:text="${data.cbs}"></div>
                                    <div class="item" th:title="${data.jczz}" th:text="${data.jczz}"></div>
                                    <div class="item" th:title="${data.cbny}" th:text="${data.cbny}"></div>
                                    <div class="item" name="dj" th:title="${data.dj}" th:text="${data.dj}"></div>
                                </div>
                                <div class="c-opt">
                                    <div class="number">
                                        <div class="name">待征订数量</div>
                                        <div class="input">
                                            <input type="number" min="0" class="layui-input sum"
                                                   name="sum" lay-verify="required" lay-verType="tips"
                                                   autocomplete="off" th:value="${data.sum}">
                                        </div>
                                    </div>
                                    <div class="j-search-item">
                                        <h5>供应商</h5>
                                        <div class="j-search-con">
                                            <input name="gys" type="text" placeholder="请选择" id="schoolYear" readonly
                                                   class="schoolSel gys" value="">
                                            <span class="j-arrow"></span>
                                            <div class="j-select-year">
                                                <ul class="gys">
                                                </ul>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="discount">供应商折扣<p name="zk" th:value="${data.zk}"
                                                                  th:text="${data.zk==null?'不打折':(#strings.substring(data.zk,#strings.indexOf(data.zk,'.')+1,#strings.length(data.zk))+'折')}">
                                        9折</p></div>

                                    <div class="tatol">总计<span name="money" th:text="${data.money}"></span></div>
                                </div>
                            </div>

                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div id="coursePage"></div>
    </div>

</div>
</body>
<script src="/distribution/js/jquery-3.3.1.min.js"></script>
<script src="/distribution/js/common.js"></script>
<script src="/distribution/js/jquery-form.js"></script>
<script src="/js/alert.js"></script>
<script th:inline="javascript">
    var fid = [[${fid}]];
    var uid = [[${uid}]];
    var orderNum = [[${order.orderNum}]];
    var xnxq = [[${order.xnxq}]];

    $(function () {
        getAllGys();
        doSumMoney();
    })

    function getAllGys() {
        $.ajax({
            type: 'get',
            dataType: 'json',
            url: '/api/material/gysxx',
            async: false,
            data: {
                fid: fid
            },
            success: function (res) {
                if (res.status) {
                    let data = res.data;
                    let html = "";
                    data.forEach(function (gys) {
                        html += '<li data-value="' + gys.gysbh + '">' + gys.gysmc + '</li>'
                    })
                    if (html != "") {
                        $(".gys").html(html);
                    }
                } else {
                    $(".gys").html("<li style=\"pointer-events: none;\">无供应商</li>");
                }
            }
        });
    }

    $('input[name=sum]').on('input', function () {
        var sum = $(this).val();
        var dj = $(this).parents(".c-main").children(".c-inform").find("div[name=dj]").text();
        var zk = $(this).parents(".c-opt").children(".discount").find("p[name=zk]").attr("value");
        if (zk==0||zk==undefined||zk==null){
            $(this).parents(".c-opt").children(".tatol").find("span[name=money]").text((sum * dj).toFixed(2));
            $(this).parents(".c-main").children(".c-inform").find("input[name=money]").val((sum * dj).toFixed(2));
        }else {
            $(this).parents(".c-opt").children(".tatol").find("span[name=money]").text((sum * dj * zk).toFixed(2));
            $(this).parents(".c-main").children(".c-inform").find("input[name=money]").val((sum * dj * zk).toFixed(2));
        }

        doSumMoney();
    })

    function doSumMoney() {
        var money = $("span[name=money]");
        let sumMoney = 0;
        for (let i = 0; i < money.length; i++) {
            sumMoney += parseFloat(money[i].innerText)
        }
        $(".sumMoney").html(sumMoney.toFixed(2))
        $(".sumMoney").val(sumMoney.toFixed(2))
    }

    function pass() {
        var flag = $("#pass").hasClass("gray");
        if (flag){
            myAlert("请勿重复提交")
            return false;
        }
        var opt = $(".jc").find(".c-opt");
        for (let i = 0; i < opt.length; i++) {
            var sum = $(opt).find(".sum").val();
            var gys = $(opt).find(".gys").val();
            if (sum == 0 || sum == "" || sum == undefined || gys == "" || gys == undefined) {
                myAlert("存在未设置的教材")
                return false;
            }
        }
        myAlert("正在保存，请稍等")
        let error = 0;
        var children = $(".j-table-tr").children("form");
        for (let i = 0; i < children.length; i++) {
            $(children[i]).ajaxSubmit(function (res) {
                if (!res.status) {
                    error++;
                    if (res.exist){
                        return false;
                    }
                }
            })
        }
        setTimeout(function () {
            if (error > 0) {
                myAlert("保存失败，请稍后再试");
            } else {
                $("#order").ajaxSubmit(function (res){
                   if (res.status){
                       myAlert("保存成功")
                       $("#pass").removeAttr("class").addClass("gray")
                   }
                });
                genProvide()
            }
        }, 2000)
    }
    function genProvide(){
        $.get("/api/material/genProvide2",{fid:fid,uid:uid,xnxq:xnxq,orderNum:orderNum})
    }

</script>
<script th:inline="javascript">
    var jc = [[${jc}]];


    let pageIndex = 1;
    layui.use(['laypage'], function () {
        var laypage = layui.laypage;
        //分页
        laypage.render({
            elem: 'coursePage'
            , curr: pageIndex//让起始页,也就是点击的页码
            , groups: 5// 默认的连续出现的页码数
            , limit: 6//每页展示条数
            , count: jc.length//数据总数
            , layout: ['count', 'prev', 'page', 'next', 'skip']
            , jump: function (obj, first) {
                if (first || obj.curr == 1) {
                    $(".j-table-tr").find('form:lt(' + (obj.limit) + ')').show();
                    $(".j-table-tr").find('form:gt(' + (obj.limit - 1) + ')').hide();
                } else {
                    $(".j-table-tr").find('form:lt(' + (obj.curr - 1) * obj.limit + ')').hide();
                    $(".j-table-tr").find('form:gt(' + ((obj.curr - 1) * obj.limit - 1) + '):lt(' + (obj.curr) * obj.limit + ')').show();
                    $(".j-table-tr").find('form:gt(' + (obj.curr * obj.limit - 1) + ')').hide();
                    pageIndex=obj.curr;
                }
            }
        });
    });

    $(".lable").click(function () {
        $(this).children("span").toggleClass("cur")
    })

    //全选
    $(".j-material .j-table").on("click", ".j-table-thead .cell span", function () {
        $(this).toggleClass("cur");
        if ($(this).hasClass("cur")) {
            $(".j-material .j-table .j-table-tr .cell .lable span").addClass("cur");
        } else {
            $(".j-material .j-table .j-table-tr .cell .lable span").removeClass("cur");
        }
    })
</script>
</html>