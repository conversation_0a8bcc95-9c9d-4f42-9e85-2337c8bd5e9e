<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1, user-scalable=no"/>
    <meta name="format-detection" content="telephone=no,email=no,adress=no">
    <meta http-equiv="pragma" content="no-cache"/>
    <meta http-equiv="cache-control" content="no-cache"/>
    <meta http-equiv="expires" content="0"/>
    <title>老师无课查询</title>
    <link rel="stylesheet" th:href="@{../../css/basic/common.css}">
    <link rel="stylesheet" th:href="@{../../css/basic/schedule.css}"/>
    <link rel="stylesheet" th:href="@{../../css/basic/iosSelect.css}">
    <link rel="stylesheet" th:href="@{../../css/basic/newStyle.css}"/>
</head>

<body>
<div class="tophead">
    <div class="head bottomLine">
        <div class="leftHead">
            <div class="back"></div>
            <div class="select-week">
                <span>第<i>1</i>周</span>
                <em></em>
            </div>
        </div>
        <div class="centerHead">
            <div class="selectBox">
                <div class="selectWeek"><span class="week" week='1'>老师无课查询</span></div>
            </div>
        </div>
        <div class="rightHead">
            <div class="search"></div>
        </div>
    </div>
    <div class="tables">
        <table id="scheduleHead">
            <thead>
                <tr></tr>
            </thead>
        </table>
    </div>

</div>


<div class="table">
    <table id="scheduleTable">
        <tbody></tbody>
    </table>
</div>

<div class="scroll-dialog">
    <ul>
    </ul>
</div>

<div class="week-dialog">
    <div class="w-con">
        <div class="w-head">
            <h3>点击查看该周周次</h3>
        </div>
        <div class="w-box">
            <ul>
            </ul>
        </div>

    </div>
</div>
<script>let _VR_ = "[[${_VR_}]]";</script>
<script th:src="@{../../js/jquery-3.3.1.min.js}"></script>
<script th:src="@{../../js/basic/mobile_teacher_idle.js(v=${new java.util.Date().getTime()})}"></script>
<script th:src="@{../../js/iosSelect.js}"></script>
<script th:src="@{../../js/CXJSBridge.js}"></script>
<script th:src="@{../../js/app.utils.js}"></script>
<script th:src="@{../../js/tools.js}"></script>
<script th:src="@{../../js/my.util.js}"></script>
</body>
</html>
