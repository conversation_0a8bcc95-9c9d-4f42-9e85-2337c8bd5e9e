<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学生测评</title>
    <link rel="stylesheet" th:href="@{/plugin/layui/css/layui.css}">
    <script th:src="@{/plugin/layui/layui.js}"></script>
</head>
<body>
<form class="layui-form" style="width: 500px;height: 300px;margin: 40px 40px;">
    <div class="layui-form-item">
        <label class="layui-form-label">学年名称</label>
        <div class="layui-input-block">
            <input type="text" name="yearName" lay-verify="required" autocomplete="off"
                   class="layui-input">
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-input-block">
            <button class="layui-btn" lay-submit lay-filter="btn-complate">提交</button>
            <button type="reset" class="layui-btn layui-btn-primary">取消</button>
        </div>
    </div>
</form>
</body>
<script th:src="@{/js/jquery-1.11.3.min.js}"></script>
<script th:inline="javascript">
    let queryId = [[${form.queryId}]];
    let uid = [[${form.uid}]];
    let fid = [[${form.fid}]];
    layui.use(["form", "layer"], function () {
        const form = layui.form;
        const layer = layui.layer;
        form.on('submit(btn-complate)', function (data) {
            data.field.queryId = queryId;
            data.field.fid = fid;
            data.field.uid = uid;
            $.post("/api/form/basic/topBtn/addStudentEvaluation", data.field, function (result) {
                if (result.success) {
                    layer.msg(result.message, {icon: 1, time: 2000});
                    setTimeout(function () {
                        window.parent.postMessage(JSON.stringify({action: 1}), "*");
                    }, 2000);
                } else {
                    layer.msg(result.message, {icon: 2, time: 2000});
                }
            }, "json");
            return false;
        })
    })

    $(".layui-btn-primary").click(function () {
        window.parent.postMessage(JSON.stringify({action: 1}), "*");
    })

</script>
</html>