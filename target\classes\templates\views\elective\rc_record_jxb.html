<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
        content="width=device-width, user-scalable=no,initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <title>[[${jxbmc}]]</title>
    <script src="js/responsive.js"></script>
    <link rel="stylesheet" href="css/global.css">
    <link rel="stylesheet" href="css/iosSelect.css">
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/sta.css?v=2">
</head>
<body>
    <div class="search-wrap">
        <div class="search flex">
            <img src="images/search.png" alt="">
            <input type="text" placeholder="搜索学生姓名或学号" id="keyword" >
        </div>
    </div>
    <div class="class-list-wrap">
        <div class="class-mes">
            <ul>
                <li>正常：<i>0</i></li>
                <li th:each="state:${rollcallStateList}" th:inline="text">[[${state.stateNamePc}]]:<i>0</i></li>
            </ul>
        </div>
        <div class="class-list">
            <ul>
            </ul>
        </div>
    </div>
</body>
<script src="js/CXJSBridge.js"></script>
<script src="js/zepto.min.js"></script>
<script src="js/iosSelect.js"></script>
<script src="js/export.js"></script>
<script th:inline="javascript">
	var jxb = [[${jxb}]];
	var kj = [[${kj}]];
	var courseTime = [[${courseTime}]];
    var rollcallStateList = [[${rollcallStateList}]];
//     function _jsBridgeReady() {
//         jsBridge.postNotification('CLIENT_CUSTOM_MENU', { show: 1, menu: "导出", option: 'exportData()' });
//     }
    var stuH = $(window).height() - $(".class-list-wrap").offset().top;
    $(".class-list-wrap").css({ height: stuH + "px" })
    var search = false;
    $('#keyword').on({
        'compositionstart': function() {
            search = true;
        },
        'compositionend': function() {
            search = false;
            if(!search) {
                getData();
            }
        },
        'input propertychange': function() {
            if(!search) {
                getData();
            }
        }
    });
    getData();
    function getData(){
    	var keyword = $("#keyword").val();
    	$.ajax({
        	type: "POST",
        	url: "/elective/rollcall/getJxbNumList",
        	data:{courseTime:courseTime,code:jxb,keyword:keyword,kj:kj},
        	dataType:'json',
        	success: function (data) {
                for (let i = 0; i < data.stateNum.length; i++) {
                    $(".class-mes").find("ul").find("li").eq(i).find("i").html(data.stateNum[i]);
                }
        		var html = "";
        		for(var i = 0;i<data.list.length;i++){
        			html += "<li>";
                    if(data.list[i].str == '正常'){
                        html += "<h1>"+data.list[i].stu+"</h1><span class=\"normal\">正常</span>";
                    }else {
                        for (let j = 0; j <rollcallStateList.length; j++) {
                            if (rollcallStateList[j].stateNamePc ==data.list[i].str){
                                if (j==0){
                                    html += "<h1>"+data.list[i].stu+"</h1><span class=\"beLate\">"+data.list[i].str+"</span>";
                                }else if (j==1){
                                    html += "<h1>"+data.list[i].stu+"</h1><span class=\"leaveEarly\">"+data.list[i].str+"</span>";
                                }else if (j==2){
                                    html += "<h1>"+data.list[i].stu+"</h1><span class=\"askLeave\">"+data.list[i].str+"</span>";
                                }else if (j==3){
                                    html += "<h1>"+data.list[i].stu+"</h1><span class=\"cutSchool\">"+data.list[i].str+"</span>";
                                }else if (j==4){
                                    html += "<h1>"+data.list[i].stu+"</h1><span class=\"others\">"+data.list[i].str+"</span>";
                                }
                            }
                        }
                    }
        			html += "</li>";
        		}
        		$(".class-list").find("ul").html(html);
       		}
		});
    }
</script>

</html>