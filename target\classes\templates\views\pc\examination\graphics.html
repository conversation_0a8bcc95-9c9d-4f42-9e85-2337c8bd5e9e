<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>手动排考</title>

    <link rel="stylesheet" th:href="@{~/css/examination/global.css}">
    <link rel="stylesheet" th:href="@{~/plugin/layui/css/layui.css}">
    <link rel="stylesheet" th:href="@{~/css/examination/tableFilter.css}">
    <link rel="stylesheet" th:href="@{~/css/examination/reset.css}">
    <link rel="stylesheet" th:href="@{~/css/examination/handSchedule.css?v=20240408}">
    <style>
        .layui-table-grid-down {
            display: none;
        }
    </style>
</head>

<body>
<div class="z-main">
    <div class="z-title">
        <h3>手动排考</h3>
        <span>当前考试批次：<span id="bcCode"></span></span>
    </div>
    <div class="z-con">
        <div id="slideMenu"></div>
        <div class="z-menu">
            <div class="z-nav">
                <ul>
                    <li class="active">监考教师</li>
                    <li>考场</li>
                </ul>
            </div>
            <div class="z-box z-invigilate">
                <div class="z-search">
<!--                    <div class="j-search-con" style="width: 100px;">-->
<!--                        <input type="text" name="role" placeholder="请选择" readonly="" class="schoolSel">-->
<!--                        <span class="j-arrow"></span>-->
<!--                        <div class="j-select-year ">-->
<!--                            <ul id="campus1">-->

<!--                            </ul>-->
<!--                        </div>-->
<!--                    </div>-->
                    <input type="text" class="teacher-name" placeholder="请输入监考教师姓名">
                    <span></span>
                </div>
                <div class="title">姓名 /工作量</div>
                <div class="con-list">
                    <ul id="workload">
                    </ul>
                </div>
                <div id="load-more-button-teacher"
                     style="display:none;font-size: 14px;line-height: 20px;text-align: center; margin-top:-20px;color: #999999">
                    加载中...
                </div>


            </div>
            <div class="z-box  z-classroom" style="display: none;">
                <div class="z-search">
<!--                    <div class="j-search-con" style="width: 100px;">-->
<!--                        <input type="text" name="role" placeholder="请选择" readonly="" class="schoolSel">-->
<!--                        <span class="j-arrow"></span>-->
<!--                        <div class="j-select-year ">-->
<!--                            <ul id="campus2">-->

<!--                            </ul>-->
<!--                        </div>-->
<!--                    </div>-->
                    <input type="text" class="teacher-name" placeholder="请输入教室名称">
                    <span></span>
                </div>
                <div class="title">教室 /容量</div>
                <div class="con-list">
                    <ul id="examinationHall">

                    </ul>
                </div>
                <div id="load-more-button-classroom"
                     style="display:none;font-size: 14px;line-height: 20px;text-align: center; margin-top:-20px;color: #999999">
                    加载中...
                </div>

            </div>
        </div>

        <div class="z-table">
            <div class="z-btn">
                <span id="clearData">撤销本次操作</span>
                <span id="saveData">保存设置</span>
            </div>

            <table class="layui-hide materialTable" id="materialTable" lay-filter="materialTable">
            </table>

        </div>
    </div>
</div>
</body>
<script th:inline="javascript">
    let _VR_ = [[${_VR_}]];
</script>
<script th:src="@{~/js/jquery-3.3.1.min.js}"></script>
<script th:src="@{~/plugin/layui/layui.js}"></script>
<script th:src="@{~/js/my.util.js}"></script>
<script th:src="@{~/js/examination/handSchedule.js?v=20240408}"></script>
<script th:inline="javascript">
    var fid = [[${fid}]]
    var xnxq = [[${xnxq}]]
    var bc = [[${bc}]]
    var campus1=[[${campus1}]]
    var campus2=[[${campus2}]]
    var banTeacher = []
    var banClassroom = [];
    /**
     * 考场分页
     * @type {number}
     */
    var page1 = 1;
    /**
     * 考场关键字
     * @type {string}
     */
    var kw1 = "";
    /**
     * 监考教师分页
     * @type {number}
     */
    var page2 = 1;
    /**
     * 监考教师关键字
     * @type {string}
     */
    var kw2 = "";



    var xq1="";
    var xq2="";

    $(function () {
        getBatch();
        getBanTeacher();
        getBanClassroom();
    })


    function getBatch() {
        $.get("/examination/basic/batch", {fid: fid, bc: bc}, function (res) {
            if (res.code == 200) {
                $("#bcCode").html(res.data[0].kspcgl_kspcmc);
                $("#bcCode").attr("data-value", bc);

                $.get("/examination/rule/classroom/get",{fid:fid,bc:bc,xnxq:xnxq},function (res2){
                    if (res2.code==500||res2.data.campus==0){
                        xq1=res.data[0].kapcgl_xq;
                    }
                    getClassroom();

                })
                $.get("/examination/rule/invigilator/get",{fid:fid,bc:bc,xnxq:xnxq},function (res2){
                    if (res2.code==500||res2.data.campus2==0){
                        xq2=res.data[0].kapcgl_xq;
                    }
                    getInvigilator();
                })
            }
        })
    }

    //滚动加载
    var classroom = false;
    $('.z-classroom .con-list').scroll(function () {
        if ($(this).scrollTop() + $(this).innerHeight() >= $(this)[0].scrollHeight - 20) {
            if (!classroom) {
                classroom = true;
                $('#load-more-button-classroom').show();
                // 这里调用你的加载更多内容的函数
                page1 += 1
                getClassroom(kw1)
            }
        }
    });

    var invigilate = false;
    $('.z-invigilate .con-list').scroll(function () {
        if ($(this).scrollTop() + $(this).innerHeight() >= $(this)[0].scrollHeight - 20) {
            if (!invigilate) {
                invigilate = true;
                $('#load-more-button-teacher').show();
                page2 += 1
                // 这里调用你的加载更多内容的函数
                getInvigilator(kw2);
            }
        }
    });

    function getClassroom(kw) {
        if (campus1){
            xq1="";
        }
        $.get("/examination/basic/hall2", {fid: fid, page: page1, kw: kw,campus:xq1}, function (res) {
            if (res.code == 200) {
                //先清空，避免因为响应时间而导致出现多条重复数据
                if (kw!==undefined&&kw!==""){
                    $("#examinationHall").html("")
                }
                $('#load-more-button-classroom').hide()
                var html = '';
                // var campus1 = '';
                var data = res.data;
                // var set = new Set();
                for (let i = 0; i < data.length; i++) {
                    html += '<li data-value="' + data[i].kcgl_kcssxq + '">\n' +
                        '                                <h3 data-value="' + data[i].kcgl_kcbh + '">' + data[i].kcgl_kcmc + '</h3>\n' +
                        '                                <span>' + (data[i].kcgl_kcrl == "" ? 0 : data[i].kcgl_kcrl) + '</span>\n' +
                        '                            </li>'

                    // set.add(data[i].kcgl_kcssxq)
                }
                // set.forEach(data => {
                //     campus1 += '<li>' + data + '</li>'
                // })
                $("#examinationHall").append(html);
                // $("#campus1").html(campus1);
                classroom = false;
            } else {
                $('#load-more-button-classroom').show().text('没有更多内容了');
                setTimeout(function () {
                    $('#load-more-button-classroom').hide()
                }, 2000)
            }
        })
    }

    function getInvigilator(kw) {
        if (campus2){
            xq2="";
        }
        $.get("/examination/basic/invigilator", {fid: fid, xnxq: xnxq, bc: bc, page: page2, kw: kw,campus:xq2}, function (res) {
            if (res.code == 200) {
                //先清空，避免因为响应时间而导致出现多条重复数据
                if (kw!==undefined&&kw!==""){
                    $("#workload").html("")
                }
                $('#load-more-button-teacher').hide()
                var html = '';
                // var campus2 = '';
                var data = res.data;
                // var set = new Set();
                for (let i = 0; i < data.length; i++) {
                    if (data[i].jkjsgl_jsxm == null || data[i].jkjsgl_jsxm == undefined || data[i].jkjsgl_jsxm == '') {
                        continue;
                    }
                    html += '<li data-value="'+data[i].jkjsgl_xq+'" >\n' +
                        '                                <h3 data-value="' + data[i].jkjsgl_jsxm.puid + '"  data-xgh="' + data[i].jkjsgl_jsgh + '">' + data[i].jkjsgl_jsxm.uname + ' </h3>\n' +
                        '                                <span>0</span>\n' +
                        '                            </li>'
                    // set.add(data[i].jkjsgl_xq)
                }


                // set.forEach(data => {
                //     campus2 += '<li>' + data + '</li>'
                // })
                $("#workload").append(html);
                //刷新监考教师工作量
                for (let i = 0; i < data.length; i++) {
                    if (data[i].jkjsgl_jsxm == null || data[i].jkjsgl_jsxm == undefined || data[i].jkjsgl_jsxm == '') {
                        continue;
                    }
                    setTimeout(function (){
                        getCurInvigilationNum(data[i].jkjsgl_jsxm.puid)
                        getLimit(data[i].jkjsgl_jsxm.puid)
                    },50)
                }
                // $("#campus2").html(campus2);
                invigilate = false;

            } else {
                $('#load-more-button-teacher').show().text('没有更多内容了');
                setTimeout(function () {
                    $('#load-more-button-teacher').hide()
                }, 2000)
            }
        })
    }


    function getCurInvigilationNum(uid) {
        $.get("/examination/basic/getInvigilationNum", {fid: fid, xnxq: xnxq, bc: bc, uid: uid}, function (res) {
            if (res.code == 200) {
                $("h3[data-value=" + uid + "]").next().html(res.data)
            }
        })
    }


    function getLimit(uid) {
        $.get("/examination/basic/getLimit", {fid: fid, xnxq: xnxq, bc: bc, uid: uid}, function (res) {
            if (res.code == 200) {
                $("h3[data-value=" + uid + "]").parent().attr("data-limit", res.data)
            }

        })
    }

    $('.teacher-name').bind('keypress', function (event) {
        if (event.keyCode == "13") {
            var tempValue = $(this).val();
            var parents = $(this).parents("div.z-box");
            if (parents.hasClass("z-classroom")) {
                $("#examinationHall").html("")
                page1 = 1;
                kw1 = tempValue;
                getClassroom(tempValue)
            }

            if (parents.hasClass("z-invigilate")) {
                $("#workload").html("")
                page2 = 1;
                kw2 = tempValue;
                getInvigilator(tempValue);
            }


            // var val = $(this).parent(".z-search").children(".j-search-con").children("input").val();
            // var list = $(this).parents(".z-box").children(".con-list").find("li");
            //
            // for (let i = 0; i < list.length; i++) {
            //     if (val != undefined && val != '') {
            //         var selCon = val.split(",");
            //         var value = $(list[i]).attr("data-value");
            //         for (let j = 0; j < selCon.length; j++) {
            //             if (selCon[j] == value) {
            //                 check(list, i, tempValue);
            //                 break
            //             } else {
            //                 $(list[i]).hide();
            //             }
            //         }
            //     } else {
            //         check(list, i, tempValue);
            //     }
            // }
        }
    })

    function getBanTeacher() {
        $.get("/examination/rule/teacher/get", {xnxq: xnxq, bc: bc}, function (res) {
            if (res.code == 200) {
                banTeacher = res.data.data;
            }
        })
    }

    function getBanClassroom() {
        $.get("/examination/rule/classroom2/get", {xnxq: xnxq, bc: bc}, function (res) {
            if (res.code == 200) {
                banClassroom = res.data.data;
            }
        })
    }

    $(".z-search span").click(function () {
        var prev = $(this).prev();
        if (!prev.val()) {
            return false
        }
        prev.val("")
        $('#load-more-button-classroom').show().text('加载中...');
        $('#load-more-button-teacher').show().text('加载中...');
        var parents = $(this).parents("div.z-box");
        if (parents.hasClass("z-classroom")) {
            $("#examinationHall").html("")
            page1 = 1;
            kw1 = "";
            getClassroom()
        }

        if (parents.hasClass("z-invigilate")) {
            $("#workload").html("")
            page2 = 1;
            kw2 = "";
            getInvigilator();
        }

    })

</script>

</html>