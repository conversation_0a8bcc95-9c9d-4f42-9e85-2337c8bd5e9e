<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人培养方案</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/global.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/plugin/layui-v2.9.13/layui/css/layui.css'}">
    <link rel="stylesheet" th:href="@{${_CPR_+_VR_}+'/css/cultivation/reset.css'(v=${new java.util.Date().getTime()})}">
    <link rel="stylesheet" th:href="@{${_CPR_+_VR_}+'/css/cultivation/personalMes.css'(v=${new java.util.Date().getTime()})}"/>
    <script th:src="${_CPR_+_VR_+'/plugin/layui-v2.9.13/layui/layui.js'}"></script>
</head>
<body>
<div class="main">
    <div class="m-top">
        <div class="title">个人培养方案</div>
    </div>
    <div class="item">
        <div class="i-top" style="margin-top: 40px;">
            <h3>学生基本信息</h3>
            <span class="arrow slide"></span>
        </div>
        <div class="stu-mes">
            <div class="photo">
                <img th:src="${student?.xsjbxx_zxzp?.fileUrl}" alt="">
            </div>
            <div class="stu-info">
                <div class="stu-name">
                    <h3 th:text="${student?.xsjbxx_xm}"></h3><span th:text="${student?.xsjbxx_xb}"></span>
                </div>
                <ul class="stu-info-con">
                    <li>
                        <div class="item-name">学号：</div>
                        <div class="item-con" th:text="${student?.xsjbxx_xh}"></div>
                    </li>
                    <li>
                        <div class="item-name">学院：</div>
                        <div class="item-con" th:text="${student?.xsjbxx_yxxx}"></div>
                    </li>
                    <li>
                        <div class="item-name">年级：</div>
                        <div class="item-con" th:text="${student?.xsjbxx_sznj}"></div>
                    </li>
                    <li>
                        <div class="item-name">班级：</div>
                        <div class="item-con" th:text="${student?.xsjbxx_bjxx}"></div>
                    </li>
                    <li>
                        <div class="item-name">专业：</div>
                        <div class="item-con" th:text="${student?.xsjbxx_zyxx}"></div>
                    </li>
                    <li>
                        <div class="item-name">预计毕业学期：</div>
                        <div class="item-con"></div>
                    </li>
                </ul>
                <ul class="stu-achievement">
                    <li>
                        <div class="ach-con">0</div>
                        <div class="ach-type">已获得学分</div>
                    </li>
                    <li>
                        <div class="ach-con">0<span>门</span></div>
                        <div class="ach-type">方案课程总数</div>
                    </li>
                    <li>
                        <div class="ach-con">0<span>门</span></div>
                        <div class="ach-type">已通过课程门数</div>
                    </li>

                    <li>
                        <div class="ach-con">0<span>门</span></div>
                        <div class="ach-type">不及格课程门数</div>
                    </li>
                    <li>
                        <div class="ach-con">0<span>门</span></div>
                        <div class="ach-type">重修课程门数</div>
                    </li>
                </ul>
            </div>

        </div>
    </div>
    <div class="item">
        <div class="filter-wrapper">
            <div class="lable">
                <div class="lable-name">课程名称</div>
                <div class="select-input">
                    <div class="name">请选择</div>
                    <em></em>
                    <div class="select-dropdown">
                        <div class="select-search">
                            <input type="text" placeholder="请输入">
                        </div>
                        <ul class="dropdown-list">
                        </ul>
                    </div>
                </div>
            </div>
            <div class="lable">
                <div class="lable-name">课程性质</div>
                <div class="select-input">
                    <div class="name">请选择</div>
                    <em></em>
                    <div class="select-dropdown">
                        <div class="select-search">
                            <input type="text" placeholder="请输入">
                        </div>
                        <ul class="dropdown-list">
                        </ul>
                    </div>
                </div>
            </div>
            <div class="lable">
                <div class="lable-name">修读性质</div>
                <div class="select-input">
                    <div class="name">请选择</div>
                    <em></em>
                    <div class="select-dropdown">
                        <div class="select-search">
                            <input type="text" placeholder="请输入">
                        </div>
                        <ul class="dropdown-list">
                            <li class="all"><span>全选</span></li>
                            <li><span>重修</span></li>
                            <li><span>补考</span></li>
                            <li><span>清考</span></li>
                            <li><span>正考</span></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="lable">
                <div class="lable-name">修读学年学期</div>
                <div class="select-input">
                    <div class="name">请选择</div>
                    <em></em>
                    <div class="select-dropdown">
                        <div class="select-search">
                            <input type="text" placeholder="请输入">
                        </div>
                        <ul class="dropdown-list">
                        </ul>
                    </div>
                </div>
            </div>
            <div class="lable">
                <div class="lable-name">成绩学年学期</div>
                <div class="select-input">
                    <div class="name">请选择</div>
                    <em></em>
                    <div class="select-dropdown">
                        <div class="select-search">
                            <input type="text" placeholder="请输入">
                        </div>
                        <ul class="dropdown-list">
                        </ul>
                    </div>
                </div>
            </div>
            <div class="lable">
                <div class="lable-name">完成状态</div>
                <div class="select-input">
                    <div class="name">请选择</div>
                    <em></em>
                    <div class="select-dropdown">
                        <div class="select-search">
                            <input type="text" placeholder="请输入">
                        </div>
                        <ul class="dropdown-list">
                            <li class="all"><span>全选</span></li>
                            <li><span>未修</span></li>
                            <li><span>已修/未通过</span></li>
                            <li><span>修读中</span></li>
                            <li><span>已修/已通过</span></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="lable">
                <div class="btn">查询</div>
            </div>
        </div>
    </div>
    <div class="item">
        <div class="i-top i-top-flex" style="margin-bottom: 32px;">
            <div class="i-title">
                <h3>学业完成情况</h3>
                <span class="arrow slide"></span>
            </div>
            <div class="i-right flex">
                <div class="i-progress flex">完成进度：
                    <div class="progress">
                        <div class="progress-con" style="width: 0%;"></div>
                    </div>
                    <div class="progress-text">0%</div>
                </div>
                <div class="refresh" id="tableRefresh">刷新</div>
            </div>
        </div>
        <div class="j-table" style="border:none;">
            <table class="layui-table" id="schoolWork" lay-filter="schoolWork">
            </table>
        </div>
    </div>
</div>
</body>
<!-- 学分名称 -->
<script type="text/html" id="scoreNmeStatus">
    <div class="stu">
        {{# if(d.status ==1 ){ }}
        <span class="status status1">未修</span>
        {{# } else if(d.status ==2 ){ }}
        <span class="status status2">已修/未通过</span>
        {{# } else if(d.status ==3 ){ }}
        <span class="status status3">修读中</span>
        {{# } else if(d.status ==4 ){ }}
        <span class="status status4">已修/已通过</span>
        {{# } }}
    </div>
</script>
<script>
    const _VR_ = "[[${_VR_}]]";
    let majorCode = "[[${student?.xsjbxx_zydm}]]";
    let stuNo = "[[${student?.xsjbxx_xh}]]";
    let grade = "[[${student?.xsjbxx_sznj}]]";
    let classNo = "[[${student?.xsjbxx_bjbh}]]";
    let $ = layui.$;
    layui.use(['table', 'jquery', 'laypage'], function () {
        const table = layui.table, laypage = layui.laypage, $ = layui.jquery;
        // 展开/收起
        $('.i-top .arrow').click(function () {
            $(this).parents('.i-top').next().slideToggle('fast')
            $(this).toggleClass('slide')
        })
        getBasicInformation();
        table.render({
            elem: '#schoolWork',
            url: _VR_ + '/pc/cultivation/personal/completionStatus',
            where: {
                majorCode: majorCode,
                stuNo: stuNo,
                grade: grade
            }, parseData: function (data) {
                if (data.status) {
                    return {
                        "code": 0, //解析接口状态
                        "msg": data.msg, //解析提示文本
                        "count": data.data.count, //解析数据长度
                        "data": data.data.list //解析数据列表
                    }
                } else {
                    return {
                        "code": 1, //解析接口状态
                        "msg": "数据获取失败", //解析提示文本
                        "count": 0, //解析数据长度
                        "data": [] //解析数据列表
                    }
                }
            },
            cols: [
                [
                    {
                        type: 'numbers',
                        field: 'index',
                        title: '序号',
                        minWidth: 60
                    },
                    {
                        field: 'courseId',
                        title: '课程编号',
                        minWidth: 150
                    },
                    {
                        field: 'courseName',
                        title: '课程名称',
                        minWidth: 150
                    },

                    {
                        field: 'courseCredit',
                        title: '标准学分',
                        minWidth: 88
                    },

                    {
                        field: 'courseCategory',
                        title: '课程类别',
                        minWidth: 88
                    },
                    {
                        field: 'courseNature',
                        title: '课程性质',
                        minWidth: 100
                    },
                    {
                        field: 'courseAttribute',
                        title: '课程属性',
                        minWidth: 100
                    },
                    {
                        field: 'chooseCompulsory',
                        title: '选必修',
                        minWidth: 90
                    },
                    {
                        field: 'finalScore',
                        title: '最终成绩',
                        minWidth: 90
                    },
                    {
                        field: 'finalSource',
                        title: '修读性质',
                        minWidth: 90
                    },
                    {
                        field: 'finalCredit',
                        title: '获得学分',
                        minWidth: 90
                    },
                    {
                        field: 'studyTerm',
                        title: '修读学年学期',
                        minWidth: 166
                    },
                    {
                        field: 'scoreTerm',
                        title: '成绩学年学期',
                        minWidth: 166
                    },
                    {
                        field: 'scoreNme',
                        title: '完成状态',
                        templet: '#scoreNmeStatus',
                        minWidth: 142
                    },
                ]
            ],
            text: {
                none: '<img src="/images/cultivation/no-data.png" alt="没有数据" style="width: 72px; height: auto; display: block;margin:0 auto"/>'
            },
            limits: [10, 15, 30, 50, 100],
            height: 458,
            page: {
                prev: '上一页',
                next: '下一页',
                limit: 10,
            },
            done: function (res, curr, count) {
                const data = res.data;
                data.forEach(function (item, index) {
                    if (item.status === 2) {
                        $('div[lay-id="schoolWork"] tbody tr').eq(index).addClass('color-red');
                    }
                });
            }
        });
        // 刷新
        $("#tableRefresh").click(function () {
            getBasicInformation();
            let statusTxt = $(".lable:eq(5) .ckd").text();
            let status = getStatusByText(statusTxt);
            const field = {
                majorCode: majorCode,
                stuNo: stuNo,
                grade: grade,
                courseName: $(".lable:eq(0) .ckd").text(),
                courseNature: $(".lable:eq(1) .ckd").text(),
                studyNature: $(".lable:eq(2) .ckd").text(),
                studyTerm: $(".lable:eq(3) .ckd").text(),
                scoreTerm: $(".lable:eq(4) .ckd").text(),
                status: status
            };
            table.reload('schoolWork', {where: field, page: {curr: 1}})
        })

        $(".select-input").on("click", ".name", function (e) {
            $(this).parent().toggleClass("clicked");
            getSearchCondition();
            stopBubble(e);
        })
        $(".select-input").on("click",
            ".select-dropdown .dropdown-list li ",
            function (e) {
                if ($(this).hasClass("all")) {
                    $(this).toggleClass("cur");
                    if ($(this).hasClass("cur")) {
                        $(this).parent().find("li").addClass("cur");

                    } else {
                        $(this).parent().find("li").removeClass("cur");
                    }
                } else {
                    $(this).toggleClass("cur");
                    let totallis = $(this).parent().find(".cur").length;
                    const curlis = $(this).parent().find("li:not(.all)").length;
                    if ($(this).parent().find(".all").hasClass("cur")) {
                        totallis--;
                    }
                    if (totallis === curlis) {
                        $(this).parent().find(".all").addClass("cur");
                    } else {
                        $(this).parent().find(".all").removeClass("cur");
                    }
                }
                let selCon = [];
                let curEles = $(this).parent().find(".cur");
                curEles.each((index, ele) => {
                    let txt = $(ele).find('span').text();
                    if (txt !== "全选") {
                        selCon.push(txt);
                    }
                });
                if (curEles.length > 0) {
                    $(this).parents(".select-input").find(".name").addClass('ckd')
                    $(this).parents(".select-input").find(".name").text(selCon.join(','))
                } else {
                    $(this).parents(".select-input").find(".name").text('请选择');
                    $(this).parents(".select-input").find(".name").removeClass('ckd')
                }
                stopBubble(e);
            })


        //确定
        $(".select-input").on("click",
            " .select-dropdown .confirm ",
            function () {
                let hmjl = '';
                $(this).prev().find("li.cur:not('.all')").each(function () {
                    hmjl += $(this).find("span").text() + ",";
                })
                hmjl = hmjl.slice(0, -1);
                $(this).parents(".select-input").find(".name").text(hmjl).addClass(
                    "ckd");
                $(this).parents(".select-input").removeClass("clicked");
            })

        // 搜索
        $(".select-input input").on('input', (e) => {
            const val = $(e.target).val().trim();
            const $items = $(e.target).closest(".select-dropdown").find(".dropdown-list li");
            const regex = new RegExp(val, 'i');
            $items.each((i, el) => $(el).toggle(!val || regex.test($(el).find('span').text())));
        });

        function stopBubble(e) {
            if (e && e.stopPropagation)
                e.stopPropagation();
            else {
                window.event.cancelBubble = true;
            }

        }

        $(document).on("click", function (event) {
            var _con = $('.select-input');
            if (!_con.is(event.target) && _con.has(event.target).length === 0) {
                $(".select-input").removeClass("clicked");
            }
        })

        function getStatusByText(text) {
            const statusMap = {
                "未修": 1,
                "已修/未通过": 2,
                "修读中": 3,
                "已修/已通过": 4
            };
            const texts = text.split(',').map(t => t.trim());
            const codes = texts
                .map(t => statusMap[t])
                .filter(code => code !== undefined);
            return codes.join(',');
        }

        $(".btn").click(function () {
            let statusTxt = $(".lable:eq(5) .ckd").text();
            let status = getStatusByText(statusTxt);
            const field = {
                majorCode: majorCode,
                stuNo: stuNo,
                grade: grade,
                courseName: $(".lable:eq(0) .ckd").text(),
                courseNature: $(".lable:eq(1) .ckd").text(),
                studyNature: $(".lable:eq(2) .ckd").text(),
                studyTerm: $(".lable:eq(3) .ckd").text(),
                scoreTerm: $(".lable:eq(4) .ckd").text(),
                status: status
            };
            table.reload('schoolWork', {where: field, page: {curr: 1}})
        })
    })

    function renderDropdownList(selector, data) {
        if (!data || !data.length) return;
        const arr = [...new Set(data)];
        let html = '<li class="all"><span>全选</span></li>';
        arr.forEach(item => {
            html += `<li class=""><span>${item}</span></li>`;
        });
        $(selector).html(html);
    }

    function getSearchCondition() {
        let len = $(".select-input:eq(0) .dropdown-list li").length;
        if (len > 0) {
            return false;
        }
        $.post(_VR_ + "/pc/cultivation/personal/getSearchCondition", {
            majorCode: majorCode,
            grade: grade,
            stuNo: stuNo,
            classNo: classNo
        }, function (result) {
            if (!result.data) {
                return false;
            }
            renderDropdownList(".select-input:eq(0) .dropdown-list", result.data.courseNameList);
            renderDropdownList(".select-input:eq(1) .dropdown-list", result.data.courseNatureList);
            renderDropdownList(".select-input:eq(3) .dropdown-list", result.data.studyTermList);
            renderDropdownList(".select-input:eq(4) .dropdown-list", result.data.scoreTermList);
        });
    }

    function getBasicInformation() {
        $.post(_VR_ + "/pc/cultivation/personal/getBasicInformation", {
            majorCode: majorCode,
            grade: grade,
            stuNo: stuNo,
            classNo: classNo
        }, function (result) {
            let courseTotal = result.data.courseTotal;
            let passCount = result.data.passCount;
            let percent = (passCount / courseTotal) * 100;
            percent = percent ? percent.toFixed(2) : 0;
            $(".stu-info-con li:eq(5) .item-con").text(result.data.classInfo ? result.data.classInfo.bjxx_byxnxq : "");
            $(".stu-achievement li:eq(0) .ach-con").text(result.data.credit ? result.data.credit : 0);
            $(".stu-achievement li:eq(1) .ach-con").html(courseTotal + "<span>门</span>");
            $(".stu-achievement li:eq(2) .ach-con").html(passCount + "<span>门</span>");
            $(".stu-achievement li:eq(3) .ach-con").html(result.data.noPassCount + "<span>门</span>");
            $(".stu-achievement li:eq(4) .ach-con").html(result.data.rebuildCount + "<span>门</span>");
            $(".progress-con").css("width", percent ? percent + "%" : 0 + "%");
            $(".progress-text").text(percent ? percent + "%" : 0 + "%");
        })
    }
</script>

</html>