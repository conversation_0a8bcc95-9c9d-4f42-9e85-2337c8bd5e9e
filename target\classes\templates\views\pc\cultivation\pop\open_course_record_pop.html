<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>公开课记录</title>
    <th:block th:include="common :: header('公开课记录')"/>
    <link rel="stylesheet" th:href="@{../css/cultivation/openCourseRecord.css}">
    <link rel="stylesheet" th:href="@{../css/jquery.mCustomScrollbar.css}">
    <link rel="stylesheet" th:href="@{../css/cultivation/print.css}">
</head>

<body style="min-width:1280px;">
<div class="popup school-diag" style="display:block;">
    <div class="window" style="width:840px;">
        <div class="p-content clearfixs">
            <div class="st-top Noprint">
                <div class="sel">
                    <div class="lable week-item">
                        <div class="names">周次</div>
                        <div class="select-input">
                            <div class="name">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <div class="search">
                                    <input type="text" placeholder="搜索"/>
                                    <span></span>
                                </div>
                                <ul class="dropdown-list term">
                                </ul>
                            </div>
                        </div>
                        <div class="names">学期</div>
                        <div class="select-input">
                            <div class="name ckd" th:text="${curTerm}"></div>
                            <em></em>
                            <div class="select-dropdown">
                                <div class="search">
                                    <input type="text" placeholder="搜索"/>
                                    <span></span>
                                </div>
                                <ul class="dropdown-list">
                                    <li th:each="term : ${list}" th:text="${term.xnxq_xnxqh}"></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="print" onclick="doPrint();" style="right: 280px;">打印</div>
                    <div class="btns export" style="right: 140px;">导出选中周次</div>
                    <div class="btns export" style="right: 40px;">导出全部</div>
                </div>
            </div>
            <div class="st-scroll" id="st-scroll" style="display: none;">
                <div style="page-break-after:always;"></div>
                <div class="print-box">
                    <h2></h2>
                    <div class="sa-table" style="width:640px;">
                        <table class="main-table">
                            <thead>
                            <tr>
                            </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script th:src="@{../js/jquery-1.11.3.min.js}"></script>
<script th:src="@{../js/jquery-migrate-1.2.1.min.js}"></script>
<script th:src="@{../js/jquery.mCustomScrollbar.concat.min.js}"></script>
<script th:src="@{../js/jquery.jqprint-0.3.js}"></script>
<script th:src="@{../js/cultivation/openCourseRecord.js(v=${new java.util.Date().getTime()})}"></script>
</body>
</html>