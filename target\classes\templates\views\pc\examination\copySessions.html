<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>复制考试场次</title>
    <link rel="stylesheet" type="text/css" th:href="@{~/css/examination/global.css}"/>
    <link rel="stylesheet" type="text/css" th:href="@{~/css/examination/slideCommon.css}">
    <link rel="stylesheet" type="text/css" th:href="@{~/plugin/layui-v2.8.18/layui/css/layui.css}"/>
    <link rel="stylesheet" type="text/css" th:href="@{~/css/examination/reset2.css}">
    <link rel="stylesheet" type="text/css" th:href="@{~/css/examination/copyBatch.css}">
    <style>
        .laydate-time-list > li:last-child {
            display: none;
        }

        .layui-laydate .layui-laydate-list > li {
            width: 50%;
        }
    </style>
</head>

<body>
<div class="masker"></div>
<div class="dialog" id="copyBatch">
    <div class="dialog-title">
        <h4>复制考试场次</h4>
<!--        <span class="dialog-close"></span>-->
    </div>
    <div class="dialog-con">
        <ul class="batch-message">
            <li><span>学年学期：</span><span th:text="${batch.kspcgl_xnxq}"></span></li>
            <li><span>所属考试批次：</span><span th:text="${batch.kspcgl_kspcmc}"></span></li>
            <li><span>批次开始时间：</span><span th:text="${#dates.format(batch.kspcgl_kssj, 'yyyy-MM-dd')}" id="kssj"></span>
            </li>
            <li><span>批次结束时间：</span><span th:text="${#dates.format(batch.kspcgl_jssj, 'yyyy-MM-dd')}" id="jssj"></span>
            </li>
        </ul>
        <div class="batch-con">
            <div class="batch-sel">
                <h3 class="batch-label">请选择要复制的考试批次 <span data-tip="复制考试批次的天数须与当前考试批次一致(可根据实际条件微调)"></span></h3>
                <div class="j-search-con single-box">
                    <input type="text" name="teacherName" placeholder="请选择" readonly="" class="schoolSel">
                    <span class="j-arrow"></span>
                    <div class="j-select-year ">
                        <div class="search">
                            <input type="text" placeholder="搜索">
                            <span></span>
                        </div>
                        <ul id="batchList">

                        </ul>
                    </div>
                </div>
                <div class="error-tips">与当前考试批次天数不一致</div>
                <div class="batch-tip">以下为复制结果预览(可根据实际条件微调)</div>
            </div>
            <div class="batch-data" style="display: none;">
                <table class="layui-hide batchTable" id="bathTable" lay-filter="batchTable">
                </table>
            </div>
        </div>
    </div>
    <div class="dialog-btn">
<!--        <button class="pu-cancel">取消</button>-->
        <button class="pu-sure">确认复制</button>
    </div>
</div>
</body>
<script th:inline="javascript">
    let _VR_ = [[${_VR_}]];
    let batch = [[${batch}]];
    let fid = [[${fid}]];
</script>
<script th:src="@{~/js/jquery-3.3.1.min.js}"></script>
<script th:src="@{~/plugin/layui-v2.8.18/layui/layui.js}"></script>
<script th:src="@{~/js/my.util.js}"></script>
<script th:src="@{~/js/examination/slideCommon.js?v=20240618}"></script>
<script type="text/html" id="startTime">
    <input class="layui-input startTime" placeholder="选择日期" readonly value="{{= d.startTime || '' }}">
</script>
<script type="text/html" id="endTime">
    <input class="layui-input endTime" placeholder="选择日期" readonly value="{{= d.endTime || '' }}">
</script>
<script>
    $(function () {
        $.get("/examination/basic/batch", {fid: fid}, function (res) {
            if (res.code == 200) {
                var html = "";
                for (let i = 0; i < res.data.length; i++) {
                    var tempData = res.data[i];
                    if (tempData.kspcgl_kspcbh === batch.kspcgl_kspcbh) {
                        continue
                    }
                    html += '<li data-xnxq="' + tempData.kspcgl_xnxq + '" data-value="' + tempData.kspcgl_kspcbh + '" data-kssj="' + tempData.kspcgl_kssj + '" data-jssj="' + tempData.kspcgl_jssj + '">' + tempData.kspcgl_kspcmc + '</li>'
                }
                $("#batchList").html(html);
            }
        })
    })

    layui.use(['jquery', 'table', 'laydate'], function () {
        var $ = layui.jquery;
        var table = layui.table;
        var laydate = layui.laydate;
        var tempDate={};
        // 获取当前行数据 - 自定义方法
        table.getRowData = function (tableId, elem) {
            var index = $(elem).closest('tr').data('index');
            return table.cache[tableId][index] || {};
        };

        var exportData = []
        table.render({
            elem: '#bathTable',
            data: exportData,
            height: '336',
            cols: [
                [{
                    field: "ccmc",
                    title: "场次名称",
                    edit: "text",
                    align: "center",
                },
                    {
                        field: "startTime",
                        title: "场次开始时间",
                        templet: '#startTime'
                    },
                    {
                        field: "endTime",
                        title: "场次结束时间",
                        templet: '#endTime'
                    }
                ]
            ],
            done: function (res, curr, count) {
                var options=this;
                var curKssj = $("#kssj").html();
                var curJssj = $("#jssj").html();
                // laydate
                laydate.render({
                    elem: '.startTime',
                    type: 'datetime',
                    format: "yyyy-MM-dd HH:mm",
                    fullPanel: true,// 2.8+
                    min: curKssj,
                    max: curJssj + " 23:59:59",
                    onConfirm: function (value, date, endDate) {
                        var data = table.getRowData(options.id, this.elem); // 获取当前行数据(如 id 等字段，以作为数据修改的索引)
                        // 更新数据中对应的字段
                        data.startTime = value
                    },
                });

                // laydate
                laydate.render({
                    elem: '.endTime',
                    type: 'datetime',
                    format: "yyyy-MM-dd HH:mm",
                    fullPanel: true,// 2.8+
                    min: curKssj,
                    max: curJssj + " 23:59:59",
                    ready: function(date){
                        tempDate=date;
                    },
                    onConfirm: function (value, date, endDate) {
                        var data = table.getRowData(options.id, this.elem); // 获取当前行数据(如 id 等字段，以作为数据修改的索引)
                        // 更新数据中对应的字段
                        data.endTime=value
                    },
                });
            }


        });


        // 模拟错误提示，根据实际情况添加
        $(".j-search-con.single-box").on("click", ".j-select-year li ", function () {
            var curKssj = $("#kssj").html();
            var curJssj = $("#jssj").html();

            var xnxq = $(this).attr("data-xnxq");
            var kspcbh = $(this).attr("data-value");
            var kssj = $(this).attr("data-kssj");
            var jssj = $(this).attr("data-jssj");

            var diffDay1 = getDiffDay(curKssj, curJssj, 1000 * 3600 * 24 - 1000);
            var diffDay2 = getDiffDay(kssj, jssj, 1000 * 3600 * 24 - 1000);


            var parents = $(this).parents(".j-search-con");
            var schoolSelEle = parents.find(".schoolSel");
            var next = parents.next();


            if (diffDay1 !== diffDay2) {
                schoolSelEle.addClass("error");
                next.show()
                next.next().hide()
                $(".batch-data").hide();
            } else {
                exportData = [];
                $.get("/examination/basic/sessions", {fid: fid, xnxq: xnxq, bc: kspcbh}, function (res) {
                    if (res.code == 200) {
                        var data = res.data;
                        data = data.sort((a, b) => Date.parse(a.ksccgl_kssj) - Date.parse(b.ksccgl_kssj));
                        for (let i = 0; i < data.length; i++) {
                            exportData.push(getNewDate($("#kssj").html(), data[i]));
                        }
                        table.reload("bathTable", {data: exportData});
                        $(".batch-data").show();

                    }
                })
                schoolSelEle.removeClass("error");
                next.hide();
                next.next().show();
            }
        })

        function getDiffDay(date_1, date_2, extraTime) {
            let myDate_1 = Date.parse(date_1)
            //截止日期为当天的23点58分59秒
            let myDate_2 = Date.parse(date_2) + extraTime
            let diffDate = Math.abs(myDate_1 - myDate_2)
            return Math.ceil(diffDate / (1000 * 3600 * 24))
        }


        function formatDate(date) {
            var year = date.getFullYear();
            var month = addZero(date.getMonth() + 1);
            var day = addZero(date.getDate());
            var hour = addZero(date.getHours());
            var minute = addZero(date.getMinutes());
            return year + '-' + month + '-' + day + " " + hour + ":" + minute;
        }

        function addZero(num) {
            return num < 10 ? '0' + num : num;
        }

        function getNewDate(origin, session) {
            var diffDay1 = getDiffDay(session.ksccgl_kssj, session.var11, 0);
            var diffDay2 = getDiffDay(session.ksccgl_kssj, session.var11, 0);
            var time1 = session.ksccgl_kssj.split(" ");
            var time2 = session.ksccgl_jssj.split(" ");
            var kssj = Date.parse(origin + " " + time1[1]) + 1000 * 3600 * 24 * (diffDay1 - 1);
            var jssj = Date.parse(origin + " " + time2[1]) + 1000 * 3600 * 24 * (diffDay2 - 1);
            var json = {
                ccmc: session.ksccgl_ccmc,
                startTime: formatDate(new Date(kssj)),
                endTime: formatDate(new Date(jssj)),
            }
            return json;
        }

        $(".pu-sure").click(function () {
            for (let i = 0; i < exportData.length; i++) {
                var sessions = exportData[i];
                if (sessions.startTime>=sessions.endTime){
                    U.fail("请检查【"+sessions.ccmc+"】场次时间");
                    return false;
                }
            }
            if (document.getElementsByClassName("active").length != 1){
                U.fail("请选择批次");
                return false;
            }
            U.ajax({
                type: 'post',
                url: "/examination/sessions/copy?formUserId="+batch.rowInfo.formUserId,
                data: JSON.stringify(exportData),
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                success:function (res){
                    U.success("复制成功！");
                    console.log(res);
                }
            })

        })
    })


</script>

</html>