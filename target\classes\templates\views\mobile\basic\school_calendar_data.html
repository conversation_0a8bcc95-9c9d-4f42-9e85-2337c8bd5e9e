<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width,initial-scale=1.0, maximum-scale=1.0, user-scalable=0, viewport-fit=cover">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta content="telephone=no" name="format-detection"/>
    <title>校历</title>
    <script th:src="@{/js/responsive-1.0.js}"></script>
    <link rel="stylesheet" th:href="@{/css/global.css}">
    <link rel="stylesheet" th:href="@{/css/calendar/header.css}">
    <link rel="stylesheet" th:href="@{/css/basic/iosSelect.css}">
    <link rel="stylesheet" th:href="@{/css/calendar/date-editing.css}">
</head>

<body>
<div class="header">
    <div class="head-signal"></div>
    <div class="head-con">
        <a href="javascript:void(0)" class="head-return">
            <img th:src="@{/images/calendar/mobile/back-icon.png}" alt="">
        </a>
        <span class="head-title">校历日期编辑</span>
        <span class="head-right">
                <img th:src="@{/images/calendar/mobile/menus.png}" alt="">
            </span>
    </div>
</div>
<div class="main">
    <div class="m-top">
        <div class="lable border">
            <div class="name">日程</div>
            <div class="input">
                <input type="text" placeholder="请输入日程">
            </div>
        </div>
        <div class="lable border">
            <div class="name">地点</div>
            <div class="input">
                <input type="text" placeholder="请输入地点">
            </div>
        </div>
    </div>
    <div class="m-con">
        <ul>
        </ul>
    </div>
</div>

<div class="school-bottom">
    <div class="handle">
        <div class="all-select">
            <span>全选</span>
            <div class="nus">
                <em>0</em>/<i>9</i>
            </div>
        </div>
        <div class="btns">
            <span class="cancle">取消</span>
            <span class="confirm">确定</span>
        </div>
    </div>
</div>

<div class="settingMask">
    <div class="settingModal">
        <div class="settingList list">
            <div class="listItem color1">添加</div>
            <div class="listItem color2">删除</div>
            <div class="listItem color3">查询</div>
        </div>
    </div>
</div>
<script th:src="@{/js/jquery-3.3.1.min.js}"></script>
<script th:src="@{/js/CXJSBridge.js}"></script>
<script th:src="@{/js/iosSelect.js}"></script>
<script th:src="@{/js/mobileVersion.js}"></script>
<script th:src="@{/js/calendar/school_calendar_data.js}"></script>
</body>

</html>