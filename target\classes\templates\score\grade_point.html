<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>成绩管理</title>
    <link rel="stylesheet" href="/score/css/global.css">
    <link rel="stylesheet" href="/score/layui/css/layui.css">
    <link rel="stylesheet" href="/score/css/common.css">
    <link rel="stylesheet" href="/score/css/reset.css">
    <link rel="stylesheet" href="/score/css/indexRule.css">

</head>

<body>

<div class="main">
    <div class="content calculRuleSet">
        <div class="c-top">
            <h2>课程绩点基础设置</h2>
        </div>
        <div class="form-box">
            <form class="layui-form" lay-filter="switch-form" id="saveForm">
                <input type="hidden" id="grdPoiRule_id" name="id" th:value="${grdepoiBase?.id}">
                <input type="hidden" id="grdPoiRule_fid" name="fid" th:value="${fid}">
                <input type="hidden" id="grdPoiRule_xnxq" name="xnxq" th:value="${xnxq}">
                <input type="hidden" id="grdPoiRule_createUid" name="createUid" th:value="${uid}">
                <input type="hidden" id="grdPoiRule_courseEnableFlag" name="courseEnableFlag" th:value="${grdepoiBase?.courseEnableFlag}">
                <input type="hidden" id="grdPoiRule_gpaEnableFlag" name="gpaEnableFlag" th:value="${grdepoiBase?.gpaEnableFlag}">
                <div class="layui-form-item" style="margin-bottom:20px;">
                    <label class="layui-form-label">是否启用课程绩点：</label>
                    <div class="layui-input-block ">
                        <div class="limit-switch" id="sfqyEdit">
                            <input type="checkbox" name="switchormuladiting" th:checked="${grdepoiBase?.courseEnableFlag == 1}" lay-skin="switch"
                                   lay-filter="switchormuladiting">
                            <span th:text="${grdepoiBase?.courseEnableFlag == 1} ? '是':'否'">否</span>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <div class="c-top">
            <h2>课程绩点换算规则设置</h2>
        </div>

        <div class="add-lab" id="add-lab-id" th:styleappend="${grdepoiBase?.courseEnableFlag == 1?'':'display:none'}" >
            <span>添加</span>
        </div>
        <div class="tabWrap" id="tabWrapId" th:styleappend="${grdepoiBase?.courseEnableFlag == 1?'':'display:none'}">
            <table class="layui-table" id="main-table1" lay-filter="dataTable1" lay-data='{page: true}'>
            </table>
        </div>

        <div class="form-box" style="margin-top:20px;">
            <form class="layui-form " lay-filter="editForm">
                <div class="layui-form-item">
                    <label class="layui-form-label">是否允许任课教师自选课程绩点规则：</label>
                    <div class="layui-input-block ">
                        <div class="limit-switch">
                            <input type="checkbox" name="switchTeacherFilter" th:checked="${grdepoiBase?.gpaEnableFlag == 1}" lay-skin="switch"
                                   lay-filter="switchTeacherFilter">
                            <span th:text="${grdepoiBase?.gpaEnableFlag == 1} ? '是':'否'">否</span>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <div class="save-settings">
            <span>保存设置</span>
        </div>

    </div>
</div>

<!-- 添加 -->
<div id="ruleAddPoups" class="addPoups popup">
    <div class="title">
        <div class="name">添加</div>
        <div class="close"></div>
    </div>
    <div class="popup-con">
        <form class="layui-form" lay-filter="addeditForm">
            <input type="hidden" id="grdePoiRuleId" value="">
            <div class="layui-form-item no-flex">
                <label class="layui-form-label"><em>*</em>规则名称：</label>
                <div class="layui-input-block w240">
                    <input type="text" name="templateName" id="ruleName" lay-filter="templateName" placeholder="请输入"
                           autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><em>*</em>绩点数据来源类型：</label>
                <div class="layui-input-block w240">
                    <select name="modules" lay-verify="required" id="dataSources"  lay-filter="originType">
                        <option value="">请选择</option>
                        <option value="1">分数</option>
                        <option value="2">等级</option>
                        <option value="3">排名</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item levelType hide">
                <label class="layui-form-label"><em>*</em>等级级制类型：</label>
                <div class="layui-input-block w240">
                    <select name="modules" lay-verify="required" id="levelId">
                        <option value="">请选择</option>
                        <option th:each="info : ${gradeMakeRuleList}" th:attr="value=${info.id},name=${info.gradeName}"
                                th:text="${info.gradeName}"></option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item flex-item">
                <label class="layui-form-label"><em>*</em>是否启用：</label>
                <div class="layui-input-block">
                    <div class="limit-switch">
                        <input type="checkbox" name="switchGradeFilter" id="enableFlag" lay-skin="switch"
                               lay-filter="switchGradeFilter">
                        <span>否</span>
                    </div>
                </div>
            </div>
        </form>
        <div class="f-top">
            <h3><em>*</em>规则设置</h3>
            <div class="add-lab">
                <span>添加</span>
            </div>
        </div>

        <div class="table table-box1 scrollBox">
            <table class="layui-table" id="main-table2" lay-filter="dataTable2">
            </table>
        </div>
        <div class="table table-box2 scrollBox hide">
            <table class="layui-table" id="main-table3" lay-filter="dataTable3">
            </table>
        </div>
        <div class="table table-box3 scrollBox hide">
            <table class="layui-table" id="main-table4" lay-filter="dataTable4">
            </table>
        </div>
    </div>
    <div class="bottom">
        <button type="reset" class="layui-btn layui-btn-primary exam-cancle">取消</button>
        <button class="layui-btn exam-sure" id="rule-add">确定</button>
    </div>

</div>

<!-- 添加 -->
<div id="gapaAddPoups" class="addPoups popup">
    <div class="title">
        <div class="name">添加</div>
        <div class="close"></div>
    </div>
    <div class="popup-con">
        <form class="layui-form" lay-filter="addeditForm">
            <div class="layui-form-item no-flex">
                <label class="layui-form-label"><em>*</em>规则名称：</label>
                <div class="layui-input-block w240">
                    <input type="text" name="templateName" lay-filter="templateName" placeholder="请输入"
                           autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><em>*</em>绩点数据来源类型：</label>
                <div class="layui-input-block w240">
                    <select name="modules" lay-verify="required">
                        <option value="">请选择</option>
                        <option value="1">分数</option>
                        <option value="2">等级</option>
                        <option value="3">排名</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><em>*</em>等级级制类型：</label>
                <div class="layui-input-block w240">
                    <select name="modules" lay-verify="required">
                        <option value="">请选择</option>
                        <option value="1">分数</option>
                        <option value="2">等级</option>
                        <option value="3">排名</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><em>*</em>是否启用：</label>
                <div class="layui-input-block">

                    <div class="limit-switch">
                        <input type="checkbox" name="switchGradeFilter" lay-skin="switch"
                               lay-filter="switchGradeFilter">
                        <span>否</span>
                    </div>
                </div>
            </div>
        </form>
        <div class="f-top">
            <h3><em>*</em>规则设置</h3>
            <div class="add-lab">
                <span>添加</span>
            </div>
        </div>

        <div class="table table-box1 scrollBox">
            <table class="layui-table" id="main-table5" lay-filter="dataTable3">
            </table>
        </div>
    </div>
    <div class="bottom">
        <button type="reset" class="layui-btn layui-btn-primary exam-cancle">取消</button>
        <button class="layui-btn exam-sure">确定</button>
    </div>

</div>

<script type="text/html" id="barDemo1">
    <a style="color:#3A8BFF;;cursor:pointer;margin-right:8px;" lay-event="edit">编辑</a>
    <a style="color:#F33131;cursor:pointer;" lay-event="del">删除</a>
</script>
<script type="text/html" id="barDemo4">
    <a style="color:#F33131;cursor:pointer;" lay-event="del">删除</a>
</script>
<script src="/js/jquery-3.3.1.min.js"></script>
<script src="/js/jquery.mCustomScrollbar.concat.min.js"></script>
<script src="/score/layui/layui.js"></script>
<script src="/score/js/common.js"></script>
<script type="text/javascript" th:src="@{~/js/my.util.js}"></script>
<script>
    var ruleAddPoupsindex;
    $(function () {
        var form, table, laydate, layer;
        layui.use(['form', 'table', 'laydate'],
            function () {
                var $ = layui.jquery;
                form = layui.form;
                table = layui.table;
                laydate = layui.laydate;
                layer = layui.layer;

                //监听下拉选择事件
                form.on('select(originType)', function(data){
                    console.log(data.value);

                    if(data.value==1){
                        $(".levelType").addClass("hide");
                        $("#ruleAddPoups .table-box1").removeClass("hide");
                        $("#ruleAddPoups .table-box2").addClass("hide");
                        $("#ruleAddPoups .table-box3").addClass("hide");
                        table.reload('main-table2');
                    }else if(data.value==2){
                        $(".levelType").removeClass("hide");
                        $("#ruleAddPoups .table-box1").addClass("hide");
                        $("#ruleAddPoups .table-box2").removeClass("hide");
                        $("#ruleAddPoups .table-box3").addClass("hide");
                        table.reload('main-table3');
                    }else{
                        $(".levelType").removeClass("hide");
                        $("#ruleAddPoups .table-box1").addClass("hide");
                        $("#ruleAddPoups .table-box2").addClass("hide");
                        $("#ruleAddPoups .table-box3").removeClass("hide");
                        table.reload('main-table4');
                    }
                });

                //开关监听
                form.on('switch(switchormuladiting)', function (data) {
                    if (data.elem.checked) {
                        $(this).parent().find("span").text('是');
                        $("#tabWrapId").show();
                        $("#add-lab-id").show();
                        $("#grdPoiRule_courseEnableFlag").val(1);
                        table.reload('dataTable1');
                    } else {
                        $(this).parent().find("span").text('否');
                        $("#tabWrapId").hide();
                        $("#add-lab-id").hide();
                        $("#grdPoiRule_courseEnableFlag").val(0);
                    }
                });

                form.on('switch(switchTeacherFilter)', function (data) {
                    if (data.elem.checked) {
                        $(this).parent().find("span").text('是');
                        $("#grdPoiRule_gpaEnableFlag").val(1);
                    } else {
                        $(this).parent().find("span").text('否');
                        $("#grdPoiRule_gpaEnableFlag").val(0);
                    }
                });

                form.on('switch(switchGradeFilter)', function (data) {
                    if (data.elem.checked) {
                        $(this).parent().find("span").text('是');
                    } else {
                        $(this).parent().find("span").text('否');
                    }
                });

                $(".save-settings span").click(function (){
                    savegrdPoiRule();
                })

                var table1 = table.render({
                    elem: "#main-table1",
                    id: 'main-table1',
                    page: {
                        limit: 10,
                        limits: [10, 20, 30, 40, 50],
                        layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                    },
                    where: {
                        'fid': $("#grdPoiRule_fid").val(),
                        "xnxq": $("#grdPoiRule_xnxq").val()
                    },
                    request: {
                        pageName: "curPage",
                        limitName: "pageSize"
                    },
                    parseData: function (res) {
                        return {
                            "count": res.data.count,
                            "data": res.data.dataList,
                            "code": 0
                        }
                    },
                    url: '/api/score/gradePoint/getGrdepoiRuleList',
                    cols: [
                        [{
                            field: "ruleName",
                            align: "center",
                            title: "规则名称",
                            minWidth: 120,
                        },
                            {
                                field: "dataSources",
                                align: "center",
                                title: "绩点数据来源类型",
                                minWidth: 120,
                                templet: function (d) {
                                    if (d.dataSources == "1") {
                                        return '<span>分数</span>';
                                    } else if(d.dataSources == "2"){
                                        return '<span>等级</span>';
                                    } else if(d.dataSources == "3"){
                                        return '<span>排名</span>';
                                    }
                                }
                            },
                            {
                                field: "enableFlag",
                                align: "center",
                                title: "是否启用",
                                minWidth: 120,
                                templet: function (d) {
                                    if (d.enableFlag == "1") {
                                        return '<span class="wstatus correct">是</span>';
                                    } else {
                                        return '<span class="wstatus deny">否</span>';
                                    }
                                }
                            },
                            {
                                field: "options",
                                align: "center",
                                title: "操作",
                                toolbar: "#barDemo1",
                                minWidth: 120
                            },
                        ]
                    ],
                    done: function (res) {}
                })

                var dataList = [];
                var table2 = table.render({
                    elem: "#main-table2",
                    id: 'main-table2',
                    data: dataList,
                    cols: [
                        [{
                            field: "1",
                            align: "center",
                            title: "分数区间",
                            width: '400',
                            templet: function (d) {
                                var html = '<div class="fractionalSegment">' +
                                    '<input type="number" min="0" name="beforeScore" value="' +
                                    d.beforeScore +
                                    '" lay-filter="beforeScore" placeholder="" autocomplete="off" class="layui-input w64">' +
                                    '<select name="modules" lay-verify="required">';
                                if(d.selectType == "1"){
                                    html += '<option value="1" selected>≤  成绩  <</option>';
                                } else {
                                    html += '<option value="1">≤  成绩  <</option>';
                                }
                                if(d.selectType == "2"){
                                    html += '<option value="2" selected>≤  成绩  ≤</option>';
                                } else {
                                    html += '<option value="2">≤  成绩  ≤</option>';
                                }
                                if(d.selectType == "3"){
                                    html += '<option value="3" selected><  成绩  ≤</option>';
                                } else {
                                    html += '<option value="3"><  成绩  ≤</option>';
                                }
                                html += '</select>' +
                                    '<input type="number" min="0" name="afterScore" value="'+d.afterScore+'" ' +
                                    'lay-filter="afterScore" placeholder="" autocomplete="off" class="layui-input w64">' +
                                    '</div>';
                                return html;
                            }
                        },
                            {
                                field: "point",
                                align: "center",
                                width: '185',
                                title: "绩点",
                                templet: function (d) {
                                    console.log(d.point)
                                    return ' <input type="number" style="width:60px;" value="' +
                                        d.point +
                                        '"  placeholder="" autocomplete="off" class="layui-input">'
                                }
                            },
                            {
                                field: "options",
                                align: "center",
                                width: '185',
                                title: "操作",
                                toolbar: "#barDemo4",
                            },
                        ]
                    ],
                    done: function (res) {}
                })

                var table3 = table.render({
                    elem: "#main-table3",
                    id: 'main-table3',
                    data: [],
                    cols: [
                        [{
                            field: "pgradelevelname",
                            align: "center",
                            title: "级制名称",
                            width: '185',
                            merge:true
                        },
                            {
                                field: "gradelevelname",
                                align: "center",
                                width: '185',
                                title: "等级名称",
                                templet: function (d) {
                                    return ' <span class="gradelevel" gid="'+d.gradelevelid+'">'+d.gradelevelname+'</span>';
                                }
                            },
                            {
                                field: "point",
                                align: "center",
                                width: '185',
                                title: "绩点",
                                templet: function (d) {
                                    console.log(d.point)
                                    return ' <input type="number" style="width:60px;" value="' +
                                        d.point +
                                        '"  placeholder="" autocomplete="off" class="layui-input">'
                                }
                            },
                            {
                                field: "options",
                                align: "center",
                                width: '185',
                                title: "操作",
                                toolbar: "#barDemo4",
                            },
                        ]
                    ],
                    done: function (res) {
                        merge(this);
                    }
                })

                var dataList4 = [];
                var table2 = table.render({
                    elem: "#main-table4",
                    id: 'main-table4',
                    data: dataList4,
                    cols: [
                        [{
                            field: "",
                            align: "center",
                            title: "学生排名",
                            width: '400',
                            templet: function (d) {
                                var html = '<div class="fractionalSegment">' +
                                    '<input type="number" min="0" name="beforeScore" value="' +
                                    d.beforeScore +
                                    '" lay-filter="beforeScore" placeholder="" autocomplete="off" class="layui-input w64">' +
                                    '<select name="modules" lay-verify="required">';
                                if(d.selectType == "1"){
                                    html += '<option value="1" selected>≤  成绩  <</option>';
                                } else {
                                    html += '<option value="1">≤  成绩  <</option>';
                                }
                                if(d.selectType == "2"){
                                    html += '<option value="2" selected>≤  成绩  ≤</option>';
                                } else {
                                    html += '<option value="2">≤  成绩  ≤</option>';
                                }
                                if(d.selectType == "3"){
                                    html += '<option value="3" selected><  成绩  ≤</option>';
                                } else {
                                    html += '<option value="3"><  成绩  ≤</option>';
                                }
                                html += '</select>' +
                                    '<input type="number" min="0" name="afterScore" value="'+d.afterScore+'" ' +
                                    'lay-filter="afterScore" placeholder="" autocomplete="off" class="layui-input w64">' +
                                    '</div>';
                                return html;
                            }
                        },
                            {
                                field: "point",
                                align: "center",
                                width: '185',
                                title: "绩点",
                                templet: function (d) {
                                    console.log(d.point)
                                    return ' <input type="number" style="width:60px;" value="' +
                                        d.point +
                                        '"  placeholder="" autocomplete="off" class="layui-input">'
                                }
                            },
                            {
                                field: "options",
                                align: "center",
                                width: '185',
                                title: "操作",
                                toolbar: "#barDemo4",
                            },
                        ]
                    ],
                    done: function (res) {}
                })

                /* 监听工具条 */
                table.on("tool(dataTable1)", function (obj) {
                    var data = obj.data; //获得当前行数据
                    if (obj.event === "del") {
                        deleteGrdepoiRule(data.id);
                    } else if (obj.event === "edit") {
                        console.log(data);
                        ruleAddPoupsindex = layer.open({
                            type: 1,
                            title: false,
                            closeBtn: false,
                            shadeClose: true,
                            isOutAnim: true,
                            content: $('#ruleAddPoups'),
                            area: ['auto', 'auto'],
                            success: function () {
                                $("#grdePoiRuleId").val(data.id);
                                $("#ruleName").val(data.ruleName);
                                $("#dataSources").val(data.dataSources);
                                if(data.enableFlag == 1){
                                    $("#enableFlag").attr("checked","checked");
                                } else {
                                    $("#enableFlag").removeAttr("checked");
                                }
                                var ruleSettext = [];
                                if(data.ruleSettext != null && data.ruleSettext != ""){
                                    ruleSettext = JSON.parse(data.ruleSettext);
                                } else {
                                    ruleSettext = [];
                                }
                                table.reload('main-table2', {
                                    data: ruleSettext
                                })
                                $("#totalLevel").show();
                                form.render();
                            },

                        }, function () {

                        });
                    }
                })

                table.on("tool(dataTable2)", function (obj) {
                    var data = obj.data; //获得当前行数据
                    if (obj.event === "del") {
                        obj.del();
                        layer.msg("删除")
                    }
                })
                table.on("tool(dataTable3)", function (obj) {
                    var data = obj.data; //获得当前行数据
                    if (obj.event === "del") {
                        obj.del();
                        layer.msg("删除")
                    }
                })

                table.on("tool(dataTable4)", function (obj) {
                    var data = obj.data; //获得当前行数据
                    if (obj.event === "del") {
                        obj.del();
                        layer.msg("删除")
                    }
                })

                $("#rule-add").click(function (){
                    var ruleName = $("#ruleName").val();
                    var dataSources = $("#dataSources").find("option:selected").val();
                    var enableFlag = $("#enableFlag").is(":checked") ? "1" : "0";
                    var levelId = $("#levelId").find("option:selected").val();
                    if(U.isEmpty(ruleName)){
                        layer.msg("请输入规则名称！");
                        return;
                    }
                    if(U.isEmpty(dataSources)){
                        layer.msg("请选择绩点数据来源类型！");
                        return;
                    }
                    if(dataSources == "2"){
                        if(U.isEmpty(levelId)){
                            layer.msg("请选择等级级制类型！");
                            return;
                        }
                    }
                    var ruleText = getDataList(dataSources);
                    if(ruleText.length == 0){
                        layer.msg("请添加规则设置！");
                        return;
                    }
                    let formdata = new FormData();
                    formdata.append("ruleName", ruleName);
                    formdata.append("dataSources", dataSources);
                    formdata.append("enableFlag", enableFlag);
                    formdata.append("levelId", levelId);
                    formdata.append("ruleSettext", JSON.stringify(ruleText));
                    formdata.append("fid", $("#grdPoiRule_fid").val());
                    formdata.append("xnxq", $("#grdPoiRule_xnxq").val());
                    formdata.append("createUid", $("#grdPoiRule_createUid").val());
                    formdata.append("id", $("#grdePoiRuleId").val());
                    $.ajax({
                        type: "POST",
                        url: "/api/score/gradePoint/saveGrdepoiRule",
                        data: formdata,
                        contentType : false,
                        processData : false,
                        dataType: 'json',
                        success: function (result) {
                            if(result.code == 200){
                                layer.close(ruleAddPoupsindex);
                                table.reload('main-table1');
                            } else {
                                layer.msg(result.msg);
                            }
                        }
                    });
                })

                window.getDataList = function(type){
                    var levelId = $("#levelId").find("option:selected").val();
                    var levelName = $("#levelId").find("option:selected").text();
                    if(type == 1){
                        var ruleTextArr = [];
                        var $table = $(".table-box1").find(".layui-table-view").find(".layui-table-box").find(".layui-table-main").find("table");
                        $table.find("tbody tr").each(function (){
                            var $row = $(this);
                            var rowData = {};
                            $row.find("td").each(function(){
                                var length1 = $(this).find(".layui-table-cell").find(".fractionalSegment").length;
                                var length2 = $(this).find(".layui-table-cell").find("input").length;
                                if(length1 > 0){
                                    var $obj = $(this).find(".layui-table-cell").find(".fractionalSegment");
                                    rowData["beforeScore"] = $obj.find("input[name='beforeScore']").val();
                                    rowData["selectType"] = $obj.find("select").find("option:selected").val();
                                    rowData["afterScore"] = $obj.find("input[name='afterScore']").val();
                                }
                                if(length2 > 0){
                                    var $obj = $(this).find(".layui-table-cell").find("input");
                                    rowData["point"] = $obj.val();
                                }
                            })
                            ruleTextArr.push(rowData);
                        })
                        return ruleTextArr;
                    } else if(type == "2"){
                        var ruleTextArr = [];
                        var $table = $(".table-box2").find(".layui-table-view").find(".layui-table-box").find(".layui-table-main").find("table");
                        $table.find("tbody tr").each(function (i) {
                            var $row = $(this);
                            var rowData = {};
                            $row.find("td").each(function(){
                                var length1 = $(this).find(".layui-table-cell").find(".gradelevel").length;
                                var length2 = $(this).find(".layui-table-cell").find("input").length;
                                if(length1 > 0){
                                    var $obj = $(this).find(".layui-table-cell").find(".gradelevel");
                                    rowData["gradelevelid"] = $obj.attr("gid");
                                    rowData["gradelevelname"] = $obj.text();
                                    rowData["pgradelevelid"] = levelId;
                                    rowData["pgradelevelname"] = levelName;
                                }
                                if(length2 > 0){
                                    var $obj = $(this).find(".layui-table-cell").find("input");
                                    rowData["point"] = $obj.val();
                                }
                            })
                            ruleTextArr.push(rowData);
                        })
                        console.log(ruleTextArr);
                        return ruleTextArr;
                    } else if(type == "3"){
                        var ruleTextArr = [];
                        var $table = $(".table-box3").find(".layui-table-view").find(".layui-table-box").find(".layui-table-main").find("table");
                        $table.find("tbody tr").each(function (){
                            var $row = $(this);
                            var rowData = {};
                            $row.find("td").each(function(){
                                var length1 = $(this).find(".layui-table-cell").find(".fractionalSegment").length;
                                var length2 = $(this).find(".layui-table-cell").find("input").length;
                                if(length1 > 0){
                                    var $obj = $(this).find(".layui-table-cell").find(".fractionalSegment");
                                    rowData["beforeScore"] = $obj.find("input[name='beforeScore']").val();
                                    rowData["selectType"] = $obj.find("select").find("option:selected").val();
                                    rowData["afterScore"] = $obj.find("input[name='afterScore']").val();
                                }
                                if(length2 > 0){
                                    var $obj = $(this).find(".layui-table-cell").find("input");
                                    rowData["point"] = $obj.val();
                                }
                            })
                            ruleTextArr.push(rowData);
                        })
                        return ruleTextArr;
                    }

                }

                //添加
                $("#ruleAddPoups .popup-con .f-top .add-lab span").click(function () {
                    var dataSources = $("#dataSources").find("option:selected").val();
                    var levelId = $("#levelId").find("option:selected").val();
                    var levelName = $("#levelId").find("option:selected").text();
                    var ruleText = getDataList(dataSources);
                    var elemId = "";
                    if(dataSources == "1"){
                        ruleText.push({
                            "beforeScore": "",
                            "afterScore": "",
                            "selectType": "",
                            "point": ""
                        });
                        elemId = 'main-table2';
                    } else if(dataSources == "2"){
                        var dataArr = getGrdeSelectList(levelId);
                        for(var i in dataArr){
                            ruleText.push({
                                "gradelevelid": dataArr[i].id,
                                "gradelevelname": dataArr[i].name,
                                "pgradelevelid": levelId,
                                "pgradelevelname": levelName,
                                "point": ""
                            });
                        }

                        elemId = 'main-table3';
                    } else if(dataSources == "3"){
                        ruleText.push({
                            "beforeScore": "",
                            "afterScore": "",
                            "selectType": "",
                            "point": ""
                        });
                        elemId = 'main-table4';
                    }
                    table.reload(elemId, {
                        data: ruleText
                    });
                })


                //添加
                $(".calculRuleSet .add-lab span").click(function () {

                    ruleAddPoupsindex = layer.open({
                        type: 1,
                        title: false,
                        closeBtn: false,
                        shadeClose: true,
                        isOutAnim: true,
                        content: $('#ruleAddPoups'),
                        area: ['auto', 'auto'],
                        success: function () {
                            table.reload('main-table2');
                            $("#totalLevel").show();
                        },

                    }, function () {

                    });
                })


                //隐藏弹窗
                $('.close,.exam-cancle').on("click", function () {
                    var index = $(this).parents(".layui-layer").attr("times");
                    layer.close(index);
                })

                function merge(myTable) {
                    var tableBox = $(myTable.elem).next().children('.layui-table-box'),
                        $main = $(tableBox.children('.layui-table-body').children('table').children('tbody').children('tr').toArray().reverse()),
                        $fixLeft = $(tableBox.children('.layui-table-fixed-l').children('.layui-table-body').children('table').children('tbody').children('tr').toArray().reverse()),
                        $fixRight = $(tableBox.children('.layui-table-fixed-r').children('.layui-table-body').children('table').children('tbody').children('tr').toArray().reverse()),
                        cols = myTable.cols[0], mergeRecord = {};

                    for (let i = 0; i < cols.length; i++) {
                        var item3 = cols[i], field=item3.field;
                        if (item3.merge) {
                            var mergeField = [field];
                            if (item3.merge !== true) {
                                if (typeof item3.merge == 'string') {
                                    mergeField = [item3.merge]
                                } else {
                                    mergeField = item3.merge
                                }
                            }
                            mergeRecord[i] = {mergeField: mergeField, rowspan:1}
                        }
                    }

                    $main.each(function (i) {

                        for (var item in mergeRecord) {
                            if (i==$main.length-1 || isMaster(i, item)) {
                                $(this).children('[data-key$="-'+item+'"]').attr('rowspan', mergeRecord[item].rowspan).css('position','static');
                                $fixLeft.eq(i).children('[data-key$="-'+item+'"]').attr('rowspan', mergeRecord[item].rowspan).css('position','static');
                                $fixRight.eq(i).children('[data-key$="-'+item+'"]').attr('rowspan', mergeRecord[item].rowspan).css('position','static');
                                mergeRecord[item].rowspan = 1;
                            } else {
                                $(this).children('[data-key$="-'+item+'"]').remove();
                                $fixLeft.eq(i).children('[data-key$="-'+item+'"]').remove();
                                $fixRight.eq(i).children('[data-key$="-'+item+'"]').remove();
                                mergeRecord[item].rowspan +=1;
                            }
                        }
                    })

                    function isMaster (index, item) {
                        var mergeField = mergeRecord[item].mergeField;
                        var dataLength = layui.table.cache[myTable.id].length;
                        for (var i=0; i<mergeField.length; i++) {

                            if (layui.table.cache[myTable.id][dataLength-2-index][mergeField[i]]
                                !== layui.table.cache[myTable.id][dataLength-1-index][mergeField[i]]) {
                                return true;
                            }
                        }
                        return false;
                    }
                }

                window.deleteGrdepoiRule = function(id){
                    var confirmindex = layer.confirm('确认删除？', {
                        btn: ['确定', '取消'] //按钮
                    }, function () {
                        $.ajax({
                            type: "POST",
                            url: "/api/score/gradePoint/deleteGrdepoiRule",
                            data: {"id": id},
                            dataType: 'json',
                            success: function (result) {
                                if(result.code == 200){
                                    layer.msg("删除成功");
                                    table.reload('main-table1');
                                }
                            },
                            error: function(){
                                layer.msg("系统错误");
                            }
                        });
                    }, function () {
                        layer.close(confirmindex);
                    });
                }

            })
    })

    function savegrdPoiRule(){
        $.ajax({
            type: "POST",
            url: "/api/score/gradePoint/saveGrdepoiBase",
            data: $("#saveForm").serialize(),
            dataType: 'json',
            async: false,
            success: function (result) {
                if(result.code == "200"){
                    $("#grdPoiRule_id").val(result.data.grdepoiBase.id);
                    layer.msg('保存成功');
                }
            },
            error: function(){
                layer.msg('系统错误');
            }
        });
    }

    function getGrdeSelectList(id){
        var dataArr = [];
        $.ajax({
            type: "POST",
            url: "/api/score/gradePoint/getGrdeSelectList",
            data: {"id": id},
            dataType: 'json',
            async: false,
            success: function (result) {
                if(result.code == "200"){
                    dataArr = result.data.dataList;
                }
            },
            error: function(){
                layer.msg('系统错误');
            }
        });
        return dataArr;
    }
</script>

</body>

</html>