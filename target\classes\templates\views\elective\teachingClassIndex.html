<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
        content="width=device-width, user-scalable=no,initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <title>微点名</title>
    <script src="js/responsive.js"></script>
    <link rel="stylesheet" href="css/global.css">
    <link rel="stylesheet" href="css/iosSelect.css">
    <link rel="stylesheet" href="css/rollCall.css">
</head>
<body>
    <div class="search-wrap">
        <div class="search flex">
            <img src="images/search.png" alt="">
            <input type="text" id="teachingClassNameIp" placeholder="搜索教学班" onkeyup="getList();">
        </div>
    </div>
    <div class="con-select">
        <ul>
            <li data-cla="course" id="selCourse"><span>课程</span> <i></i></li>
            <li data-cla="date" id="selDate"><span>日期</span> <i></i></li>
        </ul>
    </div>
    <div class="class-list" id="listBox">
    </div>
    <div class="marker" style="display: none;"></div>
    <div class="slide-sel course-sel" data-cla="course">
        <div class="course-list" id="courseBox">
        </div>
    </div>
</body>
<script src="js/zepto.min.js"></script>
<script src="js/iosSelect.js"></script>
<script src="js/CXJSBridge.js"></script>
<script>
    function _jsBridgeReady() {

        jsBridge.postNotification('CLIENT_CUSTOM_MENU', {show: 1, menu: "记录", option: 'toStatic()'});
    }
    $(document).ready(function () {
    	var fid = [[${fid}]];
    	var uid = [[${uid}]];
        var offsetHeight = $('.class-list').offset().top;
        var winH = $(window).height();
        var claH = winH - offsetHeight;
        $(".class-list").css({ height: claH + 'px' });
        //    下拉选择
        $(".con-select li[data-cla='course']").on('click', function () {
            let idx = $(this).index();
            let dataAttr = $(this).attr('data-cla');
            $(this).toggleClass("active").siblings().removeClass("active");
            let isHasActive = $(this).hasClass("active");
            if (isHasActive) {
                $(".marker").show()
                $(".course-sel").show();
            } else {
                $(".marker").hide()
                $('.course-sel').hide();
            }
        });
        // 选择课程
        $(".course-sel").on('click','ul li', function () {
            $(".course-list ul li").removeClass("active");
            $(this).addClass("active");
            $(this).parents(".course-sel").hide();
            $(".marker").hide();
            var text = $(this).find('h1').text();
            $(".con-select ul li:first-child span").text(text);
            var bh = $(this).find('h1').attr("bh");
            $(".con-select ul li:first-child span").attr("bh",bh);
            $(".con-select ul li:first-child").removeClass("active");
            getList();
        });
        //点击遮罩
        $(".marker").click(function () {
            $(".marker,.slide-sel").hide();
            $(".con-select ul li").removeClass("active");
        })
        var showDateDom = $('#selDate');
        // 初始化时间
        var now = new Date();
        var nowYear = now.getFullYear();
        var nowMonth = now.getMonth() + 1;
        var nowDate = now.getDate();
        showDateDom.attr('data-year', nowYear);
        showDateDom.attr('data-month', nowMonth);
        showDateDom.attr('data-date', nowDate);
        // 数据初始化
        function formatYear(nowYear) {
            var arr = [];
            for (var i = nowYear - 5; i <= nowYear + 5; i++) {
                arr.push({
                    id: i + '',
                    value: i + '年'
                });
            }
            return arr;
        }
        function formatMonth() {
            var arr = [];
            for (var i = 1; i <= 12; i++) {
                arr.push({
                    id: i + '',
                    value: i + '月'
                });
            }
            return arr;
        }
        function formatDate(count) {
            var arr = [];
            for (var i = 1; i <= count; i++) {
                arr.push({
                    id: i + '',
                    value: i + '日'
                });
            }
            return arr;
        }
        var yearData = function (callback) {
            callback(formatYear(nowYear))
        };
        var monthData = function (year, callback) {
            callback(formatMonth());
        };
        var dateData = function (year, month, callback) {
            if (/^(1|3|5|7|8|10|12)$/.test(month)) {
                callback(formatDate(31));
            } else if (/^(4|6|9|11)$/.test(month)) {
                callback(formatDate(30));
            } else if (/^2$/.test(month)) {
                if (year % 4 === 0 && year % 100 !== 0 || year % 400 === 0) {
                    callback(formatDate(29));
                } else {
                    callback(formatDate(28));
                }
            } else {
                throw new Error('month is illegal');
            }

        };
        showDateDom.bind('click', function () {
            var oneLevelId = showDateDom.attr('data-year');
            var twoLevelId = showDateDom.attr('data-month');
            var threeLevelId = showDateDom.attr('data-date');
            var iosSelect = new IosSelect(3,
                [yearData, monthData, dateData],
                {
                    title: '选择时间',
                    itemHeight: 35,
                    oneLevelId: oneLevelId,
                    twoLevelId: twoLevelId,
                    threeLevelId: threeLevelId,
                    showLoading: true,
                    callback: function (selectOneObj, selectTwoObj, selectThreeObj) {
                        showDateDom.attr('data-year', selectOneObj.id);
                        showDateDom.attr('data-month', selectTwoObj.id);
                        showDateDom.attr('data-date', selectThreeObj.id);
                        showDateDom.find('span').text(selectOneObj.value + selectTwoObj.value + selectThreeObj.value);
                    }
                });
        });
    })
    getList();
    function getList(){
    	var courseTime = $("#selDate").find("span").text();
    	var courseCode = $("#selCourse").find("span").attr("bh");
    	var teachingClassName = $("#teachingClassNameIp").val();
    	$.ajax({
        	type: "POST",
        	url: "/elective/rollcall/getTeachingClassList",
        	data:{courseTime:courseTime,teachingClassName:teachingClassName,courseCode:courseCode},
        	dataType:'json',
        	success: function (data) {
        		var html = "";
            	if(data.status){
            		for(var i = 0;i<data.list.length;i++){
            			html+="<a href=\"javascript:toRollcall('/elective/rollcall/teacherRollcall?teachingClassCode="+data.list[i].pkjgsj_jxbbh+"&teachingClassName="+data.list[i].pkjgsj_jxbmc+"&courseName="+data.list[i].pkjgsj_kcmc+"&zc="+data.list[i].pkjgsj_zc+"&kj="+data.list[i].pkjgsj_kj+"&xq="+data.list[i].pkjgsj_xq+"&classDate="+data.courseTime+"')\">";
            			html+="<div class=\"class-name\">";
                		html+="<h1>"+data.list[i].pkjgsj_kcmc+"</h1>";
                		html+="<span></span></div>";
            			html+="<div class=\"class-week\"><span>第"+data.list[i].pkjgsj_zc+"周</span><span>周"+data.list[i].pkjgsj_xq+"</span><span>第"+data.list[i].pkjgsj_kj+"节</span></div></a>";
            		}
            	}
            	$("#listBox").html(html);
       		}
		});
    }
    $.ajax({
       	type: "POST",
       	url: "/elective/rollcall/getCourseList",
       	dataType:'json',
       	success: function (data) {
       		var html = "";
           	if(data.status){
           		for(var i = 0;i<data.list.length;i++){
           			html+="<ul>";
                	html+="<li>";
                    html+="<h1 bh=\""+data.list[i].bh+"\">"+data.list[i].mc+"</h1>";
                    html+="<span></span></li></ul>";
           		}
           	}
           	$("#courseBox").html(html);
      	}
	});
	function toRollcall(url){
		var webUrl = window.location.origin+url;
        jsBridge.postNotification('CLIENT_OPEN_URL', { "toolbarType": 2, "webUrl": webUrl, "loadType": 1 });
	}
	function toStatic(){
        var webUrl = window.location.origin+"/elective/rollcall/staticIndex?js=1";
        jsBridge.postNotification('CLIENT_OPEN_URL', { "toolbarType": 2, "webUrl": webUrl, "loadType": 1 });
    }
</script>
</html>