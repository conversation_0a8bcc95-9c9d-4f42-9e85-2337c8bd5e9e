<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>班课速配</title>
    <link rel="stylesheet" th:href="@{${_CPR_}+'/css/cultivation/global.css'}">
    <link rel="stylesheet" th:href="@{${_CPR_}+'/plugin/layui/css/layui.css'}">
    <link rel="stylesheet" th:href="@{${_CPR_}+'/css/cultivation/reset.css'}">
    <link rel="stylesheet" th:href="@{${_CPR_}+'/css/cultivation/classCourse.css'}">
    <script th:src="@{${_CPR_}+'/plugin/layui/layui.js'}"></script>
</head>

<body>
<div class="main">
    <div class="m-top">
        <div class="back">返回</div>
        <div class="title">开课管理</div>
        <span></span>
        <h3>班课速配</h3>
        <div class="btns"><span>开班</span></div>
    </div>

    <div class="item">
        <div class="i-top">
            <span>筛选课程</span>
        </div>
        <div class="form-con">
            <form class="layui-form layui-form-course" action="" lay-filter="courseForm">
                <div class="layui-inline">
                    <label class="layui-form-label">课程名称</label>
                    <div class="layui-input-inline">
                        <div class="j-search-con single-box">
                            <input type="text" name="kck_kcmc" placeholder="请选择" readonly="" class="schoolSel">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">课程编号</label>
                    <div class="layui-input-inline">
                        <div class="j-search-con single-box">
                            <input type="text" name="kck_kcbh" placeholder="请选择" readonly="" class="schoolSel">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">开课系部</label>
                    <div class="layui-input-inline">
                        <div class="j-search-con single-box">
                            <input type="text" name="kck_kkyx" placeholder="请选择" readonly="" class="schoolSel">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">课程性质</label>
                    <div class="layui-input-inline">
                        <div class="j-search-con single-box">
                            <input type="text" name="kck_kcxz" placeholder="请选择" readonly="" class="schoolSel">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-inline" style="display: none;">
                        <button type="reset" class="layui-btn layui-btn-primary layui-btn-reset">重置</button>
                    </div>
                </div>
            </form>
            <div class="form-btn form-btn-course" style="display: flex;">
                <button class="btn btn-reset">重置</button>
                <button class="btn btn-search">筛选</button>

            </div>
        </div>
        <div class="course-table">
            <table class="layui-hide" id="courseTable" lay-filter="courseTable"></table>
        </div>
    </div>
</div>
<div class="main1">
    <div class="title">
        <h1><span>“筛选课程” </span>设置结果</h1>
        <div class="clear">清除</div>
    </div>
    <div class="course-table">
        <table class="layui-hide" lay-filter="courseTable1" id="courseTable1"></table>
    </div>
</div>


</body>
<script type="text/html" id="tmplToolBar">
    <div class="oprate-table">
        <div class="delet" lay-event="delet">删除</div>
    </div>
</script>
<script th:src="@{${_CPR_}+'/js/jquery-3.3.1.min.js'}"></script>
<script th:src="@{${_CPR_}+'/js/cultivation/select.js'}"></script>
<script>
    const _VR_ = "[[${_VR_}]]";
    const fid = "[[${clazzSpeedMatchVO.fid}]]";
    const uid = "[[${clazzSpeedMatchVO.uid}]]";
    const term = "[[${clazzSpeedMatchVO.term}]]";
    layui.use(['jquery', "form", "table"], function () {
        const $ = layui.jquery;
        const form = layui.form;
        const table = layui.table;
        let tabData = [];
        // 查询
        $(".btn-search").click(function () {
            const kck_kcmc = $("input[name='kck_kcmc']").val();
            const kck_kcbh = $("input[name='kck_kcbh']").val();
            const kck_kkyx = $("input[name='kck_kkyx']").val();
            const kck_kcxz = $("input[name='kck_kcxz']").val();
            const field = {courseName: kck_kcmc, courseNo: kck_kcbh, dept: kck_kkyx, courseNature: kck_kcxz};
            layui.table.reload("courseTable", {where: field, page: {curr: 1}});
        })
        // 重置
        $(".btn-reset").click(function () {
            $(".layui-btn-reset").click();
            const field = {courseName: "", courseNo: "", major: "", courseNature: ""};
            layui.table.reload("courseTable", {where: field, page: {curr: 1}});
        });
        // table
        table.render({
            elem: '#courseTable',
            url: _VR_ + '/pc/cultivation/clazzSpeedMatch/courseData',
            where: {
                fid: fid,
                clazzData: window.localStorage.getItem("clazzData"),
                term: window.localStorage.getItem("termData")
            },
            cols: [
                [{
                    type: 'checkbox',
                    width: 86,
                    fixed: 'left'
                }, {
                    field: 'kck_kcmc',
                    title: '课程名称',
                    align: "center",
                    minWidth: 190
                }, {
                    field: 'kck_kcbh',
                    title: '课程编号',
                    align: "center",
                    minWidth: 190
                }, {
                    field: 'kck_kkyx',
                    title: '开课系部',
                    align: "center",
                    minWidth: 190
                }, {
                    field: 'kck_kkjys',
                    title: '开课教研室',
                    align: "center",
                    minWidth: 190
                }, {
                    field: 'kck_sfqy',
                    title: '是否启用',
                    align: "center",
                    minWidth: 190
                }, {
                    field: 'kck_kcsx',
                    title: '选必修',
                    align: "center",
                    minWidth: 190
                }, {
                    field: 'kck_kcxz',
                    title: '课程性质',
                    align: "center",
                    minWidth: 190
                }, {
                    field: 'kck_zxs',
                    title: '总学时',
                    align: "center",
                    minWidth: 190,

                }, {
                    field: 'kck_xf',
                    title: '学分',
                    align: "center",
                    minWidth: 190,

                }]
            ],
            page: {
                layout: ['count', 'prev', 'page', 'next', 'limit', 'skip']
            },
            done: function (res, curr, count) {
                //选中了某一行，使不同管理员的行不可选
                tabData = res.data;
                res.data.forEach((data, i) => {
                    const match = selTable.some(item => item.kck_kcbh === data.kck_kcbh);
                    if (match) {
                        tabData[i].isSel = true;
                        disabledRow(i);
                    } else {
                        abledRow(i);
                    }
                });
                getFormDistinctFiled();
            }
        });
        // 获取table选择的数据
        const selectData = table.checkStatus('courseTable').data;
        // 获取table选择的数据
        let selTable = window.localStorage.getItem("courseData") ? JSON.parse(window.localStorage.getItem("courseData")) : [];
        table.render({
            elem: '#courseTable1',
            data: selTable,
            cols: [
                [{
                    field: 'kck_kcmc',
                    title: '课程名称',
                    align: "center",
                    minWidth: 190
                }, {
                    field: 'kck_kcbh',
                    title: '课程编号',
                    minWidth: 190,
                    align: "center",
                }, {
                    field: 'kck_kkyx',
                    title: '开课系部',
                    minWidth: 190,
                    align: "center",
                }, {
                    field: 'kck_kcxz',
                    title: '课程性质',
                    align: "center",
                    minWidth: 190
                }, {
                    field: 'kck_kcsx',
                    title: '选必修',
                    align: "center",
                    minWidth: 190
                }, {
                    title: '操作',
                    align: "center",
                    minWidth: 190, toolbar: "#tmplToolBar",
                    fixed: "right"

                }]
            ],
            page: {
                layout: ['count', 'prev', 'page', 'next', 'limit', 'skip']
            }
        });
        // 删除
        table.on('tool(courseTable1)', function (obj) {
            const data = obj.data;
            if (obj.event === 'delet') {
                layer.confirm('确定删除吗？', function (index) {
                    obj.del();
                    const idx1 = selTable.findIndex(item => item.id == data.id);
                    selTable.splice(idx1, 1);
                    table.reload('courseTable1', {data: selTable, page: {curr: 1}});
                    layer.close(index);
                    //如果勾选为0，则重置
                    const idx = tabData.findIndex(item => item.id == data.id);
                    tabData[idx].isSel = false;
                    abledRow(idx);
                    window.localStorage.setItem("courseData", JSON.stringify(selTable));
                });
            }
        });
        // 选择
        //监听表格复选框
        table.on('checkbox(courseTable)', function (obj) {
            let tableArray = layui.table.cache['courseTable'];
            if (obj.type === "all") {
                if (obj.checked && !$(this).hasClass("layui-btn-disabled")) {
                    //选中了某一行，使不同管理员的行不可选
                    for (let i = 0; i < tableArray.length; i++) {
                        if (!tableArray[i].isSel) {
                            tableArray[i].isSel = true;
                            selTable.push(tableArray[i]);
                            disabledRow(i);
                        }
                    }
                    table.reload('courseTable1', {data: selTable})
                    // let t = $(obj.tr).find('input[type=checkbox]');
                    $(this).prop('disabled', true);
                    $(this).addClass('layui-btn-disabled');
                    $(this).next().css("cursor", "not-allowed");
                }
            } else {
                if (obj.checked && !$(this).hasClass("layui-btn-disabled")) {
                    //选中了某一行，使不同管理员的行不可选
                    if (!obj.data.isSel) {
                        obj.data.isSel = true;
                        obj.update(obj.data);
                        selTable.push(obj.data);
                        disabledRow($(obj.tr).index());
                        table.reload('courseTable1', {data: selTable});
                        let th = $("div[lay-id=courseTable] .layui-table-header input[type='checkbox']")
                        let thCheck = th.prop('checked');
                        if (thCheck) {
                            th.prop('disabled', true);
                            th.addClass('layui-btn-disabled');
                            th.next().css("cursor", "not-allowed");
                        }
                    }

                }
            }
        })

        function abledRow(index) {
            //取消行不可选
            let t = $("div[lay-id=courseTable] .layui-table tr[data-index=" + index + "] input[type='checkbox']");
            $("div[lay-id=courseTable] th[data-field='0'] input[type='checkbox']").prop('disabled', false).prop('checked', false).removeClass('layui-btn-disabled');
            t.prop('disabled', false);
            t.prop('checked', false);
            t.removeClass('layui-btn-disabled');
            t.next().css("cursor", "unset").removeClass('layui-form-checked');
            $("div[lay-id=courseTable] .layui-table tr[data-index=" + index + "]").css("background-color", "");
            $("div[lay-id=courseTable] .layui-table tr[data-index=" + index + "]").css("color", "#666");

            let th = $("div[lay-id=courseTable] .layui-table-header input[type='checkbox']");
            th.prop('disabled', false);
            th.prop('checked', false);
            th.removeClass('layui-btn-disabled');
            th.next().css("cursor", "unset").removeClass('layui-form-checked');
        }

        function disabledRow(index) {
            //第index行复选框不可选
            let t = $("div[lay-id=courseTable] .layui-table tr[data-index=" + index + "] input[type='checkbox']");
            $("div[lay-id=courseTable] th[data-field='0'] input[type='checkbox']").prop('disabled', true).addClass('layui-btn-disabled');
            t.prop('disabled', true);
            t.addClass('layui-btn-disabled');
            t.next().css("cursor", "not-allowed");
            //置灰
            $("div[lay-id=courseTable] .layui-table tr[data-index=" + index + "]").css("background-color", "#E0E0E0");
            $(" div[lay-id=courseTable] .layui-table tr[data-index=" + index + "]").css("color", "#9f9696");
        }

        // 清除
        $(".clear").click(function () {
            table.reload('courseTable1', {data: []});
            tabData.map(item => {
                item.isSel = false
            })
            selTable = [];
            table.reload('courseTable');
            window.localStorage.removeItem("courseData");
        })

        function getFormDistinctFiled() {
            const fieldAliasArray = ["kck_kcmc", "kck_kcbh", "kck_kkyx", "kck_kcxz"];
            fieldAliasArray.forEach(fieldAlias => {
                $.post(_VR_ + `/teacherIdle/getFormDistinctFiled`, {
                    formAlias: "kck",
                    fieldAlias,
                    fid: fid
                }, function (data) {
                    if (data.list) {
                        let inputElement = $(`input[name="${fieldAlias}"]`).parent().find("ul");
                        data.list.forEach(item => {
                            inputElement.append("<li>" + item + "</li>");
                        });
                    }
                }, 'json');
            });
        }

        $(".back").click(function () {
            location.href = "/pc/cultivation/clazzSpeedMatch/clazzIndex?fid=" + fid + "&uid=" + uid+"&clear=true";
            window.localStorage.setItem("courseData", JSON.stringify(selTable));
        })

        $(".btns span").click(function () {
            let clazzData = window.localStorage.getItem("clazzData") ? JSON.parse(window.localStorage.getItem("clazzData")) : [];
            if (selTable.length === 0) {
                layer.msg("请选择课程", {icon: 2, time: 2000});
                return false;
            }
            const clazzArray = clazzData.map(item => ({
                abkk_bjmc: item.bjxx_bjmc,
                abkk_bjbh: item.bjxx_bjbh,
                abkk_nj: item.bjxx_rxnf,
                abkk_ssxb: item.bjxx_ssyx,
                abkk_zy: item.bjxx_zy,
                abkk_zybh: item.bjxx_zybh,
                abkk_bzr: item.bjxx_bzr,
                abkk_bjrs: item.bjxx_njrs,
                abkk_gdjs: item.bjxx_gdjs
            }));
            const courseArray = selTable.map(item => ({
                abkk_kcmc: item.kck_kcmc,
                abkk_kcbh: item.kck_kcbh,
                abkk_kkxb: item.kck_kkyx,
                abkk_kkjys: item.kck_kkjys,
                abkk_xbx: item.kck_kcsx,
                abkk_kcxz: item.kck_kcxz,
                abkk_ksfs: item.kck_ksfs,
                abkk_xf: item.kck_xf,
                abkk_mzxs: item.kck_mzxs
            }));
            $.post(_VR_ + `/pc/cultivation/clazzSpeedMatch/openClazz`, {
                clazzData: JSON.stringify(clazzArray),
                courseData: JSON.stringify(courseArray),
                fid: fid,
                uid: uid,
                term: term
            }, function (data) {
                layer.msg("开班成功", {icon: 1, time: 2000});
                localStorage.clear();
            }, 'json');
        })
    })

</script>

</html>