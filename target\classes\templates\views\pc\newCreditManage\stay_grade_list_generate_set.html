<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>留级名单生成设置</title>
        <link rel="stylesheet" th:href="@{~/css/creditManage/global.css}">
        <link rel="stylesheet" th:href="@{~/plugin/layui/css/layui.css}">
        <link rel="stylesheet" th:href="@{~/css/creditManage/common.css}">
        <link rel="stylesheet" th:href="@{~/css/creditManage/reset.css}">
        <link rel="stylesheet" th:href="@{~/css/creditManage/scoreRule.css}">
        <link rel="stylesheet" th:href="@{~/css/creditManage/scoreSet.css}">
        <style>
            /*  由于和其他页面的css文件样式有冲突，所以此处单独定义css样式  */
            .layui-form-checkbox[lay-skin=primary] span {
                color: #86909c;
                display: block;
                font-size: 14px;
                padding-left: 0;
                padding-right: 15px;
                line-height: 18px;
                background: none;
            }
            /* 产品样式优化 */
            body {
                padding: 0;
            }
            .container {
                padding: 0 30px;
                margin: 0;
            }
            .top {
                border-bottom: 1px solid #E8EBF1;
                margin-bottom: 20px;
            }
            .con-title {
                font-size: 16px;
                color: #1d2129;
                margin: 0;
                font-weight: 700;
            }
            .btn {
                position: absolute;
                top: 12px;
                right: 30px;
                width: 116px;
                height: 36px;
                text-align: center;
                line-height: 36px;
                cursor: pointer;
                font-size: 14px;
                color: #FFFFFF;
                background: #4D88FF;
                box-shadow: 0 0 10px #4D88FF;
                border-radius: 4px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="top">
                <div class="tab">
                    <ul>
                        <li class="cur con-title">留级名单生成设置</li>
                    </ul>
                </div>
                <div class="btn" lay-submit lay-filter="saveSetting">保存设置</div>
            </div>
<!--            <div>-->
<!--                <div class="con-title">留级名单生成设置</div>-->
<!--                <div class="btn" lay-submit lay-filter="saveSetting">保存设置</div>-->
<!--            </div>-->
            <form class="layui-form set-form" action="">
                <div class="layui-form-item">
                    <input type="hidden" id="stayGradeListGenerateSetId">
                    <label class="layui-form-label" style="width: 119px;">留级学年学期</label>
                    <div class="layui-input-block">
                        <input type="text" name="title" disabled required lay-verify="required" value="2022-2023-02"
                               style="color: #86909C;cursor:not-allowed" placeholder="请输入" autocomplete="off"
                               class="layui-input" id="stayGradeTerm">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="width: 119px;">年级范围</label>
                    <div class="layui-input-block">
                        <div class="j-search-con multiple-box">
                            <input type="text" placeholder="请选择" readonly class="schoolSel" id="gradeRange">
                            <span class="j-arrow"></span>
                            <div class="j-select-year ">
                                <div class="allSelect">
                                    全选
                                </div>
                                <ul id="gradeRangeUl">
                                    <li>2020</li>
                                    <li>2019</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="width: 119px;">学生范围</label>
                    <div class="layui-input-block">
                        <input type="checkbox" lay-skin="primary" name="inSchool" title="在校" id="inSchool">
                        <input type="checkbox" lay-skin="primary" name="outSchool" title="不在校" id="outSchool">
                    </div>
                </div>
                <div class="j-material-lable">
                    <div class="add-oprate">
                        <h5>生成规则设置</h5>
                        <div class="add add">添加</div>
                    </div>
                    <div class="j-table" style="margin-bottom:0;">
                        <div class="j-head">
                            <div class="j-th">学年学期</div>
                            <div class="j-th">培养层次</div>
                            <div class="j-th">判定条件</div>
                            <div class="j-th">操作</div>
                        </div>
                        <div class="j-body">
                            <!--                            <div class="j-tr">-->
                            <!--                                <div class="j-td">-->
                            <!--                                    <div class="oprate">-->
                            <!--                                        <div class="select-input score-sel">-->
                            <!--                                            <div class="name termBegin">请选择学期</div>-->
                            <!--                                            <em></em>-->
                            <!--                                            <div class="select-dropdown ">-->
                            <!--                                                <ul class="dropdown-list">-->
                            <!--                                                    <li>2022-2023-1</li>-->
                            <!--                                                    <li>2022-2023-2</li>-->
                            <!--                                                    <li>2021-2022-1</li>-->
                            <!--                                                    <li>2021-2022-2</li>-->
                            <!--                                                </ul>-->
                            <!--                                            </div>-->
                            <!--                                        </div>-->
                            <!--                                        <div class="splite">-</div>-->
                            <!--                                        <div class="select-input score-sel">-->
                            <!--                                            <div class="name termEnd">请选择学期</div>-->
                            <!--                                            <em></em>-->
                            <!--                                            <div class="select-dropdown ">-->
                            <!--                                                <ul class="dropdown-list">-->
                            <!--                                                    <li>2022-2023-1</li>-->
                            <!--                                                    <li>2022-2023-2</li>-->
                            <!--                                                    <li>2021-2022-1</li>-->
                            <!--                                                    <li>2021-2022-2</li>-->
                            <!--                                                </ul>-->
                            <!--                                            </div>-->
                            <!--                                        </div>-->

                            <!--                                    </div>-->
                            <!--                                </div>-->
                            <!--                                <div class="j-td">-->
                            <!--                                    <div class="oprate">-->
                            <!--                                        <div class="select-input score-sel">-->
                            <!--                                            <div class="name">请选择</div>-->
                            <!--                                            <em></em>-->
                            <!--                                            <div class="select-dropdown ">-->
                            <!--                                                <ul class="dropdown-list">-->
                            <!--                                                    <li>中职</li>-->
                            <!--                                                    <li>大学</li>-->
                            <!--                                                </ul>-->
                            <!--                                            </div>-->
                            <!--                                        </div>-->
                            <!--                                    </div>-->

                            <!--                                </div>-->
                            <!--                                <div class="j-td">-->
                            <!--                                    <div class="oprate">-->
                            <!--                                        <div class="select-input score-sel">-->
                            <!--                                            <div class="name">请选择</div>-->
                            <!--                                            <em></em>-->
                            <!--                                            <div class="select-dropdown ">-->
                            <!--                                                <ul class="dropdown-list">-->
                            <!--                                                    <li>所获得学分数</li>-->
                            <!--                                                    <li>补考不及格门数</li>-->
                            <!--                                                </ul>-->
                            <!--                                            </div>-->
                            <!--                                        </div>-->
                            <!--                                        <div class="inputs right-inp">-->
                            <!--                                            <div class="select-input">-->
                            <!--                                                <div class="name">≥</div>-->
                            <!--                                                <em></em>-->
                            <!--                                                <div class="select-dropdown ">-->
                            <!--                                                    <ul class="dropdown-list">-->
                            <!--                                                        <li>=</li>-->
                            <!--                                                        <li>></li>-->
                            <!--                                                        <li class="cur">≥</li>-->
                            <!--                                                        <li>-->
                            <!--                                                            <-->
                            <!--                                                        </li>-->
                            <!--                                                        <li>≤-->
                            <!--                                                        </li>-->
                            <!--                                                    </ul>-->
                            <!--                                                </div>-->
                            <!--                                            </div>-->
                            <!--                                            <input class="input layui-input" type="number" placeholder="请输入">-->
                            <!--                                            <div class="error">请重新输入</div>-->
                            <!--                                        </div>-->
                            <!--                                    </div>-->
                            <!--                                </div>-->
                            <!--                                <div class="j-td">-->
                            <!--                                    <div class="oprate">-->
                            <!--                                        <span class="preservation">删除</span>-->
                            <!--                                    </div>-->
                            <!--                                </div>-->
                            <!--                            </div>-->
                        </div>
                    </div>
                </div>
            </form>
<!--            <div class="save-set">-->
<!--                <button type="submit" class="layui-btn btn-submit" lay-submit lay-filter="saveSetting">保存设置</button>-->
<!--            </div>-->
        </div>
    </body>
    <script th:src="@{~/js/jquery-3.3.1.min.js}"></script>
    <script th:src="@{~/js/creditManage/common.js}"></script>
    <script th:src="@{~/plugin/layui/layui.js}"></script>
    <script>

        // 学年学期号列表
        var termList = [];
        // 培养层次列表
        var levelNameList = [];

        // 初始化页面
        layui.use(['jquery', 'laydate', "form"], function () {
            var form = layui.form;
            var $ = layui.jquery;

            // 初始化页面的数据
            initPageData();

            // 点击[保存设置]按钮触发提交
            form.on('submit(saveSetting)', function (data) {
                var id = $("#stayGradeListGenerateSetId").val();
                var stayGradeTerm = $("#stayGradeTerm").val();
                var gradeRange = $("#gradeRange").val();
                var studentRange = "";
                if ($("#inSchool").next("div").hasClass("layui-form-checked")) {
                    studentRange += "在校";
                }
                if ($("#outSchool").next("div").hasClass("layui-form-checked")) {
                    if (studentRange === "") {
                        studentRange += "不在校";
                    } else {
                        studentRange += ",不在校";
                    }
                }
                if (!gradeRange) {
                    layer.msg("请选择年级范围", {icon: 2, time: 2000});
                    return false;
                } else if (!studentRange) {
                    layer.msg("请选择学生范围", {icon: 2, time: 2000});
                    return false;
                }
                // 封装生成规则设置
                var ruleSetList = [];
                var subRuleEleList = $(".j-material-lable .j-table .j-body .j-tr");
                if (!subRuleEleList || subRuleEleList.length === 0) {
                    layer.msg("未设置留级名单生成规则，请设置后再保存", {icon: 2, time: 2000});
                    return false;
                } else {
                    for (var i = 0; i < subRuleEleList.length; i++) {
                        var subRule = subRuleEleList[i];
                        var subId = $(subRule).find(".subRuleSetId").val();
                        var termBegin = $(subRule).find(".termBegin").text();
                        var termEnd = $(subRule).find(".termEnd").text();
                        var levelName = $(subRule).find(".levelName").text();
                        var judgeConditionType = $(subRule).find(".judgeConditionType").text();
                        var judgeConditionMark = $(subRule).find(".judgeConditionMark").text();
                        var judgeConditionCount = $(subRule).find(".judgeConditionCount").val();
                        if (termBegin === "请选择学期" || termEnd === "请选择学期") {
                            layer.msg("请选择第" + (i + 1) + "条生成规则设置的学年学期", {icon: 2, time: 2000});
                            return false;
                        } else {
                            var termBeginNum = parseInt(termBegin.replaceAll("-", ""));
                            var termEndNum = parseInt(termEnd.replaceAll("-", ""));
                            if (termBeginNum > termEndNum) {
                                layer.msg("第" + (i + 1) + "条生成规则设置的学年学期范围选择错误", {icon: 2, time: 2000});
                                return false;
                            }
                        }
                        if (!levelName || levelName === "请选择") {
                            layer.msg("请选择第" + (i + 1) + "条生成规则设置的培养层次", {icon: 2, time: 2000});
                            return false;
                        } else if (!judgeConditionType || judgeConditionType === "请选择") {
                            layer.msg("请选择第" + (i + 1) + "条生成规则设置的判定条件类型", {icon: 2, time: 2000});
                            return false;
                        } else if (!judgeConditionMark) {
                            layer.msg("请选择第" + (i + 1) + "条生成规则设置的判定条件符号", {icon: 2, time: 2000});
                            return false;
                        } else if (!judgeConditionCount || judgeConditionCount <= 0) {
                            layer.msg("请正确填写第" + (i + 1) + "条生成规则设置的判定条件数量：大于0", {icon: 2, time: 3000});
                            return false;
                        } else if ((judgeConditionCount + "").indexOf(".") !== -1) {
                            layer.msg("请正确填写第" + (i + 1) + "条生成规则设置的判定条件数量：判定条件限输入整数，请修改后保存", {icon: 2, time: 3000});
                            return false;
                        }
                        var ruleSet = {
                            "termBegin": termBegin,
                            "termEnd": termEnd,
                            "levelName": levelName,
                            "judgeConditionType": judgeConditionType === "所获得学分数" ? 1 : (judgeConditionType === "补考不及格门数" ? 2 : 0),
                            "judgeConditionMark": judgeConditionMark,
                            "judgeConditionCount": judgeConditionCount,
                        };
                        if (subId) {
                            ruleSet.id = subId;
                        }
                        ruleSetList.push(ruleSet);
                    }
                }
                // 封装传递的表单数据
                var formData = {
                    "stayGradeTerm": stayGradeTerm,
                    "gradeRange": gradeRange,
                    "studentRange": studentRange,
                    "ruleSetList": ruleSetList
                };
                if (id) {
                    formData.id = id;
                }
                // 提交保存留级名单生成设置
                $.ajax({
                    type: "post",
                    url: "/new/credit/stay/grade/list/generate/set/save",
                    data: JSON.stringify(formData),
                    dataType: 'json',
                    contentType: "application/json",
                    success: function (result) {
                        if (result) {
                            if (result.code === 200) {
                                layer.msg("保存成功", {icon: 1, time: 2000});
                                // 保存页面后，刷新页面
                                initPageData();
                            } else {
                                layer.msg("保存留级名单生成设置失败", {icon: 2, time: 2000});
                            }
                        }
                    }
                });
                // 阻止表单跳转。如果需要表单跳转，去掉这段即可。
                return false;
            });
        });

        // 下拉
        $(".j-material-lable ").on("click", ".j-table .j-body .j-tr .j-td .select-input .name", function (e) {
            $(this).parent().toggleClass("clicked");
            stopBubble(e);
        });

        $(".j-material-lable").on("click", " .j-table .j-body .j-tr .j-td .select-input .select-dropdown .dropdown-list li", function (e) {
            $(this).addClass("cur").siblings().removeClass("cur");
            let kosl = '';
            kosl = $(this).text();
            if (kosl === '为空') {
                kosl = '';
            }
            $(this).parents(".select-input").find(".name").addClass("ckd");
            $(this).parents(".select-input").find(".name").text(kosl);
            $(this).parents(".select-input").removeClass("clicked");
        });

        function stopBubble(e) {
            if (e && e.stopPropagation)
                e.stopPropagation();
            else {
                window.event.cancelBubble = true;
            }
        }

        $(document).on("click", function (event) {
            var _con = $('.select-input');
            if (!_con.is(event.target) && _con.has(event.target).length === 0) {
                $(".select-input").removeClass("clicked");
            }
        });

        // 删除生成规则设置
        $(".j-material-lable").on("click", " .j-table .j-body .j-tr .j-td .preservation", function () {
            $(this).parents(".j-tr").remove();
        });

        // 添加生成规则设置
        $(".j-material-lable").on("click", " .add-oprate .add", function () {
            var ktml = getSubRuleSetHtml(termList, levelNameList);
            $(this).parent(".add-oprate").next().find(".j-body").append(ktml);
        });

        /**
         * 返回一个封装好的[生成规则设置]项，填充了学年学期和培养层次
         * @param termList 学年学期号数据列表
         * @param levelNameList 培养层次数据列表
         * @param subRuleSet 需要回显的数据实体
         * @returns {string} 封装好的[生成规则设置]项html
         */
        function getSubRuleSetHtml(termList, levelNameList, subRuleSet) {
            var termLiList = "";
            if (termList && termList.length !== 0) {
                for (var i = 0; i < termList.length; i++) {
                    termLiList += ("<li >" + termList[i].num + "</li>");
                }
            }
            var levelNameLiList = "";
            if (levelNameList && levelNameList.length !== 0) {
                for (var i = 0; i < levelNameList.length; i++) {
                    levelNameLiList += ("<li >" + levelNameList[i] + "</li>");
                }
            }
            var judgeConditionMarkLiList = "";
            var markList = ["=", ">", "≥", "<", "≤"];
            for (var i = 0; i < markList.length; i++) {
                var mark = markList[i];
                if (subRuleSet && subRuleSet.judgeConditionMark && subRuleSet.judgeConditionMark === mark) {
                    judgeConditionMarkLiList += "<li class='cur'>" + mark + "</li>";
                } else {
                    if ("≥" === mark) {
                        judgeConditionMarkLiList += "<li class='cur'>" + mark + "</li>";
                    } else {
                        judgeConditionMarkLiList += "<li>" + mark + "</li>";
                    }
                }
            }
            var termBegin = (subRuleSet && subRuleSet.termBegin) ? subRuleSet.termBegin : "请选择学期";
            var termEnd = (subRuleSet && subRuleSet.termEnd) ? subRuleSet.termEnd : "请选择学期";
            var levelName = (subRuleSet && subRuleSet.levelName) ? subRuleSet.levelName : "请选择";
            var judgeConditionType = (subRuleSet && subRuleSet.judgeConditionType) ?
                (subRuleSet.judgeConditionType === 1 ? "所获得学分数" : (subRuleSet.judgeConditionType === 2 ? "补考不及格门数" : "错误类型"))
                : "请选择";
            var judgeConditionMark = (subRuleSet && subRuleSet.judgeConditionMark) ? subRuleSet.judgeConditionMark : "≥";
            var judgeConditionCount = (subRuleSet && subRuleSet.judgeConditionCount !== undefined) ? subRuleSet.judgeConditionCount : "";
            return ' <div class="j-tr">' +
                '<div div class="j-td" >' +
                '<div class="oprate">' +
                ' <div class="select-input score-sel">' +
                '<div class="name termBegin' + ((subRuleSet && subRuleSet.termBegin) ? " ckd" : "") + '">' + termBegin + '</div>' +
                '<em></em>' +
                '<div class="select-dropdown ">' +
                '<input type="hidden" class="subRuleSetId" value="' + (subRuleSet ? (subRuleSet.id ? subRuleSet.id : "") : "") + '">' +
                ' <ul class="dropdown-list">' +
                termLiList +
                ' </ul>' +
                '</div>' +
                '</div>' +
                ' <div class="splite">-</div>' +
                '<div class="select-input score-sel">' +
                ' <div class="name termEnd' + ((subRuleSet && subRuleSet.termEnd) ? " ckd" : "") + '">' + termEnd + '</div>' +
                ' <em></em>' +
                ' <div class="select-dropdown ">' +
                '<ul class="dropdown-list">' +
                termLiList +
                '</ul>' +
                ' </div>' +
                ' </div>' +
                ' </div> ' +
                ' </div > ' +
                '<div class="j-td"> ' +
                '<div class="oprate"> ' +
                '<div class="select-input score-sel"> ' +
                ' <div class="name levelName' + ((subRuleSet && subRuleSet.levelName) ? " ckd" : "") + '">' + levelName + '</div> ' +
                '<em></em> ' +
                '<div class="select-dropdown "> ' +
                ' <ul class="dropdown-list"> ' +
                levelNameLiList +
                '</ul>' +
                ' </div>' +
                '</div>' +
                ' </div>' +
                ' </div>' +
                '<div class="j-td">' +
                ' <div class="oprate">' +
                ' <div class="select-input score-sel">' +
                ' <div class="name judgeConditionType' + ((subRuleSet && subRuleSet.judgeConditionType) ? " ckd" : "") + '">' + judgeConditionType + '</div>' +
                ' <em></em>' +
                ' <div class="select-dropdown ">' +
                '<ul class="dropdown-list">' +
                ' <li >所获得学分数</li>' +
                '  <li>补考不及格门数</li>' +
                ' </ul>' +
                '</div>' +
                ' </div>' +
                '<div class="inputs right-inp">' +
                ' <div class="select-input">' +
                ' <div class="name judgeConditionMark' + ((subRuleSet && subRuleSet.judgeConditionMark) ? " ckd" : "") + '">' + judgeConditionMark + '</div>' +
                ' <em></em>' +
                '<div class="select-dropdown ">' +
                '  <ul class="dropdown-list">' +
                judgeConditionMarkLiList +
                '</ul>' +
                '</div>' +
                ' </div>' +
                '<input class="input layui-input judgeConditionCount" type="number" placeholder="请输入整数" value="' + (judgeConditionCount) + '">' +
                '<div class="error">请重新输入</div>' +
                '</div>' +
                '</div> ' +
                ' </div> ' +
                '<div class="j-td"> ' +
                '<div class="oprate"> ' +
                ' <span class="preservation">删除</span> ' +
                '</div> ' +
                '</div> ' +
                '</div >  ';
        }

        /**
         * 初始化页面数据
         */
        function initPageData() {
            var stayGradeGenerateSet = {};
            // 先同步获取留级名单生成设置的数据
            $.ajax({
                type: "get",
                url: "/new/credit/stay/grade/list/generate/set/detail",
                data: {},
                async: false,
                success: function (result) {
                    if (result) {
                        if (result.code === 200) {
                            stayGradeGenerateSet = result.data;
                        } else {
                            layer.msg("获取留级名单生成设置失败", {icon: 2, time: 2000});
                        }
                    }
                }
            });
            if (stayGradeGenerateSet && stayGradeGenerateSet.id) {
                $("#stayGradeListGenerateSetId").val(stayGradeGenerateSet.id);
            }
            // 获取当前学年学期
            $.ajax({
                type: "get",
                url: "/new/credit/stay/grade/current/term",
                data: {},
                success: function (result) {
                    if (result) {
                        if (result.code === 200) {
                            $("#stayGradeTerm").val(result.data);
                        }
                    }
                }
            });
            // 获取年级范围下拉框数据
            $.ajax({
                type: "get",
                url: "/new/credit/stay/grade/enabled/grade/list",
                data: {},
                success: function (result) {
                    if (result) {
                        if (result.code === 200) {
                            $("#gradeRangeUl").text("");
                            var gradeList = result.data;
                            if (gradeList && gradeList.length !== 0) {
                                var gradeSelectList = [];
                                if (stayGradeGenerateSet && stayGradeGenerateSet.gradeRange) {
                                    gradeSelectList = stayGradeGenerateSet.gradeRange.split(',');
                                    $("#gradeRange").val(stayGradeGenerateSet.gradeRange);
                                } else {
                                    // 默认选中全部年级
                                    $("#gradeRange").val(gradeList.join(","));
                                }
                                for (var i = 0; i < gradeList.length; i++) {
                                    var grade = gradeList[i];
                                    if (gradeSelectList && gradeSelectList.length > 0) {
                                        if (gradeSelectList.indexOf(grade) !== -1) {
                                            $("#gradeRangeUl").append("<li class='active'>" + grade + "</li>");
                                        } else {
                                            $("#gradeRangeUl").append("<li>" + grade + "</li>");
                                        }
                                    } else {
                                        $("#gradeRangeUl").append("<li class='active'>" + grade + "</li>");
                                    }
                                }
                            }
                        }
                    }
                }
            });
            // 回显学生范围复选框
            if (stayGradeGenerateSet && stayGradeGenerateSet.studentRange) {
                var studentRangeList = stayGradeGenerateSet.studentRange.split(",");
                if (studentRangeList.indexOf("在校") !== -1) {
                    $("#inSchool").attr("checked", "checked");
                    $("#inSchool").next("div").addClass("layui-form-checked");
                }
                if (studentRangeList.indexOf("不在校") !== -1) {
                    $("#outSchool").attr("checked", "checked");
                    $("#outSchool").next("div").addClass("layui-form-checked");
                }
            } else {
                // 默认选中在校
                $("#inSchool").attr("checked", "checked");
                $("#inSchool").next("div").addClass("layui-form-checked");
            }
            // 获取学年学期号下拉框数据
            $.ajax({
                type: "get",
                url: "/new/credit/stay/grade/semester/list",
                data: {},
                async: false,
                success: function (result) {
                    if (result) {
                        if (result.code === 200) {
                            termList = result.data;
                        }
                    }
                }
            });
            // 获取培养层次下拉框数据
            $.ajax({
                type: "get",
                url: "/new/credit/stay/grade/level/list",
                data: {},
                async: false,
                success: function (result) {
                    if (result) {
                        if (result.code === 200) {
                            levelNameList = result.data;
                        }
                    }
                }
            });
            // 回显生成规则设置
            $(".add-oprate").next().find(".j-body").text("");
            if (stayGradeGenerateSet && stayGradeGenerateSet.generateRuleSetList && stayGradeGenerateSet.generateRuleSetList.length !== 0) {
                for (var i = 0; i < stayGradeGenerateSet.generateRuleSetList.length; i++) {
                    var subRuleSet = stayGradeGenerateSet.generateRuleSetList[i];
                    var ktml = getSubRuleSetHtml(termList, levelNameList, subRuleSet);
                    $(".add-oprate").next().find(".j-body").append(ktml);
                }
            } else {
                var ktml = getSubRuleSetHtml(termList, levelNameList, subRuleSet);
                $(".add-oprate").next().find(".j-body").append(ktml);
            }
        }

    </script>
</html>