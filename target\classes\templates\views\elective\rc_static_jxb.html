<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no,initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <title>[[${jxbmc}]]</title>
    <script src="js/responsive.js"></script>
    <link rel="stylesheet" href="css/global.css">
    <link rel="stylesheet" href="css/iosSelect.css">
    <link rel="stylesheet" href="css/commonV1.css">
    <link rel="stylesheet" href="css/staV1.css?v=1">
</head>

<body>
<div class="search-wrap" style="height: auto">
    <!--    <div class="current-state">-->
    <!--        <div class="time">-->
    <!--            <span>当前时间：</span>-->
    <!--            <em id="dateTime">[[${courseTime}]]</em>-->
    <!--        </div>-->
    <!--        <div class="btn selDate" style="display: none">-->
    <!--            <span>切换时间</span>-->
    <!--        </div>-->
    <!--    </div>-->
    <div class="search flex">
        <img src="images/search.png" alt="">
        <input type="text" placeholder="搜索学生姓名或学号" id="keyword">
    </div>
</div>
<div class="stu-list-wrap">
    <div class="class-mes">
        <ul>
<!--            <li id="zc">正常:<i>0</i></li>-->
<!--            <li id="cd">迟到:<i>0</i></li>-->
<!--            <li id="kk">旷课:<i>0</i></li>-->
<!--            <li id="qj">请假:<i>0</i></li>-->
<!--            <li id="zt">早退:<i>0</i></li>-->
        </ul>
    </div>
    <div class="total">
        <span>共<i>0</i>名学生</span>
    </div>
    <div class="class-list">
    </div>
</div>


</body>
<script src="js/CXJSBridge.js"></script>
<script src="js/iosSelect.js"></script>
<script src="js/zepto.min.js"></script>
<script th:inline="javascript">
    var jxbmc = [[${jxbmc}]];
    var jxbCode = [[${jxbCode}]];
    var courseTime = [[${courseTime}]];
    var fid = [[${fid}]];
    var stateNum = [[${stateNum}]];
    var rollcallStateList = [[${rollcallStateList}]];
    var statehtml = "<li>正常:<i>" + stateNum[0] + "</i></li>";
    for (let i = 0; i < rollcallStateList.length; i++) {
        var index = (parseInt(i) + 1);
        statehtml += "<li>" + rollcallStateList[i].stateNamePc + ":<i>" + stateNum[index] + "</i></li>";
    }
    $(".class-mes ul").html(statehtml);
    var search = false;
    $('#keyword').on({
        'compositionstart': function () {
            search = true;
        },
        'compositionend': function () {
            search = false;
            if (!search) {
                getList();
            }
        },
        'input propertychange': function () {
            if (!search) {
                getList();
            }
        }
    });

    function _jsBridgeReady() {

        // jsBridge.postNotification('CLIENT_CUSTOM_MENU', {
        //     show: 1,
        //     icon: 'https://p.ananas.chaoxing.com/star3/origin/39834c7ca00ad3b5885a12722b25ee8b.png',
        //     height: 24,
        //     width: 24,
        //     menu: '',
        //     children: children
        // });
    }


    $(document).ready(function () {
        var stuH = $(window).height() - $(".stu-list-wrap").offset().top;
        $(".stu-list-wrap").css({height: stuH + "px"});
    })

    getList(true)

    function getList(first) {
        var keyword = $("#keyword").val();
        // var classDate = $("#dateTime").text();
        $.ajax({
            type: "POST",
            url: "/elective/rollcall/getJxbStaticStudents",
            data: {jxbCode: jxbCode, keyword: keyword, courseTime: courseTime},
            dataType: 'json',
            async: false,
            success: function (data) {
                var html = "";
                for (var i = 0; i < data.data.length; i++) {
                    html += "<div class='stu' uuuid = '" + data.data[i].studentUid + "' uuxm = '" + data.data[i].studentRealname + "'>";
                    html += "<div class='order'>" + (i + 1) + "</div>";
                    html += "<h1><span>" + data.data[i].studentRealname + "</span><em>" + data.data[i].studentCodeDesensitize + "</em></h1>";
                    html += "<ul>";
                    html += "<li>正常:<i>" + data.data[i].zcNum + "</i></li>";
                    for (let j = 0; j < rollcallStateList.length; j++) {
                        html += "<li>"+rollcallStateList[j].stateNamePc+":<i>" + data.data[i].stateNum[j] + "</i></li>";
                    }
                    html += "</ul>";
                    html += "</div>";
                }
                if (first) {
                    $(".total").find("i").html(data.data.length);
                }
                $(".class-list").html(html);
            }
        });
    }


</script>
</html>