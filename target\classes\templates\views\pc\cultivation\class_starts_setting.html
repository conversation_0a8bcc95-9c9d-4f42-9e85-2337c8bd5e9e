<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>开课设置</title>
    <link rel="stylesheet" th:href="@{../css/cultivation/global.css}">
    <link rel="stylesheet" th:href="@{../plugin/layui/css/layui.css}">
    <link rel="stylesheet" th:href="@{../css/cultivation/set.css}">
    <script th:src="@{../plugin/layui/layui.js}"></script>
</head>

<body>
    <div class="main">
        <div class="m-top">
            <div class="back"></div>
            <div class="title">开课</div>
            <span></span>
            <h3>开课设置</h3>
        </div>
        <!--开课设置 -->
        <div class="item item-set">
            <div class="i-top">
                <span>开课设置</span>
            </div>
            <div class="set-con">
                <form class="layui-form" action="">
                    <div class="layui-form-item" style="margin-bottom: 10px;">
                        <div class="layui-inline">
                            <label class="layui-form-label">教学班编号</label>
                            <div class="layui-input-inline">
                                <p class="txt" th:text="${classStartsInfoDetail.teachClassNo}"></p>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">已分配学时</label>
                            <div class="layui-input-inline">
                                <p class="txt">12</p>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">教学班名称</label>
                            <div class="layui-input-inline">
                                <input type="text" name="teachClassName" lay-verify="teachClassName" autocomplete="off" th:value="${classStartsInfoDetail.teachClassName}"
                                       placeholder="请输入教学班名称" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">授课方式</label>
                            <div class="layui-input-inline">
                                <select name="teachMethod" lay-filter="teachMethod" style="width: 240px;">
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">开课校区</label>
                            <div class="layui-input-inline">
                                <select name="campusName" lay-filter="campusName" style="width: 240px;">
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">是否排课</label>
                            <div class="layui-input-inline">
                                <select name="sfpk" lay-filter="sfpk">
                                    <option value="">请选择是否排课</option>
                                    <option value="是" selected>是</option>
                                    <option value="否">否</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">是否排考</label>
                            <div class="layui-input-inline">
                                <select name="sfExam" lay-filter="sfExam" style="width: 240px;">
                                    <option value="">请选择是否排考</option>
                                    <option value="是" selected>是</option>
                                    <option value="否">否</option>

                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">考试形式</label>
                            <div class="layui-input-inline">
                                <select name="examForm" lay-filter="examForm" th:value="${classStartsInfoDetail.examForm}">
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">考试方式</label>
                            <div class="layui-input-inline">
                                <select name="examMethod" lay-filter="examMethod" style="width: 240px;" th:value="${classStartsInfoDetail.examMethod}">
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">教学模式</label>
                            <div class="layui-input-inline">
                                <select name="teachModel" lay-filter="teachModel">
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item" style="display: none;">
                        <div class="layui-input-block">
                            <button type="submit" class="layui-btn btnSubmitHide" lay-submit=""
                                lay-filter="setSubmit">提交</button>
                        </div>
                    </div>
                </form>
            </div>
            <div class="btn">
                <button type="button" class="layui-btn btnSubmit" lay-submit="">提交 </button>
            </div>
        </div>
    </div>
</body>
<script th:src="@{../js/jquery-3.3.1.min.js}"></script>
<script th:inline="javascript">
    document.domain = "chaoxing.com";
    let pageIndex = 1;
    layui.use(["form"], function () {
        var form = layui.form;
        getData("campusName","../processData/getDictData?dictType=XQ");
        getData("examForm","../processData/getDictData?dictType=KSXS");
        getData("examMethod","../processData/getDictData?dictType=KSFS");
        getData("teachModel","../processData/getDictData?dictType=JXMS");
        getData("teachMethod","../processData/getDictData?dictType=SKFS");
        // 点击提交
        $('.btnSubmit').on('click', function () {
            form.on('submit(setSubmit)', function (data) {
                data.field.id = [[${detailId}]];
                $.post("../processData/addClassStarts",data.field,function(result){
                    layer.msg("开课成功", {icon: 1, time: 2000});
                    addCourseInformation();
                    setTimeout(successFun, 3000);
                },"json");
                return false;
            })
            $('.btnSubmitHide').trigger('click');
        });
    });
    $(".back").click(function (){
        window.location.href=document.referrer;
    })


    function successFun(){
        window.location.href = "../process/classStartsDetail?fid=[[${classStartsInfoDetail.fid}]]&formUserId=[[${classStartsInfoDetail.formUserId}]]";
    }

    function addCourseInformation(){
        $.post("../processData/addCourseInformation",{detailId:[[${detailId}]],fid:[[${classStartsInfoDetail.fid}]]},function(result){
        },"json");
    }

    function getData(selName,url){
        $.post(url,function(result){
            for (let i = 0; i < result.list.length; i++) {
                const option = result.list[i];
                const name = option.dictName;
                $("select[name=\""+selName+"\"]").append(new Option(name, name));
            }
            layui.form.render('select');
        },"json");
    }
</script>

</html>