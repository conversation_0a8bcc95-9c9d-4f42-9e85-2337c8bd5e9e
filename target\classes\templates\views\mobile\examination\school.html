<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!--    <meta name="viewport" content="width=device-width, initial-scale=1.0">-->
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no"/>
    <title>全校考表</title>
    <script th:src="@{~/js/responsive-1.0.js}"></script>
    <link rel="stylesheet" th:href="@{~/css/examination/mobile/iosSelect.css}">
    <link rel="stylesheet" th:href="@{~/css/examination/mobile/common.css}"/>
    <link rel="stylesheet" th:href="@{~/css/examination/mobile/schedule.css}"/>
    <link rel="stylesheet" th:href="@{~/css/examination/mobile/newStyle.css}"/>
    <!--    <link rel="stylesheet" th:href="@{~/css/mescroll.min.css}" />-->

</head>

<body>
<!--<div class="wcApplyBox mescroll" id="wcApplyBox">-->
<!--    <div class="tophead">-->
<!--        <div class="head bottomLine">-->
<!--            <div class="leftHead">-->
<!--                <div class="back"></div>-->

<!--            </div>-->
<!--            <div class="centerHead">-->
<!--                <div class="selectBox">-->
<!--                    <div class="selectWeek"><span class="week" week='1'>全校考表</span></div>-->
<!--                </div>-->
<!--            </div>-->
<!--            <div class="rightHead">-->
<!--                <div class="menu" id="menu"></div>-->
<!--            </div>-->
<!--        </div>-->
<!--    </div>-->
<div class="search">
    <div class="search-text"><img th:src="@{~/images/examination/search-icons.png}" alt="">搜索</div>
    <input type="text" placeholder="请输入">
</div>
<div class="batchSel" id="batchSel">
    <h3>当前批次：<em></em></h3> <span class="arrow"></span>
</div>
<div class="teacherList">

</div>
<div id="load-more-button"
     style="display:none;font-size: 14px;line-height: 20px;text-align: center; margin-top:-20px;color: #999999">
    加载中...
</div>
<!--</div>-->
</body>
<script th:src="@{~/js/jquery-3.3.1.min.js}"></script>
<script th:src="@{~/js/iosSelect.js}"></script>
<script type="text/javascript" th:src="@{~/js/mobileVersion.js}"></script>
<script type="text/javascript" th:src="@{~/js/CXJSBridge.js}"></script>
<script type="text/javascript" th:src="@{~/js/app.utils.js}"></script>
<script type="text/javascript" th:src="@{~/js/tools.js}"></script>
<script type="text/javascript" th:src="@{~/js/base.js}"></script>
<script type="text/javascript" th:src="@{~/js/my.util.js}"></script>
<!--<script type="text/javascript" th:src="@{~/js/mescroll.js}"></script>-->
<!--<script>-->
<!--    //创建MeScroll对象-->
<!--    var mescroll = new MeScroll("wcApplyBox", {-->
<!--        down: {-->
<!--            auto: false, //是否在初始化完毕之后自动执行下拉回调callback; 默认true-->
<!--            callback: downCallback, //下拉刷新的回调-->
<!--        }-->
<!--    });-->
<!--    function downCallback(){-->
<!--        location.reload();-->
<!--    }-->
<!--</script>-->
<!--<script>-->
<!--    function _jsBridgeReady(){-->
<!--        jsBridge.postNotification('CLIENT_CUSTOM_MENU',{-->
<!--            show:1,-->
<!--            icon:'https://p.ananas.chaoxing.com/star3/origin/8ca1856bfeae5ec84868a4220a5fc4d5',-->
<!--            width:50,-->
<!--            children:[{menu:'筛选',option:'openMenu()'}]-->
<!--        });-->
<!--    }-->
<!--</script>-->
<script th:inline="javascript">
    var fid = [[${fid}]];
    //选择考试
    var data = [];
    var page = 1
    $(function () {
        getInvigilator(page);
        getExaminationBatch();
    })
    var kw = '';
    $('.search input').bind('keypress', function (event) {
        if (event.keyCode == "13") {
            kw = $(this).val();
            page=1;
            $(".teacherList").html("");
            getInvigilator(page,kw)
        }
    });

    //加载更多
    var loadMore=true;

    function getInvigilator(page, kw) {
        $.get("/examination/basic/invigilator", {fid: fid, page: page, kw}, function (res) {
            if (res.code == 200) {
                var html = '';
                var data = res.data;
                for (let i = 0; i < data.length; i++) {
                    if (data[i].jkjsgl_jsxm == undefined || data[i].jkjsgl_jsxm == '') {
                        continue;
                    }
                    html += '<div class="teacher" data-value=' + data[i].jkjsgl_jsxm.puid + '>'
                    html += '            <img src="http://photo.chaoxing.com/p/' + data[i].jkjsgl_jsxm.puid + '_80" alt="">'
                    html += '            <h3>' + data[i].jkjsgl_jsxm.uname + '</h3>'
                    html += '            <span class="arrow"></span>'
                    html += '        </div>'
                }
                $('#load-more-button').hide();
                $(".teacherList").append(html);
            }else {
                loadMore=false;
            }
        })
    }

    $('.teacherList').scroll(function () {
        if ($(this).scrollTop() + $(this).innerHeight() >= $(this)[0].scrollHeight - 20) {
            if (loadMore){
                $('#load-more-button').show();
                // 这里调用你的加载更多内容的函数
                page += 1
                getInvigilator(page,kw)
            }
        }
    });

    function getExaminationBatch() {
        $.get("/examination/basic/batch", {fid: fid, sffb: '1'}, function (res) {
            if (res.code == 200) {
                var tempData = res.data;

                for (let i = 0; i < tempData.length; i++) {
                    var tempData = res.data;
                    $("#batchSel em").text(tempData[0].kspcgl_kspcmc)
                    $("#batchSel em").attr("data-value", tempData[0].kspcgl_kspcbh)
                    $("#batchSel em").attr("data-xnxq", tempData[0].kspcgl_xnxq)
                    $("#batchSel em").attr("data-publish", tempData[0].kspcgl_sffb)
                    let json = {
                        "id": tempData[i].kspcgl_kspcbh,
                        "value": tempData[i].kspcgl_kspcmc,
                        "xnxq": tempData[i].kspcgl_xnxq,
                        "ispublish": tempData[i].kspcgl_sffb
                    }
                    data.push(json);
                }
            }
        })
    }

    $(".teacherList").on("click", "div", function () {
        var publish = $("#batchSel em").attr("data-publish");
        if (publish == undefined || publish == "" || publish == "未发布") {
            U.fail("当前批次尚未发布")
            return;
        }
        var kspcbh = $("#batchSel em").attr("data-value");
        var xnxq = $("#batchSel em").attr("data-xnxq");
        var puid = $(this).attr("data-value");
        var ua = navigator.userAgent.toLowerCase();
        if (ua && ua.indexOf("chaoxingstudy") != -1 && ua.indexOf('chaoxingstudypc') == -1) {
            // 在客户端里面才进行相关操作
            openA("教师考表", "/mobile/examination/tch?fid=" + fid + "&kspcbh=" + kspcbh + "&uid=" + puid + "&xnxq=" + xnxq)
        } else {
            window.open("/mobile/examination/tch?fid=" + fid + "&kspcbh=" + kspcbh + "&uid=" + puid + "&xnxq=" + xnxq)
        }
    })

    function openMenu() {
        var ua = navigator.userAgent.toLowerCase();
        if (ua && ua.indexOf("chaoxingstudy") != -1 && ua.indexOf('chaoxingstudypc') == -1) {
            // 在客户端里面才进行相关操作
            openA("筛查详情", "/mobile/examination/sift")
        } else {
            window.open("/mobile/examination/sift")
        }
    }

    var showBankDom = document.querySelector('#batchSel');
    showBankDom.addEventListener('click', function () {
        var bankId = showBankDom.dataset['id'];
        var bankName = showBankDom.dataset['value'];

        var bankSelect = new IosSelect(1,
            [data], {
                // container: '.container',
                title: '选择考试批次',
                itemShowCount: 7,
                oneLevelId: bankId,
                callback: function (selectOneObj) {
                    if (selectOneObj.ispublish == undefined || selectOneObj.ispublish == "" || selectOneObj.ispublish == "未发布") {
                        U.fail("所选批次尚未发布")
                    } else {
                        $("#batchSel em").text(selectOneObj.value)
                        $("#batchSel em").attr("data-value", selectOneObj.id)
                        $("#batchSel em").attr("data-xnxq", selectOneObj.xnxq)
                        $("#batchSel em").attr("data-publish", selectOneObj.ispublish)
                    }

                },
            });
    });


    $(document).ready(function () {
        //适配iphone头部
        addIphoneHeight();

        // 头部检索
        $(".search-text").click(function () {
            $(this).hide().next().show().focus();
        })
        $(".search input").blur(function () {
            if ($(this).val() == "") {
                $(this).hide().prev().show();
            }
        })

        function addIphoneHeight() {
            var isIphone = /iphone/gi.test(navigator.userAgent);
            console.log(isIphone);
            if (isIphone && (screen.height == 812 && screen.width == 375)) {
                $('body').addClass('iosxwrapMax');
            } else if (isIphone && (screen.width == 414)) {
                $('body').addClass('iospluswrapMax');
            } else if (isIphone) {
                $('body').addClass('ioswrapMax');
            }
        }

        setTimeout(function () {
            var tabHeight = $(window).height() - $(".teacherList").offset().top;
            $(".teacherList").css({
                'height': tabHeight,
                "overflow-y": "auto"
            })
        }, 300)
    })
</script>

</html>