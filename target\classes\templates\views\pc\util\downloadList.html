<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导出记录</title>
    <link rel="stylesheet" th:href="@{~/css/examination/global2.css}">
    <link rel="stylesheet" th:href="@{~/css/examination/dialog.css}">
    <link rel="stylesheet" th:href="@{~/css/examination/reset.css}">
    <link rel="stylesheet" th:href="@{~/plugin/layui/css/layui.css}">
    <script th:src="@{~/plugin/layui/layui.js}"></script>
    <script th:src="@{~/js/jquery-1.11.3.min.js}"></script>
    <script th:src="@{~/js/my.util.js}"></script>
    <style>
        .dialog {
            border-radius: 0;
        }
    </style>
</head>

<body>
<div class="masker"></div>
<div class="dialog" id="exportRecord">
    <div class="dialog-con">
        <div style="height: 470px">
            <table class="layui-hide materialTable" id="materialTable" lay-filter="materialTable"></table>
        </div>
        <div class="tab-mes">
            <div class="total">共<em id="total"></em>条数据</div>
            <div class="refresh" id="refresh">刷新</div>
        </div>
    </div>
</div>
</body>
<script type="text/html" id="status">
    {{#  if(d.status==0){ }}
    <span title="正在导出，如果长时间无结果，请联系管理员">正在导出</span>
    {{#  } else if(d.status==1){ }}
    <span style="color: #00B368">导出成功</span>
    {{#  } else{ }}
    <span style="color: #F33131">导出失败：{{d.msg}}</span>
    {{#  } }}
</script>
<script type="text/html" id="tmplToolBar">
    {{#  if(d.status==1){ }}
    <div class="oprate-table" style="width: 200px;">
        {{#  if( d.fileType != 'zip'){ }}
        <span class="download" lay-event="preview">预览</span>
        {{# } }}
        <span class="download" lay-event="download">下载</span>
        {{#  if(d.formId==2){ }}
        <span class="download" lay-event="download-pdf">pdf下载</span>
        {{# } }}
        <span class="delete" lay-event="delete">删除</span>
    </div>
    {{# } else { }}
    <div class="oprate-table">
        <span class="delete" lay-event="delete">删除</span>
    </div>
    {{# } }}
</script>
<script th:inline="javascript">
    var fid = [[${fid}]];
    var uid = [[${uid}]];
    var formId = [[${formId}]];
    layui.use(['jquery', 'table'], function () {
        var table = layui.table;
        $ = layui.jquery;
        table.render({
            elem: '#materialTable',
            url: '/downloadCenter/list',
            where: {
                fid: fid,
                uid: uid,
                formId: formId
            },
            parseData: function (res) {
                if (res.code==200){
                    return {
                        "code": 0, //解析接口状态
                        "msg": res.msg, //解析提示文本
                        "count": res.data.total, //解析数据长度
                        "data": res.data.records //解析数据列表
                    }
                }else {
                    return {
                        "code":  1, //解析接口状态
                        "msg": "服务异常", //解析提示文本
                    }
                }
            },
            height: '470',
            cols: [
                [{
                    field: "fileName",
                    title: "导出文件名",
                    align: "center",
                    width: 180,
                    templet: function (data) {
                        return data.fileName ? data.fileName : '--';
                    }
                },
                    {
                        field: "fileType",
                        title: "文件类型",
                        align: "center",
                        width: 90,
                        templet: function (data) {
                            return data.fileType ? data.fileType : '--';
                        }
                    },
                    {
                        field: "fileSize",
                        title: "文件大小",
                        align: "center",
                        width: 120,
                        templet: function (data) {
                            return data.fileSize ? U.convertFileSize(data.fileSize, 2) : '--';
                        },
                    },
                    {
                        field: "createTime",
                        title: "导出时间",
                        align: "center",
                    },
                    {
                        field: "status",
                        title: "导出状态",
                        align: "center",
                        templet: "#status",
                        width: 100
                    },
                    {
                        title: "操作",
                        align: "center",
                        toolbar: "#tmplToolBar",
                        width: function () {
                            return formId==2?200:140;
                        },
                    },
                ]
            ],
            done: function (res, curr, count) {
                $("#total").html(count)
                $('td[data-field=fileName] div').each(function (index, element) {
                    $(element).attr('title', $(element).text());
                });
            },
            page: {
                layout: ['prev', 'page', 'next', 'limit', 'skip']
            }
        });
        table.on('tool(materialTable)', function (obj) {
            crObj = obj;
            var data = obj.data;
            if (obj.event === 'download') {
                //创建iframe
                const iframe = document.createElement("iframe");
                //设置iframe属性
                iframe.setAttribute("hidden","hidden");
                document.body.appendChild(iframe);
                iframe.onload = () => {
                    if(iframe){
                        iframe.setAttribute('src','about:blank');
                    }
                };
                iframe.setAttribute("src",data.downloadURL);
                //三秒之后删除iframe
                setTimeout(function (){
                    iframe.remove()
                },3000)
            }else if (obj.event === 'preview') {
                if (data.objectid==null||data.objectid==''){
                    layer.msg("当前文件暂不支持预览", {icon: 2, time: 2000});
                    return false;
                }
                $.get("/downloadCenter/previewFile?objectId="+data.objectid, {}, function (res) {
                    if (res.code == 200) {
                        window.open(res.data);
                    }else{
                        layer.msg("当前文件暂不支持预览！", {icon: 2, time: 2000});
                    }
                });
            }else if (obj.event === 'download-pdf') {
                if (data.objectid==null||data.objectid==''){
                    layer.msg("当前文件暂不支持下载", {icon: 2, time: 2000});
                    return false;
                }
                window.location.href = "/downloadCenter/download/pdf?objectId="+data.objectid
            } else if (obj.event === 'delete') {
                layer.confirm('确定删除吗？', function (index) {
                    $.get("/downloadCenter/delete", {id: data.id}, function (res) {
                        if (res.code == 200) {
                            obj.del();
                            table.reload('materialTable');
                            layer.close(index);
                        } else {
                            layer.msg("删除失败", {icon: 2, time: 2000});
                        }
                    })
                });
            }
        });
        // 刷新
        $("#refresh").click(function () {
            table.reload('materialTable');
        })
    })
</script>

</html>