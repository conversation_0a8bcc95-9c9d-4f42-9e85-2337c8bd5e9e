<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生成教学计划</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/global.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/slideCommon.css'}"/>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/teachPlan.css'}"/>
</head>

<body>
<div class="marker"></div>
<div class="dialog" id="teachPlanDialog">
    <div class="dialog-con">
        <div class="item">
            <div class="label">选择打回学年学期</div>
            <div class="j-search-con multiple-box">
                <input type="text" name="termSel" placeholder="请选择" readonly="" class="schoolSel">
                <span class="j-arrow"></span>
                <div class="j-select-year">
                    <div class="search">
                        <input type="text" placeholder="搜索">
                        <span></span>
                    </div>
                    <div class="all-selects">全选</div>
                    <ul class="dropdown-list">
                    </ul>
                </div>
            </div>
        </div>
        <p class="error-tips">请选择打回学年学期</p>
    </div>
    <div class="dialog-footer">
        <button class="btn-cancel">取消</button>
        <button class="btn-sure">确定</button>
    </div>
</div>
</body>
<script th:src="${_CPR_+_VR_+'/js/jquery-3.3.1.min.js'}"></script>
<script th:src="${_CPR_+_VR_+'/plugin/layui/layui.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/cultivation/slideCommon.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/my.util.js'}"></script>
<script th:inline="javascript">
    const formId = [[${formTopBtnVO.formId}]];
    const fid = [[${formTopBtnVO.fid}]];
    const uid = [[${formTopBtnVO.uid}]];
    const selectTotal = [[${formTopBtnVO.selectTotal}]];
    const queryId = [[${formTopBtnVO.queryId}]];
    const _VR_ = [[${_VR_}]];
    $(document).ready(function () {
        const postData = {
            queryId: queryId,
            fid: fid,
            uid: uid
        };
        $.post(_VR_ + "/admin/formData/getMcSemesterData", postData, function (result) {
            let html = "";
            let keys = Object.keys(result.list).sort((a, b) => b.localeCompare(a));
            keys.forEach(semester => {
                html += "<li>" + semester + "</li>";
            });
            $(".dropdown-list").html(html);
        }, "json");
        $('#teachPlanDialog .btn-sure').click(function () {
            let semester = $('.schoolSel').val();
            if (!semester) {
                U.fail("请选择学年学期！");
                return false;
            }
            location.href = _VR_ + "/api/form-btn/gm/outpost.popup?code=2T10009&semester=" + semester + "&formId=" + formId + "&fid=" + fid + "&queryId=" + queryId + "&uid=" + uid + "&selectTotal=" + selectTotal;
        });
    })
</script>
</html>