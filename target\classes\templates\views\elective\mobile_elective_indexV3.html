<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1, user-scalable=no"/>
    <title>选课</title>
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/global.css'"/>
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/head.css'"/>
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/selCourseMV3.css?v=8'"/>
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/courseListV4.css?v=8'"/>
    <script th:src="${_CPR_}+'/elective/js/responsive-1.0.js'"></script>
    <!-- //2025.4.9 -->
    <style>
        header .head-con .head-back {
            position: relative;
            left: 0;
            top: 0;
            margin-left: .32rem;
        }

        header .head-con {
            display: flex;
            align-items: center;
            height: 0.92rem;
            justify-content: space-between;
        }

        .dialog-wrap.dialog-plan .dialog .plan.active {
            background: #E1EBFF;
            color: #4D88FF;
        }

        .dialog-wrap.dialog-plan .dialog .plan {
            padding: 0.24rem 0.48rem;
            position: relative;
        }

        .dialog-wrap.dialog-plan .dialog .plan .state {
            position: absolute;
            top: 0.24rem;
            right: 0.24rem;
            line-height: 0.34rem;
            font-size: 0.22rem;
            padding: 0 0.1rem;
            border-radius: 0.04rem;
            color: #fff;
        }

        .dialog-wrap.dialog-plan .dialog .plan .state.not-finished {
            background-color: #ffb026;
        }

        .dialog-wrap.dialog-plan .dialog .plan .state.finished {
            background-color: #f76560;
        }

        .dialog-wrap.dialog-plan .dialog .dialog-con {
            padding: 0.24rem 0;
        }
    </style>
</head>

<body>
<header>
    <div class="head-signal"></div>
    <div class="head-con">
        <a href="javascript:void(0)" class="head-back"> </a>
                <h1 class="head-title">学生选课</h1>
<!--        <div class="centerHead">-->
<!--            <div class="selectBox">-->
<!--                <div class="selectWeek"><span class="week" week="1"></span><em></em></div>-->
<!--            </div>-->
<!--        </div>-->
        <div class="head-menu">
            <div class="menu" id="menu"></div>
            <ul class="menu-list" id="menuList">
                <li class="active">课表显示</li>
                <li>列表显示</li>
            </ul>
        </div>
    </div>
</header>
<!-- 2025.4.9 新增结构 -->
<div class="con-select">
    <ul>
        <li class="course"><span></span> <i></i></li>
        <li class="date"><span>暂无选课计划</span><i></i></li>
    </ul>
</div>
<div class="z-table" id="Curriculum">
    <div class="select-week">第<i>1</i>周</div>
    <div id="thead">
        <table>
            <thead class="thead">
            <tr>
                <td></td>
                <td>
                    <span class="week">周一</span>
                    <span class="weekdate" id="week1"></span>
                </td>
                <td>
                    <span class="week">周二</span>
                    <span class="weekdate" id="week2"></span>
                </td>
                <td>
                    <span class="week today">周三</span>
                    <span class="weekdate" id="week3"></span>
                </td>
                <td>
                    <span class="week">周四</span>
                    <span class="weekdate" id="week4"></span>
                </td>
                <td>
                    <span class="week">周五</span>
                    <span class="weekdate" id="week5"></span>
                </td>
                <td>
                    <span class="week">周六</span>
                    <span class="weekdate" id="week6"></span>
                </td>
                <td>
                    <span class="week">周日</span>
                    <span class="weekdate" id="week7"></span>
                </td>
            </tr>
            </thead>
        </table>
    </div>
    <div id="tbody">
        <table>
            <tbody class="tbody">
            </tbody>
        </table>
    </div>
</div>

<!-- 进入提示 -->
<div class="dialog-wrap dialog-plan" style="display: none">
    <div class="dialog">
        <div class="dialog-con">
        </div>
        <div class="dialog-btn" id="planSel">确定</div>
    </div>
</div>
<!-- 选择周次 -->
<div class="week-dialog" style="display: none">
    <div class="w-con active">
        <div class="w-head">
            <h3>点击查看该周周次</h3>
        </div>
        <div class="w-box">
            <ul>
                <li class="cur">1</li>
                <li>2</li>
            </ul>
        </div>
    </div>
</div>

<!-- 选择学期 -->
<!--<div class="selectWeek-dialog">-->
<!--    <div class="w-con active">-->
<!--        <div class="w-head">-->
<!--            <h3>点击选择学期</h3>-->
<!--        </div>-->
<!--        <div class="w-box">-->
<!--            <ul id="xnxqdiv">-->
<!--                <li class="cur">1</li>-->
<!--            </ul>-->
<!--        </div>-->

<!--    </div>-->
<!--</div>-->
<!-- 详情 -->
<div class="dialog-wrap dialog-mes">
    <div class="dialog">
        <div class="dialog-con">
        </div>
        <div class="dialog-btn">
            <button class="cancel">取消</button>
            <button class="sure" id="exitCourse">退课</button>
        </div>
    </div>
</div>
<div id="tipsBox"></div>
<!-- 选择学期 2025.3.25 -->
<div class="selectWeek-dialog dialog-box">
    <div class="w-con active">
        <div class="w-head">
            <h3>点击选择学期</h3>
        </div>
        <div class="w-box">
            <ul id="xnxqdiv">
            </ul>
        </div>

    </div>
</div>

<!-- 选择选课计划 2025.3.25 -->
<div class="selectCoursePlan-dialog dialog-box">
    <div class="w-con active">
        <div class="w-head">
            <h3>点击选择选课计划</h3>
        </div>
        <div class="search-course">
            <input type="search" placeholder="搜索">
        </div>
        <div class="w-box">
            <ul id="jhdiv">
            </ul>
        </div>

    </div>
</div>
<div id="captcha"></div>
</body>
<script th:inline="javascript">
    const _VR_ = [[${_VR_}]] || '';
    var fid = [[${fid}]];
    let r = [[${r}]];
    let academicYear = [[${academicYear}]];
    let dates = [[${dates}]];
    let fidEnc = [[${fidEnc}]];
    let allAcademicYear = [[${allAcademicYear}]];
</script>
<script th:src="${_CPR_}+'/elective/js/jquery-3.3.1.min.js'"></script>
<script th:src="${_CPR_}+'/elective/layui/layui.js'"></script>
<script type="text/javascript" src="https://kb.chaoxing.com/res/app/curriculum/js/CXJSBridge.js"></script>
<script type="text/javascript" src="https://kb.chaoxing.com/res/app/curriculum/js/app.utils.js"></script>
<script type="text/javascript" src="https://kb.chaoxing.com/res/app/curriculum/js/tools.js"></script>
<script type='text/javascript' src='https://captcha.chaoxing.com/load.min.js?t='></script>
<script th:src="${_CPR_}+'/js/base.js'"></script>
<script th:src="${_CPR_}+'/js/my.util.js'"></script>
<script th:src="${_CPR_}+'/elective/js/headHeight.js'"></script>
<!--<script src="https://cdn.bootcss.com/vConsole/3.2.0/vconsole.min.js"></script>-->
<script th:inline="javascript">
    layui.use(['layer'], function () {
        var layer = layui.layer;
    })
    // var vConsole = new VConsole();
    jsBridge.bind('CLIENT_REFRESH_EVENT', function (object) {
        refreshData();
    });
    jsBridge.postNotification('CLIENT_WEB_BOUNCES', {
        "forbiddenFlag": 1
    });
    var currentDate = new Date();
    var currentMonth = currentDate.getMonth() + 1;
    var currentDay = currentDate.getDate();
    var xxk_kknj = '';
    var xxk_kkyx = '';
    var xxk_kkzy = '';
    var kskc = '';
    var xxk_kkxiaoqu = '';
    var xxk_kclx = '';
    var jh = "";
    var jc = '';
    var zc = 1;
    var stuOptional;
    var stuSelectCourseCid = [];
    var opcid;
    var finaltaskBdid = 0;
    var finaltaskcid = 0;
    var opCourse = 1;
    var captchaIns = null;
    var listtab = 1;
    var yzm = false;
    var jcArr = [];
    var xnxqh = "";
    if (academicYear) {
        xnxqh = academicYear.xnxq_xnxqh;
        if (academicYear.xnxq_xnxqmc) {
            $(".con-select ul li.course").find("span").text(academicYear.xnxq_xnxqmc);
        } else {
            $(".con-select ul li.course").find("span").text(academicYear.xnxq_xnxqh);
        }
        var selectHtml = '';
        for (var i = 1; i <= Number(academicYear.xnxq_jsz); i++) {
            selectHtml += '<li>' + i + '</li>';
        }
        $('.week-dialog .w-box ul').html(selectHtml);
        $('.week-dialog .w-box ul li:first').addClass('cur');
    }
    $(".head-back").on('click', function () {
        AppUtils.closeView();
    })
    if (allAcademicYear) {
        var html = "";
        for (let i = 0; i < allAcademicYear.length; i++) {
            if (academicYear && academicYear.xnxq_xnxqmc && academicYear.xnxq_xnxqmc == allAcademicYear[i].xnxq_xnxqmc) {
                html += "<li data-id='" + allAcademicYear[i].xnxq_xnxqh + "' class='cur'>" + allAcademicYear[i].xnxq_xnxqmc + "</li>";
            } else if (academicYear && academicYear.xnxq_xnxqh && academicYear.xnxq_xnxqh == allAcademicYear[i].xnxq_xnxqh) {
                html += "<li data-id='" + allAcademicYear[i].xnxq_xnxqh + "' class='cur'>" + allAcademicYear[i].xnxq_xnxqh + "</li>";
            } else if (allAcademicYear[i].xnxq_xnxqmc) {
                html += "<li data-id='" + allAcademicYear[i].xnxq_xnxqh + "' class=''>" + allAcademicYear[i].xnxq_xnxqmc + "</li>";
            } else {
                html += "<li data-id='" + allAcademicYear[i].xnxq_xnxqh + "' class=''>" + allAcademicYear[i].xnxq_xnxqh + "</li>";
            }
        }
        $("#xnxqdiv").html(html);
    }
    initCXCaptcha({
        captchaId: 'dRgtnuKwnxSvXXOl0btdNZqATWH8Kmjv',
        element: '#captcha',
        mode: 'popup',
        // type: 'iconclick',
        onVerify: function (err, data) {
            /**
             * 第一个参数是err（Error的实例），验证失败才有err对象
             * 第二个参数是data对象，验证成功后的相关信息，data数据结构为key-value，如下：
             * {
             * validate: 'xxxxx' // 二次验证信息
             * }
             **/
            if (err) return; // 当验证失败时，内部会自动refresh方法，无需手动再调用一次
            // if (opCourse == 1) {
            //     electiveCourses(data.validate)
            // } else {
            dropCourses(data.validate);
            // }
            captchaIns.refresh()
        }
    }, function onload(instance) {
        captchaIns = instance;
    }, function onerror(err) {
    });

    // 切换展示形式
    $("#menu").click(function (e) {
        $(this).next().show();
        e.stopPropagation();
    });


    function setHeight(){
        var tH = $(window).height() - $("#tbody").offset().top - 9;
        $("#tbody").height(tH);
    }
    setTimeout(function () {
        setHeight()
    }, 500);

    console.log($("#tbody").offset().top);
    function _jsBridgeReady() {
        jsBridge.postNotification('CLIENT_TOOLBAR_TYPE', {'toolbarType': 0});

    }
    $("#menuList li").click(function () {
        var index = $(this).index();
        $("#menuList").hide();
        if (index == 1) {
            openA("", window.location.origin+"/elective/task/mobileListV3/index?fidEnc=" + fidEnc + "&xnxqh=" + xnxqh+"&jh="+$(".con-select ul li.date").find("span").text());
            // window.location.href = "/elective/task/mobileListV3/index?fidEnc=" + fidEnc + "&xnxqh=" + xnxqh;
        }
    });

    U.ajax({
        type: 'post',
        url: "../elective/task/getTask",
        data: {fid: fid, xnxqh: xnxqh},
        dataType: 'json',
        async: false,
        success: function (result) {
            if (result.code !== 200 || !result.data) {
                return false;
            }
            var html = "";
            if (result.data.length == 0) {
                html += "<div class='plan'>";
                html += "<h1>当前无选课计划</h1>";
                html += "</div>";
                $(".dialog-plan .dialog .dialog-con").html(html);
                $(".dialog-plan").show();
                return;
            }
            var newjh = [[${jh}]];
            var jhdivhtml = '';
            for (let i = 0; i < result.data.length; i++) {
                jhdivhtml +="<li>";
                jhdivhtml +="<div class='name'>" + result.data[i].xkjhbJhmc + "</div>";
                html += "<div class='plan'>";
                html += "<h1>" + result.data[i].xkjhbJhmc + "</h1>";
                html += "<p>" + result.data[i].xkjhbKssj + "~" + result.data[i].xkjhbJssj + "</p>";
                html += "<p>" + result.data[i].xkjhbXksm + "</p>";
                if ('未结束'==result.data[i].xkjhbXkjhzt){
                    html += "<div class='state not-finished'>未结束</div>";
                    jhdivhtml +="<div class='state not-finished'>未结束</div>";

                }else {
                    html += "<div class='state finished'>已结束</div>";
                    jhdivhtml +="<div class='state finished'>已结束</div>";
                }
                if (newjh ==''){
                    newjh = result.data[i].xkjhbJhmc;
                }
                html += "</div>";
                jhdivhtml +="</li>";
            }
            if (newjh!=''){
                $(".con-select ul li.date").find("span").text(newjh);
            }
            $(".dialog-plan .dialog .dialog-con").html(html);
            $(".dialog-plan").show();
            $("#jhdiv").append(jhdivhtml);
        }
    })

    if (r) {
        if (r.code !== 200) {
            layer.msg(r.msg);
        } else {
            refreshData()
        }
    } else {
        layer.msg("课表数据异常");
    }

    function optionalCourse() {
        U.ajax({
            type: 'post',
            url: "../elective/task/optional/course",
            data: {fid: fid, xnxqh: xnxqh,jh:$(".con-select ul li.date").find("span").text()},
            dataType: 'json',
            async: false,
            success: function (result) {
                if (result.code !== 200) {
                    return false;
                }
                if (!result.data) {
                    $(".dialog-plan").hide();
                    return false;
                }
                stuSelectCourseCid = [];
                stuOptional = result.data;
                if (stuOptional) {
                    for (let z = 0; z < stuOptional.length; z++) {
                        var selected_course = $.parseJSON(stuOptional[z].selectedCourse);
                        for (let y = 0; y < selected_course.length; y++) {
                            stuSelectCourseCid.push(selected_course[y].cid)
                        }
                    }

                }
            }
        })
    }

    function stuOptionalCou() {
        var djj = 0; //第几节课
        var html = "";
        for (let i = 0; i < r.data.lessons.length; i++) {
            jcArr[r.data.lessons[i][0].actualNum] = r.data.lessons[i][0].lessonNumName;
            html += "<tr>";
            html += "<td>";
            html += " <span class='section'>" + r.data.lessons[i][0].lessonNumName + "</span>";
            html += "<span class='time'>" + r.data.lessons[i][0].begin + "</span>";
            html += "<span class='time'>" + r.data.lessons[i][0].end + "</span>";
            html += "</td>";
            if (r.data.lessons[i][0].period === 2 || r.data.lessons[i][0].period === 3 || r.data.lessons[i][0].period === 1 || r.data.lessons[i][0].period === 4) {
                djj++;
            }
            for (let j = 1; j <= 7; j++) {
                if (r.data.lessons[i][0].period !== 2 && r.data.lessons[i][0].period !== 3 && r.data.lessons[i][0].period !== 1 && r.data.lessons[i][0].period !== 4) {
                    html += "<td></td>";
                    continue;
                }
                if (!stuOptional) {
                    html += "<td></td>";
                    continue;
                }
                var xknum = 0; //0灰色
                var xkfalse = true;
                var temphtml = "";
                one :for (let z = 0; z < stuOptional.length; z++) {
                    var selected_course = $.parseJSON(stuOptional[z].selectedCourse);
                    var optional_course = $.parseJSON(stuOptional[z].optionalCourse);
                    if (selected_course.length > 0) {
                        two: for (let y = 0; y < selected_course.length; y++) {
                            var allSj = selected_course[y].allSj;
                            for (let l = 0; l < allSj.length; l++) {
                                if (allSj[l] == (zc + ',' + j + ',' + djj)) {
                                    temphtml = "<td><div class='course' djj = '" + djj + "' zc = '" + j + "'  cid='" + selected_course[y].cid + "'><h5>" + selected_course[y].kcmc + "</h5></div></td>";
                                    xkfalse = false;
                                    break one;
                                }
                            }
                        }
                    }
                    if (optional_course.length > 0) {
                        two: for (let y = 0; y < optional_course.length; y++) {
                            var allSj = optional_course[y].allSj;
                            for (let l = 0; l < allSj.length; l++) {
                                if (allSj[l] == (zc + ',' + j + ',' + djj)) {
                                    temphtml = "<td><div class='course selCourse' djj = '" + djj + "' zc = '" + j + "'><h5>待选课</h5> </div></td>";
                                    xkfalse = false;
                                    break two;
                                }
                            }
                        }
                    }
                    if (xkfalse) {
                        temphtml = "<td></td>";
                    }
                }
                if (xkfalse) {
                    temphtml = "<td></td>";
                }
                html += temphtml;
            }
            html += "</tr>";
        }
        $(".tbody").html(html);
        rowSpanCell($(".z-table tbody"))
    }

    function yzmdropCourses(taskBdid, cid) {
        opCourse = 2;
        finaltaskBdid = taskBdid;
        finaltaskcid = cid;
        if (captchaIns && yzm) {
            captchaIns.popUp();
        } else {
            dropCourses("");
        }
    }

    function dropCourses(validate) {
        var succ = true;
        U.ajax({
            type: 'post',
            url: "../elective/task/drop/courses",
            data: {fid: fid, taskBdid: finaltaskBdid, cid: finaltaskcid, validate: validate, captchaIns: !!captchaIns},
            dataType: 'json',
            async: false,
            success: function (result) {
                if (result.code !== 200) {
                    if ('不在计划时间范围内报名不允许退课' == result.msg) {
                        //姓名 tksqb_xm
                        //学号 tksqb_xh  0009
                        //uid tksqb_uid
                        //所属退课计划 13
                        //退选选修课名称 tksqb_txxxkmc
                        var param = {
                            'data': [
                                {
                                    'alias': 'tksqb_xm',
                                    'val': ['' + result.data.tksqb_xm + ''],
                                    'compt': 'editinput'
                                }
                                , {
                                    'alias': 'tksqb_xh',
                                    'val': ['' + result.data.tksqb_xh + ''],
                                    'compt': 'editinput'
                                }
                                , {
                                    'alias': 'tksqb_uid',
                                    'val': ['' + result.data.tksqb_uid + ''],
                                    'compt': 'editinput'
                                }
                                , {'alias': '13', 'val': ['' + result.data.taskname + ''], 'compt': 'editinput'}
                                , {
                                    'alias': 'tksqb_txxxkmc',
                                    'val': ['' + result.data.tksqb_txxxkmc + ''],
                                    'compt': 'editinput'
                                }
                            ]
                        }
                        // 创建表单
                        var temp_form = document.createElement('form')
                        // 填写表单数据
                        temp_form.action = result.data.url;
                        temp_form.target = '_blank'
                        temp_form.method = 'post'
                        temp_form.style.display = 'none'
                        // 添加参数
                        var opt = document.createElement('textarea')
                        opt.name = 'precast'
                        opt.value = JSON.stringify(param)
                        temp_form.appendChild(opt)
                        document.body.appendChild(temp_form)
                        // 提交数据
                        // temp_form.submit()
                        openA("", result.data.url + "&precast=" + encodeURIComponent(JSON.stringify(param)))
                        return
                    }
                    yzm = result.data;
                    layer.msg(result.msg)
                    succ = false;
                    return
                }
                layer.msg("退课成功")
                yzm = result.data;
                refreshData();
                $(".dialog-mes").hide();
            }
        })
        return succ;
    }

    function refreshData() {
        optionalCourse()
        stuOptionalCou()
    }

    /* **************** 周次 *********************** */
    //选择周次
    $(".select-week").click(function () {
        $(".week-dialog").show();
        $(".week-dialog .w-con").addClass("active");
    });

    //周次切换
    $(".week-dialog").on("click", ".w-con .w-box ul li", function () {
        $(this).addClass("cur").siblings().removeClass("cur");
        $(".week-dialog").hide();
        $(".week-dialog .w-con").removeClass("active");
        let texts = $(this).text();
        zc = texts;
        $(".select-week i").text(texts);
        zcdate();
    });
    zcdate();

    function zcdate() {
        $(".z-table .thead .week").removeClass("today");
        var date = dates[zc];
        let split = date.split(",");
        for (let i = 1; i <= split.length; i++) {
            $("#week" + i).text(split[i - 1]);
            let zcdateday = split[i - 1].split("/");
            if (zcdateday.length == 2 && zcdateday[0] == currentMonth && zcdateday[1] == currentDay) {
                $("#week" + i).addClass("today")
                $("#week" + i).prev().addClass("today")
            }

        }
    }

    //选择学期
    // $(".selectWeek").click(function () {
    //     $(".selectWeek-dialog").show();
    // });
    //学期切换
    // $(".selectWeek-dialog").on("click", ".w-con .w-box ul li", function () {
    //     $(this).addClass("cur").siblings().removeClass("cur");
    //     $(".selectWeek-dialog").hide();
    //     // $(".selectWeek-dialog .w-con").removeClass("active");
    //     let texts = $(this).text();
    //     $(".tophead .centerHead .selectBox .week").text(texts);
    //     window.location.href = "/elective/task/mobileV3/index?fidEnc=" + fidEnc + "&xnxqh=" + $(this).attr("data-id");
    // });
    /* ************** 选课计划 ********************** */
    var planSel = sessionStorage.getItem("planSel");
    if (planSel == 1) {
        $(".dialog-plan").hide()
        sessionStorage.setItem("planSel", 0)
    } else {
        $(".dialog-plan").show()
    }
    // 选课计划确定
    $("#planSel").click(function () {
        let plantext = $(".dialog-wrap.dialog-plan").find(".plan.active h1").text();
        if (plantext!=''){
            $(".con-select ul li.date").find("span").text(plantext);
            refreshData();
        }
        $(".dialog-plan").hide();
    });
    // 点击待选课
    var selCourseEle
    $(".tbody").on("click", ".course", function () {
        if ($(this).hasClass('selCourse')) {
            // window.location.href = "/elective/task/mobileListV3/index?fidEnc=" + fidEnc + "&xnxqh=" + xnxqh + "&jc=" + zc + ',' + $(this).attr("zc") + ',' + $(this).attr("djj");
            openA("", window.location.origin+"/elective/task/mobileListV3/index?fidEnc=" + fidEnc + "&xnxqh=" + xnxqh + "&jc=" + zc + ',' + $(this).attr("zc") + ',' + $(this).attr("djj")+"&jh="+$(".con-select ul li.date").find("span").text());
        } else {
            selCourseEle = $(this)
            courseDetail($(this).attr("cid"))
            // $(".dialog-mes").show();
        }
    });

    function courseDetail(cid) {
        opcid = cid;
        U.ajax({
            type: 'post',
            url: "../elective/task/course/detail",
            data: {fid: fid, cid: cid},
            dataType: 'json',
            async: false,
            success: function (result) {
                if (result.code !== 200) {
                    layer.msg(result.msg)
                    return false;
                }
                var html = "";
                var d = result.data;

                html += "<h1 id='" + d.id + "'  taskBdid = '" + d.taskBdid + "'>" + d.xxkXxkmc + "</h1>";
                html += "<div class='item'><h3>所属选课计划 ：</h3><span>" + d.xxkmc + "</span></div>";
                html += "<div class='item'><h3>开设课程 ：</h3><span>" + d.xxkXxkmc + "</span></div>";
                html += "<div class='item'><h3>开课年级 ：</h3><span>" + d.xxkKknj + "</span></div>";
                html += "<div class='item'><h3>开课院系 ：</h3><span>" + d.xxkKkyx + "</span></div>";
                html += "<div class='item'><h3>开课专业 ：</h3><span>" + d.xxkKkzy + "</span></div>";
                html += "<div class='item'><h3>课程类型 ：</h3><span>" + d.xxkKclx + "</span></div>";
                html += "<div class='item'><h3>上课时间 ：</h3><span>" + getsksjStr(d.xxkSksj) + "</span></div>";
                html += "<div class='item'><h3>上课地点 ：</h3><span>" + d.xxkSkdd + "</span></div>";
                html += "<div class='item'><h3>授课教师 ：</h3><span>" + d.xxkSkjs + "</span></div>";
                html += "<div class='item'><h3>学分 ：</h3><span>" + d.xxkXxkxf + "分</span></div>";
                if (d.xxkXxkrl == -1) {
                    html += "<div class='item'><h3>课程容量：</h3><span>无上限</span></div>";
                } else {
                    html += "<div class='item'><h3>课程容量 ：</h3><span>" + d.xxkXxkrl + "</span></div>";
                }
                html += "<div class='item'><h3>性别 ：</h3><span>" + d.xxkKxxsxb + "</span></div>";
                html += "<div class='item'><h3>编组 ：</h3><span>" + d.courseGroupName + "</span></div>";

                if (d.kcjs !=''){
                    html += "<div class='item'>";
                    html += "<h3>课程简介：</h3>";
                    html += "<div class='wrapper'>";
                    html += "<div class='ellipse'>...</div>";
                    html += "<div class='text ellText' id='introText'>";
                    html += "<label class='btn' for='exp1' style=''></label>";
                    html += d.kcjs;
                    html += "</div>";
                    html += "</div>";
                    html += "</div>";
                }

                $(".dialog-mes .dialog .dialog-con").html(html);
                $(".dialog-mes").show();
                if (d.kcjs !=''){
                    // 详情展开收起
                    $('#introText').removeClass('ellText')
                    $('#introText').find('.btn').hide()
                    $('.wrapper .btn').removeClass('collapse')
                    $('.wrapper .ellipse').hide()
                    var introH = $('#introText').height()
                    var lineH = parseFloat($('#introText').css('line-height'))
                    console.log(introH, lineH)
                    if (introH > lineH * 4) {
                        $('#introText').addClass('ellText')
                        $('#introText').find('.btn').show()
                        $('.wrapper .ellipse').show()
                    } else {
                        $('#introText').removeClass('ellText')
                        $('#introText').find('.btn').hide()
                        $('.wrapper .ellipse').hide()
                    }
                }

            }
        })
    }

    // 点击展开收起
    $(".dialog-wrap").on("click", ".wrapper .btn", function () {
        if ($(this).hasClass('collapse')) {
            $(this).removeClass('collapse')
            $(this).parent().addClass('ellText')
            $('.wrapper .ellipse').show()
        } else {
            $(this).addClass('collapse')
            $(this).parent().removeClass('ellText')
            $('.wrapper .ellipse').hide()
        }
    })


    //2025.4.9
    $(".dialog-wrap.dialog-plan").on("click", ".dialog .plan", function () {
        $(this).addClass("active").siblings().removeClass("active");
    })
    //2025.3.25

    //选择学期
    $(".con-select ul li.course").click(function () {
        $(this).addClass("active").siblings().removeClass("active");
        $(".selectWeek-dialog").show();
    })

    $(".selectWeek-dialog").on("click", ".w-con .w-box ul li", function () {
        $(this).addClass("cur").siblings().removeClass("cur");
        let text = $(this).text();
        $(".con-select ul li.course").find("span").text(text);
        $(".con-select ul li.course").removeClass("active");
        $(".selectWeek-dialog").hide();
        window.location.href = "/elective/task/mobileV3/index?fidEnc=" + fidEnc + "&xnxqh=" + $(this).attr("data-id");
    })


    //选择选课
    $(".con-select ul li.date").click(function () {
        $(this).addClass("active").siblings().removeClass("active");
        $(".selectCoursePlan-dialog").show();
    })

    $(".selectCoursePlan-dialog").on("click", " .w-con .w-box ul li", function () {
        $(this).addClass("cur").siblings().removeClass("cur");
        let text = $(this).find(".name").text();
        $(".con-select ul li.date").find("span").text(text);
        $(".con-select ul li.date").removeClass("active");
        refreshData();
        $(".selectCoursePlan-dialog").hide();
    })

    $(".dialog-box").on('click', function(event) {
        console.log($(event.target))
        if (!$(event.target).closest('.w-con').length) {
            // 如果点击的元素不是弹窗或其子元素，则隐藏弹窗
            $(".con-select ul li.course").removeClass("active");
            $(".con-select ul li.date").removeClass("active");
            $('.dialog-box').hide();
        }

    });
    $(".selectCoursePlan-dialog .w-con .search-course input").on("keyup",function(event){
        if (event.keyCode == 13) {
            let val=$(this).val();
            performSearch(val);
        }
        return false;
    })

    function performSearch(val){
        $(".selectCoursePlan-dialog .w-con .w-box ul li").each(function(){
            if($(this).text().indexOf(val)==-1){
                $(this).hide();
            }else{
                $(this).show();
            }
        })
    }

    /* ***************** 详情 **************************** */

    $(".dialog-mes .dialog-btn button").click(function () {
        $(".dialog-mes").hide();
    })

    // 退课
    $('#exitCourse').click(function () {
        var taskBdid = $("#" + opcid + "").attr("taskBdid");
        yzmdropCourses(taskBdid, opcid);
    })

    // 合并单元格
    function rowSpanCell(el) {
        var tr = el.find('tr');
        var tdAry = JSON.parse(JSON.stringify(new Array(8).fill([])));
        for (let i = 0; i < tr.length; i++) {
            var curTr = tr.eq(i);
            var tds = curTr.find('td');
            var nextTR = tr.eq(i + 1);
            for (let j = 1; j < tds.length; j++) {
                var curTxt = tds.eq(j).find('.course:not(.selCourse) h5').text();
                var nextTxt = nextTR.find('td').eq(j).find('.course:not(.selCourse) h5').text();
                if (curTxt != "") {
                    if (curTxt == nextTxt) {
                        tdAry[j].push(tds.eq(j))
                    } else {
                        tdAry[j].push(tds.eq(j))
                        var tdLen = tdAry[j].length;
                        if (tdLen > 1) {
                            tdAry[j].forEach((element, index) => {
                                if (index == 0) {
                                    $(element).attr('rowspan', tdLen)
                                } else {
                                    $(element).hide();
                                }

                            });
                        }
                        tdAry[j] = []
                    }
                }
            }
        }
    }

    //[{"xxk_skjc":"3","xxk_skxq":"2","xxk_skzc":"1-5,10"}]
    //获取上课时间
    function getsksjStr(sj) {
        let sjsjon = $.parseJSON(sj);
        var sksjstr = "";
        for (let i = 0; i < sjsjon.length; i++) {
            let split = sjsjon[i].xxk_skzc.split(",");
            for (let j = 0; j < split.length; j++) {
                sksjstr += split[j] + "周、";
            }
            sksjstr += "周" + getxq(sjsjon[i].xxk_skxq) + "、";
            sksjstr += getjc(sjsjon[i].xxk_skjc);
            sksjstr += ";";
        }
        return sksjstr;
    }

    function getjc(xxk_skjc) {
        var skxqstr = "";
        let split = xxk_skjc.split(",");
        for (let i = 0; i < split.length; i++) {
            if (split[i].indexOf("-") != -1) {
                if (skxqstr != '') {
                    skxqstr += ",";
                }
                let split1 = split[i].split("-");
                skxqstr += getjcStr(split1[0]) + "-" + getjcStr(split1[1]);
            } else {
                if (skxqstr != '') {
                    skxqstr += ",";
                }
                skxqstr += getjcStr(split[i]);
            }
        }
        return skxqstr;
    }

    function getxq(xxk_skxq) {
        var skxqstr = "";
        let split = xxk_skxq.split(",");
        for (let i = 0; i < split.length; i++) {
            if (split[i].indexOf("-") != -1) {
                if (skxqstr != '') {
                    skxqstr += ",";
                }
                let split1 = split[i].split("-");
                skxqstr += getxqStr(split1[0]) + "-" + getxqStr(split1[1]);
            } else {
                if (skxqstr != '') {
                    skxqstr += ",";
                }
                skxqstr += getxqStr(split[i]);
            }
        }
        return skxqstr;
        // for (let i = 0; i < xxk_skxq.length; i++) {
        //     let split = xxk_skxq[i].split(",");
        //     for (let j = 0; j < split.length; j++) {
        //         skxqstr += split[j] + "周、";
        //     }
        // }
    }

    function getxqStr(xq) {
        if (xq == 1) {
            return "一";
        } else if (xq == 2) {
            return "二";
        } else if (xq == 3) {
            return "三";
        } else if (xq == 4) {
            return "四";
        } else if (xq == 5) {
            return "五";
        } else if (xq == 6) {
            return "六";
        } else if (xq == 7) {
            return "日";
        } else {
            return "";
        }
    }

    function getjcStr(jc) {
        return jcArr[jc];
    }

</script>
</html>