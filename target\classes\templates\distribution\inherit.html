<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教材继承</title>
    <link rel="stylesheet" href="/distribution/css/global.css">
    <link rel="stylesheet" href="/distribution/css/jcPopup.css?v=1">
    <script src="/distribution/js/layer.js"></script>
</head>

<body>
<!-- 教材继承 -->
<div class="popup" id="puCourse">
    <div class="popup-box">
        <div class="pu-title">教材继承</div>
        <div class="pu-con">
            <div class="pu-tips">功能说明：同样的课程，教材复制给下一年度的学生升班后使用。如：第二学年第一学期的课程教材，复制给下一届学生升班后在第二学年第一学期使用</div>
            <form action="">
                <div class="form-item">
                    <ul class="radioCon radioSelCon">
                        <li class="active" v="1">从课程教材继承</li>
                        <li v="2">从“学期课程教材管理”的历史学期继承（适用于历史学期已维护教材）</li>
                    </ul>
                </div>
                <div class="form-item" id="item-content-1" style="width: 310px;display: none">
                    <h3>源学期</h3>
                    <input type="text" placeholder="源学期" id="sourceschoolYear" class="schoolYear" readonly>
                    <span class="j-arrow"></span>
                    <div class="j-select-year">
                        <ul id="source_term_li">

                        </ul>
                    </div>
                </div>
                <div class="form-item" id="item-content-2" style="width: 310px;">
                    <h3>继承到</h3>
                    <input type="text" placeholder="目标学年学期" id="targetschoolYear" class="schoolYear" readonly>
                    <span class="j-arrow"></span>
                    <div class="j-select-year">
                        <ul id="target_term_li">

                        </ul>
                    </div>
                </div>
                <div class="form-item">
                    <h3>继承方式</h3>
                    <ul class="radioCon radioSelCon1">
                        <li class="active" v="1">追加教材</li>
                        <li v="2">覆盖教材</li>
                    </ul>
                </div>
            </form>
        </div>
        <div class="pu-btn">
            <button class="pu-cancel">取消</button>
            <button class="pu-sure">确定</button>
        </div>

    </div>
</div>
</body>
<script src="/distribution/js/jquery-3.3.1.min.js"></script>
<script th:inline="javascript">
    var fid = [[${fid}]];
    var uid = [[${uid}]];
    $(document).ready(function () {
        getTermList();
        // 单选
        $(".radioSelCon1 li").click(function () {
            $(this).addClass('active').siblings().removeClass('active');
        })

        $(".pu-cancel").click(function(){
            window.parent.postMessage(JSON.stringify({action:1}),"*");
        })

        $(".radioSelCon li").click(function () {
            var index = $(this).index();
            if(index == 0){
                $("#item-content-1").hide();
            } else {
                $("#item-content-1").show();
            }
            $(this).addClass('active').siblings().removeClass('active');
        })

        $(".pu-sure").click(function () {
            var index = $(".radioSelCon").find(".active").attr("v");
            if(index == 1){
                addInheritData();
            } else {
                addDataByHistory();
            }

        })

        // 下拉
        $(".schoolYear").click(function (e) {
            let parent = $(this).parent();
            parent.find('.j-arrow').toggleClass('j-arrow-slide');
            parent.find('.j-select-year').toggleClass('slideShow')
            e.stopPropagation()
        })

        $('.j-select-year').on('click', "ul li", function () {
            $(this).addClass('active').siblings().removeClass('active');
            let txt = $(this).text();
            $(this).parents('.form-item').find('.schoolYear').val(txt.trim());
            $(this).parents('.j-select-year').removeClass("slideShow");

        })

        // 点击页面其他地方消失
        $(document).on('click', function (e) {
            if ($(e.target).closest('.j-select-year')
                .length > 0) {
                // alert('弹出框内部被点击了');
            } else {
                $(".j-select").hide();
                $('.j-select-year').removeClass("slideShow")
            }
        })
    })

    function getTermList(){
        $.ajax({
            type: 'get',
            dataType: 'json',
            url: '/api/inherit/getTermList',
            async: false,
            data: {"fid": fid},
            success: function (data){
                var html1 = "";
                var html2 = "";
                for(var i = 0; i < data.sourceset.length; i++){
                    html1 += "<li>"+data.sourceset[i]+"</li>";
                }
                for(var i = 0; i < data.targetset.length; i++){
                    html2 += "<li>"+data.targetset[i]+"</li>";
                }
                $("#source_term_li").html(html1);
                $("#target_term_li").html(html2);
            }
        });
    }

    function addDataByHistory(){
        var sourceterm = $("#sourceschoolYear").val();
        var targetterm = $("#targetschoolYear").val();
        if(sourceterm == ""){
            alert("请选择源学期");
            return ;
        }
        if(targetterm == ""){
            alert("请选择目标学期");
            return ;
        }
        var type = $(".radioSelCon1").find(".active").attr("v");
        if(type == "" || typeof(type) == 'undefined'){
            alert("请选择继承方式");
            return ;
        }
        var formId = getQueryString("formId");
        if(formId == null){
            formId = "2318591";
        }
        $.ajax({
            type: 'get',
            dataType: 'json',
            url: '/api/inherit/addDataByHistory',
            async: false,
            data: {"fid": fid, "type": type, "sourceterm": sourceterm,
                "targetterm": targetterm, "uid": uid, formId: formId},
            success: function (data){
                alert("添加完成，成功"+data.successcount+"条，失败"+data.failcount+"条");
            }
        });
    }

    function addInheritData(){
        var targetterm = $("#targetschoolYear").val();
        if(targetterm == ""){
            alert("请选择目标学期");
            return ;
        }
        var type = $(".radioSelCon1").find(".active").attr("v");
        if(type == "" || typeof(type) == 'undefined'){
            alert("请选择继承方式");
            return ;
        }
        var formId = getQueryString("formId");
        if(formId == null){
            formId = "2318591";
        }
        $.ajax({
            type: 'get',
            dataType: 'json',
            url: '/api/inherit/addInheritData',
            async: false,
            data: {"fid": fid, "type": type,
                "targetterm": targetterm, "uid": uid, formId: formId},
            success: function (data){
                alert("添加完成，成功"+data.successcount+"条，失败"+data.failcount+"条");
            }
        });
    }

    function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]); return null;
    }
</script>

</html>