<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>成绩管理</title>
    <link rel="stylesheet" href="/score/css/global.css">
    <link rel="stylesheet" href="/layui/css/layui.css">
    <link rel="stylesheet" href="/score/css/common.css">
    <link rel="stylesheet" href="/score/css/reset.css">
    <link rel="stylesheet" href="/css/jquery.mCustomScrollbar.css">
    <link rel="stylesheet" href="/score/css/indexRule.css">
</head>
<body>

    <div class="main">
        <div class="content">
            <div class="c-top">
                <h2>级制管理</h2>
            </div>
            <div class="add-lab">
                <span>添加</span>
            </div>
            <div class="tabWrap">
                <table class="layui-table" id="main-table1" lay-filter="dataTable1" lay-data='{page: true}'>
                </table>
            </div>
        </div>
    </div>

    <!-- 添加 -->
    <div id="addPoups" class="levelAdd popup">
        <div class="title">
            <div class="name">添加</div>
            <div class="close"></div>
        </div>
        <div class="popup-con">
            <form class="layui-form "  lay-filter="editForm">
            	<input type="hidden" name="id" autocomplete="off" class="layui-input">
                <div class="layui-form-item">
                    <label class="layui-form-label"><em style="visibility:hidden;">*</em>级制代码：</label>
                    <div class="layui-input-block w275">
                        <input type="text" name="gradeCode"placeholder="JZ001" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>级制名称：</label>
                    <div class="layui-input-block w275">
                        <input type="text" name="gradeName"placeholder="输入内容" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>级制满分：</label>
                    <div class="layui-input-block w275">
                        <input type="number" name="totalScore" placeholder="输入内容" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>级制类型：</label>
                    <div class="layui-input-block">
                        <input type="radio" name="type" lay-filter="gradeType" value="0" title="分值制">
                        <input type="radio" name="type" lay-filter="gradeType" value="1" title="等级制">
                    </div>
                </div>
                <div class="layui-form-item levelManages" >
                    <label class="layui-form-label"><em>*</em>等级管理：</label>
                    <div class="layui-input-block">
                        <div class="radio-list scoreZd">
                            <input type="radio" name="levelWay" lay-filter="gradeManage" value="0" title="无等级">
                            <input type="radio" name="levelWay" lay-filter="gradeManage" value="1" title="按分数">
                            <input type="radio" name="levelWay" lay-filter="gradeManage" value="2" title="按排名">
                        </div>
                        <!-- <div class="radio-list levelZd hide">
                            <input type="radio" name="levelWay" disabled lay-filter="gradeManage" value="1" title="按分数">
                        </div> -->
                        <div class="add-level hide add-fs-level">
                            <span>添加等级</span>
                        </div>
                        <div class="add-level hide add-pm-level">
                            <span>添加等级</span>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>合格标准：<span class="title-tip"></span></label>
                    <div class="layui-input-block">
                        <div class="limit-switch">
                            <input type="checkbox" name="standardOpen" lay-skin="switch" lay-filter="switch_filter">
                            <div class="tit">
                                <h4>高级设置</h4>
                                <p>开启后，可以按照培养层次添加合格标准</p>
                            </div>
                        </div>
                        <div class="eligibility hide fzeligibility">
			                <div class="name">合格标准</div>
			                <div class="radio-list">
			                  <input type="radio" name="standard" lay-filter="standard" value="0" title="分数" checked="" />
			                  <input type="radio" name="standard"lay-filter="standard"value="1" title="等级" />
			                </div>
			                <div class="input">
			                  <input type="text" name="score" placeholder="60" autocomplete="off" class="layui-input"/>
			                </div>
			                <div class="select" style="width: 165px; display: none">
			                  <select name="modules" lay-verify="required">
			                    <option value="">请选择</option>
			                    <option value="差">差</option>
			                    <option value="一般">一般</option>
			                    <option value="优秀">优秀</option>
			                  </select>
			                </div>
			              </div>
			
			              <div class="eligibility hide djeligibility">
			                <div class="name">合格标准</div>
			                <div class="select" style="width: 128px">
			                  <select name="modules" lay-verify="required">
			                    <option value="">请选择</option>
			                    <option value="差">差</option>
			                    <option value="一般">一般</option>
			                    <option value="优秀">优秀</option>
			                  </select>
			                </div>
			              </div>
                        <div class="cultivation-level hide">
                            <div class="lable">
                                <div class="name">培养层次</div>
                                <div class="select w128">
                                    <select name="modules0" lay-verify="required">
                                        <option value="">请选择</option>
                                        <option th:each="level:${list}" th:value="${level.education_level}" th:text="${level.education_level}"></option>
                                    </select>
                                </div>
                                <div class="name">合格标准</div>
                                <div class="radio-list">
                                    <input type="radio" name="standard0" lay-filter="standard0" value="0" title="分数" checked="">
                                    <input type="radio" name="standard0" lay-filter="standard0" value="1" title="等级">
                                </div>
                                <div class="input w128">
                                    <input type="text" name="score0" placeholder="60" autocomplete="off" class="layui-input">
                                </div>
                                <div class="select w128 selects hide">
                                    <select name="notch0" lay-verify="required">
                                        <option value="">请选择</option>
                                        <option value="1">不及格</option>
                                        <option value="2">良</option>
                                        <option value="3">优秀</option>
                                    </select>
                                </div>
                                <div class="operate">
                                    <div class="delet"></div>
                                    <div class="add"></div>
                                </div>
                            </div>
                        </div>
                        
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>是否启用级制：</label>
                    <div class="layui-input-block">
                        <input type="radio" name="deleteFlag"  value="0" title="是">
                        <input type="radio" name="deleteFlag" value="1" title="否">
                    </div>
                </div>

            </form>
        </div>
        <div class="bottom">
            <button type="reset" class="layui-btn layui-btn-primary exam-cancle">取消</button>
            <button class="layui-btn exam-sure">确定</button>
        </div>

    </div>

    <!-- 二级分项 -->
    <div id="subitem" class="subitem popup">
        <div class="title">
            <div class="name">添加</div>
            <div class="close"></div>
        </div>
        <div class="popup-con">
            <div class="add-lab">
                <span>添加</span>
            </div>
            <div class="table">
                <table class="layui-table" id="main-table2" lay-filter="dataTable2">
                </table>
            </div>
        </div>
        <div class="bottom">
            <button type="reset" class="layui-btn layui-btn-primary exam-cancle">取消</button>
            <button class="layui-btn exam-sure">确定</button>
        </div>

    </div>

     <!-- 添加等级-->
    <div id="subitemSecond" class="subitem popup">
        <div class="title">
            <div class="name">添加</div>
            <div class="close"></div>
        </div>
        <div class="popup-con">
            <div class="add-lab">
                <span>添加</span>
            </div>
            <div class="table">
                <table class="layui-table" id="main-table3" lay-filter="dataTable3">
                </table>
            </div>
        </div>
        <div class="bottom">
            <button type="reset" class="layui-btn layui-btn-primary exam-cancle">取消</button>
            <button class="layui-btn exam-sure">确定</button>
        </div>

    </div>

    <!-- 添加级制信息 -->
    <div id="subitemadd" class="addInform popup">
        <div class="title">
            <div class="name">添加级制信息</div>
            <div class="close"></div>
        </div>
        <div class="popup-con">
            <form class="layui-form" lay-filter="secondaryEditForm">
            	<input type="hidden" min="0" name="id" placeholder="">
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>等级对应分数段：</label>
                    <div class="layui-input-block w240">
                        <div class="fractionalSegment">
                            <input type="number" min="0" name="startScore" lay-filter="beforeScore" placeholder="" autocomplete="off" class="layui-input w64">
                            <select name="symbol" lay-verify="required">
                                <option value="">请选择</option>
                                <option value="1">≤  成绩  <</option>
                                <option value="2">≤  成绩  ≤</option>
                                <option value="3"><  成绩  ≤</option>
                            </select>
                            <input type="number" min="0" name="endScore" lay-filter="afterScore" placeholder="" autocomplete="off" class="layui-input w64">
                        </div>
                        
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>等级名称：</label>
                    <div class="layui-input-block w240">
                        <input type="text" name="name" lay-filter="seconName" placeholder="输入内容" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>对应分数：</label>
                    <div class="layui-input-block w240">
                        <input type="text" name="sectionScore" lay-filter="seconNumber"  placeholder="输入内容" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </form>
        </div>
        <div class="bottom">
            <button type="reset" class="layui-btn layui-btn-primary exam-cancle">取消</button>
            <button class="layui-btn exam-sure">确定</button>
        </div>
    </div>

        <!-- 等级管理 -->
        <div id="levelManagement" class="addInform popup">
            <div class="title">
                <div class="name">等级管理</div>
                <div class="close"></div>
            </div>
            <div class="popup-con">
                <form class="layui-form" lay-filter="secondaryEditForManage">
                    <div class="layui-form-item">
			            <input type="hidden" min="0" name="id" placeholder="">
                        <label class="layui-form-label"><em>*</em>等级对应排名：</label>
                        <div class="layui-input-block">
                            <div class="fractionalSegments">
                                <input type="number" min="0" name="startScore" lay-filter="beforeScore1" placeholder="" autocomplete="off" class="layui-input w50">
                                <span>%</span>
                                <div class="select">
                                    <select name="symbol" lay-verify="required">
                                        <option value="">请选择</option>
                                        <option value="1">≤  学生排名  <</option>
                                        <option value="2">≤  学生排名  ≤</option>
                                        <option value="3"><  学生排名  ≤</option>
                                    </select>
                                </div>
                              
                                <input type="number" min="0" name="endScore" lay-filter="afterScore1" placeholder="" autocomplete="off" class="layui-input w50">
                                <span>%</span>
                            </div>
                            
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><em>*</em>等级中文名称：</label>
                        <div class="layui-input-block w240">
                            <input type="text" name="name" lay-filter="seconName1" placeholder="输入内容" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </form>
            </div>
            <div class="bottom">
                <button type="reset" class="layui-btn layui-btn-primary exam-cancle">取消</button>
                <button class="layui-btn exam-sure">确定</button>
            </div>
        </div>

    <script type="text/html" id="barDemo3">
        <a style="color:#3A8BFF;;cursor:pointer;margin-right:8px;" lay-event="edit">编辑</a>
        <a style="color:#F33131;cursor:pointer;" lay-event="del">删除</a>
    </script>
    <script type="text/html" id="barDemo4">
        <a style="color:#3A8BFF;;cursor:pointer;margin-right:8px;" lay-event="edit">编辑</a>
        <a style="color:#F33131;cursor:pointer;" lay-event="del">删除</a>
    </script>
    <script type="text/html" id="barDemo5">
        <a style="color:#3A8BFF;;cursor:pointer;margin-right:8px;" lay-event="edit">编辑</a>
        <a style="color:#F33131;cursor:pointer;" lay-event="del">删除</a>
    </script>

    <script src="/js/jquery1.11.1.min.js"></script>
    <script src="/js/jquery.mCustomScrollbar.concat.min.js"></script>
    <script src="/layui/layui.js"></script>
    <script th:inline="javascript">
    	var fid = [[${fid}]];
    	var xnxq = [[${xnxq}]];
    	var levelForm = [[${list}]];
        $(function () {
            var form, table, laydate, layer;
            layui.use(['form', 'table', 'laydate'],
                function () {
                    var $ = layui.jquery;
                    form = layui.form;
                    table = layui.table;
                    laydate = layui.laydate;
                    layer = layui.layer;

                    //日期选择
                    laydate.render({
                        elem: '#startTimes',
                        format: 'yyyy-MM-dd HH:mm:ss',
                        type: 'datetime',
                    });

                    //日期选择
                    laydate.render({
                        elem: '#endTimes',
                        format: 'yyyy-MM-dd HH:mm:ss',
                        type: 'datetime',
                    });
					var dataTable2 = [];
                    var table2 = table.render({
                        elem: "#main-table1",
                        url:"/api/score/gradeMake/getList?fid="+fid+"&xnxq="+xnxq,
                        id: 'main-table1',
                        page: {
                            limit: 10,
                            limits: [10, 20, 30, 40, 50],
                            layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                        },
                        cols: [
                            [{
                                    field: "gradeCode",
                                    align: "center",
                                    title: "级制代码",
                                    minWidth: 120
                                },
                                {
                                    field: "gradeName",
                                    align: "center",
                                    title: "级制名称",
                                    minWidth: 120
                                },
                                {
                                    field: "deleteFlag",
                                    align: "center",
                                    title: "是否启用级制",
                                    minWidth: 120,
                                    templet: function (d) {
                                        if (d.deleteFlag == 0) {
                                            return '<span class="wstatus correct">是</span>';
                                        }else {
                                            return '<span class="wstatus deny">否</span>';
                                        }
                                    }
                                },
                                {
                                    field: "id",
                                    align: "center",
                                    title: "id",
                                    minWidth: 120,
                                    hide:true
                                },
                                {
                                    field: "options",
                                    align: "center",
                                    title: "操作",
                                    toolbar: "#barDemo3",
                                    minWidth: 120
                                },
                            ]
                        ],
                        done: function (res) {}
                    })
                    
                 


                    /* 监听工具条 */
                    table.on("tool(dataTable1)", function (obj) {
                        var data = obj.data; //获得当前行数据
                        if (obj.event === "del") {
                            obj.del();
                            layer.msg("删除");
                            $.ajax({
					            type: 'post',
					            dataType: 'json',
					            url: '/api/score/gradeMake/deleteById?',
					            data: {id:data.id},
					            success: function (res){
					                if (res.code == 200){
					                    table.reload("main-table1",{page: {curr: 1}});
					                }
					            }
					        });
                        } else if (obj.event === "edit") {
                            console.log(data);
                            $("#addPoups .title .name").html("编辑");
							
							var standardSet = [];
							if(data.standardSet != ''){
								standardSet = JSON.parse(data.standardSet);
							}
							var editData = {
                                "gradeCode": data.gradeCode,
                                "gradeName": data.gradeName,
                                "deleteFlag":data.deleteFlag,
                                "id":data.id,
                                "type":data.type,
                                "totalScore":data.totalScore,
                                "levelWay":data.levelWay,
                                "subitem":data.enableSecondary};
                            levelWay(data.levelWay);
							if(data.standardOpen == 1){
								editData['standardOpen'] = "on";
								$("#addPoups .cultivation-level").removeClass("hide");
								$("#addPoups .cultivation-level .lable").each(function(e){
									if(standardSet.length <= e && e != 0){
										$(this).remove();
									}
								});
								standardSet.forEach(function (info,e){
									if($("#addPoups .cultivation-level .lable").length <= e){
										$("#addPoups .cultivation-level .lable .add").click();
									}
									editData['modules'+e] = info.modules;
									editData['standard'+e] = info.standard;
									if(info.standard == 1){
										$("#addPoups .cultivation-level .lable").eq(e).find(".input.w128").addClass("hide");
										$("#addPoups .cultivation-level .lable").eq(e).find(".selects").removeClass("hide");
										editData['notch'+e] = info.notch;
									}else{
										$("#addPoups .cultivation-level .lable").eq(e).find(".input.w128").removeClass("hide");
										$("#addPoups .cultivation-level .lable").eq(e).find(".selects").addClass("hide");
										editData['score'+e] = info.score;
									}
								});
							}else{
								$("#addPoups .cultivation-level").addClass("hide");
								if(standardSet != '' && standardSet.length > 0){
									var option = standardSet[0];
									if(data['type'] == 1){
										editData['modules'] = option.notch;
									}else {
										editData['standard'] = option.standard;
										if(option.standard == 0){
											editData['score'] = option.score;
										}else{
											editData['notch'] = option.notch;
										}
									}
								
								}
								editData['standardOpen'] = "";
							}
                          	layer.open({
                                type: 1,
                                title: false,
                                closeBtn: false,
                                shadeClose: true,
                                isOutAnim: true,
                                content: $('#addPoups'),
                                area: ['auto', 'auto'],
                                success: function () {
                                	form.val("editForm",editData);
                                	pdState();
	                                $.ajax({
							            type: 'post',
							            dataType: 'json',
							            url: '/api/score/gradeSection/getList?',
							            data: {gradeMakeId:editData.id,levelType:editData.levelWay},
							            success: function (res){
							            	var newDataTable = [];
							                dataTable3 = [];
							                dataTable2 = [];
							                if (res.data.length > 0){
							                	res.data.forEach(function(info, i) {
							                		newDataTable[i] = info;
							                	});
							                }
							                if(editData.levelWay == 1){
							                	dataTable2 = newDataTable;
							                }else{
							                	dataTable3 = newDataTable;
							                }
				                            table.reload('main-table2',{data:dataTable2});
						                	table.reload('main-table3',{data:dataTable3});
							            }
							        });
                                }
                                
                            	}, function () {
                            });
                        }else if(obj.event === "itemize"){
                            secondaryFn();
                        }
                    })

                    /* 监听工具条 */
                    table.on("tool(dataTable3)", function (obj) {
                        var data = obj.data; //获得当前行数据
                        if (obj.event === "del") {
                            obj.del();
                            layer.msg("删除");
                            var newDataTable = [];
			                if (dataTable3.length > 0){
			                	dataTable3.forEach(function(info, i) {
			                		if(data.id != info.id){
				                		newDataTable[i] = info;
			                		}
			                	});
			                }
			                dataTable3 = newDataTable;
                        } else if (obj.event === "edit") {
                            console.log(data);
                            $("#levelManagement .title .name").html("编辑");
                            form.val("secondaryEditForManage", {
                                "symbol":data.symbol,
                                "startScore": data.startScore,
                                "endScore":data.endScore,
                                "name":data.name,
                                "id":data.id,
                                "sectionScore":data.sectionScore
                            });

                          	layer.open({
                                type: 1,
                                title: false,
                                closeBtn: false,
                                shadeClose: true,
                                isOutAnim: true,
                                content: $('#levelManagement'),
                                area: ['auto', 'auto'],
                                success: function () {
                                },
                                
                            }, function () {}
                            );
                        }
                    })

                    table.on("tool(dataTable2)", function (obj) {
                        var data = obj.data; //获得当前行数据
                        if (obj.event === "del") {
                            obj.del();
                            console.log(obj.index);
                            layer.msg("删除")
                            var newDataTable = [];
			                if (dataTable2.length > 0){
			                	dataTable2.forEach(function(info, i) {
			                		if(data.id != info.id){
				                		newDataTable[i] = info;
			                		}
			                	});
			                }
			                dataTable2 = newDataTable;
                        } else if (obj.event === "edit") {
                            $("#subitemadd .title .name").text("编辑");
                            console.log(data);
                            form.val("secondaryEditForm", {
                                "symbol":data.symbol,
                                "startScore": data.startScore,
                                "endScore":data.endScore,
                                "name":data.name,
                                "id":data.id,
                                "sectionScore":data.sectionScore
                            });
                           layer.open({
                                type: 1,
                                title: false,
                                closeBtn: false,
                                shadeClose: true,
                                isOutAnim: true,
                                content: $('#subitemadd'),
                                area: ['auto', 'auto'],
                                success: function () {
                                },
                            }, function () {
                            });
                          
                        }
                    })

                    var table2 = table.render({
                        elem: "#main-table2",
                        id: 'main-table2',
                        //url:"/api/score/gradeSection/getList",
                        cols: [
                            [	{
                                    field: "gardeSection",
                                    align: "center",
                                    title: "等级对应分数段",
                                    templet:function (d){
                                    	var symbol = '';
                                    	if(d.symbol == 1){
                                    		symbol = "≤  成绩  <";
                                    	}else if(d.symbol == 2){
                                    		symbol = "≤  成绩  ≤";
                                    	}else if(d.symbol == 3){
                                    		symbol = "<  成绩  ≤";
                                    	}
                                    	return d.startScore+symbol+d.endScore;
                                    }
                                },
                                {
                                    field: "name",
                                    align: "center",
                                    title: "等级名称",
                                },
                                {
                                    field: "sectionScore",
                                    align: "center",
                                    title: "对应分数",
                                },
                                {
                                    field: "id",
                                    align: "center",
                                    title: "id",
                                    hide:true
                                },
                                {
                                    field: "options",
                                    align: "center",
                                    title: "操作",
                                    toolbar: "#barDemo4",
                                },
                            ]
                        ],
                        done: function (res) {}
                    })

                    var dataTable3 = [];
                    var table3 = table.render({
                        elem: "#main-table3",
                        id: 'main-table3',
                        //url:"/api/score/gradeSection/getList",
                        data:dataTable3,
                        cols: [
                            [{
                                    field: "gardeSection",
                                    align: "center",
                                    width:'160',
                                    title: "等级对应年级排名",
                                    templet:function (d){
                                    	var symbol = "";
                                    	if(d.symbol == 1){
                                    		symbol ="≤  学生排名  <";
                                    	}else if(d.symbol == 2){
                                    		symbol ="≤  学生排名  ≤";
                                    	}else{
                                    		symbol ="<  学生排名  ≤";
                                    	}
                                    	return d.startScore+"%"+symbol+d.endScore+"%";
                                    }
                                },
                                {
                                    field: "name",
                                    align: "center",
                                    width:'140',
                                    title: "等级名称",
                                },
                                {
                                    field: "sectionScore",
                                    align: "center",
                                    width:'120',
                                    title: "对应分数",
                                },
                                {
                                    field: "id",
                                    align: "center",
                                    title: "id",
                                    hide:true
                                },
                                {
                                    align: "center",
                                    title: "操作",
                                    width:'140',
                                    toolbar: "#barDemo5",
                                },
                            ]
                        ],
                        done: function (res) {}
                    })
                    //添加
                    $(".content .add-lab span").on("click", function () {
                        $("#addPoups .title .name").html("添加");
                        form.val("editForm", {
                            "addpCode": '',
                            "addpName": '',
                            "scoreBreakdown":'1',
                            "subitem":'1',
                        });
                        var editData = {
                                "gradeCode": '',
                                "gradeName": '',
                                "deleteFlag":'',
                                "id":'',
                                "type":'',
                                "totalScore":'',
                                "levelWay":'',
                                "subitem":''};
						$("#addPoups .cultivation-level .lable").each(function(e){
							if(e > 0){
								$("#addPoups .cultivation-level .lable .delet").click();
							}
						});
						$("#addPoups .cultivation-level .lable").eq(0).find(".input.w128").removeClass("hide");
						$("#addPoups .cultivation-level .lable").eq(0).find(".selects").addClass("hide");
						$("#addPoups .cultivation-level").addClass("hide");
						editData['standardOpen'] = "";
						editData['score0'] = '';
						editData['modules0'] = '';
						editData['standard0'] = '';
                        layer.open({
                            type: 1,
                            title: false,
                            closeBtn: false,
                            shadeClose: true,
                            isOutAnim: true,
                            content: $('#addPoups'),
                            area: ['auto', 'auto'],
                            success: function () {
                            	form.val("editForm",editData);
                            },
                            
                        }, function () {

                        });
                    })
                    //添加确认
					$("#addPoups .bottom .exam-sure").click(function (obj){
						var data = form.val("editForm");
						var standardSet = new Array();
						if(data['standardOpen'] == 'on'){
							var num = $("#addPoups .cultivation-level .lable").length;
							for(var i = 0;i < num;i++){
								var option = {};
								if(data['modules'+i] != ''){
									option['modules'] = data['modules'+i];
									option['standard'] = data['standard'+i];
									if(data['standard'+i] == 0){
										option['score'] = data['score'+i];
									}else{
										option['notch'] = data['notch'+i];
									}
									standardSet.push(option);
								}
							}
							data['standardOpen'] = 1;
						}else {
							var option = {};
							data['standardOpen'] = 0;
							if(data['type'] == 1){
								option['notch'] = $(".djeligibility select[name='modules']").val();
							}else {
								var standard = $(".fzeligibility input[name='standard']").val();
								if(standard == 0){
									option['standard'] = standard;
									option['score'] = $(".fzeligibility input[name='score']").val();
								}else{
									option['notch'] = $(".fzeligibility input[name='modules']").val();
								}
							}
							standardSet.push(option);
						}
						data['standardSet'] = JSON.stringify(standardSet);
						data['fid'] = fid;
						data['xnxq'] = xnxq;
						if(data.levelWay == 1){
							data['gradeSection'] = JSON.stringify(dataTable2);
						}else if(data.levelWay == 2){
							data['gradeSection'] = JSON.stringify(dataTable3);
						}
						$.ajax({
				            type: 'get',
				            dataType: 'json',
				            url: '/api/score/gradeMake/saveEntity',
				            data: data,
				            success: function (res){
				                if (res.msg == "success"){
				                	$("#addPoups .close").click();
				                    table.reload("main-table1",{page: {curr: 1}});
				                }
				            }
				        });
					});

                    //按排名/按分数-添加等级弹框 
                    function secondaryFn(){
                    	var levelType = $("#addPoups input[name=levelWay]:checked").val();
                    	var content = "subitem";
                    	if(levelType == 2){
                    		content = "subitemSecond";
                    	}
                    	layer.ready(function(){
			                let index = layer.load(2);
			                layer.open({
			                  type: 1,
			                  title: false,
			                  closeBtn: false,
			                  shadeClose: true,
			                  isOutAnim: true,
			                  content: $("#"+content),
			                  area: ["auto", "auto"],
			                  success: function () {
			                    table.reload("main-table2");
			                  },
			                },
			                function () {}
			              );
			              layer.close(index);
			            })
                    }

                    //隐藏弹窗
                    $('.close,.exam-cancle').on("click", function () {
                        // layer.closeAll();
                     var index=$(this).parents(".layui-layer").attr("times");
                     layer.close(index);
                    })
                    //弹窗确定
                    $("#addPoup .exam-sure").on("click", function () {
                        $("#extra").addClass('wrong');
                    })
                    //组件切换
                    $(".main").on("click", ".m-tab ul li", function () {
                        $(this).addClass("cur").siblings().removeClass("cur");
                    })
                    //添加弹窗：等级管理-按分数--添加等级--添加
                    $("#subitem .add-lab span").click(function(){
                        $("#subitemadd .title .name").text("添加级制信息");
                        form.val("secondaryEditForm", {
                                "symbol":'',
                                "startScore": '',
                                "endScore":'',
                                "name":'',
                                "sectionScore":''
                            });
                       layer.open({
                            type: 1,
                            title: false,
                            closeBtn: false,
                            shadeClose: true,
                            isOutAnim: true,
                            content: $('#subitemadd'),
                            area: ['auto', 'auto'],
                            success: function () {
                            },
                        }, function () {
                        });
                    })
                    $("#subitemadd .bottom .exam-sure").click(function(){
                    	var data = form.val("secondaryEditForm");
                    	if($("#subitemadd .title .name").html() == '编辑'){
	                    	dataTable2.forEach(function(info, i) {
	                    		if(info.id == data.id){
	                    			dataTable2[i] = data;
	                    		}	
	                    	})
                    	}else{
                    		data['id'] = 't2'+dataTable2.length;
                    		dataTable2.push(data);
                    	}
                    	table.reload("main-table2",{data:dataTable2,page: {curr: 1}});
                    	$("#subitemadd .close").click();
                    });
                    $("#subitem .bottom .exam-sure").click(function(){
                    	layer.msg("保存成功");
                    	$("#subitem .close").click();
                    });
                    $("#subitemSecond .exam-sure").click(function (){
                    	$("#subitemSecond .close").click();
                    });
                     //等级添加弹窗：等级管理-按等级--添加等级--添加
                     $("#subitemSecond .add-lab span").click(function(){
                         $("#levelManagement .title .name").text("等级管理");
                         form.val("secondaryEditForManage", {
                             "symbol":'',
                             "startScore": '',
                             "endScore":'',
                             "name":'',
                             "sectionScore":''
                         });
                         layer.open({
                            type: 1,
                            title: false,
                            closeBtn: false,
                            shadeClose: true,
                            isOutAnim: true,
                            content: $('#levelManagement'),
                            area: ['auto', 'auto'],
                            success: function () {
                            },
                        }, function () {
                        });
                    })
					$("#levelManagement .bottom .exam-sure").click(function(){
                    	var data = form.val("secondaryEditForManage");
                    	if($("#levelManagement .title .name").html() == '编辑'){
	                    	dataTable3.forEach(function(info, i) {
	                    		if(info.id == data.id){
	                    			dataTable3[i] = data;
	                    		}	
	                    	})
                    	}else{
                    		data['id'] = 't3'+dataTable3.length;
                    		dataTable3.push(data);
                    	}
                    	table.reload("main-table3",{data:dataTable3,page: {curr: 1}});
                    	$("#levelManagement .close").click();
                    });

                    //删除培养层次
                    $(".levelAdd .cultivation-level").on("click"," .lable .operate .delet",function(){
                        if ($(".levelAdd .popup-con .layui-form .layui-form-item .cultivation-level").children().size() > 1) {
                            $(this).parents(".lable").remove();
                        } else {
                            layer.msg("最后一条无法移除!");
                        }
                    })
                    //增加培养层次
                    $(".levelAdd .cultivation-level").on("click"," .lable .operate .add",function(){
                        var dt =  $(this).parents(".cultivation-level").children().eq(0).prop("outerHTML");
                        let nus= $(this).parents(".cultivation-level").children().length;
                        console.log(nus);
                        var level = "";
                        levelForm.forEach(function(info) {
                        	level +="<option value='"+info.education_level+"'>"+info.education_level+"</option>";
                        })
                        var khtml=' <div class="lable">'+
                                '<div class="name">培养层次</div>'+
                                '<div class="select w128">'+
                                    '<select name="modules'+nus+'" lay-verify="required">'+
                                        '<option value="">请选择</option>'+
                                        level+
                                    '</select>'+
                                '</div>'+
                                '<div class="name">合格标准</div>'+
                                '<div class="radio-list">'+
                                    '<input type="radio" name="standard'+nus+'" lay-filter="standard'+nus+'" value="0" title="分数" checked="">'+
                                    '<input type="radio" name="standard'+nus+'" lay-filter="standard'+nus+'" value="1" title="等级">'+
                                '</div>'+
                                '<div class="input w128">'+
                                    '<input type="text" name="score'+nus+'" placeholder="60" autocomplete="off" class="layui-input">'+
                                '</div>'+
                                '<div class="select w128 hide selects">'+
                                    '<select name="notch'+nus+'" lay-verify="required">'+
                                        '<option value="">请选择</option>'+
                                        '<option value="1">不及格</option>'+
                                        '<option value="2">良</option>'+
                                        '<option value="3">优秀</option>'+
                                    '</select>'+
                                '</div>'+
                                '<div class="operate">'+
                                    '<div class="delet"></div>'+
                                    '<div class="add"></div>'+
                                '</div>'+
                            '</div>'
                   
                        
                        $(this).parents(".cultivation-level").append(khtml);

                        let kindex=$(".levelAdd .cultivation-level .lable").length;
                           formnewAdd(kindex);

                        form.render();
                    })
                    //添加弹窗逻辑
			        form.on("radio(gradeType)", function (data) {
			            pdState();
			        });
			        //高级设置
			        form.on("switch(switch_filter)", function (data) {
			            pdState();
			        });
                    function formnewAdd(nums){
                           console.log(nums);
                        for(i = 0; i < nums; i++){
                            console.log('standard'+nums);

                            (function(k) {
                                form.on('radio(standard'+k+')', function(data){
                                    console.log(data);
                                    if(data.value=='0'){
                                        console.log(k);
                                       $(".levelAdd .popup-con .layui-form .layui-form-item .cultivation-level .lable").eq(k).find(".input").removeClass("hide");
                                       $(".levelAdd .popup-con .layui-form .layui-form-item .cultivation-level .lable").eq(k).find(".selects").addClass("hide");
                                        // form.render();
                                    }
                                    if(data.value=='1'){
                                        $(".levelAdd .popup-con .layui-form .layui-form-item .cultivation-level .lable").eq(k).find(".selects").removeClass("hide");
                                       $(".levelAdd .popup-con .layui-form .layui-form-item .cultivation-level .lable").eq(k).find(".input").addClass("hide");
                                        // form.render();
                                    }
                                });
                            })(i);
                        }
                    }
					
                    form.on('radio(gradeManage)', function(data){
                        console.log(data);
                        levelWay(data.value);
                    });
					function levelWay(levelType){
						if(levelType == '0'){
                            $(".add-fs-level").addClass("hide");
                            $(".add-pm-level").addClass("hide");
                        }
                        else if(levelType == '1'){
                           $(".add-fs-level").removeClass("hide");
                           $(".add-pm-level").addClass("hide");
                        }else{
                           $(".add-pm-level").removeClass("hide");
                           $(".add-fs-level").addClass("hide");
                        }
					}
					form.on("radio(standard)", function (data) {
			            let kols = $("input[name='type']:checked").val();
			            console.log(kols);
			            if (data.value == "0") {
			              $(this).parent().parent().find(".select").hide();
			              $(this).parent().parent().find(".input").show();
			            } else {
			              $(this).parent().parent().find(".select").show();
			              $(this).parent().parent().find(".input").hide();
			            }
			        });
			
			        form.on("radio(gradeManage)", function (data) {
			            pdState();
			        });
                    //分数添加等级
                    $(".add-fs-level").click(function(){
                        secondaryFn();
                    })
                    //按排名添加等级
                    $(".add-pm-level").click(function(){
                        secondaryFn();
                    })
                    function pdState() {
			            let a = $("input[name='type']:checked").val();
			            let b = $("input[name='levelWay']:checked").val();
			            let c = $("input[name='standardOpen']").prop("checked");
			            console.log(a);
			            console.log(b);
			            console.log(c);
			            if (b == 0 || !b) {
			              $(".add-fs-level").addClass("hide");
			              $(".add-pm-level").addClass("hide");
			            } else if (b == 1) {
			              $(".add-fs-level").removeClass("hide");
			              $(".add-pm-level").addClass("hide");
			            } else if (b == 2) {
			              $(".add-fs-level").addClass("hide");
			              $(".add-pm-level").removeClass("hide");
			            }
			
			            if (a == 1) {
			              $("input[name='levelWay'][value=0]").attr("disabled", true);
			              $("input[name='levelWay'][value=1]").attr("checked", true);
			              $("input[name='levelWay'][value=2]").attr("disabled", true);
			              $(".add-pm-level").removeClass("hide");
			              $(".add-fs-level").addClass("hide");
			              $(".djeligibility").removeClass("hide");
			              $(".fzeligibility").addClass("hide");
			              form.val("editForm", {
			                gradeManage: 1,
			              });
			            } else {
			              $("input[name='levelWay'][value=0]").removeAttr("disabled");
			              $("input[name='levelWay'][value=2]").removeAttr("disabled");
			            }
			
			            if (c) {
			              $(".djeligibility").addClass("hide");
			              $(".fzeligibility").addClass("hide");
			              $(".cultivation-level").removeClass("hide");
			              let kindex = $(
			                ".levelAdd .popup-con .layui-form .layui-form-item .cultivation-level .lable"
			              ).length;
			              formnewAdd(kindex);
			            } else if (!c) {
			              $(".cultivation-level").addClass("hide");
			
			              if (a && b) {
			                if (a == 1) {
			                  $(".djeligibility").removeClass("hide");
			                  $(".fzeligibility").addClass("hide");
			                }
			
			                if (a == 0) {
			                  $(".fzeligibility").removeClass("hide");
			                  $(".djeligibility").addClass("hide");
			                  if (b == 0) {
			                    $(".fzeligibility").find(".radio-list").hide();
			                    $(".fzeligibility").find(".select").hide();
			                    $(".fzeligibility").find(".input").show();
			                  } else if (b == 1) {
			                    $("input[name='levelWay'][value=0]").removeAttr(
			                      "disabled"
			                    );
			                    $("input[name='levelWay'][value=2]").removeAttr(
			                      "disabled"
			                    );
			                    $(".djeligibility").addClass("hide");
			                    $(".add-pm-level").removeClass("hide");
			                    $(".add-fs-level").addClass("hide");
			                    form.val("editForm", {
			                      standard: 0,
			                    });
			                    $(".fzeligibility").find(".radio-list").show();
			                  } else if (b == 2) {
			                    $(".fzeligibility").find(".radio-list").hide();
			                    $(".fzeligibility").find(".select").hide();
			                    $(".fzeligibility").find(".input").show();
			                  }
			                }
			              }
			            }
			            form.render();
			          }
                })
        })
    </script>

</body>

</html>