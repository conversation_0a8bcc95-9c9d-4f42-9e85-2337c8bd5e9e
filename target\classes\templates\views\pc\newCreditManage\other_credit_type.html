<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>其他学分类型</title>
    <link rel="stylesheet" th:href="@{~/css/creditManage/global.css}">
    <link rel="stylesheet" th:href="@{~/plugin/layui-v2.8.18/layui/css/layui.css}">
    <link rel="stylesheet" th:href="@{~/css/creditManage/common.css}">
    <link rel="stylesheet" th:href="@{~/css/creditManage/reset.css}">
    <link rel="stylesheet" th:href="@{~/css/creditManage/index.css}">
    <script th:src="@{~/plugin/layui/layui2.8.12.js}"></script>
    <style>
        #isSetUnifyStandardCreditLab .tips {
            position: relative;
            box-sizing: border-box;
            -webkit-box-sizing: border-box;
            -moz-box-sizing: border-box;
            -ms-box-sizing: border-box;
            -o-box-sizing: border-box;
            width: 15px;
            height: 15px;
            margin-left: 5px;
            background: url(/images/creditManage/tips-icons.png) no-repeat left center;
            background-size: 138px 17px;
            background-position-x: -12px;
        }

        #isSetUnifyStandardCreditLab .tips:hover em {
            display: inline-block;
            color: #aaa;
        }

        #isSetUnifyStandardCreditLab .tips em {
            position: absolute;
            top: -15px;
            left: 12px;
            display: none;
            width: 200px;
            height: 15px;
            background-color: #F2F5F7;
            border-radius: 2px;
            font-size: 12px;
            text-align: center;
            line-height: 15px;
        }

        #cTime .c-con {
            display: none;
            margin-bottom: 10px;
        }

        #cTime .c-con .name {
            float: left;
            height: 34px;
            width: 90px;
            line-height: 34px;
        }

        #cTime .c-con .name em {
            color: red;
        }

        #cTime .c-con .j-search-con {
            float: left;
        }

        #cTime .c-con .j-search-con .j-select-year ul li {
            position: relative;
        }

        #cTime .c-con .j-search-con .j-select-year ul li i {
            display: block;
            position: absolute;
            top: 50%;
            right: 10px;
            transform: translateY(-50%);
            height: 16px;
            width: 16px;
            background: url(/images/creditManage/radio-icon.png) no-repeat center center;
        }

        #cTime .c-con .addLevelInstitution {
            cursor: pointer;
            height: 34px;
            margin-left: 5px;
            color: #4C88FF;
            line-height: 34px;
        }

        #cTime .c-con .addLevelInstitution span {
            display: inline-block;
            width: 60px;
            line-height: 34px;
        }

        #cTime .c-con .addLevelInstitution .set-img {
            float: left;
            width: 34px;
            height: 34px;
            background: url(/images/creditManage/icon-set.png) no-repeat center center;
        }

        /*  等级类型添加弹出框样式  */
        .selected {
            background: url(/images/creditManage/radio-cur-icon.png) no-repeat center center !important;
        }

        .formulaConversionLevelSub .popup-box .pu-con .pu-con-top {
            height: 85px;
        }

        .formulaConversionLevel .popup-box .pu-con .pu-con-top .pu-con-top-item,
        .formulaConversionLevelSub .popup-box .pu-con .pu-con-top .pu-con-top-item {
            float: left;
            width: 50%;
            height: 85px;
        }

        .formulaConversionLevel .popup-box .pu-con .pu-con-top .pu-con-top-item .title,
        .formulaConversionLevelSub .popup-box .pu-con .pu-con-top .pu-con-top-item .title {
            height: 30px;
            font-size: 16px;
            line-height: 30px;
        }

        .formulaConversionLevel .popup-box .pu-con .pu-con-top .pu-con-top-item .title em,
        .formulaConversionLevelSub .popup-box .pu-con .pu-con-top .pu-con-top-item .title em {
            color: red;
        }

        .formulaConversionLevel .popup-box .pu-con .pu-con-top .pu-con-top-item input,
        .formulaConversionLevelSub .popup-box .pu-con .pu-con-top .pu-con-top-item input {
            box-sizing: border-box;
            width: 80%;
            height: 40px;
            padding-left: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }

        .formulaConversionLevel .popup-box .pu-con .pu-con-top .pu-con-top-item:nth-child(1) input {
            color: #ccc;
            cursor: not-allowed;
        }

        .formulaConversionLevel .popup-box .pu-con .pu-con-bottom .addLevel {
            width: 80px;
            height: 30px;
            margin-bottom: 10px;
            border: 0;
            border-radius: 5px;
            background-color: #4D88FF;
            color: #fff;
            line-height: 20px;
            cursor: pointer;
        }

        .formulaConversionLevel2 .popup-box .pu-con .pu-con-top {
            height: 100px;
        }

        .formulaConversionLevel2 .popup-box .pu-con .pu-con-top em {
            color: red;
        }

        .formulaConversionLevel2 .popup-box .pu-con .pu-con-top .basic_info {
            height: 30px;
            margin-bottom: 10px;
            color: #000;
            line-height: 30px;
            font-weight: 700;
        }

        .formulaConversionLevel2 .popup-box .pu-con .pu-con-top .pu-con-top-item {
            float: left;
            width: 50%;
        }

        .formulaConversionLevel2 .popup-box .pu-con .pu-con-top .pu-con-top-item .title {
            float: left;
            height: 40px;
            margin-right: 10px;
            font-size: 16px;
            line-height: 40px;
        }

        .formulaConversionLevel2 .popup-box .pu-con .pu-con-top .pu-con-top-item input {
            box-sizing: border-box;
            width: 55%;
            height: 40px;
            padding-left: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            color: #ccc;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
<div class="main">
    <div class="top">
        <div class="title">
            <div class="back">返回</div>
            <div class="levelone">学分参数设置</div>
            <div class="icon"></div>
            <div class="leveltwo">其他学分规则设置</div>
        </div>
        <div class="btn" id="saveBth">保存设置</div>
    </div>
    <div class="con">
        <div class="c-item active">
            <h3>学分数据来源设置</h3>
            <input type="hidden" id="creditDataSourceSetId">
            <div class="lab">
                <div class="name" style="width:154px;">来源数据类型</div>
                <div class="j-search-con single-box" style="width: 260px;">
                    <input type="text" style="width: 260px;" placeholder="请选择" readonly class="schoolSel"
                           th:value="分数" id="sourceDataType">
                    <span class="j-arrow"></span>
                    <div class="j-select-year" style="width: 260px;">
                        <ul>
                            <li class="active">分数</li>
                            <li>等级</li>
                            <li>学时</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="lab" id="isSetUnifyStandardCreditLab">
                <div class="name" style="width:154px;">是否设定统一标准学分</div>
                <div class="switc-con">
                    <div class="switch switch-open"><span></span></div>
                    <div class="switch-con" id="isSetUnifyStandardCredit">开启</div>
                </div>
                <div class="tips"><em>开启后，学分可用变量增加标准学分</em></div>
            </div>
            <div class="lab">
                <div class="name" style="width:154px;">标准学分分值</div>
                <div class="input">
                    <input type="number" style="width: 270px;" name="title" lay-verify="title"
                           autocomplete="off" placeholder="请输入整数,最多输入两位有效小数位数"
                           class="layui-input" id="standardCredit">
                </div>
            </div>
        </div>
        <div class="c-item active" id="cTime">
            <div class="c-top">
                <h4>学分换算规则设置</h4>
                <div class="btns-list">
                    <span class="add">新增换算规则</span>
                </div>
            </div>
            <div class="c-con">
                <div class="name"><em>*</em>请选择级制</div>
                <div class="j-search-con single-box" style="width: 260px;">
                    <input type="text" style="width: 260px;" placeholder="请选择" readonly class="schoolSel">
                    <span class="j-arrow"></span>
                    <div class="j-select-year" style="width: 260px;">
                        <ul>
                            <li>
                                <input type="hidden" class="institution_code" value="">
                                <input type="hidden" class="institution_name" value="">
                                <span class="institution_name">品行学分级制</span>
                                <i></i>
                            </li>
                            <li>
                                <input type="hidden" class="institution_code" value="">
                                <input type="hidden" class="institution_name" value="">
                                <span class="institution_name">品行学分级制</span>
                                <i></i>
                            </li>
                            <li>
                                <input type="hidden" class="institution_code" value="">
                                <input type="hidden" class="institution_name" value="">
                                <span class="institution_name">品行学分级制</span>
                                <i></i>
                            </li>
                            <li>
                                <input type="hidden" class="institution_code" value="">
                                <input type="hidden" class="institution_name" value="">
                                <span class="institution_name">品行学分级制</span>
                                <i></i>
                            </li>
                        </ul>
                    </div>
                    <input type="hidden" id="institutionCode" value="">
                    <input type="hidden" id="institutionName" value="">
                </div>
                <div class="addLevelInstitution">
                    <div class="set-img"></div>
                    <span>添加级制</span>
                </div>
            </div>
            <table class="layui-hide" id="otherTable" lay-filter="otherTable"></table>
        </div>
    </div>
</div>
<!-- 分数：添加 -->
<div class="popup formulaConversion formulaConversionScore">
    <div class="popup-box">
        <div class="pu-title">添加换算规则</div>
        <input type="hidden" id="creditOtherRuleSetDataId1">
        <div class="pu-con">
            <div class="lab t-lab">
                <div class="f-top">级制名称</div>
                <div class="input">两档制</div>
            </div>
            <div class="lab t-lab">
                <div class="f-top">等级名称</div>
                <div class="input">通过</div>
            </div>
            <div class="lab score-range">
                <div class="f-top"><em>*</em>成绩区间</div>
                <div class="oprate">
                    <div class="inputs">
                        <input class="input inp layui-input" type="number" id="leftScore1" placeholder="请输入">
                        <div class="error">请重新输入</div>
                        <div class="select-input pre-sel">
                            <div class="name ckd" id="leftMark1">≤</div>
                            <em></em>
                            <div class="select-dropdown">
                                <ul class="dropdown-list">
                                    <li>=</li>
                                    <li>&lt;</li>
                                    <li class="cur">≤</li>
                                    <li class="leftMarkEmptyLi">为空</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="name" style="margin-right:10px;" id="scoreType1">原始成绩</div>
                    <div class="inputs right-inp">
                        <div class="select-input next-sel">
                            <div class="name ckd" id="rightMark1">≤</div>
                            <em></em>
                            <div class="select-dropdown ">
                                <ul class="dropdown-list">
                                    <li>=</li>
                                    <li>&lt;</li>
                                    <li class="cur">≤</li>
                                    <li class="rightMarkEmptyLi">为空</li>
                                </ul>
                            </div>
                        </div>
                        <input class="input inpf layui-input" type="number" id="rightScore1" placeholder="请输入">
                        <div class="error">请重新输入</div>
                    </div>
                </div>
            </div>
            <div class="lab">
                <div class="f-top"><em>*</em>计算公式</div>
                <div class="section">
                    <div class="tit">实际学分=</div>
                    <div class="s-con">
                    </div>
                </div>
            </div>
            <div class="lab btns">
                <div class="available">
                    <h3>可用变量</h3>
                    <div class="a-con">
                        <ul>
                            <li>原始成绩</li>
                            <li style="display: none" id="standardCreditLi1">标准学分</li>
                        </ul>
                    </div>
                </div>
                <div class="keyboard">
                    <h3>可用变量</h3>
                    <div class="k-con">
                        <span class="sign lbracket">(</span>
                        <span class="sign rbracket">)</span>
                        <span class="delet"></span>
                        <span class="empty">清空</span>
                        <span class="num">7</span>
                        <span class="num">8</span>
                        <span class="num">9</span>
                        <span class="sign sign-add">+</span>
                        <span class="num">4</span>
                        <span class="num">5</span>
                        <span class="num">6</span>
                        <span class="sign sign-cancle">－</span>
                        <span class="num">1</span>
                        <span class="num">2</span>
                        <span class="num">3</span>
                        <span class="sign sign-mul">×</span>
                        <span class="num zero">0</span>
                        <span class="num spot">.</span>
                        <span class="sign sign-except">÷</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="pu-btn">
            <button class="pu-cancel">取消</button>
            <button class="pu-sure">确定</button>
        </div>
    </div>
</div>
<!-- 等级：添加级制 -->
<div class="popup formulaConversion formulaConversionLevel">
    <div class="popup-box">
        <div class="pu-title">添加级制</div>
        <input type="hidden" id="creditOtherRuleSetDataId2">
        <div class="pu-con">
            <div class="pu-con-top">
                <div class="pu-con-top-item">
                    <div class="title" title="自动生成">级制代码</div>
                    <input type="text" value="XFJZ001" readonly id="newLevelInstitutionCode"/>
                </div>
                <div class="pu-con-top-item">
                    <div class="title"><em>*</em>&nbsp;级制名称</div>
                    <input type="text" placeholder="输入内容" id="levelInstitutionName"/>
                </div>
            </div>
            <div class="pu-con-bottom">
                <button class="addLevel">添加等级</button>
            </div>
            <table class="layui-hide" id="levelRuleSetTable" lay-filter="levelRuleSetTable"></table>
        </div>
        <div class="pu-btn">
            <button class="pu-cancel">取消</button>
            <button class="pu-sure">确定</button>
        </div>
    </div>
</div>
<!-- 等级：编辑级制-等级 -->
<div class="popup formulaConversion formulaConversionLevel2">
    <div class="popup-box">
        <div class="pu-title">编辑级制</div>
        <input type="hidden" id="creditOtherRuleSetDataId2_1">
        <div class="pu-con">
            <div class="pu-con-top">
                <div class="basic_info"><em>*</em>&nbsp;<span>级制基本信息</span></div>
                <div class="pu-con-top-item">
                    <div class="title"><em>*</em>&nbsp;<span>级制名称</span></div>
                    <input type="text" placeholder="输入内容" id="levelInstitutionName2" readonly/>
                    <input type="hidden" placeholder="输入内容" id="levelInstitutionNameOld2"/>
                </div>
                <div class="pu-con-top-item">
                    <div class="title"><em>*</em>&nbsp;<span>等级名称</span></div>
                    <input type="text" placeholder="输入内容" value="" id="levelName2" readonly/>
                    <input type="hidden" placeholder="输入内容" value="" id="levelNameOld2"/>
                </div>
            </div>
            <div class="lab">
                <div class="f-top"><em>*</em>计算公式</div>
                <div class="section">
                    <div class="tit">实际学分=</div>
                    <input type="hidden" placeholder="输入内容" value="" id="calculationFormulaOld2"/>
                    <input type="hidden" placeholder="输入内容" value="" id="calculationFormulaShowOld2"/>
                    <div class="s-con">
                    </div>
                </div>
            </div>
            <div class="lab btns">
                <div class="available">
                    <h3>可用变量</h3>
                    <div class="a-con">
                        <ul>
                            <li>原始成绩</li>
                            <li style="display: none" id="standardCreditLi2">标准学分</li>
                        </ul>
                    </div>
                </div>
                <div class="keyboard">
                    <h3>可用变量</h3>
                    <div class="k-con">
                        <span class="sign lbracket">(</span>
                        <span class="sign rbracket">)</span>
                        <span class="delet"></span>
                        <span class="empty">清空</span>
                        <span class="num">7</span>
                        <span class="num">8</span>
                        <span class="num">9</span>
                        <span class="sign sign-add">+</span>
                        <span class="num">4</span>
                        <span class="num">5</span>
                        <span class="num">6</span>
                        <span class="sign sign-cancle">－</span>
                        <span class="num">1</span>
                        <span class="num">2</span>
                        <span class="num">3</span>
                        <span class="sign sign-mul">×</span>
                        <span class="num zero">0</span>
                        <span class="num spot">.</span>
                        <span class="sign sign-except">÷</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="pu-btn">
            <button class="pu-cancel">取消</button>
            <button class="pu-sure">确定</button>
        </div>
    </div>
</div>
<!-- 等级：添加级制-添加等级 -->
<div class="popup formulaConversion formulaConversionLevelSub">
    <div class="popup-box">
        <div class="pu-title">添加等级信息</div>
        <div class="pu-con">
            <div class="pu-con-top">
                <div class="pu-con-top-item">
                    <div class="title"><em>*</em>&nbsp;等级名称</div>
                    <input type="text" placeholder="输入内容" class="levelName" id="levelName"/>
                    <input type="hidden" placeholder="输入内容" class="levelName" id="levelNameOld"/>
                </div>
            </div>
        </div>
        <div class="pu-btn">
            <button class="pu-cancel">取消</button>
            <button class="pu-sure">确定</button>
        </div>
    </div>
</div>
<!-- 学时：添加 -->
<div class="popup formulaConversion formulaConversionStudyTime">
    <div class="popup-box">
        <div class="pu-title">添加换算规则</div>
        <input type="hidden" id="creditOtherRuleSetDataId3">
        <div class="pu-con">
            <div class="lab score-range">
                <div class="f-top"><em>*</em>成绩区间</div>
                <div class="oprate">
                    <div class="inputs">
                        <input class="input inp layui-input" type="number" id="leftScore3" placeholder="请输入">
                        <div class="error">请重新输入</div>
                        <div class="select-input pre-sel">
                            <div class="name ckd" id="leftMark3">≤</div>
                            <em></em>
                            <div class="select-dropdown">
                                <ul class="dropdown-list">
                                    <li>=</li>
                                    <li>&lt;</li>
                                    <li class="cur">≤</li>
                                    <li class="leftMarkEmptyLi">为空</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="name" style="margin-right:10px;" id="scoreType3">原始成绩</div>
                    <div class="inputs right-inp">
                        <div class="select-input next-sel">
                            <div class="name ckd" id="rightMark3">≤</div>
                            <em></em>
                            <div class="select-dropdown ">
                                <ul class="dropdown-list">
                                    <li>=</li>
                                    <li>&lt;</li>
                                    <li class="cur">≤</li>
                                    <li class="rightMarkEmptyLi">为空</li>
                                </ul>
                            </div>
                        </div>
                        <input class="input inpf layui-input" type="number" id="rightScore3" placeholder="请输入">
                        <div class="error">请重新输入</div>
                    </div>
                </div>
            </div>
            <div class="lab">
                <div class="f-top"><em>*</em>计算公式</div>
                <div class="section">
                    <div class="tit">实际学分=</div>
                    <div class="s-con">
                    </div>
                </div>
            </div>
            <div class="lab btns">
                <div class="available">
                    <h3>可用变量</h3>
                    <div class="a-con">
                        <ul>
                            <li>原始成绩</li>
                            <li style="display: none" id="standardCreditLi3">标准学分</li>
                        </ul>
                    </div>
                </div>
                <div class="keyboard">
                    <h3>可用变量</h3>
                    <div class="k-con">
                        <span class="sign lbracket">(</span>
                        <span class="sign rbracket">)</span>
                        <span class="delet"></span>
                        <span class="empty">清空</span>
                        <span class="num">7</span>
                        <span class="num">8</span>
                        <span class="num">9</span>
                        <span class="sign sign-add">+</span>
                        <span class="num">4</span>
                        <span class="num">5</span>
                        <span class="num">6</span>
                        <span class="sign sign-cancle">－</span>
                        <span class="num">1</span>
                        <span class="num">2</span>
                        <span class="num">3</span>
                        <span class="sign sign-mul">×</span>
                        <span class="num zero">0</span>
                        <span class="num spot">.</span>
                        <span class="sign sign-except">÷</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="pu-btn">
            <button class="pu-cancel">取消</button>
            <button class="pu-sure">确定</button>
        </div>
    </div>
</div>
<!--<div class="popup addLevelInfo">-->
<!--    <div class="pu-title">添加等级信息</div>-->
<!--    <div class="pu-btn">-->
<!--        <button class="pu-cancel">取消</button>-->
<!--        <button class="pu-sure">确定</button>-->
<!--    </div>-->
<!--</div>-->
</body>
<script type="text/html" id="tmplToolBar">
    <div class="oprate-table">
        <div class="edit" lay-event="edit">编辑</div>
        <div class="delet" lay-event="delet">删除</div>
    </div>
</script>
<script th:src="@{~/js/jquery-3.3.1.min.js}"></script>
<script th:src="@{~/js/creditManage/common.js}"></script>
<script th:inline="javascript">

    layui.use(["table", "jquery", "form"], function () {
        var table = layui.table;
        var $ = layui.jquery;
        var form = layui.form;

        var sourceTypeScore = "分数";
        var sourceTypeLevel = "等级";
        var sourceTypeStudyTime = "学时";

        var zw = new RegExp("[\u4E00-\u9FA5]+");

        // 从页面中取得csId和学年学期参数值
        var ruleSetId = [[${id}]];
        var csId = [[${csId}]];
        var term = [[${term}]];

        let insertIdCount = -1;

        let deleteDataList = [];
        let updateDataList = [];
        let insertDataList = [];

        var table = layui.table,
            $ = layui.jquery;
        let tabData = [];
        // 当来源数据类型选择【分数】时，规则设置中需要显示的列信息
        var scoreCols = [
            [
                {
                    field: "id",
                    title: "id",
                    align: "center",
                    hide: true
                },
                {
                    field: "gradeInterval",
                    title: "成绩区间",
                    align: "center",
                    hide: true
                },
                {
                    field: "calculationFormula",
                    title: "计算公式",
                    align: "center",
                    hide: true
                },
                {
                    field: "gradeIntervalShow",
                    title: "成绩区间",
                    align: "center",
                },
                {
                    field: "calculationFormulaShow",
                    title: "计算公式",
                    align: "center",
                },
                {
                    title: "操作",
                    align: "center",
                    toolbar: "#tmplToolBar",
                },
            ]
        ];
        // 当来源数据类型选择【等级】时，规则设置中需要显示的列信息
        var levelCols = [
            [
                {
                    field: "id",
                    title: "id",
                    align: "center",
                    hide: true
                },
                {
                    field: "calculationFormula",
                    title: "计算公式",
                    align: "center",
                    hide: true
                },
                {
                    field: "levelInstitutionCode",
                    title: "级制代码",
                    align: "center",
                    hide: true
                },
                {
                    field: "levelInstitutionName",
                    title: "级制名称",
                    align: "center",
                },
                {
                    field: "levelName",
                    title: "等级名称",
                    align: "center",
                },
                {
                    field: "calculationFormulaShow",
                    title: "计算公式",
                    align: "center",
                },
                {
                    title: "操作",
                    align: "center",
                    toolbar: "#tmplToolBar",
                },
            ]
        ];
        // 当来源数据类型选择【学时】时，规则设置中需要显示的列信息
        var studyTimeCols = [
            [
                {
                    field: "id",
                    title: "id",
                    align: "center",
                    hide: true
                },
                {
                    field: "calculationFormula",
                    title: "计算公式",
                    align: "center",
                    hide: true
                },
                {
                    field: "studyTimeInterval",
                    title: "学时区间",
                    align: "center",
                    hide: true
                },
                {
                    field: "studyTimeIntervalShow",
                    title: "学时区间",
                    align: "center",
                },
                {
                    field: "calculationFormulaShow",
                    title: "计算公式",
                    align: "center",
                },
                {
                    title: "操作",
                    align: "center",
                    toolbar: "#tmplToolBar",
                },
            ]
        ]
        // table
        table.render({
            elem: '#otherTable',
            // url: '../../demo/table/user/-page=1&limit=20.js',
            data: tabData,
            cols: scoreCols,
            page: false,
            limit: Number.MAX_VALUE,
            done: function (res, curr, count) {

            }
        });

        table.on('tool(otherTable)', function (obj) {
            var rowData = obj.data;
            if (obj.event === 'edit') {
                editFn(rowData);
            } else if (obj.event === 'delet') {
                layer.confirm('确定删除吗？', function (index) {
                    var sourceDataType = $("#sourceDataType~.j-select-year ul li.active").text();
                    if (sourceDataType === sourceTypeScore || sourceDataType === sourceTypeStudyTime) {
                        if (rowData.id < 0) {
                            for (let i = 0; i < insertDataList.length; i++) {
                                if (insertDataList[i].id === rowData.id) {
                                    insertDataList.splice(i, 1);
                                }
                            }
                        } else {
                            deleteDataList.push(rowData.id);
                        }
                        for (let i = 0; i < tabData.length; i++) {
                            if (tabData[i].id === rowData.id) {
                                tabData.splice(i, 1);
                                break;
                            }
                        }
                        obj.del();
                    } else if (sourceDataType === sourceTypeLevel) {
                        if (deleteDataList.indexOf(rowData.id) === -1) {
                            deleteDataList.push(rowData.id);
                        }
                        for (let i = 0; i < tabData.length; i++) {
                            if (tabData[i].id === rowData.id && tabData[i].levelName === rowData.levelName) {
                                tabData.splice(i, 1);
                                break;
                            }
                        }
                        // 当删除或更新级制的等级信息之后，重新填充需要传递的更新体参数
                        fillLevelUpdateData();
                        renderConversionRuleSetTable(tabData, sourceDataType);
                    }
                    layer.close(index);
                });
            }
        });

        // 级制-添加级制-添加等级表格
        table.on('tool(levelRuleSetTable)', function (obj) {
            var rowData = obj.data;
            if (obj.event === 'edit') {
                editFn(rowData, true);
            } else if (obj.event === 'delet') {
                layer.confirm('确定删除吗？', function (index) {
                    if (rowData.id < 0) {
                        for (let i = 0; i < insertDataList.length; i++) {
                            if (insertDataList[i].id === rowData.id) {
                                insertDataList.splice(i, 1);
                            }
                        }
                    } else {
                        deleteDataList.push(rowData.id);
                    }
                    for (let i = 0; i < tabData.length; i++) {
                        if (tabData[i].id === rowData.id) {
                            tabData.splice(i, 1);
                        }
                    }
                    obj.del();
                    layer.close(index);
                });
            }
        });

        /**
         * 点击编辑按钮
         */
        function editFn(rowData, isLevelTable) {
            var sourceDataType = $("#sourceDataType~.j-select-year ul li.active").text();
            if (sourceTypeScore === sourceDataType) {
                echoSourceTypeScoreEditData(rowData);
            } else if (sourceTypeLevel === sourceDataType && !isLevelTable) {
                echoSourceTypeLevelEditData(rowData);
            } else if (sourceTypeStudyTime === sourceDataType) {
                echoSourceTypeStudyTimeEditData(rowData);
            } else if (isLevelTable) {
                echoLevelNameEditData(rowData);
            }
        }

        // 下拉
        $(".formulaConversionScore,.formulaConversionStudyTime").on("click", ".popup-box .pu-con .lab .select-input .name", function (e) {
            $(this).parent().toggleClass("clicked");
            stopBubble(e);
        });

        $(".formulaConversionScore,.formulaConversionStudyTime").on("click", ".popup-box .pu-con .lab .oprate .select-input .select-dropdown .dropdown-list li",
            function (e) {
                $(this).addClass("cur").siblings().removeClass("cur");
                let kosl = $(this).text();
                if (kosl === '为空') {
                    kosl = '';
                    var sourceDataType = $("#sourceDataType~.j-select-year ul li.active").text();
                    if ($(this).hasClass("leftMarkEmptyLi")) {
                        if (sourceDataType === sourceTypeScore) {
                            $("#leftScore1").val(kosl);
                        } else if (sourceDataType === sourceTypeLevel) {
                            $("#leftScore3").val(kosl);
                        }
                    } else if ($(this).hasClass("rightMarkEmptyLi")) {
                        if (sourceDataType === sourceTypeScore) {
                            $("#rightScore1").val(kosl);
                        } else if (sourceDataType === sourceTypeStudyTime) {
                            $("#rightScore3").val(kosl);
                        }
                    }
                }
                $(this).parents(".select-input").find(".name").addClass("ckd");
                $(this).parents(".select-input").find(".name").text(kosl);
                $(this).parents(".select-input").removeClass("clicked");
            }
        );

        function stopBubble(e) {
            if (e && e.stopPropagation)
                e.stopPropagation();
            else {
                window.event.cancelBubble = true;
            }
        }

        $(document).on("click", function (event) {
            var _con = $('.select-input');
            if (!_con.is(event.target) && _con.has(event.target).length === 0) {
                $(".select-input").removeClass("clicked");
            }
        });

        // 成绩计算
        $(".formulaConversionScore,.formulaConversionLevel2,.formulaConversionStudyTime").on("click", ".popup-box .pu-con .lab .available .a-con ul li", function () {
            var sourceDataType = $("#sourceDataType~.j-select-year ul li.active").text();
            if ($(this).hasClass("disabled")) {
                return false;
            }
            // $(this).addClass("disabled");
            let txt = $(this).text();
            if (sourceDataType === sourceTypeScore) {
                $(".formulaConversionScore .popup-box .pu-con .lab .section .s-con").append('<span>' + txt + '</span>');
            } else if (sourceDataType === sourceTypeLevel) {
                $(".formulaConversionLevel2 .popup-box .pu-con .lab .section .s-con").append('<span>' + txt + '</span>');
            } else if (sourceDataType === sourceTypeStudyTime) {
                $(".formulaConversionStudyTime .popup-box .pu-con .lab .section .s-con").append('<span>' + txt + '</span>');
            }
        });

        // 后移
        $(".formulaConversionScore,.formulaConversionLevel2,.formulaConversionStudyTime").on("click", ".popup-box .pu-con .lab .keyboard .k-con .delet", function () {
            var sourceDataType = $("#sourceDataType~.j-select-year ul li.active").text();
            if (sourceDataType === sourceTypeScore) {
                $(".formulaConversionScore .popup-box .pu-con .lab .section .s-con").find("span").last().remove();
            } else if (sourceDataType === sourceTypeLevel) {
                $(".formulaConversionLevel2 .popup-box .pu-con .lab .section .s-con").find("span").last().remove();
            } else if (sourceDataType === sourceTypeStudyTime) {
                $(".formulaConversionStudyTime .popup-box .pu-con .lab .section .s-con").find("span").last().remove();
            }
        });

        // 清空
        $(".formulaConversionScore,.formulaConversionLevel2,.formulaConversionStudyTime").on("click", ".popup-box .pu-con .lab .keyboard .k-con .empty", function () {
            var sourceDataType = $("#sourceDataType~.j-select-year ul li.active").text();
            if (sourceDataType === sourceTypeScore) {
                $(".formulaConversionScore .popup-box .pu-con .lab .section .s-con").find("span").remove();
            } else if (sourceDataType === sourceTypeLevel) {
                $(".formulaConversionLevel2 .popup-box .pu-con .lab .section .s-con").find("span").remove();
            } else if (sourceDataType === sourceTypeStudyTime) {
                $(".formulaConversionScore .popup-box .pu-con .lab .section .s-con").find("span").remove();
            }
        });

        // 加减乘除
        $(".formulaConversionScore,.formulaConversionLevel2,.formulaConversionStudyTime").on("click", ".popup-box .pu-con .lab .keyboard .k-con .sign", function () {
            let signTxt = $(this).text();
            var sourceDataType = $("#sourceDataType~.j-select-year ul li.active").text();
            if (sourceDataType === sourceTypeScore) {
                $(".formulaConversionScore .popup-box .pu-con .lab .section .s-con").append('<span class="sign">' + signTxt + '</span>');
            } else if (sourceDataType === sourceTypeLevel) {
                $(".formulaConversionLevel2 .popup-box .pu-con .lab .section .s-con").append('<span class="sign">' + signTxt + '</span>');
            } else if (sourceDataType === sourceTypeStudyTime) {
                $(".formulaConversionStudyTime .popup-box .pu-con .lab .section .s-con").append('<span class="sign">' + signTxt + '</span>');
            }
        });

        // 输入数字
        $(".formulaConversionScore,.formulaConversionLevel2,.formulaConversionStudyTime").on("click", ".popup-box .pu-con .lab .keyboard .k-con .num", function () {
            let signTxt = $(this).text();
            let cls = $(this).attr("class");
            var sourceDataType = $("#sourceDataType~.j-select-year ul li.active").text();
            if (sourceDataType === sourceTypeScore) {
                $(".formulaConversionScore .popup-box .pu-con .lab .section .s-con").append('<span class="' + cls + '">' + signTxt + '</span>');
            } else if (sourceDataType === sourceTypeLevel) {
                $(".formulaConversionLevel2 .popup-box .pu-con .lab .section .s-con").append('<span class="' + cls + '">' + signTxt + '</span>');
            } else if (sourceDataType === sourceTypeStudyTime) {
                $(".formulaConversionStudyTime .popup-box .pu-con .lab .section .s-con").append('<span class="' + cls + '">' + signTxt + '</span>');
            }
        });

        // 弹窗隐藏
        $(".formulaConversionScore,.formulaConversionLevel,.formulaConversionLevel2,.formulaConversionStudyTime,.formulaConversionLevelSub").on("click", ".popup-box .pu-btn button", function () {
            var sourceDataType = $("#sourceDataType~.j-select-year ul li.active").text();
            // 是否为2级弹窗，如果是，得到true
            let subPopupFlag = $(this).parents(".formulaConversionLevelSub").length !== 0;
            let editLevelPopupFlag = $(this).parents(".formulaConversionLevel2").length !== 0;
            if ($(this).text() === "确定") {
                var flag = false;
                if (sourceTypeScore === sourceDataType) {
                    flag = handlerSourceTypeScoreSave(this, sourceDataType);
                } else if (sourceTypeLevel === sourceDataType && !subPopupFlag) {
                    if (editLevelPopupFlag) {
                        flag = handlerSourceTypeLevelSave2(this, sourceDataType);
                    } else {
                        flag = handlerSourceTypeLevelSave(this, sourceDataType);
                    }
                } else if (sourceTypeStudyTime === sourceDataType) {
                    flag = handlerSourceTypeStudyTimeSave(this, sourceDataType);
                } else if (subPopupFlag) {
                    var levelName = $("#levelName").val();
                    flag = renderLevelName(levelName);
                }
                if (flag) {
                    if (subPopupFlag) {
                        $(".formulaConversionLevelSub").hide();
                    } else {
                        $(".formulaConversionScore").hide();
                        $(".formulaConversionLevel").hide();
                        $(".formulaConversionLevel2").hide();
                        $(".formulaConversionStudyTime").hide();
                    }
                }
            } else {
                if (subPopupFlag) {
                    $(".formulaConversionLevelSub").hide();
                } else {
                    $(".formulaConversionScore").hide();
                    $(".formulaConversionLevel").hide();
                    $(".formulaConversionLevel2").hide();
                    $(".formulaConversionStudyTime").hide();
                }
            }
        });

        // 点击返回按钮，后退
        $(".back").click(function () {
            history.back();
        });

        // 点击【新增换算规则】按钮 或 点击【等级】的【添加级制】
        $(".main .con .c-item .c-top .btns-list span.add,#cTime .c-con .addLevelInstitution").click(function () {
            var sourceDataType = $("#sourceDataType~.j-select-year ul li.active").text();
            if (sourceTypeScore === sourceDataType) {
                $(".formulaConversionScore .popup-box .pu-title").text("添加换算规则");
                $(".formulaConversionScore .popup-box .pu-con .lab.t-lab").hide();
                $(".formulaConversionScore .popup-box .pu-con .lab .oprate .inputs .inp").val("");
                $(".formulaConversionScore .popup-box .pu-con .lab .oprate .inputs .inpf").val("");
                $(".formulaConversionScore .popup-box .pu-con .lab .oprate .tab-sel").find(".name").removeClass("ckd").text("原始成绩");
                $(".formulaConversionScore .popup-box .pu-con .lab .oprate .select-input .select-dropdown .dropdown-list li").eq(0).removeClass("cur");
                $(".formulaConversionScore .popup-box .pu-con .lab .section .s-con").empty();
                $(".formulaConversionScore .popup-box .pu-con .lab .available .a-con ul li").removeClass("disabled");
                $("#creditOtherRuleSetDataId1").val("");
                $(".formulaConversionScore").show();
            } else if (sourceTypeLevel === sourceDataType) {
                $(".formulaConversionLevel .popup-box .pu-title").text("添加级制");
                $("#creditOtherRuleSetDataId2").val("");
                $("#levelInstitutionName").val("");
                $(".formulaConversionLevel").show();
                echoSourceTypeLevelTwoData();
            } else if (sourceTypeStudyTime === sourceDataType) {
                $(".formulaConversionStudyTime .popup-box .pu-title").text("添加换算规则");
                $(".formulaConversionStudyTime .popup-box .pu-con .lab .oprate .inputs .inp").val("");
                $(".formulaConversionStudyTime .popup-box .pu-con .lab .oprate .inputs .inpf").val("");
                $(".formulaConversionStudyTime .popup-box .pu-con .lab .oprate .tab-sel").find(".name").removeClass("ckd").text("原始成绩");
                $(".formulaConversionStudyTime .popup-box .pu-con .lab .oprate .select-input .select-dropdown .dropdown-list li").eq(0).removeClass("cur");
                $(".formulaConversionStudyTime .popup-box .pu-con .lab .section .s-con").empty();
                $(".formulaConversionStudyTime .popup-box .pu-con .lab .available .a-con ul li").removeClass("disabled");
                $("#creditOtherRuleSetDataId3").val("");
                $(".formulaConversionStudyTime").show();
            }
        });

        // 选择【等级】时，点击添加级制
        $(".formulaConversionLevel .popup-box .pu-con .pu-con-bottom .addLevel").click(function () {
            $(".formulaConversionStudyTime .popup-box .pu-title").text("添加等级信息");
            $("#levelName").val("");
            $("#levelNameOld").val("");
            $(".formulaConversionLevelSub").show();
        });

        // 是否设定统一标准学分的开关
        $(".switc-con .switch").click(function () {
            if ($(this).hasClass("switch-open")) {
                $(this).removeClass("switch-open");
                $(this).next().text('关闭');
                $(this).parents(".lab").next().hide();
                $("#standardCreditLi1").css("display", "none");
                $("#standardCreditLi2").css("display", "none");
                $("#standardCreditLi3").css("display", "none");
            } else {
                $(this).addClass("switch-open");
                $(this).next().text('开启');
                $(this).parents(".lab").next().show();
                $("#standardCreditLi1").css("display", "block");
                $("#standardCreditLi2").css("display", "block");
                $("#standardCreditLi3").css("display", "block");
            }
        });

        // 点击保存设置按钮
        $("#saveBth").click(function () {
            let id = $("#creditDataSourceSetId").val();
            let institutionCode = $("#institutionCode").val();
            let institutionName = $("#institutionName").val();
            let sourceDataType = $("#sourceDataType").val();
            let isSetUnifyStandardCredit = $("#isSetUnifyStandardCredit").text() === "开启" ? 0 : 1;
            let standardCredit = $("#standardCredit").val();
            let checkStandardCredit = true;
            if (isSetUnifyStandardCredit === 0) {
                if (standardCredit === "") {
                    checkStandardCredit = false;
                } else {
                    let index = standardCredit.indexOf(".");
                    if (index === 0) {
                        checkStandardCredit = false;
                    } else if (index !== -1) {
                        // 截取小数点和之后的字符
                        let subStr = standardCredit.slice(index);
                        if (subStr.length > 3) {
                            checkStandardCredit = false;
                        }
                    }
                }
            }
            if (!sourceDataType) {
                layer.confirm('学分数据来源设置，来源数据类型未设置', {
                    btn: ['取消', '确定'] //按钮
                });
            } else if (sourceTypeLevel === sourceDataType && (!institutionCode || !institutionName)) {
                layer.confirm('请选择级制', {
                    btn: ['取消', '确定'] //按钮
                });
            } else if (!checkStandardCredit) {
                layer.confirm('标准学分分值只能设置整数，最多设置2位有效小数位数，请重新设置', {
                    btn: ['取消', '确定'] //按钮
                });
            } else if (tabData.length === 0) {
                layer.confirm('学分换算规则未设置，请设置后保存', {
                    btn: ['取消', '确定'] //按钮
                });
            } else if (!checkContinue(tabData)) {
                layer.confirm('成绩区间不连续，请检查', {
                    btn: ['取消', '确定'] //按钮
                });
            } else if (!checkGradeIntervalContinue(tabData)) {
                layer.confirm('成绩区间设置有误，请重新设置', {
                    btn: ['取消', '确定'] //按钮
                });
            } else {
                $.ajax({
                    type: 'post',
                    dataType: 'json',
                    data: {
                        "creditDataSourceSetJson": JSON.stringify({
                            "id": id,
                            "csId": csId,
                            "ruleSetId": ruleSetId,
                            "term": term,
                            "sourceDataType": sourceDataType,
                            "isSetUnifyStandardCredit": isSetUnifyStandardCredit,
                            "standardCredit": isSetUnifyStandardCredit === 0 ? standardCredit : undefined,
                            "institutionCode": institutionCode,
                            "institutionName": institutionName,
                        }),
                        "insertDataListJson": JSON.stringify(insertDataList),
                        "updateDataListJson": JSON.stringify(updateDataList),
                        "deleteDataListJson": JSON.stringify(deleteDataList)
                    },
                    url: '/new/credit/other/saveOtherCreditSet',
                    success: function (res) {
                        if (res.code === 200) {
                            layer.msg("保存成功", {icon: 1, time: 2000}, function () {
                                // 加载学分数据来源设置
                                loadCreditDataSourceSetData();
                                insertDataList = [];
                                updateDataList = [];
                                deleteDataList = [];
                                insertIdCount = -1;
                            });
                        } else {
                            layer.msg(res.msg, {icon: 2, time: 2000});
                        }
                    }
                });
            }
        });

        // 当点击来源数据类型下拉框时
        $("#sourceDataType~.j-select-year").on("click", "ul li", function () {
            loadCreditConversionRuleData();
            var sourceDataType = $("#sourceDataType~.j-select-year ul li.active").text();
            if (sourceTypeLevel === sourceDataType) {
                loadLevelInstitutionNameInfo($("#institutionName").val());
            }
            insertIdCount = -1;
            deleteDataList = [];
            updateDataList = [];
            insertDataList = [];
        });

        // 点击【等级】新增换算规则【请选择级制】下拉框时
        $(".formulaConversionLevel .popup-box .pu-con .pu-con-top .j-search-con .j-select-year,#cTime .c-con .j-search-con .j-select-year").on("click", "ul li", function () {
            $(this).find("i").addClass("selected");
            $(this).siblings("li").find("i").removeClass("selected");
            // 填充级制编码和级制名称
            $("#institutionCode").val($(this).find(".institution_code").val());
            $("#institutionName").val($(this).find(".institution_name").val());
            // 切换下拉框内容后，重新渲染换算规则设置表格，并初始化修改的数据和id
            loadCreditConversionRuleData();
            insertIdCount = -1;
            deleteDataList = [];
            updateDataList = [];
            insertDataList = [];
        });

        // 立即执行函数，初始化页面
        $(function () {
            // 加载学分数据来源设置
            loadCreditDataSourceSetData();

        });

        /**
         * 加载学分数据来源设置
         */
        function loadCreditDataSourceSetData() {
            // 获取学分数据来源设置
            $.ajax({
                type: 'get',
                dataType: 'json',
                url: '/new/credit/other/getCreditDataSourceSet?ruleSetId=' + ruleSetId + '&csId=' + csId,
                success: function (res) {
                    if (res.code === 200) {
                        let data = res.data;
                        if (data) {
                            $("#creditDataSourceSetId").val(data.id);
                            $("#institutionCode").val(data.institutionCode);
                            $("#institutionName").val(data.institutionName);
                            if (data.sourceDataType) {
                                $("#sourceDataType").val(data.sourceDataType);
                                var liList = $("#sourceDataType").siblings(".j-select-year").find("ul li");
                                for (var i = 0; i < liList.length; i++) {
                                    var ele = liList[i];
                                    if ($(ele).text() === data.sourceDataType) {
                                        $(ele).addClass("active");
                                    } else {
                                        $(ele).removeClass("active");
                                    }
                                }
                            }
                            let isSetUnifyStandardCreditEle = $(".switc-con .switch");
                            if (data.isSetUnifyStandardCredit === 0) {
                                $(isSetUnifyStandardCreditEle).next().text('开启');
                                $(isSetUnifyStandardCreditEle).parents(".lab").next().show();
                                $("#standardCreditLi1").css("display", "block");
                                $("#standardCreditLi2").css("display", "block");
                                $("#standardCreditLi3").css("display", "block");
                                if (!$(isSetUnifyStandardCreditEle).hasClass("switch-open")) {
                                    $(isSetUnifyStandardCreditEle).addClass("switch-open");
                                }
                            } else if (data.isSetUnifyStandardCredit === 1) {
                                $(isSetUnifyStandardCreditEle).next().text('关闭');
                                $(isSetUnifyStandardCreditEle).parents(".lab").next().hide();
                                $("#standardCreditLi1").css("display", "none");
                                $("#standardCreditLi2").css("display", "none");
                                $("#standardCreditLi3").css("display", "none");
                                if ($(isSetUnifyStandardCreditEle).hasClass("switch-open")) {
                                    $(isSetUnifyStandardCreditEle).removeClass("switch-open");
                                }
                            }
                            if (data.standardCredit === 0 || data.standardCredit === "0.0") {
                                $("#standardCredit").val(0);
                            } else if (data.standardCredit) {
                                $("#standardCredit").val(data.standardCredit);
                            }
                            // 加载学分换算规则设置
                            loadCreditConversionRuleData(true);
                        } else {
                            $("#standardCreditLi1").css("display", "block");
                            $("#standardCreditLi2").css("display", "block");
                            $("#standardCreditLi3").css("display", "block");
                        }
                    } else {
                        layer.msg(res.msg, {icon: 2, time: 2000});
                    }
                }
            });
        }

        /**
         * 加载学分换算规则设置
         */
        function loadCreditConversionRuleData(init) {
            var sourceDataType = $("#sourceDataType~.j-select-year ul li.active").text();
            if (sourceDataType === sourceTypeLevel) {
                $("#cTime .c-con").css("display", "block");
                $("#cTime .c-top .btns-list").css("display", "none");
            } else {
                $("#cTime .c-con").css("display", "none");
                $("#cTime .c-top .btns-list").css("display", "block");
            }
            if (init) {
                sourceDataType = "";
            }
            var institutionCode = $("#institutionCode").val() ? $("#institutionCode").val() : "";
            // 获取学分换算规则设置
            $.ajax({
                type: 'get',
                dataType: 'json',
                url: '/new/credit/other/getCreditConversionRuleSet?ruleSetId=' + ruleSetId + '&csId=' + csId + "&sourceDataType=" + sourceDataType + "&institutionCode=" + institutionCode,
                success: function (res) {
                    if (res.code === 200) {
                        let data = res.data;
                        if (data) {
                            tabData = res.data;
                            sourceDataType = sourceDataType ? sourceDataType : $("#sourceDataType~.j-select-year ul li.active").text();
                            // 加载级制选择下拉框列表
                            if (init && sourceTypeLevel === sourceDataType) {
                                loadLevelInstitutionNameInfo($("#institutionName").val());
                            }
                            for (let i = 0; i < tabData.length; i++) {
                                var element = tabData[i];
                                try {
                                    if (sourceTypeScore === sourceDataType) {
                                        let gradeInterval = JSON.parse(element.gradeInterval);
                                        let gradeIntervalShow = "";
                                        gradeIntervalShow += gradeInterval.leftScore === undefined ? "" : gradeInterval.leftScore;
                                        gradeIntervalShow += gradeInterval.leftMark ? gradeInterval.leftMark : "";
                                        gradeIntervalShow += gradeInterval.scoreType ? gradeInterval.scoreType : "实习成绩";
                                        gradeIntervalShow += gradeInterval.rightMark ? gradeInterval.rightMark : "";
                                        gradeIntervalShow += gradeInterval.rightScore === undefined ? "" : gradeInterval.rightScore;
                                        element.gradeIntervalShow = gradeIntervalShow;
                                        $("#cTime .c-top .btns-list").css("display", "block");
                                        $("#cTime .c-con").css("display", "none");
                                    } else if (sourceTypeLevel === sourceDataType) {
                                        let levelInterval = element.levelInterval;
                                        $("#cTime .c-top .btns-list").css("display", "none");
                                        $("#cTime .c-con").css("display", "block");
                                    } else if (sourceTypeStudyTime === sourceDataType) {
                                        let studyTimeInterval = JSON.parse(element.studyTimeInterval);
                                        let studyTimeIntervalShow = "";
                                        studyTimeIntervalShow += studyTimeInterval.leftScore === undefined ? "" : studyTimeInterval.leftScore;
                                        studyTimeIntervalShow += studyTimeInterval.leftMark ? studyTimeInterval.leftMark : "";
                                        studyTimeIntervalShow += studyTimeInterval.scoreType ? studyTimeInterval.scoreType : "实习成绩";
                                        studyTimeIntervalShow += studyTimeInterval.rightMark ? studyTimeInterval.rightMark : "";
                                        studyTimeIntervalShow += studyTimeInterval.rightScore === undefined ? "" : studyTimeInterval.rightScore;
                                        element.studyTimeIntervalShow = studyTimeIntervalShow;
                                        $("#cTime .c-top .btns-list").css("display", "block");
                                        $("#cTime .c-con").css("display", "none");
                                    }
                                    element.calculationFormulaShow = JSON.parse(element.calculationFormula).join("");
                                } catch (e) {
                                    if (sourceTypeScore === sourceDataType) {
                                        element.gradeIntervalShow = element.gradeInterval;
                                    } else if (sourceTypeLevel === sourceDataType) {
                                        // 等级暂时不需要额外处理
                                    } else if (sourceTypeStudyTime === sourceDataType) {
                                        element.studyTimeIntervalShow = element.studyTimeInterval;
                                    }
                                    element.calculationFormulaShow = element.calculationFormula;
                                }
                            }
                            renderConversionRuleSetTable(tabData, sourceDataType);
                        }
                    } else {
                        layer.msg(res.msg, {icon: 2, time: 2000});
                    }
                }
            });
        }

        /**
         * 根据选择的【来源数据类型】，返回需要渲染的换算规则设置列字段数组
         */
        function getTargetCols(type) {
            if (type === 1 || type === sourceTypeScore) {
                return scoreCols;
            } else if (type === 2 || type === sourceTypeLevel) {
                return levelCols;
            } else if (type === 3 || type === sourceTypeStudyTime) {
                return studyTimeCols;
            }
        }

        /**
         * 渲染学分换算规则设置表格
         */
        function renderConversionRuleSetTable(tabData, sourceDataType) {
            table.render({
                elem: '#otherTable',
                // url: '../../demo/table/user/-page=1&limit=20.js',
                data: tabData,
                cols: getTargetCols(sourceDataType),
                page: false,
                limit: Number.MAX_VALUE,
                done: function (res, curr, count) {
                    if (sourceTypeLevel === sourceDataType) {
                        /* 需要合并栏目的数组信息 */
                        var merge_column_infos = [
                            {
                                /* 该字段起到一个标注作用，类似于注释 */
                                field: "levelInstitutionName",
                                index: 3 /* 需要合并栏目的索引 */
                            }
                        ];
                        /* 从第几行开始遍历合并 去除表头就是 1 咯 */
                        var start_tr_index = 1;
                        /* 查找到需要合并表格所有的 tr */
                        var tr_s = $("div[lay-id=otherTable]").find("tr");
                        /* 开始遍历需要合并的栏目 */
                        for (var merge_item of merge_column_infos) {
                            var field = merge_item.field;
                            /* 需要合并栏目的索引 */
                            var index = merge_item.index;
                            /* 需要合并栏目的数量 */
                            var merge_num = 0;
                            /* 需要合并栏目的 td */
                            var merge_tds = [];
                            /* 开始遍历需要合并表格的所有 tr */
                            for (var i = start_tr_index; i < tr_s.length; i++) {
                                /* 当前 td */
                                var cur_td = tr_s.eq(i).find("td").eq(index);
                                /* 下一个 td */
                                var next_td = tr_s.eq(i + 1).find("td").eq(index);
                                /* 当前 td 的 text */
                                var cur_text = $(cur_td).text();
                                /* 下一个 td 的 text当遍历到最后默认为空 */
                                var next_text = $(next_td).text();
                                /* 如果当前 td=下一个 td */
                                if (cur_text == next_text) {
                                    /*放入到合并 td 的集合中 */
                                    merge_tds.push(cur_td);
                                    /* 需要合并的 td 数量加 1 */
                                    merge_num++;
                                } else {
                                    if (cur_text == '小计') {
                                        tr_s.eq(i).addClass('layui-tr-total')
                                        $(cur_td).attr("colspan", 5);
                                        $(cur_td).nextAll().addClass("layui-hide");
                                        $(".layui-table-fixed-r tr").eq(i).addClass('layui-tr-total').find(
                                            '.layui-clear-space')
                                            .hide();
                                    }
                                    /* 如果 如果当前 td!=下一个 td 且要合并的 td 数量不等于 0 */
                                    if (merge_num != 0) {
                                        /* 第一个 td 合并 因为动态添加 rowspan 属性是向下延申 */
                                        $(merge_tds[0]).attr("rowspan", merge_num + 1); /* 遍历所有的需要合并的 td 将他们的属性设置为 不可见 */
                                        for (var d = 1; d <
                                        merge_tds.length; d++) {
                                            $(merge_tds[d]).addClass("layui-hide");
                                        }
                                        /* 当前 td 属性也需要设置为不可见 */
                                        $(cur_td).addClass("layui-hide");
                                    }
                                    /* 重置合并 td 数据 */
                                    merge_num = 0;
                                    merge_tds = [];
                                }
                            }
                        }
                        // 定位表格高度
                        $(".layui-table-main tr").each(function (index, val) {
                            $($(".layui-table-fixed .layui-table-body tbody tr")[index]).height($(val).height());
                        });
                    }
                }
            });
        }

        /**
         * 当分数类型，新增换算规则点击确定时
         */
        function handlerSourceTypeScoreSave(_this, sourceDataType) {
            let creditOtherRuleSetDataId = $("#creditOtherRuleSetDataId1").val();
            let leftScore = $("#leftScore1").val();
            let leftMark = $("#leftMark1").text();
            let scoreType = $("#scoreType1").text();
            let rightMark = $("#rightMark1").text();
            let rightScore = $("#rightScore1").val();
            let gradeInterval = {};
            let gradeIntervalShow = "";
            // 校验成绩区间是否正确
            if (checkGradeInterval(leftScore, leftMark, rightMark, rightScore)) {
                gradeIntervalShow += (leftScore !== "" && leftScore !== undefined ? leftScore : "");
                gradeInterval.leftScore = (leftScore !== "" && leftScore !== undefined ? leftScore : "");
                gradeIntervalShow += (leftMark !== "" ? leftMark : "");
                gradeInterval.leftMark = (leftMark !== "" ? leftMark : "");
                gradeIntervalShow += (scoreType !== "" ? scoreType : "");
                gradeInterval.scoreType = (scoreType !== "" ? scoreType : "");
                gradeIntervalShow += (rightMark !== "" ? rightMark : "");
                gradeInterval.rightMark = (rightMark !== "" ? rightMark : "");
                gradeIntervalShow += (rightScore !== "" && rightScore !== undefined ? rightScore : "");
                gradeInterval.rightScore = (rightScore !== "" && rightScore !== undefined ? rightScore : "");
                // console.log("正确的成绩区间：" + gradeIntervalShow);
            } else {
                layer.msg("成绩区间设置不正确：" + leftScore + leftMark + scoreType + rightMark + rightScore, {
                    icon: 2,
                    time: 2000
                });
                return false;
            }
            gradeInterval = JSON.stringify(gradeInterval);
            let calculationFormula = ["实际学分", "="];
            let calculationFormulaShow = "";
            $(".formulaConversionScore .popup-box .pu-con .lab .section .s-con").find("span").each(function () {
                calculationFormulaShow += $(this).text();
                calculationFormula.push($(this).text());
            });
            calculationFormula = JSON.stringify(calculationFormula);
            // 校验计算公式是否正确
            if (checkFormula(calculationFormulaShow)) {
                // console.log("正确的计算公式：" + calculationFormulaShow);
            } else {
                layer.msg("计算公式设置不正确：" + calculationFormulaShow, {
                    icon: 2,
                    time: 2000
                });
                return false;
            }
            calculationFormulaShow = "实际学分=" + calculationFormulaShow;
            // 如果id不为空，则是更新数据；否则是新增数据
            if (creditOtherRuleSetDataId) {
                creditOtherRuleSetDataId = parseInt(creditOtherRuleSetDataId);
                for (let i = 0; i < tabData.length; i++) {
                    let rowData = tabData[i];
                    if (rowData.id === creditOtherRuleSetDataId) {
                        rowData.gradeInterval = gradeInterval;
                        rowData.gradeIntervalShow = gradeIntervalShow;
                        rowData.calculationFormula = calculationFormula;
                        rowData.calculationFormulaShow = calculationFormulaShow;

                        // 编辑需要新增的数据,id从-1开始递减
                        if (creditOtherRuleSetDataId < 0) {
                            for (let j = 0; j < insertDataList.length; j++) {
                                let insertRowData = insertDataList[j];
                                if (insertRowData.id === creditOtherRuleSetDataId) {
                                    insertRowData.gradeInterval = gradeInterval;
                                    insertRowData.gradeIntervalShow = gradeIntervalShow;
                                    insertRowData.calculationFormula = calculationFormula;
                                    insertRowData.calculationFormulaShow = calculationFormulaShow;
                                    break;
                                }
                            }
                        } else {
                            let flag = false;
                            for (let j = 0; j < updateDataList.length; j++) {
                                let updateRowData = updateDataList[j];
                                if (updateRowData.id === creditOtherRuleSetDataId) {
                                    updateRowData.gradeInterval = gradeInterval;
                                    updateRowData.gradeIntervalShow = gradeIntervalShow;
                                    updateRowData.calculationFormula = calculationFormula;
                                    updateRowData.calculationFormulaShow = calculationFormulaShow;
                                    flag = true;
                                    break;
                                }
                            }
                            if (!flag) {
                                let updateRowData = {};
                                updateRowData.id = creditOtherRuleSetDataId;
                                updateRowData.gradeInterval = gradeInterval;
                                updateRowData.gradeIntervalShow = gradeIntervalShow;
                                updateRowData.calculationFormula = calculationFormula;
                                updateRowData.calculationFormulaShow = calculationFormulaShow;
                                updateDataList.push(updateRowData);
                            }
                        }
                        break;
                    }
                }
            } else {
                let insertRowData = {};
                insertRowData.id = insertIdCount--;
                insertRowData.gradeInterval = gradeInterval;
                insertRowData.gradeIntervalShow = gradeIntervalShow;
                insertRowData.calculationFormula = calculationFormula;
                insertRowData.calculationFormulaShow = calculationFormulaShow;
                insertDataList.push(insertRowData);
                tabData.push(insertRowData);
            }
            console.log(tabData);
            renderConversionRuleSetTable(tabData, sourceDataType);
            return true;
        }

        /**
         * 当等级类型，添加级制点击确定时
         */
        function handlerSourceTypeLevelSave(_this, sourceDataType) {
            var flag = false;
            var levelInstitutionCode = $("#levelInstitutionCode").val();
            // 校验级制名称是否重复
            var levelInstitutionName = $("#levelInstitutionName").val();
            if (!levelInstitutionName) {
                layer.msg("请填写级制名称", {icon: 2, time: 2000});
                return flag;
            }
            $.ajax({
                type: 'get',
                dataType: 'json',
                data: {"levelInstitutionName": levelInstitutionName},
                url: '/new/credit/other/check/AddLevelInstitution',
                async: false,
                success: function (res) {
                    if (res.code === 200) {
                        var data = res.data;
                        flag = data.flag;
                    } else {
                        layer.msg(res.msg, {icon: 2, time: 2000});
                    }
                }
            });
            if (!flag) {
                layer.msg("级制名称重复", {icon: 2, time: 2000});
            } else {
                // 校验
                var levelNameDataArray = table.getData("levelRuleSetTable");
                if (!levelNameDataArray || levelNameDataArray.length === 0) {
                    layer.msg("请至少添加1条等级", {icon: 2, time: 2000});
                    flag = false;
                }
                // else {
                //     for (let i = 0; i < levelNameDataArray.length; i++) {
                //         let insertRowData = {
                //             id: insertIdCount--,
                //             levelInstitutionCode: $("#newLevelInstitutionCode").val(),
                //             levelInstitutionName: levelInstitutionName,
                //             levelName: levelNameDataArray[i].levelName
                //         };
                //         insertDataList.push(insertRowData);
                //         tabData.unshift(insertRowData);
                //     }
                //     renderConversionRuleSetTable(tabData, sourceDataType);
                // }
                if (flag) {
                    // 添加新的级制到表单中
                    $.ajax({
                        type: 'post',
                        dataType: 'json',
                        data: {
                            "levelInstitutionCode": $("#newLevelInstitutionCode").val(),
                            "levelInstitutionName": levelInstitutionName,
                            "levelNameDataArray": JSON.stringify(levelNameDataArray),
                        },
                        url: '/new/credit/other/save/levelInstitutionToForm',
                        success: function (res) {
                            if (res.code === 200) {
                                layer.msg("添加级制成功", {icon: 1, time: 2000}, function () {
                                    // 添加级制成功之后，刷新下拉框列表数据
                                    loadLevelInstitutionNameInfo($("#institutionName").val());
                                });
                                var data = res.data;
                                flag = data.flag;
                            } else {
                                layer.msg(res.msg, {icon: 2, time: 2000});
                            }
                        }
                    });
                }
            }
            return flag;
        }

        /**
         * 当等级类型，新增换算规则点击确定时
         */
        function handlerSourceTypeLevelSave2(_this, sourceDataType) {
            var flag = false;
            var levelInstitutionName = $("#levelInstitutionName2").val();
            var levelInstitutionNameOld = $("#levelInstitutionNameOld2").val();
            var levelName = $("#levelName2").val();
            var levelNameOld = $("#levelNameOld2").val();
            var calculationFormulaOld = $("#calculationFormulaOld2").val();
            var calculationFormulaShowOld = $("#calculationFormulaShowOld2").val();
            if (!levelInstitutionName) {
                layer.msg("请填写级制名称", {icon: 2, time: 2000});
                return flag;
            } else if (!levelName) {
                layer.msg("请填写等级名称", {icon: 2, time: 2000});
                return flag;
            }
            // 处理计算公式
            let calculationFormula = ["实际学分", "="];
            let calculationFormulaShow = "";
            $(".formulaConversionLevel2 .popup-box .pu-con .lab .section .s-con").find("span").each(function () {
                calculationFormulaShow += $(this).text();
                calculationFormula.push($(this).text());
            });
            calculationFormula = JSON.stringify(calculationFormula);
            // 校验计算公式是否正确
            if (checkFormula(calculationFormulaShow)) {
                // console.log("正确的计算公式：" + calculationFormulaShow);
            } else {
                layer.msg("计算公式设置不正确：" + calculationFormulaShow, {
                    icon: 2,
                    time: 2000
                });
                return false;
            }
            calculationFormulaShow = "实际学分=" + calculationFormulaShow;
            let creditOtherRuleSetDataId = $("#creditOtherRuleSetDataId2_1").val();
            var tableData = table.getData("otherTable");
            // 将改动的数据放到更新列表或新增列表中
            if (creditOtherRuleSetDataId) {
                creditOtherRuleSetDataId = parseInt(creditOtherRuleSetDataId);
                if (creditOtherRuleSetDataId < 0) {
                    for (let i = 0; i < insertDataList.length; i++) {
                        let insertRowData = insertDataList[i];
                        if (insertRowData.id === creditOtherRuleSetDataId) {
                            insertRowData.levelInstitutionName = levelInstitutionName;
                            if (insertRowData.levelName === levelNameOld) {
                                insertRowData.levelName = levelName;
                                // 更新计算公式
                                insertRowData.calculationFormula = calculationFormula;
                                insertRowData.calculationFormulaShow = calculationFormulaShow;
                            }
                        }
                    }
                } else {
                    var tempUpdateList = [];
                    for (let i = 0; i < tableData.length; i++) {
                        let tempData = tableData[i];
                        if (tempData.id === creditOtherRuleSetDataId) {
                            let editFlag = false;
                            for (let i = 0; i < updateDataList.length; i++) {
                                let updateRowData = updateDataList[i];
                                if (updateRowData.id === creditOtherRuleSetDataId) {
                                    updateRowData.levelInstitutionName = levelInstitutionName;
                                    if (updateRowData.levelName === levelNameOld) {
                                        updateRowData.levelName = levelName;
                                        updateRowData.calculationFormula = calculationFormula;
                                        updateRowData.calculationFormulaShow = calculationFormulaShow;
                                        editFlag = true;
                                    }
                                    if (updateRowData.levelName === levelName) {
                                        editFlag = true;
                                    }
                                }
                            }
                            // 如果editFlag为false，则说明当前编辑的数据不在编辑数据列表中，需要新增
                            if (!editFlag) {
                                tempUpdateList.push({
                                    id: creditOtherRuleSetDataId,
                                    levelInstitutionName: levelInstitutionName,
                                    levelName: tempData.levelName === levelNameOld ? levelName : tempData.levelName,
                                    calculationFormula: tempData.calculationFormula === calculationFormulaShowOld ? calculationFormula : tempData.calculationFormula,
                                    calculationFormulaShow: tempData.calculationFormulaShow === calculationFormulaShowOld ? calculationFormulaShow : tempData.calculationFormulaShow,
                                });
                            }
                        }
                    }
                    updateDataList = updateDataList.concat(tempUpdateList);
                }
            }
            // 更新渲染页面的表格数据
            for (let i = 0; i < tableData.length; i++) {
                var tempData = tableData[i];
                if (tempData.id === creditOtherRuleSetDataId) {
                    tempData.levelInstitutionName = levelInstitutionName;
                    if (tempData.levelName === levelNameOld) {
                        tempData.levelName = levelName;
                        tempData.calculationFormula = calculationFormula;
                        tempData.calculationFormulaShow = calculationFormulaShow;
                    }
                }
            }
            renderConversionRuleSetTable(tableData, sourceDataType);
            // 当删除或更新级制的等级信息之后，重新填充需要传递的更新体参数
            fillLevelUpdateData();
            flag = true;
            return flag;
        }

        /**
         * 校验等级类型的换算规则设置保存的参数是否合法
         */
        function checkLevelParam(levelInstitutionName, levelName) {

        }

        /**
         * 当学时类型，新增换算规则点击确定时
         */
        function handlerSourceTypeStudyTimeSave(_this, sourceDataType) {
            let creditOtherRuleSetDataId = $("#creditOtherRuleSetDataId3").val();
            let leftScore = $("#leftScore3").val();
            let leftMark = $("#leftMark3").text();
            let scoreType = $("#scoreType3").text();
            let rightMark = $("#rightMark3").text();
            let rightScore = $("#rightScore3").val();
            let studyTimeInterval = {};
            let studyTimeIntervalShow = "";
            // 校验成绩区间是否正确
            if (checkGradeInterval(leftScore, leftMark, rightMark, rightScore)) {
                studyTimeIntervalShow += (leftScore !== "" && leftScore !== undefined ? leftScore : "");
                studyTimeInterval.leftScore = (leftScore !== "" && leftScore !== undefined ? leftScore : "");
                studyTimeIntervalShow += (leftMark !== "" ? leftMark : "");
                studyTimeInterval.leftMark = (leftMark !== "" ? leftMark : "");
                studyTimeIntervalShow += (scoreType !== "" ? scoreType : "");
                studyTimeInterval.scoreType = (scoreType !== "" ? scoreType : "");
                studyTimeIntervalShow += (rightMark !== "" ? rightMark : "");
                studyTimeInterval.rightMark = (rightMark !== "" ? rightMark : "");
                studyTimeIntervalShow += (rightScore !== "" && rightScore !== undefined ? rightScore : "");
                studyTimeInterval.rightScore = (rightScore !== "" && rightScore !== undefined ? rightScore : "");
            } else {
                layer.msg("成绩区间设置不正确：" + leftScore + leftMark + scoreType + rightMark + rightScore, {
                    icon: 2,
                    time: 2000
                });
                return false;
            }
            studyTimeInterval = JSON.stringify(studyTimeInterval);
            let calculationFormula = ["实际学分", "="];
            let calculationFormulaShow = "";
            $(".formulaConversionStudyTime .popup-box .pu-con .lab .section .s-con").find("span").each(function () {
                calculationFormulaShow += $(this).text();
                calculationFormula.push($(this).text());
            });
            calculationFormula = JSON.stringify(calculationFormula);
            // 校验计算公式是否正确
            if (checkFormula(calculationFormulaShow)) {
                // console.log("正确的计算公式：" + calculationFormulaShow);
            } else {
                layer.msg("计算公式设置不正确：" + calculationFormulaShow, {
                    icon: 2,
                    time: 2000
                });
                return false;
            }
            calculationFormulaShow = "实际学分=" + calculationFormulaShow;
            // 如果id不为空，则是更新数据；否则是新增数据
            if (creditOtherRuleSetDataId) {
                creditOtherRuleSetDataId = parseInt(creditOtherRuleSetDataId);
                for (let i = 0; i < tabData.length; i++) {
                    let rowData = tabData[i];
                    if (rowData.id === creditOtherRuleSetDataId) {
                        rowData.studyTimeInterval = studyTimeInterval;
                        rowData.studyTimeIntervalShow = studyTimeIntervalShow;
                        rowData.calculationFormula = calculationFormula;
                        rowData.calculationFormulaShow = calculationFormulaShow;
                        break;
                    }
                    if (creditOtherRuleSetDataId < 0) {
                        for (let j = 0; j < insertDataList.length; j++) {
                            let insertRowData = insertDataList[j];
                            if (insertRowData.id === creditOtherRuleSetDataId) {
                                insertRowData.studyTimeInterval = studyTimeInterval;
                                insertRowData.studyTimeIntervalShow = studyTimeIntervalShow;
                                insertRowData.calculationFormula = calculationFormula;
                                insertRowData.calculationFormulaShow = calculationFormulaShow;
                                break;
                            }
                        }
                    } else {
                        let flag = false;
                        for (let j = 0; j < updateDataList.length; j++) {
                            let updateRowData = updateDataList[j];
                            if (updateRowData.id === creditOtherRuleSetDataId) {
                                updateRowData.studyTimeInterval = studyTimeInterval;
                                updateRowData.studyTimeIntervalShow = studyTimeIntervalShow;
                                updateRowData.calculationFormula = calculationFormula;
                                updateRowData.calculationFormulaShow = calculationFormulaShow;
                                flag = true;
                                break;
                            }
                        }
                        if (!flag) {
                            let updateRowData = {};
                            updateRowData.id = creditOtherRuleSetDataId;
                            updateRowData.gradeInterval = studyTimeInterval;
                            updateRowData.gradeIntervalShow = studyTimeIntervalShow;
                            updateRowData.calculationFormula = calculationFormula;
                            updateRowData.calculationFormulaShow = calculationFormulaShow;
                            updateDataList.push(updateRowData);
                        }
                    }
                }
            } else {
                let insertRowData = {};
                insertRowData.id = insertIdCount--;
                insertRowData.studyTimeInterval = studyTimeInterval;
                insertRowData.studyTimeIntervalShow = studyTimeIntervalShow;
                insertRowData.calculationFormula = calculationFormula;
                insertRowData.calculationFormulaShow = calculationFormulaShow;
                insertDataList.push(insertRowData);
                tabData.push(insertRowData);
            }
            console.log(tabData);
            renderConversionRuleSetTable(tabData, sourceDataType);
            return true;
        }

        /**
         * 点击编辑按钮：回显分数类型的规则设置编辑
         */
        function echoSourceTypeScoreEditData(rowData) {
            $(".formulaConversionScore .popup-box .pu-title").text("编辑换算规则");
            $("#creditOtherRuleSetDataId1").val(rowData.id);
            // 回显成绩区间
            try {
                rowData.gradeInterval = rowData.gradeInterval.replaceAll("&lt;", "<");
                let gradeInterval = JSON.parse(rowData.gradeInterval);
                console.log(gradeInterval);
                if (gradeInterval.leftScore !== undefined) {
                    $("#leftScore1").val(gradeInterval.leftScore);
                }
                if (gradeInterval.leftMark !== undefined) {
                    $("#leftMark1").text(gradeInterval.leftMark);
                    $(".formulaConversionScore .popup-box .pu-con .lab .oprate .inputs .pre-sel .select-dropdown ul li").each(function () {
                        if ($(this).text() === gradeInterval.leftMark || ($(this).text() === "为空" && gradeInterval.leftMark === "")) {
                            if (!$(this).hasClass("cur")) {
                                $(this).addClass("cur");
                            }
                        } else {
                            $(this).removeClass("cur");
                        }
                    });
                }
                if (gradeInterval.scoreType) {
                    $("#scoreType1").text(gradeInterval.scoreType);
                }
                if (gradeInterval.rightMark !== undefined) {
                    $("#rightMark1").text(gradeInterval.rightMark);
                    $(".formulaConversionScore .popup-box .pu-con .lab .oprate .inputs .next-sel .select-dropdown ul li").each(function () {
                        if ($(this).text() === gradeInterval.rightMark || ($(this).text() === "为空" && gradeInterval.rightMark === "")) {
                            if (!$(this).hasClass("cur")) {
                                $(this).addClass("cur");
                            }
                        } else {
                            $(this).removeClass("cur");
                        }
                    });
                }
                if (gradeInterval.rightScore !== undefined) {
                    $("#rightScore1").val(gradeInterval.rightScore);
                }
            } catch (e) {
                throw new Error("解析成绩区间json数组失败：" + rowData.gradeInterval);
            }
            // 计算公式中的元素数组，例：["实际学分","=","原始成绩","÷","60","×","标准学分"]
            let calculationFormulaArray = JSON.parse(rowData.calculationFormula ? rowData.calculationFormula : "[]");
            // 回显计算公式
            try {
                let contextEle = $(".formulaConversionScore .popup-box .pu-con .lab .section .s-con");
                let numList = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "."];
                let markList = ["+", "-", "×", "÷", "(", ")"];
                contextEle.text("");
                for (let i = 0; i < calculationFormulaArray.length; i++) {
                    if (i === 0 || i === 1) {
                        continue;
                    }
                    let item = calculationFormulaArray[i];
                    if (numList.indexOf(item) !== -1) {
                        if (item === "0") {
                            $(contextEle).append('<span class="num zero">0</span>');
                        } else if (item === ".") {
                            $(contextEle).append('<span class="num spot">.</span>');
                        } else {
                            $(contextEle).append('<span class="num">' + item + '</span>');
                        }
                    } else if (markList.indexOf(item) !== -1) {
                        $(contextEle).append('<span class="sign">' + item + '</span>');
                    } else {
                        $(contextEle).append('<span>' + item + '</span>');
                    }
                }
            } catch (e) {
            }
            $(".formulaConversionScore .popup-box .pu-con .lab.t-lab").hide();
            $(".formulaConversionScore").show();
        }

        /**
         * 点击编辑按钮：回显等级类型的规则设置编辑
         */
        function echoSourceTypeLevelEditData(rowData) {
            $(".formulaConversionLevel2 .popup-box .pu-title").text("编辑换算规则");
            $("#creditOtherRuleSetDataId2_1").val(rowData.id);
            $("#levelInstitutionName2").val(rowData.levelInstitutionName ? rowData.levelInstitutionName : "");
            $("#levelInstitutionNameOld2").val(rowData.levelInstitutionName ? rowData.levelInstitutionName : "");
            $("#levelName2").val(rowData.levelName ? rowData.levelName : "");
            $("#levelNameOld2").val(rowData.levelName ? rowData.levelName : "");
            // 计算公式中的元素数组，例：["实际学分","=","原始成绩","÷","60","×","标准学分"]
            let calculationFormulaArray = JSON.parse(rowData.calculationFormula ? rowData.calculationFormula : "[]");
            // 回显计算公式
            try {
                let contextEle = $(".formulaConversionLevel2 .popup-box .pu-con .lab .section .s-con");
                let numList = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "."];
                let markList = ["+", "-", "×", "÷", "(", ")"];
                contextEle.text("");
                for (let i = 0; i < calculationFormulaArray.length; i++) {
                    if (i === 0 || i === 1) {
                        continue;
                    }
                    let item = calculationFormulaArray[i];
                    if (numList.indexOf(item) !== -1) {
                        if (item === "0") {
                            $(contextEle).append('<span class="num zero">0</span>');
                        } else if (item === ".") {
                            $(contextEle).append('<span class="num spot">.</span>');
                        } else {
                            $(contextEle).append('<span class="num">' + item + '</span>');
                        }
                    } else if (markList.indexOf(item) !== -1) {
                        $(contextEle).append('<span class="sign">' + item + '</span>');
                    } else {
                        $(contextEle).append('<span>' + item + '</span>');
                    }
                }
            } catch (e) {
            }
            $("#calculationFormulaOld2").val(rowData.calculationFormula ? JSON.stringify(rowData.levelName) : "");
            $("#calculationFormulaShowOld2").val(rowData.calculationFormulaShow ? rowData.calculationFormulaShow : "");
            $(".formulaConversionLevel2").show();
            echoSourceTypeLevelTwoData(rowData);
        }

        /**
         * 点击编辑按钮：回显学时类型的规则设置编辑
         */
        function echoSourceTypeStudyTimeEditData(rowData) {
            $(".formulaConversionStudyTime .popup-box .pu-title").text("编辑换算规则");
            $("#creditOtherRuleSetDataId3").val(rowData.id);
            // 回显成绩区间
            try {
                rowData.studyTimeInterval = rowData.studyTimeInterval.replaceAll("&lt;", "<");
                let studyTimeInterval = JSON.parse(rowData.studyTimeInterval);
                if (studyTimeInterval.leftScore !== undefined) {
                    $("#leftScore3").val(studyTimeInterval.leftScore);
                }
                if (studyTimeInterval.leftMark !== undefined) {
                    $("#leftMark3").text(studyTimeInterval.leftMark);
                    $(".formulaConversionStudyTime .popup-box .pu-con .lab .oprate .inputs .pre-sel .select-dropdown ul li").each(function () {
                        if ($(this).text() === studyTimeInterval.leftMark || ($(this).text() === "为空" && studyTimeInterval.leftMark === "")) {
                            if (!$(this).hasClass("cur")) {
                                $(this).addClass("cur");
                            }
                        } else {
                            $(this).removeClass("cur");
                        }
                    });
                }
                if (studyTimeInterval.scoreType) {
                    $("#scoreType3").text(studyTimeInterval.scoreType);
                }
                if (studyTimeInterval.rightMark !== undefined) {
                    $("#rightMark3").text(studyTimeInterval.rightMark);
                    $(".formulaConversionStudyTime .popup-box .pu-con .lab .oprate .inputs .next-sel .select-dropdown ul li").each(function () {
                        if ($(this).text() === studyTimeInterval.rightMark || ($(this).text() === "为空" && studyTimeInterval.rightMark === "")) {
                            if (!$(this).hasClass("cur")) {
                                $(this).addClass("cur");
                            }
                        } else {
                            $(this).removeClass("cur");
                        }
                    });
                }
                if (studyTimeInterval.rightScore !== undefined) {
                    $("#rightScore3").val(studyTimeInterval.rightScore);
                }
            } catch (e) {
                throw new Error("解析成绩区间json数组失败：" + rowData.studyTimeInterval);
            }
            // 计算公式中的元素数组，例：["实际学分","=","原始成绩","÷","60","×","标准学分"]
            let calculationFormulaArray = JSON.parse(rowData.calculationFormula ? rowData.calculationFormula : "[]");
            // 回显计算公式
            try {
                let contextEle = $(".formulaConversionStudyTime .popup-box .pu-con .lab .section .s-con");
                let numList = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "."];
                let markList = ["+", "-", "×", "÷", "(", ")"];
                contextEle.text("");
                for (let i = 0; i < calculationFormulaArray.length; i++) {
                    if (i === 0 || i === 1) {
                        continue;
                    }
                    let item = calculationFormulaArray[i];
                    if (numList.indexOf(item) !== -1) {
                        if (item === "0") {
                            $(contextEle).append('<span class="num zero">0</span>');
                        } else if (item === ".") {
                            $(contextEle).append('<span class="num spot">.</span>');
                        } else {
                            $(contextEle).append('<span class="num">' + item + '</span>');
                        }
                    } else if (markList.indexOf(item) !== -1) {
                        $(contextEle).append('<span class="sign">' + item + '</span>');
                    } else {
                        $(contextEle).append('<span>' + item + '</span>');
                    }
                }
            } catch (e) {
            }
            $(".formulaConversionStudyTime").show();
        }

        /**
         * 回显：添加级制-添加等级-编辑
         */
        function echoLevelNameEditData(rowData) {
            $("#levelName").val(rowData.levelName);
            $("#levelNameOld").val(rowData.levelName);
            $(".formulaConversionLevelSub").show();
        }

        /**
         * 加载渲染选择级制名称下拉框列表
         */
        function loadLevelInstitutionNameInfo(targetLevelInstitutionName) {
            $.ajax({
                type: 'get',
                dataType: 'json',
                data: {},
                url: '/new/credit/other/level/institution/name/list',
                success: function (res) {
                    if (res.code === 200) {
                        var targetUl = $("#cTime .c-con .j-search-con .j-select-year ul");
                        targetUl.empty();
                        for (var i = 0; i < res.data.length; i++) {
                            var temp = res.data[i];
                            if (temp.levelInstitutionName === targetLevelInstitutionName) {
                                targetUl.append("<li class='active'>" +
                                    "<input type='hidden' class='institution_code' value='" + temp.levelInstitutionCode + "'>" +
                                    "<input type='hidden' class='institution_name' value='" + temp.levelInstitutionName + "'>" +
                                    "<span class='institution_name'>" + temp.levelInstitutionName + "</span><i class='selected'></i>" +
                                    "</li>");
                                targetUl.parent().siblings(".schoolSel").val(targetLevelInstitutionName);
                            } else {
                                targetUl.append("<li>" +
                                    "<input type='hidden' class='institution_code' value='" + temp.levelInstitutionCode + "'>" +
                                    "<input type='hidden' class='institution_name' value='" + temp.levelInstitutionName + "'>" +
                                    "<span class='institution_name'>" + temp.levelInstitutionName + "</span><i></i>" +
                                    "</li>");
                            }
                        }
                    } else {
                        layer.msg(res.msg, {icon: 2, time: 2000});
                    }
                }
            });
        }

        /**
         * 渲染成绩类型为【等级】规则设置页面的【添加或编辑二级页面数据】
         */
        function echoSourceTypeLevelTwoData(rowData) {
            var codeDataList = $("#institutionCode").siblings("div.j-select-year").find("ul li input.institution_code");
            if (codeDataList.length === 0) {
                $("#newLevelInstitutionCode").val("XFJZ001");
            } else {
                var numArray = [];
                for (let i = 0; i < codeDataList.length; i++) {
                    var tempCode = $(codeDataList[i]).val();
                    if (!tempCode) {
                        continue;
                    }
                    var num = parseInt(tempCode.replace(/[^0-9]/ig, ""));
                    if (isNaN(num)) {
                        continue;
                    }
                    numArray.push(num);
                }
                numArray.sort(function (a, b) {
                    return a - b;
                });
                var targetNum = 0;
                var tempNum = 0;
                // numArray可能是[1,2,2,4],此时需要返回3
                for (var i = 0; i < numArray.length; i++) {
                    // 历史遗留重复编码
                    if (numArray[i] === tempNum) {
                        continue;
                    }
                    tempNum++;
                    if (numArray[i] !== tempNum) {
                        targetNum = tempNum;
                        break;
                    }
                }
                if (targetNum === 0) {
                    targetNum = numArray.length + 1;
                }
                targetNum = targetNum > 99 ? targetNum : (targetNum > 9 ? "0" + targetNum : "00" + targetNum);
                var targetCode = "XFJZ" + targetNum;
                $("#newLevelInstitutionCode").val(targetCode);
            }
            renderLevelRuleSetTable();
        }

        /**
         * 渲染级制下的等级名称表格
         */
        function renderLevelRuleSetTable(levelRuleSetTableData) {
            levelRuleSetTableData = levelRuleSetTableData ? levelRuleSetTableData : [];
            table.render({
                elem: '#levelRuleSetTable',
                // url: '../../demo/table/user/-page=1&limit=20.js',
                data: levelRuleSetTableData,
                cols: [
                    [
                        {
                            field: "levelName",
                            title: "等级名称",
                            align: "center",
                        },
                        {
                            title: "操作",
                            align: "center",
                            toolbar: "#tmplToolBar",
                        },
                    ]
                ],
                page: false,
                // limit: 1,
                done: function (res, curr, count) {

                }
            });
        }

        /**
         * 校验等级名称是否符合规则
         */
        function renderLevelName(levelName) {
            var flag = false;
            var levelNameOld = $("#levelNameOld").val();
            if (levelName) {
                var levelNameTableData = table.getData("levelRuleSetTable");
                var levelNameArray = [];
                if (levelNameTableData && levelNameTableData.length > 0) {
                    for (let i = 0; i < levelNameTableData.length; i++) {
                        levelNameArray.push(levelNameTableData[i].levelName);
                    }
                }
                // 如果是新增的等级名称，则新增渲染至表格中
                if (!levelNameOld && levelNameArray.indexOf(levelName) === -1) {
                    flag = true;
                    levelNameTableData.push({"levelName": levelName});
                    renderLevelRuleSetTable(levelNameTableData);
                }
                // 如果是编辑等级名称，无改变则不作处理，有改变则重新修改后渲染
                if (levelNameOld) {
                    flag = true;
                    if (levelNameOld !== levelName) {
                        for (let i = 0; i < levelNameTableData.length; i++) {
                            var tempLevelName = levelNameTableData[i].levelName;
                            if (tempLevelName === levelNameOld) {
                                levelNameTableData[i].levelName = levelName;
                                renderLevelRuleSetTable(levelNameTableData);
                                break;
                            }
                        }
                    }
                }
            }
            if (!flag) {
                layer.msg("名称不能重复且不能为空", {icon: 2, time: 2000});
            }
            return flag;
        }

        /**
         * 当删除或更新级制的等级信息之后，重新填充需要传递的更新体参数
         */
        function fillLevelUpdateData() {
            var targetIdList = [];
            if (updateDataList && updateDataList.length) {
                for (let i = 0; i < updateDataList.length; i++) {
                    if (targetIdList.indexOf(updateDataList[i].id) === -1) {
                        targetIdList.push(updateDataList[i].id);
                    }
                }
            }
            if (deleteDataList && deleteDataList.length) {
                for (let i = 0; i < deleteDataList.length; i++) {
                    if (targetIdList.indexOf(deleteDataList[i]) === -1) {
                        targetIdList.push(deleteDataList[i]);
                    }
                }
            }
            updateDataList = [];
            var tableData = table.getData("otherTable");
            for (let i = 0; i < tableData.length; i++) {
                if (targetIdList.indexOf(tableData[i].id) !== -1) {
                    updateDataList.push(tableData[i]);
                }
            }
        }

        /**
         * 检查成绩区间是否正确
         */
        function checkGradeInterval(leftScore, leftMark, rightMark, rightScore) {
            if (leftMark && (leftScore === "" || leftScore === undefined)) {
                return false;
            }
            if ((leftScore !== "" && leftScore !== undefined) && leftMark === "") {
                return false;
            }
            if (rightMark && (rightScore === "" || rightScore === undefined)) {
                return false;
            }
            if ((rightScore !== "" && rightScore !== undefined) && rightMark === "") {
                return false;
            }
            if (leftMark === "=" && rightMark === "=" || leftMark === "" && rightMark === "") {
                return false;
            }
            if ((leftScore !== "" && leftScore !== undefined) && (rightScore !== "" && rightScore !== undefined)) {
                if (leftScore - rightScore > 0) {
                    return false;
                }
            }
            return true;
        }

        /**
         * 保存之前，检查所有的成绩区间是否连续
         */
        function checkGradeIntervalContinue(tabData) {
            let result = true;
            var sourceDataType = $("#sourceDataType~.j-select-year ul li.active").text();
            if (tabData && tabData.length) {
                // 先获取第一个成绩区间范围，并放到总区间范围中
                let intervalCompare = {};
                if (sourceTypeScore === sourceDataType) {
                    intervalCompare = JSON.parse(tabData[0].gradeInterval);
                } else if (sourceTypeLevel === sourceDataType) {

                } else if (sourceTypeStudyTime === sourceDataType) {
                    intervalCompare = JSON.parse(tabData[0].studyTimeInterval);
                }
                let intervalArray = [];
                intervalArray.push(intervalCompare);
                for (let i = 1; i < tabData.length; i++) {
                    let interval = {};
                    if (sourceTypeScore === sourceDataType) {
                        interval = JSON.parse(tabData[i].gradeInterval);
                    } else if (sourceTypeLevel === sourceDataType) {

                    } else if (sourceTypeStudyTime === sourceDataType) {
                        interval = JSON.parse(tabData[i].studyTimeInterval);
                    }
                    let leftMarkSource = interval.leftMark === "&lt;" ? "<" : interval.leftMark;
                    let leftScoreSource = interval.leftScore;
                    let rightMarkSource = interval.rightMark === "&lt;" ? "<" : interval.rightMark;
                    let rightScoreSource = interval.rightScore;
                    // 循环判断当前成绩区间是否和总区间范围有交集冲突
                    for (let j = 0; j < intervalArray.length; j++) {
                        let compareTarget = intervalArray[j];
                        let leftMarkTarget = compareTarget.leftMark === "&lt;" ? "<" : compareTarget.leftMark;
                        let leftScoreTarget = compareTarget.leftScore;
                        let rightMarkTarget = compareTarget.rightMark === "&lt;" ? "<" : compareTarget.rightMark;
                        let rightScoreTarget = compareTarget.rightScore;

                        if (leftMarkTarget && !rightMarkTarget) {
                            // 对比区域只有左边，例如：60≤实习成绩
                            if (leftMarkSource && !rightMarkSource) {
                                result = false;
                                break;
                            } else if (rightMarkSource) {
                                let differ = leftScoreTarget - rightScoreSource;
                                if (differ === 0) {
                                    if (leftMarkTarget !== "<" && rightMarkSource !== "<") {
                                        result = false;
                                        break;
                                    }
                                } else if (differ < 0) {
                                    result = false;
                                    break;
                                }
                            }
                        } else if (!leftMarkTarget && rightMarkTarget) {
                            // 对比区域只有右边，例如：实习成绩≤60
                            if (leftMarkSource) {
                                let differ = rightScoreTarget - leftScoreSource;
                                if (differ === 0) {
                                    if (rightMarkTarget !== "<" && leftMarkSource !== "<") {
                                        result = false;
                                        break;
                                    }
                                } else if (differ > 0) {
                                    result = false;
                                    break;
                                }
                            } else if (!leftMarkSource && rightMarkSource) {
                                result = false;
                                break;
                            }
                        } else if (leftMarkTarget && rightMarkTarget) {
                            // 对比区域两边都有，例如：60≤实习成绩≤100
                            if (leftMarkSource && !rightMarkSource) {
                                let differ = rightScoreTarget - leftScoreSource;
                                if (differ === 0) {
                                    if (rightMarkTarget !== "<" && leftMarkSource !== "<") {
                                        result = false;
                                        break;
                                    }
                                } else if (differ > 0) {
                                    result = false;
                                    break;
                                }
                            } else if (!leftMarkSource && rightMarkSource) {
                                let differ = leftScoreTarget - rightScoreSource;
                                if (differ === 0) {
                                    if (leftMarkTarget !== "<" && rightMarkSource !== "<") {
                                        result = false;
                                        break;
                                    }
                                } else if (differ < 0) {
                                    result = false;
                                    break;
                                }
                            } else if (leftMarkSource && rightMarkSource) {
                                let differ1 = rightScoreTarget - leftScoreSource;
                                let differ2 = leftScoreTarget - rightScoreSource;
                                if (differ1 === 0) {
                                    if (rightMarkTarget !== "<" && leftMarkSource !== "<") {
                                        result = false;
                                        break;
                                    }
                                } else if (differ1 > 0) {
                                    if (differ2 === 0) {
                                        if (leftMarkTarget !== "<" && rightMarkSource !== "<") {
                                            result = false;
                                            break;
                                        }
                                    } else if (differ2 < 0) {
                                        result = false;
                                        break;
                                    }
                                }
                            }
                        }
                    }
                    // 如果有冲突，则跳出循环
                    if (!result) {
                        break;
                    } else {
                        intervalArray.push(interval);
                    }
                }
            }
            return result;
        }

        /**
         * 检查成绩区间是否连续
         */
        function checkContinue(tabData) {
            if (tabData.length < 1) {
                return true;
            }
            var sourceDataType = $("#sourceDataType~.j-select-year ul li.active").text();
            for (let i = 0; i < tabData.length; i++) {
                let flag = true;
                for (let j = 0; j < tabData.length - i - 1; j++) {
                    var curData = {};
                    var nextData = {};
                    if (sourceTypeScore === sourceDataType) {
                        curData = JSON.parse(tabData[j].gradeInterval);
                        nextData = JSON.parse(tabData[j + 1].gradeInterval);
                    } else if (sourceTypeLevel === sourceDataType) {
                        break;
                    } else if (sourceTypeStudyTime === sourceDataType) {
                        curData = JSON.parse(tabData[j].studyTimeInterval);
                        nextData = JSON.parse(tabData[j + 1].studyTimeInterval);
                    }
                    if (curData.leftScore === undefined || curData.leftScore === null || curData.leftScore === "") {
                        if (j === 0) {
                            continue;
                        }
                        let temp = tabData[j];
                        tabData[j] = tabData[0];
                        tabData[0] = temp;
                        flag = false;
                    } else if (parseFloat(curData.leftScore)> parseFloat(nextData.leftScore)) {
                        let temp = tabData[j];
                        tabData[j] = tabData[j + 1];
                        tabData[j + 1] = temp;
                        flag = false;
                    }
                }
                if (flag) {
                    break;
                }
            }
            for (let i = 1; i < tabData.length; i++) {
                var data = {};
                var preData = {};
                if (sourceTypeScore === sourceDataType) {
                    data = JSON.parse(tabData[i].gradeInterval.replaceAll("<", "&lt;"));
                    preData = JSON.parse(tabData[i - 1].gradeInterval.replaceAll("<", "&lt;"));
                } else if (sourceTypeLevel === sourceDataType) {
                    break;
                } else if (sourceTypeStudyTime === sourceDataType) {
                    data = JSON.parse(tabData[i].studyTimeInterval.replaceAll("<", "&lt;"));
                    preData = JSON.parse(tabData[i - 1].studyTimeInterval.replaceAll("<", "&lt;"));
                }
                if (data.leftMark === preData.rightMark && data.leftScore === preData.rightScore) {
                    return false;
                }
                if (data.leftMark !== "≤" && preData.rightMark !== "≤") {
                    return false;
                }
                if ((data.leftMark === "≤" && preData.rightMark === "<" || data.leftMark === "<" && preData.rightMark === "≤") && data.leftScore !== preData.rightScore) {
                    return false;
                }
            }
            return true;
        }

        /**
         * 验证公式是否正确
         */
        function checkFormula(string) {
            // 剔除空白符
            string = string.replace(/\s/g, ' ');

            // 错误情况，空字符串
            if ("" === string) {
                return false;
            }

            // 错误情况，运算符连续
            if (/[\+\×\÷\-\－\*\/]{2,}/.test(string)) {
                return false;
            }

            // 空括号
            if (/\(\)/.test(string)) {
                return false;
            }

            // 错误情况，括号不配对
            var stack = [];
            for (var i = 0, item; i < string.length; i++) {
                item = string.charAt(i);
                if ('(' === item) {
                    stack.push('(');
                } else if (')' === item) {
                    if (stack.length > 0) {
                        stack.pop();
                    } else {
                        return false;
                    }
                }
            }
            if (0 !== stack.length) {
                return false;
            }

            // 错误情况，(后面是运算符
            if (/\([\+\×\÷\-\－\*\/]/.test(string)) {
                return false;
            }

            // 错误情况，)前面是运算符
            if (/[\+\×\÷\-\－\*\/]\)/.test(string)) {
                return false;
            }

            // 错误情况，(前面不是运算符
            if (/[^\+\×\÷\-\－\*\/]\(/.test(string)) {
                return false;
            }

            // 错误情况，)后面不是运算符
            if (/\)[^\+\×\÷\-\－\*\/]/.test(string)) {
                return false;
            }

            //错误情况，运算符号不能在首末位
            if (/^[\+\×\÷\-\*\/.]|[\+\×\÷\-\－\*\/.]$/.test(string)) {
                return false;
            }

            for (var i = 0; i < string.length; i++) {
                if (zw.test(string.charAt(i))) {
                    var item = string.charAt(i);
                    //判断中文前是不是运算符
                    if (i != 0 && !zw.test(string.charAt(i - 1)) && !/[\+\×\÷\-\－\*\/]/.test(string.charAt(i - 1))) {
                        return false;
                    }
                    //判断中文后是不是运算符
                    if (i != string.length - 1 && !zw.test(string.charAt(i + 1)) && !/[\+\×\÷\-\－\*\/]/.test(string.charAt(i + 1))) {
                        return false;
                    }
                    if (item == '原' || item == '标') {
                        //判断下一组数据是否还是中文
                        if (zw.test(string.charAt(i + 4))) {
                            return false;
                        }
                        i += 2;
                    }
                }
            }
            return true;
        }
    });
</script>
</html>