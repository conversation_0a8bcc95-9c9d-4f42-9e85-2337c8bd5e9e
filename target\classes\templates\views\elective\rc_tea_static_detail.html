<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
  <meta charset="UTF-8">
  <meta name="viewport"
    content="width=device-width, user-scalable=no,initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
  <title>教师端-统计-详情</title>
  <script src="/elective/rollcall/js/responsive.js"></script>
  <link rel="stylesheet" href="/elective/rollcall/css/global.css">
  <link rel="stylesheet" href="/elective/rollcall/css/iosSelect.css">
  <link rel="stylesheet" href="/elective/rollcall/css/common.css">
  <link rel="stylesheet" href="/elective/rollcall/css/head.css">
  <link rel="stylesheet" href="/elective/rollcall/css/mescroll.min.css">
  <link rel="stylesheet" href="/elective/rollcall/css/student-statistics.css?v=1">
  <style>
    .stu-list-wrap .total {
      height: 0.82rem;
      line-height: 0.82rem;
      margin-bottom: 0;
    }

    .stu-list-wrap .stu-list .stu .course {
      overflow: hidden;
      margin-top: 0.16rem;
    }

    .stu-list-wrap .stu-list .stu .course span {
      float: left;
      height: 0.39rem;
      line-height: 0.39rem;
      font-size: 0.28rem;
      color: #4e5969;
      margin-right: 0.16rem;
    }
    .stu-list .stu.for-leave::after {
      content: "";
      width: .96rem;
      height: .96rem;
      background: url(/elective/rollcall/images/subtract.png) no-repeat center;
      background-size: .96rem;
      position: absolute;
      right: 0;
      top: 0;
    }
    .stu-list .stu.for-leave::before {
      content: "已点名";
      font-size: 0.24rem;
      transform: rotate(45deg);
      color: rgb(255, 255, 255);
      position: absolute;
      z-index: 99;
      right: -0.15rem;
      top: 0.2rem;
      letter-spacing: 0.06rem;
    }
  </style>
</head>

<body>
  <div class="header">
    <div class="head-signal"></div>
    <div class="head-con">
      <a href="javascript:void(0)" onclick="AppUtils.closeView();" class="head-return">
        <img src="/elective/rollcall/images/back-icon1.png" alt="">
      </a>
      <span class="head-title">
        <span th:text="${teaName}"></span>
      </span>
    </div>
  </div>

  <div class="con-select">
    <ul>
      <li data-cla="course" class="course"><span>课程</span> <i></i></li>
      <li data-cla="date" id="selDate" data-start_id="10029" data-end_id="20029" class=""><span>日期</span> <i></i>
      </li>
    </ul>
  </div>

  <div class="stu-list-wrap mescroll" style="padding-top:0;" id="mescroll">

    <div class="total">
      <span>共<i>0</i>名学生</span>
    </div>

    <div class="stu-list">
    </div>
  </div>

  <div class="select-courses-dialog dialog-box" id="selectCourse">
    <div class="w-con active">
      <div class="w-head">
        <div class="cancle">取消</div>
        <div class="name">选择课程</div>
        <div class="btns">
          <span class="save">保存</span>
        </div>
      </div>
      <div class="w-box">
        <div class="all-select">全选</div>
        <ul id="pkjgsj_kcmc">
        </ul>
      </div>

    </div>
  </div>

</body>
<script src="/elective/rollcall/js/CXJSBridge.js"></script>
<script src="/elective/rollcall/js/jquery-3.3.1.min.js"></script>
<script src="/elective/rollcall/js/headHeight.js"></script>
<script src="/elective/rollcall/js/mescroll.min.js"></script>
<script type="text/javascript" src="/js/app.utils.js"></script>
<script src="/elective/rollcall/js/iosSelect1.js"></script>
<script th:inline="javascript">
  var searchArr = [
    {formAlias: "pkjgsj", fieldAlias: "pkjgsj_kcmc"}
  ];
  var teaUid = [[${teaUid}]];
  var courseTime = [[${courseTime}]];
  var teaCode = [[${teaCode}]];
  function _jsBridgeReady() {

    jsBridge.postNotification('CLIENT_TOOLBAR_TYPE', {
      'toolbarType': 0
    });

    jsBridge.postNotification('CLIENT_WEB_BOUNCES', {
      "forbiddenFlag": 1
    });

  }

  var mescroll = new MeScroll("mescroll", {
    up: {
      auto: true, //是否在初始化时以上拉加载的方式自动加载第一页数据; 默认false
      isBounce: true, //此处禁止ios回弹,解析(务必认真阅读,特别是最后一点): http://www.mescroll.com/qa.html#q10
      callback: getList, //上拉回调,此处可简写; 相当于 callback: function (page) { upCallback(page); }
      htmlNodata: '<p class="upwarp-nodata">-- 没有更多数据了 --</p>',
    }

  });

  function getList(page) {
    var courseName = $(".con-select ul .course span").text();
    if (courseName=="课程"){
      courseName = '';
    }
    $.ajax({
      type: "get",
      url: "/elective/rollcall/getTeachingClassListV1",
      data: {
        courseTime: courseTime,
        xgh: teaCode,
        page: page.num,
        courseName:courseName
      },
      dataType: 'json',
      success: function (data) {
        var html = "";
        for (let i = 0; i < data.data.length; i++) {
          if (data.data[i].pkjgsj_sfwcdm == "是"){
            html+='<div class="stu for-leave" jxbCode = '+data.data[i].pkjgsj_jxbbh+'>' ;
          }else {
            html+='<div class="stu" jxbCode = '+data.data[i].pkjgsj_jxbbh+'>' ;
          }
          html+='<div class="grace"></div>' ;
          html+='<div class="course-name">'+data.data[i].pkjgsj_jxbmc+'</div>' ;
          html+='<div class="inform">' ;
          html+='<span>第'+data.data[i].pkjgsj_zc+'周</span>' ;
          html+='<span>周'+data.data[i].pkjgsj_xq+'</span>' ;
          html+='<span>第'+data.data[i].pkjgsj_kj+'节</span>' ;
          html+='</div>' ;
          html+='<div class="course">' ;
          html+='<span>'+data.data[i].pkjgsj_jxbzc+'</span>' ;
          html+='</div>' ;
          html+='</div>';
        }
        if (page.num == 1) {
          $(".stu-list").html(html);
        } else {
          $(".stu-list").append(html);
        }
        $(".total span i").text(data.count)
        mescroll.endSuccess(data.data.length, data.count);
      }
    })
  }

  $(".stu-list-wrap").on('click', ".stu-list .stu", function () {
    var webUrl = window.location.origin + "/elective/rollcall/jxbStaticIndex?jxbCode=" + $(this).attr("jxbCode")
            + "&courseTime=" + courseTime.replace("-","年").replace("-","月").replace("-","年").replace("-","月").replace(" ~ ","日 - ")+"日"
            + "&jxbmc=" + $(this).find(".course-name").text();
    // location.href = webUrl;
    jsBridge.postNotification('CLIENT_OPEN_URL', { "toolbarType": 2, "webUrl": webUrl, "loadType": 1 });
  });


  $(document).ready(function () {

    setTimeout(function () {
      var stuH = $(window).height() - $(".stu-list-wrap").offset().top;
      $(".stu-list-wrap").css({
        height: stuH + "px"
      });
    }, 500)


    $(".select-courses-dialog").on("click", ".w-con .w-box ul li", function () {
      $(this).toggleClass("cur");
      let currentlen = $(this).parent().find("li.cur").length;
      let allLen = $(this).parent().find("li").length;
      if (currentlen == allLen) {
        $(this).parent().siblings(".all-select").addClass("cur");
      } else {
        $(this).parent().siblings(".all-select").removeClass("cur");
      }
    })

    //全选

    $(".select-courses-dialog .w-con .w-box .all-select").click(function () {
      $(this).toggleClass("cur");
      if ($(this).hasClass("cur")) {
        $(this).parent().find("li").addClass("cur");
      } else {
        $(this).parent().find("li").removeClass("cur");
      }
    })


    //取消
    $(".select-courses-dialog .w-con .w-head .cancle").click(function () {
      $(".select-courses-dialog").hide();
      $(".con-select ul li.course").removeClass("active");
    })

    //保存
    $(".select-courses-dialog .w-con .w-head .btns").click(function () {
      $(".select-courses-dialog").hide();
      $(".con-select ul li.course").removeClass("active");

      let arr = [];
      $(".select-courses-dialog .w-con .w-box ul li.cur").each(function () {
        arr.push($(this).text());
      })
      if (arr.length==0){
        arr.push("课程");
      }
      $(".con-select ul li.course span").text(arr.join(","));
      mescroll.setPageNum(1);
      mescroll.triggerDownScroll();
    })


    //选择学期
    $(".con-select ul li.course").click(function () {
      $(this).addClass("active").siblings().removeClass("active");
      $(".select-courses-dialog").show();
    })

    $(".dialog-box").on('click', function (event) {
      if (!$(event.target).closest('.w-con').length) {
        // 如果点击的元素不是弹窗或其子元素，则隐藏弹窗
        $(".con-select ul li.course").removeClass("active");
        $(".con-select ul li.date").removeClass("active");
        $('.dialog-box').hide();
      }

    });



    var showDataDom = document.querySelector('#selDate');

    const today = new Date(); // 获取今天的日期
    const dates = []; // 用于存储最近30天的日期

    for (let i = 0; i < 30; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() - i); // 逐天减去天数

      // 格式化日期为 "YYYY年MM月DD日"
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1
      const day = String(date.getDate()).padStart(2, '0');

      const formattedDate = `${year}年${month}月${day}日`;
      dates.unshift(formattedDate);
    }

// 输出结果


    var startDate =dates.map(function(item,index){
      return {'id': "100"+index, 'value': item}
    });
    var endData =dates.map(function(item,index){
      return {'id': "200"+index, 'value': item}
    });


    $(document).on('touchstart',".ios-select-widget-box",function(event) {
      $(".con-select ul li").removeClass("active");
    });

    showDataDom.addEventListener('click', function () {
      $(this).addClass('active').siblings().removeClass('active');
      var startId = showDataDom.dataset['start_id'];
      var endId = showDataDom.dataset['end_id'];

      var sanguoSelect = new IosSelect(2,
              [startDate, endData],
              {
                title: '选择日期',
                sureText:'保存',
                itemHeight: 35,
                oneLevelId: startId,
                twoLevelId: endId,
                callback: function (selectOneObj, selectTwoObj) {

                  $("#selDate").find("span").text(selectOneObj.value + ' ~ ' + selectTwoObj.value)
                  courseTime = (selectOneObj.value + ' ~ ' + selectTwoObj.value).replaceAll("年","-").replaceAll("月","-").replaceAll("日","")
                  console.log(courseTime)
                  mescroll.setPageNum(1);
                  mescroll.triggerDownScroll();
                  showDataDom.dataset['start_id'] = selectOneObj.id;
                  showDataDom.dataset['end_id'] = selectTwoObj.id;
                }
              });
    });

    for (let ob of searchArr) {
      $.ajax({
        type: "get",
        url: "/elective/rollcall/getFormDistinctFiled",
        data: ob,
        dataType: 'json',
        // async: false,
        success: function (data) {
          var html = "";
          if (data.status) {
            for (var i = 0; i < data.data.length; i++) {
              if (data.data[i] == '') {
                continue;
              }
              html += "<li>" + data.data[i] + "</li>";
            }
          }
          $("#" + ob.fieldAlias).html(html);
        }
      })
    }

  })
</script>

</html>