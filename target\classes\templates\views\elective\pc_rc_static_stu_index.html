<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" li-content="IE=edge">
    <meta name="viewport" li-content="width=device-width, initial-scale=1.0">
    <title>微点名</title>
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/global.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/layui1/css/layui.css'">
    <link rel="stylesheet" href="/elective/rollcall/css/commonPC.css">
    <link rel="stylesheet" href="/elective/rollcall/css/reset.css">
    <link rel="stylesheet" href="/elective/rollcall/css/poup.css">
    <link rel="stylesheet" href="/elective/rollcall/css/microdot-name_pc_stu_index.css">
    <link rel="stylesheet" href="/elective/rollcall/css/new-microdot-name.css">
    <link rel="stylesheet" href="/elective/rollcall/css/name-calling-end.css">
    <script th:src="${_CPR_}+'/elective/layui1/layui.js'"></script>

    <style>
        body .main .con {
            height: auto;
            min-height: calc(100vh - 196px);
        }

        body .main .con .table {
            padding-left: 30px;
        }

        body .main .con .filter-box.filter-box {
            justify-content: flex-start;
        }

        body .main .con .filter-box.filter-box .layui-form .layui-form-item {
            margin-bottom: 32px;
        }

        .main .con .table .detail span i.color6 {
            color: rgba(175, 119, 255, 1);
        }

        .layui-form-select dl dd.layui-select-tips {
            display: block;
        }

        .layui-form-select dl dd.layui-select-tips {
            padding-left: 20px !important;
        }
    </style>

</head>

<body>
<div class="main">
    <div class="top">
        <div class="title">
            <div class="back" th:if="${back}">返回</div>
            <div class="levelone">学生点名统计</div>
        </div>
    </div>
    <div class="con">
        <div class="filter-box filter-box">
            <form class="layui-form" action="" id="example" lay-filter="myFormFilter">
                <div class="layui-form-item">
                    <label class="layui-form-label">学年学期</label>
                    <div class="layui-input-block w240">
                        <select name="typeCover1" lay-filter="typeCover1" lay-verify="required" lay-search=""
                                id="semester">
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">姓名</label>
                    <div class="layui-input-block w240">
                        <input type="text" name="studentName" class="layui-input" placeholder="请输入学生姓名">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">学号</label>
                    <div class="layui-input-block w240">
                        <input type="text" name="studentId" class="layui-input" placeholder="请输入学号">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">所属班级</label>
                    <div class="layui-input-block w240">
                        <select name="typeCover2" lay-filter="typeCover2" lay-verify="required" lay-search=""
                                id="class">
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">日期选择</label>
                    <div class="layui-input-block ">
                        <div class="times w240">
                            <input type="text" name="time" readonly="" placeholder="请选择" class="layui-input"
                                   id="startTime">
                        </div>
                    </div>
                </div>
            </form>
            <div class="button-list">
                <!-- <div class="reset">重置</div> -->
                <div class="searcher" lay-filter="formDemo">查询</div>
            </div>

        </div>
        <div class="table">
            <div class="tab-cons roll-call-record">
                <table class="layui-table" id="materialTable" lay-filter="materialTable"></table>
            </div>


        </div>


    </div>
    <div id="coursePage"></div>
</div>


</body>
<script type="text/html" id="tmplToolBar">
    <div class="oprate-table">
        <div class="seek" lay-event="seek" style="color:#3A8BFF;;cursor:pointer;">查看详情</div>
    </div>
</script>
<script th:src="${_CPR_}+'/elective/js/jquery-3.3.1.min.js'"></script>
<script src="/elective/rollcall/js/common1.js"></script>
<script th:inline="javascript">
    let pageIndex = 1;
    let pageSize = 10;
    var rollcallStateList = [[${rollcallStateList}]];
    var current = [[${current}]];
    layui.use(['table', 'jquery', 'laypage', 'laydate', 'form'], function () {

        var laypage = layui.laypage;
        var table = layui.table;
        var $ = layui.jquery;
        var layer = layui.layer;
        var laydate = layui.laydate;
        var form = layui.form;
        let xqMap = new Map();

        let timeEL = laydate.render({
            elem: '#startTime',
            format: 'yyyy-MM-dd',
            type: 'date',
            range: '~',
            min: current.xnxq_xqkssj,
            max: current.xnxq_xqjssj,
            done: function (value, date) {
            }
        });

        form.on('select(typeCover1)', function (data) {
            var elem = document.querySelector("#startTime");
            if (elem) {
                elem.outerHTML = "<input type=\"text\" name=\"time\" readonly=\"\" placeholder=\"请选择\" class=\"layui-input\" id=\"startTime\">";
            }
            timeEL = laydate.render({
                elem: '#startTime',
                format: 'yyyy-MM-dd',
                type: 'date',
                range: '~',
                min: xqMap.get(data.value).xnxq_xqkssj,
                max: xqMap.get(data.value).xnxq_xqjssj,
                done: function (value, date) {
                }
            });
        });

        $.ajax({
            type: "get",
            url: "/elective/rollcall/getSemesters",
            dataType: 'json',
            async: false,
            success: function (data) {
                var html = "<option value=''>请选择</option>";
                if (data.status) {
                    for (var i = 0; i < data.data.length; i++) {
                        xqMap.set(data.data[i].xnxq_xnxqh, data.data[i]);
                        if (current.xnxq_xnxqh == data.data[i].xnxq_xnxqh) {
                            html += "<option value='" + data.data[i].xnxq_xnxqh + "' selected = 'selected'>" + data.data[i].xnxq_xnxqh + "</option>";
                        } else {
                            html += "<option value='" + data.data[i].xnxq_xnxqh + "'>" + data.data[i].xnxq_xnxqh + "</option>";
                        }
                    }
                }
                $("#semester").html(html);
                layui.form.render("select");
            }
        })

        $.ajax({
            type: "POST",
            url: "/elective/rollcall/getXzbList",
            dataType: 'json',
            success: function (data) {
                var html = "<option value=''>请选择</option>";
                if (data.status) {
                    for (var i = 0; i < data.data.length; i++) {
                        html += "<option value='" + data.data[i].administrationClassCode + "'>" + data.data[i].administrationClassName + "</option>";
                    }
                }
                $("#class").html(html);
                layui.form.render("select");
            }
        });
        $(".searcher").click(function () {
            pageIndex = 1;
            table.reload('materialTable', {
                where: {
                    xnxqh: $('#semester').val(),
                    courseTime:$("#startTime").val(),
                    uname: $('input[name="studentName"]').val(),
                    stuCode: $('input[name="studentId"]').val(),
                    classCode: $('#class').val(),
                    page: pageIndex,
                    limit: pageSize
                }
            }); //只重载数据
        })

        var titleArr = [{
            field: "studentRealname",
            align: "center",
            title: "姓名",
        },
            {
                field: "studentCodeDesensitize",
                align: "center",
                title: "学号",
            },
            {
                field: "administrationClassName",
                align: "center",
                title: "所属班级",
            },
            {
                field: "num",
                align: "center",
                title: "点名次数",
            },
            {
                field: "zcNum",
                align: "center",
                title: "正常",
                templet: function (d) {
                    return d.zcNum + "（" + (((parseInt(d.zcNum) / parseInt(d.num)) * 100).toFixed(1) + "%") + "）";
                }
            }];
        for (let i = 0; i < rollcallStateList.length; i++) {
            titleArr.push({
                align: "center",
                title: rollcallStateList[i].stateNamePc,
                templet: function (d) {
                    return d.stateNum[i] + "（" + (((parseInt(d.stateNum[i]) / parseInt(d.num)) * 100).toFixed(1) + "%") + "）";
                }
            });
        }
        titleArr.push({
            field: "attendanceRate",
            align: "center",
            title: "出勤率",
            templet: function (d) {
                return d.attendanceRate + "%";
            }
        });
        titleArr.push(
            {
                field: "options",
                align: "center",
                title: "操作",
                toolbar: "#tmplToolBar",
                width: 88,
                fixed: "right",
            });

        var table1 = table.render({
            elem: "#materialTable",
            id: 'materialTable',
            url: '/elective/rollcall/getStaticStudentsPage', //数据接口
            where: {
                xnxqh: $('#semester').val(),
                courseTime:$("#startTime").val(),
                uname: $('input[name="studentName"]').val(),
                stuCode: $('input[name="studentId"]').val(),
                classCode: $('#class').val()
            },
            cols: [titleArr],
            done: function (res) {
                //分页
                laypage.render({
                    elem: 'coursePage',
                    curr: pageIndex,
                    groups: 5,
                    limit: pageSize,
                    limits: [10, 20, 30],
                    count: res.count,
                    layout: ['count', 'prev', 'page', 'next', 'limit', 'limits', 'skip'],
                    jump: function (obj, first) {
                        if (!first) {
                            pageIndex = obj.curr;
                            pageSize = obj.limit;
                            table.reload('materialTable', {
                                where: {
                                    xnxqh: $('#semester').val(),
                                    courseTime:$("#startTime").val(),
                                    uname: $('input[name="studentName"]').val(),
                                    stuCode: $('input[name="studentId"]').val(),
                                    classCode: $('#class').val(),
                                    page: pageIndex,
                                    limit: pageSize
                                }
                            }); //只重载数据
                        }
                    }
                });
            }
        })

        table.on("tool(materialTable)", function (obj) {
            var data = obj.data; //获得当前行数据
            if (obj.event === "seek") {
                window.open("/elective/rollcall/staticIndex/stu/detail?xnxqh=" + $('#semester').val() + "&uname=" +
                    data.studentRealname + "&stuCode=" + data.studentCodeDesensitize + "&className=" + data.administrationClassName + "&stuUid=" + data.studentUid+"&courseTime="+$("#startTime").val(), "_self");
            }
        })


        //返回

        $(".main .top .title .back").click(function () {
            window.history.go(-1);
        })


        //2024.12.5
        resizeHh();

        $('.filter-box .button-list .reset').click(function () {
            console.log("重置");

            form.val("myFormFilter", { // myFormFilter 是表单定义的lay-filter的值
                "studentName": '',
                "studentId": '',
                "callStatus": '',
            });

            layui.form.render();
            let placeholdertext = $(
                ".filter-box .layui-form .layui-form-item .sel .select-input .name.ckd").attr(
                "data-name");
            $(".filter-box .layui-form .layui-form-item .sel .select-input .name.ckd").removeClass(
                "ckd").text(placeholdertext);
            $(".filter-box .layui-form .layui-form-item .sel .select-input .select-dropdown .dropdown-lists li")
                .removeClass("cur");
            $(".filter-box .layui-form .layui-form-item .sel .select-input .select-dropdown .all-selects.cur")
                .removeClass("cur").text("全选");
        });

        function resizeHh() {
            let hh = $(".roll-call-record").offset().top + 106;
            let cuurent = $(window).height() - hh;
            $(".roll-call-record").css("min-height", cuurent);
        }

        $(window).resize(function () {
            resizeHh();
        });


    });
</script>

</html>