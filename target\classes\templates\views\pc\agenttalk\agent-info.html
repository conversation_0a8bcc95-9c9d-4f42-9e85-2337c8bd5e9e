<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能体内容-信息</title>
    <link rel="stylesheet" type="text/css" th:href="@{~/css/agenttalk/global.css}"/>
    <link rel="stylesheet" type="text/css" th:href="@{~/css/agenttalk/index.css}"/>
    <style>
        h1 {
            font-size: 20px;
            margin: 10px auto;
        }
    </style>
</head>

<body>
<div id="info" class="chat-item-content-message message-body">
    <div class="content">
        <div class="pure-text">
            <div class="types"><span id="info-info" style="white-space: pre-wrap;" th:text="${data.data}" ></span>
            </div>
        </div>
    </div>
</div>

</body>
<script th:src="@{~/js/agenttalk/jquery-3.3.1.min.js}"></script>
<script>
    $(function () {
        function sendHeight() {
            const message = {
                type: "CXBOT:resizeMessage",
                data: {
                    messageId: '[[${botMsg}]]', // 请从页面地址栏中获取，字段为 bot_msg
                    height: $(".message-body").height()+30 // 允许携带 px 作为单位
                },
            };
            window.parent.postMessage(message, '[[${botReferer}]]');
        }
        sendHeight();
    })
</script>


</html>