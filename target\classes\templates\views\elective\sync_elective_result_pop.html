<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>选课计划</title>
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/global.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/dialog.css'">
    <style>
        .j-search-con .j-select-year.slideShow {
            height: 124px;
        }
    </style>
</head>

<body>
    <!-- 手动安排考试 -->
    <div class="masker"></div>
    <div class="dialog" id="invigilateMax">
        <div class="dialog-con">
            <div class="item">
                <div class="label">请选择选课计划</div>
                <div class="j-search-con single-box">
                    <input type="text" name="teacherName" placeholder="请选择" readonly="" class="schoolSel">
                    <span class="j-arrow"></span>
                    <div class="j-select-year ">
                        <div class="search">
                            <input type="text" placeholder="搜索">
                            <span></span>
                        </div>
                        <ul name="teacherName">
                            <li data-id="0">计划1</li>
                            <li data-id="1">计划2</li>
                            <li data-id="2">计划3</li>
                            <li data-id="3">计划4</li>
                        </ul>
                    </div>
                </div>
            </div>

        </div>
        <div class="dialog-btn"><button class="pu-cancel">取消</button><button class="pu-sure">确定</button>
        </div>
    </div>
</body>
<script th:inline="javascript">
    const _VR_ = [[${_VR_}]] || '';
    var fid = [[${fid}]];
    var uid = [[${uid}]];
</script>
<script th:src="${_CPR_}+'/elective/js/jquery-3.3.1.min.js'"></script>
<script th:src="${_CPR_}+'/js/my.util.js'"></script>
<script th:src="${_CPR_}+'/elective/layui/layui.js'"></script>
<script>
    var taskBdid ;
    layui.use(['layer'], function () {
        var layer = layui.layer;});
    $(document).ready(function () {
        U.ajax({
            type: 'post',
            url: "../elective/task/getAllTask",
            data: {},
            dataType: 'json',
            success: function (data) {
                if (data.data) {
                    var html = "";
                    for (let i = 0; i < data.data.length; i++) {
                        html += "<li bdid = '"+data.data[i].bdid+"'>" + data.data[i].xkjhbJhmc + "</li>";
                    }
                    $(".j-select-year ul").html(html);
                }
            }
        });
        // 下拉多选
        $(".j-search-con").on("click", ".schoolSel", function (e) {
            $(".j-select").hide();
            let parent = $(this).parent();
            parent.find('.j-arrow').toggleClass('j-arrow-slide');
            parent.find('.j-select-year').toggleClass('slideShow')
            stopBubble(e)
        })

        $(".pu-cancel").on("click", function (e) {
            window.parent.postMessage(JSON.stringify({ action: 1 }), "*");
        })
        $(".pu-sure").on("click", function (e) {
            U.ajax({
                type: 'post',
                url: "../elective/task/sync/result",
                data: {fid:fid,taskBdid:taskBdid,uid:uid},
                dataType: 'json',
                success: function (data) {
                    if (data.code==200){
                        layer.msg("执行成功，请稍后刷新查看！")
                        setTimeout(function () {
                            window.parent.postMessage(JSON.stringify({action: 1}), "*");
                        }, 2000)
                    }else {
                        layer.msg(data.msg)
                    }
                }
            });

        })

        // 选择-单选
        $(".j-search-con.single-box").on("click",
            ".j-select-year li ",
            function (e) {
                $(this).addClass('active').siblings().removeClass();
                var parents = $(this).parents('.j-search-con');
                var schoolSelEle = parents.find('.schoolSel');
                var txt = $(this).text();
                schoolSelEle.val(txt);
                taskBdid = $(this).attr("bdid");
                parents.find('.j-arrow').removeClass('j-arrow-slide');
                parents.find('.j-select-year').removeClass('slideShow')
                stopBubble(e);
            })
        // 搜索
        $(".j-search-con").on('click', '.search input', function (e) {
            stopBubble(e);
        })
        $(".j-search-con").on('input', '.search input', function (e) {
            let val = $(this).val();
            var nextEle = $(this).parents('.j-search-con').find('ul')
            if (val != "") {

                nextEle.find('li').each(function (i, ele) {
                    let txt = $(ele).text();
                    if (txt.indexOf(val) >= 0) {
                        $(ele).show();
                    } else {
                        $(ele).hide();
                    }
                })
            } else {
                nextEle.find('li').show();
            }
            stopBubble(e)
        })

        function stopBubble(e) {
            if (e && e.stopPropagation)
                e.stopPropagation();
            else {
                window.event.cancelBubble = true;
            }

        }
        $(document).on("click", function (event) {
            var _con = $('.select-input');
            if (!_con.is(event.target) && _con.has(event.target).length === 0) {
                $(".j-search-con").find('.j-arrow').removeClass('j-arrow-slide');
                $(".j-search-con").find('.j-select-year').removeClass('slideShow')
            }
        })

    })
</script>

</html>