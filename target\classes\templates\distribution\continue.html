<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教材延用</title>
    <link rel="stylesheet" href="/distribution/css/global1.css">
    <link rel="stylesheet" href="/distribution/layui/css/layui.css">
    <link rel="stylesheet" href="/distribution/css/reset.css">
    <link rel="stylesheet" href="/distribution/css/reviewSub.css">
    <script src="/distribution/layui/layui.js"></script>
    <style>
    	.j-con .j-left .j-table .lable{
            display:flex;
            display:-webkit-box;
            align-items: center;
            justify-content: flex-start;
            margin-bottom:10px;
        }
        .j-con .j-left .j-table .lable .name{
           width:75px;
        }
        .j-con .j-left .j-table .lable .tit{
            color:#86909C;
        }
        .j-material .j-search-item h5{
            font-size: 16px;
            color: #1D2129;
            font-weight: 700;
            line-height: 20px;
            float: left;
            margin-right: 16px;
        }
    </style>
</head>

<body>
    <div class="j-material-wrap">
        <div class="j-material">
            <div class="j-title">
                <h4>教材延用</h4>
                <p>第一学期课程上完，教材没有上完，在第二学期继续用。将所选教材沿用到下一学期</p>
                <div class="j-btns">
                    <button class="btn-cancel">取消</button>
                    <button class="btn-complate">完成</button>
                </div>
            </div>
            <div class="j-con">
                 <div class="j-left">
                    <div class="j-item">
                        <div class="j-top">
                            <div class="j-search-item">
                                <h5>请选择原课程</h5>
                                <div class="j-search-con">
                                    <input type="text" placeholder="选择学期" id="schoolTerm" readonly="" class="schoolSel">
                                    <span class="j-arrow"></span>
                                    <div class="j-select-year">
                                        <ul>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="search">
                                <input type="txt" class="layui-input bookName" placeholder="请输入" name="title" lay-verify="required" lay-verType="tips"  autocomplete="off" >
                                <div class="btn" onclick="search(1)">搜索</div>
                            </div>
                        </div>
                        <div class="j-table">
                            <table class="layui-table" id="courseTable" lay-filter="courseTable">
                            </table>
                        </div>
                    </div>
                    <div class="j-item">
                        <div class="j-top">
                            <div class="name">
                                <h4>选择待延用课程</h4>
                                <p class="xnxq">学年学期：</p>
                            </div>
                            <div class="search">
                                <input type="txt" class="layui-input kcmc" placeholder="请输入" name="title" lay-verify="required" lay-verType="tips" autocomplete="off" >
                                <div class="btn" onclick="search(2)">搜索</div>
                            </div>
                        </div>
                        <div class="j-table">
                            <table class="layui-table" id="courseTable2" lay-filter="courseTable2">
                            </table>
                        </div>
                    </div>
                    
                 </div>
                 <div class="j-right j-left">
                 	<div class="j-item">
                        <div class="j-top">
                            <div class="name">
                                <h4>选择结果</h4>
                            </div>
                            
                        </div>
                    </div>
                 	<div class="j-item">
                        <div class="j-top">
                            <div class="name">
                                <h4>原课程</h4>
                                <p class="term">学年学期：2022-2023-1</p>
                            </div>
                        </div>
                        <div class="j-table">
                            <div class="lable">
                                <div class="name">教材名称：</div>
                                <div class="tit bookName"></div>
                            </div>
                            <div class="lable">
                                <div class="name">教材编号：</div>
                                <div class="tit bookNum"></div>
                            </div>
                            <div class="lable">
                                <div class="name">ISBN：</div>
                                <div class="tit isbn"></div>
                            </div>
                            <div class="lable">
                                <div class="name">出版社：</div>
                                <div class="tit publisher"></div>
                            </div>
                            <div class="lable">
                                <div class="name">教材作者：</div>
                                <div class="tit author"></div>
                            </div>
                            <div class="lable">
                                <div class="name">课程名称：</div>
                                <div class="tit courseName"></div>
                            </div>
                            <div class="lable">
                                <div class="name">课程编号：</div>
                                <div class="tit courseNum"></div>
                            </div>
                        </div>
                    </div>
                    <div class="j-item">
                        <div class="j-top">
                            <div class="name">
                                <h4>设置结果</h4>
                            </div>
                            
                        </div>
                        <div class="j-table">
                            <table class="layui-table" id="courseTable3" lay-filter="courseTable3">
                            </table>
                        </div>
                    </div>
                 </div>
            </div>
           
        </div>
     
    </div>
    
</body>
<script type="text/html" id="tmplToolBar">
    <div class=" select"  lay-event="select">选择</div>
</script>
<script type="text/html" id="tmplToolBar2">
    <div class=" cancle"  lay-event="cancle">取消</div>
</script>
<script src="/js/jquery-3.3.1.min.js"></script>
<script src="/js/commons/common.js"></script>
<script th:inline="javascript">
	var table = "",reset = "";
	let pageIndex = 1;
    layui.use(['table', 'jquery', 'laydate','laypage'], function () {
        table = layui.table,
        laydate = layui.laydate,
        $ = layui.jquery;
        var laypage = layui.laypage;
        //分页
        laypage.render({
            elem: 'courseTable3'
                , curr: pageIndex//让起始页,也就是点击的页码
                , groups: 5// 默认的连续出现的页码数
                , limit: 10//每页展示条数
                , count: 0//数据总数
                , layout: ['count','prev', 'page', 'next', 'skip']
                , data:function (){
                	var courseData4 = new Array();
                	courseData3.forEach(function (info){
	                	if(info.LAY_TABLE_INDEX < pageIndex*10 && info.LAY_TABLE_INDEX >=(pageIndex - 1)*10){
	                		courseData4.push(info);
	                	}
	                });
	                return courseData4;
                }
                , jump: function (obj, first) {
                    if (first || obj.curr == 1) {
	                    $(".j-table-tr").find('form:lt(' + (obj.limit) + ')').show();
	                    $(".j-table-tr").find('form:gt(' + (obj.limit - 1) + ')').hide();
	                } else {
	                    $(".j-table-tr").find('form:lt(' + (obj.curr - 1) * obj.limit + ')').hide();
	                    $(".j-table-tr").find('form:gt(' + ((obj.curr - 1) * obj.limit - 1) + '):lt(' + (obj.curr) * obj.limit + ')').show();
	                    $(".j-table-tr").find('form:gt(' + (obj.curr * obj.limit - 1) + ')').hide();
	                }
                }
        });
        /* ********************* 已选课程 ************************ */
        let courseData = []
    	let courseData2 = []
        let courseData3 = []
        let courseData5 = [];
		$("#layui-laypage-1").remove();
        table.on('tool(courseTable3)', function (obj) {
            if('cancle' == obj.event) {
                //可以在这里写触发事件希望执行的代码
                console.log("nide");
                //$(this).parents("tr").remove();
                var id = obj.data.id;
                var courseData4 = new Array();
                courseData3.forEach(function (info){
                	if(info.id == id){
                		delete courseData3[info.LAY_TABLE_INDEX];
                	}else{
                		courseData4.push(info);
                	}
                });
                courseData5.forEach(function (info){
                	if(info.id == id){
                		delete courseData5[info.LAY_TABLE_INDEX];
                	}
                });
                courseData3 = courseData4;
                console.log(courseData3);
                /* table.reload("courseTable3",{
				    data:courseData3   // 将新数据重新载入表格
				}) */
				table.reload("courseTable3",{data:courseData3,page: {curr: 1}});
            }
        });


        table.render({
            elem: '#courseTable2',
            url: '/api/continue/getXqkcList?fid='+fid+"&xnxq="+xnxq, //数据接口
            data: courseData2, //静态数据，真实数据用url接口
            limit:10,
            page:true,
            cols: [
                [ //表头
                	{title: '操作',minWidth: 100,align: 'center', fixed: 'left',toolbar: '#tmplToolBar'},
                	{field: 'formUserId',title: 'id',align: 'center',width: 50,hide: true}, 
                    {field: 'jckcmc',title: '课程名称',align: 'center',width: 152},
                    {field: 'jckcbh',title: '课程编号',align: 'center',width: 152},
                    {field: 'jckkyx',title: '开课院系',align: 'center',width: 152},
                    {field: 'jckclb',title: '课程类别',align: 'center',width: 152},
                    {field: 'jcskjs',title: '授课老师',align: 'center',width: 152,templet: function (d) {
                    	if(d.jcskjs != ''){
                    	 	var skjs = JSON.parse(d.jcskjs); 
                    	 	if(skjs != ''){return skjs[0].uname;}else{return '';}}
                    	}
                    },
                    {field: 'jcjxbzclx',title: '教学班组成类型',align: 'center',width: 152}, 
                    {field: 'jcjxbid',title: '教学班ID',align: 'center',width: 152}, 
                    {field: 'jcjxbm',title: '教学班名',align: 'center',width: 152}
                    
                ]
            ]
        });

        table.on('tool(courseTable2)', function (obj) {
            if('select' == obj.event) {
                //可以在这里写触发事件希望执行的代码
                console.log("wode");
                if(chooseJc == null || chooseJc == ''){
                	alert("请先选择教材！");
                	return ;
                }
                var kc = {};
                kc['id'] = chooseJc.formUserId+'_'+obj.data.formUserId;
                kc['jckcmc'] = obj.data.jckcmc;
                kc['jckcbh'] = obj.data.jckcbh;
                kc['jckkyx'] = obj.data.jckkyx;
                kc['jckclb'] = obj.data.jckclb;
                if(obj.data.jcskjs != ''){ 
                	var skjs = JSON.parse(obj.data.jcskjs);               
	                kc['jcskjs'] = skjs[0].uname;
                }
                kc['jcjxbzclx'] = obj.data.jcjxbzclx;
                kc['jcjxbid'] = obj.data.jcjxbid;
                kc['jcjxbm'] = obj.data.jcjxbm;
                
                courseData3.push(kc);
                table.render({
		            elem: '#courseTable3',
		            data: courseData3, //静态数据，真实数据用url接口
		            limit:10,
		            page:true,
		            count:courseData3.length,
		            cols: [
		                [ //表头
		                    {title: '操作',minWidth: 100,align: 'center', fixed: 'left',toolbar: '#tmplToolBar2'},
		                	{field: 'formUserId',title: 'id',align: 'center',width: 50,hide: true}, 
		                    {field: 'jckcmc',title: '课程名称',align: 'center',width: 152},
		                    {field: 'jckcbh',title: '课程编号',align: 'center',width: 152},
		                    {field: 'jckkyx',title: '开课院系',align: 'center',width: 152},
		                    {field: 'jckclb',title: '课程类别',align: 'center',width: 152},
		                    {field: 'jcskjs',title: '授课老师',align: 'center',width: 152},
		                    {field: 'jcjxbzclx',title: '教学班组成类型',align: 'center',width: 152}, 
		                    {field: 'jcjxbid',title: '教学班ID',align: 'center',width: 152}, 
		                    {field: 'jcjxbm',title: '教学班名',align: 'center',width: 152}
		                ]
		            ]
		        });
            }
        });

        table.render({
            elem: '#courseTable',
            url: '/api/continue/getXqkcjcList?fid='+fid+"&xnxq="+xnxq, //数据接口
            data: courseData, //静态数据，真实数据用url接口
            limit:10,
            page:true,
            cols: [
                [ //表头
                    {type: 'radio',title: '选择',width: 100,fixed:'left'},
                    {field: 'formUserId',title: 'id',align: 'center',width: 50,hide: true},
                    {field: 'term',title: '学年学期',align: 'center',width: 50,hide: true}, 
                    {field: 'bookName',title: '教材名称',align: 'center',width: 152}, 
                    {field: 'bookNum', title: '教材编号',align: 'center',width: 152 }, 
                    {field: 'isbn',title: 'ISBN',align: 'center',width: 152}, 
                    {field: 'publisher',title: '出版社',align: 'center',width: 152}, 
                    {field: 'author',title: '教材作者',align: 'center', width: 152}, 
                    {field: 'courseName', title: '课程名称',align: 'center', width: 152}, 
                    {field: 'courseNum', title: '课程编号',align: 'center',width: 152}
                ]
            ]
        });
        // 监听table复选框
        // 默认选中第一行
        //$('.layui-table-box').find("tr[data-index=0]").find("td div.laytable-cell-radio div.layui-form-radio I").click();
        //$(".layui-table-body table.layui-table tbody tr").eq(0).addClass('trSel');
     	
        table.on('radio(courseTable)', function (obj) {
            // 当前行数据
            console.log(obj.data);
            
            // 所有选中数据
            let selData = layui.table.checkStatus('courseTable');
            console.log(selData);
            $(".j-right .j-top .term").html("学年学期："+obj.data.term);
			$(".j-right .j-table .tit.bookName").html(obj.data.bookName);
			$(".j-right .j-table .tit.bookNum").html(obj.data.bookNum);
			$(".j-right .j-table .tit.isbn").html(obj.data.isbn);
			$(".j-right .j-table .tit.publisher").html(obj.data.publisher);
			$(".j-right .j-table .tit.author").html(obj.data.author);
			$(".j-right .j-table .tit.courseName").html(obj.data.courseName);
			$(".j-right .j-table .tit.courseNum").html(obj.data.courseNum);
			if(chooseJc != '' && chooseJc.formUserId != obj.data.formUserId){
				var courseData4 = new Array();
				var id = obj.data.formUserId;
				//保存已选的课程
				courseData3.forEach(function (info){
					var b = false;
	            	courseData5.forEach(function (info2){
		            	if(info.id == info2.id){
		            		b = true;
		            	}
	            	});
	            	if(!b){
	            		courseData5.push(info);
	            	}
	            });
	            console.log(courseData5);
	            //暂存数据中是否有选中教材的课程
	            courseData5.forEach(function (info){
	            	if(info.id.indexOf(id) > -1){
	            		courseData4.push(info);
	            	}
	            });
	            courseData3 = courseData4;
				table.reload("courseTable3",{data:courseData3,page: {curr: 1}});
			}
			
			chooseJc = obj.data;
            //判断是否被选中
            if (obj.tr.find('.layui-form-radio').hasClass('layui-form-radioed')) {
                obj.tr.addClass("trSel");
                obj.tr.siblings().removeClass("trSel");
            }
            
        });
		// 搜索
        $("#courseSearch").on('keyup', function () {
            $(this).parent().find(".j-select").show();
        });
        // 选择
        $(".j-select").on('click', "ul li", function () {
            $(this).addClass('active').siblings().removeClass('active');
            $(this).parents('.j-select').hide();
        })
        // 下拉
        $(".schoolSel").on('click', function (e) {
            let parent = $(this).parent();
            parent.find('.j-arrow').toggleClass('j-arrow-slide');
            parent.find('.j-select-year').toggleClass('slideShow')
            e.stopPropagation()
        })
        $('.j-select-year').on('click', "ul li", function () {
            $(this).addClass('active').siblings().removeClass('active');
            let txt = $(this).text();
            let parent = $(this).parents('.j-search-con');
            parent.find('.schoolSel').val(txt.trim());
            parent.find('.j-arrow').removeClass('j-arrow-slide');
            $(this).parents('.j-select-year').removeClass("slideShow")
        })
        $(".j-btns .btn-cancel").click(function (){
			courseData3 = new Array();
			table.reload("courseTable3",{
			    data:courseData3   // 将新数据重新载入表格
			})
			window.opener=null;
			window.open('','_self');
			window.close();
		});
		$(".j-btns .btn-complate").click(function (){
			console.log(courseData3);
			var ids = "";
			//保存已选的课程
			courseData3.forEach(function (info){
				var b = false;
            	courseData5.forEach(function (info2){
	            	if(info.id == info2.id){
	            		b = true;
	            	}
            	});
            	if(!b){
            		courseData5.push(info);
            	}
            });
			courseData5.forEach(function (info){
                if(ids == ''){
                	ids = info.id;
                }else{
                	ids += ","+info.id;
                }
            });
			$.ajax({
            type: 'get',
            dataType: 'json',
            url: '/api/continue/save',
            data: {fid:fid,uid:uid,xnxq:xnxq,ids:ids},
            success: function (res){
                if (res.status){
                    alert("延用完成");
                    window.opener=null;
					window.open('','_self');
					window.close();
                }
            }
        });
		});
        // 点击页面其他地方消失
        $(document).on('click', function (e) {
            if ($(e.target).closest('.j-select').length > 0 || $(e.target).closest('.j-select-year')
                .length > 0) {
                // alert('弹出框内部被点击了');
            } else {
                $(".j-select").hide();
                $('.j-select-year').removeClass("slideShow")
            }
        })
        //    选择
        table.on('tool(materialTable)', function (obj) {
            var data = obj.data; //获得当前行数据
            var layEvent = obj.event; //获得 lay-event 对应的值（也可以是表头的 event 参数对应的值）
            var tr = obj.tr; //获得当前行 tr 的 DOM 对象（如果有的话）

            if (layEvent === 'selected') { //查看
                //do somehing
                console.log('选择');
            }
        })
		reset = function(courseTable,courseData2){
			table.reload(courseTable,{
				data:courseData2   // 将新数据重新载入表格
			})
		}
    })
	var fid = [[${fid}]];
	var xnxq = [[${xnxq}]];
	var uid = [[${uid}]];
	var curXnxq = "";
	var chooseJc = "";//选中教材信息
	$(document).ready(function (){
		$(".j-left .j-top .xnxq").html("学年学期："+xnxq);
		getXqList();
		/* getXqkcjcList();
		getXqkcList(); */
	});
	function  search(type){
		if(type == 1){
			//getXqkcjcList();
			curXnxq = $(".j-select-year ul li.active").attr("value");
			var field = {curXnxq:curXnxq,keyword:$(".bookName").val()};
			table.reload("courseTable",{where: field,page: {curr: 1}});
		}else if(type == 2){
			//getXqkcList();
			var field = {keyword:$(".kcmc").val()};
			table.reload("courseTable2",{where: field,page: {curr: 1}});
		}
	}
	
	function getXqList(){
    	$.ajax({
            type: 'get',
            dataType: 'json',
            url: '/api/continue/getXqList',
            data: {fid: fid},
            success: function (res){
                if (res.status){
                    let data = res.list;
                    data.forEach(function (info){
                    	$(".j-select-year ul").append("<li value='"+info.xnxq_xnxqh+"'>"+info.xnxq_xnxqh+"</li>")
                    })
                }
            }
        });
    }
    
</script>

</html>