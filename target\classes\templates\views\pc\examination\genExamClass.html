<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">

<head>
    <th:block th:include="common :: header('手动安排考试')"/>
    <th:block th:include="common :: jquery-mCustomScrollbar-css"/>

    <link rel="stylesheet" type="text/css" th:href="@{~/css/examination/global.css}" href=""/>
    <link rel="stylesheet" type="text/css" th:href="@{~/css/examination/chosen.css}"/>
    <link rel="stylesheet" type="text/css" th:href="@{~/plugin/layui/css/layui.css}"/>
    <link rel="stylesheet" type="text/css" th:href="@{~/css/examination/style.css}"/>
    <style>
        .inputs input {
            margin: 0;
            outline: 0;
            -webkit-appearance: none;
            border-radius: 0;
            line-height: 30px;
            padding-left: 10px;
            border: none;
            background: transparent;
            height: 29px;
            float: left;
            color: rgb(51, 51, 51);
            font-size: 14px;
        }

        .inputs input::placeholder {
            color: #8F97A8;
            font-size: 14px;
        }

        .zy_title_h3 {
            height: 56px;
            line-height: 56px;
            font-size: 14px;
            padding-left: 20px;
        }

        .layui-table-body {
            min-height: 487px;
        }
        .bodyColor{
            background:#ffffff;
        }

    </style>
</head>

<body class="bodyColor">

<div class="zy_box">
    <div class="zy_main">
        <div class="zy_title">
            <h2 class="zy_title_h2 fl">选择考试班</h2>
            <h3 class="zy_title_h3 fl">当前学期:<span id="curXnxq" th:text="${xnxq}"></span></h3>
            <h3 class="zy_title_h3 fl">当前考试批次：<span id="curBatch" th:text="${batchName}"></span></h3>
        </div>
        <div class="xaScreen clearAfter">
            <div class="xaScreen_row fl">
                开课学期：
                <select name="courseYearSemester" id="courseYearSemester" data-placeholder="请选择学年学期"
                        style="width:200px;" class="dept_select">
                    <option value="" selected="selected">请选择学年学期</option>
                </select>
            </div>
            <div class="xaScreen_row fl">
                年级：
                <select name="grade" id="grade" data-placeholder="请选择年级" style="width:200px;" class="dept_select">
                    <option value="" selected="selected">请选择年级</option>
                </select>

            </div>
            <div class="xaScreen_row fl">
                考试科目：
                <select name="dept" id="kskm" data-placeholder="请选择考试科目" style="width:200px;" class="dept_select">
                    <option value="" selected="selected">请选择考试科目</option>
                </select>

            </div>
            <div class="xaScreen_row fl">
                考试编号：
                <select name="course" id="ksbh" data-placeholder="请选择考试编号" style="width:200px;"
                        class="dept_select">
                    <option value="" selected="selected">请选择考试编号</option>
                </select>

            </div>
            <div class="xaScreen_row fl"><a class="xaScreen_bnt" href="javascript:search();">查询</a></div>
            <div class="xaScreen_row fl"><a class="xaScreen_bnt" href="javascript:reSetParam();">重置</a></div>
        </div>
        <div class="xaScreen clearAfter" th:if="${status==1}">
            <div class="xaScreen_row fl" style="float: right;">
                <a class="xaScreen_add layui-tips" href="javascript:confirmTip();"
                   data-tip="点击后在考试安排表创建新的考试班，将所选学生添加到新的考试班中" style="width: 150px;">添加到新的考试班</a></div>
        </div>
        <div class="xaTable_box">
            <div class="xaTable_container">
                <table class="layui-table" lay-filter="courseTable" id="courseTable"></table>
            </div>
        </div>
    </div>
</div>

<script th:src="@{~/js/jquery-3.6.0.min.js}" type="text/javascript" charset="utf-8"></script>
<script th:src="@{~/js/jquery.nicescroll.min.js}" type="text/javascript" charset="utf-8"></script>
<script th:src="@{~/js/examination/fyPublic.js}" type="text/javascript" charset="utf-8"></script>
<script th:src="@{~/js/examination/chosen.jquery.js}" type="text/javascript" charset="utf-8"></script>
<script type="text/html" id="toolBar">
    <a class="colorBlue" href="javascript:void(0);" lay-event="open">选择</a>
    <a class="colorGreen" href="javascript:void(0);" style="display: none;" lay-event="none">已选择</a>
</script>
<script>
    $('.dept_select').chosen({
        no_results_text: "没有结果匹配",
        hide_results_on_select: false,
        max_shown_results: '2',
        //显示搜索框之前所需的最少选项数
        disable_search_threshold: 10,
        allow_single_deselect: true,
    });
</script>
<script th:src="@{~/plugin/layui/layui.js}" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript" th:inline="javascript">
    var fid = [[${fid}]];
    var uid = [[${uid}]];
    var queryId = [[${queryId}]];
    var status = [[${status}]];
    var msg = [[${msg}]];
    var xnxq = [[${xnxq}]];
    var batchCode = [[${batchCode}]];

    $(function () {
        if (status == 0) {
            U.fail(msg, 3000)
            return;
        }
        getExamResult();
    })

    var tableData = '';

    function getExamResult() {
        $.get("/examination/basic/result", {fid: fid, ignoreTime: true, xnxq: xnxq, bc: batchCode}, function (res) {
            if (res.code == 200) {
                tableData = res.data;
                initTab(res.data)
                initForm(res.data)
            }
        })
    }

    function search() {
        var xq = $("#courseYearSemester").val();
        var grade = $("#grade").val();
        var kskm = $("#kskm").val();
        var ksbh = $("#ksbh").val();
        var tempData = tableData;
        if (xq != undefined && xq != "") {
            tempData = tempData.filter(item => item.pkjgcx_xnxq == xq);
        }
        if (grade != undefined && grade != "") {
            tempData = tempData.filter(item => item.pkjgcx_nj == grade);
        }
        if (kskm != undefined && kskm != "") {
            tempData = tempData.filter(item => item.pkjgcx_kskm == kskm);
        }
        if (ksbh != undefined && ksbh != "") {
            tempData = tempData.filter(item => item.pkjgcx_pkjgbh == ksbh);
        }
        initTab(tempData);
    }

    function initForm(data) {
        var kkxq = new Set();
        var nj = new Set();
        var kskm = new Set();
        var ksbh = new Set();


        for (let i = 0; i < data.length; i++) {
            if (data[i].pkjgcx_xnxq != "") {
                kkxq.add(data[i].pkjgcx_xnxq);
            }
            if (data[i].pkjgcx_nj != "") {
                nj.add(data[i].pkjgcx_nj);
            }
            if (data[i].pkjgcx_kskm != "") {
                kskm.add(data[i].pkjgcx_kskm);
            }
            if (data[i].pkjgcx_pkjgbh != "") {
                ksbh.add(data[i].pkjgcx_pkjgbh);
            }

        }

        var sortedXq = sort([...kkxq])
        var sortedNj = sort([...nj])
        var sortedksbh = sort([...ksbh])


        sortedXq.forEach(data => {
            var kkxqHtml = "<option value='" + data + "'>" + data + "</option>"
            $("#courseYearSemester").append(kkxqHtml);
            $('#courseYearSemester').trigger("chosen:updated");
        })

        sortedNj.forEach(data => {
            var njHtml = "<option value='" + data + "'>" + data + "</option>"
            $("#grade").append(njHtml);
            $('#grade').trigger("chosen:updated");
        })

        kskm.forEach(data => {
            var kskmHtml = "<option value='" + data + "'>" + data + "</option>"
            $("#kskm").append(kskmHtml);
            $('#kskm').trigger("chosen:updated");
        })

        sortedksbh.forEach(data => {
            var ksbhHtml = "<option value='" + data + "'>" + data + "</option>"
            $("#ksbh").append(ksbhHtml);
            $('#ksbh').trigger("chosen:updated");
        })

    }

    function sort(data) {
        //排序
        for (let i = 0; i < data.length - 1; i++) {
            for (let j = 0; j < data.length - i - 1; j++) {
                if (data[j] > data[j + 1]) {
                    let temp = data[j + 1];
                    data[j + 1] = data[j];
                    data[j] = temp

                }
            }
        }
        return data;
    }


    function initTab(data) {
        layui.use('table', function () {
            table = layui.table;
            insTb = table.render({
                elem: '#courseTable',
                method: "POST",
                data: data,
                page: true,
                limits: [10, 20, 100],
                cellMinWidth: 100,
                cols: [
                    [
                        {field: 'pkjgcx_kskm', title: '考试科目'},
                        {field: 'pkjgcx_pkjgbh', title: '考试编号'},
                        {field: 'pkjgcx_nj', title: '年级'},
                        {field: 'pkjgcx_jxbmc', title: '教学班名称'},
                        {field: 'pkjgcx_ksrs', title: '考试人数'},
                        {field: 'pkjgcx_kscc', title: '考试场次'},
                        {field: 'pkjgcx_kc', title: '考场'},
                        {
                            title: '监考教师', templet: function (data) {
                                let jsxm = "";
                                for (let i = 0; i < data.pkjgcx_jkjs.length; i++) {
                                    if (i == data.pkjgcx_jkjs.length - 1) {
                                        jsxm += data.pkjgcx_jkjs[i].uname;
                                    } else {
                                        jsxm += data.pkjgcx_jkjs[i].uname + ",";
                                    }
                                }

                                return jsxm;
                            }
                        },
                        {field: 'pkjgcx_xsssbjbh', title: '学生所属班级'},
                        {title: '操作', toolbar: '#toolBar', field: 'tool', width: 80, fixed: 'right'}
                    ]
                ],
                done: function (res, curr, count) {
                    $(".opt_data_num #totalSpan").text("共 " + count + " 条");
                }
            });

            table.on('tool(courseTable)', function (obj) {
                if (obj.event !== 'open') {
                    return;
                }
                confirmTip(obj.data)
            })
        });

    }

    function confirmTip(data) {
        U.confirm({
            title: "提示",
            msg: data == undefined ? "是否将所选学生添加到新的考试班中" : "是否将所选学生添加到该考试班中",
            sureBtnTxt: '确定',
            cancelBtnTxt: '取消',
            sure: function () {
                U.ajax({
                    type: 'post',
                    url: "/exam/gen/merge",
                    data: {
                        fid: fid,
                        uid: uid,
                        queryId: queryId,
                        formUserId: data == undefined ? "" : data.rowInfo.formUserId
                    },
                    dataType: 'json',
                    success: function (res) {
                        if (res.code==200){
                            getExamResult();
                            U.success("保存成功")
                            setTimeout(function (){
                               U.closePop()
                            }, 2000)
                        }else {
                            U.fail(res.msg)
                        }

                    },
                    error: function () {
                        U.fail("未知异常")
                    }
                })
                console.log(data)
            },
            cancel: function () {
                U.closePop();
            }
        })
    }

    function reSetParam() {
        $('#courseYearSemester').val("");
        $('#courseYearSemester').trigger("chosen:updated");
        $('#grade').val("");
        $('#grade').trigger("chosen:updated");
        $('#kskm').val("");
        $('#kskm').trigger("chosen:updated");
        $('#ksbh').val("");
        $('#ksbh').trigger("chosen:updated");

        initTab(tableData);
    }

    var layTips = "";
    $('.layui-tips').on({
        mouseenter: function () {
            var that = this;
            var con = $(this).attr('data-tip')
            layTips = layer.tips(con, that, {
                tips: 1,
                time: 0
            });

        },
        mouseleave: function () {
            layer.close(layTips);
        }
    });
</script>


</body>

</html>