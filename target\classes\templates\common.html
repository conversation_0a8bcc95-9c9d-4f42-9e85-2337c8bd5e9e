<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<!-- 通用CSS -->
<head th:fragment="header(title)">
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="keywords" content="">
    <meta name="description" content="">
    <title th:text="${title}"></title>
    <script th:inline="javascript">
        const _VR_ = [[${_VR_}]] || '';
        const _CPR_ = [[${_CPR_}]] || '';
    </script>
    <link rel="stylesheet" th:href="${_CPR_+'/css/reset.css'}">
    <link rel="stylesheet" th:href="${_CPR_}+'/css/common.css'">
    <script th:src="${_CPR_}+'/js/jquery-1.11.3.min.js'"></script>
    <script th:src="${_CPR_}+'/js/template-web.js'"></script>
    <script th:src="${_CPR_}+'/js/my.util.js?v=2.1.6'"></script>
    <script type="javascript">
        template.defaults.imports.U = U;
    </script>
</head>
<!--/*导入外部方法的方式如下，只需把methodName替换为自己的方法名即可。注意：导入自己的业务方法请放到自己的页面或js中不要放到此页面内
        template.defaults.imports.methodName = function () {
          return 'test';
        };*/-->
<!-- layui插件 -->
<th:block th:fragment="layui-css">
    <link th:href="${_CPR_}+'/plugin/layui/css/layui.css'" rel="stylesheet"/>
</th:block>
<th:block th:fragment="layui-js">
    <script th:src="${_CPR_}+'/plugin/layui/layui.js'"></script>
</th:block>
<th:block th:fragment="layui-css-2_8_18">
    <link th:href="${_CPR_}+'/plugin/layui-v2.8.18/layui/css/layui.css'" rel="stylesheet"/>
</th:block>
<th:block th:fragment="layui-js-2_8_18">
    <script th:src="${_CPR_}+'/plugin/layui-v2.8.18/layui/layui.js'"></script>
</th:block>
<th:block th:fragment="layui-css-2_9_13">
    <link th:href="${_CPR_}+'/plugin/layui-v2.9.13/layui/css/layui.css'" rel="stylesheet"/>
</th:block>
<th:block th:fragment="layui-js-2_9_13">
    <script th:src="${_CPR_}+'/plugin/layui-v2.9.13/layui/layui.js'"></script>
</th:block>

<!-- mCustomScrollbar插件 -->
<th:block th:fragment="jquery-mCustomScrollbar-css">
    <link th:href="${_CPR_}+'/css/jquery.mCustomScrollbar.css'" rel="stylesheet"/>
</th:block>
<th:block th:fragment="jquery-mCustomScrollbar-concat-min-js">
    <script th:src="${_CPR_}+'/js/jquery.mCustomScrollbar.concat.min.js'"></script>
</th:block>

</html>
