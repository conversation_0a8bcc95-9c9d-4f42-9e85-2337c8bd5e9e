<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>初始化学生范围</title>
    <link rel="stylesheet" th:href="@{~/css/creditManage/global.css}">
    <link rel="stylesheet" th:href="@{~/plugin/layui/css/layui.css}">
    <link rel="stylesheet" th:href="@{~/css/creditManage/dialog.css}">
    <style>
        #invigilateMax .dialog-title {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        #invigilateMax .dialog-title span {
            margin-right: 24px;
            background: url('/images/creditManage/export.png') no-repeat left center;
            background-size: 16px;
            color: #3A8BFF;
            width: auto;
            text-indent: 0;
            padding-left: 20px;
            font-size: 15px;
            cursor: pointer;
        }
    </style>
</head>

<body>
    <!-- 手动安排考试 -->
    <div class="masker"></div>
    <div class="dialog" id="invigilateMax">
        <div class="dialog-title">
            <h4>初始化学生范围</h4>
            <span id = "record">初始化记录</span>
        </div>
        <div class="dialog-con" style="margin-bottom: 100px;">
            <div class="item">
                <div class="label">选择学生年级</div>
                <div class="j-search-con multiple-box">
                    <input type="text" name="teacherName" placeholder="请选择" readonly="" class="schoolSel">
                    <span class="j-arrow"></span>
                    <div class="j-select-year ">
                        <div class="search">
                            <input type="text" placeholder="搜索">
                            <span></span>
                        </div>
                        <div class="all-selects">全选</div>
                        <ul name="teacherName">
                            <li th:each="data : ${gradeList}" th:text="${data}" th:data-id="${data}"></li>

                        </ul>
                    </div>
                </div>
            </div>


        </div>
        <div class="dialog-btn"><button class="pu-cancel">取消</button><button class="pu-sure"
                style="width: 88px;">确定</button>
        </div>
    </div>
</body>
<script th:src="@{~/js/jquery-3.3.1.min.js}"></script>
<script th:src="@{~/plugin/layui/layui.js}"></script>
<script th:src="@{~/js/my.util.js}"></script>
<script>
    var uid = [[${uid}]];
    var fid = [[${fid}]];
    var formId = [[${formId}]];
    $(".pu-cancel").click(function () {
        window.parent.postMessage(JSON.stringify({action: 0}), '*')
    })
    $(document).ready(function () {
        /* ***************** 下拉 **************************** */
        // 下拉多选
        $(".j-search-con").on("click", ".schoolSel", function (e) {
            $(".j-select").hide();
            let parent = $(this).parent();
            parent.find('.j-arrow').toggleClass('j-arrow-slide');
            parent.find('.j-select-year').toggleClass('slideShow');
            var sibling = $(this).parents('.item').siblings();
            sibling.find('.j-arrow').removeClass('j-arrow-slide');
            sibling.find('.j-select-year').removeClass('slideShow');
            stopBubble(e)
        })
        // 全选/取消全选
        $(".j-search-con").on('click', '.j-select-year .all-selects', function (e) {
            $(this).toggleClass("active");
            if ($(this).hasClass("active")) {
                $(this).next().find("li").addClass("active");
                $(this).text('取消全选');
                let selCon = [];
                let curEles = $(this).parent().find("li.active");

                curEles.each((index, ele) => {
                    let txt = $(ele).text();
                    selCon.push(txt);

                });
                $(this).parents('.j-search-con').find('.schoolSel').val(selCon.join(','))

            } else {
                $(this).next().find("li").removeClass("active");
                $(this).text('全选');
                $(this).parents('.j-search-con').find('.schoolSel').val('');
            }
            stopBubble(e)
        })
        // 选择-多选
        $(".j-search-con").on("click",
            ".j-select-year li ",
            function (e) {
                $(this).toggleClass("active");
                var parentEle = $(this).parent();
                var totallis = parentEle.find(".active").length;
                var curlis = parentEle.find("li:not(.active)").length;
                var prev = parentEle.prev(".all-selects")
                if (totallis == curlis) {
                    prev.addClass("active");
                    prev.text('取消全选')
                } else {
                    prev.removeClass("active");
                    prev.text('全选')
                }

                let selCon = [];
                let curEles = parentEle.find(".active");

                curEles.each((index, ele) => {
                    let txt = $(ele).text();
                    selCon.push(txt);
                });
                var schoolSelEle = $(this).parents('.j-search-con').find('.schoolSel')
                if (curEles.length > 0) {
                    schoolSelEle.val(selCon.join(','))
                } else {
                    schoolSelEle.val('');
                }
                stopBubble(e);
            })
        //  选择-单选
        $(".j-search-con.single-box").on("click",
            ".j-select-year li ",
            function (e) {
                $(this).addClass('active').siblings().removeClass();
                var parents = $(this).parents('.j-search-con');
                var schoolSelEle = parents.find('.schoolSel');
                var txt = $(this).text();
                schoolSelEle.val(txt);
                parents.find('.j-arrow').removeClass('j-arrow-slide');
                parents.find('.j-select-year').removeClass('slideShow')
                stopBubble(e);
            })
        // 搜索
        $(".j-search-con").on('click', '.search input', function (e) {
            stopBubble(e);
        })
        $(".j-search-con").on('input', '.search input', function (e) {
            let val = $(this).val();
            var nextEle = $(this).parents('.j-search-con').find('ul')
            if (val != "") {

                nextEle.find('li').each(function (i, ele) {
                    let txt = $(ele).text();
                    if (txt.indexOf(val) >= 0) {
                        $(ele).show();
                    } else {
                        $(ele).hide();
                    }
                })
            } else {
                nextEle.find('li').show();
            }
            stopBubble(e)
        })

        function stopBubble(e) {
            if (e && e.stopPropagation)
                e.stopPropagation();
            else {
                window.event.cancelBubble = true;
            }

        }
        $(document).on("click", function (event) {
            var _con = $('.select-input');
            if (!_con.is(event.target) && _con.has(event.target).length === 0) {
                $(".j-search-con").find('.j-arrow').removeClass('j-arrow-slide');
                $(".j-search-con").find('.j-select-year').removeClass('slideShow')
            }
        })

    })

    $(".pu-sure").click(function () {
        var gradeName = $("input[name='teacherName']").val()
        if (gradeName == null || gradeName == ""){
            U.fail("年级未选择");
            return false;
        }
        $.post("/api/credit/yy/syncXsjbxx", {
            fid: fid,
            formAlias: "xsjbxx",
            uid: uid,
            gradeName:gradeName
        }, function (res) {
            if (res.success) {
                U.success(res.message)
            } else {
                U.fail(res.message)
            }
        })
    })

    $("#record").click(function () {
        layui.use(["form", "table"], function () {
            layer.open({
                type: 2,  // 2表示弹出的是iframe，1表示弹出的是层
                offset: 'auto',
                title: false,
                area: ['858px', '560px'],
                scrollbar: true,
                content: "/sync/record/list.html?fid="+fid+"&tableName=xsjbxx",   // 弹出iframe的页面链接
                btn: '',
                shade: 0.3 //显示遮罩
            });
        })
    })

</script>

</html>