<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>考务</title>
  <link rel="stylesheet" th:href="@{~/css/examination/global2.css}">
  <link rel="stylesheet" th:href="@{~/layui/css/layui.css}">
  <link rel="stylesheet" th:href="@{~/css/examination/poup.css}">
  <style>
    .exam-popup .popup-con .from-wrapper .lable .textarea{
      height:132px;
      overflow-y: auto;
    }
    .exam-popup .popup-con .from-wrapper .lable .name i em:after{
      bottom:-5px;
    }
  </style>


</head>

<body>

<div id="generateExamNumber"  class="exam-popup popups" style="display: block;width: 400px">
  <div>&nbsp;</div>
  <div>&nbsp;</div>
  <div  style="font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;是否将所选数据生成一个考试班？</div>
  <div>&nbsp;</div>
  <div class="bottom" style="border:none;text-align: center !important;justify-content: normal">
    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<!--    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-->
<!--    <div onclick="cancle()" class="cancle">取消</div>-->
    <div onclick="break2()" class="confirm">确定</div>
  </div>
</div>

</body>

<script th:src="@{~/js/jquery-3.3.1.min.js}"></script>
<script th:src="@{~/plugin/layui-v2.8.18/layui/layui.js}" type="text/javascript" charset="utf-8"></script>
<script th:src="@{~/js/examination/Sortable.js}"></script>
<script th:src="@{~/js/my.util.js}"></script>
<!--<script th:src="@{~/js/alert.js}"></script>-->
<script th:inline="javascript">
  var fid = [[${fid}]];
  var uid = [[${uid}]];
  var queryId = [[${queryId}]];
  
  function break2() {
    // 遍历这些ul标签
    var data = {
      fid: fid,
      uid: uid,
      queryId: queryId,
    }
    $.get("/examination/result/break2", data, function (res) {
      if (res.code == 200) {
        U.success(res.msg, 2000)
        setTimeout(U.closePop, 2000)
      } else {
        U.fail("保存失败", 2000)
      }
    })

  }

function cancle() {
  window.parent.postMessage(JSON.stringify({action: 1}), "*");
  // window.parent.postMessage({type: "close"}, '*');
}



</script>
<script>


</script>

</html>