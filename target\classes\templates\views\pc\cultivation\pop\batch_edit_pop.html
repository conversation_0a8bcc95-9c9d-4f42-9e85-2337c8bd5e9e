<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量修改</title>
    <link rel="stylesheet" th:href="@{/css/global.css}">
    <link rel="stylesheet" th:href="@{/css/cultivation/batchEditPop.css}">
</head>

<body>
<div class="mask" style="z-index: 16; display: none;"></div>
<div class="mould_pop batch_editor_pop">
    <div class="pop_cont">
        <div class="bep_cont">
            <p class="bep_tip mb20"></p>
            <div class="bep_per mb20">
                <div class="bep_per_left fl">修改字段</div>
                <div class="bep_per_right fl">
                    <div class="bep_per_sel"><input type="text" readonly="readonly" class="bep_per_input">
                        <div class="bep_search_sel one" style="display: none;">
                            <ul class="bep_per_uls">
                                <li title="kkgl_sflcj" class="bep_per_lis">是否录成绩</li>
                                <li title="kkgl_zc" class="bep_per_lis">周次</li>
                                <li title="kkgl_zks" class="bep_per_lis">周课时</li>
                                <li title="kkgl_lpjc" class="bep_per_lis">联排节次</li>
                                <li title="kkgl_lpgz" class="bep_per_lis">联排规则</li>
                                <li title="kkgl_xf" class="bep_per_lis">学分</li>
                                <li title="kkgl_jslx" class="bep_per_lis">教室类型</li>
                            </ul>
                        </div>
                    </div>
                    <p class="bep_per_tip">修改已选数据，如需修改所有数据请先勾选表格下方“选中所有数据”</p></div>
                <div class="clear"></div>
            </div>
            <div class="bep_per">
                <div class="bep_per_left fl">修改为</div>
                <div class="bep_per_right fl">
                    <div class="bep_per_sel formField">
                        <input type="text" placeholder="请输入" class="bep_per_text">
                    </div>
                    <p class="bep_per_tip">如需清空字段，保留此项为空即可</p></div>
                <div class="clear"></div>
            </div>
        </div>
    </div>
    <div class="pop_btm"><span class="pop_cal">取消</span> <span class="pop_sure">提交</span></div>
</div>
</body>
<script th:src="@{/js/jquery-3.3.1.min.js}"></script>
<script th:src="@{/js/my.util.js}"></script>
<script>
    let fid = "[[${formTopBtnBO.fid}]]";
    let uid = "[[${formTopBtnBO.uid}]]";
    let queryId = "[[${formTopBtnBO.queryId}]]";
    document.domain = document.domain.split('.').slice(-2).join('.');
    $(".mb20 .bep_per_right").click(function () {
        $(".one").show();
        $(".formFieldSel").hide();
    })
    // 选择
    $(".bep_per_sel").on('click', "ul li", function (event) {
        const txt = $(this).text();
        const title = $(this).attr("title");
        $(this).parents(".bep_per_sel").find(".bep_per_input").attr("title", title);
        $(this).parents(".bep_per_sel").find(".bep_per_input").val(txt);
        $(this).parents(".bep_per_sel").find(".bep_per_text").val(txt);
        $(".bep_search_sel").hide();
        $(this).addClass('active').siblings().removeClass('active');
        if ($(this).parents(".formField").length === 0) {
            let html = "<input type=\"text\" placeholder=\"请输入\" class=\"bep_per_text\">";
            if (txt === "是否录成绩") {
                html += "<div class=\"bep_search_sel formFieldSel\" style=\"display: none;\"><ul class=\"bep_per_uls\">" +
                    "<li class=\"bep_per_lis\">是</li>" +
                    "<li class=\"bep_per_lis\">否</li>" +
                    "</ul></div>";
            }
            if (txt === "教室类型" || txt === "联排规则") {
                let count = 0;
                let formAlias = txt === "教室类型" ? "238225" : "lpjczdx";
                let fieldAlias = txt === "教室类型" ? "zd_jslx" : "lpjczdx_ltgz";
                html += "<div class=\"bep_search_sel formFieldSel\" style=\"display: none;\"><ul class=\"bep_per_uls\">";
                $.ajax({
                    type: 'post',
                    url: "/teacherIdle/getFormDistinctFiled",
                    data: {formAlias: formAlias, fieldAlias: fieldAlias},
                    dataType: 'json',
                    async: false,
                    success: function (data) {
                        if (data.list) {
                            count = data.list.length;
                            for (let i = 0; i < data.list.length; i++) {
                                html += "<li class=\"bep_per_lis\">" + data.list[i] + "</li>";
                            }
                        }
                    }
                });
                html += "</ul></div>";
                if (count === 0) {
                    html = "<input type=\"text\" placeholder=\"请输入\" class=\"bep_per_text\">";
                }
            }
            $(".formField").html(html);
        }
        event.stopPropagation();
    })

    $(document).on("click", function (event) {
        const target = event.target;
        const isChildElement = $(target).hasClass('bep_per_input') || $(target).hasClass('bep_per_text');
        if (!isChildElement) {
            $(".bep_search_sel").hide();
        }
        event.stopPropagation();
    })

    $(".pop_sure").click(function () {
        const formField = $(".bep_per_input").attr("title");
        const formFieldVal = $(".bep_per_text").val();
        const regexMap = {
            kkgl_zc: /^\d+(-\d+)?(,\d+(-\d+)?)*$/,
            kkgl_zks: /^\d+$/,
            kkgl_lpjc: /^\d+$/,
            kkgl_lpgz: /^\d+(\+\d+)+$/,
            kkgl_xf: /^\d+$/
        };
        const regex = regexMap[formField];
        if (regex && !regex.test(formFieldVal)) {
            const errorMessageMap = {
                kkgl_zc: "请按规范输入周次，标准格式为：连续周用\"-\"（英文连字符）相连，例\"1-10\"表示1至10周；不连续周次用\",\"（英文逗号）隔开，例\"1-3,14-14\"表示1至3周和第14周",
                kkgl_zks: "请输入整数数字",
                kkgl_lpjc: "请输入小于等于周课时的整数数字",
                kkgl_lpgz: "请按规范输入联排规则，标准格式为：课时数1+课时数2+...+课时数n，用\"+\"相连，例\"3+2+2\"表示排课时会将7个周课时分为1个3节联排、2个2节联排",
                kkgl_xf: "请输入纯数字"
            };
            U.fail(errorMessageMap[formField]);
            return false;
        }
        $.post("../cultivation/appointTeacher", {
            fid: fid,
            uid: uid,
            queryId: queryId,
            formField: formField,
            formFieldVal: formFieldVal
        }, function (result) {
            U.success("更新成功", 2000);
            setTimeout(closePop, 2000);
        }, "json");
    })

    $(".pop_cal").click(function () {
        window.parent.postMessage(JSON.stringify({action: 1}), "*");
    })

    function closePop() {
        window.parent.postMessage(JSON.stringify({action: 1}), "*");
    }

    $(".formField").click(function () {
        $(".formFieldSel").show();
    })
</script>

</html>