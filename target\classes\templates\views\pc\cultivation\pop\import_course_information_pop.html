<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量导入</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_}+'/css/cultivation/global.css'"/>
    <link rel="stylesheet"
          th:href="@{${_CPR_+_VR_}+'/css/cultivation/major_course_set.css'(v=${new java.util.Date().getTime()})}"/>
    <link rel="stylesheet" th:href="${_CPR_+_VR_}+'/plugin/layui/css/layui2.8.2.css'"/>
    <style>
        .dialog-title {
            margin-top: 10px;
            border-top: 1px solid #e5e6eb;
            border-bottom: 0 solid #e5e6eb !important;
        }

        #importTeachProcess .dialog-con {
            padding: 0 30px 30px 30px !important;
        }

        #importResult .dialog-con {
            padding: 45px 100px;
        }

        #importTeachProcessFile .dialog-con {
            padding: 35px 100px;
        }
    </style>
    <script th:src="${_CPR_+_VR_}+'/plugin/layui/layui2.8.2.js'"></script>
</head>
<body>
<!-- 导入教学进程表  -->
<div id="importTeachProcess" class="dialog" style="display: block;">
    <div class="dialog-title">
        <h5></h5>
        <div class="opt-right">
            <a class="btn-export" style="display: none;">导出上次错误数据项</a>
        </div>
    </div>
    <div class="dialog-con">
        <h5>注意事项:</h5>
        <p>1.表格第一行是表头，对应系统字段，请不要轻易删改。</p>
        <p>2.子表单明细同样占用行数。</p>
        <p>3.为保证数据导入顺利,请<a class="down-template">下载excel模板</a>，并仔细阅读其中的导入示例和说明。</p>
        <button type="button" class="layui-btn demo-class-accept" id="uploadBtn">
            上传文件
        </button>
    </div>
</div>
<!-- 处理导入文件 -->
<div class="dialog" id="importTeachProcessFile" style="width: 563px;">
    <div class="dialog-con">
        <img class="loding" src="/images/cultivation/mooc/loading.gif" alt="">
        <div class="importTips importTipsSuccess importTipsError">
            <p><span class="icon"></span>处理中...成功上传<em class="green">0</em>条数据<span class="error">，失败<em
                    class="yellow">0</em>条数据，</span></p>
            <p>请修改后再次导入</p>

        </div>
    </div>
    <div class="dialog-footer">
        <button id="importTeachCancelBtn">取消</button>
        <button id="importTeachSureBtn">确定</button>
    </div>
</div>
<!-- 导入结果 失败增加-->
<div class="dialog importSuccess" id="importResult" style="width: 563px;">
    <div class="dialog-con ">
        <div class="importIcon"></div>
        <div class="importTips ">
            <p>处理完成，成功上传<em class="green">0</em>条数据<span class="error">，失败<em
                    class="yellow">0</em>条数据，</span>
            </p>
            <p>请修改后再次导入</p>
        </div>
    </div>
    <div class="dialog-footer">
        <a href="#" id="importErrorData">导出错误数据项</a>
        <button id="importSureBtn">确定</button>
    </div>
</div>
<script th:inline="javascript">
    const _VR_ = [[${_VR_}]];
    const fid = [[${formTopBtnVO.fid}]];
    const uid = [[${formTopBtnVO.uid}]];
    let layerIndex, layerIndex1, layerIndex2;
    layui.use(['table', 'jquery', 'util', 'form', 'layer', 'laytpl', 'upload'], function () {
        const layer = layui.layer, upload = layui.upload, $ = layui.jquery;
        upload.render({
            elem: "#uploadBtn",//导入id
            url: _VR_ + "/cultivation/importCourseInformationHandler",
            accept: "file",
            exts: 'xls|excel|xlsx',
            acceptMime: 'application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            before: function () {
                this.data = {
                    fid: fid,
                    uid: uid
                }
                const timer = setInterval(function () {
                    $.post(_VR_ + '/cultivation/getImportProcess', {
                        successKey: "course_information_success_record_" + uid + "_" + fid,
                        failedKey: "course_information_fail_record_" + uid + "_" + fid,
                        totalKey: "course_information_total_record_" + uid + "_" + fid
                    }, function (response) {
                        $("#importTeachProcessFile .green,#importResult .green").text(response.successCount);
                        $("#importTeachProcessFile .yellow,#importResult .yellow").text(response.failCount);
                        if (response.failCount > 0) {
                            $("#importResult").addClass("importError");
                        }
                        if (response.successCount + response.failCount === response.totalCount) {
                            clearInterval(timer);
                            layer.close(layerIndex);
                            layerIndex1 = layer.open({
                                type: 1,
                                title: false,
                                closeBtn: false,
                                shadeClose: true,
                                isOutAnim: true,
                                content: $('#importResult'),
                                area: ['auto', 'auto'],
                                success: function () {
                                },
                            });
                        }
                    })
                }, 2000);
                layerIndex = layer.open({
                    type: 1,
                    title: false,
                    closeBtn: false,
                    shadeClose: true,
                    isOutAnim: true,
                    content: $('#importTeachProcessFile'),
                    area: ['auto', 'auto'],
                    success: function () {
                    },
                });
            },
            done: function (res) {
            }
        });
        $.post(_VR_ + '/cultivation/getErrorDataFile', {
            filePath: "/mnt/mfs/academic/course_information_error_" + uid + "_" + fid + ".xlsx"
        }, function (response) {
            if (response) {
                $(".btn-export").show();
            }
        });
        $("#importTeachCancelBtn,#importTeachSureBtn,#importSureBtn").click(function () {
            window.parent.postMessage(JSON.stringify({action: 1}), "*");
        })
        //导出上次错误数据
        $(".btn-export,#importErrorData").click(function () {
            fetch("/down/course_information_error_" + uid + "_" + fid + ".xlsx")
                .then(response => response.blob())
                .then(blob => {
                    const url = window.URL.createObjectURL(blob);
                    const downloadLink = document.createElement('a');
                    downloadLink.style.display = 'none';
                    downloadLink.href = url;
                    downloadLink.download = "开课信息表（错误数据）.xlsx";
                    document.body.appendChild(downloadLink);
                    downloadLink.click();
                    document.body.removeChild(downloadLink);
                });
        })
        //下载模板
        $(".down-template").click(function () {
            layer.load(0, {shade: [0.1, '#fff']});
            $.post(_VR_ + '/cultivation/downImportTpl', {
                fid: fid,
                fileName: "courseInformationImportTemplate.xlsx"
            }, function (response) {
                layer.closeAll('loading');
                fetch("/down/" + response.data.file + ".xlsx")
                    .then(response => response.blob())
                    .then(blob => {
                        const url = window.URL.createObjectURL(blob);
                        const downloadLink = document.createElement('a');
                        downloadLink.style.display = 'none';
                        downloadLink.href = url;
                        downloadLink.download = "开课信息表导入模板.xlsx";
                        document.body.appendChild(downloadLink);
                        downloadLink.click();
                        document.body.removeChild(downloadLink);
                    });
            });
        })
    })
</script>
</body>
</html>