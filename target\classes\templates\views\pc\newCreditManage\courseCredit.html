<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>课程学分基础设置</title>
    <link rel="stylesheet" th:href="@{~/css/creditManage/global.css}">
    <link rel="stylesheet" th:href="@{~/plugin/layui/css/layui.css}">
    <link rel="stylesheet" th:href="@{~/css/creditManage/common.css}">
    <link rel="stylesheet" th:href="@{~/css/creditManage/index.css}">
    <link rel="stylesheet" th:href="@{~/css/creditManage/reset.css}">
    <link rel="stylesheet" th:href="@{~/css/creditManage/scoreRule.css}">
    <style>
        .input .layui-input {
            transition: unset !important;
        }

        html{
            overflow-x: hidden; overflow-y: auto;
        }
        .main .content {
            padding: 30px;
        }
    </style>
</head>


<body>
<div class="main">
    <div class="top" th:styleappend="${nh==1?'display: none;':''}">
        <div class="title">
            <div class="back">返回</div>
            <div class="levelone">学分参数设置</div>
            <div class="icon"></div>
            <div class="leveltwo">课程学分规则设置</div>
        </div>

    </div>
    <div class="container content" th:classappend="${nh==1?'ruleContainer':''}">
        <div class="con-title">课程学分基础设置</div>
        <form class="layui-form " id="formCon" action="#" lay-filter="formCon">
            <div class="layui-form-item" th:styleappend="${nh==0&&set.status==1?'display: none;':''}">
                <label class="layui-form-label">是否启用课程学分：</label>
                <div class="layui-input-block item-radio">
                    <div class="radio-list">
                        <div th:if="${set.status==1}">
                            <input type="radio" lay-filter="openCourse" name="openCourse" value="1" title="是" checked>
                            <input type="radio" lay-filter="openCourse" name="openCourse" value="0" title="否">
                        </div>
                        <div th:unless="${set.status==1}">
                            <input type="radio" lay-filter="openCourse" name="openCourse" value="1" title="是">
                            <input type="radio" lay-filter="openCourse" name="openCourse" value="0" title="否" checked>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-con" th:styleappend="${set.status==1?'display: black;':'display: none;'}">
                <div class="layui-form-item">
                    <label class="layui-form-label">学年学期：</label>
                    <div class="layui-input-block">
                        <div class="j-search-con single-box">
                            <input type="text" placeholder="请选择" readonly class="schoolSel xnxq" id="xnxq">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <ul class="xnxqul">
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">学分保留小数位数：</label>
                    <div class="input">
                        <input id="decimal"
                               name="decimal"
                               lay-filter="decimal"
                               th:value="${set.decimal}"
                               lay-verify="title" autocomplete="off"
                               placeholder="请输入整数" class="layui-input"
                               oninput="value=value.replace(/[^\d]/g,'');if(value>5)value=5;if(value.length>1)value=value.slice(0,1);if(value<0)value=0;">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">学分是否需要四舍五入：</label>
                    <div class="layui-input-block item-radio">
                        <div class="radio-list">
                            <div th:if="${set.round==1}">
                                <input type="radio" name="round" lay-filter="round" value="1" title="是" checked>
                                <input type="radio" name="round" lay-filter="round" value="0" title="否">
                            </div>
                            <div th:unless="${set.round==1}">
                                <input type="radio" name="round" lay-filter="round" value="1" title="是">
                                <input type="radio" name="round" lay-filter="round" value="0" title="否" checked>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="con-title mar30">课程学分换算规则设置</div>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="width: 105px;"><em>*</em>适用成绩类型</label>
                    <div class="layui-input-block" style="margin-left: 105px;">
                        <div class="j-search-con multiple-box">
                            <input type="text" placeholder="请选择" readonly class="schoolSel" id="scoreType">
                            <span class="j-arrow"></span>
                            <div class="j-select-year ">
                                <ul>
                                    <li>正考</li>
                                    <li>补考</li>
                                    <li>重修</li>
                                    <li>毕业考试</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 正考 -->
                <div class="examCon" id="examCon1">
                    <div class="showMore clicked moreEle">
                        <span>正考</span>
                        <em></em>
                    </div>
                    <div class="examScore">
                        <div class="addRule" id="addRule">新增换算规则</div>
                        <div class="tableCon">
                            <table class="layui-hide materialTable" id="materialTable" lay-filter="materialTable">
                            </table>
                        </div>

                    </div>
                </div>
                <!-- 补考 -->
                <div class="examCon" id="examCon2">
                    <div class="showMore clicked moreEle">
                        <span>补考</span>
                        <em></em>
                    </div>
                    <div class="examScore">
                        <div class="layui-form-item">
                            <label class="layui-form-label" style="width: 119px;"><em>*</em>选择换算方式</label>
                            <div class="layui-input-block" style="margin-left: 119px;">
                                <div class="j-search-con single-box">
                                    <input type="text" placeholder="请选择" readonly class="schoolSel" id="bkMethod">
                                    <span class="j-arrow"></span>
                                    <div class="j-select-year">
                                        <ul>
                                            <li data-type="scoreLevel">成绩等级</li>
                                            <li data-type="conversionMethod">公式计算</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item" style="display: none;">
                            <label class="layui-form-label" style="width: 84px;"><em>*</em>实际学分=</label>
                            <div class="layui-input-block" style="margin-left: 84px;">
                                <div class="actualCredits" id="actualCredits1">点击输入计算公式</div>
                            </div>
                        </div>
                        <div class="materialTableCon" style="display: none;">
                            <table class="layui-hide bukao" id="bukao" lay-filter="bukao">
                            </table>
                        </div>

                    </div>
                </div>
                <!-- 重修 -->
                <div class="examCon" id="examCon3">
                    <div class="showMore clicked moreEle">
                        <span>重修</span>
                        <em></em>
                    </div>
                    <div class="examScore">
                        <div class="layui-form-item">
                            <label class="layui-form-label" style="width: 119px;"><em>*</em>选择换算方式</label>
                            <div class="layui-input-block" style="margin-left: 119px;">
                                <div class="j-search-con single-box">
                                    <input type="text" placeholder="请选择" readonly class="schoolSel" id="cxMethod">
                                    <span class="j-arrow"></span>
                                    <div class="j-select-year">
                                        <ul>
                                            <li data-type="scoreLevel">成绩等级</li>
                                            <li data-type="conversionMethod">公式计算</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item" style="display: none;">
                            <label class="layui-form-label" style="width: 84px;"><em>*</em>实际学分=</label>
                            <div class="layui-input-block" style="margin-left: 84px;">
                                <div class="actualCredits" id="actualCredits2">点击输入计算公式</div>
                            </div>
                        </div>
                        <div class="materialTableCon" style="display: none;">
                            <table class="layui-hide chongxiu" id="chongxiu" lay-filter="chongxiu">
                            </table>
                        </div>

                    </div>
                </div>
                <!--毕业考试-->
                <div class="examCon" id="examCon4">
                    <div class="showMore clicked moreEle">
                        <span>毕业考试</span>
                        <em></em>
                    </div>
                    <div class="examScore">
                        <div class="layui-form-item">
                            <label class="layui-form-label" style="width: 119px;"><em>*</em>选择换算方式</label>
                            <div class="layui-input-block" style="margin-left: 119px;">
                                <div class="j-search-con single-box">
                                    <input type="text" placeholder="请选择" readonly class="schoolSel" id="qkMethod">
                                    <span class="j-arrow"></span>
                                    <div class="j-select-year">
                                        <ul>
                                            <li data-type="scoreLevel">成绩等级</li>
                                            <li data-type="conversionMethod">公式计算</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item" style="display: none;">
                            <label class="layui-form-label" style="width: 84px;"><em>*</em>实际学分=</label>
                            <div class="layui-input-block" style="margin-left: 84px;">
                                <div class="actualCredits" id="actualCredits3">点击输入计算公式</div>
                            </div>
                        </div>
                        <div class="materialTableCon" style="display: none;">
                            <table class="layui-hide qingkao" id="qingkao" lay-filter="qingkao">
                            </table>
                        </div>

                    </div>
                </div>
            </div>
        </form>
        <div class="save-btn" id="saveBth">保存设置</div>
    </div>
</div>
<!-- 新增换算规则 -->
<div class="popup formulaDialogRule" id="dialogRule">
    <div class="dialogRule">
        <div class="dialog-title">
            <h3>新增换算规则</h3>
            <span class="dialog-close"></span>
        </div>
        <div class="dialog-con">
            <form action="" class="layui-form">
                <div class="layui-form-item">
                    <label class="layui-form-label">规则代码</label>
                    <div class="layui-input-block">
                        <input type="text" name="title" required disabled placeholder="请输入" id="ruleCode"
                               autocomplete="off" class="layui-input input-disabled">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>规则名称</label>
                    <div class="layui-input-block">
                        <input type="text" name="title" required lay-verify="required" placeholder="请输入"
                               autocomplete="off" class="layui-input" id="ruleName">
                    </div>
                </div>
                <div class="con-title">课程学分基础设置</div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><em>*</em>成绩数据类型</label>
                    <div class="layui-input-block">
                        <div class="j-search-con single-box">
                            <input type="text" placeholder="请选择" readonly class="schoolSel" id="dataType">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <ul>
                                    <li data-type="examCla">等级</li>
                                    <li data-type="examCla">分数</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 选择等级 -->
                <div class="selLevel" id="selLevel" style="display: none;">
                    <div class="layui-form-item">
                        <label class="layui-form-label"><em>*</em>选择等级制名称</label>
                        <div class="layui-input-block">
                            <div class="j-search-con single-box">
                                <input type="text" placeholder="请选择" readonly class="schoolSel" id="gradeName">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <ul id="gradeUl">
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="setRule" id="setRuleLevel" style="display: none;">
                        <div class="showMore clicked">
                            <span>学分换算规则设置</span>
                        </div>
                        <div class="scoreTab" id="scoreTab">
                            <table class="layui-hide materialTable2" id="materialTable2" lay-filter="materialTable2">
                            </table>
                        </div>
                    </div>
                </div>
                <!-- 选择分数 -->
                <div class="selScore" id="selScore" style="display: none;">
                    <div class="layui-form-item">
                        <label class="layui-form-label"><em>*</em>选择换算方式</label>
                        <div class="layui-input-block">
                            <div class="j-search-con single-box">
                                <input type="text" placeholder="请选择" readonly class="schoolSel" id="method">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <ul>
                                        <li data-type="examScore">成绩区间</li>
                                        <li data-type="examScore">公式计算</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 成绩区间 -->
                    <div class="setRule" id="setRuleResult" style="display: none;">
                        <div class="showMore clicked">
                            <span>学分换算规则设置</span>
                        </div>
                        <div class="addScoreRule" id="addScoreRule">添加</div>
                        <div class="scoreTab">
                            <table class="layui-hide materialTable3" id="materialTable3" lay-filter="materialTable3">
                            </table>
                        </div>
                    </div>
                    <!-- 公式计算 -->
                    <div class="lab-con" id="setRuleFormula" style="display: none;">
                        <div class="lab">
                            <div class="f-top"><em>*</em>计算公式</div>
                            <div class="section">
                                <div class="tit">实际学分=</div>
                                <div class="s-con">
                                </div>
                            </div>
                        </div>
                        <div class="lab btns">
                            <div class="available">
                                <h3>可用变量</h3>
                                <div class="a-con">
                                    <ul>
                                        <li>原始成绩</li>
                                        <li>标准学分</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="keyboard">
                                <h3>计算键盘</h3>
                                <div class="k-con">
                                    <span class="sign lbracket">(</span>
                                    <span class="sign rbracket">)</span>
                                    <span class="delet"></span>
                                    <span class="empty">清空</span>
                                    <span class="num">7</span>
                                    <span class="num">8</span>
                                    <span class="num">9</span>
                                    <span class="sign sign-add">+</span>
                                    <span class="num">4</span>
                                    <span class="num">5</span>
                                    <span class="num">6</span>
                                    <span class="sign sign-cancle">－</span>
                                    <span class="num">1</span>
                                    <span class="num">2</span>
                                    <span class="num">3</span>
                                    <span class="sign sign-mul">×</span>
                                    <span class="num zero">0</span>
                                    <span class="num spot">.</span>
                                    <span class="sign sign-except">÷</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="dialog-footer">
            <span class="btn-cancel" id="ruleCancelBtn">取消</span>
            <span class="btn-sure" id="ruleSureBtn">确定</span>
        </div>
    </div>
</div>
<!-- 成绩区间添加 -->
<div class="popup formulaConversion" id="achievementRange">
    <div class="popup-box">
        <div class="pu-title">添加</div>
        <div class="pu-con">

            <div class="lab score-range">
                <div class="f-top"><em>*</em>成绩区间</div>
                <div class="oprate">
                    <div class="inputs">
                        <input class="input inp minScore layui-input" type="number" placeholder="请输入">
                        <div class="error">请重新输入</div>
                        <div class="select-input pre-sel">
                            <div class="name ckd minSymbol">≤</div>
                            <em></em>
                            <div class="select-dropdown">
                                <ul class="dropdown-list minScoreList">
                                    <li><</li>
                                    <li class="cur">≤</li>
                                    <li>=</li>
                                    <li>为空</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="select-input select-score">
                        原始成绩
                    </div>
                    <div class="inputs right-inp">

                        <div class="select-input next-sel">
                            <div class="name ckd maxSymbol">≤</div>
                            <em></em>
                            <div class="select-dropdown ">
                                <ul class="dropdown-list maxScoreList">
                                    <li><</li>
                                    <li class="cur">≤</li>
                                    <li>=</li>
                                    <li>为空</li>
                                </ul>
                            </div>
                        </div>
                        <input class="input inpf maxScore layui-input" type="number" placeholder="请输入">
                        <div class="error">请重新输入</div>

                    </div>
                </div>
            </div>
            <div class="lab">
                <div class="f-top"><em>*</em>计算公式</div>
                <div class="section">
                    <div class="tit">实际学分=</div>
                    <div class="s-con">
                        <!-- <span>标准学分</span>
                        <em>+－×÷</em> -->
                    </div>
                </div>
            </div>
            <div class="lab btns">
                <div class="available">
                    <h3>可用变量</h3>
                    <div class="a-con">
                        <ul>
                            <li>原始成绩</li>
                            <li>标准学分</li>
                        </ul>
                    </div>
                </div>
                <div class="keyboard">
                    <h3>计算键盘</h3>
                    <div class="k-con">
                        <span class="sign lbracket">(</span>
                        <span class="sign rbracket">)</span>
                        <span class="delet"></span>
                        <span class="empty">清空</span>
                        <span class="num">7</span>
                        <span class="num">8</span>
                        <span class="num">9</span>
                        <span class="sign sign-add">+</span>
                        <span class="num">4</span>
                        <span class="num">5</span>
                        <span class="num">6</span>
                        <span class="sign sign-cancle">－</span>
                        <span class="num">1</span>
                        <span class="num">2</span>
                        <span class="num">3</span>
                        <span class="sign sign-mul">×</span>
                        <span class="num zero">0</span>
                        <span class="num spot">.</span>
                        <span class="sign sign-except">÷</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="pu-btn">
            <button class="pu-cancel">取消</button>
            <button class="pu-sure" id="achievementRangeBtn">确定</button>
        </div>

    </div>
</div>
<!-- 补考编辑 -->
<div class="popup formulaConversion" id="resitEdit">
    <div class="popup-box">
        <div class="pu-title">编辑</div>
        <div class="pu-con">
            <div class="lab t-lab">
                <div class="f-top"><em>*</em>等级名称</div>
                <div class="input">及格</div>
            </div>

            <div class="lab">
                <div class="f-top"><em>*</em>计算公式</div>
                <div class="section">
                    <div class="tit">实际学分=</div>
                    <div class="s-con">
                        <!-- <span>标准学分</span>
                        <em>+－×÷</em> -->
                    </div>
                </div>
            </div>
            <div class="lab btns">
                <div class="available">
                    <h3>可用变量</h3>
                    <div class="a-con">
                        <ul>
                            <li>原始成绩</li>
                            <li>标准学分</li>
                        </ul>
                    </div>
                </div>
                <div class="keyboard">
                    <h3>计算键盘</h3>
                    <div class="k-con">
                        <span class="sign lbracket">(</span>
                        <span class="sign rbracket">)</span>
                        <span class="delet"></span>
                        <span class="empty">清空</span>
                        <span class="num">7</span>
                        <span class="num">8</span>
                        <span class="num">9</span>
                        <span class="sign sign-add">+</span>
                        <span class="num">4</span>
                        <span class="num">5</span>
                        <span class="num">6</span>
                        <span class="sign sign-cancle">－</span>
                        <span class="num">1</span>
                        <span class="num">2</span>
                        <span class="num">3</span>
                        <span class="sign sign-mul">×</span>
                        <span class="num zero">0</span>
                        <span class="num spot">.</span>
                        <span class="sign sign-except">÷</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="pu-btn">
            <button class="pu-cancel">取消</button>
            <button class="pu-sure resitSureBtn">确定</button>
        </div>

    </div>
</div>

<!-- 换算规则 -->
<div class="popup formulaConversion" id="conversionRule1">
    <div class="popup-box">
        <div class="pu-title">编辑</div>
        <div class="pu-con">
            <div class="lab">
                <div class="f-top"><em>*</em>计算公式</div>
                <div class="section">
                    <div class="tit">实际学分=</div>
                    <div class="s-con">
                        <!-- <span>标准学分</span>
                        <em>+－×÷</em> -->
                    </div>
                </div>
            </div>
            <div class="lab btns">
                <div class="available">
                    <h3>可用变量</h3>
                    <div class="a-con">
                        <ul>
                            <li>原始成绩</li>
                            <li>标准学分</li>
                        </ul>
                    </div>
                </div>
                <div class="keyboard">
                    <h3>计算键盘</h3>
                    <div class="k-con">
                        <span class="sign lbracket">(</span>
                        <span class="sign rbracket">)</span>
                        <span class="delet"></span>
                        <span class="empty">清空</span>
                        <span class="num">7</span>
                        <span class="num">8</span>
                        <span class="num">9</span>
                        <span class="sign sign-add">+</span>
                        <span class="num">4</span>
                        <span class="num">5</span>
                        <span class="num">6</span>
                        <span class="sign sign-cancle">－</span>
                        <span class="num">1</span>
                        <span class="num">2</span>
                        <span class="num">3</span>
                        <span class="sign sign-mul">×</span>
                        <span class="num zero">0</span>
                        <span class="num spot">.</span>
                        <span class="sign sign-except">÷</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="pu-btn">
            <button class="pu-cancel">取消</button>
            <button class="pu-sure " id="conversionBtn1">确定</button>
        </div>

    </div>
</div>
<div class="popup formulaConversion" id="conversionRule2">
    <div class="popup-box">
        <div class="pu-title">编辑</div>
        <div class="pu-con">
            <div class="lab">
                <div class="f-top"><em>*</em>计算公式</div>
                <div class="section">
                    <div class="tit">实际学分=</div>
                    <div class="s-con">
                        <!-- <span>标准学分</span>
                        <em>+－×÷</em> -->
                    </div>
                </div>
            </div>
            <div class="lab btns">
                <div class="available">
                    <h3>可用变量</h3>
                    <div class="a-con">
                        <ul>
                            <li>原始成绩</li>
                            <li>标准学分</li>
                        </ul>
                    </div>
                </div>
                <div class="keyboard">
                    <h3>计算键盘</h3>
                    <div class="k-con">
                        <span class="sign lbracket">(</span>
                        <span class="sign rbracket">)</span>
                        <span class="delet"></span>
                        <span class="empty">清空</span>
                        <span class="num">7</span>
                        <span class="num">8</span>
                        <span class="num">9</span>
                        <span class="sign sign-add">+</span>
                        <span class="num">4</span>
                        <span class="num">5</span>
                        <span class="num">6</span>
                        <span class="sign sign-cancle">－</span>
                        <span class="num">1</span>
                        <span class="num">2</span>
                        <span class="num">3</span>
                        <span class="sign sign-mul">×</span>
                        <span class="num zero">0</span>
                        <span class="num spot">.</span>
                        <span class="sign sign-except">÷</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="pu-btn">
            <button class="pu-cancel">取消</button>
            <button class="pu-sure " id="conversionBtn2">确定</button>
        </div>

    </div>
</div>
<div class="popup formulaConversion" id="conversionRule3">
    <div class="popup-box">
        <div class="pu-title">编辑</div>
        <div class="pu-con">
            <div class="lab">
                <div class="f-top"><em>*</em>计算公式</div>
                <div class="section">
                    <div class="tit">实际学分=</div>
                    <div class="s-con">
                        <!-- <span>标准学分</span>
                        <em>+－×÷</em> -->
                    </div>
                </div>
            </div>
            <div class="lab btns">
                <div class="available">
                    <h3>可用变量</h3>
                    <div class="a-con">
                        <ul>
                            <li>原始成绩</li>
                            <li>标准学分</li>
                        </ul>
                    </div>
                </div>
                <div class="keyboard">
                    <h3>计算键盘</h3>
                    <div class="k-con">
                        <span class="sign lbracket">(</span>
                        <span class="sign rbracket">)</span>
                        <span class="delet"></span>
                        <span class="empty">清空</span>
                        <span class="num">7</span>
                        <span class="num">8</span>
                        <span class="num">9</span>
                        <span class="sign sign-add">+</span>
                        <span class="num">4</span>
                        <span class="num">5</span>
                        <span class="num">6</span>
                        <span class="sign sign-cancle">－</span>
                        <span class="num">1</span>
                        <span class="num">2</span>
                        <span class="num">3</span>
                        <span class="sign sign-mul">×</span>
                        <span class="num zero">0</span>
                        <span class="num spot">.</span>
                        <span class="sign sign-except">÷</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="pu-btn">
            <button class="pu-cancel">取消</button>
            <button class="pu-sure " id="conversionBtn3">确定</button>
        </div>

    </div>
</div>

<!-- 等级-两档制 -->
<div class="popup formulaConversion" id="levelRule">
    <div class="popup-box">
        <div class="pu-title">换算规则管理</div>
        <div class="pu-con">
            <div class="lab t-lab1">
                <div class="f-top"><em>*</em>级制基本信息</div>
                <div class="f-item-wrap ">
                    <div class="f-item ">
                        <div class="f-top">级制名称</div>
                        <div class="input">两档制</div>
                    </div>
                    <div class="f-item">
                        <div class="f-top">等级名称</div>
                        <div class="input">两档制</div>
                    </div>
                </div>

            </div>

            <div class="lab">
                <div class="f-top"><em>*</em>计算公式</div>
                <div class="section">
                    <div class="tit">实际学分=</div>
                    <div class="s-con">
                        <!-- <span>标准学分</span>
                        <em>+－×÷</em> -->
                    </div>
                </div>
            </div>
            <div class="lab btns">
                <div class="available">
                    <h3>可用变量</h3>
                    <div class="a-con">
                        <ul>
                            <li>原始成绩</li>
                            <li>标准学分</li>
                        </ul>
                    </div>
                </div>
                <div class="keyboard">
                    <h3>计算键盘</h3>
                    <div class="k-con">
                        <span class="sign lbracket">(</span>
                        <span class="sign rbracket">)</span>
                        <span class="delet"></span>
                        <span class="empty">清空</span>
                        <span class="num">7</span>
                        <span class="num">8</span>
                        <span class="num">9</span>
                        <span class="sign sign-add">+</span>
                        <span class="num">4</span>
                        <span class="num">5</span>
                        <span class="num">6</span>
                        <span class="sign sign-cancle">－</span>
                        <span class="num">1</span>
                        <span class="num">2</span>
                        <span class="num">3</span>
                        <span class="sign sign-mul">×</span>
                        <span class="num zero">0</span>
                        <span class="num spot">.</span>
                        <span class="sign sign-except">÷</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="pu-btn">
            <button class="pu-cancel">取消</button>
            <button class="pu-sure" id="levelRuleBtn">确定</button>
        </div>

    </div>
</div>

<script type="text/html" id="tmplToolBar">
    <div class="oprate-table">
        <span class="edit" lay-event="edit">编辑</span>
        {{# if(d.isOpen==1){ }}
        <span class="stop" lay-event="stop">停用</span>
        {{# } else { }}
        <span class="stop" lay-event="stop">启用</span>
        {{# } }}
        <span class="delete" lay-event="delete">删除</span>
    </div>
</script>
<script type="text/html" id="tmplToolBar1">
    <div class="oprate-table">
        <span class="edit" lay-event="edit">编辑</span>
    </div>
</script>
<script type="text/html" id="tmplToolBar2">
    <div class="oprate-table">
        <span class="edit" lay-event="edit" style="margin-right: 16px;">编辑</span>
        <span class="delete" lay-event="delete">删除</span>
    </div>
</script>
<script type="text/html" id="isOpen">
    {{#  if(d.isOpen==1){ }}
    <span>是</span>
    {{#  } else { }}
    <span>否</span>
    {{#  } }}
</script>
<script type="text/html" id="formula">
    实际学分=
    {{# layui.each(d.formula, function(indexs, items){   }}
    {{items}}
    {{# });  }}
</script>
<script type="text/html" id="formulaScore">
    {{d.minScore}}{{d.minSymbol}}原始成绩{{d.maxSymbol}}{{d.maxScore}}
</script>
<script th:src="@{~/js/jquery-3.3.1.min.js}"></script>
<script th:src="@{~/plugin/layui/layui.js}"></script>
<script th:inline="javascript">
    var fid = [[${fid}]];
    var uid = [[${uid}]];
    var curxnxq = [[${xnxq}]];
    var status = [[${set.status}]];
    var setRound = [[${set.round}]];
    var setDecimal = [[${set.decimal}]];
</script>
<script th:src="@{~/js/creditManage/scoreRule.js}"></script>
<script>
    $(".back").click(function () {
        history.back();
    })
</script>