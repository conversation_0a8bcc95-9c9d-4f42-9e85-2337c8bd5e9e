<!DOCTYPE html>
<html lang="en">
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发放明细</title>
    <link rel="stylesheet" href="/distribution/css/global1.css">
    <link rel="stylesheet" href="/distribution/layui/css/layui.css">
    <link rel="stylesheet" href="/distribution/css/reset.css">
    <link rel="stylesheet" href="/distribution/css/reviewSub.css">
    <script src="/distribution/layui/layui.js"></script>
    <style>
        .j-material .j-search input{
            width:180px;
        }
        .j-material .j-search-item .j-select-year{
            width:178px;
        }
        .j-material .j-search-item .j-search-con{
            margin-right:0;
        }
        .j-material .j-search-item .button{
            margin-left:40px;
        }
        .j-material{
            min-height:auto;
        }
        .j-material .j-table{
            margin-bottom:0;
        }
        .j-material .j-table{
            margin:0;
            margin-bottom:40px;
        }
        .j-material .j-table .j-table-tr .cell .c-main .c-inform .item{
            background: transparent;
        }
        .j-material{
            min-height:calc(100vh - 40px);
        }
        .j-material .j-table{
            min-height:calc(100vh - 330px);
        }
        /*11.21*/
        .j-material .j-review-con .j-top .left{
            display: flex;
            display: -webkit-flex;
            align-items: center;
            justify-content: flex-start;
        }

        .j-material .j-review-con .j-top .j-search-item h5{
            color: #15171C;
            font-size: 14px;
            line-height: 30px;
            margin-right: 10px;
        }
        .j-material .j-review-con .j-top .j-search-item input{
             width:180px;
        }
    </style>
</head>

<body>
    <div class="j-material-wrap">
        <div class="j-material">
            <div class="j-title">
                <h4>发放明细</h4>
                <div class="j-btns">
                    <button class="btn-complate export">导出</button>
                </div>
            </div>
            <div class="j-review-con " style="padding-top:20px;">
                <div class="j-top">
                    <div class="left">
                        <div class="j-search-item">
                            <h5>班级</h5>
                            <div class="j-search-con">
                                <input type="text" placeholder="选择班级" id="schoolClass" readonly class="schoolSel">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <ul>
                                        <li th:each="data:${clist}" th:text="${data.bjxx_bjmc}"></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="j-search-item">
                            <h5>学期学年</h5>
                            <div class="j-search-con">
                                <input type="text" placeholder="选择学年" id="schoolYear" readonly class="schoolSel">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <ul>
                                        <li th:each="data:${xqlist}" th:text="${data.xnxq_xnxqh}"></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="j-btns">
	                    <button class="btn-complate search">确定</button>
	                </div>
                    <div class="status">
                        <span class="grant">发放</span>
                        <span class="ungrant">不发放</span>
                    </div>
                </div>
                 
                <div class="j-table" style="border:none;"> 
                    <table class="layui-table" id="materialTable" lay-filter="materialTable">
                    </table>
                </div>
            </div>
            <div id="coursePage"></div>
           
          
        </div>

       
        
    </div>
</body>
<script src="/distribution/js/jquery-3.3.1.min.js"></script>
<script src="/distribution/js/common.js"></script>
<script type="text/html" id="tmplToolBar2">
    <div class=" warehouse"  lay-event="warehouse">入库</div>
</script>
<script th:inline="javascript">
	var fid = [[${fid}]];
     let pageIndex=1;
     $(document).ready(function (){
    	 $(".j-btns .search").click(function (){
    		 getStudentList();
    	 })
    	 $(".j-btns .export").click(function (){
    		 var xnxq = $("#schoolYear").val();
        	 var className = $("#schoolClass").val();
        	 if(xnxq == '' || className == ''){
        		 alert("请选择班级和学年学期");
        		 return "";
        	 }
    		 window.location.href = "/api/provide/exportData?fid="+fid+"&xnxq="+xnxq+"&xzbmc="+className;
    	 })
     })
     function getStudentList(){
    	 var xnxq = $("#schoolYear").val();
    	 var className = $("#schoolClass").val();
    	 if(xnxq == '' || className == ''){
    		 alert("请选择班级和学年学期");
    		 return "";
    	 }
     	$.ajax({
             type: 'get',
             dataType: 'json',
             url: '/api/provide/getStudentList',
             data: {fid:fid,xnxq:xnxq,xzbmc:className},
             success: function (res){
                 if (res.status){
                     let data = res.list;
                     let map = res.map;
                     let courseData = new Array();
                     let title = new Array();
                     
                     title.push({
                            field: 'studentName',
                            title: '学生姓名',
                            align: 'center',
                            minWidth:150
                        });
                     title.push({
                         field: 'studentNumber',
                         title: '学生工号',
                         align: 'center',
                         width: 152,
                        
                     });
                     data.forEach(function (info,e){
                     	var json = {studentName: info.xm, studentNumber: info.xh};
                    	for(var i = 1; i < 30;i++){
                    		var jcbh = info[i];
                    		if(jcbh != undefined){
                    			if(info[jcbh] == '已发放' || info[jcbh] == '待发放'){
	                    			json[jcbh] = "1";
                    			}else{
                    				json[jcbh] = "0";
                    			}
                    			courseData.push(json);
                    			if(e == 0){
	                    			var sfield = {
	                                    field: jcbh,
	                                    title: map[jcbh],
	                                    align: 'center',
	                                    width: 152,
	                                    templet: function(d) {
	                                    	var jcbh = sfield.field;
		                                    if(d[jcbh]=="1") {
		                                        return '<span class="grant"></span>';
		                                    } else {
		                                        return '<span class="ungrant"></span>';
		                                    }
	                                    }
	                                };
	                    			title.push(sfield);
                    			}
                    		}
                    	}
                     })
                     layui.use(['table','jquery','laypage'], function () {
                         var table = layui.table;
                         var laypage = layui.laypage;
                         var $ = layui.jquery;
                         table.render({
                             elem: '#materialTable',
                             // url: '/static/json/table/user.json', //数据接口
                             data: courseData, //静态数据，真实数据用url接口
                             cols: [title]
                         });
                     });
                 }
             }
         });
     }

</script>

</html>