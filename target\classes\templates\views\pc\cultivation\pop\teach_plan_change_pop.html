<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <title>教学计划</title>
    <style>
        #tipDialog {
            width: 560px;
            background-color: #fff;
            border-radius: 10px;
            padding: 40px 100px;
            box-sizing: border-box;
        }

        #tipDialog img {
            display: block;
            width: 40px;
            height: 40px;
            margin: 0 auto 24px;
        }

        #tipDialog p {
            text-align: center;
            line-height: 22px;
            font-size: 16px;
            color: #1D2129;
        }

        #tipDialog #tipDialogSure {
            width: 88px;
            height: 34px;
            border-radius: 18px;
            background: #4D88FF;
            box-shadow: 0px 0px 8px 0px rgba(77, 136, 255, 0.3);
            color: #ffffff;
            outline: none;
            margin: 32px auto 0;
            border: none;
            cursor: pointer;
        }

        #tipDialog #tipDialogCancel {
            width: 88px;
            height: 34px;
            border-radius: 18px;
            background: #fff;
            color: #4E5969;
            outline: none;
            margin: 32px auto 0;
            border: 1px solid #C9CDD4;
            cursor: pointer;
            margin-right: 16px;
        }

        .dialog-btn {
            margin-left: 80px;
            display: none;
        }

    </style>
</head>
<body>
<div class="pouop" id="tipDialog">
    <img src="/images/cultivation/mooc/error-icon.png" alt="">
    <p><span></span>执行中</p>
    <div class="dialog-btn">
        <button id="tipDialogCancel">取消</button>
        <button id="tipDialogSure">确定</button>
    </div>

</div>
<script th:src="${_CPR_+_VR_+'/js/jquery-1.11.3.min.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/my.util.js'}"></script>
<script th:inline="javascript">
    const _VR_ = [[${_VR_}]] || '';
    document.domain = "chaoxing.com";
    $(document).ready(function () {
        let operateType = [[${operateType}]] === 1 ? '修改' : '删除';
        let flag;
        U.ajax({
            type: 'post',
            url: "/cultivation/teachPlanChangeOperateVerify",
            data: {
                fid: [[${formRightBtnVO.fid}]],
                formUserId: [[${formRightBtnVO.formUserId}]],
                operateType: [[${operateType}]]
            },
            dataType: 'json',
            async: false,
            success: function (result) {
                $("#tipDialog").attr("url", result.data.url).attr("precast", result.data.precast);
                flag = result.data.flag;
                if (!result.data.flag) {
                    return false;
                }
                let msg = result.data.flag === 2
                    ? "请确认是否" + operateType
                    : "当前教学计划已存在排课信息，请确认是否" + operateType;
                $("#tipDialog p").text(msg);
                $(".dialog-btn").show();
            }
        })
        if (!flag) {
            openApprove();
        }
    })

    $("#tipDialogSure").click(function () {
        openApprove();
    })


    $("#tipDialogCancel").click(function () {
        closePop();
    })

    function openApprove() {
        const temp_form = document.createElement('form');
        temp_form.action = $("#tipDialog").attr("url");
        temp_form.target = '_blank'
        temp_form.method = 'post'
        temp_form.style.display = 'none'
        const opt = document.createElement('textarea');
        opt.name = 'precast'
        opt.value = $("#tipDialog").attr("precast")
        temp_form.appendChild(opt)
        document.body.appendChild(temp_form)
        temp_form.submit();
        closePop();
    }

    function closePop() {
        window.parent.postMessage(JSON.stringify({action: 1}), "*");
    }
</script>
</body>
</html>