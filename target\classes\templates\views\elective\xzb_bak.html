<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
        content="width=device-width, user-scalable=no,initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <title>[[${xzb}]]</title>
    <script src="js/responsive.js"></script>
    <link rel="stylesheet" href="css/global.css">
    <link rel="stylesheet" href="css/iosSelect.css">
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/sta.css">
</head>
<body>
    <div class="search-wrap">
        <div class="search flex">
            <img src="images/search.png" alt="">
            <input type="text" placeholder="搜索学生姓名或学号" id="keyword" onkeyup="getList();">
        </div>
    </div>
    <div class="stu-list-wrap">
        <div class="class-mes">
            <ul>
                <li id="zc">正常：<i></i></li>
                <li id="cd">迟到：<i></i></li>
                <li id="kk">旷课：<i></i></li>
                <li id="qj">请假：<i></i></li>
                <li id="zt">早退：<i></i></li>
            </ul>
        </div>
        <div class="stu-list">
        </div>
    </div>
    <div class="dialog-wrap" id="stuClassMes">
        <div class="dialog stu-class-mes">
            <div class="dialog-close"></div>
            <ul>
            </ul>
        </div>
    </div>
</body>
<script src="js/CXJSBridge.js"></script>
<script src="js/iosSelect.js"></script>
<script src="js/zepto.min.js"></script>
<script src="js/export.js"></script>
<script>
	var xzb = '[[${xzb}]]';
	var courseTime = '[[${courseTime}]]';
//     function _jsBridgeReady() {
//         jsBridge.postNotification('CLIENT_CUSTOM_MENU', { show: 1, menu: "导出", option: 'exportData()' });
//     }
    $(document).ready(function () {
        var stuH = $(window).height() - $(".stu-list-wrap").offset().top;
        $(".stu-list-wrap").css({ height: stuH + "px" })
        // 查看详情
        $(".stu-list-wrap .stu-list").on('click', '.stu', function () {
        	var zcnum = $(this).find("li").eq(0).find("i").html();
        	var cdnum = $(this).find("li").eq(1).find("i").html();
        	var kknum = $(this).find("li").eq(2).find("i").html();
        	var qjnum = $(this).find("li").eq(3).find("i").html();
        	var ztnum = $(this).find("li").eq(4).find("i").html();
        	var uid = $(this).attr("uuuid");
        	$.ajax({
	        	type: "POST",
	        	url: "/elective/rollcall/getStuNumList",
	        	data:{uid:uid,courseTime:courseTime,zcnum:zcnum,cdnum:cdnum,kknum:kknum,qjnum:qjnum,ztnum:ztnum},
	        	dataType:'json',
	        	success: function (data) {
	        		var html = "";
	        		for(var i = 0;i<data.list.length;i++){
	        			 html += "<li>";
	        			 if(data.list[i].str == "正常"){
	        			 	html += "<h1>"+data.list[i].course+"</h1><span class=\"normal\">正常</span>";
	        			 }
	        			 if(data.list[i].str == "早退"){
	        			 	html += "<h1>"+data.list[i].course+"</h1><span class=\"normal leaveEarly\">早退</span>";
	        			 }
	        			 if(data.list[i].str == "迟到"){
	        			 	html += "<h1>"+data.list[i].course+"</h1><span class=\"normal beLate\">迟到</span>";
	        			 }
	        			 if(data.list[i].str == "请假"){
	        			 	html += "<h1>"+data.list[i].course+"</h1><span class=\"normal askLeave\">请假</span>";
	        			 }
	        			 if(data.list[i].str == "旷课"){
	        			 	html += "<h1>"+data.list[i].course+"</h1><span class=\"normal cutSchool\">旷课</span>";
	        			 }
	        			 html += "</li>";
	        		}
	        		$("#stuClassMes").find("ul").html(html);
	       		}
			});
            $("#stuClassMes").show();
        })
        // 关闭弹窗
        $(".dialog-close").click(function () {
            $("#stuClassMes").hide();
        })
    })
    getNum("zc");
    getNum("cd");
    getNum("zt");
    getNum("kk");
    getNum("qj");
    function getNum(str){
    	$.ajax({
        	type: "POST",
        	url: "/elective/rollcall/getXzbNum",
        	data:{str:str,courseTime:courseTime,xzb:xzb},
        	dataType:'json',
        	success: function (data) {
        		$("#"+str).find("i").html(data.num);
       		}
		});
    }
    var uids = [];
    getList();
    function getList(){
    	var keyword = $("#keyword").val();
    	$.ajax({
        	type: "POST",
        	url: "/elective/rollcall/getXzbStuList",
        	data:{courseTime:courseTime,xzb:xzb,keyword:keyword},
        	dataType:'json',
        	async:false,
        	success: function (data) {
        		var html = "";
                uids = [];
        		for(var i = 0;i<data.list.length;i++){
                    uids.push(data.list[i].studentUid);
        			html += "<div class=\"stu\" uuuid=\""+data.list[i].studentUid+"\">";
                	html += "<h1>"+data.list[i].studentRealname+"</h1>";
                	html += "<ul>";
                    html += "<li id=\"stuzc"+data.list[i].studentUid+"\">正常：<i></i></li>";
                    html += "<li id=\"stucd"+data.list[i].studentUid+"\">迟到：<i></i></li>";
                    html += "<li id=\"stukk"+data.list[i].studentUid+"\">旷课：<i></i></li>";
                    html += "<li id=\"stuqj"+data.list[i].studentUid+"\">请假：<i></i></li>";
                    html += "<li id=\"stuzt"+data.list[i].studentUid+"\">早退：<i></i></li>";
                    html += "</ul></div>";
        		}
        		$(".stu-list").html(html);
       		}
		});
		getDtail();
    }
    function getDtail(){
    	$.each(uids,function(index,item){
	    	if(item != undefined){
	    		getStuNum("zc",item);
	    		getStuNum("cd",item);
	    		getStuNum("zt",item);
	    		getStuNum("kk",item);
	    		getStuNum("qj",item);
	    	}
		});
    }
	function getStuNum(str,uid){
    	$.ajax({
        	type: "POST",
        	url: "/elective/rollcall/getStuNum",
        	data:{str:str,courseTime:courseTime,uid:uid},
        	dataType:'json',
        	success: function (data) {
        		$("#stu"+str+uid).find("i").html(data.num);
       		}
		});
    }
</script>
</html>