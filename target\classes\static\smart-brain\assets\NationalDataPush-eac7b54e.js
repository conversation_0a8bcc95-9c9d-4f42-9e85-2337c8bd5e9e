import{e as r,f as H,g as J,o as K,c as O,a as l,b as o,w as i,h as E,v as x,F as Q,d as v,p as W,i as X}from"./index-f3df8255.js";import{t as R,p as Y,s as Z,R as n,a as $,b as ee,m as te,E as ae,c as se,d as le,M as V,e as oe,f as ne,g as ue,h as re,i as ie}from"./basic.setting-61aeaadd.js";import{_ as ce}from"./_plugin-vue_export-helper-c27b6911.js";const h=f=>(W("data-v-98d6db6d"),f=f(),X(),f),pe={class:"data-push-wrap"},de=h(()=>l("div",{class:"label-info"},"基础设置",-1)),me={class:"form-item"},he={class:"form-item-score"},fe={class:"form-item"},_e={class:"push-top"},ve=h(()=>l("div",{class:"label-info"},"推送设置",-1)),be={class:"btn"},ge=h(()=>l("div",{class:"oprate"},[l("span",{class:"primary"},"--")],-1)),Se=h(()=>l("div",{class:"tip-box"},[l("div",{class:"icons loading"}),l("h3",null,[v("数据准备中..."),l("br")]),l("p",null,"数据推送任务开启成功")],-1)),Te=h(()=>l("div",{class:"tip-box"},[l("div",{class:"icons warning"}),l("h3",{style:{"margin-bottom":"24px"}},"确认是否开启推送？")],-1)),Pe={__name:"NationalDataPush",setup(f){const C=r("default"),T=r({}),A=r([{show:!0,label:"目标表",value:t=>{let e="";return d.value.forEach(s=>{if(t.formAlias===s.sourceAlias)return e=s.value,!1}),e}}]),p=r(2),a=r({enablePush:0,multipleState:!0,targetTable:[],semester:""}),B=()=>{Z(p.value).then(t=>{if(n.ecode(t)){n.errorTip(t.msg);return}const e=t.data||{};a.value.id=e.id,T.value.fid=e.fid,a.value.enablePush=e.enable===1,a.value.semester=e.semester,a.value.targetTable=e.targetTable?e.targetTable.split(","):[],k()}).catch(t=>{n.errorTip(t.message)})},b=r([]),L=async()=>{const t=await $(p.value);if(n.ecode(t)){n.errorTip(t.msg);return}let e=[];t.data.forEach(s=>e.push({label:s,value:s})),b.value=e},d=r([]),k=async()=>{const t=await ee(p.value);if(n.ecode(t)){n.errorTip(t.msg);return}const e=t.data,s=[];e.forEach(u=>{s.push({label:u.id.toString(),value:u.name,sourceAlias:u.sourceAlias,targetAlias:u.targetAlias})}),d.value=s},I=async()=>{let t=a.value.enablePush?1:0,e=a.value.semester;b.value.forEach(_=>{if(e===_.label)return e=_.value,!1});let s=a.value.targetTable;if(t&&(!e||!s))return n.errorTip("请选择学年学期和目标表"),!1;let u=s?s.join(","):"";const m=await oe({id:a.value.id,enable:t,semester:e,targetTable:u,type:p.value});n.ecode(m)?n.errorTip(m.msg):n.successTip("保存成功")},N=H(()=>{let t=[],e=a.value.targetTable?a.value.targetTable.slice(","):[];return e&&d.value&&d.value.forEach(s=>{e.forEach(u=>{s.label===u&&t.push(s)})}),t}),P=r([]),D=t=>{P.value=t},y=r(null),w=r(null),F=r(null),g=r({batchPush:{}}),M=async()=>{let t=a.value.semester;if(!t){n.errorTip("请选择学年学期");return}let e="";if(P.value.forEach(s=>e+=s.label+","),!e){n.errorTip("请选择目标表");return}g.value.batchPush={semester:t,targetTableIds:e},y.value.open()},z=async()=>{g.value.batchPush.type=p.value;const t=await te(g.value.batchPush);n.ecode(t)?n.errorTip(t.msg):w.value.open()},U=()=>{},j=()=>{};return J(()=>{B(),L()}),(t,e)=>{const s=ne,u=ue,m=ae,_=se,S=re,q=ie,G=le;return K(),O(Q,null,[l("div",pe,[o(_,{ref:"basicSettingRef",model:a.value,"label-width":"auto",class:"demo-basicSetting",size:C.value,"label-position":"left","status-icon":""},{default:i(()=>[de,l("div",me,[l("div",he,[v(" 是否开启国系统推送： "),o(s,{modelValue:a.value.enablePush,"onUpdate:modelValue":e[0]||(e[0]=c=>a.value.enablePush=c),"inline-prompt":"","active-text":"是","inactive-text":"否"},null,8,["modelValue"])])]),E(l("div",fe,[o(u,{label:"学年",prop:"semester"},{default:i(()=>[o(V,{modelValue:a.value.semester,"onUpdate:modelValue":e[1]||(e[1]=c=>a.value.semester=c),width:"350px",optionSource:b.value},null,8,["modelValue","optionSource"])]),_:1}),o(u,{label:"目标表",prop:"targetTable"},{default:i(()=>[o(V,{multiple:a.value.multipleState,modelValue:a.value.targetTable,"onUpdate:modelValue":e[2]||(e[2]=c=>a.value.targetTable=c),width:"350px",optionSource:d.value},null,8,["multiple","modelValue","optionSource"])]),_:1}),o(u,{style:{"margin-top":"60px"}},{default:i(()=>[o(m,{type:"primary",onClick:I},{default:i(()=>[v(" 保存设置 ")]),_:1})]),_:1})],512),[[x,a.value.enablePush]])]),_:1},8,["model","size"]),E(l("div",null,[l("div",_e,[ve,l("div",be,[o(m,{type:"primary",onClick:M},{default:i(()=>[v(" 批量推送 ")]),_:1})])]),o(G,{data:N.value,style:{width:"100%"},stripe:"",border:"",onSelectionChange:D},{empty:i(()=>[o(q,{description:"没有数据呀"})]),default:i(()=>[o(S,{type:"selection",width:"55"}),o(S,{label:"目标表",prop:"value",width:"auto",align:"center"}),o(S,{label:"操作",align:"center",width:"152",fixed:"right"},{default:i(({row:c})=>[ge]),_:1})]),_:1},8,["data"])],512),[[x,a.value.enablePush]])]),o(R,{ref_key:"pushResultPopupRef",ref:w,cancleState:!1,onSuccess:U},{default:i(()=>[Se]),_:1},512),o(R,{ref_key:"pushConfirmPopupRef",ref:y,cancleState:!0,onSuccess:z},{default:i(()=>[Te]),_:1},512),o(Y,{ref_key:"pushLogPopupRef",ref:F,RPS:T.value,ECS:A.value,onSuccess:j},null,8,["RPS","ECS"])],64)}}},xe=ce(Pe,[["__scopeId","data-v-98d6db6d"]]);export{xe as default};
