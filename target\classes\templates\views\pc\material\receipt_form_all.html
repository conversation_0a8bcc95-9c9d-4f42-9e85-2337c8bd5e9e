<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教材签收单</title>
    <link rel="stylesheet" th:href="@{/css/material/global.css}">
    <link rel="stylesheet" th:href="@{/plugin/layui/css/layui.css}">
    <link rel="stylesheet" th:href="@{/css/material/noClassSearch.css}">
    <script th:src="@{/plugin/layui/layui.js}"></script>
    <style>
        .main .top .title {
            position: relative;
            width: 100%;
        }

        .main .top .title .print {
            position: absolute;
            top: 0;
            right: 46px;
            line-height: 36px;
            font-size: 14px;
            color: rgba(49, 143, 197, 0.73);
            cursor: pointer;
        }

        .tableDetail .inform-bottom .notes {
            min-height: 135px;
        }
        .tableDetail .inform-bottom {
            margin-top: 34px;
            overflow: hidden;
        }


    </style>
    <style type="text/css" media=print>
        .noprint {
            display: none
        }

        .print-box {
            width: 100%;
            margin: 0 auto;
            padding: 0;
            transform-origin: center top;
        }

        .lab-box {
            overflow: hidden;
            margin: 0 auto;
        }
    </style>
</head>

<body>
<div class="main">
    <div class="top noprint">
        <div class="title">
            <div class="back">返回</div>
            <div class="print" id="print">打印</div>
        </div>

    </div>
    <div id="st-scroll">
    </div>
</div>
</body>
<script th:inline="javascript">
    const formData = [[${formData}]];
    let fid = "[[${fid}]]";
    let term = [[${term}]];
    let content = [[${content}]];
    const _VR_ = [[${_VR_}]] || '';
</script>
<script th:src="@{/js/jquery-3.3.1.min.js}"></script>
<script th:src="@{/js/my.util.js}"></script>
<script th:src="@{/js/material/receipt_form_all.js(v=${new java.util.Date().getTime()})}"></script>
<script th:src="@{/js/print.js}"></script>
<script th:src="@{/js/unitChange.js}"></script>
</html>