<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>开课信息</title>
    <link rel="stylesheet" th:href="@{../plugin/layui/css/layui.css}">
    <script th:src="@{../plugin/layui/layui.js}"></script>
</head>
<body>
<form class="layui-form" style="width: 500px;margin: 40px 40px;">
    <div class="layui-form-item">
        <label class="layui-form-label">单位id</label>
        <div class="layui-input-block">
            <input type="text" name="deptId" required lay-verify="required" placeholder="请输入fid" autocomplete="off"
                   class="layui-input">
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-input-block">
            <button class="layui-btn" lay-submit lay-filter="btn-complate">提交</button>
            <button type="reset" class="layui-btn layui-btn-primary">取消</button>
        </div>
    </div>
</form>
</body>
<script th:src="@{../js/jquery-3.3.1.min.js}"></script>
<script>
    document.domain = "chaoxing.com";
    layui.use(['form', 'layer'], function () {
        const form = layui.form;
        const layer = layui.layer;
        //监听提交
        form.on('submit(btn-complate)', function (data) {
            let deptId = data.field.deptId;
            $.post("../dict/addFidDataPush", {
                deptId: deptId,
                uid: "[[${uid}]]"
            }, function (result) {
                if (result.success) {
                    layer.msg(result.message, {icon: 2, time: 2000});
                    setTimeout(function () {
                        window.parent.postMessage(JSON.stringify({action: 1}), "*");
                    }, 2000);
                } else {
                    layer.msg(result.message, {icon: 2, time: 2000});
                }
            }, "json");
            return false;
        })
    })
    $(".layui-btn-primary").click(function () {
        window.parent.postMessage(JSON.stringify({action: 1}), "*");
    })
</script>
</html>