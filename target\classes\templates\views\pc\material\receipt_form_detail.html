<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教材签收单</title>
    <link rel="stylesheet" th:href="@{/css/material/global.css}">
    <link rel="stylesheet" th:href="@{/plugin/layui/css/layui.css}">
    <link rel="stylesheet" th:href="@{/css/material/noClassSearch.css}">
    <script th:src="@{/plugin/layui/layui.js}"></script>
    <style>
        .main .top .title {
            position: relative;
            width: 100%;
        }

        .main .top .title .print ,.main .top .title .export{
            position: absolute;
            top: 0;
            right: 46px;
            line-height: 36px;
            font-size: 14px;
            color: rgba(49, 143, 197, 0.73);
            cursor: pointer;
        }
		.main .top .title .export{
			margin-right:50px;
		}
        .tableDetail .inform-bottom .notes {
            min-height: 135px;
        }
        .tableDetail .inform-bottom {
            margin-top: 34px;
            overflow: hidden;
        }
    </style>
</head>

<body>
<div class="main">
    <div class="top">
        <div class="title">
            <div class="back">返回</div>
            <div class="levelone"></div>
            <div class="export">导出</div>
            <div class="print">打印</div>
        </div>
    </div>
    <div id="st-scroll">
        <div class="lab-box">
            <div class="tableDetail print-box" style="page-break-after: always;">
                <h1></h1>
                <div class="cons">
                    <div class="l-table">
                        <table>
                            <thead>
                            <tr>
                                <td width="16.67%">学籍号</td>
                                <td width="16.67%">班级</td>
                                <td width="16.67%">姓名</td>
                                <td width="16.67%">性别</td>
                                <td width="16.67%">专业</td>
                                <td width="16.67%">签收</td>
                            </tr>
                            </thead>
                            <tbody class="list">
                            </tbody>
                        </table>
                    </div>
                    <div class="r-table">
                        <table>
                            <thead>
                            <tr>
                                <td width="50%">书单</td>
                                <td width="50%">班级配置数量</td>
                            </tr>
                            </thead>
                            <tbody class="materialReplenishmentList">
                            </tbody>
                        </table>
                        <div class="grades-inform">
                            <ul class="exerciseBookReplenishmentList">
                                <li>
                                    <div class="name">班级</div>
                                    <div class="number classname"></div>
                                </li>
                                <li>
                                    <div class="name">人数</div>
                                    <div class="number classcount"></div>
                                </li>
                            </ul>
                        </div>
                        <div class="grades-inform" style="display: none;">
                            <ul>
                                <li>
                                    <div class="name">教室地址</div>
                                    <div class="number lxbjsdz"></div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="inform-bottom">
                    <div class="notes">
                        <p>注:</p>
                        <p class="content1"></p>

                    </div>
                    <div class="signature">
                        <p>班主任签字：</p>
                    </div>
                </div>

            </div>
        </div>
    </div>

</div>
</body>
<script th:inline="javascript">
    const formData = [[${formData}]];
    let fid = "[[${fid}]]";
    let content = [[${content}]];
    const _VR_ = [[${_VR_}]] || '';
</script>
<script th:src="@{/js/jquery-3.3.1.min.js}"></script>
<script th:src="@{/js/my.util.js}"></script>
<script th:src="@{/js/material/receipt_form_detail.js(v=${new java.util.Date().getTime()})}"></script>
<script th:src="@{/js/print.js}"></script>
<script th:src="@{/js/unitChange.js}"></script>
</html>