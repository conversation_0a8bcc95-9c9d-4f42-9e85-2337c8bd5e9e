<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>维护教学计划</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/global.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/plugin/layui/css/layui2.8.2.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/maintainTeachPlan.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/slideCommon.css'}"/>
    <script th:src="${_CPR_+_VR_+'/plugin/layui/layui2.8.2.js'}"></script>
    <style>
        .layui-table-view select[lay-ignore] {
            width: 130px;
        }
    </style>
</head>

<body>
<div class="container">
    <div class="top">
        <div class="title">
            <div class="back">返回</div>
            <div class="levelone">教学计划管理</div>
            <div class="icon"></div>
            <div class="leveltwo">维护教学计划</div>
        </div>
    </div>
    <div class="major-opt">
        <div class="major-mes">
            <span th:text="'开课学年：'+${info?.tjzyjxjh_xn}"></span>
            <span th:text="'开课学期：'+${info?.tjzyjxjh_xq}"></span>
            <span th:text="'系部/院系：'+${info?.tjzyjxjh_yx}"></span>
            <span th:text="'年级：'+${info?.tjzyjxjh_nj}"></span>
            <span th:text="'专业：'+${info?.tjzyjxjh_mc}"></span>
        </div>
        <div class="major-button" style="float: unset;">
            <ul class="course-statistics">
                <li>总门数：<span></span></li>
                <li>总分数：<span></span></li>
                <li>总学时：<span></span></li>
            </ul>
            <button id="addCourseBtn">添加课程</button>
            <button id="delCourseBtn">删除课程</button>
            <button class="releaseBtn" id="releaseBtn">发布教学计划</button>
        </div>
    </div>
    <div id="tabBox">
        <table class="layui-hide" id="courseTab" lay-filter="courseTab"></table>
    </div>
</div>
<!-- 添加课程 -->
<div id="addCourse" class="dialog">
    <div class="dialog-title">
        <h5>选择课程</h5>
        <div class="close-btn"></div>
    </div>
    <div class="dialog-con">
        <form action="" class="layui-form form-course">
            <div class="layui-inline-wrap">
                <div class="layui-inline">
                    <label class="layui-form-label">课程编号</label>
                    <div class="j-search-con single-box">
                        <input type="text" class="fuzzy-query-input" name="kck_kcbh" placeholder="请输入"
                               autocomplete="off">
                        <div class="j-select-search">
                            <ul class="kck_kcbh">
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">课程名称</label>
                    <div class="j-search-con single-box">
                        <input type="text" class="fuzzy-query-input" name="kck_kcmc" placeholder="请输入"
                               autocomplete="off">
                        <div class="j-select-search">
                            <ul class="kck_kcmc">
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">开课教研室</label>
                    <div class="j-search-con multiple-box">
                        <input type="text" name="kck_kkjys" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <ul class="kck_kkjys">
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">开课部门</label>
                    <div class="j-search-con multiple-box">
                        <input type="text" name="kck_kkyx" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <ul class="kck_kkyx">
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">课程性质</label>
                    <div class="j-search-con multiple-box">
                        <input type="text" name="kck_kcxz" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <ul class="kck_kcxz">
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">选必修</label>
                    <div class="j-search-con multiple-box">
                        <input type="text" name="kck_kcsx" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <ul>
                                <li value="选修">选修</li>
                                <li value="必修">必修</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-inline layui-inline-button">
                <button type="submit" class="layui-btn " lay-submit lay-filter="selTable">筛选</button>
                <button type="reset" class="layui-btn layui-btn-primary">清空</button>
            </div>
        </form>
        <div class="courseList">
            <table class="layui-hide" id="courseList" lay-filter="courseList"></table>
            <div class="selCourse" id="selCourse">
                <div class="z-check">
                    <span class="check" id="checkAllCourse"></span>选择全部数据
                </div>
                <span>共<i></i>条，已选<em>0</em>条</span>
            </div>
        </div>
    </div>
    <div class="dialog-footer">
        <button id="cancelBtn">取消</button>
        <button id="sureBtn">确定</button>
    </div>
</div>
<!-- 确认发布弹窗 -->
<div class="dialog" id="releaseDialog">
    <div class="dialog-title">
        <h5>确认发布教学计划</h5>
        <div class="close-btn"></div>
    </div>
    <div class="dialog-con">
        <img th:src="${_CPR_+_VR_+'/images/cultivation/tips-icon1.png'}" alt=""/>
        <p>发布后，设置的课程将同步至教学计划，<br/>请确认是否发布？</p>
    </div>
    <div class="dialog-footer">
        <button class="btn-cancel">取消</button>
        <button class="btn-sure">确定</button>
    </div>
</div>
<!-- 成功弹窗 -->
<div class="dialog" id="setTipSuccessDialog">
    <div class="dialog-con">
        <img th:src="${_CPR_+_VR_+'/images/form/icon-success.png'}" alt=""/>
        <h5>发布成功</h5>
        <p>计划课程已同步至该教学计划管理</p>
        <button id="tipSuccessBtn">确定</button>
    </div>
</div>
<!-- 时间时效校验 -->
<div class="dialog" id="timeDialog">
    <div class="dialog-title">
        <h5>提示</h5>
    </div>
    <div class="dialog-con">
        <img th:src="${_CPR_+_VR_+'/images/cultivation/tips-icon1.png'}" alt=""/>
        <p>当前不在教学计划维护时间内，请至参数设置维护时间</p>
    </div>
    <div class="dialog-footer">
        <button class="btn-sure">确定</button>
    </div>
</div>
</body>
<script type="text/html" id="toolBarDel">
    <div class="layui-clear-space">
        <a class="delBtn" lay-event="del">删除</a>
    </div>
</script>
<script type="text/html" id="courseNature">
    <select name="courseNature" class="layui-border select-demo-primary" lay-ignore>
        <option value="">请选择</option>
        {{# layui.each(window.globalDropdownOptions.courseNature, function(i, v){ }}
        <option value="{{= v }}" {{# if(v=== d.courseNature){ }}selected{{# } }}>{{= v }}</option>
        {{# }); }}
    </select>
</script>
<script type="text/html" id="courseType">
    <select name="courseCategory" class="layui-border select-demo-primary" lay-ignore>
        <option value="">请选择</option>
        {{# layui.each(window.globalDropdownOptions.courseCategory, function(i, v){ }}
        <option value="{{= v }}" {{# if(v=== d.courseCategory){ }}selected{{# } }}>{{= v }}</option>
        {{# }); }}
    </select>
</script>
<script type="text/html" id="examType">
    <select name="examForm" class="layui-border select-demo-primary" lay-ignore>
        <option value="">请选择</option>
        {{# layui.each(window.globalDropdownOptions.examForm, function(i, v){ }}
        <option value="{{= v }}" {{# if(v=== d.examForm){ }}selected{{# } }}>{{= v }}</option>
        {{# }); }}
    </select>
</script>
<script type="text/html" id="courseDepth">
    <select name="teachingDepartment" class="layui-border select-demo-primary" lay-ignore>
        <option value="">请选择</option>
        {{# layui.each(window.globalDropdownOptions.teachingDepartment, function(i, v){ }}
        <option value="{{= v }}" {{# if(v=== d.teachingDepartment){ }}selected{{# } }}>{{= v }}</option>
        {{# }); }}
    </select>
</script>
<script th:src="${_CPR_+_VR_+'/js/cultivation/common.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/cultivation/maintainTeachingPlan.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/cultivation/slideCommon.js'}"></script>
<script th:inline="javascript">
    const _VR_ = /*[[${_VR_}]]*/ '';
    let formUserId = /*[[${formUserId}]]*/ '';
    let info = /*[[${info}]]*/ '';
</script>
</html>
