<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合班</title>
    <link rel="stylesheet" th:href="@{../css/cultivation/global.css}">
    <link rel="stylesheet" th:href="@{../plugin/layui/css/layui.css}">
    <link rel="stylesheet" th:href="@{../css/cultivation/reset.css}">
    <link rel="stylesheet" th:href="@{../css/cultivation/index.css}">
    <style>
        .itemBtn {
            width: 118px;
            height: 40px;
            text-align: center;
            line-height: 40px;
            border-radius: 6px;
            font-size: 14px;
            color: #FFFFFF;
            background-color: #6A9CFF;
            margin-left: 30px;
            margin-top: 20px;
            cursor: pointer;
        }
    </style>
    <script th:src="@{../plugin/layui/layui.js}"></script>
</head>

<body>
<div class="main">
    <div class="m-top">
        <div class="title">合班</div>
    </div>

    <div class="item">

        <div class="i-con" style="align-items: center;">
            <div class="class-box" style="background: none;padding: 0">
                <div class="i-top" style="margin: 20px 0 10px 0;">
                    <span>可选行政班</span>
                </div>
                <div class="j-wrap" style="background: #F9FAFB;border-radius: 4px;padding: 20px;">
                    <div class="j-search clearfixs">
                        <div class="j-search-item">
                            <h5>年级</h5>
                            <div class="j-search-con">
                                <input type="text" placeholder="请选择年级" readonly="" class="schoolSel">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <ul class="nj">

                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="j-search-item">
                            <h5>学院</h5>
                            <div class="j-search-con">
                                <input type="text" placeholder="选择学院" readonly="" class="schoolSel">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <ul class="skyx">

                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="j-search-item">
                            <h5>班级</h5>
                            <div class="j-search-con">
                                <input type="text" placeholder="选择班级" readonly="" class="schoolSel">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <ul class="bjdm">

                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="button">搜索</div>
                    </div>
                    <div class="j-table" style="border:none;">
                        <table class="layui-table" id="classTable" lay-filter="classTable">
                        </table>
                    </div>
                </div>

            </div>

            <div class="mutate" style="height: auto;">
                <div class="up"></div>
                <div class="down"></div>
            </div>

            <div class="class-box" style="background: none;    padding: 0">
                <div class="i-top" style="margin: 20px 0 10px 0;">
                    <span>生成教学班</span>
                </div>
                <div class="j-wrap" style="background: #F9FAFB;border-radius: 4px;padding: 20px;">
                    <div class="j-search clearfixs">
                        <div class="j-search-item">
                            <h5>年级</h5>
                            <div class="j-search-con">
                                <input type="text" placeholder="请选择年级" readonly="" class="schoolSel">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <ul class="nj">

                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="j-search-item">
                            <h5>学院</h5>
                            <div class="j-search-con">
                                <input type="text" placeholder="选择学院" readonly="" class="schoolSel">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <ul class="skyx">

                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="j-search-item">
                            <h5>班级</h5>
                            <div class="j-search-con">
                                <input type="text" placeholder="选择班级" readonly="" class="schoolSel">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <ul class="bjdm">

                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="button">搜索</div>
                    </div>
                    <div class="j-table" style="border:none;">
                        <table class="layui-table" id="teachingClassForm" lay-filter="teachingClassForm">
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="itemBtn">提交</div>
</div>
</body>
<script th:src="@{../js/jquery-3.3.1.min.js}"></script>
<script th:src="@{../js/cultivation/common.js}"></script>
<script th:src="@{../js/cultivation/select_data.js}"></script>
<script type="text/html" id="tmplToolBar2">
    <div class="edit" lay-event="edit">编辑</div>
    <div class="delet" lay-event="delet">删除</div>
</script>
<script th:inline="javascript">
    let pageIndex = 1;
    let formUserId = [[${formUserId}]];
    let fid = [[${fid}]];
    let detailId = [[${classStartsInfoDetail.id}]]
    layui.use(['table', 'jquery', 'laypage'], function () {
        var table = layui.table;
        var $ = layui.jquery;
        table.render({
            elem: '#teachingClassForm',
            url:"../processData/getClassStartsInfoPageData",
            where: {formUserId:formUserId},
            cols: [
                [{
                    type: 'checkbox',
                    width: 70,
                    fixed: 'center',
                },{
                    field: 'id',
                    title: 'id',
                    hide:true
                }, {
                    field: 'year',
                    title: '年级',
                    align: 'left',
                    minWidth: 119
                }, {
                    field: 'dept',
                    title: '院系',
                    align: 'left',
                    minWidth: 196
                }, {
                    field: 'name',
                    title: '班级名称',
                    align: 'left',
                    minWidth: 120
                }, {
                    field: 'classNum',
                    title: '班级人数',
                    align: 'left',
                    minWidth: 122
                }, {
                    field: 'distributeNum',
                    title: '已安排人数',
                    align: 'left',
                    minWidth: 122
                }]
            ],
            limits: [7, 15, 30, 50, 100],
            limit: 10,
            page: false,
        });


        table.render({
            elem: '#classTable',
            url: '../processData/getFastClassInfoPageData',
            where: {formUserId:formUserId,fid:fid},
            cols: [
                [{
                    type: 'checkbox',
                    width: 70,
                    fixed: 'center',
                },{
                    field: 'id',
                    title: 'id',
                    hide:true
                }, {
                    field: 'year',
                    title: '年级',
                    align: 'center',
                    minWidth: 82
                }, {
                    field: 'dept',
                    title: '院系',
                    align: 'center',
                    minWidth: 242
                }, {
                    field: 'name',
                    title: '班级名称',
                    align: 'center',
                    minWidth: 125
                }, {
                    field: 'classNum',
                    title: '班级人数',
                    align: 'center',
                    minWidth: 100
                }, {
                    field: 'distributeNum',
                    title: '未安排人数',
                    align: 'center',
                    width: 100,
                    templet: distribute
                }
                ]
            ],
            limits: [7, 15, 30, 50, 100],
            limit: 10,
            page: false,
        });
    });

    $(".mutate .up").click(function (){
        let cData = layui.table.checkStatus('classTable').data;
        let tData = layui.table.cache["teachingClassForm"];
        tData = $.merge(tData,cData);
        layui.table.reload("teachingClassForm",{data:tData,url:''});
        let cTable = layui.table.cache["classTable"];
        if(cTable.length == cData.length){
            cTable = [];
        }
        if(cTable.length > 0){
            for (let i = 0; i < cTable.length; i++) {
                const d = cTable[i];
                for (let j = 0; j < cData.length; j++) {
                    const t = cData[j];
                    if(t && t.id === d.id){
                        cTable.splice(i,cData.length);
                    }
                }
            }
        }
        layui.table.reload("classTable",{data:cTable,url:''});
        for (let i = 0; i < cData.length; i++) {
            const c = cData[i];
            $("#teachingClassForm").next().find(".layui-table-main tbody tr").each(function (){
                const id = $(this).find("td").eq(1).find("div").text();
                if(c.id == id){
                    const distributeNum = c.distributeNum?c.classNum-c.distributeNum:c.classNum;
                    $(this).find("td").last().find("div").text(distributeNum);
                }
            })
        }
    })

    //合班
    let nameArray = [], codeArray = [];
    $(".itemBtn").click(function (){
        let tData = layui.table.cache["teachingClassForm"];
        if(tData.length === 0){
            layer.msg("请选择要合并的行政班", {icon: 2, time: 2000});
            return false;
        }
        layer.confirm('确定合并教学班吗？', function (index) {
            nameArray = [];
            codeArray = [];
            for (let i = 0; i < tData.length; i++) {
                const t = tData[i];
                nameArray.push(t.name);
                codeArray.push(t.code);
            }
            $.post("../processData/coShiftClass",
                {
                    formUserId: formUserId,
                    detailId:detailId,
                    tData:JSON.stringify(tData),
                    teachPlanNo:[[${form.kskk_jxjhbh}]]
                },
                function(result) {
                    layer.msg("合班成功", {icon: 1, time: 2000});
                    updFastClassStartsForm();
                },"json");

            $.post("../processData/addClassStarts",{
                id:detailId,
                classForm:nameArray.length>0?nameArray.join(","):"",
                classFormCode:codeArray.length>0?codeArray.join(","):""
            },function(result){
                addCourseInformation();
            },"json");
        })
    })

    function updFastClassStartsForm(){
        $.post("../processData/updFastClassStartsForm",{
            fid:[[${classStartsInfoDetail.fid}]],
            kskkJxjhbh:[[${form.kskk_jxjhbh}]],
            classNo:codeArray.length>0?codeArray.join(","):"",
            kskkWaprs:0,
            flag:"是"
        },function(result){
        },"json");
    }

    function addCourseInformation(){
        $.post("../processData/addCourseInformation",{detailId:detailId,fid:[[${classStartsInfoDetail.fid}]],type:1},function(result){
        },"json");
    }

    $(".mutate .down").click(function (){
        const tcData = layui.table.cache["teachingClassForm"];
        const tData = layui.table.checkStatus('teachingClassForm').data;
        let data = layui.table.cache["classTable"];
        for (let j = 0; j < tcData.length; j++) {
            const t = tcData[j];
            for (let i = 0; i < tData.length; i++) {
                const d = tData[i];
                if(t && t.id == d.id){
                    tcData.splice(j,tData.length);
                }
            }
        }
        $("#teachingClassForm").next().find(".layui-table-main tbody tr").each(function (){
            const id = $(this).find("td").eq(1).find("div").text();
            const tYear = $(this).find("td").eq(2).find("div").text();
            const tDept = $(this).find("td").eq(3).find("div").text();
            const tName = $(this).find("td").eq(4).find("div").text();
            const tClassNum = $(this).find("td").eq(5).find("div").text();
            let distributeNum = 0;
            for (let j = 0; j < tData.length; j++) {
                const t = tData[j];
                if(t && t.id == id){
                    for (let i = 0; i < data.length; i++) {
                        const d = data[i];
                        if(d && d.id == id){
                            data.splice(i,data.length);
                        }
                    }
                    data.push({id:id,year:tYear,dept:tDept,name:tName,classNum:tClassNum,distributeNum:distributeNum});
                }
            }
        })
        layui.table.reload("teachingClassForm",{data:tcData,url:''});
        layui.table.reload("classTable",{data:data,url:''});
    })

    function distribute(data){
        return data.distributeNum?data.classNum-data.distributeNum:data.classNum;
    }

    $(document).ready(function (){
        getData("nj","../processData/getGradeInfoData");
        getData("skyx","../processData/getCollegeInfoData");
        getData("bjdm","../processData/getClassInfoData");
    })
</script>

</html>