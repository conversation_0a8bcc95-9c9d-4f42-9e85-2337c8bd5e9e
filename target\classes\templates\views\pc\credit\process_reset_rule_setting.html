<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

    <head>
        <meta charset="UTF-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>重修规则设置</title>
        <link rel="stylesheet" th:href="@{~/css/credit/global1.css}">
        <link rel="stylesheet" th:href="@{~/plugin/layui/css/layui.css}">
        <link rel="stylesheet" th:href="@{~/css/credit/reset.css}">
        <link rel="stylesheet" th:href="@{~/css/credit/index.css}">
        <link rel="stylesheet" th:href="@{~/css/credit/jquery.toast.css}">
        <script th:src="@{~/plugin/layui/layui.js}"></script>

    </head>

    <body>
        <div class="j-material-wrap">
            <div class="j-material">
                <div class="j-title">
                    <h4>过程重修名单生成设置</h4>
                    <div class="j-btns">
                        <input id="retakeRuleSettingId" value="" type="hidden">
                        <button class="btn-complate">保存设置</button>
                    </div>
                </div>
                <div class="j-material-lable">
                    <!--                <h4>过程重修名单生成设置</h4>-->
                    <div class="set-list">
                        <div class="sl-lab">
                            <div class="name">重修学年学期</div>
                            <div class="select-input reset-year-semester">
                                <div class="name" id="resetYearSemester"></div>
                            </div>
                        </div>
                        <div class="sl-lab">
                            <div class="name">成绩学年学期</div>
                            <div class="select-input retake-year-semester-range">
                                <div class="name">请选择</div>
                                <em></em>
                                <div class="select-dropdown">
                                    <ul class="dropdown-list retake-year-semester-range">
                                        <li class="all"><span>全选</span></li>
                                        <!--                                    <li><span>2015-2016-1</span></li>-->
                                        <!--                                    <li><span>2015-2016-2</span></li>-->
                                        <!--                                    <li><span>2016-2017-1</span></li>-->
                                        <!--                                    <li><span>2016-2017-2</span></li>-->
                                        <!--                                    <li><span>2017-2018-1</span></li>-->
                                        <!--                                    <li><span>2017-2018-2</span></li>-->
                                        <!--                                    <li><span>2018-2019-1</span></li>-->
                                        <!--                                    <li><span>2018-2019-2</span></li>-->
                                        <!--                                    <li><span>2019-2020-1</span></li>-->
                                        <!--                                    <li><span>2019-2020-2</span></li>-->
                                        <!--                                    <li><span>2020-2021-1</span></li>-->
                                        <!--                                    <li><span>2020-2021-2</span></li>-->
                                        <!--                                    <li><span>2021-2022-1</span></li>-->
                                        <!--                                    <li><span>2021-2022-2</span></li>-->
                                        <!--                                    <li><span>2022-2023-1</span></li>-->
                                        <!--                                    <li><span>2022-2023-2</span></li>-->
                                        <!--                                    <li><span>2023-2024-1</span></li>-->
                                        <!--                                    <li><span>2023-2024-2</span></li>-->
                                        <!--                                    <li><span>2024-2025-1</span></li>-->
                                        <!--                                    <li><span>2024-2025-2</span></li>-->
                                    </ul>
                                    <div class="confirm">确定</div>
                                </div>
                            </div>
                        </div>
                        <div class="sl-lab">
                            <div class="name">参加重修年级</div>
                            <div class="select-input join-retake-grade">
                                <div class="name">请选择</div>
                                <em></em>
                                <div class="select-dropdown">
                                    <ul class="dropdown-list join-retake-grade">
                                        <li class="all"><span>全选</span></li>
                                        <!--                                    <li><span>2015</span></li>-->
                                        <!--                                    <li><span>2016</span></li>-->
                                        <!--                                    <li><span>2017</span></li>-->
                                        <!--                                    <li><span>2018</span></li>-->
                                        <!--                                    <li><span>2019</span></li>-->
                                        <!--                                    <li><span>2020</span></li>-->
                                        <!--                                    <li><span>2021</span></li>-->
                                        <!--                                    <li><span>2022</span></li>-->
                                        <!--                                    <li><span>2023</span></li>-->
                                        <!--                                    <li><span>2024</span></li>-->
                                        <!--                                    <li><span>2025</span></li>-->
                                    </ul>
                                    <div class="confirm">确定</div>
                                </div>
                            </div>
                        </div>
                        <div class="sl-lab">
                            <div class="name">学生范围</div>
                            <div class="mult-choice student-range">
                                <ul>
                                    <!--                                <li>在校生</li>-->
                                    <!--                                <li>不在校生</li>-->
                                </ul>
                            </div>
                        </div>
                        <div class="sl-lab">
                            <div class="name">学生当前状态</div>
                            <div class="mult-choice student-current-state">
                                <ul>
                                    <!--                                <li>在读</li>-->
                                    <!--                                <li>退学</li>-->
                                    <!--                                <li>休学</li>-->
                                    <!--                                <li>停业</li>-->
                                    <!--                                <li>复学</li>-->
                                    <!--                                <li>流失</li>-->
                                    <!--                                <li>毕业</li>-->
                                    <!--                                <li>结业</li>-->
                                    <!--                                <li>肄业</li>-->
                                    <!--                                <li>转学（转出）</li>-->
                                    <!--                                <li>保留入学资格</li>-->
                                    <!--                                <li>公派出国</li>-->
                                    <!--                                <li>开除</li>-->
                                    <!--                                <li>死亡</li>-->
                                    <!--                                <li>下落不明</li>-->
                                    <!--                                <li>其他</li>-->
                                </ul>
                            </div>
                        </div>
                        <!--                    <div class="sl-lab">-->
                        <!--                        <div class="name">课程属性</div>-->
                        <!--                        <div class="mult-choice course-nature">-->
                        <!--                            <ul>-->
                        <!--                                <li>公共必修课</li>-->
                        <!--                                <li>第二课堂</li>-->
                        <!--                                <li>专业核心课</li>-->
                        <!--                                <li>专业拓展课</li>-->
                        <!--                                <li>专业方向课</li>-->
                        <!--                                <li>学科基础课</li>-->
                        <!--                                <li>公共选修课</li>-->
                        <!--                                <li>军训</li>-->
                        <!--                                <li>实习实训</li>-->
                        <!--                                <li>顶岗实习</li>-->
                        <!--                                <li>毕业论文</li>-->
                        <!--                            </ul>-->
                        <!--                        </div>-->
                        <!--                    </div>-->
                        <div class="sl-lab">
                            <div class="name">过程学分范围</div>
                            <div class="mult-choice credit-range">
                                <ul>
                                    <li style="width:100%;">重修过程学分=0</li>
                                    <li style="width:100%;">正考过程学分=0且重修过程学分为空</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <!--                <h4>重修结果设置</h4>-->
                    <!--                <div class="j-switch">-->
                    <!--                   <div class="name">是否覆盖上次重修结果</div>-->
                    <!--                   <div class="switch">-->
                    <!--                       <span></span>-->
                    <!--                   </div>-->
                    <!--                   <div class="tit">是</div>-->
                    <!--                </div>-->
                </div>
            </div>
        </div>
    </body>
    <script th:src="@{~/js/jquery-3.3.1.min.js}"></script>
    <script th:src="@{~/js/jquery.toast.min.js}"></script>
    <script th:src="@{~/js/jquery.cookie.js}"></script>
    <script th:src="@{~/plugin/layui/layui.js}"></script>
    <script>

        // 初始化页面
        layui.use(['jquery', 'laydate', "form"], function () {
            // 初始化页面
            $(function () {
                initPage();
            });
        });
        // 重修结果设置开关点击事件
        $(".j-material").on("click", ".j-material-lable .j-switch .switch", function () {
            $(this).toggleClass("active");
            if ($(this).hasClass("active")) {
                $(".j-material .j-material-lable .j-switch .tit").text("是");
            } else {
                $(".j-material .j-material-lable .j-switch .tit").text("否");
            }
        });

        // 多选框点击选中或取消选中事件
        $(".j-material").on("click", " .j-material-lable .set-list .sl-lab .mult-choice ul li", function () {
            $(this).toggleClass("cur");
        });

        // 点击下拉框弹出或隐藏下拉框
        $(".j-material").on("click", ".j-material-lable .set-list .sl-lab .select-input .name", function (e) {
            $(this).parent().toggleClass("clicked");
            stopBubble(e);
        });

        // 重修学年学期范围和参加重修年级下拉框内容点击事件
        $(".j-material").on("click", ".j-material-lable .set-list .sl-lab .select-input .select-dropdown .dropdown-list li", function (e) {
            if ($(this).hasClass("all")) {
                $(this).toggleClass("cur");
                if ($(this).hasClass("cur")) {
                    $(this).parent().find("li").addClass("cur");
                } else {
                    $(this).parent().find("li").removeClass("cur");
                }
            } else {
                $(this).toggleClass("cur");
                var totallis = $(this).parent().find(".cur").length;
                var curlis = $(this).parent().find("li:not(.all)").length;
                if ($(this).parent().find(".all").hasClass("cur")) {
                    totallis--;
                }
                if (totallis == curlis) {
                    $(this).parent().find(".all").addClass("cur");
                } else {
                    $(this).parent().find(".all").removeClass("cur");
                }
            }
        });

        // 点击下拉框中的确定
        $(".j-material").on("click", ".j-material-lable .set-list .sl-lab .select-input .select-dropdown .confirm", function () {
            let hmjl = '';
            $(this).prev().find("li.cur:not('.all')").each(function () {
                hmjl += $(this).find("span").text() + ",";
            })
            hmjl = hmjl.slice(0, -1);
            $(this).parents(".select-input").find(".name").text(hmjl).addClass("ckd");
            $(this).parents(".select-input").removeClass("clicked");
        });

        // 点击下拉框中的列表项
        $(".j-material").on("click", ".j-material-lable .set-list .sl-lab .select-input .select-dropdown .dropdown-list li", function () {
            let hmjl = '';
            $(this).parents(".dropdown-list").find("li.cur:not('.all')").each(function () {
                hmjl += $(this).find("span").text() + ",";
            })
            hmjl = hmjl.slice(0, -1);
            $(this).parents(".select-input").find(".name").text(hmjl).addClass("ckd");
        });

        // 停止冒泡
        function stopBubble(e) {
            if (e && e.stopPropagation) {
                e.stopPropagation();
            } else {
                window.event.cancelBubble = true;
            }
        }

        // 点击文档空白处，下拉框隐藏
        $(document).on("click", function (event) {
            var _con = $('.select-input');
            if (!_con.is(event.target) && _con.has(event.target).length === 0) {
                $(".select-input").removeClass("clicked");
            }
        });

        // 点击保存设置按钮
        $(".j-material").on("click", ".j-title .j-btns .btn-complate", function () {
            var targetObj = {};
            // 封装重修学年学期范围数据
            var retakeYearSemesterRangeLis = $(".j-material .j-material-lable .set-list .sl-lab .select-input .select-dropdown .retake-year-semester-range li.cur").not(".all");
            var retakeYearSemesterRange = "";
            if (retakeYearSemesterRangeLis && retakeYearSemesterRangeLis.length > 0) {
                for (var i = 0; i < retakeYearSemesterRangeLis.length; i++) {
                    var retakeYearSemesterRangeLi = retakeYearSemesterRangeLis[i];
                    retakeYearSemesterRange += $(retakeYearSemesterRangeLi).find("span").text();
                    if (i != retakeYearSemesterRangeLis.length - 1) {
                        retakeYearSemesterRange += ",";
                    }
                }
            }
            targetObj.retakeYearSemesterRange = retakeYearSemesterRange;
            // 封装参加重修年级
            var joinRetakeGradeLis = $(".j-material .j-material-lable .set-list .sl-lab .select-input .select-dropdown .join-retake-grade li.cur").not(".all");
            var joinRetakeGrade = "";
            if (joinRetakeGradeLis && joinRetakeGradeLis.length > 0) {
                for (var i = 0; i < joinRetakeGradeLis.length; i++) {
                    var joinRetakeGradeLi = joinRetakeGradeLis[i];
                    joinRetakeGrade += $(joinRetakeGradeLi).find("span").text();
                    if (i != joinRetakeGradeLis.length - 1) {
                        joinRetakeGrade += ",";
                    }
                }
            }
            targetObj.joinRetakeGrade = joinRetakeGrade;
            // 封装学生范围
            var studentRangeLis = $(".j-material .j-material-lable .set-list .sl-lab .student-range li.cur");
            var studentRange = "";
            if (studentRangeLis && studentRangeLis.length > 0) {
                for (var i = 0; i < studentRangeLis.length; i++) {
                    var studentRangeLi = studentRangeLis[i];
                    studentRange += $(studentRangeLi).text();
                    if (i != studentRangeLis.length - 1) {
                        studentRange += ",";
                    }
                }
            }
            targetObj.studentRange = studentRange;
            // 封装学生当前状态
            var studentCurrentStateLis = $(".j-material .j-material-lable .set-list .sl-lab .student-current-state li.cur");
            var studentCurrentState = "";
            if (studentCurrentStateLis && studentCurrentStateLis.length > 0) {
                for (var i = 0; i < studentCurrentStateLis.length; i++) {
                    var studentCurrentStateLi = studentCurrentStateLis[i];
                    studentCurrentState += $(studentCurrentStateLi).text();
                    if (i != studentCurrentStateLis.length - 1) {
                        studentCurrentState += ",";
                    }
                }
            }
            targetObj.studentCurrentState = studentCurrentState;
            // 封装课程性质
            // var courseNatureLis = $(".j-material .j-material-lable .set-list .sl-lab .course-nature li.cur");
            var courseNature = "";
            // if (courseNatureLis && courseNatureLis.length > 0) {
            //     for (var i = 0; i < courseNatureLis.length; i++) {
            //         var courseNatureLi = courseNatureLis[i];
            //         courseNature += $(courseNatureLi).text();
            //         if (i != courseNatureLis.length - 1) {
            //             courseNature += ",";
            //         }
            //     }
            // }
            targetObj.courseNature = courseNature;
            // 封装学分范围
            var creditRangeLis = $(".j-material .j-material-lable .set-list .sl-lab .credit-range li.cur");
            var creditRange = "";
            if (creditRangeLis && creditRangeLis.length > 0) {
                for (var i = 0; i < creditRangeLis.length; i++) {
                    var creditRangeLi = creditRangeLis[i];
                    creditRange += $(creditRangeLi).text();
                    if (i != creditRangeLis.length - 1) {
                        creditRange += ",";
                    }
                }
            }
            targetObj.creditRange = creditRange;
            // 封装重修结果设置：1覆盖，2不覆盖
            // var coverLastRetakeResult = 2;
            // var coverLastRetakeResultDiv = $(".j-material .j-material-lable .j-switch .switch");
            // if ($(coverLastRetakeResultDiv).hasClass("active")) {
            //     coverLastRetakeResult = 1;
            // }
            // targetObj.coverLastRetakeResult = coverLastRetakeResult;
            if ($("#retakeRuleSettingId").val()) {
                targetObj.id = $("#retakeRuleSettingId").val();
            }
            if (!targetObj.retakeYearSemesterRange) {
                layer.msg("请选择重修学年学期", {icon: 2, time: 2000});
                return;
            } else if (!targetObj.joinRetakeGrade) {
                layer.msg("请选择成绩学年学期", {icon: 2, time: 2000});
                return;
            }  else if (!targetObj.joinRetakeGrade) {
                layer.msg("请选择参加重修年级", {icon: 2, time: 2000});
                return;
            } else if (!targetObj.studentRange) {
                layer.msg("请选择学生范围", {icon: 2, time: 2000});
                return;
            } else if (!targetObj.studentCurrentState) {
                layer.msg("请选择学生当前状态", {icon: 2, time: 2000});
                return;
            } else if (!targetObj.creditRange) {
                layer.msg("请选择过程学分范围", {icon: 2, time: 2000});
                return;
            }
            $.ajax({
                type: "POST",
                url: "/new/credit/process/reset/insertOrUpdate",
                data: targetObj,
                dataType: 'json',
                success: function (result) {
                    if (result) {
                        if (result.code === 200) {
                            layer.msg(result.msg, {icon: 1, time: 2000});
                            initPage();
                        } else {
                            layer.msg(result.msg, {icon: 2, time: 2000});
                        }
                    }
                }
            });
        });

        // 回显下拉框和多选框数据
        function echoBoxData(boxData) {
            if (!boxData) {
                return;
            }
            // 动态渲染成绩学年学期范围下拉框
            var retakeYearSemesterRangeData = boxData.retakeYearSemesterRangeData;
            if (retakeYearSemesterRangeData && retakeYearSemesterRangeData.length > 0) {
                // 动态填充重修学年学期下拉框：默认为当前学年学期，成绩学年学期下拉框数组的第一个数据就是当前学年学期
                $("#resetYearSemester").text(retakeYearSemesterRangeData[0] ? retakeYearSemesterRangeData[0] : "");
                $(".j-material .j-material-lable .set-list .sl-lab .select-input .select-dropdown .retake-year-semester-range").text("");
                $(".j-material .j-material-lable .set-list .sl-lab .select-input .select-dropdown .retake-year-semester-range").append("<li class='all'><span>全选</span></li>");
                for (var i = 0; i < retakeYearSemesterRangeData.length; i++) {
                    $(".j-material .j-material-lable .set-list .sl-lab .select-input .select-dropdown .retake-year-semester-range")
                        .append("<li><span>" + retakeYearSemesterRangeData[i] + "</span></li>");
                }
            }
            // 动态渲染参加重修年级下拉框
            var joinRetakeGradeData = boxData.joinRetakeGradeData;
            if (joinRetakeGradeData && joinRetakeGradeData.length > 0) {
                $(".j-material .j-material-lable .set-list .sl-lab .select-input .select-dropdown .join-retake-grade").text("");
                $(".j-material .j-material-lable .set-list .sl-lab .select-input .select-dropdown .join-retake-grade").append("<li class='all'><span>全选</span></li>");
                for (var i = 0; i < joinRetakeGradeData.length; i++) {
                    $(".j-material .j-material-lable .set-list .sl-lab .select-input .select-dropdown .join-retake-grade")
                        .append("<li><span>" + joinRetakeGradeData[i] + "</span></li>");
                }
            }
            // 动态渲染学生范围复选框
            var studentRangeData = boxData.studentRangeData;
            if (studentRangeData && studentRangeData.length > 0) {
                $(".j-material .j-material-lable .set-list .sl-lab .student-range ul").text("");
                for (var i = 0; i < studentRangeData.length; i++) {
                    $(".j-material .j-material-lable .set-list .sl-lab .student-range ul")
                        .append("<li>" + studentRangeData[i] + "</li>");
                }
            }
            // 动态渲染学生当前状态复选框
            var studentCurrentStateData = boxData.studentCurrentStateData;
            if (studentCurrentStateData && studentCurrentStateData.length > 0) {
                $(".j-material .j-material-lable .set-list .sl-lab .student-current-state ul").text("");
                for (var i = 0; i < studentCurrentStateData.length; i++) {
                    $(".j-material .j-material-lable .set-list .sl-lab .student-current-state ul")
                        .append("<li>" + studentCurrentStateData[i] + "</li>");
                }
            }
            // 动态渲染课程性质复选框
            // var courseNatureData = boxData.courseNatureData;
            // if (courseNatureData && courseNatureData.length > 0) {
            //     for (var i = 0; i < courseNatureData.length; i++) {
            //         $(".j-material .j-material-lable .set-list .sl-lab .course-nature ul")
            //             .append("<li>" + courseNatureData[i] + "</li>");
            //     }
            // }
        }

        // 回显已经设置的数据
        function echoData(single) {
            if (!single) {
                // 学生范围：默认勾选"在校"
                var studentRangeLis = $(".j-material .j-material-lable .set-list .sl-lab .student-range li");
                for (var i = 0; i < studentRangeLis.length; i++) {
                    var studentRangeLi = studentRangeLis[i];
                    if ($(studentRangeLi).text() === "在校") {
                        $(studentRangeLi).addClass("cur");
                        break;
                    }
                }
                // 学生当前状态：默认勾选"在读"
                var studentCurrentStateLis = $(".j-material .j-material-lable .set-list .sl-lab .student-current-state li");
                for (var i = 0; i < studentCurrentStateLis.length; i++) {
                    var studentCurrentStateLi = studentCurrentStateLis[i];
                    if ($(studentCurrentStateLi).text() === "在读") {
                        $(studentCurrentStateLi).addClass("cur");
                        break;
                    }
                }
                return;
            }
            $("#retakeRuleSettingId").val(single.id);
            // 回显重修学年学期范围下拉框
            var retakeYearSemesterRange = single.retakeYearSemesterRange;
            if (retakeYearSemesterRange) {
                var retakeYearSemesterRanges = retakeYearSemesterRange.split(",");
                var retakeYearSemesterRangeLis = $(".j-material .j-material-lable .set-list .sl-lab .select-input .select-dropdown .retake-year-semester-range li").not(".all");
                for (var i = 0; i < retakeYearSemesterRangeLis.length; i++) {
                    var retakeYearSemesterRangeLi = retakeYearSemesterRangeLis[i];
                    if (retakeYearSemesterRanges.indexOf($(retakeYearSemesterRangeLi).find("span").text()) != -1) {
                        $(retakeYearSemesterRangeLi).addClass("cur");
                    }
                }
                if (retakeYearSemesterRanges.length == retakeYearSemesterRangeLis.length) {
                    $(".j-material .j-material-lable .set-list .sl-lab .select-input .select-dropdown .retake-year-semester-range li.all").addClass("cur");
                }
                $(".j-material .j-material-lable .set-list .sl-lab .select-input.retake-year-semester-range").find(".name").text(retakeYearSemesterRange).addClass("ckd");
            }
            // 回显参加重修年级下拉框
            var joinRetakeGrade = single.joinRetakeGrade;
            if (joinRetakeGrade) {
                var joinRetakeGrades = joinRetakeGrade.split(",");
                var joinRetakeGradeLis = $(".j-material .j-material-lable .set-list .sl-lab .select-input .select-dropdown .join-retake-grade li").not(".all");
                for (var i = 0; i < joinRetakeGradeLis.length; i++) {
                    var joinRetakeGradeLi = joinRetakeGradeLis[i];
                    if (joinRetakeGrades.indexOf($(joinRetakeGradeLi).find("span").text()) != -1) {
                        $(joinRetakeGradeLi).addClass("cur");
                    }
                }
                if (joinRetakeGrades.length == joinRetakeGradeLis.length) {
                    $(".j-material .j-material-lable .set-list .sl-lab .select-input .select-dropdown .join-retake-grade li.all").addClass("cur");
                }
                $(".j-material .j-material-lable .set-list .sl-lab .select-input.join-retake-grade").find(".name").text(joinRetakeGrade).addClass("ckd");
            }
            // 回显学生范围复选框
            var studentRange = single.studentRange;
            if (studentRange) {
                var studentRanges = studentRange.split(",");
                var studentRangeLis = $(".j-material .j-material-lable .set-list .sl-lab .student-range li");
                for (var i = 0; i < studentRangeLis.length; i++) {
                    var studentRangeLi = studentRangeLis[i];
                    if (studentRanges.indexOf($(studentRangeLi).text()) != -1) {
                        $(studentRangeLi).addClass("cur");
                    }
                }
            }
            // 回显学生当前状态
            var studentCurrentState = single.studentCurrentState;
            if (studentCurrentState) {
                var studentCurrentStates = studentCurrentState.split(",");
                var studentCurrentStateLis = $(".j-material .j-material-lable .set-list .sl-lab .student-current-state li");
                for (var i = 0; i < studentCurrentStateLis.length; i++) {
                    var studentCurrentStateLi = studentCurrentStateLis[i];
                    if (studentCurrentStates.indexOf($(studentCurrentStateLi).text()) != -1) {
                        $(studentCurrentStateLi).addClass("cur");
                    }
                }
            }
            // 回显课程性质
            // var courseNature = single.courseNature;
            // if (courseNature) {
            //     var courseNatures = courseNature.split(",");
            //     var courseNatureLis = $(".j-material .j-material-lable .set-list .sl-lab .course-nature li");
            //     for (var i = 0; i < courseNatureLis.length; i++) {
            //         var courseNatureLi = courseNatureLis[i];
            //         if (courseNatures.indexOf($(courseNatureLi).text()) != -1) {
            //             $(courseNatureLi).addClass("cur");
            //         }
            //     }
            // }
            // 回显学分范围
            var creditRange = single.creditRange;
            if (creditRange) {
                var creditRanges = creditRange.split(",");
                var creditRangeLis = $(".j-material .j-material-lable .set-list .sl-lab .credit-range li");
                for (var i = 0; i < creditRangeLis.length; i++) {
                    var creditRangeLi = creditRangeLis[i];
                    if (creditRanges.indexOf($(creditRangeLi).text()) != -1) {
                        $(creditRangeLi).addClass("cur");
                    }
                }
            }
            // 回显是否覆盖上次重修结果
            // var coverLastRetakeResult = single.coverLastRetakeResult;
            // // 如果为1，则是不覆盖
            // if (coverLastRetakeResult == 1) {
            //     $(".j-material .j-material-lable .j-switch .switch").addClass("active");
            // }
        }

        /**
         * 初始化页面，请求回显数据
         */
        function initPage() {
            // 先渲染页面的静态下拉框和多选框数据
            $.ajax({
                type: "get",
                url: "/new/credit/process/reset/select/check/box",
                data: {},
                dataType: 'json',
                async: false,
                success: function (result) {
                    if (result) {
                        if (result.code === 200) {
                            echoBoxData(result.data);
                        } else {
                            layer.msg(result.msg, {icon: 2, time: 2000});
                        }
                    }
                }
            });
            // 再渲染之前保存的设置
            $.ajax({
                type: "get",
                url: "/new/credit/process/reset/single",
                data: {},
                dataType: 'json',
                success: function (result) {
                    if (result) {
                        if (result.code === 200) {
                            echoData(result.data);
                        } else {
                            layer.msg(result.msg, {icon: 2, time: 2000});
                        }
                    }
                }
            });
        }

        /**
         * 提示框
         * @param title 标题
         * @param text 内容
         * @param icon 图标：success,info,warning,error
         * @param hideAfter 显示时长
         */
        function showMsg(title, text, icon, hideAfter) {
            if (title == undefined) {
                var title = "提示";
            }
            $.toast({
                text: text,//消息提示框的内容。
                heading: title,//消息提示框的标题。
                icon: icon,//消息提示框的图标样式。
                showHideTransition: 'fade',//消息提示框的动画效果。可取值：plain，fade，slide。
                allowToastClose: true,//是否显示关闭按钮。(true 显示，false 不显示)
                hideAfter: hideAfter,//设置为false则消息提示框不自动关闭.设置为一个数值则在指定的毫秒之后自动关闭消息提框
                stack: 1,//消息栈。同时允许的提示框数量
                position: 'mid-center',//消息提示框的位置：bottom-left, bottom-right,bottom-center,top-left,top-right,top-center,mid-center。
                textAlign: 'left',//文本对齐：left, right, center。
                loader: true,//是否显示加载条
                //bgColor: '#FF1356',//背景颜色。
                //textColor: '#eee',//文字颜色。
                loaderBg: '#ffffff',//加载条的背景颜色。
                beforeShow: function () {
                },
                afterShown: function () {
                },
                beforeHide: function () {
                },
                afterHidden: function () {
                }
                /*toast事件
                beforeShow 会在toast即将出现之前触发
                afterShown 会在toast出现后触发
                beforeHide 会在toast藏起来之前触发
                afterHidden 会在toast藏起来后被触发
                */
            });
        }
    </script>

</html>