<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>同步学生教材</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/material/global.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/plugin/layui/css/layui.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/material/sync_student_material_pop.css'}"/>
    <script th:src="${_CPR_+_VR_+'/plugin/layui/layui.js'}"></script>
</head>

<body>
<div id="scheduleExport" class="schedule-export popups">
    <div class="popup-con">
        <div class="export-cons">
            <div class="sel-row">
                <div class="name">学年学期</div>
                <div class="sel-item">
                    <div class="sel disabled">
                        <div class="select-input">
                            <div class="name ckd" th:text="${semester}"></div>
                            <em></em>
                        </div>
                    </div>
                    <div class="tips">仅支持当前使用学期同步</div>
                </div>
            </div>
            <div class="sel-row">
                <div class="name">选择年级</div>
                <div class="sel-item">
                    <div class="sel select-grade" style="width: 360px;">
                        <div class="select-input">
                            <div class="name">请选择</div>
                            <em></em>
                            <div class="select-dropdown" style="width: 360px;">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <div class="all-selects">
                                    全选
                                </div>
                                <ul class="dropdown-lists">
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="bottom">
        <div class="cancle">取消</div>
        <div class="confirm">确定</div>
    </div>
</div>

</body>
<script th:src="${_CPR_+_VR_+'/js/jquery-1.11.3.min.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/material/sync_student_material.js'}"></script>
<script th:inline="javascript">
    const _VR_ = [[${_VR_}]];
    $(function () {
        let form, table, laydate, layer;
        layui.use(["form", "table", "laydate"], function () {
            const $ = layui.jquery;
            form = layui.form;
            table = layui.table;
            laydate = layui.laydate;
            layer = layui.layer;
            $("#scheduleExport .bottom div.confirm").click(function () {
                let k1 = $("#scheduleExport .select-grade .name").hasClass("ckd");
                if (!k1) {
                    layer.msg('请选择年级', {icon: 2, time: 3000});
                    return false;
                }
                syncStudentMaterial();
            })
        });
        getMaterialGradeData();

        function getMaterialGradeData() {
            $.post(_VR_ + "/material/pop/getMaterialGradeData", {
                fid: [[${formTopBtnBO.fid}]]
            }, function (result) {
                if (result.data && result.data.length > 0) {
                    let html = '';
                    for (let i = 0; i < result.data.length; i++) {
                        html += "<li><span>" + result.data[i] + "</span></li>";
                    }
                    $(".dropdown-lists").html(html);
                }
            });
        }

        function syncStudentMaterial() {
            $.post(_VR_ + "/api/form/material/topBtn/syncStudentMaterial", {
                fid: [[${formTopBtnBO.fid}]],
                uid: [[${formTopBtnBO.uid}]],
                semester: [[${semester}]],
                formId: [[${formTopBtnBO.formId}]],
                grade: $(".select-grade .ckd").text()
            }, function (result) {
                layer.msg(result.message, {icon: 1, time: 3000});
                setTimeout(closePop, 3000);
            });
        }

        $(".cancle").click(function () {
            closePop();
        })

        function closePop() {
            window.parent.postMessage(JSON.stringify({action: 1}), "*");
        }
    });
</script>
<script>


</script>

</html>