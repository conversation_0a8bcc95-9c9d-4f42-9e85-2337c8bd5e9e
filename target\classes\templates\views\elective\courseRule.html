<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>选课规则</title>
    <th:block th:include="common :: header('选课规则')"/>
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/global.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/layui/css/layui.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/reset.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/selCourseSet.css'">
    <style>
        .opt_relevance ul{
            overflow: hidden;
            padding: 0 30px;
        }
        .opt_relevance ul li{
            float: left;
            overflow: hidden;
            height: 20px;
            margin: 16px 40px 0 0;
            font-size: 14px;
            color: #484f5d;
            line-height: 20px;
            word-break: break-all;
        }
        .opt_relevance ul li span{
            float: left;
        }
        .opt_relevance ul li i{
            float: left;
        }
    </style>
</head>
<body>
<div class="z-main">
    <div class="z-title">
        <h3>选课规则</h3>
        <!-- <div id="saveBth"> 保存设置</div> -->
    </div>
    <div class="opt_relevance">
        <ul>
            <li>
                <span>计划名称：</span><i th:text="${task.xkjhbJhmc}"></i>
            </li>
            <li>
                <span>所属学期：</span><i th:text="${task.xkjhbSsxq}"></i>
            </li>
        </ul>
    </div>
    <div class="z-tab">
        <ul>
            <li class="active">学分门数要求</li>
            <li>其他规则</li>
        </ul>
    </div>
    <div class="z-box">
        <!-- 学分限制 -->
        <div class="box-con box-common" style="display: block;">
            <div class="z-search">
                <form action="" class="layui-form" onsubmit="return false">
                    <div class="layui-form-item">
                        <label class="layui-form-label">所在校区 </label>
                        <div class="layui-input-block">
                            <div class="j-search-con multiple-box">
                                <input type="text" name="teacherName" placeholder="请选择" readonly=""
                                       class="schoolSel" id="ruleXqIp2">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <ul name="teacherName" id="ruleXqDiv">
                                        <li data-id="0" class="" th:each="data:${addXq}" th:text="${data}"></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">所属年级</label>
                        <div class="layui-input-block">
                            <div class="j-search-con multiple-box">
                                <input type="text" name="teacherName" placeholder="请选择" readonly=""
                                       class="schoolSel" id="ruleNjIp2">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <ul name="teacherName" id="ruleNjDiv">
                                        <li data-id="0" class="" th:each="data:${addNj}" th:text="${data}"></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">所属院系 </label>
                        <div class="layui-input-block">
                            <div class="j-search-con multiple-box">
                                <input type="text" name="teacherName" placeholder="请选择" readonly=""
                                       class="schoolSel" id="ruleYxIp2">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <ul name="teacherName" id="ruleYxDiv">
                                        <li data-id="0" class="" th:each="data:${addYx}" th:text="${data}"></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">所属专业 </label>
                        <div class="layui-input-block">
                            <div class="j-search-con multiple-box">
                                <input type="text" name="teacherName" placeholder="请选择" readonly=""
                                       class="schoolSel" id="ruleZyIp2">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <ul name="teacherName" id="ruleZyDiv">
                                        <li data-id="0" class="" th:each="data:${addZy}" th:text="${data}"></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"> 所在班级 </label>
                        <div class="layui-input-block">
                            <div class="j-search-con multiple-box">
                                <input type="text" name="teacherName" placeholder="请选择" readonly=""
                                       class="schoolSel" id="ruleBjIp2">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <ul name="teacherName" id="ruleBjDiv">
                                        <li data-id="0" class="" th:each="data:${addBj}" th:text="${data}"></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <button class="z-btn">查询</button>
                    <div class="clear"></div>
                </form>
            </div>
            <div class="z-btns" style="margin-bottom: 18px;">
                <div class="addRule" id="addScoreLimit"><img th:src="${_CPR_}+'/elective/images/add-icon.png'">添加学分门数要求
                </div>
                <div class="del">删除</div>
            </div>
            <div class="z-table">
                <table class="layui-hide materialTable4" id="materialTable4" lay-filter="materialTable4">
                </table>
            </div>
        </div>
        <!-- 其他限制 -->
        <div class="box-con box-common box-limit" style="display: none;">
            <div class="item">
                <h3>是否限制学生不可重复选课</h3>
                <div class="tips layui-tips" data-tip="允许学生重复选择同一门课程，如三年内不可重复选课，则学期数输入6"></div>
                <div class="switch switchCourse" id="limitAgainDiv"><span></span></div>
                <span class="witchState" id="limitAgainSpan">否</span>
            </div>
            <div class="item itemAllow" style="display: none;">
                <h3 style="width: 216px;">请输入限制重复选课的学期数</h3>
                <input type="text" placeholder="三年内则输入6" id="limitAgainIp" onkeyup="exitAgain(this.value);">
<!--                <div class="tip-mes1">个学期内重复选课</div>-->
            </div>
        </div>
    </div>
</div>
<!-- 学分门数要求 -->
<div id="selScoreGates" class="dialog">
    <div class="dialog-title">
        <h3>学分门数要求</h3><span class="pu-close"></span>
    </div>
    <div class="dialog-con">
        <div class="tips">限制学生选择选修课的学分或门数</div>
        <form action="" class="layui-form">
            <div class="layui-form-item">
                <label class="layui-form-label">名称</label>
                <div class="layui-input-block" style="width: 351px;">
                    <input type="text" name="relationName" lay-verify="required" placeholder="请输入"
                           autocomplete="off" class="layui-input" id="ruleMcIp">
                </div>
            </div>
            <div class="title">要求学生范围</div>
            <div class="form-item">
                <div class="layui-form-item">
                    <label class="layui-form-label">所在校区 </label>
                    <div class="layui-input-block">
                        <div class="j-search-con multiple-box">
                            <input type="text" name="teacherName" value="全部" placeholder="请选择" readonly=""
                                   class="schoolSel" id="ruleXqIp">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul name="teacherName" id="ruleXqDiv3">
                                    <li data-id="" class="all">全部</li>
                                    <li data-id="0" class="" th:each="data:${addXq}" th:text="${data}"></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">所属年级 </label>
                    <div class="layui-input-block">
                        <div class="j-search-con multiple-box">
                            <input type="text" name="teacherName" value="全部" placeholder="请选择" readonly=""
                                   class="schoolSel" id="ruleNjIp">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul name="teacherName" id="ruleNjDiv3">
                                    <li data-id="" class="all">全部</li>
                                    <li data-id="0" class="" th:each="data:${addNj}" th:text="${data}"></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">所属院系 </label>
                    <div class="layui-input-block">
                        <div class="j-search-con multiple-box">
                            <input type="text" name="teacherName" value="全部" placeholder="请选择" readonly=""
                                   class="schoolSel" id="ruleYxIp">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul name="teacherName" id="ruleYxDiv3">
                                    <li data-id="" class="all">全部</li>
                                    <li data-id="0" class="" th:each="data:${addYx}" th:text="${data}"></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">所属专业 </label>
                    <div class="layui-input-block">
                        <div class="j-search-con multiple-box">
                            <input type="text" name="teacherName" value="全部" placeholder="请选择" readonly=""
                                   class="schoolSel" id="ruleZyIp">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul name="teacherName" id="ruleZyDiv3">
                                    <li data-id="" class="all">全部</li>
                                    <li data-id="0" class="" th:each="data:${addZy}" th:text="${data}"></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">所在班级 </label>
                    <div class="layui-input-block">
                        <div class="j-search-con multiple-box">
                            <input type="text" name="teacherName" value="全部" placeholder="请选择" readonly=""
                                   class="schoolSel" id="ruleBjIp">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul name="teacherName" id="ruleBjDiv3">
                                    <li data-id="" class="all">全部</li>
                                    <li data-id="0" class="" th:each="data:${addBj}" th:text="${data}"></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="title" style="padding-top: 20px;">学分门数要求</div>
            <div class="ask-score">
                <div class="ask-item">
                    <div class="j-search-con single-box" style="width: 100px;">
                        <input type="text" name="teacherName" placeholder="请选择" readonly="" class="schoolSel"
                               id="ruleIp1">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <ul name="teacherName">
                                <li data-id="0">学分</li>
                                <li data-id="1">门数</li>
                            </ul>
                        </div>
                    </div>
                    <span class="range-text">要求范围</span>
                    <input type="number" placeholder="最少" min="0" id="rulemin1">
                    <img th:src="${_CPR_}+'/elective/images/range.png'" alt="">
                    <input type="number" placeholder="最多" min="0" id="rulemax1">
                    <span class="add-range"></span>
                </div>
                <div class="condition" style="display: none;" id="relationDiv">
                    <div class="j-search-con single-box" style="width: 60px;margin:34px 0 0 10px;">
                        <input type="text" name="teacherName" value="且" placeholder="请选择" readonly=""
                               class="schoolSel" id="ruleIp2">
                        <span class="j-arrow"></span>
                        <div class="j-select-year" style="left: 67px;top: -26px;">
                            <ul name="teacherName">
                                <li data-id="0" class="active">且</li>
                                <li data-id="1">或</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="ask-item" style="display: none;">
                    <div class="j-search-con single-box" style="width: 100px;">
                        <input type="text" name="teacherName" placeholder="请选择" readonly="" class="schoolSel"
                               id="ruleIp3">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <ul name="teacherName">
                                <li data-id="0">学分</li>
                                <li data-id="1">门数</li>
                            </ul>
                        </div>
                    </div>
                    <span class="range-text">要求范围</span>
                    <input type="number" placeholder="最少" min="0" id="rulemin2">
                    <img th:src="${_CPR_}+'/elective/images/range.png'" alt="">
                    <input type="number" placeholder="最多" min="0" id="rulemax2">
                    <span class="range-del"></span>
                </div>
            </div>
        </form>
    </div>
    <div class="dialog-btn">
        <button class="pu-cancel">取消</button>
        <button class="pu-sure" id="scoreSure">确定</button>
    </div>
</div>
<!-- 学分限制-查看 -->
<div id="selScoreCheck" class="dialog dialogCheck">
    <div class="dialog-title">
        <h3>学分限制详情</h3><span class="pu-close"></span>
    </div>
    <div class="dialog-con">
        <dl>
            <dt>名称：</dt>
            <dd>计算机</dd>
        </dl>
        <dl>
            <dt>所属年级：</dt>
            <dd>1年级</dd>
        </dl>
        <dl>
            <dt>所属院系：</dt>
            <dd>计算机学院</dd>
        </dl>

        <dl>
            <dt>所属专业：</dt>
            <dd>计算机</dd>
        </dl>
        <dl>
            <dt>所在班级：</dt>
            <dd>1班</dd>
        </dl>
        <dl>
            <dt>所在校区：</dt>
            <dd>南校区</dd>
        </dl>
        <dl>
            <dt>学分：</dt>
            <dd>5</dd>
        </dl>
        <dl>
            <dt>门数：</dt>
            <dd>1</dd>
        </dl>
        <dl>
            <dt>学分：</dt>
            <dd>5</dd>
        </dl>
        <dl>
            <dt>学分门数关系：</dt>
            <dd>1</dd>
        </dl>
    </div>
</div>
</body>
<script type="text/html" id="tmplToolBar">
    <div class="oprate-table">
        <span class="edit" lay-event="check">查看</span>
        <span class="edit" lay-event="edit">编辑</span>
        <span class="delete" lay-event="delete">删除</span>
    </div>
</script>
<script th:src="${_CPR_}+'/elective/js/jquery-3.3.1.min.js'"></script>
<script th:src="${_CPR_}+'/elective/js/common.js'"></script>
<script th:src="${_CPR_}+'/elective/layui/layui.js'"></script>
<script th:src="${_CPR_}+'/elective/js/selCourseSet1.js?v=2'"></script>
<script type="text/javascript">
    var formId = [[${formId}]];
    var formUserId = [[${formUserId}]];
    var fid = [[${fid}]];
    var jhmc = '[[${jhmc}]]';
    var editid = "";
    if ([[${taskRun}]]){
        alert("当前学生数据正在写入，请等待学生选课结果表数据写入完成再添加规则!")
    }

    function exitAgain(num) {
        if (!/^(?:[1-9]|1\d|20)$/.test(num)){
            layer.msg("请输入正确的学期数（1-20内的整数）");
            return ;
        }
        $.ajax({
            type: "POST",
            url: "/elective/course/rule/editLimitAgain",
            data: {formUserId: formUserId, fid: fid, formId: formId, num: num},
            success: function () {
            }
        });
    }
</script>

</html>