<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1, user-scalable=no"/>
    <meta name="format-detection" content="telephone=no,email=no,adress=no">
    <meta http-equiv="pragma" content="no-cache"/>
    <meta http-equiv="cache-control" content="no-cache"/>
    <meta http-equiv="expires" content="0"/>
    <title>教务系统</title>
    <link rel="stylesheet" th:href="@{~/css/examination/mobile/common.css}"/>
    <link rel="stylesheet" th:href="@{~/css/examination/mobile/schedule.css}"/>
    <link rel="stylesheet" th:href="@{~/css/examination/mobile/newStyle.css}"/>
</head>

<body class="">
<!--<div class="tophead">-->
<!--    <div class="head bottomLine">-->
<!--        <div class="leftHead">-->
<!--            <div class="back"></div>-->
<!--        </div>-->
<!--        <div class="centerHead">-->
<!--            <div class="selectBox">-->
<!--                <div class="selectWeek"><span>选择数据范围</span></div>-->
<!--            </div>-->
<!--        </div>-->

<!--    </div>-->

<!--</div>-->
<div class="list hislocationList">
    <div class="listItem ycenter">
        <div class="itemCon">
            <h1>选择考试批次</h1>
        </div>
        <div class="itemRight">
            <p class="arrowRight ycenter" style="height: 100%;" id="selectRole"><span>请选择</span> <img
                    th:src="@{~/images/examination/arrowgrayRight.png}"/></p>
        </div>
    </div>
    <div class="listItem ycenter">
        <div class="itemCon">
            <h1>选择院系</h1>
        </div>
        <div class="itemRight">
            <p class="arrowRight ycenter" id="selectDepart" style="height: 100%;"><span>请选择</span><img
                    th:src="@{~/images/examination/arrowgrayRight.png}"/></p>
        </div>
    </div>
    <div class="listItem ycenter">
        <div class="itemCon">
            <h1>选择年级</h1>
        </div>
        <div class="itemRight">
            <p class="arrowRight ycenter" id="selectTeacherNumber" date-week="4" data-id="10004" data-value="10003"
               style="height: 100%;"><span>请选择</span><img th:src="@{~/images/examination/arrowgrayRight.png}"/></p>
        </div>
    </div>
    <div class="listItem ycenter">
        <div class="itemCon">
            <h1>选择教师</h1>
        </div>
        <div class="itemRight">
            <p class="arrowRight ycenter" style="height: 100%;" id="selectTeacher"><span>请选择</span><img
                    th:src="@{~/images/examination/arrowgrayRight.png}"/></p>
        </div>
    </div>

</div>

<div class="recall-bottom">
    <div class="refresh">重置</div>
    <div class="search">查询</div>
</div>

<!-- 选择院系 -->
<div class="choosedepartment-dialog" id="choosedepartment">
    <div class="w-con">
        <div class="w-head">
            <div class="cancle">取消</div>
            <div class="name">选择院系</div>
            <div class="btns">
                <span class="acllSelect">全选</span>
                <span class="save">保存</span>
            </div>
        </div>
        <div class="w-box">
            <div class="search-inputs">
                <div class="s-con">
                    <img th:src="@{~/images/examination/search-icons.png}" alt="">
                    <input type="search" placeholder="请输入" id="mSearch1">
                    <span class="m-close" style="display: none;"></span>
                </div>
            </div>
            <ul>
                <li class="cur">安保处</li>
                <li>财会经贸系</li>
                <li>诚优商贸教研室</li>
                <li>党政办公室</li>
                <li>轨道机电系</li>
            </ul>
        </div>

    </div>
</div>
<!-- 选择考试批次 -->
<div class="choosedepartment-dialog" id="chooserole">
    <div class="w-con">
        <div class="w-head">
            <div class="cancle">取消</div>
            <div class="name">选择考试批次</div>
            <div class="btns">
                <span class="acllSelect">全选</span>
                <span class="save">保存</span>
            </div>
        </div>
        <div class="w-box">
            <div class="search-inputs">
                <div class="s-con">
                    <img th:src="@{~/images/examination/search-icons.png}" alt="">
                    <input type="search" placeholder="请输入" id="mSearch2">
                    <span class="m-close" style="display: none;"></span>
                </div>
            </div>
            <ul>
                <li class="cur">批次11</li>
                <li>批次22</li>
                <li>批次33</li>
                <li>批次44</li>
                <li>批次55</li>
            </ul>
        </div>

    </div>
</div>
<!-- 选择老师 -->
<div class="choosedepartment-dialog" id="chooseteacher">
    <div class="w-con">
        <div class="w-head">
            <div class="cancle">取消</div>
            <div class="name">选择老师</div>
            <div class="btns">
                <span class="acllSelect">全选</span>
                <span class="save">保存</span>
            </div>
        </div>
        <div class="w-box">
            <div class="search-inputs">
                <div class="s-con">
                    <img th:src="@{~/images/examination/search-icons.png}" alt="">
                    <input type="search" placeholder="请输入" id="mSearch3">
                    <span class="m-close" style="display: none;"></span>
                </div>
            </div>
            <ul>
                <li class="cur">张三</li>
                <li>李四</li>
                <li>王二麻子</li>
                <li>赵丽颖</li>
                <li>古力娜扎</li>
            </ul>
        </div>

    </div>
</div>
<!-- 选择年级 -->
<div class="choosedepartment-dialog" id="chooseteachernumer">
    <div class="w-con">
        <div class="w-head">
            <div class="cancle">取消</div>
            <div class="name">选择年级</div>
            <div class="btns">
                <span class="acllSelect">全选</span>
                <span class="save">保存</span>
            </div>
        </div>
        <div class="w-box">
            <div class="search-inputs">
                <div class="s-con">
                    <img th:src="@{~/images/examination/search-icons.png}" alt="">
                    <input type="search" placeholder="请输入" id="mSearch4">
                    <span class="m-close" style="display: none;"></span>
                </div>
            </div>
            <ul>
                <li class="cur">1年级</li>
                <li>二年级</li>
                <li>三年级</li>
                <li>四年级</li>
                <li>五年级</li>
            </ul>
        </div>

    </div>
</div>


<script th:src="@{~/js/jquery-3.3.1.min.js}"></script>

<script type="text/javascript" th:src="@{~/js/CXJSBridge.js}"></script>
<script type="text/javascript" th:src="@{~/js/app.utils.js}"></script>
<script type="text/javascript" th:src="@{~/js/tools.js}"></script>

<script>
    addIphoneHeight();

    function addIphoneHeight() {
        var isIphone = /iphone/gi.test(navigator.userAgent);
        if (isIphone && (screen.height == 812 && screen.width == 375)) {
            $('body').addClass('iosxwrapMax');
        } else if (isIphone && (screen.width == 414)) {
            $('body').addClass('iospluswrapMax');

        } else if (isIphone) {
            $('body').addClass('ioswrapMax');
        }
    }


    //选择院系
    $(".choosedepartment-dialog").on("click", ".w-con .w-box ul li", function () {
        $(this).toggleClass("cur");
        let totalNus = $(this).parents(".choosedepartment-dialog").find(".w-con .w-box ul li").length;
        let currentNus = $(this).parents(".choosedepartment-dialog").find(".w-con .w-box ul li.cur").length;

        if (totalNus == currentNus) {
            $(this).parents(".choosedepartment-dialog").find(".w-con .w-head .btns span.acllSelect").addClass(
                "clicked").text("取消全选");
        } else {
            $(this).parents(".choosedepartment-dialog").find(".w-con .w-head .btns span.acllSelect").removeClass(
                "clicked").text("全选");
        }
    })

    //全选
    $(".choosedepartment-dialog .w-con .w-head .btns span.acllSelect").click(function () {
        $(this).toggleClass("clicked");
        if ($(this).hasClass("clicked")) {
            $(this).parents(".choosedepartment-dialog").find(".w-con .w-box ul li").addClass("cur");
            $(this).text("取消全选");
        } else {
            $(this).parents(".choosedepartment-dialog").find(".w-con .w-box ul li").removeClass("cur");
            $(this).text("全选");
        }
    })


    $("#selectDepart").click(function () {
        $("#choosedepartment").show();
        setTimeout(function () {
            $("#choosedepartment .w-con").addClass("active");
        }, 10)
    })

    $("#selectRole").click(function () {
        $("#chooserole").show();
        setTimeout(function () {
            $("#chooserole .w-con").addClass("active");
        }, 10)
    })

    $("#selectTeacher").click(function () {
        $("#chooseteacher").show();
        setTimeout(function () {
            $("#chooseteacher .w-con").addClass("active");
        }, 10)
    })

    $("#selectTeacherNumber").click(function () {
        $("#chooseteachernumer").show();
        setTimeout(function () {
            $("#chooseteachernumer .w-con").addClass("active");
        }, 10)
    })

    //取消
    $(".choosedepartment-dialog .w-con .w-head .cancle").click(function () {
        $(".choosedepartment-dialog").hide();
        $(".choosedepartment-dialog .w-con").removeClass("active");
    })

    //保存
    $("#choosedepartment .w-con .w-head .btns .save").click(function () {
        let cStr = '';
        $("#choosedepartment .w-con .w-box ul li.cur").each(function () {
            cStr += $(this).text() + ','
        })
        $("#choosedepartment").hide();
        $("#choosedepartment .w-con").removeClass("active");
        $("#selectDepart").find("span").text(cStr.slice(0, cStr.length - 1)).addClass("color1");
    })

    $("#chooserole .w-con .w-head .btns .save").click(function () {
        let cStr = '';
        $("#chooserole .w-con .w-box ul li.cur").each(function () {
            cStr += $(this).text() + ','
        })
        $("#chooserole").hide();
        $("#chooserole .w-con").removeClass("active");
        $("#selectRole").find("span").text(cStr.slice(0, cStr.length - 1)).addClass("color1");
    })
    $("#chooseteacher .w-con .w-head .btns .save").click(function () {
        let cStr = '';
        $("#chooseteacher .w-con .w-box ul li.cur").each(function () {
            cStr += $(this).text() + ','
        })
        $("#chooseteacher").hide();
        $("#chooseteacher .w-con").removeClass("active");
        $("#selectTeacher").find("span").text(cStr.slice(0, cStr.length - 1)).addClass("color1");
    })
    $("#chooseteachernumer .w-con .w-head .btns .save").click(function () {
        let cStr = '';
        $("#chooseteachernumer .w-con .w-box ul li.cur").each(function () {
            cStr += $(this).text() + ','
        })
        $("#chooseteachernumer").hide();
        $("#chooseteachernumer .w-con").removeClass("active");
        $("#selectTeacherNumber").find("span").text(cStr.slice(0, cStr.length - 1)).addClass("color1");
    })


    //重置
    $(".recall-bottom .refresh").click(function () {
        $("#selectDepart").find("span").removeClass("color1").text("请选择院系");
        $("#week").find("span").removeClass("color1").text("请选择周次");
    })
</script>
</body>

</html>