<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>同步记录</title>
    <link rel="stylesheet" th:href="@{~/css/examination/global2.css}">
    <link rel="stylesheet" th:href="@{~/css/util/syncList.css}">
    <link rel="stylesheet" th:href="@{~/plugin/layui-v2.8.18/layui/css/layui.css}">
    <script th:src="@{~/js/jquery-1.11.3.min.js}"></script>
    <script th:src="@{~/plugin/layui-v2.8.18/layui/layui.js}"></script>
    <script th:src="@{~/js/cultivation/slideCommon.js}"></script>
</head>

<body>
<div class="masker"></div>
<div class="dialog" id="exportRecord">
    <div class="dialog-con">
        <div class="item">
            <div class="label">状态<span class="layui-tips"
                                         data-tip="批量编辑已选数据，如需修改所有数据请先勾选表格下方“选中所有数据”"></span>
            </div>
            <div class="j-search-con multiple-box">
                <input type="text" name="termSel" placeholder="请选择" readonly="" class="schoolSel" value="生成失败">
                <span class="j-arrow"></span>
                <div class="j-select-year">
                    <ul>
                        <li data-id="1" class="">正在处理</li>
                        <li data-id="2" class="">处理成功</li>
                        <li data-id="3" class="">处理失败</li>
                    </ul>
                </div>
            </div>
            <button type="submit" class="search-btn">查询</button>
        </div>
        <div class="recordList">
            <table class="layui-hide" id="recordList" lay-filter="recordList"></table>
            <div class="selRecord" id="selRecord">
                <span>共<i>4</i>条</span>
            </div>
            <div class="refresh">刷新</div>
        </div>
    </div>
    <div class="dialog-footer">
        <button class="btn-cancel">取消</button>
        <button class="btn-sure">确定</button>
    </div>
</div>
</body>
<script type="text/html" id="status">
    {{#  if(d.state==1){ }}
    <span title="正在处理，如果长时间无结果，请联系管理员">正在处理</span>
    {{#  } else if(d.state==2){ }}
    <span style="color: #3EB35A">处理成功</span>
    {{#  } else{ }}
    <span style="color: #F76560">处理失败</span>
    {{#  } }}
</script>
<script type="text/html" id="tmplToolBar">
    {{#  if(d.state==3){ }}
    <a class="recordDetail" lay-event="detail">查看失败详情</a>
    {{# } else { }}
    <div class="oprate-table">
        <span lay-event="delete">--</span>
    </div>
    {{# } }}
</script>
<script th:inline="javascript">
    const fid = [[${fid}]];
    const tableName = [[${tableName}]];
    const formId = [[${formId}]];
    layui.use(['jquery', 'table', 'element'], function () {
        const table = layui.table;
        const element = layui.element;
        const $ = layui.jquery;
        table.render({
            elem: '#recordList',
            url: '/sync/record/list',
            where: {
                fid: fid,
                tableName: tableName,
                formId: formId
            },
            parseData: function (res) {
                if (res.code == 200) {
                    return {
                        "code": 0, //解析接口状态
                        "msg": res.msg, //解析提示文本
                        "count": res.data.total, //解析数据长度
                        "data": res.data.records //解析数据列表
                    }
                } else {
                    return {
                        "code": 1, //解析接口状态
                        "msg": "服务异常", //解析提示文本
                    }
                }

            },
            height: '470',
            cols: [
                [{
                    field: "name",
                    title: "操作人姓名",
                    align: "center",
                    width: 124,
                },
                    {
                        field: "uid",
                        title: "操作人uid",
                        align: "center",
                    },

                    {
                        field: "createTime",
                        title: "操作时间",
                        minWidth: 156,
                        align: "center",
                    },
                    {
                        title: "当前进度",
                        align: "center",
                        templet: function (d) {
                            return '<div class="progress-content"><div class="progress-bar"><span class="progress proSuccess" id="rate' + d.LAY_NUM + '"></span></div><div class="progress-text">' + d.syncNum + '/' + d.totalNum + '</div><span class="progress-tip"></span></div>';
                        },
                    },
                    {
                        field: "state",
                        title: "状态",
                        align: "center",
                        templet: "#status",
                        width: 100
                    },
                    {
                        title: "操作",
                        align: "center",
                        toolbar: "#tmplToolBar",
                        width: 140
                    },
                ]
            ],
            done: function (res, curr, count) {
                $("#selRecord span i").text(count);
                element.render();
                $("#total").html(count)
                if (res.data == undefined) {
                    return;
                }

                res.data.forEach((data, index) => {
                    if (data.syncNum < data.totalNum && data.state == 1) {
                        printNumbers(index, data, 0);
                    }
                })

                function printNumbers(index, data, curNum) {
                    $.get("/sync/record/num", {id: data.id}, function (res) {
                        if (res.code !== 200) {
                            return;
                        }
                        res = res.data;
                        if (res === -1) {
                            return;
                        }
                        const rateId = '#rate' + ((curr - 1) * 10 + index + 1);
                        $(rateId).css("width", (res / data.totalNum) * 100 + "%")
                        $(rateId).parents(".progress-content").find(".progress-text").text(res + "/" + data.totalNum);
                        if (res < data.totalNum) {
                            setTimeout(function () {
                                printNumbers(index, data, curNum);
                            }, 1000);
                        }
                        if (res >= data.totalNum) {
                            const status = $("tr[data-index=" + index + "] td[data-field=state] div").eq(0);
                            status.html('<span style="color: #3EB35A">处理成功</span>')
                        }
                    })
                }
            },
            page: {
                prev: '上一页',
                next: '下一页',
                limit: 10,
                layout: ['prev', 'page', 'next', 'skip', 'limit']
            }
        });

        table.on('tool(recordList)', function (obj) {
            if (obj.event === 'detail') {
                var data = obj.data;

                var hostname = window.location.host;
                var protocol = window.location.protocol;
                window.open(protocol + "//" + hostname + "/data-monitor/index.html?fid=" + data.fid + "&type=2&opType=" + data.opType + "&uid=" + data.uid + "&module=" + (data.moduleCode == null ? "" : data.moduleCode) + "&alias=" + data.formAlias + "&targetAlias=" + data.targetPoint[0]?.alias + "&startTime=" + data.createTime + "&endTime=" + data.updateTime)
            }
        })
        // 刷新
        $(".refresh").click(function () {
            table.reload('recordList');
        })
        //切换状态
        $(".search-btn").click(function () {
            let state = $(".j-select-year .active").map(function() {
                return $(this).attr("data-id");
            }).get();
            let field = {
                fid: fid,
                tableName: tableName,
                formId: formId,
                state: state.join(",")
            }
            table.reload('recordList', {where: field, page: {curr: 1}});
        })
        $(".btn-cancel,.btn-sure").click(function () {
            window.parent.postMessage(JSON.stringify({action: 1}), "*");
        })
    })
</script>

</html>