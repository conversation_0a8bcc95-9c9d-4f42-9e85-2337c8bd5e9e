<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>同步补考学生名单</title>
    <link rel="stylesheet" th:href="@{~/css/examination/global2.css}">
    <link rel="stylesheet" th:href="@{~/css/examination/dialog.css}">

</head>

<body>
<div class="masker"></div>
<div class="dialog" id="invigilateMax">
    <div class="dialog-title">同步补考学生名单</div>
    <div class="dialog-con">
        <div class="item">
            <div class="label">选择补考学年学期</div>
            <div class="j-search-con single-box">
                <input type="text" name="teacherName" placeholder="请选择" readonly="" class="schoolSel" id="xnxq">
                <span class="j-arrow"></span>
                <div class="j-select-year ">
                    <div class="search">
                        <input type="text" placeholder="搜索">
                        <span></span>
                    </div>
                    <ul class="xnxqUl">

                    </ul>
                </div>
            </div>
        </div>
    </div>
    <div class="dialog-btn">
        <button class="pu-sure" id="sync">开始同步</button>
    </div>
</div>
</body>
<script th:src="@{~/js/jquery-3.3.1.min.js}"></script>
<script th:src="@{~/js/my.util.js}"></script>
<script>
    $(document).ready(function () {
        // 下拉多选
        $(".j-search-con").on("click", ".schoolSel", function (e) {
            $(".j-select").hide();
            let parent = $(this).parent();
            parent.find('.j-arrow').toggleClass('j-arrow-slide');
            parent.find('.j-select-year').toggleClass('slideShow')
            stopBubble(e)
        })

        // 选择-单选
        $(".j-search-con.single-box").on("click",
            ".j-select-year li ",
            function (e) {
                $(this).addClass('active').siblings().removeClass();
                var parents = $(this).parents('.j-search-con');
                var schoolSelEle = parents.find('.schoolSel');
                var txt = $(this).text();
                schoolSelEle.val(txt);
                parents.find('.j-arrow').removeClass('j-arrow-slide');
                parents.find('.j-select-year').removeClass('slideShow')
                stopBubble(e);
            })
        // 搜索
        $(".j-search-con").on('click', '.search input', function (e) {
            stopBubble(e);
        })
        $(".j-search-con").on('input', '.search input', function (e) {
            let val = $(this).val();
            var nextEle = $(this).parents('.j-search-con').find('ul')
            if (val != "") {

                nextEle.find('li').each(function (i, ele) {
                    let txt = $(ele).text();
                    if (txt.indexOf(val) >= 0) {
                        $(ele).show();
                    } else {
                        $(ele).hide();
                    }
                })
            } else {
                nextEle.find('li').show();
            }
            stopBubble(e)
        })

        function stopBubble(e) {
            if (e && e.stopPropagation)
                e.stopPropagation();
            else {
                window.event.cancelBubble = true;
            }

        }

        $(document).on("click", function (event) {
            var _con = $('.select-input');
            if (!_con.is(event.target) && _con.has(event.target).length === 0) {
                $(".j-search-con").find('.j-arrow').removeClass('j-arrow-slide');
                $(".j-search-con").find('.j-select-year').removeClass('slideShow')
            }
        })

    })
</script>
<script th:inline="javascript">
    var fid = [[${fid}]];
    var uid = [[${uid}]];
    var formId = [[${formId}]];
    var queryId = [[${queryId}]];
    var selectTotal = [[${selectTotal}]];

    $(function () {
        getXnxq();
    })

    function getXnxq() {
        $.get("/basic/xnxq", {fid: fid}, function (res) {
            if (res.code == 200) {
                var data = res.data;
                var html = '';
                var curxnxq='';
                for (let i = 0; i < data.length; i++) {
                    if (data[i].xnxq_sfdqxq == '是') {
                        curxnxq = data[i].xnxq_xnxqh;
                        html += "<li class='active'>" + data[i].xnxq_xnxqh + "</li>"
                    } else {
                        html += "<li>" + data[i].xnxq_xnxqh + "</li>"
                    }
                }
                $("#xnxq").val(curxnxq);
                $(".xnxqUl").html(html);
            }
        })
    }


    $("#sync").click(function () {
       var flag=check();
       if (!flag){
           return;
       }
        $.get("/api/make_up/sync", {
            fid: fid,
            uid: uid,
            formId:formId,
            queryId:queryId,
            selectTotal:selectTotal,
            xnxq: $("#xnxq").val()
        }, function (res) {
            if (res.code == 200) {
                U.success("正在处理中，请稍后...")
                setTimeout(U.closePop,2000)
            } else {
                U.fail(res.msg)
            }
        })
    })


    function check(){
        if ($("#xnxq").val() == undefined || $("#xnxq").val() == '') {
            U.fail("请选择学年学期")
            return false;
        }

        return true;
    }
</script>

</html>