<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教室预约</title>
    <link rel="stylesheet" th:href="@{../css/basic/roomReserve/global.css}">
    <link rel="stylesheet" th:href="@{../layui/css/layui.css}">
    <link rel="stylesheet" th:href="@{../layui/css/layui.css}">
    <link rel="stylesheet" th:href="@{../css/basic/roomReserve/reset.css}">
    <link rel="stylesheet" th:href="@{../css/basic/roomReserve/common.css}">
    <link rel="stylesheet" th:href="@{../css/basic/roomReserve/courseScheduleReport.css}">
    <link rel="stylesheet" th:href="@{../css/basic/roomReserve/classroom-reservation.css}">
    <link rel="stylesheet" th:href="@{../css/basic/roomReserve/supplementary-style.css}">
    <style>
        /* 2024.12.25*/
        #classroomReservation {
            width: 1159px;
            height: auto !important;
        }

        #classroomReservation .popup-con {
            height: auto;
        }

        #classroomReservation {
            width: 1000px;
        }

        #appointmentPersonInformation {
            width: 1000px;
        }

        #classroomReservation .popup-con .layui-table-body.layui-table-main {
            max-height: 111px;
        }

        #appointmentPersonInformation .popup-con .filter-wrapper .layui-form-item .layui-input-block {
            width: 134px;
        }

        #classroomReservation .popup-con .layui-form .layui-form-item .layui-input-block {
            width: 180px;
        }

        #appointmentPersonInformationStudent {
            width: 1000px;
            height: auto;
        }

        #appointmentPersonInformationStudent .popup-con .filter-wrapper {
            margin-bottom: 10px;
        }

        #appointmentPersonInformationStudent .popup-con .filter-wrapper .layui-form-item {
            margin-bottom: 12px;
        }

        #appointmentPersonInformationStudent .popup-con .filter-wrapper {
            flex-wrap: wrap;
        }

        #appointmentPersonInformationStudent .popup-con .filter-wrapper .layui-form-item .layui-input-block {
            width: 216px;
        }

        #appointmentPersonInformationStudent .popup-con .layui-disabled {
            border: 1px solid #E5E6EB !important;
            border-radius: 2px;
        }

        #appointmentPersonInformationStudent .jpPage {
            width: 100%;
            height: 52px;
            position: relative;
            display: flex;
            display: -webkit-flex;
            align-items: center;
            justify-content: flex-end;
        }

        #appointmentPersonInformationStudent .jpPage .count-total {
            position: absolute;
            left: 0;
            line-height: 52px;
            color: #86909C;
        }

        #appointmentPersonInformation .jpPage {
            width: 100%;
            height: 52px;
            position: relative;
            display: flex;
            display: -webkit-flex;
            align-items: center;
            justify-content: flex-end;
        }

        #appointmentPersonInformation .jpPage .count-total {
            position: absolute;
            left: 0;
            line-height: 52px;
            color: #86909C;
        }

        #appointmentPersonInformation .layui-table-body.layui-table-main {
            max-height: 296px;
        }

        #appointmentPersonInformationStudent .layui-table-body.layui-table-main {
            max-height: 296px;
        }

        /* 2024.12.25*/
    </style>

</head>

<body>
    <div class="main">
        <div class="top">
            <div class="titles">
                <ul>
                    <li><a href="javascript:viod()">教室预约</a></li>
                </ul>
            </div>
            <div class="r-title">
                <span class="book-classroom">预约教室</span>
                <span th:if="${isSuperAdmin}" class="set-appointment-rules">设置预约规则</span>
            </div>
        </div>

        <div class="form-con">
            <div class="sel-box">
                <div class="sel-item">
                    <div class="sel-title">校区</div>
                    <div class="sel" style="margin-right:0;">
                        <div class="select-input">
                            <div class="name" data-name="全部">全部</div>
                            <em></em>
                            <div class="select-dropdown">
                                <ul class="dropdown-lists dropdown-lists-single school-name">
                                    <li class="cur">全部</li>
                                    <li th:each="schoolName : ${schoolNameList}" th:text="${schoolName}"></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="sel-item">
                    <div class="sel-title">教室类型</div>
                    <div class="sel" style="margin-right:0;">
                        <div class="select-input">
                            <div class="name" data-name="全部">全部</div>
                            <em></em>
                            <div class="select-dropdown">
                                <ul class="dropdown-lists dropdown-lists-single room-type">
                                    <li class="cur">全部</li>
                                    <li th:each="roomType : ${roomTypeList}" th:text="${roomType}"></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="sel-item">
                    <div class="sel-title">教学楼</div>
                    <div class="sel" style="margin-right:0;">
                        <div class="select-input">
                            <div class="name" data-name="全部">全部</div>
                            <em></em>
                            <div class="select-dropdown">
                                <ul class="dropdown-lists dropdown-lists-single building" id="building-name">
                                    <li class="cur">全部</li>
                                    <li th:each="buildingName : ${buildingNameList}" th:text="${buildingName}"></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="sel-item">
                    <div class="sel-title">教室</div>
                    <div class="sel" style="margin-right:0;">
                        <div class="select-input">
                            <div class="name" data-name="全部">全部</div>
                            <em></em>
                            <div class="select-dropdown">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul class="dropdown-lists dropdown-lists-single room-name" id="room-name">
                                    <li class="cur">全部</li>
                                    <li th:each="roomName : ${roomNameList}" th:text="${roomName}"></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="sel-item">
                    <div class="sel-title">座位数</div>
                    <div class="sel seat" style="margin-right:0;">
                        <input type="number" min="0" autocomplete="off" class="layui-input first seatsNumber1" placeholder="请输入">
                        <span>-</span>
                        <input type="number" min="0" autocomplete="off" class="layui-input second seatsNumber2" placeholder="请输入">
                    </div>
                </div>
                <div class="sel-item">
                    <div class="sel-title">周次</div>
                    <div class="sel" style="margin-right:0;">
                        <div class="select-input">
                            <div class="name" data-name="请选择">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <div class="all-selects all-selects-week">
                                    全选
                                </div>
                                <ul class="dropdown-lists week-number" id="week-number">
                                    <li th:each="item : ${weekInfo}" th:value="${item.weekNumber}"><span th:text="'第'+${item.weekNumber}+'周'"></span></li>
                                </ul>
                                <input type="hidden" class="week-day" th:each="item : ${weekInfo}" th:name="${item.weekNumber}" th:value="${item.value}"/>
                                <input type="hidden" class="week-day-time" th:each="item : ${weekInfo}" th:name="${item.weekNumber}" th:value="${item.day}"/>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="sel-item">
                    <div class="sel-title">星期</div>
                    <div class="sel" style="margin-right:0;">
                        <div class="select-input">
                            <div class="name" data-name="请选择">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <div class="all-selects all-selects-day">
                                    全选
                                </div>
                                <ul class="dropdown-lists day-of-week" id="day-of-week">
                                    <li value="1">
                                        <span>星期一</span>
                                    </li>
                                    <li value="2">
                                        <span>星期二</span>
                                    </li>
                                    <li value="3">
                                        <span>星期三</span>
                                    </li>
                                    <li value="4">
                                        <span>星期四</span>
                                    </li>
                                    <li value="5">
                                        <span>星期五</span>
                                    </li>
                                    <li value="6">
                                        <span>星期六</span>
                                    </li>
                                    <li value="7">
                                        <span>星期日</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="sel-item">
                    <div class="sel-title">节次</div>
                    <div class="sel" style="margin-right:0;">
                        <div class="select-input">
                            <div class="name" data-name="请选择">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <div class="all-selects">
                                    全选
                                </div>
                                <ul class="dropdown-lists curricula-number">
                                    <li th:each="item : ${tablesTimes}" th:value="${item}"><span th:text="${item}"></span></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="sel-item">
                    <div class="sel-title">借用类型</div>
                    <div class="sel" style="margin-right:0;">
                        <div class="select-input">
                            <div class="name" data-name="请选择">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <ul class="dropdown-lists dropdown-lists-single borrow-type">
                                    <li class="cur">全部</li>
                                    <li th:each="item : ${roomUseTypeList}" th:if="${item != ''}" th:value="${item}"><span th:text="${item}"></span></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="sel-item">
                    <button class="btn btn-search">查询</button>
                    <button class="btn btn-reset">重置</button>

                </div>

            </div>

        </div>
        <div class="selected-wrapper">
            <span class="classrooms-nums">已选教室数：<i>0</i></span>
            <span class="all-seat">总座位数：<i>0</i></span>
            <span class="test-seat">总考试座位数：<i>0</i></span>
        </div>

        <div class="course-list">
            <table class="layui-hide" id="courseList" lay-filter="course-list"></table>
        </div>
        <div class="jpPage">
            <div class="count-total .courseList-total">共<span>0</span>条</div>
            <div class="pageCon" id="coursePage"></div>
        </div>

    </div>

    <div id="classroomReservation" class="popup">
        <div class="title">
            <div class="name">教室预约信息</div>
            <div class="close"></div>
        </div>
        <div class="popup-con">
            <div class="reservatio-inform">
                <div class="item" style="margin-right:70px;">
                    <div class="name">预约时间：</div>
                    <div class="texts" id="timeParam">周次：7-9 星期：2 节次：1-2</div>
                </div>
                <div class="item">
                    <div class="name">总教室数：</div>
                    <div class="texts">5</div>
                </div>
                <div class="item">
                    <div class="name">总座位数：</div>
                    <div class="texts">126</div>
                </div>
                <div class="item">
                    <div class="name">总考试座位数：</div>
                    <div class="texts">126</div>
                </div>
            </div>
            <table class="layui-table" id="courseList1" lay-filter="course-list1"></table>
            <div class="layui-form">
                <div class="layui-form-item">
                    <div class="layui-form-label"><em>*</em>借用人类型</div>
                    <div class="layui-input-block" style="flex:1;width: 0;">
                        <input type="radio" name="personType" lay-filter="ptype" value="0" title="老师" checked>
                        <input type="radio" name="personType" lay-filter="ptype" value="1" title="学生">
                        <input type="radio" name="personType" lay-filter="ptype" value="2" title="其他">
                    </div>
                </div>
                <div class="layui-form-item">

                    <div class="layui-form-label" style="width:77px;"><em>*</em>借用人</div>
                    <input type="hidden" id="reservePersonUid"/>
                    <input type="hidden" id="reservePersonName"/>
                    <div class="layui-input-block" style="margin-right:24px;">
                        <div class="select-borrower"><span>选择借用人</span></div>
                        <input id="personName" type="text" class="layui-input hide" placeholder="填写借用人信息">
                    </div>
                    <div class="layui-form-label" style="width:100px;"><em>*</em>联系电话</div>
                    <div class="layui-input-block" style="margin-right:24px;">
                        <input id="personPhone" type="text" placeholder="请输入" class="layui-input">
                    </div>
                    <div class="layui-form-label borrow-item hide" style="width:77px;"><em>*</em>借用部门</div>
                    <div class="layui-input-block borrow-item hide">
                        <select lay-filter="department" id="department">
                            <option value="">请选择</option>
                            <option th:each="item : ${deptInfoList}" th:value="${item}" th:text="${item}"></option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-form-label" style="width:100px;"><em>*</em>使用设备</div>
                    <div class="layui-input-block" style="margin-right:24px;">
                        <select lay-filter="device" id="device" th:value="1">
                            <option value="1">是</option>
                            <option value="2">否</option>
                        </select>
                    </div>
                    <div class="layui-form-label" style="width:77px;">借用用途 </div>
                    <div class="layui-input-block" style="margin-right:24px;">
                        <select lay-filter="device" id="reservePurpose">
                            <option value="">请选择</option>
                            <option th:each="item : ${reservePurposeList}" th:value="${item}" th:text="${item}">请选择</option>
                        </select>
                    </div>
                    <div class="layui-form-label" style="width:70px;">添加附件</div>
                    <div class="layui-input-block" style="margin-right:0;">
                        <div class="add-attachment" id="test-upload-btn"><span>请添加</span></div>
                        <div class="file-inform hide">
                            <input type="hidden" id="fileObjectId"/>
                            <input type="hidden" id="fileName"/>
                            <input type="hidden" id="fileType"/>
                            <input type="hidden" id="fileResId"/>
                            <span>71236438127.zip</span>
                            <em></em>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-form-label">借用原因</div>
                    <div class="layui-input-block layui-textarea1" style="flex:1;width: 0;">
                        <textarea id="reserveDesc" placeholder="请输入内容" class="layui-textarea"></textarea>
                    </div>
                </div>
            </div>

        </div>
        <div class="bottom">
            <button type="reset" class="layui-btn exam-cancle">
                取消
            </button>
            <button class="layui-btn exam-sure">确定</button>
        </div>
    </div>

    <div id="classroomReservationSet" class="popup">
        <div class="title">
            <div class="name">教室预约设置</div>
            <div class="close"></div>
        </div>
        <div class="popup-con">
            <div class="set-wrapper layui-form">
                <div class="item">
                    <div class="label">
                        <em>教室可借用时间控制 </em>
                        <i class="tips"><em>教室可借用时间控制</em></i>
                    </div>
                    <div class="select-block">
                        <select lay-filter="startTime" id="startTime" width="100px" >
                            <option value="">请选择</option>
                        </select>
                        <div class="tips">-</div>
                        <select lay-filter="endTime" id="endTime" width="100px" >
                            <option value="">请选择</option>
                        </select>
                    </div>
                </div>
                <div class="item">
                    <div class="label">
                        <em>排考教室同周不预约 </em>
                        <i class="tips"><em>排考教室同周不预约</em></i>
                    </div>
                    <div class="limit-switch" id="weeklyAppointment">
                        <input type="checkbox" name="weeklyAppointment" lay-skin="switch"
                            lay-filter="weeklyAppointment">
                    </div>
                </div>
                <div class="item room-item">
                    <div class="label">
                        <em>排考教室同天不预约 </em>
                        <i class="tips"><em>排考教室同天不预约</em></i>
                    </div>
                    <div class="limit-switch" id="dayReservation">
                        <input type="checkbox" name="dayReservation" lay-skin="switch" lay-filter="dayReservation">
                    </div>
                </div>
            </div>

        </div>
        <div class="bottom">
            <button type="reset" class="layui-btn exam-cancle">
                取消
            </button>
            <button class="layui-btn exam-sure">确定</button>
        </div>
    </div>
    <div id="appointmentPersonInformation" class="popup">
        <div class="title">
            <div class="name">预约人信息</div>
            <div class="close"></div>
        </div>
        <div class="popup-con">
            <form class="layui-form filter-wrapper" id="form_info">
                <div class="layui-form-item">
                    <div class="layui-form-label">教师</div>
                    <div class="layui-input-block">
                        <input type="text" class="layui-input" placeholder="请输入姓名/工号" id="teaName">
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-form-label">所属部门</div>
                    <div class="layui-input-block">
                        <select lay-filter="department" id="teaDept">
                            <option value="">请选择</option>
                            <option value="1">计算机教学部</option>
                            <option value="2">教务处</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-form-label">教研室</div>
                    <div class="layui-input-block">
                        <select lay-filter="department" id="teaJYS">
                            <option value="">请选择</option>
                            <option value="1">软件工程</option>
                            <option value="2">信息技术</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="btn btn-search">查询</div>
                    <div class="btn btn-reset">重置</div>
                </div>
            </form>
            <table class="layui-table" id="courseList2" lay-filter="course-list2"></table>
            <div class="jpPage">
                <div class="count-total courseList2-total">共<span>240</span>条</div>
                <div class="pageCon" id="coursePage2"></div>
            </div>

        </div>
        <div class="bottom">
            <button type="reset" class="layui-btn exam-cancle">
                取消
            </button>
            <button class="layui-btn exam-sure">确定</button>
        </div>
    </div>


    <div id="appointmentPersonInformationStudent" class="popup">
        <div class="title">
            <div class="name">预约人信息</div>
            <div class="close"></div>
        </div>
        <div class="popup-con">
            <form class="layui-form filter-wrapper" id="form_info1">
                <div class="layui-form-item">
                    <div class="layui-form-label">学生</div>
                    <div class="layui-input-block">
                        <input type="text" class="layui-input" placeholder="请输入学生姓名/工号" id="stuName">
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-form-label">班级</div>
                    <div class="layui-input-block">
                        <select lay-filter="department" id="stuClass">
                            <option value="">请选择</option>
                            <option value="1">班级1</option>
                            <option value="2">班级2</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-form-label">系部</div>
                    <div class="layui-input-block">
                        <select lay-filter="department" id="stuDept">
                            <option value="">请选择</option>
                            <option value="1">系部1</option>
                            <option value="2">系部2</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-form-label">年级</div>
                    <div class="layui-input-block">
                        <select lay-filter="department" id="gradeName">
                            <option value="">请选择</option>
                            <option value="1">年级1</option>
                            <option value="2">年级2</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="btn btn-search">查询</div>
                    <div class="btn btn-reset">重置</div>
                </div>
            </form>
            <table class="layui-table" id="courseList3" lay-filter="course-list3"></table>

            <!-- 2024.12.25 -->
            <div class="jpPage">
                <div class="count-total courseList3-total">共<span>0</span>条</div>
                <div class="pageCon" id="coursePage1"></div>
            </div>
            <!--2024.12.25-->

        </div>
        <div class="bottom">
            <button type="reset" class="layui-btn exam-cancle">
                取消
            </button>
            <button class="layui-btn exam-sure">确定</button>
        </div>
    </div>

</body>
<script type="text/html" id="tmplToolBar">
    <div class="inform" style="color: #ef3e3e;cursor: pointer;" lay-event="delete">删除</div>
</script>

<script th:src="@{../../js/jquery-3.3.1.min.js}"></script>
<script th:src="@{../../layui/layui.js}"></script>
<script th:src="@{../../js/basic/roomReserve/common.js}"></script>
<script th:src="@{../../js/basic/roomReserve/classroom-reservation.js}"></script>

</html>