<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"  lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量修改开课系部</title>
    <link rel="stylesheet" th:href="@{../css/cultivation/global.css}">
    <link rel="stylesheet" th:href="@{../css/cultivation/batchChangeDepth.css}">
</head>

<body>
    <div class="dialog-mark" id="dialogSet">
        <div class="d-dialog dialog-set">
            <div class="dialog-con">
                <div class="d-count">本次操作将修改<em>2</em>条数据</div>
                <div class="d-item">
                    <div class="item-title">修改字段</div>
                    <div class="item-con">
                        <input type="text" disabled value="开课系部" class="depth">
                        <p>修改已选数据，如需修改所有数据请先勾选表格下方“选中所有数据”</p>
                    </div>
                </div>
                <div class="d-item">
                    <div class="item-title">修改为</div>
                    <div class="item-con">
                        <div class="j-search-con single-box">
                            <input type="text" placeholder="请选择" readonly="" class="j-select">
                            <span class="j-arrow j-arrow-slide"></span>
                            <div class="j-select-list">
                                <div class="j-select-search">
                                    <input type="text" placeholder="请输入">
                                    <span></span>
                                </div>
                                <ul>
                                </ul>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
            <div class="dialog-btn">
                <button class="btn-cancel" id="btn-cancel">取消</button>
                <button class="btn-submit">提交</button>
            </div>
        </div>
    </div>
    <div class="dialog-mark" id="dialogTip" style="display: none;">
        <div class="d-dialog dialog-tip">
            <div class="dialog-title">
                <h3>提示</h3>
                <p>本次操作将修改<em>2</em>条数据</p>
                <span class="close"></span>
            </div>
            <div class="dialog-con">
                <p>批量编辑操作无法撤回，确定修改？</p>
            </div>
            <div class="dialog-btn">
                <button class="btn-cancel">取消</button>
                <button class="btn-submit">确认</button>
            </div>
        </div>
        <div class="d-dialog dialog-modify">
            <div class="modify"><img src="/images/cultivation/loading1.gif" alt="">正在修改中</div>
        </div>
    </div>
</body>
<script th:src="@{../js/jquery-3.3.1.min.js}"></script>
<script th:inline="javascript">
    let type = [[${type}]];
    let selectTotal = [[${selectTotal}]];
    let queryId = [[${queryId}]];
    let fid = [[${fid}]];
    let uid = [[${uid}]];
    document.domain = "chaoxing.com";
    $(document).ready(function () {
        $(".d-count em").text(selectTotal);
        $(".dialog-title p em").text(selectTotal);
        $(".j-select").click(function () {
            $(this).next().toggleClass('j-arrow-slide');
            $(this).next().next().toggleClass('slideShow');
        });
        // 选择
        $(".j-select-list").on('click', "ul li", function () {
            var txt = $(this).text();
            var parentEle = $(this).parents('.j-select-list');
            parentEle.removeClass('slideShow');
            parentEle.prev().prev().val(txt);
            $(this).addClass('active').siblings().removeClass('active');
        });

        $("#btn-cancel").click(function (){
            window.parent.postMessage(JSON.stringify({action:1}),"*");
        })

        // 点击确认
        $(".dialog-set .btn-submit").click(function () {
            const dept = $(".j-select").val();
            if(!dept){
                alert("请选择院系");
                return false;
            }
            $("#dialogTip").show();
        })
        // 确定/取消
        $(".dialog-tip .dialog-btn button").click(function () {
            if ($(this).hasClass("btn-submit")) {
                const dept = $(".j-select").val();
                // 确定
                // 正在修改中
                $(".dialog-modify").css({
                    display: 'flex'
                })
                $.post("../processData/modifyDepartment",{queryId:queryId,fid:fid,type:type,uid:uid,dept:dept},function(result){
                    if(result.success){
                        $(".dialog-mark").hide();
                        window.parent.postMessage(JSON.stringify({action:1}),"*");
                    }
                },"json");
                return false;
            }
            $("#dialogTip").hide();
        })
        $(".dialog-tip .dialog-title .close").click(function () {
            $("#dialogTip").hide();
        })
        getCollegeInfoData();
        if(type === 2){
            $.post("../processData/getTeachPlanApprovedCount",{queryId:queryId,fid:fid,uid:uid},function(result){
                const approvedCount = result.approvedCount;
                $(".d-count").html("本次选中<em>"+selectTotal+"</em>条数据，<em>"+approvedCount+"</em>条数据已审核通过，本次操作将修改<em>"+(selectTotal-approvedCount)+"</em>条数据");
                $(".dialog-title p").html("本次操作将修改<em>"+(selectTotal-approvedCount)+"</em>条数据");
            },"json");
        }
    })

    let flag = true;
    $('.j-select-search input').on({
        'compositionstart': function() {
            flag = true;
        },
        'compositionend': function() {
            flag = false;
            if(!flag) {
                getCollegeInfoData();
            }
        },
        'input propertychange': function() {
            if(!flag) {
                getCollegeInfoData();
            }
        }
    });

    function getCollegeInfoData(){
        let html = "";
        let name = $(".j-select-search input").val();
        $.post("../processData/getCollegeInfoData",{name:name},function(result){
            for (let i = 0; i < result.list.length; i++) {
                const college = result.list[i];
                html += "<li>"+college.name+"</li>";
            }
            $(".j-select-list ul").html(html);
        },"json");
    }
</script>

</html>