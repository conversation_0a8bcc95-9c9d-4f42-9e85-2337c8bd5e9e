<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>开课</title>
    <link rel="stylesheet" th:href="@{../css/cultivation/global.css}">
    <link rel="stylesheet" th:href="@{../plugin/layui/css/layui.css}">
    <link rel="stylesheet" th:href="@{../css/cultivation/reset.css}">
    <link rel="stylesheet" th:href="@{../css/cultivation/classStarts.css}">
    <script th:src="@{../plugin/layui/layui.js}"></script>
</head>
<body>
<div class="main">
    <div class="m-top">
        <div class="title">开课</div>
    </div>
    <div class="item">

        <div class="i-con">
            <div class="course-inform">
                <div class="i-top">
                    <span>课程信息</span>
                </div>
                <ul>
                    <li>
                        <div class="name">开课学期：</div>
                        <div class="tit" th:text="${form?.kskk_xnxq}"></div>
                    </li>
                    <li>
                        <div class="name">开课院系：</div>
                        <div class="tit" th:text="${form?.kskk_kkxb}"></div>
                    </li>
                    <li>
                        <div class="name">所属专业：</div>
                        <div class="tit" th:text="${form?.kskk_sszy}"></div>
                    </li>
                    <li>
                        <div class="name">培养层次：</div>
                        <div class="tit" th:text="${form?.kskk_pycc}"></div>
                    </li>
                    <li>
                        <div class="name">课程名称：</div>
                        <div class="tit" th:text="${form?.kskk_kcmc}"></div>
                    </li>
                    <li>
                        <div class="name">课程性质：</div>
                        <div class="tit" th:text="${form?.kskk_kcxz}"></div>
                    </li>
                    <li>
                        <div class="name">是否为纯毕业实践课：</div>
                        <div class="tit" th:text="${form?.kskk_sfcsjhj}"></div>
                    </li>
                    <li>
                        <div class="name">选必修：</div>
                        <div class="tit" th:text="${form?.kskk_xbx}"></div>
                    </li>
                    <li>
                        <div class="name">总学分：</div>
                        <div class="tit" th:text="${form?.kskk_xf}?${form.kskk_xf}:0"></div>
                    </li>
                    <li>
                        <div class="name">总学时：</div>
                        <div class="tit" th:text="${form?.kskk_zxs}?${form.kskk_zxs}:0"></div>
                    </li>
                    <li>
                        <div class="name">理论学时：</div>
                        <div class="tit" th:text="${form?.kskk_llxs}?${form.kskk_llxs}:0"></div>
                    </li>
                    <li>
                        <div class="name">实践学时：</div>
                        <div class="tit" th:text="${form?.kskk_sjxs}?${form.kskk_sjxs}:0"></div>
                    </li>
                    <li>
                        <div class="name">上机学时：</div>
                        <div class="tit" th:text="${form?.kskk_sjxss}?${form.kskk_sjxss}:0"></div>
                    </li>
                    <li>
                        <div class="name">实验学时：</div>
                        <div class="tit" th:text="${form?.kskk_syxs}?${form.kskk_syxs}:0"></div>
                    </li>
                    <li>
                        <div class="name">其他学时：</div>
                        <div class="tit" th:text="${form?.kskk_qtxs}?${form.kskk_qtxs}:0"></div>
                    </li>
                    <li>
                        <div class="name">计划人数：</div>
                        <div class="tit" th:text="${form?.kskk_bjrs}?${form.kskk_bjrs}:0"></div>
                    </li>
                    <li>
                        <div class="name">未安排人数：</div>
                        <div class="tit" th:text="${form?.kskk_waprs}?${form.kskk_waprs}:0"></div>
                    </li>
                </ul>
            </div>
            <div class="class-box">
                <div class="i-top">
                    <span>开课设置</span>
                </div>
                <div class="set-con">
                    <form class="layui-form" action="">
                        <input type="hidden" name="courseId" th:value="${form?.kskk_kcid}">
                        <input type="hidden" name="courseName" th:value="${form?.kskk_kcmc}">
                        <input type="hidden" name="formUserId" th:value="${formUserId}">
                        <input type="hidden" name="distributionNum" th:value="${form?.kskk_bjrs}">
                        <input type="hidden" name="semester" th:value="${form?.kskk_xnxq}">
                        <input type="hidden" name="totalClassHour" th:value="${form?.kskk_zxs}">
                        <input type="hidden" name="classForm" th:value="${form?.kskk_xzb}">
                        <input type="hidden" name="classFormCode" th:value="${form?.kskk_xzbbh}">
                        <input type="hidden" name="year" th:value="${form?.kskk_nj}">
                        <input type="hidden" name="dept" th:value="${form?.kskk_kkxb}">
                        <input type="hidden" name="fid" th:value="${fid}">
                        <div class="layui-form-item" style="margin-bottom: 10px;">
                            <div class="layui-inline">
                                <label class="layui-form-label">教学班编号</label>
                                <div class="layui-input-inline">
                                    <p class="txt" name="teachClassNo" th:text="${teachClassNo}"></p>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">已分配学时</label>
                                <div class="layui-input-inline">
                                    <p class="txt" id="allocatedHours_p"
                                       th:text="${allocatedHours}?${allocatedHours}:0"></p>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">教学班名称</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="teachClassName" lay-verify="teachClassName"
                                           autocomplete="off"
                                           placeholder="请输入教学班名称" class="layui-input"
                                           th:value="${teachClassName}">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">授课方式</label>
                                <div class="layui-input-inline">
                                    <select name="teachMethod" lay-filter="teachMethod" style="width: 240px;">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">开课校区</label>
                                <div class="layui-input-inline">
                                    <select name="campusName" lay-filter="campusName" style="width: 240px;">
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">是否排课</label>
                                <div class="layui-input-inline">
                                    <select name="sfpk" lay-filter="sfpk">
                                        <option value="">请选择是否排课</option>
                                        <option value="是" selected>是</option>
                                        <option value="否">否</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">是否排考</label>
                                <div class="layui-input-inline">
                                    <select name="sfExam" lay-filter="sfExam" style="width: 240px;">
                                        <option value="">请选择是否排考</option>
                                        <option value="是" selected>是</option>
                                        <option value="否">否</option>

                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">考试形式</label>
                                <div class="layui-input-inline">
                                    <select name="examForm" lay-filter="examForm">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">考试方式</label>
                                <div class="layui-input-inline">
                                    <select name="examMethod" lay-filter="examMethod" style="width: 240px;">
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">教学模式</label>
                                <div class="layui-input-inline">
                                    <select name="teachModel" lay-filter="teachModel">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item" style="display: none;">
                            <div class="layui-input-block">
                                <button type="submit" class="layui-btn btnSubmitHide" lay-submit=""
                                        lay-filter="setSubmit">提交
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="item item-teacher">
        <div class="i-top">
            <span>指定任课教师</span>
        </div>
        <div class="i-search">
            <form class="layui-form" action="">
                <div class="layui-inline">
                    <label class="layui-form-label">教师姓名</label>
                    <div class="layui-input-inline">
                        <input type="text" name="teacherName" lay-verify="teacherName" autocomplete="off"
                               placeholder="请输入教师姓名" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">助教</label>
                    <div class="layui-input-inline">
                        <input type="text" name="teachAsistant" lay-verify="assistant" autocomplete="off"
                               placeholder="请输入助教姓名" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <button type="submit" class="layui-btn btnSubmit" lay-submit=""
                            lay-filter="searchSubmit">搜索
                    </button>
                </div>
            </form>
            <div class="i-add"><span>+</span>添加教师</div>
        </div>
        <div class="i-table">
            <table class="layui-table" id="teacherTable" lay-filter="teacherTable">
            </table>
        </div>
        <div class="i-top">
            <span>已生成教学班</span>
        </div>
        <div class="i-table">
            <table class="layui-table" id="classTable" lay-filter="classTable">
            </table>
        </div>
    </div>
    <div class="itemBtn">提交</div>
</div>
</body>

<!-- 周次 -->
<script type="text/html" id="tmplWeek">
    <div class="tmplOpt">
        <input type="text" class="layui-input weekOpt" value="{{d.scheduleWeek ? d.scheduleWeek : ''}}">
        <span class="set week">设置</span>
    </div>
</script>
<!-- 节次 -->
<script type="text/html" id="tmplSection">
    <div class="tmplOpt">
        <input type="text" class="layui-input" value="{{d.lpSection ? d.lpSection : 2}}">
    </div>
</script>
<!-- 指定教室 -->
<script type="text/html" id="tmplClassroom">
    <div class="tmplOpt">
        <h5 class="classroom">{{d.appointClassRoom ? d.appointClassRoom : ""}}</h5>
        <span class="set classroom">选择</span>
    </div>
</script>
<!-- 助教 -->
<script type="text/html" id="tmplAssistant">
    <div class="tmplOpt">
        <h5 class="asistant">{{d.teachAsistant ? d.teachAsistant : ""}}</h5>
        <span class="set assistant">选择</span>
    </div>
</script>
<!-- 操作 -->
<script type="text/html" id="tmplOpt">
    <div class="edit" lay-event="edit">编辑</div>
    <div class="delet" lay-event="del">删除</div>
</script>
<!-- 班级操作 -->
<script type="text/html" id="tmplClassOpt">
    <div class="delet" lay-event="del">删除</div>
</script>
<!-- 周次 -->
<script type="text/html" id="tmplScoreEntry">
    {{#  if(d.scoreEntry ==1){ }}
    <span>是</span>
    {{#  } else { }}
    <span>否</span>
    {{#  } }}
</script>
<script th:src="@{../js/jquery-3.3.1.min.js}"></script>
<script th:inline="javascript">
    document.domain = "chaoxing.com";
    let formUserId = [[${formUserId}]];
    let fid = [[${fid}]];
    let detailId = [[${detailId}]];
    let appName = [[${appName}]];
    let allocatedHours_p = 0;
    layui.use(['jquery', "form", "table"], function () {
        var $ = layui.jquery;
        var form = layui.form;
        var table = layui.table;
        // 点击搜索
        form.on('submit(searchSubmit)', function (data) {
            table.reload('teacherTable', {
                page: {
                    curr: 1 //重新从第 1 页开始
                },
                where: data.field //传给后台数据并重载
            });
            return false;
        })
        getData("campusName", "../processData/getDictData?dictType=XQ");
        getData("examForm", "../processData/getDictData?dictType=KSXS");
        getData("examMethod", "../processData/getDictData?dictType=KSFS");
        getData("teachModel", "../processData/getDictData?dictType=JXMS");
        getData("teachMethod", "../processData/getDictData?dictType=SKFS");
        // table
        table = $.extend(table, {
            config: {
                checkName: 'sfScoreTeacher'
            }
        });
        table.render({
            elem: '#teacherTable',
            url: '../processData/getClassStartsTeacherData',
            // height: 'full-260',
            cols: [
                [{
                    field: 'teacherNo',
                    title: '教师编号',
                    align: 'left',
                    minWidth: 225
                }, {
                    field: 'teacherName',
                    title: '教师姓名',
                    align: 'left',
                    minWidth: 148
                }, {
                    type: 'radio',
                    title: '成绩录入',
                    width: 104,
                }, {
                    field: 'scheduleWeek',
                    title: '安排周次',
                    align: 'left',
                    minWidth: 317,
                    templet: '#tmplWeek'
                }, {
                    field: 'lpSection',
                    title: '连排节次',
                    align: 'left',
                    minWidth: 252,
                    templet: '#tmplSection'
                }, {
                    field: 'appointClassRoom',
                    title: '指定教室',
                    align: 'left',
                    minWidth: 232,
                    templet: '#tmplClassroom'
                }, {
                    field: 'teachAsistant',
                    title: '助教',
                    align: 'left',
                    minWidth: 176,
                    templet: '#tmplAssistant'
                }, {
                    field: 'id',
                    title: 'id',
                    hide: true
                }, {
                    title: '操作',
                    width: 164,
                    align: 'left',
                    fixed: 'right',
                    toolbar: '#tmplOpt'
                }]
            ],
            where: {detailId: detailId},
            limit: 10,
            page: false,
            done: function () {
                $("#teacherTable").next().find(".layui-table-main tbody tr").each(function () {
                    const week = $(this).find("td").eq(3).find(".weekOpt").val();
                    if (week) {
                        let weekArray = week.split(",");
                        for (let i = 0; i < weekArray.length; i++) {
                            let wk = weekArray[i];
                            let num = wk.split(":")[1];
                            allocatedHours_p = allocatedHours_p + parseInt(num);
                        }
                    }
                    $("#allocatedHours_p").text(allocatedHours_p);
                })
            }
        });
        //监听工具条 （编辑|删除）
        table.on('tool(teacherTable)', function (obj) {
            var data = obj.data;
            if (obj.event === 'del') {
                layer.confirm('确定删除任课教师吗？', function (index) {
                    $.post("../processData/delClassStartsTeacher", {id: obj.data.id}, function (result) {
                        obj.del();
                    }, "json");
                    layer.close(index);
                });
            } else if (obj.event === 'edit') {
                // JSON.stringify(data)
                openTeacherPop("", obj);
            }
        });

        //教学班

        let classTableArray = [{
            field: 'teachClassNo',
            title: '教学班编号',
            align: 'center',
            minWidth: 100
        }, {
            field: 'teachClassName',
            title: '教学班名称',
            align: 'center',
            minWidth: 300
        }, {
            field: 'teacher',
            title: '任课教师',
            align: 'center',
            minWidth: 104,
        }, {
            field: 'classForm',
            title: '行政班组成',
            align: 'center',
            minWidth: 417,
        }, {
            field: 'distributionNum',
            title: '已分配人数',
            align: 'center',
            minWidth: 120,
        }, {
            title: '操作',
            width: 164,
            align: 'center',
            fixed: 'right',
            toolbar: '#tmplClassOpt'
        }];
        if (appName == 555291) {
            classTableArray.splice(3, 1);
        }
        // table
        table.render({
            elem: '#classTable',
            url: '../processData/getClassStartsData',
            where: {formUserId: formUserId},
            // height: 'full-260',
            cols: [
                classTableArray
            ],
            limit: 10,
            page: false
        });
        //监听工具条 （编辑|删除）
        table.on('tool(classTable)', function (obj) {
            var data = obj.data;
            if (obj.event === 'del') {
                layer.confirm('确定删除教学班吗？', function (index) {
                    $.post("../processData/delClassStarts", {
                        id: obj.data.id,
                        teachPlanNo: [[${form?.kskk_jxjhbh}]]
                    }, function (result) {
                        updFastClassStartsForm("否", obj.data.id);
                        location.reload();
                    }, "json");
                    layer.close(index);
                });
            }
        });

        //提交
        $(".itemBtn").click(function () {
            if (addTeacher()) {
                form.on('submit(setSubmit)', function (data) {
                    data.field.id = [[${detailId}]];
                    data.field.teacher = JSON.stringify(tData);
                    $.post("../processData/addClassStarts", data.field, function (result) {
                        layer.msg("开课成功", {icon: 1, time: 2000});
                        updFastClassStartsForm("是");
                        for (let i = 0; i < result.ids.length; i++) {
                            let id = result.ids[i];
                            addCourseInformation(id);
                        }
                        addTeachClass(result.ids[0]);
                        location.reload();
                    }, "json");
                    return false;
                })
                $('.btnSubmitHide').trigger('click');
            }
        })
    });

    function updFastClassStartsForm(flag, id) {
        $.post("../processData/updFastClassStartsForm", {
            fid: fid,
            id: id,
            kskkJxjhbh: [[${form?.kskk_jxjhbh}]],
            classNo: [[${form?.kskk_xzbbh}]],
            teachers: teacherArray.length > 0 ? teacherArray.join(",") : "",
            appName: appName,
            kskkWaprs: 0,
            flag: flag
        }, function (result) {
        }, "json");
        $.post("../processData/updCourseInfoForm", {
            fid: fid,
            zxkkkJxjhbh: [[${form?.kskk_jxjhbh}]],
            zxkkkBjapzt: '已安排',
            zxkkkWaprs: 0,
            zxkkkJsapzt: '是'
        }, function (result) {
        }, "json");
    }

    function addTeachClass(detailId) {
        let tData = [{
            code: [[${form?.kskk_xzbbh}]],
            name: [[${form?.kskk_xzb}]],
            year: [[${form?.kskk_nj}]],
            dept: [[${form?.kskk_kkxb}]],
            major: [[${form?.kskk_sszy}]],
            classNum: [[${form?.kskk_bjrs}]],
            distributeNum: [[${form?.kskk_bjrs}]]
        }];
        $.post("../processData/addTeachClass",
            {
                formUserId: formUserId,
                detailId: detailId,
                tData: JSON.stringify(tData),
                teachPlanNo: [[${form?.kskk_jxjhbh}]]
            },
            function (result) {
            }, "json");
    }

    //添加教师弹窗
    $(".i-add").click(function () {
        openTeacherPop();
    })

    //安排周次弹窗
    let totalHours = 0;
    $(".i-table").on("click", ".week", function () {
        let _this = $(this);
        let semester = [[${form?.kskk_xnxq}]];
        let totalClassHour = [[${form?.kskk_zxs}]];
        layer.open({
            type: 2,
            area: ["100%", "350px"],
            title: "安排周次",
            maxmin: false, //开启最大化最小化按钮
            content: "../process/weeksPop?semester=" + semester + "&week=" + _this.prev().val() + "&totalClassHour=" + totalClassHour,
            btn: ['确定', '关闭'],
            skin: 'teacher_layer',
            yes: function (index, layero) {
                // 提交 获取一个 按钮 进行 提交测试
                let iframeWin = layero.find('iframe')[0];
                let detail = iframeWin.contentWindow.getdata();// 调用子页面的函数
                if (!detail) {
                    layer.msg("请先设置周学时", {icon: 2, time: 2000});
                    return false;
                }
                totalHours = totalHours + detail.total;
                $("#allocatedHours_p").text(totalHours);
                _this.prev().val(detail.cstr);
                _this.prev().attr("total", detail.total);
                layer.close(index);
            },
            cancel: function () {
                layer.closeAll();
            },
            end: function () {
                layer.closeAll();
            }
        });
    })

    //指定教室弹窗
    $(".i-table").on("click", ".classroom", function () {
        let _this = $(this);
        layer.open({
            type: 2,
            area: ["840px", "600px"],
            title: "选择教室",
            maxmin: false, //开启最大化最小化按钮
            content: "../process/classRoomPop",
            btn: ['确定', '关闭'],
            skin: 'teacher_layer',
            yes: function (index, layero) {
                // 提交 获取一个 按钮 进行 提交测试
                var iframeWin = layero.find('iframe')[0];
                var rowData = iframeWin.contentWindow.getSelectData();// 调用子页面的函数
                if (0 == rowData) {
                    layer.alert("请选择教室。");
                } else {
                    _this.prev().text(rowData.name);
                    layer.close(index);
                }
            },
            cancel: function () {
                layer.closeAll();
            },
            end: function () {
                layer.closeAll();
            }
        });
    })

    //提交
    let tData = [];
    let teacherArray = [];

    function addTeacher() {
        let flag = true;
        $("#teacherTable").next().find(".layui-table-main tbody tr").each(function () {
            const teacherNo = $(this).find("td").eq(0).find("div").text();
            const teacherName = $(this).find("td").eq(1).find("div").text();
            const sfScoreTeacher = $(this).find("td").eq(2).find(".layui-form-radioed").length > 0;
            const scheduleWeek = $(this).find("td").eq(3).find(".weekOpt").val();
            const allocatedHours = $(this).find("td").eq(3).find(".weekOpt").attr("total");
            const lpSection = $(this).find("td").eq(4).find(".layui-input").val();
            const appointClassRoom = $(this).find("td").eq(5).find("h5").text();
            const teachAsistant = $(this).find("td").eq(6).find("h5").text();
            const teachAsistantNo = $(this).find("td").eq(6).find("h5").attr("no");
            const id = $(this).find("td").eq(7).find("div").text();
            if (!/[0-9]+[-]{1}[0-9]+[:]{1}[0-9]+/.test(scheduleWeek)) {
                layer.msg("安排周次输入格式有误,请按照1-1:4,2-20:2的格式输入", {icon: 2, time: 2000});
                flag = false;
                return false;
            }
            teacherArray.push(teacherName);
            tData.push({
                id: id,
                teacherNo: teacherNo,
                teacherName: teacherName,
                sfScoreTeacher: sfScoreTeacher,
                scheduleWeek: scheduleWeek,
                lpSection: lpSection,
                appointClassRoom: appointClassRoom,
                teachAsistant: teachAsistant,
                teachAsistantNo: teachAsistantNo,
                allocatedHours: allocatedHours
            })
        })
        if ($("#teacherTable").next().find(".layui-table-main tbody tr").length == 0) {
            layer.msg("请添加教师", {icon: 2, time: 2000});
            return false;
        }
        if (flag) {
            $('.itemBtn').attr('disabled', true);
        }
        return flag;
    }

    //助教弹窗
    $(".i-table").on("click", ".assistant", function () {
        let _this = $(this);
        openTeacherPop(_this);
    })

    function openTeacherPop(_this, obj) {
        layer.open({
            type: 2,
            area: ["840px", "600px"],
            title: "选择教师",
            maxmin: false, //开启最大化最小化按钮
            content: "../process/teacherPop",
            btn: ['确定', '关闭'],
            skin: 'teacher_layer',
            yes: function (index, layero) {
                // 提交 获取一个 按钮 进行 提交测试
                var iframeWin = layero.find('iframe')[0];
                var rowData = iframeWin.contentWindow.getTeachers();// 调用子页面的函数
                if (0 == rowData) {
                    layer.alert("请选择教师。");
                } else {
                    if (_this) {
                        let teacherName = [];
                        for (let i = 0; i < rowData.length; i++) {
                            teacherName.push(rowData[i].teacherName);
                        }
                        _this.prev().text(teacherName.join(","));
                    } else {
                        let tData = [];
                        $("#teacherTable").next().find(".layui-table-main tbody tr").each(function () {
                            const teacherNo = $(this).find("td").eq(0).find("div").text();
                            const teacherName = $(this).find("td").eq(1).find("div").text();
                            const sfScoreTeacher = $(this).find("td").eq(2).find(".layui-form-radioed").length > 0;
                            const scheduleWeek = $(this).find("td").eq(3).find(".weekOpt").val();
                            const allocatedHours = $(this).find("td").eq(3).find(".weekOpt").attr("total");
                            const lpSection = $(this).find("td").eq(4).find(".layui-input").val();
                            const appointClassRoom = $(this).find("td").eq(5).find("h5").text();
                            const teachAsistant = $(this).find("td").eq(6).find("h5").text();
                            const teachAsistantNo = $(this).find("td").eq(6).find("h5").attr("no");
                            const id = $(this).find("td").eq(7).find("div").text();
                            tData.push({
                                id: id,
                                teacherNo: teacherNo,
                                teacherName: teacherName,
                                sfScoreTeacher: sfScoreTeacher,
                                scheduleWeek: scheduleWeek,
                                lpSection: lpSection,
                                appointClassRoom: appointClassRoom,
                                teachAsistant: teachAsistant,
                                teachAsistantNo: teachAsistantNo,
                                allocatedHours: allocatedHours
                            })
                        })
                        if (tData.length === 0) {
                            rowData[0].sfScoreTeacher = true;
                        }
                        if (tData.length > 0 && obj) {
                            for (let i = 0; i < tData.length; i++) {
                                const t = tData[i];
                                if (t.teacherNo === obj.data.teacherNo) {
                                    t.teacherNo = rowData[0].teacherNo;
                                    t.teacherName = rowData[0].teacherName;
                                    t.sfScoreTeacher = rowData[0].sfScoreTeacher;
                                }
                            }
                        }
                        if (!obj) {
                            tData = $.merge(tData, rowData);
                        }
                        tData = unRepeat(tData, "teacherNo");
                        layui.table.reload('teacherTable', {data: tData, url: ''});
                    }
                    layer.close(index);
                }
            },
            cancel: function () {
                layer.closeAll();
            },
            end: function () {
                layer.closeAll();
            }
        });
    }


    function addCourseInformation(detailId) {
        $.post("../processData/addCourseInformation", {
            detailId: detailId,
            fid: fid,
            type: 1,
            appName: appName
        }, function (result) {
        }, "json");
    }

    function getData(selName, url) {
        $.post(url, function (result) {
            for (let i = 0; i < result.list.length; i++) {
                const option = result.list[i];
                const name = option.dictName;
                $("select[name=\"" + selName + "\"]").append(new Option(name, name));
            }
            if ([[${classStartsInfoDetail?.teachClassType}]]) {
                $("select[name='teachClassType']").val([[${classStartsInfoDetail?.teachClassType}]]);
            }
            if ([[${classStartsInfoDetail?.campusName}]]) {
                $("select[name='campusName']").val([[${classStartsInfoDetail?.campusName}]]);
            }
            if ([[${classStartsInfoDetail?.sfpk}]]) {
                $("select[name='sfpk']").val([[${classStartsInfoDetail?.sfpk}]]);
            }
            if ([[${classStartsInfoDetail?.sfExam}]]) {
                $("select[name='sfExam']").val([[${classStartsInfoDetail?.sfExam}]]);
            }
            if ([[${classStartsInfoDetail?.examForm}]]) {
                $("select[name='examForm']").val([[${classStartsInfoDetail?.examForm}]]);
            }
            if ([[${classStartsInfoDetail?.examMethod}]]) {
                $("select[name='examMethod']").val([[${classStartsInfoDetail?.examMethod}]]);
            }
            if ([[${classStartsInfoDetail?.teachModel}]]) {
                $("select[name='teachModel']").val([[${classStartsInfoDetail?.teachModel}]]);
            }
            if ([[${classStartsInfoDetail?.teachMethod}]]) {
                $("select[name='teachMethod']").val([[${classStartsInfoDetail?.teachMethod}]]);
            }


            layui.form.render('select');
        }, "json");
    }

    function unRepeat(arry, key) {
        const res = new Map();
        return arry.filter((arr) => !res.has(arr[key]) && res.set(arr[key], 1))
    }
</script>
</html>