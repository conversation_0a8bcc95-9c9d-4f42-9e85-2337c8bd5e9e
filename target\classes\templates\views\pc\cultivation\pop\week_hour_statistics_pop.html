<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>开始统计</title>
    <style>
        * {
            margin: 0;
            padding: 0;
        }

        .marsker {
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            position: fixed;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
        }

        .dialog {
            width: 560px;
            padding: 40px 0;
            box-sizing: border-box;
            background-color: #fff;
            border-radius: 10px;
            position: fixed;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
        }

        .dialog img {
            display: block;
            margin: 0 auto 24px;
            border-radius: 50%;
        }

        .dialog p {
            font-size: 16px;
            text-align: center;
            color: #1D2129;
        }

        .loading img {
            animation: loading 2.3s linear infinite;
        }

        .success img {
            width: 60px;
            height: 60px;
        }

        .btnSure {
            width: 88px;
            height: 36px;
            border-radius: 18px;
            background: #4D88FF;
            box-shadow: 0px 0px 10px 0px rgba(0, 108, 226, 0.40);
            text-align: center;
            line-height: 36px;
            font-size: 14px;
            color: #fff;
            margin: 22px auto 0;
            cursor: pointer;
        }

        @keyframes loading {
            0% {
                transform: rotate(0);
            }
            100% {
                transform: rotate(360deg);
            }
        }
    </style>
</head>
<body>
<div class="dialog loading" style="display: none;">
    <img src="/images/cultivation/loading.png" alt="">
    <p>数据更新中<br>更新时长受数据量影响，平均时长为3-5分钟</p>
    <p class="light">（关闭弹窗不影响更新进程）</p>
</div>
<div class="dialog success" style="display: none;">
    <img src="/images/cultivation/success-icon.png" alt="">
    <p>当前已是最新数据</p>
</div>
</body>
<script th:src="@{${_CPR_}+'/js/jquery-3.3.1.min.js'}"></script>
<script>
    $(document).ready(function () {
        formFlowStatus();
        window.parent.postMessage(JSON.stringify({"command": "urlPopWndClose", "type": "register"}), '*');
        $(window).on('message', function (e) {
            const message = e.originalEvent.data; // 获取 postMessage 传递的数据
            const data = JSON.parse(message);
            if (data.command === "urlPopWndClose" && data.appName === "officeApp") {
                window.parent.postMessage(JSON.stringify({action: 1}), "*");
            }
        });
    })

    function formFlowStatus(intervalId) {
        $.post("/cultivation/formFlowStatus", {
            deptId: "[[${formTopBtnBO.fid}]]",
            uid: "[[${operateUid}]]",
            flowId: "[[${flowId}]]",
            enc: "[[${enc}]]"
        }, function (res) {
            if (res.data.code !== 0) {
                $(".start,.success").hide();
                $(".loading").show();
                formFlowData();
                const intervalId = setInterval(function () {
                    formFlowStatus(intervalId);
                }, 30 * 1000);
                return false;
            }
            $(".loading,.start").hide();
            $(".success").show();
            clearInterval(intervalId);
        }, "json");
    }

    function formFlowData() {
        $.post("/cultivation/formFlowData", {
            deptId: "[[${formTopBtnBO.fid}]]",
            uid: "[[${operateUid}]]",
            flowId: "[[${flowId}]]",
            enc: "[[${enc}]]"
        }, function (res) {
        }, "json");
    }
</script>
</html>