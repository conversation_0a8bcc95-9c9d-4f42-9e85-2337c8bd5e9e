<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>培养方案</title>
    <link rel="stylesheet" th:href="@{../../plugin/layui/css/layui.css}">
    <link rel="stylesheet" th:href="@{../../plugin/layui/css/modules/formSelects-v4.css}">
    <link rel="stylesheet" th:href="@{/css/cultivation/dialog.css}">
    <style>
        .dialog .dialog-con {
            height: 285px;
        }

        .j-search-con .j-select-year ul {
            max-height: 130px;
        }
    </style>
    <script th:src="@{../../plugin/layui/layui.js}"></script>
    <script th:src="@{../../plugin/layui/lay/modules/formSelects-v4.js}"></script>
</head>
<body>
<div class="dialog" id="invigilateMax" style="width: 438px;">
    <div class="dialog-con">
        <div class="item">
            <div class="label">选择年级</div>
            <div class="j-search-con single-box">
                <input type="text" name="grade" placeholder="请选择" readonly="" class="schoolSel">
                <span class="j-arrow"></span>
                <div class="j-select-year ">
                    <div class="search">
                        <input type="text" placeholder="搜索">
                        <span></span>
                    </div>
                    <ul name="grade">
                    </ul>
                </div>
            </div>
        </div>
        <div class="item">
            <div class="label">选择专业</div>
            <div class="j-search-con multiple-box">
                <input type="text" name="major" placeholder="请选择" readonly="" class="schoolSel">
                <span class="j-arrow"></span>
                <div class="j-select-year ">
                    <div class="search">
                        <input type="text" placeholder="搜索">
                        <span></span>
                    </div>
                    <div class="all-selects">全选</div>
                    <ul name="major">
                    </ul>
                </div>
            </div>
        </div>
        <div class="item" th:if="${fid==133816}">
            <div class="label">选择班级</div>
            <div class="j-search-con multiple-box">
                <input type="text" name="clazz" placeholder="请选择" readonly="" class="schoolSel">
                <span class="j-arrow"></span>
                <div class="j-select-year ">
                    <div class="search">
                        <input type="text" placeholder="搜索">
                        <span></span>
                    </div>
                    <div class="all-selects">全选</div>
                    <ul name="clazz">
                    </ul>
                </div>
            </div>
        </div>

    </div>
    <div class="dialog-btn">
        <button class="pu-cancel">取消</button>
        <button class="pu-sure" id="invigilateSure" style="width: 88px;">确定</button>
    </div>
</div>
</body>
<script th:src="@{../../js/jquery-1.11.3.min.js}"></script>
<script th:src="@{../../js/cultivation/cultivationProcess.js(v=${new java.util.Date().getTime()})}"></script>
<script th:inline="javascript">
    document.domain = "chaoxing.com";
    var formUserId = [[${formUserId}]];
    var formId = [[${formId}]];
    var fid = [[${fid}]];
    var uid = [[${uid}]];
    var formSelects = "";
    layui.config({
        base: '../lay/modules/' //此处路径请自行处理, 可以使用绝对路径
    }).extend({
        formSelects: 'formSelects-v4'
    });
    layui.use(['form', 'layer'], function () {
        const layer = layui.layer;
        formSelects = layui.formSelects;
        //监听提交
        $(".pu-sure").click(function () {
            let loading = layer.load(1);
            var grade = $("input[name='grade']").val();
            var major = $("input[name='major']").val();
            let clazz = $("input[name='clazz']").attr("code");
            $.post("/api/pygc/trainingProgramCopy", {
                formUserId: formUserId,
                formId: formId,
                fid: fid,
                uid: uid,
                grade: grade,
                major: major,
                clazz: clazz
            }, function (result) {
                if (result.success) {
                    window.parent.postMessage(JSON.stringify({action: 1}), "*");
                } else {
                    layer.msg(result.message, {icon: 2, time: 3000});
                }
                layer.close(loading);
            }, "json");
        })

        // 下拉多选
        $(".j-search-con").on("click", ".schoolSel", function (e) {
            $(".j-select").hide();
            let parent = $(this).parent();
            parent.find('.j-arrow').toggleClass('j-arrow-slide');
            parent.find('.j-select-year').toggleClass('slideShow');
            var sibling = $(this).parents('.item').siblings();
            sibling.find('.j-arrow').removeClass('j-arrow-slide');
            sibling.find('.j-select-year').removeClass('slideShow');
            stopBubble(e)
        })
        // 全选/取消全选
        $(".j-search-con").on('click', '.j-select-year .all-selects', function (e) {
            $(this).toggleClass("active");
            if ($(this).hasClass("active")) {
                $(this).text('取消全选');
                let selCon = [];
                let curEles = $(this).parent().find("li");
                curEles.each((index, ele) => {
                    if ($(ele).is(':hidden')) {
                        return true;
                    }
                    $(ele).addClass("active");
                    let txt = $(ele).text();
                    selCon.push(txt);
                });
                $(this).parents('.j-search-con').find('.schoolSel').val(selCon.join(','))

            } else {
                $(this).next().find("li").removeClass("active");
                $(this).text('全选');
                $(this).parents('.j-search-con').find('.schoolSel').val('');
            }
            stopBubble(e)
        })
        // 选择-多选
        $(".j-search-con.multiple-box").on("click",
            ".j-select-year li ",
            function (e) {
                $(this).toggleClass("active");
                var parentEle = $(this).parent();
                var totallis = parentEle.find(".active").length;
                var curlis = parentEle.find("li:not(.active)").length;
                var prev = parentEle.prev(".all-selects")
                if (totallis == curlis) {
                    prev.addClass("active");
                    prev.text('取消全选')
                } else {
                    prev.removeClass("active");
                    prev.text('全选')
                }

                let selCon = [];
                let curEles = parentEle.find(".active");

                curEles.each((index, ele) => {
                    let txt = $(ele).text();
                    selCon.push(txt);
                });
                var schoolSelEle = $(this).parents('.j-search-con').find('.schoolSel')
                if (curEles.length > 0) {
                    schoolSelEle.val(selCon.join(','))
                } else {
                    schoolSelEle.val('');
                }
                stopBubble(e);
                getClassByMajorAndGrade($(this));
            })
        //  选择-单选
        $(".j-search-con.single-box").on("click",
            ".j-select-year li ",
            function (e) {
                $(this).addClass('active').siblings().removeClass();
                var parents = $(this).parents('.j-search-con');
                var schoolSelEle = parents.find('.schoolSel');
                var txt = $(this).text();
                schoolSelEle.val(txt);
                parents.find('.j-arrow').removeClass('j-arrow-slide');
                parents.find('.j-select-year').removeClass('slideShow')
                stopBubble(e);
                getClassByMajorAndGrade($(this));
            })
        // 搜索
        $(".j-search-con").on('click', '.search input', function (e) {
            stopBubble(e);
        })
        $(".j-search-con").on('input', '.search input', function (e) {
            let val = $(this).val();
            var nextEle = $(this).parents('.j-search-con').find('ul')
            if (val != "") {

                nextEle.find('li').each(function (i, ele) {
                    let txt = $(ele).text();
                    if (txt.indexOf(val) >= 0) {
                        $(ele).show();
                    } else {
                        $(ele).hide();
                    }
                })
            } else {
                nextEle.find('li').show();
            }
            stopBubble(e)
        })

        function stopBubble(e) {
            if (e && e.stopPropagation)
                e.stopPropagation();
            else {
                window.event.cancelBubble = true;
            }
        }

        $(document).on("click", function (event) {
            var _con = $('.select-input');
            if (!_con.is(event.target) && _con.has(event.target).length === 0) {
                $(".j-search-con").find('.j-arrow').removeClass('j-arrow-slide');
                $(".j-search-con").find('.j-select-year').removeClass('slideShow')
            }
        })
        $("input[name='major']").val([[${info.pyfagl_zy}]]);
    })
    $(".pu-cancel").click(function () {
        window.parent.postMessage(JSON.stringify({action: 1}), "*");
    })

    function getClassByMajorAndGrade(_this) {
        if (_this.parents(".item").find("input[name='clazz']").length > 0) {
            let code = [];
            $("ul[name='clazz'] .active").each(function () {
                code.push(`${$(this).attr('major')},`
                    + `${$(this).attr('className')},`
                    + `${$(this).attr('classCode')},`
                    + `${$(this).attr('grade')},`
                    + `${$(this).attr('num') || 0}`);
            })
            $("input[name='clazz']").attr("code", code.join("|"));
            return false;
        }
        let grade = $("input[name='grade']").val();
        let major = $("input[name='major']").val();
        $.post("/cultivation/getClassByMajorAndGrade", {
            fid: fid,
            grade: grade,
            major: major
        }, function (result) {
            if (!result.data) {
                return false;
            }
            let html = "";
            for (let i = 0; i < result.data.length; i++) {
                let item = result.data[i];
                html += "<li major='" + item.bjxx_zybh + "' className='" + item.bjxx_bjmc + "' classCode='" + item.bjxx_bjbh
                    + "' grade='" + item.bjxx_rxnf + "' num='" + item.bjxx_njrs + "'>" + item.bjxx_bjmc + "</li>";
            }
            $("ul[name='clazz']").html(html);
        }, "json");
    }
</script>
</html>