<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <title>教学楼</title>
    <link rel="stylesheet" th:href="@{../css/cultivation/jquery.searchableSelect.css}"/>
    <link rel="stylesheet" th:href="@{../css/cultivation/newStyle.css}"/>
    <link rel="stylesheet" th:href="@{../plugin/layui/css/layui.css}"/>
    <style type="text/css">
        html, body {
            height: 100%;
        }

        .layui-laypage .layui-laypage-curr .layui-laypage-em {
            background-color: #4C88FF !important;
        }

        .layui-form-radio > i:hover, .layui-form-radioed > i {
            color: #4C88FF !important;
        }
    </style>
</head>
<body>
<div class="popDiv popMove wid840">
    <div class="popBody" id="popScroll">
        <div class="popSearch clearAfter">
            <div class="popSearch_row fl">
                <div class="popSearch_name">
                    <div class="popSearch_table">
                        <div class="popSearch_cell"><span>校区</span></div>
                    </div>
                </div>
                <select name="kkxqdm" class="qselect">
                    <option value="">请选择</option>
                </select>
            </div>
            <div class="popSearch_row fl">
                <div class="popSearch_name">
                    <div class="popSearch_table">
                        <div class="popSearch_cell"><span>教学楼编号</span></div>
                    </div>
                </div>
                <div class="popSearch_per"><input type="text" placeholder="教学楼编号" name="jxlbh"
                                                  class="popSearch_input"></div>
            </div>
            <div class="popSearch_row fl">
                <div class="popSearch_name">
                    <div class="popSearch_table">
                        <div class="popSearch_cell"><span>教学楼名称</span></div>
                    </div>
                </div>
                <div class="popSearch_per"><input type="text" placeholder="教学楼名称" name="jxlmc"
                                                  class="popSearch_input"></div>
            </div>
        </div>
        <div class="popSearch_btns">
            <div class="popSearch_clear fr">清空筛选</div>
            <div class="popSearch_search_btn fr" lay-filter="search" lay-submit>筛选</div>
        </div>
        <div class="popSearch_cont">
            <table lay-filter="jxlTable" class="layui-table" id="jxlTable">
            </table>
        </div>
    </div>
</div>
<script th:src="@{../js/jquery-3.3.1.min.js}"></script>
<script th:src="@{../js/jquery.nicescroll.min.js}"></script>
<script th:src="@{../js/cultivation/jquery.searchableSelect.js}"></script>
<script th:src="@{../js/cultivation/select_data.js}"></script>
<script th:src="@{../plugin/layui/layui.js}"></script>
<script type="text/javascript">
    //弹窗居中
    function MoveFixed(userClass) {
        $(userClass).css({
            top: function () {
                return 0;
            }, left: function () {
                return ($(window).width() - $(this).width()) / 2;
            }
        });

    }

    window.onresize = function () {
        MoveFixed('.popMove')
    }
    MoveFixed('.popMove')

    function scrollBox(userID) {
        $(userID).niceScroll({
            cursorborder: "",
            cursorwidth: 8,
            cursorcolor: "#CFD8E6",
            boxzoom: false,
            autohidemode: true
        });
        $(userID).getNiceScroll().resize(); //检测滚动条是否重置大小（当窗口改变大小时）
    }

    scrollBox("#popScroll");
    document.domain = document.domain.split('.').slice(-2).join('.');
    $('.qselect').click();

    function getSelectData() {
        var checkRows = table.checkStatus('jxlTable').data;
        if (checkRows.length == 0) {
            return 0;
        }
        var rowData = checkRows[0];
        return rowData;
    }

    $(".popSearch_search_btn").click(function () {
        var xqdm = $("select[name='xqdm']").val();
        var jxlbh = $("input[name='jxlbh']").val();
        var jxlmc = $("input[name='jxlmc']").val();
        var field = {xqdm: xqdm, jxlbh: jxlbh, jxlmc: jxlmc};
        insTb.reload({where: field, page: {curr: 1}});
    })

    $(".popSearch_clear").click(function () {
        location.reload();
    })

    var table = "", insTb = "";
    layui.use('table', function () {
        table = layui.table;
        insTb = table.render({
            elem: '#jxlTable',
            url: '../processData/getTeachBuildingData',
            page: true,
            cols: [
                [
                    {type: 'radio', field: 'id', fixed: 'left'},
                    {field: 'code', title: '教学楼编号'},
                    {field: 'name', title: '教学楼名称'},
                    {field: 'campusName', title: '校区名称'},
                    {
                        field: 'status', title: '可用状态', templet: function (d) {
                            var kyzt = d.kyzt == 0 ? "否" : "是";
                            return kyzt;
                        }
                    }
                ]
            ],
            done: function () {
                scrollBox("#popScroll");
            }
        });

        table.on('row(jxlTable)', function (obj) {
            obj.tr.addClass('tr_bj_color').siblings().removeClass('tr_bj_color');
        });
    });

</script>
</body>
</html>