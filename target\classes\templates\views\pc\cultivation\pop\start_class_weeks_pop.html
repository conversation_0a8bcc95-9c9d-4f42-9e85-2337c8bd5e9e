<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>老师学时配置</title>
    <link rel="stylesheet" th:href="@{/css/cultivation/newStyle.css}"/>
    <link rel="stylesheet" th:href="@{/plugin/layui/css/layui.css}"/>
    <style>
        .multi-selected {
            background-color: #4C88FF !important;
        }

        .multi-cur {
            background-color: #4C88FF !important;
        }

        .th-cur {
            background-color: #4C88FF !important;
        }

        /* 表格拖动选择优化 */
        .popWeekly_table th {
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            transition: all 0.2s ease;
            position: relative;
        }

        /* 禁用总学时和已分配学时的点击 */
        .popWeekly_table th:nth-child(1),
        .popWeekly_table th:nth-child(2) {
            pointer-events: none;
            cursor: default;
            opacity: 0.7;
        }

        /* 可操作的周次列悬停效果 */
        .popWeekly_table th:nth-child(n+3):hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(76, 136, 255, 0.2);
            cursor: pointer;
        }

        /* 拖动模式指示器 */
        .drag-mode-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            z-index: 10000;
            pointer-events: none;
            transform: translateY(-50px);
            opacity: 0;
            transition: all 0.3s ease;
        }

        .drag-mode-indicator.show {
            transform: translateY(0);
            opacity: 1;
        }

        .drag-mode-indicator.select-mode {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
        }

        .drag-mode-indicator.deselect-mode {
            background: linear-gradient(135deg, #ff6b6b, #ff5252);
            color: white;
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
        }

        /* 操作计数器 */
        .operation-counter {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 10px 16px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            border-radius: 8px;
            font-size: 12px;
            z-index: 10000;
            transform: translateY(50px);
            opacity: 0;
            transition: all 0.3s ease;
        }

        .operation-counter.show {
            transform: translateY(0);
            opacity: 1;
        }

        /* 智能操作提示样式 */
        .smart-tips {
            margin: 15px 0;
            padding: 16px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 8px;
            border-left: 4px solid #4C88FF;
        }

        .tips-row {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
            font-size: 13px;
            color: #495057;
        }

        .tips-row:last-child {
            margin-bottom: 0;
        }

        .tips-icon {
            font-size: 16px;
            width: 20px;
            text-align: center;
            flex-shrink: 0;
        }

        .tips-text strong {
            color: #4C88FF;
            font-weight: 600;
        }

        /* 实时状态栏样式 */
        .status-bar {
            margin: 15px 0;
            padding: 12px 16px;
            background: linear-gradient(135deg, #fff3e0 0%, #fce4ec 100%);
            border: 1px solid #ffcc02;
            border-radius: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 13px;
        }

        .status-label {
            color: #6c757d;
            font-weight: 500;
        }

        .status-value {
            color: #495057;
            font-weight: 700;
            font-size: 14px;
            min-width: 20px;
            text-align: center;
            padding: 2px 6px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 4px;
        }

        #remainingHours {
            color: #28a745;
        }

        #remainingHours.warning {
            color: #ffc107;
        }

        #remainingHours.danger {
            color: #dc3545;
        }

        @media (max-width: 768px) {
            .smart-tips {
                padding: 12px;
            }

            .tips-row {
                font-size: 12px;
                margin-bottom: 6px;
            }

            .status-bar {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .status-item {
                font-size: 12px;
            }
        }

        /* 操作按钮区域样式优化 - 自适应宽度 */
        .popWeekly_tab {
            margin-bottom: 20px;
            padding: 15px;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 12px;
            border: 1px solid #e1e2e5;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            position: relative;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            align-items: center;
        }

        .popWeekly_tab::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #4C88FF, #6c5ce7, #a29bfe);
            border-radius: 12px 12px 0 0;
        }

        /* 重写按钮样式 - 覆盖原始CSS的固定宽度 */
        .popWeekly_tab li {
            width: auto !important; /* 覆盖原始的 width:80px */
            min-width: 80px; /* 设置最小宽度保证美观 */
            height: 36px;
            line-height: 36px;
            padding: 0 16px; /* 增加左右内边距 */
            background: #ffffff;
            font-size: 14px;
            color: #4C88FF;
            border: 1px solid #4C88FF;
            border-radius: 8px;
            float: none; /* 移除浮动 */
            text-align: center;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 2px 4px rgba(76, 136, 255, 0.1);
            position: relative;
            overflow: hidden;
            white-space: nowrap; /* 防止文字换行 */
            flex-shrink: 0; /* 防止按钮被压缩 */
        }

        /* 移除原始的margin-left，使用gap代替 */
        .popWeekly_tab li + li {
            margin-left: 0 !important;
        }

        .popWeekly_tab li::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .popWeekly_tab li:hover::before {
            left: 100%;
        }

        .popWeekly_tab li:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(76, 136, 255, 0.25);
            border-color: #6c5ce7;
        }

        .popWeekly_tab li.active {
            background: linear-gradient(135deg, #4C88FF, #6c5ce7) !important;
            color: #ffffff !important;
            box-shadow: 0 4px 12px rgba(76, 136, 255, 0.4);
            transform: translateY(-1px);
        }

        /* 学时信息显示样式 */
        .popWeekly_table_header {
            margin-bottom: 20px;
            padding: 18px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e1e2e5;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .hour-info-container {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .hour-info-row {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            align-items: center;
        }

        .hour-info-item {
            display: flex;
            align-items: center;
            min-width: 120px;
        }

        .hour-info-item.current-hour,
        .hour-info-item.current-total {
            min-width: 150px;
            font-weight: 600;
        }

        .hour-label {
            color: #495057;
            font-size: 13px;
            margin-right: 5px;
        }

        .hour-value {
            color: #007bff;
            font-weight: 500;
            font-size: 14px;
        }

        .current-hour .hour-value {
            color: #28a745;
            font-weight: 600;
        }

        .current-total .hour-value {
            color: #dc3545;
            font-weight: 600;
            font-size: 15px;
        }

        /* 高亮当前学时类型 */
        .hour-info-item.highlight-current {
            background-color: #e3f2fd;
            padding: 6px 10px;
            border-radius: 4px;
            border: 1px solid #2196f3;
        }

        .hour-info-item.highlight-current .hour-label {
            color: #1976d2;
            font-weight: 600;
        }

        .hour-info-item.highlight-current .hour-value {
            color: #0d47a1;
            font-weight: 700;
        }

        /* 响应式设计 - 按钮自适应优化 */
        @media (max-width: 1200px) {
            .popWeekly_tab {
                gap: 8px;
            }

            .popWeekly_tab li {
                min-width: 70px;
                padding: 0 12px;
                font-size: 13px;
            }
        }

        @media (max-width: 992px) {
            .popWeekly_tab {
                gap: 6px;
                padding: 12px;
            }

            .popWeekly_tab li {
                min-width: 60px;
                padding: 0 10px;
                font-size: 12px;
                height: 32px;
                line-height: 32px;
            }
        }

        @media (max-width: 768px) {
            .popWeekly_tab {
                gap: 8px;
                padding: 15px;
                justify-content: center;
            }

            .popWeekly_tab li {
                flex: 1;
                min-width: 0;
                max-width: 120px;
                padding: 0 8px;
                font-size: 11px;
                height: 34px;
                line-height: 34px;
            }

            .hour-info-row {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .hour-info-item {
                min-width: auto;
                width: 100%;
            }
        }

        @media (max-width: 576px) {
            .popWeekly_tab {
                gap: 6px;
                padding: 12px;
            }

            .popWeekly_tab li {
                flex: 1;
                min-width: 0;
                padding: 0 6px;
                font-size: 10px;
                height: 30px;
                line-height: 30px;
                border-radius: 6px;
            }

            .popWeekly {
                padding: 15px;
            }
        }

        @media (max-width: 480px) {
            .popWeekly_tab {
                flex-direction: column;
                gap: 8px;
                align-items: stretch;
            }

            .popWeekly_tab li {
                flex: none;
                width: 100%;
                max-width: none;
                min-width: 0;
                padding: 0 12px;
                font-size: 12px;
                height: 36px;
                line-height: 36px;
            }
        }
    </style>
</head>
<body class="gray-bg">
<div class="popDiv popMove wid100b">
    <div class="popBody">
        <div class="popWeekly">
            <!-- 操作按钮区域 -->
            <ul class="popWeekly_tab">
                <li class="active" onclick="selectOddWeeks()" title="选择单周">单周</li>
                <li class="active" onclick="selectEvenWeeks()" title="选择双周">双周</li>
                <li class="active" onclick="selectAllWeeks()" title="选择所有周次">全选</li>
                <li class="active" onclick="clearAllSelection()" title="清除所有选择">取消选择</li>
                <li class="active" onclick="setOddEvenHours()" title="分别设置单双周学时">单双周赋值</li>
                <li class="active" onclick="averageDistributeHours()" title="平均分配总学时到选中周次">平均分配学时</li>
                <li class="active" onclick="setUniformHours()" title="为选中周次设置相同学时">一键设置学时</li>
            </ul>
            <div class="popWeekly_table_header">
                <div class="hour-info-container">
                    <div class="hour-info-row">
                        <div class="hour-info-item">
                            <span class="hour-label">总学时：</span>
                            <span class="hour-value" id="totalHours">0</span>
                        </div>
                        <div class="hour-info-item">
                            <span class="hour-label">理论学时：</span>
                            <span class="hour-value" id="theoryHours">0</span>
                        </div>
                        <div class="hour-info-item">
                            <span class="hour-label">实践学时：</span>
                            <span class="hour-value" id="practiceHours">0</span>
                        </div>
                        <div class="hour-info-item">
                            <span class="hour-label">上机学时：</span>
                            <span class="hour-value" id="computerHours">0</span>
                        </div>
                        <div class="hour-info-item">
                            <span class="hour-label">实验学时：</span>
                            <span class="hour-value" id="experimentHours">0</span>
                        </div>
                        <div class="hour-info-item">
                            <span class="hour-label">其他学时：</span>
                            <span class="hour-value" id="otherHours">0</span>
                        </div>
                        <div class="hour-info-item">
                            <span class="hour-label">周学时：</span>
                            <span class="hour-value" th:text="${tpf?.jxjhgl_mzxs}"></span>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 学时配置表格 -->
            <div class="popWeekly_table">
                <table>
                    <tbody id="hoursTable">
                    <!-- 表格内容将通过JavaScript动态生成 -->
                    </tbody>
                </table>
            </div>

            <!-- 隐藏的数据存储 -->
            <input type="hidden" id="datajk" name="datajk" value="">
        </div>
    </div>
</div>

<!-- 拖动模式指示器 -->
<div class="drag-mode-indicator" id="dragModeIndicator">
    <span id="dragModeText">选择模式</span>
</div>

<!-- 操作计数器 -->
<div class="operation-counter" id="operationCounter">
    <span id="operationText">已选择 0 个周次</span>
</div>

<script th:src="@{/js/jquery-3.3.1.min.js}"></script>
<script th:src="@{/plugin/layui/layui.js}"></script>
<script th:src="@{/js/selection.js}"></script>
<script th:inline="javascript">
    // 全局配置和状态管理
    const CONFIG = {
        tpf: [[${tpf}]],
        hourType: [[${hourType}]],
        semester: [[${semester}]],
        week: [[${week}]],
        hourTypeMap: {
            "理论学时": "jxjhgl_llxs",
            "实践学时": "jxjhgl_sjxs",
            "上机学时": "jxjhgl_sjxss",
            "实验学时": "jxjhgl_syxs",
            "其他学时": "jxjhgl_qtxs"
        },
        colors: {
            selected: "#4C88FF",
            default: "#f2f2f2"
        }
    };

    const STATE = {
        nowxs: CONFIG.tpf[CONFIG.hourTypeMap[CONFIG.hourType]] || 0,
        firstTime: 0,
        lastTime: 0,
        layer: null,
        zcArray: []
    };

    // 设置域名
    document.domain = document.domain.split('.').slice(-2).join('.');

    // 工具函数集合
    const Utils = {
        // 字符串处理
        trim: str => str.replace(/(^\s*)|(\s*$)/g, ""),

        // 获取随机颜色（固定为主题色）
        getRandomColor: () => CONFIG.colors.selected,

        // 构建表格HTML
        buildTableHTML: (weeks, totalHours) => {
            const headerCells = Array.from({length: weeks}, (_, i) =>
                `<th class='th'>${i + 1}</th>`
            ).join('');

            const inputCells = Array.from({length: weeks}, () =>
                `<td><input class="tdInput" name="jk" type="text" value="0"/></td>`
            ).join('');

            return `
                <tr>
                    <th>${CONFIG.hourType}</th>
                    <th>已分配学时</th>
                    ${headerCells}
                </tr>
                <tr>
                    <td><div class="tdNum">${totalHours}</div></td>
                    <td><div class="tdNum" name="jkyfp">0</div></td>
                    ${inputCells}
                </tr>`;
        },

        // 计算总分配学时
        calculateTotalHours: () => {
            let total = 0;
            $("input[name='jk']").each(function() {
                const val = parseInt($(this).val()) || 0;
                total += val;
            });
            return total;
        },

        // 更新已分配学时显示
        updateAllocatedHours: () => {
            const total = Utils.calculateTotalHours();
            $("div[name='jkyfp']").text(total);

            // 同时更新状态栏
            if (typeof SmartSelection !== 'undefined' && SmartSelection.updateStatusBar) {
                SmartSelection.updateStatusBar();
            }

            return total;
        },

        // 验证学时是否超限
        validateHours: (total, maxHours) => {
            if (total > maxHours) {
                top.layer.msg("您分配的学时总数已经超过了该项总学时。");
                return false;
            }
            return true;
        }
    };

    // 智能选择模块 - 解决单击和拖动冲突
    const SmartSelection = {
        processedElements: new Set(), // 记录已处理的元素

        // 初始化智能选择
        init: () => {
            SmartSelection.bindEvents();
            SmartSelection.initKeyboardShortcuts();
        },

        // 绑定事件
        bindEvents: () => {
            let mouseDownTime = 0;
            let mouseDownPos = { x: 0, y: 0 };
            let lastPos = { x: 0, y: 0 };
            let isDragging = false;
            let dragMode = null;
            let dragStarted = false;
            let processedElements = new Set(); // 记录已处理的元素

            // 鼠标按下
            $(".popWeekly_table").on('mousedown', 'th', function(e) {
                const $th = $(this);
                const idx = $th.index();

                // 跳过总学时和已分配学时列
                if (idx <= 1) return false;

                mouseDownTime = Date.now();
                mouseDownPos = { x: e.pageX, y: e.pageY };
                lastPos = { x: e.pageX, y: e.pageY };
                isDragging = false;
                dragStarted = false;
                SmartSelection.processedElements.clear();

                // 确定拖动模式
                const isSelected = SmartSelection.isElementSelected($th);
                dragMode = isSelected ? 'deselect' : 'select';

                e.preventDefault();
            });

            // 使用全局鼠标移动事件，避免快速移动时遗漏
            $(document).on('mousemove.smartSelection', function(e) {
                if (mouseDownTime === 0) return;

                // 计算移动距离
                const moveDistance = Math.abs(e.pageX - mouseDownPos.x) + Math.abs(e.pageY - mouseDownPos.y);
                const moveTime = Date.now() - mouseDownTime;

                // 判断是否开始拖动
                if (!isDragging && (moveDistance > 5 || moveTime > 150)) {
                    isDragging = true;
                    dragStarted = true;

                    // 只在真正开始拖动时显示模式指示器
                    SmartSelection.showDragModeIndicator(dragMode);
                }

                // 如果正在拖动，处理路径上的所有元素
                if (isDragging) {
                    SmartSelection.processMousePath(lastPos, { x: e.pageX, y: e.pageY }, dragMode);
                    lastPos = { x: e.pageX, y: e.pageY };
                }
            });

            // 鼠标释放
            $(document).on('mouseup.smartSelection', function(e) {
                if (mouseDownTime === 0) return;

                const clickTime = Date.now() - mouseDownTime;
                const moveDistance = Math.abs(e.pageX - mouseDownPos.x) + Math.abs(e.pageY - mouseDownPos.y);

                // 判断是单击还是拖动结束
                if (!dragStarted && clickTime < 200 && moveDistance < 5) {
                    // 单击操作 - 找到点击的元素
                    const $target = $(document.elementFromPoint(e.clientX, e.clientY));
                    if ($target.hasClass('th') && $target.index() > 1) {
                        SmartSelection.handleSingleClick($target);
                    }
                }

                // 重置状态
                mouseDownTime = 0;
                isDragging = false;
                dragStarted = false;
                dragMode = null;
                SmartSelection.processedElements.clear();

                // 隐藏拖动模式指示器
                SmartSelection.hideDragModeIndicator();

                // 更新已分配学时和状态显示
                Utils.updateAllocatedHours();
                SmartSelection.updateStatusBar();
            });

            // 防止拖动时选中文本
            $(".popWeekly_table").on('selectstart', function(e) {
                if (isDragging) {
                    e.preventDefault();
                    return false;
                }
            });
        },

        // 处理鼠标路径上的所有元素 - 高精度版本
        processMousePath: (startPos, endPos, mode) => {
            // 计算路径距离
            const distance = Math.sqrt(
                Math.pow(endPos.x - startPos.x, 2) +
                Math.pow(endPos.y - startPos.y, 2)
            );

            // 使用更密集的采样，确保快速移动时不遗漏
            const sampleCount = Math.max(Math.ceil(distance / 5), 2);

            // 处理路径上的每个采样点
            for (let i = 0; i <= sampleCount; i++) {
                const ratio = sampleCount === 0 ? 0 : i / sampleCount;
                const sampleX = startPos.x + (endPos.x - startPos.x) * ratio;
                const sampleY = startPos.y + (endPos.y - startPos.y) * ratio;

                // 获取采样点处的元素
                const element = document.elementFromPoint(sampleX, sampleY);
                if (element) {
                    const $elem = $(element);
                    if ($elem.hasClass('th') && $elem.index() > 1) {
                        SmartSelection.processElement($elem, mode);
                    }
                }
            }

            // 额外处理起点和终点，确保不遗漏
            SmartSelection.processPointElement(startPos, mode);
            SmartSelection.processPointElement(endPos, mode);
        },

        // 处理指定位置的元素
        processPointElement: (pos, mode) => {
            const element = document.elementFromPoint(pos.x, pos.y);
            if (element) {
                const $elem = $(element);
                if ($elem.hasClass('th') && $elem.index() > 1) {
                    SmartSelection.processElement($elem, mode);
                }
            }
        },

        // 处理单个元素（带去重）
        processElement: ($elem, mode) => {
            const elemId = $elem.index(); // 使用索引作为唯一标识

            // 避免在同一次拖动中重复处理同一元素
            if (!SmartSelection.processedElements.has(elemId)) {
                SmartSelection.processedElements.add(elemId);
                SmartSelection.toggleElement($elem, mode);
            }
        },

        // 处理单击
        handleSingleClick: ($elem) => {
            const idx = $elem.index();
            const isSelected = SmartSelection.isElementSelected($elem);
            const weekNum = idx - 1; // 周次编号

            if (isSelected) {
                // 取消选中
                $elem.removeClass('th-cur');
                $elem.css({"background-color": CONFIG.colors.default});

                // 清空对应的输入框
                const $input = $elem.parents("tr").next().find("td").eq(idx).find("input");
                if ($input.length > 0) {
                    $input.val(0);
                }

                // 显示简洁的操作提示
                SmartSelection.showOperationHint(`已取消选择第${weekNum}周`, 'info', 1000);
            } else {
                // 选中
                $elem.addClass('th-cur');
                $elem.css({"background-color": CONFIG.colors.selected});

                // 显示简洁的操作提示
                SmartSelection.showOperationHint(`已选择第${weekNum}周`, 'info', 1000);
            }
        },

        // 检查元素是否已选中
        isElementSelected: (element) => {
            if (!element || !element.length) return false;
            return element.hasClass('th-cur');
        },

        // 切换元素选择状态（拖动时使用）- 优化版
        toggleElement: ($elem, mode) => {
            if (!$elem || !$elem.length) return;

            const idx = $elem.index();
            const isCurrentlySelected = $elem.hasClass('th-cur');

            if (mode === 'select') {
                // 选中模式：只处理未选中的元素
                if (!isCurrentlySelected) {
                    $elem.addClass('th-cur');
                    $elem.css({"background-color": CONFIG.colors.selected});
                }
            } else if (mode === 'deselect') {
                // 取消选中模式：只处理已选中的元素
                if (isCurrentlySelected) {
                    $elem.removeClass('th-cur');
                    $elem.css({"background-color": CONFIG.colors.default});

                    // 清空对应的输入框
                    const $input = $elem.parents("tr").next().find("td").eq(idx).find("input");
                    if ($input.length > 0) {
                        $input.val(0);
                    }
                }
            }
        },

        // 显示拖动模式指示器
        showDragModeIndicator: (mode) => {
            const $indicator = $('#dragModeIndicator');
            const $text = $('#dragModeText');

            if (mode === 'select') {
                $indicator.removeClass('deselect-mode').addClass('select-mode show');
                $text.text('🎯 选择模式');
            } else {
                $indicator.removeClass('select-mode').addClass('deselect-mode show');
                $text.text('❌ 取消模式');
            }
        },

        // 隐藏拖动模式指示器
        hideDragModeIndicator: () => {
            $('#dragModeIndicator').removeClass('show');
        },

        // 更新状态栏
        updateStatusBar: () => {
            const selectedCount = $('.th-cur').length;
            const totalAllocated = Utils.calculateTotalHours();
            const remaining = STATE.nowxs - totalAllocated;

            $('#selectedCount').text(selectedCount);
            $('#totalAllocated').text(totalAllocated);

            const $remaining = $('#remainingHours');
            $remaining.text(remaining);

            // 根据剩余学时设置颜色
            $remaining.removeClass('warning danger');
            if (remaining < 0) {
                $remaining.addClass('danger');
            } else if (remaining < STATE.nowxs * 0.1) {
                $remaining.addClass('warning');
            }

            // 更新操作计数器
            $('#operationText').text(`已选择 ${selectedCount} 个周次，分配 ${totalAllocated} 学时`);
        },

        // 显示操作提示
        showOperationHint: (message, type = 'info', duration = 1500) => {
            const $counter = $('#operationCounter');
            $('#operationText').text(message);
            $counter.addClass('show');

            setTimeout(() => {
                $counter.removeClass('show');
            }, duration);
        },

        // 初始化键盘快捷键
        initKeyboardShortcuts: () => {
            $(document).on('keydown.smartSelection', (e) => {
                // Ctrl+A 全选
                if (e.ctrlKey && e.key.toLowerCase() === 'a') {
                    e.preventDefault();
                    $('.th').each(function() {
                        const $th = $(this);
                        if ($th.index() > 1) {
                            $th.addClass('th-cur');
                            $th.css({"background": CONFIG.colors.selected});
                        }
                    });
                    Utils.updateAllocatedHours();
                    SmartSelection.showOperationHint('已全选所有周次');
                }

                // Esc 清除选择
                if (e.key === 'Escape') {
                    $('.th').each(function() {
                        const $th = $(this);
                        if ($th.index() > 1) {
                            $th.removeClass('th-cur');
                            $th.css({"background": CONFIG.colors.default});
                        }
                    });
                    $("input[name='jk']").val(0);
                    Utils.updateAllocatedHours();
                    SmartSelection.showOperationHint('已清除所有选择');
                }

                // Delete 清空选中周次的学时
                if (e.key === 'Delete') {
                    $('.th-cur').each(function() {
                        const $th = $(this);
                        const idx = $th.index();
                        const $input = $th.parents("tr").next().find("td").eq(idx).find("input");
                        if ($input.length > 0) {
                            $input.val(0);
                        }
                    });
                    Utils.updateAllocatedHours();
                    SmartSelection.showOperationHint('已清空选中周次的学时');
                }
            });
        },

        // 清理事件监听器
        destroy: () => {
            $(document).off('mousemove.smartSelection');
            $(document).off('mouseup.smartSelection');
            $(document).off('keydown.smartSelection');
            SmartSelection.processedElements.clear();
            SmartSelection.hideDragModeIndicator();
        }




    };

    // 周次数据处理模块
    const WeekDataHandler = {
        // 初始化周次数据
        init: (weekData) => {
            if (!weekData) return;

            if (weekData.indexOf(",") === -1) {
                WeekDataHandler.handleSingleWeekRange(weekData);
            } else {
                WeekDataHandler.handleMultipleWeeks(weekData);
            }

            Utils.updateAllocatedHours();
        },

        // 处理单个周次范围
        handleSingleWeekRange: (weekData) => {
            const [range, hours] = weekData.split(":");
            const [start, end] = range.split("-").map(Number);

            for (let i = start - 1; i < end; i++) {
                $(".popWeekly_table th").eq(i + 2).addClass("th-cur");
                $("input[name='jk']").eq(i).val(hours);
            }
        },

        // 处理多个周次
        handleMultipleWeeks: (weekData) => {
            const weekArray = weekData.split(",");
            weekArray.forEach(weekItem => {
                const [range, hours] = weekItem.split(":");
                // 检查是否是范围（如 "1-5"）或单个周次
                if (range.includes("-")) {
                    const [start, end] = range.split("-").map(Number);
                    for (let i = start - 1; i < end; i++) {
                        $(".popWeekly_table th").eq(i + 2).addClass("th-cur");
                        $("input[name='jk']").eq(i).val(hours);
                    }
                } else {
                    // 单个周次的情况
                    const week = parseInt(range);
                    $(".popWeekly_table th").eq(week + 1).addClass("th-cur");
                    $("input[name='jk']").eq(week - 1).val(hours);
                }
            });
        }
    };

    // 学时信息管理模块
    const HourInfoManager = {
        // 初始化学时信息显示
        init: () => {
            HourInfoManager.updateAllHourInfo();
        },

        // 更新所有学时信息
        updateAllHourInfo: () => {
            // 更新当前学时类型
            $("#currentHourType").text(CONFIG.hourType);

            // 更新当前总学时
            $("#currentTotalHours").text(STATE.nowxs);

            // 更新各种学时类型的数值
            if (CONFIG.tpf) {
                $("#totalHours").text(CONFIG.tpf.jxjhgl_zxs || 0);
                $("#theoryHours").text(CONFIG.tpf.jxjhgl_llxs || 0);
                $("#practiceHours").text(CONFIG.tpf.jxjhgl_sjxs || 0);
                $("#computerHours").text(CONFIG.tpf.jxjhgl_sjxss || 0);
                $("#experimentHours").text(CONFIG.tpf.jxjhgl_syxs || 0);
                $("#otherHours").text(CONFIG.tpf.jxjhgl_qtxs || 0);
            }
        },

        // 高亮当前学时类型
        highlightCurrentHourType: () => {
            // 移除所有高亮
            $(".hour-info-item").removeClass("highlight-current");

            // 根据当前学时类型添加高亮
            const currentType = CONFIG.hourType;
            let targetId = "";

            switch(currentType) {
                case "理论学时":
                    targetId = "#theoryHours";
                    break;
                case "实践学时":
                    targetId = "#practiceHours";
                    break;
                case "上机学时":
                    targetId = "#computerHours";
                    break;
                case "实验学时":
                    targetId = "#experimentHours";
                    break;
                case "其他学时":
                    targetId = "#otherHours";
                    break;
            }
        }
    };

    // 初始化函数
    $(document).ready(() => {
        TableManager.init();
        EventHandlers.init();
        SmartSelection.init();
        HourInfoManager.init();

        // 初始化状态栏
        setTimeout(() => {
            SmartSelection.updateStatusBar();
        }, 100);
    });

    // 页面卸载时清理
    $(window).on('beforeunload', () => {
        SmartSelection.destroy();
    });

    // 表格管理模块
    const TableManager = {
        init: () => {
            const url = "/processData/getCurSemesterData";
            $.post(url, {name: CONFIG.semester}, (result) => {
                const weeks = result.basicSemesterInfo.xnxq_jsz;
                const tableHTML = Utils.buildTableHTML(weeks, STATE.nowxs);
                $(".popWeekly_table table tbody").html(tableHTML);

                // 初始化周次数据
                WeekDataHandler.init(CONFIG.week);

                // 高亮当前学时类型
                HourInfoManager.highlightCurrentHourType();
            }, "json");
        }
    };

    // LayUI初始化
    let layer = "";
    layui.use(['form', 'layer'], function () {
        layer = layui.layer;
        STATE.layer = layer;
    });

    // UI工具模块
    const UIUtils = {
        // 弹窗居中
        centerPopup: (selector) => {
            $(selector).css({
                top: 0,
                left: function () {
                    return ($(window).width() - $(this).width()) / 2;
                }
            });
        },

        // 初始化弹窗位置
        initPopupPosition: () => {
            UIUtils.centerPopup('.popMove');
            $(window).on('resize', () => UIUtils.centerPopup('.popMove'));
        }
    };

    // 事件处理模块
    const EventHandlers = {
        init: () => {
            EventHandlers.bindInputEvents();
            UIUtils.initPopupPosition();
        },

        // 绑定输入框事件
        bindInputEvents: () => {
            $(".popWeekly_table")
                .on("focus", "input[name='jk']", EventHandlers.handleInputFocus)
                .on("blur", "input[name='jk']", EventHandlers.handleInputBlur);
        },

        // 处理输入框获得焦点
        handleInputFocus: function() {
            const val = Utils.trim($(this).val());
            if (val === '' || val === '0') {
                $(this).val("");
            }
        },

        // 处理输入框失去焦点
        handleInputBlur: function() {
            const $input = $(this);
            const val = Utils.trim($input.val());
            const idx = $input.parent().index();
            const $th = $input.parents("tr").prev().find("th").eq(idx);

            if (val === '' || val === '0') {
                $th.removeClass("th-cur");
                $input.val("0");
            } else if (parseInt(val) > 0) {
                $th.addClass("th-cur");
            }

            const total = Utils.updateAllocatedHours();
            Utils.validateHours(total, STATE.nowxs);
        }
    };

    // 数据处理模块
    const DataProcessor = {
        // 保存课时数据，生成周次配置字符串
        saveWeekHours: (maxHours, inputs, inputElement) => {
            let total = 0;
            const result = {};
            const weekRanges = [];
            let currentHours = '';
            let minWeek = 1;
            let maxWeek = 1;

            $(inputs).each(function(index) {
                const val = Utils.trim($(this).val());

                if (currentHours === '') {
                    currentHours = val;
                } else {
                    if (val === currentHours) {
                        maxWeek = index + 1;
                    } else {
                        if (currentHours != 0) {
                            weekRanges.push(`${minWeek}-${maxWeek}:${currentHours}`);
                            total += parseInt(currentHours) * (maxWeek - minWeek + 1);
                        }
                        minWeek = index + 1;
                        maxWeek = index + 1;
                        currentHours = val;
                    }
                }
            });

            // 处理最后一组数据
            if (currentHours != 0) {
                weekRanges.push(`${minWeek}-${maxWeek}:${currentHours}`);
                total += parseInt(currentHours) * (maxWeek - minWeek + 1);
            }

            $(inputElement).val(weekRanges.join(','));

            result.cstr = weekRanges.join(',');
            result.total = total;
            return result;
        },

        // 获取当前数据
        getCurrentData: () => {
            return DataProcessor.saveWeekHours(
                STATE.nowxs,
                $("input[name='jk']"),
                $("#datajk")
            );
        }
    };

    // 周次选择操作模块
    const WeekSelection = {
        // 全选所有周次
        selectAll: () => {
            $(".popWeekly_table th").each(function() {
                const idx = $(this).index();
                if (idx > 1) {
                    $(this).css({"background-color": CONFIG.colors.selected});
                    $(this).addClass('th-cur');
                }
            });
        },

        // 清除所有选择
        clearAll: () => {
            $(".popWeekly_table th").each(function() {
                const idx = $(this).index();
                if (idx > 1) {
                    $(this).css({"background-color": CONFIG.colors.default});
                    $(this).removeClass('multi-selected multi-cur th-cur');
                }
            });

            $(".popWeekly_table input[name='jk']").val(0);
            $("div[name='jkyfp']").text(0);
        },

        // 选择奇数周
        selectOdd: () => {
            WeekSelection.clearAll();
            $(".popWeekly_table th").each(function() {
                const idx = $(this).index();
                if (idx > 1) {
                    const weekNum = idx - 1;
                    if (weekNum % 2 === 1) { // 奇数周
                        $(this).css({"background-color": CONFIG.colors.selected});
                        $(this).addClass('th-cur');
                    }
                }
            });
        },

        // 选择偶数周
        selectEven: () => {
            WeekSelection.clearAll();
            $(".popWeekly_table th").each(function() {
                const idx = $(this).index();
                if (idx > 1) {
                    const weekNum = idx - 1;
                    if (weekNum % 2 === 0) { // 偶数周
                        $(this).css({"background-color": CONFIG.colors.selected});
                        $(this).addClass('th-cur');
                    }
                }
            });
        },

        // 获取选中的周次
        getSelectedWeeks: () => {
            const selectedWeeks = [];
            $(".popWeekly_table th").each(function() {
                const idx = $(this).index();
                if (idx > 1) {
                    const bgColor = $(this).css('background-color');
                    const hasSelectedClass = $(this).hasClass('th-cur') ||
                                           $(this).hasClass('multi-cur') ||
                                           $(this).hasClass('multi-selected');

                    if (hasSelectedClass || bgColor !== 'rgb(242, 242, 242)') {
                        selectedWeeks.push(idx - 1); // 转换为周次索引
                    }
                }
            });
            return selectedWeeks;
        },

        // 验证是否有选中的周次
        validateSelection: () => {
            const selectedWeeks = WeekSelection.getSelectedWeeks();
            if (selectedWeeks.length === 0) {
                layer.msg("请先选择教学周");
                return false;
            }
            STATE.zcArray = selectedWeeks;
            return true;
        }
    };


    // 学时分配操作模块
    const HourAllocation = {
        // 平均分配学时
        averageDistribute: () => {
            if (!WeekSelection.validateSelection()) return false;

            const selectedCount = STATE.zcArray.length;
            const averageHours = STATE.nowxs / selectedCount;

            if (averageHours < 1) {
                layer.msg("总学时不能小于所选个数");
                return false;
            }

            HourAllocation.clearAllInputs();
            $("div[name='jkyfp']").text(STATE.nowxs);

            STATE.zcArray.forEach((weekIndex, i) => {
                const cellIndex = weekIndex + 1; // 表格索引偏移
                const hours = i === selectedCount - 1 ?
                    STATE.nowxs % selectedCount + Math.floor(averageHours) :
                    Math.floor(averageHours);
                $(".popWeekly_table td").eq(cellIndex).find("input").val(hours);
            });
        },

        // 一键设置相同学时
        setUniformHours: () => {
            if (!WeekSelection.validateSelection()) return false;

            layer.open({
                type: 1,
                shift: 7,
                shadeClose: true,
                title: '请输入学时',
                btn: ['确定', '取消'],
                btnAlign: 'c',
                content: `
                    <div class='layui-layer-content' style='padding: 20px;'>
                        <input type='text' id='layer-input-xs'
                               style='background-color: #fff; display: block; width: 230px; height: 36px;
                                      margin: 0 auto; line-height: 30px; padding-left: 10px;
                                      border: 1px solid #e6e6e6; color: #333;' value=''/>
                    </div>`,
                yes: (index) => {
                    const inputHours = parseInt($('#layer-input-xs').val());
                    const totalHours = inputHours * STATE.zcArray.length;

                    if (!inputHours) {
                        layer.msg("学时不能为空。");
                        return false;
                    }

                    if (totalHours > STATE.nowxs) {
                        layer.confirm("当前分配的学时已超过计划总学时，是否继续设置？", {
                            btn: ['确定', '取消'],
                            yes: (confirmIndex) => {
                                HourAllocation.fillSelectedWeeks(inputHours, totalHours);
                                layer.close(confirmIndex);
                                layer.close(index);
                            }
                        });
                    } else {
                        HourAllocation.fillSelectedWeeks(inputHours, totalHours);
                        layer.close(index);
                    }
                },
                cancel: (index) => layer.close(index)
            });
        },

        // 清空所有输入
        clearAllInputs: () => {
            $(".popWeekly_table input[name='jk']").val(0);
        },

        // 填充选中周次的学时
        fillSelectedWeeks: (hours, totalHours) => {
            HourAllocation.clearAllInputs();
            $("div[name='jkyfp']").text(totalHours);

            STATE.zcArray.forEach(weekIndex => {
                const cellIndex = weekIndex + 1;
                $(".popWeekly_table td").eq(cellIndex).find("input").val(hours);
            });
        }
    };

    // 单双周学时设置
    const OddEvenHourSetting = {
        // 设置单双周不同学时
        setOddEvenHours: () => {
            if (!WeekSelection.validateSelection()) return false;

            layer.open({
                type: 1,
                shift: 7,
                shadeClose: false,
                title: '请输入学时',
                btn: ['确定', '取消'],
                btnAlign: 'c',
                content: `
                    <div class='layui-layer-content' style='width:350px;'>
                        <div style='display:flex; margin:15px 0; align-items: center;
                                    justify-content: flex-start; width:320px; margin-left: 6%;'
                             class='form-group has-feedback'>
                            <p style='width: 60px;'>单周</p>
                            <input type='text' id='layer-input-dz'
                                   style='display: block; width: 230px; height: 36px; margin: 0 auto;
                                          line-height: 30px; padding-left: 10px; border: 1px solid #e6e6e6;
                                          color: #333;' value=''/>
                        </div>
                        <div style='display:flex; margin:15px 0; align-items: center;
                                    justify-content: flex-start; width:320px; margin-left: 6%;'
                             class='form-group has-feedback'>
                            <p style='width: 60px;'>双周</p>
                            <input type='text' id='layer-input-sz'
                                   style='display: block; width: 230px; height: 36px; margin: 0 auto;
                                          line-height: 30px; padding-left: 10px; border: 1px solid #e6e6e6;
                                          color: #333;' value=''/>
                        </div>
                    </div>`,
                yes: (index) => {
                    const oddHours = parseInt($('#layer-input-dz').val()) || 0;
                    const evenHours = parseInt($('#layer-input-sz').val()) || 0;

                    if (!oddHours && !evenHours) {
                        layer.msg("单周、双周学时不能同时为空。");
                        return false;
                    }

                    const { oddCount, evenCount } = OddEvenHourSetting.countOddEvenWeeks();
                    const totalHours = oddCount * oddHours + evenCount * evenHours;

                    if (totalHours > STATE.nowxs) {
                        layer.confirm("当前分配的学时已超过计划总学时，是否继续设置？", {
                            btn: ['确定', '取消'],
                            yes: (confirmIndex) => {
                                OddEvenHourSetting.fillOddEvenWeeks(oddHours, evenHours, totalHours);
                                layer.close(confirmIndex);
                                layer.close(index);
                            }
                        });
                    } else {
                        OddEvenHourSetting.fillOddEvenWeeks(oddHours, evenHours, totalHours);
                        layer.close(index);
                    }
                },
                cancel: (index) => layer.close(index)
            });
        },

        // 统计奇偶周数量
        countOddEvenWeeks: () => {
            let oddCount = 0, evenCount = 0;
            STATE.zcArray.forEach(weekIndex => {
                if ((weekIndex + 1) % 2 === 1) {
                    oddCount++;
                } else {
                    evenCount++;
                }
            });
            return { oddCount, evenCount };
        },

        // 填充奇偶周学时
        fillOddEvenWeeks: (oddHours, evenHours, totalHours) => {
            HourAllocation.clearAllInputs();
            $("div[name='jkyfp']").text(totalHours);

            STATE.zcArray.forEach(weekIndex => {
                const cellIndex = weekIndex + 1;
                const hours = weekIndex % 2 === 1 ? oddHours : evenHours;
                $(".popWeekly_table td").eq(cellIndex).find("input").val(hours);
            });
        }
    };

    // 公共API函数 - 供外部调用
    window.WeekHourManager = {
        // 获取当前数据
        getData: () => DataProcessor.getCurrentData(),

        // 选择操作
        selectAllWeeks: WeekSelection.selectAll,
        selectOddWeeks: WeekSelection.selectOdd,
        selectEvenWeeks: WeekSelection.selectEven,
        clearAllSelection: WeekSelection.clearAll,

        // 学时分配操作
        averageDistributeHours: HourAllocation.averageDistribute,
        setUniformHours: HourAllocation.setUniformHours,
        setOddEvenHours: OddEvenHourSetting.setOddEvenHours
    };

    // 兼容性函数 - 保持原有函数名
    function getdata() {
        return DataProcessor.getCurrentData();
    }

    function selectAllWeeks() {
        WeekSelection.selectAll();
    }

    function selectOddWeeks() {
        WeekSelection.selectOdd();
    }

    function selectEvenWeeks() {
        WeekSelection.selectEven();
    }

    function clearAllSelection() {
        WeekSelection.clearAll();
    }

    function averageDistributeHours() {
        HourAllocation.averageDistribute();
    }

    function setUniformHours() {
        HourAllocation.setUniformHours();
    }

    function setOddEvenHours() {
        OddEvenHourSetting.setOddEvenHours();
    }

    // 阻止弹窗内容点击事件冒泡
    $("body").on("click", ".layui-layer-content", function(e) {
        e.stopPropagation();
        return false;
    });
</script>
</body>
</html>
