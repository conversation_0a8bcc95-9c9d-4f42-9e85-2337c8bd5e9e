<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学分互认申请表</title>
    <link rel="stylesheet" th:href="@{~/css/creditManage/global.css}">
    <link rel="stylesheet" th:href="@{~/plugin/layui/css/layui.css}">
    <link rel="stylesheet" th:href="@{~/css/creditManage/common.css}">
    <link rel="stylesheet" th:href="@{~/css/creditManage/reset.css}">
    <link rel="stylesheet" th:href="@{~/css/creditManage/scoreApply.css}">
    <style>
        /*.layui-form-select dl dd{*/
        /*    line-height:34px!important;*/
        /*}*/

        @keyframes layui-upbit {
            /*from {*/
            /*    transform: translate3d(0, 30px, 0);*/
            /*    opacity: .3*/
            /*}*/
            /*to {*/
            /*    transform: translate3d(0, 0, 0);*/
            /*    opacity: 1*/
            /*}*/
        }
    </style>
</head>

<body>
<div class="dialog dialog-apply">
    <div class="head">
        <h1>学分互认申请</h1>
        <div class="head-opt">
            <button class="btn-cancel" id="cancel">取消</button>
            <button class="btn-submit" id="invigilateSure">提交</button>
        </div>
    </div>
    <div class="dialog-conten sel-result-wrap">
        <div class="sel-result">
            <div class="sel-stu">
                <div class="con-title">选择学生</div>
                <form class="layui-form form-stu" id="formCon" action="">
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width: 119px;"><em>*</em>申请学年学期</label>
                        <div class="layui-input-block">
                            <input type="text" name="xnxq" disabled required lay-verify="required"
                                   style="color: #86909C;cursor:not-allowed" placeholder="请输入"
                                   autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width: 119px;"><em>*</em>申请学生</label>
                        <div class="layui-input-block">
                            <input type="text" name="stu" readonly required lay-verify="required"
                                   placeholder="点击选择申请学生" id="stuApply" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </form>
            </div>

            <div class="item ">
                <div class="i-con" style="align-items: center;">
                    <div class="class-box">
                        <div class="con-title">已修学分查询</div>
                        <div class="j-wrap">
                            <div class="j-search clearfixs">
                                <div class="j-search-item">
                                    <h5>学年学期</h5>
                                    <div class="j-search-con multiple-box">
                                        <input type="text" placeholder="请选择" readonly class="schoolSel">
                                        <span class="j-arrow"></span>
                                        <div class="j-select-year ">
                                            <div class="allSelect">
                                                全选
                                            </div>
                                            <ul id="xnxqUl">
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="j-search-item">
                                    <h5>学分名称</h5>
                                    <div class="j-search-con multiple-box">
                                        <input type="creditName" placeholder="请选择" readonly class="schoolSel">
                                        <span class="j-arrow"></span>
                                        <div class="j-select-year ">
                                            <div class="allSelect">
                                                全选
                                            </div>
                                            <ul id="creditName">
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                                <button type="button" class=" set-button layui-btn layui-bg-blue"
                                        id="search1">查询
                                </button>
                                <button class="set-button layui-btn layui-btn-primary layui-border-blue"
                                        id="reset1">重置
                                </button>
                            </div>
                            <div class="j-table" style="border:none;">
                                <table class="layui-table" id="administration" lay-filter="administration">
                                </table>
                                <div class="sel-score" id="laytips">
                                    已选已修学分总计：<span id="score1">0</span>
                                </div>
                                <div class="sel-score-tips">
                                    说明：已选已修学分必须大于等于申请课程学分，否则无法提交
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="class-box" style="min-height: 320px">
                        <div class="con-title">申请互认课程</div>
                        <div class="j-wrap">
                            <div class="j-search clearfixs">
                                <div class="j-search-item ">
                                    <h5>课程性质</h5>
                                    <div class="j-search-con multiple-box">
                                        <input type="text" placeholder="请选择" readonly="" class="schoolSel">
                                        <span class="j-arrow"></span>
                                        <div class="j-select-year">
                                            <div class="allSelect">
                                                全选
                                            </div>
                                            <ul id="kcxz">

                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="j-search-item">
                                    <h5>课程名称</h5>
                                    <div class="j-search-con j-search-vague">
                                        <input type="text" name="course" placeholder="请输入" class="schoolSel">
                                    </div>
                                </div>
                                <button type="button" class=" set-button layui-btn layui-bg-blue"
                                        id="search2">查询
                                </button>
                                <button class="set-button layui-btn layui-btn-primary layui-border-blue"
                                        id="reset2">重置
                                </button>

                            </div>

                            <div class="j-table" style="border:none;">
                                <table class="layui-table" id="materialTable" lay-filter="materialTable">
                                </table>
                                <div class="sel-score sel-total">
                                    已申请课程学分总计：<span id="score2">0</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="set-result">
            <div class="set-title">设置结果</div>
            <table class="layui-table" id="tableResult" lay-filter="tableResult">
            </table>
        </div>
    </div>

</div>
<div class="dialog-wrap" style="display: none;">
    <div class="dialog dialog-stu">
        <div class="dialog-close">
        </div>
        <div class="dialogCon">
            <form class="layui-form form-stu" id="formStu" action="" lay-filter="formStu">
                <div class="item-search-con">
                    <div class="layui-form-item">
                        <label class="layui-form-label">年级</label>
                        <div class="layui-input-block">
                            <select name="grade" class="grade" lay-search="">
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">院系</label>
                        <div class="layui-input-block">
                            <select name="depth" class="depth" lay-search="">
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">专业</label>
                        <div class="layui-input-block">
                            <select name="stuMajor" class="stuMajor" lay-search="">

                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">班级</label>
                        <div class="layui-input-block">
                            <select name="stuClass" class="stuClass" lay-search="">

                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">姓名</label>
                        <div class="layui-input-block">
                            <select name="xsxm" class="stuName" lay-search="">
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">学号</label>
                        <div class="layui-input-block">
                            <select name="xsxh" class="stuXh" lay-search="">
                            </select>

                        </div>
                    </div>
                </div>
                <div class="item-search-btn">
                    <div class="layui-form-item">
                        <button type="submit" class="layui-btn set-button layui-btn layui-bg-blue" lay-submit
                                lay-filter="searchBtn" id="search3">查询
                        </button>

                        <button type="reset" class="set-button layui-btn layui-btn-primary layui-border-blue"
                                id="reset3">重置
                        </button>
                    </div>
                </div>

            </form>
            <div class="j-table">
                <table class="layui-table" id="stuList" lay-filter="stuList">
                </table>
            </div>
        </div>
        <div class="dialog-btn">
            <button class="dialog-cancel" id="btnSelCancel">取消</button>
            <button
                    class="dialog-submit" id="btnSelSure">确定
            </button>
        </div>
    </div>
</div>
</body>
<script type="text/html" id="tmplScoreEntry">
    {{#  if(d.scoreEntry ==1){ }}
    <span lay-event="cancel" style="color: #E85A5A;cursor: pointer">取消</span>
    {{#  } else { }}
    <span lay-event="sel" style="color: #1A79FF;cursor: pointer">选择 </span>
    {{#  } }}
</script>
<script th:src="@{~/js/jquery-3.3.1.min.js}"></script>
<script th:src="@{~/plugin/layui/layui2.8.12.js}"></script>
<script th:src="@{~/js/creditManage/common.js}"></script>
<script th:src="@{~/js/my.util.js}"></script>
<script th:inline="javascript">
    var user = [[${user}]];
    var role = [[${role}]];
    var set = [[${set}]];
    var curxnxq = '';
    $(function () {
        if (role == 3) {
            $("#stuApply").attr("data-value", user.xgh);
            $("#stuApply").val(user.name);
        }
        $.get("/basic/xnxq", {fid: user.fid}, function (res) {
            if (res.code == 200) {
                let html = '';

                for (let i = 0; i < res.data.length; i++) {
                    var data = res.data[i];
                    if (data.xnxq_sfdqxq === "是") {
                        $("input[name=xnxq]").val(data.xnxq_xnxqh)
                        curxnxq = data.xnxq_xnxqh;
                    }
                    html += '<li>' + data.xnxq_xnxqh + '</li>'
                }
                $("#xnxqUl").html(html);
            } else {
                U.fail("请检查学年学期设置")
            }
            if (curxnxq == '') {
                U.fail("请检查学年学期设置")
            }
        })

        if (set == null) {
            U.fail("请设置基础规则")
        } else {
            var setGrade = set.grade;
            var gradeHtml = "<option value=\"\">请选择</option>";
            var split = setGrade.split(",");
            for (let i = 0; i < split.length; i++) {
                gradeHtml += "<option value=\"" + split[i] + "\">" + split[i] + "</option>";
            }
            $(".grade").html(gradeHtml);
        }

        $.get("/basic/kcxz", {fid: user.fid}, function (res) {
            if (res.code == 200) {
                var html = ""
                res.data.forEach((data, index) => {
                    html += '<li data-value=' + data.kcxz + '>' + data.kcxz + '</li>'
                })
                $("#kcxz").html(html);
            }
        })
    })

    var creditNameSet = [];

    $("#xnxqUl").parent().children("div").click(function () {
        if ($(this).hasClass("cur")) {
            //获取学分规则
            getCreditSet();
        } else {
            creditNameSet = [];
            updateCreditName();

        }
    })

    $("#xnxqUl").on("click", "li", function () {
        var xnxqSet = [];
        var xnxq = $(this).text();
        $(this).parent().children(".active").each((index, item) => {
            var itemXnxq = $(item).text();
            if (itemXnxq !== xnxq) {
                xnxqSet.push(itemXnxq);
            }
        })
        if (!$(this).hasClass("active")) {
            xnxqSet.push(xnxq)
        }
        if (xnxqSet.length==0){
            creditNameSet = [];
            updateCreditName();
            return;
        }

        getCreditSet(xnxqSet.join(","));
    })

    function getCreditSet(xnxq) {
        creditNameSet = [];
        $.get("/new/creditRuleSet/getRule", {fid: user.fid, xnxq: xnxq}, function (res) {
            if (res.code == 200) {
                var data = res.data;
                for (let i = 0; i < data.length; i++) {
                    let json = {
                        xnxq: xnxq,
                        name: data[i].creditName,
                    }
                    creditNameSet.push(json);
                }
                updateCreditName();
            }
        })
    }

    function updateCreditName() {
        let credit = new Set();
        var html = ''
        for (let i = 0; i < creditNameSet.length; i++) {
            credit.add(creditNameSet[i].name)
        }
        credit.forEach(data => {
            html += '<li>' + data + "</li>"
        })

        $("#creditName").html(html);
        $("#creditName").parent("div").children("div").removeClass("cur");
        $("#creditName").parents(".j-search-con").children("input").val("");
    }


    layui.use(["table", "jquery", "form", 'laypage', 'layer'], function () {
        var form = layui.form;
        var table = layui.table;
        var laypage = layui.laypage;
        var layer = layui.layer;
        var $ = layui.jquery;

        $(function () {
            getSearchData()
            updateTable()
        })

        function getSearchData() {
            var data = form.val('formStu');
            if (role == 0) {
                data["bzruid"] = user.uid;
            }
            $.get("/new/credit/search/bar2", data, function (res) {
                if (res.code == 200) {
                    var result = res.data;
                    // var grade = result.grade == null ? [] : result.grade;
                    var depth = result.depth == null ? [] : result.depth;
                    var stuMajor = result.stuMajor == null ? [] : result.stuMajor;
                    var stuClass = result.stuClass == null ? [] : result.stuClass;
                    var stuName = result.stuName == null ? [] : result.stuName;
                    var stuXh = result.stuXh == null ? [] : result.stuXh;
                    var depthHtml = "<option value=\"\">请选择</option>";
                    for (let i = 0; i < depth.length; i++) {
                        depthHtml += "<option value=\"" + depth[i] + "\">" + depth[i] + "</option>";
                    }
                    var majorHtml = "<option value=\"\">请选择</option>";
                    for (let i = 0; i < stuMajor.length; i++) {
                        majorHtml += "<option value=\"" + stuMajor[i] + "\">" + stuMajor[i] + "</option>";
                    }
                    var classHtml = "<option value=\"\">请选择</option>";
                    for (let i = 0; i < stuClass.length; i++) {
                        classHtml += "<option value=\"" + stuClass[i] + "\">" + stuClass[i] + "</option>";
                    }

                    var nameHtml = "<option value=\"\">请选择</option>";
                    for (let i = 0; i < stuName.length; i++) {
                        nameHtml += "<option value=\"" + stuName[i] + "\">" + stuName[i] + "</option>";
                    }
                    var xhHtml = "<option value=\"\">请选择</option>";
                    for (let i = 0; i < stuXh.length; i++) {
                        xhHtml += "<option value=\"" + stuXh[i] + "\">" + stuXh[i] + "</option>";
                    }

                    $(".depth").html(depthHtml);
                    $(".stuMajor").html(majorHtml);
                    $(".stuClass").html(classHtml);
                    $(".stuName").html(nameHtml);
                    $(".stuXh").html(xhHtml);

                    form.render("select", "formStu");
                }
            })
        }

        $("#laytips").mouseenter(function () {
            $(this).next().fadeIn();
        }).mouseleave(function () {
            $(this).next().fadeOut();
        })

        // 已修学分查询

        let creditData = [];
        table.render({
            elem: '#administration',
            // url: '/static/json/table/user.json', //数据接口
            data: creditData, //静态数据，真实数据用url接口
            cols: [
                [{
                    type: 'checkbox',
                    width: 70,
                    fixed: 'center',
                }, {
                    field: 'xnxq',
                    title: '学年学期',
                    align: 'center',
                    minWidth: 152
                }, {
                    field: 'creditType',
                    title: '学分类型',
                    align: 'center',
                    minWidth: 152
                }, {
                    field: 'projectName',
                    title: '课程/项目名称',
                    align: 'center',
                    minWidth: 152
                }, {
                    field: 'score',
                    title: '已获学分',
                    align: 'center',
                    minWidth: 152
                }
                ]
            ],
            limits: [5, 10, 20, 30, 50, 100],
            limit: 5,
            page: true,
        });
        //查询
        $("#search1").click(function () {
            selScoreTotal1 = 0;
            search();
        })

        function search() {

            var xsxh = $("#stuApply").attr("data-value");
            if (xsxh == undefined || xsxh == '') {
                U.fail("请选择申请学生")
                return false;
            }

            let xnxq = []
            $("#xnxqUl li.active").each((idx, value) => {
                xnxq.push($(value).html());
            })
            if (xnxq.length == 0) {
                U.fail("请选择申请学年学期")
                return false;
            }
            let creditName = []
            $("#creditName li.active").each((idx, value) => {
                creditName.push($(value).html());
            })
            if (creditName.length == 0) {
                U.fail("请选择学分名称")
                return false;
            }
            let _table = table;
            $.get("/credit/mr/getCreditList", {
                xnxq: xnxq.join(","),
                creditName: creditName.join(","),
                xsxh: xsxh
            }, function (res) {
                if (res.code == 200) {
                    creditData = res.data;
                    $("#score1").html(0);
                    _table.reload('administration', {data: res.data});
                } else {
                    U.fail("无可用学分");
                    _table.reload('administration', {data: []})
                }
            })
        }

        // 重置
        $("#reset1").click(function () {
            table.reload('administration', {data: []});
            var p = $(this).parents('.j-search');
            p.find('li').removeClass('active');
            p.find('.allSelect').removeClass('cur');
            p.find('input').val('');
            resultData = [];
            table.reload("tableResult", {data: resultData})
        })

        // table复选框选中事件
        var selScoreTotal1 = 0;
        table.on('checkbox(administration)', function (obj) {
            if (obj.checked) {
                // 选中
                if (obj.type == 'one') {
                    addData(resultData, obj.data, checkedCourse);
                } else {
                    table.checkStatus('administration').data.forEach(element => {
                        addData(resultData, element, checkedCourse);
                    });
                }
            } else {
                // 取消选中
                if (obj.type == 'one') {
                    resultData = resultData.filter(item => item.id !== obj.data.formUserId)
                } else {
                    table.getData("administration").forEach(element => {
                        resultData = resultData.filter(item => item.id !== element.formUserId)
                    });
                }
            }

            selScoreTotal1 = 0;
            for (let i = 0; i < resultData.length; i++) {
                if (resultData[i].hasScore != "" && resultData[i].hasScore != undefined) {
                    selScoreTotal1 += parseFloat(resultData[i].hasScore);
                }
            }
            $("#score1").text(selScoreTotal1);
            table.reload('tableResult', {data: resultData});
        });
        // 申请课程
        let courseData = []

        table.render({
            elem: '#materialTable',
            // url: '/static/json/table/user.json', //数据接口
            data: courseData, //静态数据，真实数据用url接口
            cols: [
                [{
                    field: 'kcbh',
                    title: '课程编号',
                    align: 'center',
                    minWidth: 111
                }, {
                    field: 'kcmc',
                    title: '课程名称',
                    align: 'center',
                    minWidth: 118
                }, {
                    field: 'kcxz',
                    title: '课程性质',
                    align: 'center',
                    minWidth: 118
                }, {
                    field: 'kccj',
                    title: '课程成绩',
                    align: 'center',
                    minWidth: 118
                }, {
                    field: 'kcxf',
                    title: '课程学分',
                    align: 'center',
                    minWidth: 118
                }, {
                    title: "操作",
                    width: 100,
                    fixed: 'right',
                    align: "center",
                    toolbar: "#tmplScoreEntry",
                },]
            ],
            limits: [5, 10, 20, 30, 50, 100],
            limit: 5,
            page: true,
        });

        var checkedCourse = '';
        table.on('tool(materialTable)', function (obj) {
            courseData.forEach(tempData => {
                tempData.scoreEntry = 0;
            })
            var data = obj.data;
            $('.layui-table-view[lay-id="materialTable"]  span[lay-event="cancel"]').attr('lay-event', 'sel').text('选择').css({color: '#1A79FF'});
            if (obj.event === 'cancel') {
                data.scoreEntry = 0;
                $("#score2").text(0);
                checkedCourse = '';
                obj.tr.find('td:last-child span[lay-event="cancel"]').attr('lay-event', 'sel').text('选择').css({color: '#1A79FF'});
                for (let i = 0; i < resultData.length; i++) {
                    resultData[i].score = "";
                    resultData[i].courseScore = "";
                    resultData[i].code = "";
                    resultData[i].courseName = "";
                }
            } else if (obj.event === 'sel') {
                checkedCourse = data;
                if (obj.data.kcxf == undefined || obj.data.kcxf == null || obj.data.kcxf == "") {
                    $("#score2").text(0);
                } else {
                    $("#score2").text(obj.data.kcxf);
                }
                data.scoreEntry = 1;
                obj.tr.find('td:last-child span[lay-event="sel"]').attr('lay-event', 'cancel').text('取消').css({color: '#E85A5A'});
                for (let i = 0; i < resultData.length; i++) {
                    resultData[i].score = data.kcxf;
                    resultData[i].courseScore = data.kccj;
                    resultData[i].code = data.kcbh;
                    resultData[i].courseName = data.kcmc;
                }
            }
            obj.update(data);
            table.reload('tableResult', {data: resultData});
        })

        $("#search2").click(function () {
            var xsxh = $("#stuApply").attr("data-value");
            if (xsxh == undefined || xsxh == '') {
                U.fail("请选择申请学生")
                return false;
            }
            let kcxz = []
            $("#kcxz li.active").each((idx, value) => {
                kcxz.push($(value).attr("data-value"));
            })
            let courseName = $("input[name=course]").val();
            let _table = table;
            $.get("/credit/mr/getCourse", {
                xsxh: xsxh,
                kcxz: kcxz.join(","),
                course: courseName,
            }, function (res) {
                let data = [];
                if (res.code == 200) {
                    data = res.data
                } else {
                    U.fail("无数据");
                }
                _table.reload('materialTable', {data: data});
            })
        })
        // 重置
        $("#reset2").click(function () {
            table.reload('materialTable', {data: []});
            var p = $(this).parents('.j-search');
            p.find('li').show().removeClass('active');
            p.find('.allSelect').removeClass('cur');
            p.find('input').val('');
        })


        /* ************************** 设置结果 start ********************************** */

        // 申请课程
        let resultData = []

        table.render({
            elem: '#tableResult',
            // url: '/static/json/table/user.json', //数据接口
            data: resultData, //静态数据，真实数据用url接口
            cols: [
                [{
                    field: 'name',
                    title: '课程/项目名称',
                    align: 'center',
                    minWidth: 109
                }, {
                    field: 'hasScore',
                    title: '已获学分',
                    align: 'center',
                    minWidth: 89
                },
                    {
                        field: 'courseName',
                        title: '申请课程名称',
                        align: 'center',
                        minWidth: 109
                    },
                    {
                        field: 'code',
                        title: '申请课程编号',
                        align: 'center',
                        minWidth: 109
                    }, {
                    field: 'courseScore',
                    title: '申请课程成绩',
                    align: 'center',
                    minWidth: 109
                }, {
                    field: 'score',
                    title: '申请课程学分',
                    align: 'center',
                    minWidth: 109
                }]
            ],
            limits: [10, 20, 30, 50, 100],
            limit: 10,
            page: true,
            done: function (res, curr, count) {
                /* 需要合并栏目的数组信息 */
                var merge_column_infos = [{
                    field: "code",
                    index: 2
                }];
                /* 从第几行开始遍历合并 去除表头就是 1 咯 */
                var start_tr_index = 1;
                /* 查找到需要合并表格所有的 tr */
                var tr_s = $(".set-result .layui-table-view table").find("tr");
                var tr_r = $(".set-result .layui-table-fixed-r table").find("tr");
                /* 开始遍历需要合并的栏目 */
                for (var merge_item of merge_column_infos) {
                    var field = merge_item.field;
                    /* 需要合并栏目的索引 */
                    var index = merge_item.index;
                    /* 需要合并栏目的数量 */
                    var merge_num = 0;
                    /* 需要合并栏目的 td */
                    var merge_tds = [],
                        merge_tds1 = [],
                        merge_tds2 = [],
                        merge_tds3 = [],
                        merge_tds_r = [];
                    /* 开始遍历需要合并表格的所有 tr */
                    for (var i = start_tr_index; i < tr_s.length; i++) {
                        /* 当前 td */
                        var cur_td = tr_s.eq(i).find("td").eq(index);
                        var cur_td2 = tr_s.eq(i).find("td").eq(3);
                        var cur_td1 = tr_s.eq(i).find("td").eq(4);
                        var cur_td3 = tr_s.eq(i).find("td").eq(5);
                        var cur_td_r = tr_r.eq(i).find("td").eq(0);
                        var next_td = tr_s.eq(i + 1).find("td").eq(index);/* 下一个 td */
                        /*当前 td 的 text */
                        var cur_text = $(cur_td).text(); /* 下一个 td 的 text 当遍历到最后默认为空 */
                        var next_text = $(next_td).text(); /* 如果当前 td=下一个 td */
                        if (cur_text == next_text) {
                            /* 放入到合并 td 的集合中 */
                            merge_tds.push(cur_td); /* 需要合并的 td 数量加 1 */
                            merge_tds1.push(cur_td1);
                            merge_tds2.push(cur_td2);
                            merge_tds3.push(cur_td3);
                            merge_tds_r.push(cur_td_r);
                            merge_num++;
                        } else {
                            /* 如果 如果当前 td !=下一个 td 且要合并的 td 数量不等于 0 */
                            if (merge_num != 0) {
                                /* 第一个 td 合并 因为动态添加 rowspan 属性是向下延申 */
                                $(merge_tds[0]).attr("rowspan",
                                    merge_num + 1);
                                $(merge_tds1[0]).attr("rowspan",
                                    merge_num + 1);
                                $(merge_tds2[0]).attr("rowspan",
                                    merge_num + 1);
                                $(merge_tds3[0]).attr("rowspan",
                                    merge_num + 1);
                                $(merge_tds_r[0]).attr("rowspan",
                                    merge_num + 1);
                                var tdHeight = $(merge_tds3[0]).outerHeight(true);
                                $(merge_tds_r[0]).css({height: tdHeight})
                                /* 遍历所有的需要合并的 td 将他们的属性设置为 不可见 */
                                for (var d = 1; d < merge_tds.length; d++) {
                                    $(merge_tds[d]).addClass("layui-hide");
                                    $(merge_tds1[d]).addClass("layui-hide");
                                    $(merge_tds2[d]).addClass("layui-hide");
                                    $(merge_tds3[d]).addClass("layui-hide");
                                    $(merge_tds_r[d]).addClass("layui-hide");
                                }
                                /* 当前 td 属性也需要设置为不可见 */
                                $(cur_td).addClass("layui-hide");
                                $(cur_td1).addClass("layui-hide");
                                $(cur_td2).addClass("layui-hide");
                                $(cur_td3).addClass("layui-hide");
                                $(cur_td_r).addClass("layui-hide");
                            }
                            /*重置合并 td 数据 */
                            merge_num = 0;
                            merge_tds = [], merge_tds1 = [], merge_tds2 = [], merge_tds3 = [], merge_tds_r = [];
                        }
                    }
                }
            }
        });


        /* ************************** 设置结果 end ********************************** */

        /******************************** 选择学生 **************************/
        $("#stuApply").click(function () {
            if (role != 3) {
                table.reload("stuList");
                $(".dialog-wrap").show();
            }
        })

        function searchData() {
            var formValue = form.val('formStu');
            if (role == 0) {
                formValue["bzruid"] = user.uid;
            }
            return {
                fid: user.fid,
                nj: formValue.grade,
                yx: formValue.depth,
                zy: formValue.stuMajor,
                bj: formValue.stuClass,
                xm: formValue.xsxm,
                xh: formValue.xsxh,
                bzruid: formValue.bzruid
            };
        }

        function updateTable() {
            table.render({
                elem: '#stuList',
                url: '/basic/getStudent', //数据接口
                where: searchData(), //静态数据，真实数据用url接口
                parseData: function (res) {
                    if (res.code == 200) {
                        return {
                            "code": 0, //解析接口状态
                            "msg": res.msg, //解析提示文本
                            "count": res.data.totalCount, //解析数据长度
                            "data": res.data.data //解析数据列表
                        }
                    } else {
                        return {
                            "code": 1, //解析接口状态
                            "msg": "数据获取失败", //解析提示文本
                            "count": 0, //解析数据长度
                            "data": [] //解析数据列表
                        }
                    }
                },
                cols: [
                    [{
                        field: 'xsjbxx_xm',
                        title: '学生姓名',
                        align: 'center',
                        minWidth: 110
                    }, {
                        field: 'xsjbxx_xh',
                        title: '学生学号',
                        align: 'center',
                        minWidth: 100
                    }, {
                        field: 'xsjbxx_sznj',
                        title: '年级',
                        align: 'center',
                        minWidth: 80
                    }, {
                        field: 'xsjbxx_yxxx',
                        title: '系部',
                        align: 'center',
                        minWidth: 110
                    }, {
                        field: 'xsjbxx_zyxx',
                        title: '专业',
                        align: 'center',
                        minWidth: 110
                    }, {
                        field: 'xsjbxx_bjxx',
                        title: '班级',
                        align: 'center',
                        minWidth: 80
                    }, {
                        field: 'xsjbxx_xssfzx',
                        title: '是否在校',
                        align: 'center',
                        minWidth: 80
                    }, {
                        field: 'xsjbxx_xsdqzt',
                        title: '当前学生状态',
                        align: 'center',
                        minWidth: 80
                    }, {
                        title: "操作",
                        minWidth: 120,
                        align: "center",
                        fixed: 'right',
                        toolbar: "#tmplScoreEntry",
                    }
                    ]
                ],
                page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip'],
                    curr: 1
                },
                limit: 10,
            });
        }

        var checkedStu = '';
        table.on('tool(stuList)', function (obj) {
            var data = obj.data;
            $('.layui-table-view[lay-id="stuList"]  span[lay-event="cancel"]').attr('lay-event', 'sel').text('选择').css({color: '#1A79FF'});
            if (obj.event === 'cancel') {
                data.scoreEntry = 0;
                obj.update(data);
                obj.tr.find('td:last-child span[lay-event="cancel"]').attr('lay-event', 'sel').text('选择').css({color: '#1A79FF'});
            } else if (obj.event === 'sel') {
                data.scoreEntry = 1;
                obj.update(data)
                obj.tr.find('td:last-child span[lay-event="sel"]').attr('lay-event', 'cancel').text('取消').css({color: '#E85A5A'});
            }
            checkedStu = data;
        })

        // 查询
        $("#search3").click(function () {
            // 提交事件
            updateTable();
            return false;
        })
        // 重置
        $("#reset3").click(function () {
            setTimeout(function () {
                updateTable();
            }, 50)
        })
        // 取消
        $("#btnSelCancel,.dialog-close").click(function () {
            $(".dialog-wrap").hide();
        })
        // 确定
        $("#btnSelSure").click(function () {
            if (checkedStu == null || checkedStu == '') {
                U.fail("请选择申请学生")
                return false;
            }
            let xsxm = checkedStu.xsjbxx_xm
            let xsxh = checkedStu.xsjbxx_xh
            $("#stuApply").val(xsxm)
            $("#stuApply").attr("data-value", xsxh)
            $("#stuApply").attr("data-major", checkedStu.xsjbxx_zyxx)
            table.reload("materialTable", {data: []})
            table.reload("administration", {data: []})
            $("#score1").html(0);
            $("#score2").html(0);
            $(".dialog-wrap").hide();
            resultData = [];
            table.reload("tableResult", {data: resultData})
        })

        $("#cancel").click(function () {
            $("#reset1").click();
            $("#reset2").click();
            resultData = [];
            table.reload("tableResult", {data: resultData})
        })
        //是否允许提交
        var flag = true;
        $("#invigilateSure").click(function () {
            if (!flag) {
                U.fail("正在处理，请勿重复点击")
                return false;
            }

            var xsxh = $("#stuApply").attr("data-value");
            if (xsxh == undefined || xsxh == '') {
                U.fail("请选择申请学生")
                return false;
            }
            if (resultData == null || resultData.length == 0) {
                U.fail("请选择已修学分")
                return false;
            }
            if (checkedCourse == null || checkedCourse == undefined || checkedCourse == "") {
                U.fail("请选择申请课程")
                return false;
            }
            var score1 = $("#score1").html();
            var score2 = $("#score2").html();

            if (parseFloat(score1) < parseFloat(score2)) {
                U.fail("已选择已修学分必须大于选择的申请课程学分")
                return false;
            }

            let param = {
                xnxq: curxnxq,
                credit: JSON.stringify(resultData),
                course: JSON.stringify(checkedCourse),
                xsxm: $("#stuApply").val(),
                xsxh: xsxh,
            }
            let _table = table;
            flag = false;
            $.post("/credit/mr/submitApply", param, function (res) {
                if (res.code == 200) {
                    $("#score1").html(0);
                    $("#score2").html(0);
                    U.success("提交成功", 2000)
                    setTimeout(function () {
                        $("#search1").click();
                        $("#search2").click();

                        resultData = [];
                        _table.reload("tableResult", {data: resultData})
                        _table.reload('materialTable', {page: {cuur: 1}})
                        _table.reload('administration', {page: {cuur: 1}})
                    }, 2000)
                    setTimeout(function () {
                        flag = true;
                    }, 3000)
                } else {
                    flag = true;
                    U.fail(res.msg)
                }
            })
        })

        function addData(resultData, element, checkedCourse) {
            let flag = true;
            for (let i = 0; i < resultData.length; i++) {
                if (resultData[i].id === element.formUserId) {
                    //当前元素已存在
                    flag = false;
                    return flag;
                }
            }
            //当前元素已存在 不处理
            if (!flag) {
                return;
            }
            var json = {
                id: element.formUserId,
                formUserId: element.formUserId,
                alias: element.alians,
                projectName: element.projectName,
                xnxq: element.xnxq,
                creditType: element.creditType,

                name: element.projectName,
                score: element.score,
                courseName: checkedCourse.kcmc,
                hasScore: element.score,
                courseScore: checkedCourse.kccj,
                code: checkedCourse.kcbh,
                jxbbh: checkedCourse.jxbxbh,
                xbx: checkedCourse.xbx,
                scoreEntry: 1,
            }
            resultData.push(json);
        }
    })
</script>

</html>