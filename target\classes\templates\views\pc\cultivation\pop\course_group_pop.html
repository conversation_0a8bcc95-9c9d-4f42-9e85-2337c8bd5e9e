<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <title>课程列表</title>
    <link rel="stylesheet" th:href="@{../css/cultivation/jquery.searchableSelect.css}"/>
    <link rel="stylesheet" th:href="@{../css/cultivation/newStyle.css}"/>
    <link rel="stylesheet" th:href="@{../plugin/layui/css/layui.css}"/>
    <style>
        html, body {
            height: 100%;
        }

        .layui-laypage .layui-laypage-curr .layui-laypage-em {
            background-color: #4C88FF !important;
        }

        .layui-laypage .layui-laypage-limits {
            display: none !important;
        }

        .layui-laypage .layui-laypage-curr .layui-laypage-em {
            background-color: #4C88FF !important;
        }

        .layui-form-radio > i:hover, .layui-form-radioed > i {
            color: #4C88FF !important;
        }

        body .layui-layer-btn .layui-layer-btn0 {
            background: #4C88FF;
            border: 1px solid #4C88FF;
            border-radius: 20px;
            display: inline-block;
            width: 60px;
            height: 34px;
            text-align: center;
            line-height: 34px;
            font-size: 14px;
            color: #FFFFFF;
        }

        body .layui-layer-btn .layui-layer-btn1 {
            background: #FFFFFF;
            border: 1px solid #94C1FF;
            border-radius: 20px;
            display: inline-block;
            width: 60px;
            height: 34px;
            text-align: center;
            line-height: 34px;
            font-size: 14px;
            color: #4C88FF;
        }

        .opt_relevance .opt_rece_uls {
            margin-left: 8px;
        }

        .opt_relevance .opt_rece_uls .opt_rece_lis {
            display: inline-block;
            height: 20px;
            margin: 16px 40px 0 0;
            font-size: 14px;
            color: #484F5D;
            line-height: 20px;
        }

        .opt_relevance .clearGroup {
            color: #4C88FF !important;
            cursor: pointer;
        }

        .layui-table-tips-main {
            display: none
        }

        .layui-table-tips-c {
            display: none
        }

        .layui-table-grid-down {
            display: none
        }
    </style>
</head>
<body>
<div class="popDiv popMove wid840" style="width: 1071px;max-height: 720px;">
    <div class="popBody" id="popScroll" style="max-height: 680px;">
        <div class="popSearch clearAfter" style="padding: 20px 0 0 24px;">
            <div class="popSearch_row fl">
                <div class="popSearch_name">
                    <div class="popSearch_table">
                        <div class="popSearch_cell"><span>编组名称</span></div>
                    </div>
                </div>
                <div class="popSearch_per">
                    <select class="qselect" name="jxbbz_bzmc">
                        <option value="">请选择</option>
                    </select>
                </div>
            </div>
            <div class="popSearch_row fl">
                <div class="popSearch_name">
                    <div class="popSearch_table">
                        <div class="popSearch_cell"><span>编组id</span></div>
                    </div>
                </div>
                <div class="popSearch_per">
                    <input type="text" placeholder="编组id" name="jxbbz_bzid" class="popSearch_input">
                </div>
            </div>
            <div class="popSearch_row fl">
                <div class="popSearch_name">
                    <div class="popSearch_table" style="width: 106px;">
                        <div class="popSearch_cell"><span>教学班编组类型</span></div>
                    </div>
                </div>
                <div class="popSearch_per">
                    <select class="qselect" name="jxbbz_bzlx">
                        <option value="">请选择</option>
                    </select>
                </div>
            </div>
            <div class="popSearch_btns">
                <div class="popSearch_clear fr clearSearch">清空筛选</div>
                <div class="popSearch_search_btn fr" lay-filter="search" lay-submit>筛选</div>
            </div>
            <div class="popSearch_row fl" style="width: 960px;margin-bottom: 0;margin-left: 10px;display: none;">
                当前所属编组信息：
            </div>
            <div class="opt_relevance" style="width: 960px;display: none;">
                <ul class="opt_rece_uls" th:if="${teachClassGroupForm}">
                    <li class="opt_rece_lis" th:text="'开课学期：'+${teachClassGroupForm?.jxbbz_kkxq}"></li>
                    <li class="opt_rece_lis" th:text="'编组名称：'+${teachClassGroupForm?.jxbbz_bzmc}"></li>
                    <li class="opt_rece_lis" th:text="'编组类型：'+${teachClassGroupForm?.jxbbz_bzlx}"></li>
                    <li class="opt_rece_lis" th:text="'已有开课信息数：'+${teachClassGroupForm?.jxbbz_yykkxxs}"></li>
                    <li class="opt_rece_lis" th:text="'是否按组录入成绩：'+${teachClassGroupForm?.jxbbz_sfazlrcj}"></li>
                    <li class="opt_rece_lis clearGroup">清空教学班编组信息</li>
                </ul>
            </div>
        </div>
        <div class="popSearch_cont">
            <table lay-filter="courseGroupTable" class="layui-table" id="courseGroupTable">
            </table>
        </div>
    </div>
    <div class="layui-layer-btn"><a class="layui-layer-btn1">取消</a><a class="layui-layer-btn0">确定</a></div>
</div>
<script th:src="@{../js/jquery-3.3.1.min.js}"></script>
<script th:src="@{../js/jquery.nicescroll.min.js}"></script>
<script th:src="@{../js/cultivation/jquery.searchableSelect.js}"></script>
<script th:src="@{../plugin/layui/layui.js}"></script>
<script th:src="@{../js/cultivation/select_data.js}"></script>
<script th:inline="javascript">
    var fid = [[${fid}]];
    var formUserId = [[${formUserId}]];
    var formId = [[${formId}]];
    $('.qselect').click();

    //弹窗居中
    function MoveFixed(userClass) {
        $(userClass).css({
            top: function () {
                return 0;
            }, left: function () {
                return ($(window).width() - $(this).width()) / 2;
            }
        });

    }

    window.onresize = function () {
        MoveFixed('.popMove')
    }
    MoveFixed('.popMove')

    function scrollBox(userID) {
        $(userID).niceScroll({
            cursorborder: "",
            cursorwidth: 8,
            cursorcolor: "#CFD8E6",
            boxzoom: false,
            autohidemode: true
        });
        $(userID).getNiceScroll().resize(); //检测滚动条是否重置大小（当窗口改变大小时）
    }

    //scrollBox("#popScroll");
    document.domain = "chaoxing.com";
    var table = "", insTb = "", form = "";
    let groupId = [];
    layui.use('table', function () {
        table = layui.table;
        form = layui.form;
        insTb = table.render({
            elem: '#courseGroupTable',
            url: '../processData/getTeachClassGroupData',
            where: {
                jxbbz_kkxq: [[${courseInformationForm?.kkxxb_kkxq}]],
                formUserId: formUserId
            },
            page: true,
            cols: [
                [
                    {type: 'checkbox', field: 'id', fixed: 'left'},
                    {field: 'jxbbz_kkxq', title: '开课学期'},
                    {field: 'jxbbz_bzid', title: '编组id'},
                    {field: 'jxbbz_bzmc', title: '编组名称'},
                    {field: 'jxbbz_bzlx', title: '编组类型'},
                    {field: 'jxbbz_sfazpk', title: '是否按组排课'},
                    {field: 'jxbbz_sfazxk', title: '是否按组选课'},
                    {field: 'jxbbz_sfazlrcj', title: '是否按组录入成绩'},
                    {field: 'rowInfo', hide: true}
                ]
            ],
            done: function (res) {
                scrollBox("#popScroll");
                layui.each(res.data, function (index, item) {
                    if (item.jxbbz_bzid && [[${courseInformationForm?.kkxxb_jxbbzid}]].indexOf(item.jxbbz_bzid) !== -1) {
                        groupId.push(item.rowInfo.formUserId);
                        //这里才是真正的有效勾选
                        res.data[index]["LAY_CHECKED"] = 'true';
                        //找到对应数据改变勾选样式，呈现出选中效果
                        const idx = res.data[index]['LAY_TABLE_INDEX'];
                        $('.layui-table tr[data-index=' + idx + '] input[type="checkbox"]').prop('checked', true);
                        $('.layui-table tr[data-index=' + idx + '] input[type="checkbox"]').next().addClass('layui-form-checked');
                    }
                })
            },
            text: {
                none: '当前学期在对应筛选条件下无可选数据'  //自定义无数据时的提示文本
            }
        });
        table.on('checkbox(courseGroupTable)', function (obj) {
            groupId.push(obj.data.rowInfo.formUserId);
            groupId = $.unique(groupId);
            if (!obj.checked) {
                groupId = groupId.filter(function (item) {
                    return item !== obj.data.rowInfo.formUserId;
                });
            }
            check();
        });
    });
    $(".popSearch_search_btn").click(function () {
        var jxbbz_bzmc = $("select[name='jxbbz_bzmc']").val();
        var jxbbz_bzid = $("input[name='jxbbz_bzid']").val();
        var jxbbz_bzlx = $("select[name='jxbbz_bzlx']").val();
        var field = {jxbbz_bzmc: jxbbz_bzmc, jxbbz_bzid: jxbbz_bzid, jxbbz_bzlx: jxbbz_bzlx};
        insTb.reload({where: field, page: {curr: 1}});
    })
    $(".layui-layer-btn0").click(function () {
        if (!check()) {
            return false;
        }
        if (groupId.length === 0) {
            window.parent.postMessage(JSON.stringify({action: 1}), "*");
            return false;
        }
        $.ajax({
            url: '../processData/bindCourseGroup',
            data: {fid: fid, formUserId: groupId.join(","), courseId: formUserId},
            dataType: 'json',
            type: 'get',
            success: function (data) {
                if (data.status) {
                    layer.load(1, {shade: [0.1, '#fff']});
                    setTimeout(function () {
                        layer.closeAll('loading');
                        window.parent.postMessage(JSON.stringify({action: 1}), "*");
                    }, 2000);
                } else {
                    layer.alert("程序异常！");
                }
            }
        })
    })
    $(".layui-layer-btn1").click(function () {
        window.parent.postMessage(JSON.stringify({action: 1}), "*");
    })
    $(".clearSearch").click(function () {
        location.reload();
    })
    $(".clearGroup").click(function () {
        groupId = [];
        $('tr input[type="checkbox"]').prop('checked', false); // 取消选中
        form.render();
        $.ajax({
            url: '../processData/clearCourseGroup',
            data: {fid: fid, courseFormUserId: formUserId, formUserId: "[[${groupId}]]"},
            dataType: 'json',
            type: 'get',
            success: function (data) {
                if (data.status) {
                    $(".opt_rece_uls").hide();
                } else {
                    layer.alert("程序异常！");
                }
            }
        })
    })

    function check() {
        let checkData = table.checkStatus('courseGroupTable').data;
        const count = $.grep(checkData, function (obj) {
            return obj.jxbbz_sfazlrcj === "是";
        }).length;
        if (count > 1) {
            layer.alert('一条开课信息不能存在于多个录成绩的编组，请选择其他编组或修改编组属性后重新添加', { icon: 2, title: '',btn: []})
            return false;
        }
        return true;
    }
</script>
</body>
</html>
