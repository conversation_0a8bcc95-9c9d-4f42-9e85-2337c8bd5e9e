<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv='X-UA-Compatible' content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <title>系统字段</title>
    <link rel="stylesheet" th:href="@{~/css/graduate/global.css}">
    <link rel="stylesheet" th:href="@{~/plugin/layui/css/layui.css}">
    <link rel="stylesheet" th:href="@{~/css/graduate/reset.css}">
    <link rel="stylesheet" th:href="@{~/css/graduate/dialog.css}">
    <script th:src="@{~/layui/layui.js}"></script>
    <script th:src="@{~/js/jquery-3.6.0.min.js}" type="text/javascript" charset="utf-8"></script>
    <style>
        #systemFields {
            width: 800px;
            height: 667px;
            position: fixed;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%)
        }

        .field-search {
            display: flex;
            align-items: center;
            margin-bottom: 10px;

        }

        .field-search .input-search {
            width: 240px;
            border: 1px solid #E5E6EB;
            border-radius: 4px 0 0 4px;
            padding: 8px 10px 8px 30px;
            background: url('/images/graduate/search-icon.png') no-repeat 10px center;
        }

        .field-search .input-search::placeholder {
            color: #86909C;
        }

        .field-search .btn-search {
            background-color: #4D88FF;
            color: #fff;
            border: 1px solid #4D88FF;
            border-radius: 0 4px 4px 0;
            padding: 9px 12px;
            cursor: pointer;
        }

        .field-left,
        .field-checked {
            flex: 1;

        }

        .field-item,
        .field-checked-item {
            max-height: 430px;
            overflow-y: auto;
        }

        .field-item ul,
        .field-checked ul {

            border-radius: 5px;
            border: 1px solid #E5E6EB;

        }

        .field-item ul li,
        .field-checked ul li {
            padding: 10px 20px;
            /* border-bottom: 1px solid #E5E6EB; */
            height: 40px;
            line-height: 40px;
            cursor: pointer;

        }

        .field-item ul li:nth-child(2n),
        .field-checked ul li:nth-child(2n) {
            background-color: #FAFBFC;
        }

        .field-item ul li.active,
        .field-checked ul li.active {
            background-color: #E1EBFF;
            border-radius: 4px;
        }


        .field-item ul li i,
        .field-checked ul li i {
            width: 20px;
            height: 20px;
            line-height: 40px;
            height: 40px;
            display: inline-block;
            vertical-align: center;

            background-size: contain;

            float: right;
            cursor: pointer;
        }

        .del-icon {
            background: url('/images/graduate/graduate-reduce-btn-fill.png') no-repeat center;
        }

        .add-icon {
            background: url('/images/graduate/graduate-add-btn-fill.png') no-repeat center;
        }

        .check-icon {
            background: url('/images/graduate/graduate-success-fill.png') no-repeat center;
        }

        .field-item ul li input,
        .field-checked ul li input {
            border: 1px solid #BCBCC5;
            border-radius: 4px;
            padding: 5px;
            margin: 0px 5px;
            width: 30px;
        }

        .checked-title {
            height: 50px;
            line-height: 50px;
            background-color: #4D88FF;
            border-radius: 4px 4px 0px 0px;
            text-align: center;
            color: #fff;
        }


    </style>
</head>
<body>
<div class="masker"></div>
<div class="dialog" id="systemFields">
    <div class="dialog-title">系统字段</div>
    <div class="dialog-con" style=" height: 480px">
        <div style="display: flex;gap: 5px;">
            <div class="field-left">
                <div class="field-search">
                    <div><input type="text" class="input-search" placeholder="搜索系统字段"></div>
                    <div>
                        <button class="btn-search">搜索</button>
                    </div>
                </div>
                <div class="field-item">
                    <ul id="systemFieldList">

                    </ul>
                </div>
            </div>


            <div class="field-checked">
                <div class="checked-title">已选系统字段</div>
                <div class="field-checked-item">
                    <ul id="checkedField">
                    </ul>
                </div>
            </div>


        </div>
    </div>
    <div class="dialog-btn">
        <button class="pu-cancel">取消</button>
        <button class="pu-sure"
                id="invigilateSure">确定
        </button>
    </div>
</div>
</body>
<script th:src="@{~/js/my.util.js}"></script>
<script th:inline="javascript">
    var zybh=[[${zybh}]]
    var nj=[[${nj}]]
    var queryId=[[${queryId}]]||'';
    const _VR_ = [[${_VR_}]] || '';
    const fid = [[${fid}]] || '';
    const uid = [[${uid}]] || '';
    $(function () {
        getFiledList();
    })
    $(".btn-search").click(function () {
        var tempValue = $(".field-search div input").val();
        getData(tempValue);
    })


    $('.input-search').bind('keypress', function (event) {
        if (event.keyCode == "13") {
            return getData($(this).val());
        }
    })


    function getData(tempValue) {
        if (tempValue == undefined || tempValue == "") {
            $("#systemFieldList li").each((k, v) => {
                $(v).show()
            })
            return false;
        }
        $("#systemFieldList li").each((k, v) => {
            var name = $(v).attr("data-name");
            if (name == undefined || name == "") {
                return
            }
            if (name.indexOf(tempValue) > -1) {
                $(v).show()
            } else {
                $(v).hide()
            }
        })
    }


    function getFiledList() {
        $.get("/graduate/rule/list","", function (res) {
            if (res.code = 200) {
                var data = res.data;
                var html = '<li id="totalItems">全部（' + data.length + '项）</li>\n';
                for (let i = 0; i < data.length; i++) {
                    if (data[i].bytjzd_bytjmc.indexOf("<") > 0 || data[i].bytjzd_bytjmc.indexOf("≤") > 0 || data[i].bytjzd_bytjmc.indexOf(">") > 0 || data[i].bytjzd_bytjmc.indexOf("≥") > 0) {
                        html += '<li data-id="' + data[i].rowInfo.formUserId + '" data-name="' + data[i].bytjzd_bytjmc + '"  data-method="' + data[i].bytjzd_jsfs + '"><span>' + data[i].bytjzd_bytjmc + ' <input type="text" disabled /></span></li>\n'
                    } else if (data[i].bytjzd_bytjmc.indexOf("_") > 0) {
                        var split = data[i].bytjzd_bytjmc.split("_");
                        html += '<li data-id="' + data[i].rowInfo.formUserId + '" data-name="' + data[i].bytjzd_bytjmc + '"  data-method="' + data[i].bytjzd_jsfs + '"><span>' + split[0] + ' <input type="text" disabled />' + split[1] + '</span></li>\n'
                    } else if (data[i].bytjzd_jsfs == "统计数据") {
                        html += '<li data-id="' + data[i].rowInfo.formUserId + '" data-name="' + data[i].bytjzd_bytjmc + '"  data-method="' + data[i].bytjzd_jsfs + '"><span>' + data[i].bytjzd_bytjmc + '≥ ';
                        if (data[i].bytjzd_sffzy == "否") {
                            html += data[i].bytjzd_dbz
                        } else {
                            html += '<input type="text" disabled />';
                        }
                        html += '</span></li>\n'
                    } else if (data[i].bytjzd_jsfs == "判断固定值") {
                        html += '<li data-id="' + data[i].rowInfo.formUserId + '" data-name="' + data[i].bytjzd_bytjmc + '"  data-method="' + data[i].bytjzd_jsfs + '"><span>' + data[i].bytjzd_bytjmc + " " + data[i].bytjzd_dbz2 + ' 次数 ≥';
                        if (data[i].bytjzd_sffzy == "否") {
                            html += data[i].bytjzd_dbpc
                        } else {
                            html += '<input type="text" disabled />';
                        }
                        html += '</span></li>\n';
                    } else if (data[i].bytjzd_jsfs == "存在记录") {
                        html += '<li data-id="' + data[i].rowInfo.formUserId + '" data-name="' + data[i].bytjzd_bytjmc + '"  data-method="' + data[i].bytjzd_jsfs + '"><span>' + data[i].bytjzd_dbz3 + " " + data[i].bytjzd_bytjmc + ' ≥';
                        if (data[i].bytjzd_sffzy == "否") {
                            html += data[i].bytjzd_dbpc
                        } else {
                            html += '<input type="text" disabled />';
                        }
                        html += '</span></li>\n';
                    } else {
                        html += '<li data-id="' + data[i].rowInfo.formUserId + '" data-name="' + data[i].bytjzd_bytjmc + '"  data-method="' + data[i].bytjzd_jsfs + '"><span>' + data[i].bytjzd_bytjmc + '</span></li>\n'
                    }
                }

                $("#systemFieldList").html(html)
                getRuleList()
            } else {
                U.fail("请维护学业条件字典表")
            }
        })
    }

    function getRuleList() {
        $.get("/graduate/rule/getRule",  {zybh:zybh,nj:nj}, function (res) {
            if (res.code == 200) {
                var data = res.data
                for (let i = 0; i < data.length; i++) {
                    $("#systemFieldList li[data-id=\"" + data[i].formUserId + "\"]").append('<i class="check-icon"></i>')
                    var html = ""
                    if (data[i].ruleName.indexOf("_") > 0) {
                        var split = data[i].ruleName.split("_");
                        html = '<li data-id="' + data[i].formUserId + '" data-name="' + data[i].ruleName + '"  data-method="' + data[i].calMethod + '"><span>' + split[0] + ' <input type="text" value="' + data[i].score + '"/>' + split[1] + '</span><i class="del-icon"></i></li>'
                    } else if(data[i].calMethod!="系统内置"&&data[i].score==""){
                        html = '<li data-id="' + data[i].formUserId + '" data-name="' + data[i].ruleName + '" data-method="' + data[i].calMethod + '"><span>' + data[i].showName + '</span><i class="del-icon"></i></li>'
                    }else  if(data[i].calMethod!="系统内置"&&data[i].score!=""){
                        html = '<li data-id="' + data[i].formUserId + '" data-name="' + data[i].ruleName + '" data-method="' + data[i].calMethod + '"><span>' + data[i].showName + '<input type="text" value="' + data[i].score + '"></span><i class="del-icon"></i></li>'
                    }else {
                        html = '<li data-id="' + data[i].formUserId + '" data-name="' + data[i].ruleName + '" data-method="' + data[i].calMethod + '"><span>' + data[i].ruleName + '<input type="text" value="' + data[i].score + '"></span><i class="del-icon"></i></li>'
                    }

                    $("#checkedField").append(html);
                }
            }
        })
    }

    $("#invigilateSure").click(function () {
        var data = []
        $(".field-checked").find("li").each((idx, item) => {
            var json = {}
            var html = $(item).html();
            if (html.indexOf("input") > 0) {
                var val = $(item).find("input").val();
                if (val === undefined || val === "") {
                    U.fail("请填写【" + $(item).attr("data-name") + "】")
                    return false;
                } else {
                    json["score"] = val;
                }
            }
            json["formUserId"] = $(item).attr("data-id");
            json["fid"] = fid;
            json["ruleName"] = $(item).attr("data-name");
            json["calMethod"] = $(item).attr("data-method");
            json["zybh"] = zybh;
            json["grade"] = nj;
            json["showName"] = $(item).children("span").text();
            data.push(json)
        })
        U.ajax({
            type: 'post',
            url: "/graduate/rule/saveOrUpdate?queryId="+queryId,
            data: JSON.stringify(data),
            contentType: 'application/json; charset=utf-8',
            dataType: 'json',
            success: function (res) {
                if (res.code == 200) {
                    U.success("保存成功")
                    setTimeout(function () {
                        U.closePop()
                    }, 2000)
                } else {
                    U.fail(res.msg)
                }
            },
            error: function (res) {
                console.log(res)
                U.fail("系统繁忙")
            }
        })
    })


    function zhzs(value) {
        value = value.replace(/[^\d]/g, '').replace(/^0{1,}/g, '');
        if (value != '') {
            value = parseFloat(value);
        }
        return value;
    }

    $(".pu-cancel").click(function () {
        U.closePop()
    })

    layui.use(['jquery'], function () {
        var $ = layui.jquery;
        // 切换 active
        $(".field-item ul").on("click", "li:not(:first-child)", function () {
            var $li = $(this);
            $(".field-item ul li").removeClass("active").find("i.add-icon").remove();
            $li.addClass("active");
            if (!$li.find("i.check-icon").length) {
                $li.append('<i class="add-icon"></i>');
            }
        });

        // 添加
        $(".field-item ul").on("click", ".add-icon", function (e) {
            e.stopPropagation();
            var $li = $(this).closest("li");
            var text = $li.html();
            text = text.replace("disabled", "")
            $(".field-checked ul").append('<li data-id="' + $li.attr("data-id") + '" data-name="' + $li.attr("data-name") + '"  data-method="' + $li.attr("data-method") + '">' + text.replace('add-icon', 'del-icon').replace('<i class="check-icon"></i>', '') + '</li>');
            $li.find("i.add-icon").remove();
            $li.append('<i class="check-icon"></i>');
        });

        // 删除
        $(".field-checked ul").on("click", ".del-icon", function () {
            var $li = $(this).closest("li");
            var id = $li.attr("data-id")
            $(".field-item ul li").each(function () {
                if ($(this).attr("data-id") === id) {
                    $(this).find("i.check-icon").remove();
                }
            });
            $li.remove();
        });

    })
</script>
</html>