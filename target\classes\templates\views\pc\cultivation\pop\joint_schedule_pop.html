<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>开课信息</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/courseInformation.css'}">
</head>
<body>
<div class="main">
    <div id="courseInformation">
        <div class="popup-con">
            <div class="content">
                <div class="sel-item">
                    <div class="sel-title"><span></span>联排节次</div>
                    <div class="sel" style="margin-right:0;">
                        <div class="select-input">
                            <input type="text"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bottom">
            <button type="reset" class="layui-btn exam-cancle">
                取消
            </button>
            <button class="layui-btn exam-sure">确定</button>
        </div>
    </div>
</div>

</body>
<script th:src="${_CPR_+_VR_+'/js/jquery-1.11.3.min.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/my.util.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/cultivation/common.js'}"></script>
<script th:inline="javascript">
    document.domain = "chaoxing.com";
    const _VR_ = [[${_VR_}]];
    let queryId = [[${formTopBtnVO.queryId}]];
    const formId = [[${formTopBtnVO.formId}]];
    const fid = [[${formTopBtnVO.fid}]];
    const uid = [[${formTopBtnVO.uid}]];
    const selectTotal = [[${formTopBtnVO.selectTotal}]];
    $(".exam-cancle").click(function () {
        window.parent.postMessage(JSON.stringify({action: 1}), "*");
    })

    $(".exam-sure").click(function () {
        let festivals = $(".select-input input").val();
        if (isNaN(festivals)) {
            U.fail("联排节次必须为数字");
            return false;
        }
        if (!festivals) {
            U.fail("联排节次不能为空");
            return false;
        }
        location.href = _VR_ + "/api/form-btn/gm/outpost.popup?code=2T10013&formId=" + formId + "&fid=" + fid + "&queryId=" + queryId + "&uid=" + uid + "&selectTotal=" + selectTotal + "&festivals=" + festivals;
    });

</script>
</html>