<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <title>课程列表</title>
    <link rel="stylesheet" th:href="@{../css/cultivation/jquery.searchableSelect.css}"/>
    <link rel="stylesheet" th:href="@{../css/cultivation/newStyle.css}"/>
    <link rel="stylesheet" th:href="@{../plugin/layui/css/layui.css}"/>
    <style>
        html, body {
            height: 100%;
        }

        .layui-laypage .layui-laypage-curr .layui-laypage-em {
            background-color: #4C88FF !important;
        }

        .layui-laypage .layui-laypage-limits {
            display: none !important;
        }

        .layui-laypage .layui-laypage-curr .layui-laypage-em {
            background-color: #4C88FF !important;
        }

        .layui-form-radio > i:hover, .layui-form-radioed > i {
            color: #4C88FF !important;
        }

        body .layui-layer-btn .layui-layer-btn0 {
            background: #4C88FF;
            border: 1px solid #4C88FF;
            border-radius: 20px;
            display: inline-block;
            width: 60px;
            height: 34px;
            text-align: center;
            line-height: 34px;
            font-size: 14px;
            color: #FFFFFF;
        }

        body .layui-layer-btn .layui-layer-btn1 {
            background: #FFFFFF;
            border: 1px solid #94C1FF;
            border-radius: 20px;
            display: inline-block;
            width: 60px;
            height: 34px;
            text-align: center;
            line-height: 34px;
            font-size: 14px;
            color: #4C88FF;
        }
    </style>
</head>
<body>
<div class="popDiv popMove wid840" style="width: 1071px;max-height: 630px;">
    <div class="popBody" id="popScroll" style="max-height: 580px;">
        <div class="popSearch clearAfter" style="padding: 20px 0 0 24px;">
            <div class="popSearch_row fl">
                <div class="popSearch_name">
                    <div class="popSearch_table">
                        <div class="popSearch_cell"><span>网络课程名称</span></div>
                    </div>
                </div>
                <div class="popSearch_per"><input type="text" placeholder="网络课程名称" name="moocName"
                                                  class="popSearch_input"></div>
            </div>
            <div class="popSearch_row fl">
                <div class="popSearch_name">
                    <div class="popSearch_table">
                        <div class="popSearch_cell"><span>网络课程编号</span></div>
                    </div>
                </div>
                <div class="popSearch_per"><input type="text" placeholder="网络课程编号" name="moocId"
                                                  class="popSearch_input"></div>
            </div>
            <div class="popSearch_btns">
                <div class="popSearch_clear fr">清空筛选</div>
                <div class="popSearch_search_btn fr" lay-filter="search" lay-submit>筛选</div>
            </div>
        </div>
        <div class="popSearch_cont">
            <table lay-filter="moocTable" class="layui-table" id="moocTable">
            </table>
        </div>
    </div>
    <div class="layui-layer-btn"><a class="layui-layer-btn0">确定</a><a class="layui-layer-btn1">关闭</a></div>
</div>
<script th:src="@{../js/jquery-3.3.1.min.js}"></script>
<script th:src="@{../js/jquery.nicescroll.min.js}"></script>
<script th:src="@{../js/cultivation/jquery.searchableSelect.js}"></script>
<script th:src="@{../plugin/layui/layui.js}"></script>
<script th:inline="javascript">
    var fid = [[${fid}]];
    var formUserId = [[${formUserId}]];
    var formId = [[${formId}]];

    //弹窗居中
    function MoveFixed(userClass) {
        $(userClass).css({
            top: function () {
                return 0;
            }, left: function () {
                return ($(window).width() - $(this).width()) / 2;
            }
        });

    }

    window.onresize = function () {
        MoveFixed('.popMove')
    }
    MoveFixed('.popMove')

    function scrollBox(userID) {
        $(userID).niceScroll({
            cursorborder: "",
            cursorwidth: 8,
            cursorcolor: "#CFD8E6",
            boxzoom: false,
            autohidemode: true
        });
        $(userID).getNiceScroll().resize(); //检测滚动条是否重置大小（当窗口改变大小时）
    }

    //scrollBox("#popScroll");
    document.domain = "chaoxing.com";
    var table = "", insTb = "", moocId = "";
    layui.use('table', function () {
        table = layui.table;
        insTb = table.render({
            elem: '#moocTable',
            url: '../processData/getMoocCourseData',
            page: true,
            cols: [
                [
                    {type: 'radio', field: 'id', fixed: 'left'},
                    {field: 'moocId', title: '网络课程编号'},
                    {field: 'moocName', title: '网络课程名称'},
                    {field: 'kcId', title: '课程代码'},
                    {field: 'moocTeacher', title: '网络课程创建教师'},
                    {field: 'createDate', title: '创建时间'}
                ]
            ],
            done: function () {
                scrollBox("#popScroll");
            }
        });
        table.on('row(moocTable)', function (obj) {
            moocId = obj.data.moocId;
        });
    });
    $(".popSearch_search_btn").click(function () {
        var moocName = $("input[name='moocName']").val();
        var moocId = $("input[name='moocId']").val();
        var field = {moocId: moocId, moocName: moocName};
        insTb.reload({where: field, page: {curr: 1}});
    })
    $(".layui-layer-btn0").click(function () {
        $.ajax({
            url: '../processData/modifyMooc',
            data: {fid: fid, formId: formId, formUserId: formUserId, moocId: moocId},
            dataType: 'json',
            type: 'get',
            success: function (data) {
                if (data.status) {
                    window.parent.postMessage(JSON.stringify({action: 1}), "*");
                } else {
                    layer.alert("程序异常！");
                }
            }
        })
    })
    $(".layui-layer-btn1").click(function () {
        window.parent.postMessage(JSON.stringify({action: 1}), "*");
    })
    $(".popSearch_clear").click(function () {
        location.reload();
    })
</script>
</body>
</html>
