<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>正考成绩管理</title>
  <link rel="stylesheet" href="/score/css/global.css"/>
  <link rel="stylesheet" href="/score/css/common.css"/>
  <link rel="stylesheet" href="/score/css/reset.css"/>
  <link rel="stylesheet" href="/score/css/index.css"/>
  <link rel="stylesheet" href="/layui/css/layui.css"/>
  <style>
    .main {
      padding: 0;
    }
    .layui-form-select dl dd.layui-this {
      background-color: #1A79FF;
      color: #fff;
    }
  </style>
</head>
<body>

  <div class="main">
    <div class="content">
      <div class="filter-box">
        <form class="layui-form" action="">
          <div class="layui-form-item">
            <label class="layui-form-label">学年学期</label>
            <div class="layui-input-block w240">
              <select name="typeCover1" id="xnxq" lay-filter="typeCover1" lay-verify="required">
                <option value="">请选择</option>
                <option th:each="xnxq:${xnxqList}" th:if="${xnxq.xnxq_sfdqxq == '是'}" selected="selected" th:value="${xnxq.xnxq_xnxqh}" th:text="${xnxq.xnxq_xnxqh}"></option>
                <option th:each="xnxq:${xnxqList}" th:if="${xnxq.xnxq_sfdqxq == '否'}" th:value="${xnxq.xnxq_xnxqh}" th:text="${xnxq.xnxq_xnxqh}"></option>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label">课程名称</label>
            <div class="layui-input-block w240">
              <select name="typeCover2" id="courseSearch" lay-filter="typeCover2" lay-verify="required" lay-search="courseSearch">
                <option value="">请选择</option>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label">教学班名称</label>
            <div class="layui-input-block w240">
              <select name="typeCover3" id="classSearch" lay-filter="typeCover3" lay-verify="required" lay-search="classSearch">
                <option value="">请选择</option>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label">授课教师</label>
            <div class="layui-input-block w240">
              <select name="typeCover4" id="teacherSearch" lay-filter="typeCover4" lay-verify="required" lay-search="teacherSearch">
                <option value="">请选择</option>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label">班级名称</label>
            <div class="layui-input-block w240">
              <select name="typeCover5" id="constituteClassSearch" lay-filter="typeCover5" lay-verify="required" lay-search="constituteClassSearch">
                <option value="">请选择</option>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <div class="layui-input-block w240">
              <div class="btns">
                <div class="search">查询</div>
                <div class="resetting">重置</div>
              </div>
            </div>
          </div>
      </div>
      <div class="btn-lists">
        <div class="btn btn1">数据更新</div>
        <div class="btn btn2">批量配置计算规则</div>
        <div class="btn btn3">批量导出教学班成绩</div>
      </div>

      <div class="table">
      	<input type="hidden" id="classCourseId">
        <table class="layui-table" id="materialTable" lay-filter="materialTable"></table>
      </div>

    </div>
  </div>




  <!-- 添加 -->
  <div id="ruleAddPoups" class="addPoups popup">
    <div class="title">
      <div class="name">成绩规则</div>
      <div class="close"></div>
    </div>
    <div class="popup-con">
      <div class="inform">
        <div class="lable">
          <div class="name">课程名称：</div>
          <div class="tit">语文</div>
        </div>
        <div class="lable">
          <div class="name">教学班名称：</div>
          <div class="tit">语文1班</div>
        </div>
      </div>

      <div class="layui-form addType flex-item">
        <div class="layui-form-item" th:if="${formulaFlag == 1}">
          <label class="layui-form-label">学分计算规则：</label>
          <div class="layui-input-block w240">
            <select name="module1" lay-verify="required">
            </select>
          </div>
        </div>
        <div class="layui-form-item" th:if="${formulaFlag != 1}">
          <label class="layui-form-label">总分计分级制：</label>
          <div class="layui-input-block w240">
            <select name="totalScoreGrade" lay-verify="required">
            	<option value="">请选择</option>
                <option value="1">无等级</option>
                <option value="2">按分数</option>
                <option value="3">按排名</option>
            </select>
          </div>
        </div>
        <div class="layui-form-item">
          <label class="layui-form-label">绩点计算规则：</label>
          <div class="layui-input-block w240">
            <select name="module2" lay-verify="required">
            </select>
          </div>
        </div>
      </div>
      <div class="inputTeacher">
        <div class="name">录入教师：</div>
        <div class="it-con">
          <div class="person-list">
            <ul>
              <li class="selects"><span>选择</span></li>
            </ul>
          </div>

        </div>
      </div>
      <h2>成绩计算规则</h2>
      <div class="layui-form addType">
        <div class="layui-form-item" style="margin-bottom:0;">
          <label class="layui-form-label" th:if="${formulaFlag != 0}">添加方式：</label>
          <label class="layui-form-label" th:if="${formulaFlag != 1}">从模板选择：</label>
          <div class="layui-input-block w240">
            <select name="module4" lay-filter="module4" lay-verify="required" id="scoreTempRule">
              <option value="">请选择</option>
              <option value="1">选择模板规则</option>
              <option value="0">新建计算规则</option>
            </select>
          </div>
        </div>
      </div>
      <div class="score-list">
        <div class="item layui-form active">
          <div class="add-lab">
            <span>添加</span>
          </div>
          <div class="table" th:if="${formulaFlag == 1}">
            <table class="layui-table" id="main-table3" lay-filter="dataTable3">
            	
            </table>
          </div>
          <div class="table" th:if="${formulaFlag != 1}">
            <table class="layui-table" id="main-table1" lay-filter="dataTable1" style="display:none;">
            	
            </table>
          </div>
        </div>
        <div class="item" style="padding-top:19px;">
          <div class="layui-form radio-item temp" lay-filter="editForm1">
            
          </div>
        </div>
      </div>
    </div>
    <div class="bottom">
      <button type="reset" class="layui-btn layui-btn-primary exam-cancle">取消</button>
      <button class="layui-btn exam-sure ruleSave">确定</button>
    </div>

  </div>

  <!-- 模板详情 -->
  <div id="template-details" class="addPoups popup">
    <div class="title">
      <div class="name">模板详情</div>
      <div class="close"></div>
    </div>
    <div class="popup-con">
      <div class="table">
        <table class="layui-table" id="main-table2" lay-filter="dataTable2">
        </table>
      </div>
    </div>
    <div class="bottom">
      <button type="reset" class="layui-btn layui-btn-primary exam-cancle">取消</button>
      <button class="layui-btn exam-sure">确定</button>
    </div>

  </div>
  <!-- 添加2 -->
  <div id="subitemadd" class="subitemadd popup">
    <div class="title">
      <div class="name">新增</div>
      <div class="close"></div>
    </div>
    <div class="popup-con">
      <div class="layui-form" lay-filter="secondaryEditForm">
      	<input type="hidden" id="courseGradeId" >
        <div class="layui-form-item">
          <label class="layui-form-label"><em>*</em>成绩分项名称：</label>
          <div class="layui-input-block w275">
            <select name="item" lay-filter="name1" lay-verify="required">
              <option value="">请选择</option>
              <option value="0">十分制</option>
              <option value="1">百分制</option>
              <option value="2">千分制</option>
            </select>
          </div>
        </div>
        <div class="layui-form-item">
          <label class="layui-form-label"><em>*</em>计分级制：</label>
          <div class="layui-input-block w275">
            <select name="grade" lay-filter="level1" lay-verify="required">
              <option value="">请选择</option>
              <option value="0">十分制</option>
              <option value="1">百分制</option>
              <option value="2">千分制</option>
            </select>
          </div>
        </div>
        <div class="layui-form-item">
          <label class="layui-form-label"><em>*</em>占比(%)</label>
          <div class="layui-input-block w275">
            <input type="number" name="proportion" lay-filter="proportion" placeholder="请输入" autocomplete="off"
              class="layui-input">
          </div>
        </div>
        <div class="inputTeacher">
          <div class="name" style="width: 130px;">录入教师：</div>
          <div class="it-con">
            <div class="person-list">
              <ul>
                <li class="selects"><span>选择</span></li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="bottom">
      <button type="reset" class="layui-btn layui-btn-primary exam-cancle">取消</button>
      <button class="layui-btn exam-sure noformulaSave">确定</button>
    </div>
  </div>

  <!-- 模板详情 -->
  <div id="ruleTemplateDetails" class="addPoups popup">
    <div class="title">
      <div class="name">模板详情</div>
      <div class="close"></div>
    </div>
    <div class="popup-con" style="height: 230px;">
      <div class="formula">
        <div class="totals">总分 =</div>
        <div class="inform">
          
        </div>
      </div>
    </div>
    <div class="bottom">
      <button type="reset" class="layui-btn layui-btn-primary exam-cancle">取消</button>
      <button class="layui-btn exam-sure">确定</button>
    </div>

  </div>

  <!-- 设置录入教师 -->
  <div id="setInputTeacher" class="addPoups popup">
    <div class="title">
      <div class="name">设置录入教师</div>
      <div class="close"></div>
    </div>
    <div class="popup-con">
      <div class="inputTeacher">
        <div class="name">录入教师：</div>
        <div class="it-con">
          <div class="person-list">
            <ul>
            </ul>
          </div>

        </div>
      </div>
      <div class="layui-form addType flex-item">
        <div class="layui-form-item">
          <label class="layui-form-label">姓名：</label>
          <div class="layui-input-block w220">
            <div class="input">
              <input type="text" name="realname" placeholder="请输入" autocomplete="off" class="layui-input">
              <div class="search" onclick="getTeaList();"></div>
            </div>
          </div>
        </div>
        <div class="layui-form-item">
          <label class="layui-form-label">工号：</label>
          <div class="layui-input-block w220">
            <div class="input">
              <input type="text" name="uname" placeholder="请输入" autocomplete="off" class="layui-input">
              <div class="search" onclick="getTeaList();"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="result-box">
        <div class="r-top">
          <div class="l-box">
            <div class="all-select">全选</div>
            <div class="cancle-select">清空全选</div>
          </div>
          <div class="r-box">
            <div class="refresh">刷新</div>
          </div>
        </div>
        <div class="r-con">
          <ul>
          </ul>
        </div>
      </div>
    </div>
    <div class="bottom">
      <button type="reset" class="layui-btn layui-btn-primary exam-cancle">取消</button>
      <button class="layui-btn exam-sure" id="saveInputTeacher">确定</button>
    </div>

  </div>

  <!-- 公式编辑 -->
  <div id="gsbjsubitem" class="subitem popup">
    <div class="title">
      <div class="name">公式编辑</div>
      <div class="close"></div>
    </div>
    <div class="popup-con">
      <div class="layui-form flex-form scrollBox" lay-filter="addeditForm">
        <div class="layui-form-item" style="margin-bottom:24px;">
          <label class="layui-form-label"><em>*</em>计分级制：</label>
          <div class="layui-input-block w240">
            <div class="select selectr">
              <select name="modules" class="grade" lay-verify="required">
                
              </select>
            </div>
          </div>
        </div>
        <div class="lable">
          <h3><em>*</em>计算公式：</h3>
          <div class="formula">
            <div class="name">总分 =</div>
            <div class="f-con formula-content"> </div>
          </div>
        </div>
        <div class="grade-keyboard">
          <div class="score-breakdown">
            <div class="sb-top">
              <div class="titles">成绩分项</div>
              <div class="tips"><span>请先选中成绩分项，再选择下拉框内的计分级制</span></div>
              <div class="limit-switch">
                <span>显示二级分项</span>
                <input type="checkbox" name="switch_filter" lay-skin="switch" lay-filter="switch_filter">
              </div>
            </div>
            <div class="sb-cons scrollBox">
              <div class="sb-list">
                <div class="item">
                  <div class="i-con">
                    <div class="left">
                      <div class="name">平时</div>
                    </div>
                    <div class="right scrollBox1">
                      <ul>
                      </ul>
                    </div>
                  </div>
                  <div class="i-bottom">
                    <div class="select">
                      <select name="modules" disabled lay-filter="oneSelect1" lay-verify="required">
                        <option value="">请选择</option>
                        <option value="1">十分制</option>
                        <option value="2">百分制</option>
                      </select>
                    </div>
                  </div>
                </div>
                <div class="item">
                  <div class="i-con">
                    <div class="left">
                      <div class="name">期中</div>
                    </div>
                    <div class="right scrollBox1">
                      <ul>
                      </ul>
                    </div>
                  </div>
                  <div class="i-bottom">
                    <div class="select">
                      <select name="modules" disabled lay-filter="oneSelect1" lay-verify="required">
                        <option value="">请选择</option>
                        <option value="1">十分制</option>
                        <option value="2">百分制</option>
                      </select>
                    </div>
                  </div>
                </div>
                <div class="item">
                  <div class="i-con">
                    <div class="left">
                      <div class="name">期末</div>
                    </div>
                    <div class="right scrollBox1">
                      <ul>

                      </ul>
                    </div>
                  </div>
                  <div class="i-bottom">
                    <div class="select">
                      <select name="modules" disabled lay-filter="oneSelect1" lay-verify="required">
                        <option value="">请选择</option>
                        <option value="1">十分制</option>
                        <option value="2">百分制</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="keyboard">
            <h3>计算键盘</h3>
            <div class="k-con">
              <span class="num">7</span>
              <span class="num">8</span>
              <span class="num">9</span>
              <span class="delet"></span>
              <span class="empty">清空</span>
              <span class="num">4</span>
              <span class="num">5</span>
              <span class="num">6</span>
              <span class="sign lbracket">(</span>
              <span class="sign rbracket">)</span>
              <span class="num">1</span>
              <span class="num">2</span>
              <span class="num">3</span>
              <span class="sign sign-cancle">－</span>
              <span class="sign sign-add">+</span>
              <span class="num zero">0</span>
              <span class="num spot">.</span>
              <span class="sign sign-mul">×</span>
              <span class="sign sign-except">÷</span>
            </div>
          </div>
        </div>
      </div>

    </div>
    <div class="bottom">
      <button type="reset" class="layui-btn layui-btn-primary exam-cancle">取消</button>
      <button class="layui-btn exam-sure" id="formula-total-sure">确定</button>
    </div>

  </div>



  <!-- 公式编辑2 -->
  <div id="gsbjsubitemTwo" class="subitem popup">
    <div class="title">
      <div class="name">公式编辑</div>
      <div class="close"></div>
    </div>
    <div class="popup-con">
      <div class="layui-form flex-form scrollBox" lay-filter="addeditForm1">
         <input type="hidden" id="grdcalcuRuletempScoreruleid_1" value="">
        <div class="layui-form-item" style="margin-bottom:24px;">
          <label class="layui-form-label"><em>*</em>公式选择：</label>
          <div class="layui-input-block w240">
            <div class="select selectr">
              <select name="modules" class="item" lay-filter="itemSelected" lay-verify="required">
                <option value="">请选择</option>
              </select>
            </div>
          </div>
        </div>
        <div class="layui-form-item" style="margin-bottom:24px;">
          <label class="layui-form-label"><em>*</em>计分级制：</label>
          <div class="layui-input-block w240">
            <div class="select selectr">
              <select name="modules" class="grade" lay-verify="required">
                <option value="">请选择</option>
              </select>
            </div>
          </div>
        </div>
      </div>
      <div class="layui-form" lay-filter="addeditForm2">

        <div class="lable">
          <h3><em>*</em>计算公式：</h3>
          <div class="formula">
            <div class="name">平时 =</div>
            <div class="f-con formula-content" id=""> </div>
          </div>
        </div>
        <div class="grade-keyboard">
          <div class="score-breakdown">
            <div class="sb-top">
              <div class="titles">成绩分项</div>
              <div class="tips"><span>请先选中成绩分项，再选择下拉框内的计分级制</span></div>
            </div>
            <div class="sb-cons scrollBox">
              <div class="sb-list">
                <ul>
                </ul>
              </div>
            </div>
          </div>
          <div class="keyboard">
            <h3>计算键盘</h3>
            <div class="k-con">
              <span class="num">7</span>
              <span class="num">8</span>
              <span class="num">9</span>
              <span class="delet"></span>
              <span class="empty">清空</span>
              <span class="num">4</span>
              <span class="num">5</span>
              <span class="num">6</span>
              <span class="sign lbracket">(</span>
              <span class="sign rbracket">)</span>
              <span class="num">1</span>
              <span class="num">2</span>
              <span class="num">3</span>
              <span class="sign sign-cancle">－</span>
              <span class="sign sign-add">+</span>
              <span class="num zero">0</span>
              <span class="num spot">.</span>
              <span class="sign sign-mul">×</span>
              <span class="sign sign-except">÷</span>
            </div>
          </div>
        </div>
      </div>

    </div>
    <div class="bottom">
      <button type="reset" class="layui-btn layui-btn-primary exam-cancle">取消</button>
      <button class="layui-btn exam-sure" id="formula-sure">确定</button>
    </div>

  </div>





  <script type="text/html" id="barDemo3">
    <a style="color:#3A8BFF;;cursor:pointer;margin-right:16px;" lay-event="rule">规则</a>
    <a style="color:#3A8BFF;;cursor:pointer;margin-right:16px;" lay-event="entry">录入</a>
    <a style="color:#3A8BFF;;cursor:pointer;" lay-event="manage">管理</a>
  </script>
  <script type="text/html" id="barDemo2">
    <a style="color:#3A8BFF;;cursor:pointer;margin-right:8px;" lay-event="edit">编辑</a>
    <a style="color:#F33131;cursor:pointer;" lay-event="del">删除</a>
  </script>
  <script type="text/html" id="barDemo1">
    <a style="color:#3A8BFF;;cursor:pointer;margin-right:8px;" lay-event="formulaEdit">公式编辑</a>
    <a style="color:#F33131;cursor:pointer;" lay-event="del">删除</a>
  </script>
  <script src="/js/jquery-3.3.1.min.js"></script>
  <script src="/layui/layui.js"></script>
  <script src="/score/js/index.js"></script>
  <script type="text/javascript" th:src="@{~/js/my.util.js}"></script>
  <script>
  	var fid = [[${fid}]]+"";
  	var uid = [[${uid}]]+"";
  	var xnxq = $("#xnxq").val();
  	var formula_flag = [[${formulaFlag}]]+"";
  	var teachereditFlag = [[${teachereditFlag}]]+"";
  	var role = [[${role}]]+"";
  	var tempList = "";
  	var basicList = "";
  	var grdepoiRuleList = "";
  	var gradeMakeList = "";
  	var classCourseList = "";
  	var main_table2_dataArr = [];
    var main_table3_dataArr = [];
    var noformulatemp = [];
    var gsbjsubitemindex;
    var selected = ",";
    var courseData = [];
    $(function () {
    	//默认加载
        getCreditSet();
        getGrdepoiRuleList();
        getGrdcalcuRuletempList();
        getGradeMakeList();
        getItemDictionaryList();
        getClassCourseList();
        getClassList();
      var form, table, laydate, layer;
      layui.use(['form', 'table', 'laydate', 'upload'],
        function () {
          var $ = layui.jquery;
          form = layui.form;
          table = layui.table;
          laydate = layui.laydate;
          layer = layui.layer;
          upload = layui.upload;
		  
          var table2 = table.render({
            elem: "#materialTable",
            id: 'materialTable',
            url:"/api/score/classcourse/getList",
            where:{xnxq:xnxq},
            page: {
              limit: 10,
              limits: [10, 20, 30, 40, 50],
              layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
            },
            data: [],
            cols: [
              [{
                  type: "checkbox",
                  width: 78,
                  fixed: "left",
                },
                {
                  field: "id",
                  align: "center",
                  title: "id",
                  hide: true,
                },
                {
                  field: "xnxq",
                  align: "center",
                  title: "学年学期",
                  minWidth: 120
                },
                {
                  field: "courseName",
                  align: "center",
                  title: "课程名称",
                  minWidth: 120
                },
                {
                  field: "courseMajor",
                  align: "center",
                  title: "课程专业",
                  minWidth: 120
                },
                {
                  field: "className",
                  align: "center",
                  title: "教学班名称",
                  minWidth: 120
                },
                {
                  field: "constituteClassName",
                  align: "center",
                  title: "班级名称",
                  minWidth: 120,
                  templet:function (d){
                  	return $(".typeCover5 option[value='"+d.constituteClassCode+"']").text()||"";
                  }
                },
                {
                  field: "teacher",
                  align: "center",
                  title: "授课教师",
                  minWidth: 120
                },
                {
                  field: "courseType",
                  align: "center",
                  title: "课程性质",
                  minWidth: 120
                },
                {
                  field: "credit",
                  align: "center",
                  title: "课程学分",
                  minWidth: 120
                },
                {
                  field: "compPercentage",
                  align: "center",
                  title: "录入完成百分比",
                  minWidth: 120,
                  templet: function (d) {
                  	var percentage = 0;
                  	if(d.totalNum != '' && d.entryNum != ''){
                  		percentage = Math.floor(d.entryNum*100/d.totalNum);
                  	}
                    return percentage+"%";
                  }
                },
                {
                  field: "releaseState",
                  align: "center",
                  title: "发布状态",
                  minWidth: 120,
                  templet: function (d) {
                    var releaseState = "未发布";
                  	if(d.releaseState == 1){
                  		releaseState = "已发布";
                  	}
                    return '<div class="stute">' + releaseState + '</div>';
                  },
                  fixed: "right",
                },
                {
                  field: "lockState",
                  align: "center",
                  title: "锁定状态",
                  minWidth: 120,
                  templet: function (d) {
                  	var lockState = "未锁定";
                  	if(d.releaseState == 1){
                  		lockState = "已锁定";
                  	}else if(d.releaseState == 2){
                  		lockState = "已解锁";
                  	}
                    return '<div class="stute">' + lockState + '</div>';
                  },
                  fixed: "right",
                },
                {
                  field: "options",
                  align: "center",
                  title: "操作",
                  toolbar: "#barDemo3",
                  minWidth: 200,
                  fixed: "right",
                },
              ]
            ],
            done: function (res) {
            	courseData = res.data;
            }
          })
		  $(".main .content .filter-box .search").on("click", function () {
				var xnxq = $("#xnxq").val();
          		var courseSearch = $("#courseSearch").val();
          		var classSearch = $("#classSearch").val();
          		var teacherSearch = $("#teacherSearch").val();
          		var constituteClassSearch = $("#constituteClassSearch").val();
				var field = {xnxq:xnxq,classCode:classSearch,courseCode:courseSearch,teacher:teacherSearch,
					constituteClassCode:constituteClassSearch};
				table.reload("materialTable",{where: field,page: {curr: 1}});
          })
		  $(".resetting").click(function (){
		  		$("#courseSearch").val("");
          		$("#classSearch").val("");
          		$("#teacherSearch").val("");
          		$("#constituteClassSearch").val("");
          		form.render();
		  });
          $(".main .content .btn-lists .btn1").on("click", function () {
            var xnxq = $("#xnxq").val();
          	$.ajax({
	            type: 'post',
	            dataType: 'json',
	            url: '/api/score/classcourse/syncData?',
	            data: {xnxq:xnxq},
	            success: function (res){
	            	if(res.code == 200){
	            		alert("数据更新完成");
	            	}else{
	            		alert("数据更新失败");
	            	}
	            }
	        });
          })
          $(".main .content .btn-lists .btn2").click(function () {
	            $("#ruleAddPoups").find(".inform").hide();
				$("#classCourseId").val("");
				$("#ruleAddPoups .inform .lable:eq(0) .tit").html("");
               	$("#ruleAddPoups .inform .lable:eq(1) .tit").html("");
              	$("#ruleAddPoups select[name=module4]").val("");
              	$("#ruleAddPoups select[name=totalScoreGrade]").val("");
				$("#ruleAddPoups select[name=module1]").val("");//公式
				$("#ruleAddPoups select[name=module2]").val("");
				main_table3_dataArr = [];
	            layer.open({
	              type: 1,
	              title: false,
	              closeBtn: false,
	              shadeClose: true,
	              isOutAnim: true,
	              content: $('#ruleAddPoups'),
	              area: ['auto', 'auto'],
	              success: function () {
						table.reload('main-table1',{data:[]});
		                table.reload('main-table3',{data:[{"itemName":"总分","totalType":1}]});
	                	form.render();
	              },
	            }, function () {
	
	            });
          })
          $(".main .content .btn-lists .btn3").click(function () {
          		var xnxq = $("#xnxq").val();
          		var courseSearch = $("#courseSearch").val();
          		var classSearch = $("#classSearch").val();
          		var teacherSearch = $("#teacherSearch").val();
          		var constituteClassSearch = $("#constituteClassSearch").val();
          		var courseIds = selected
          		window.location.href = "/api/score/classcourse/expStuScore?xnxq="+xnxq+"&courseCode="+courseSearch
          			+"&classCode="+classSearch+"&teacher="+teacherSearch+"&constituteClassCode="+constituteClassSearch+"&courseIds="+selected;
          		selected = ",";
          		table.reload("materialTable",{});
          });
          //隐藏弹窗
          $('.close,.exam-cancle').on("click", function () {
            var index = $(this).parents(".layui-layer").attr("times");
            layer.close(index);
          })

          var table3 = table.render({
            elem: "#main-table1",
            id: 'main-table1',
            data: [],
            cols: [
              [{
                  field: "itemCode",
                  align: "center",
                  title: "成绩分项代码",
                  minWidth: 120
                },
                {
                  field: "itemName",
                  align: "center",
                  title: "成绩分项名称",
                  minWidth: 120
                },
                {
                  field: "gradeName",
                  align: "center",
                  title: "计分级制",
                  minWidth: 120
                },
                {
                  field: "proportion",
                  align: "center",
                  title: "占比（%）",
                  minWidth: 120
                },
                {
                  field: "options",
                  align: "center",
                  title: "操作",
                  toolbar: "#barDemo2",
                  minWidth: 120
                },
              ]
            ],
            done: function (res) {}
          })

          var table4 = table.render({
            elem: "#main-table2",
            id: 'main-table2',
            data: [{
                "itemCode": "FX001",
                "itemName": "期中",
                "classificationSystem": "百分制",
                "proportion": "50"
              },
              {
                "itemCode": "FX001",
                "itemName": "期中",
                "classificationSystem": "百分制",
                "proportion": "50"
              },
            ],
            cols: [
              [{
                  field: "itemCode",
                  align: "center",
                  title: "成绩分项代码",
                  minWidth: 120
                },
                {
                  field: "itemName",
                  align: "center",
                  title: "成绩分项名称",
                  minWidth: 120
                },
                {
                  field: "classificationSystem",
                  align: "center",
                  title: "计分级制",
                  minWidth: 120
                },
                {
                  field: "proportion",
                  align: "center",
                  title: "占比（%）",
                  minWidth: 120
                },
                {
                  field: "options",
                  align: "center",
                  title: "操作",
                  toolbar: "#barDemo2",
                  minWidth: 120
                },
              ]
            ],
            done: function (res) {}
          })

          var table5 = table.render({
            elem: "#main-table3",
            id: 'main-table3',
            //url:"/api/score/classcourse/getItemList",
            data: [],
            cols: [
              [{
                  field: "itemName",
                  align: "center",
                  title: "公式",
                  minWidth: 120
                },
                {
                  field: "gradeName",
                  align: "center",
                  title: "计分级制",
                  minWidth: 120
                },
                {
                  field: "options",
                  align: "center",
                  title: "操作",
                  toolbar: "#barDemo1",
                  minWidth: 120
                },
              ]
            ],
            done: function (res) {}
          })

          /* 监听工具条 */

          table.on("tool(materialTable)", function (obj) {
            var data = obj.data; //获得当前行数据
            if (obj.event === "rule") {
              $("#ruleAddPoups").find(".inform").show();
              $("#classCourseId").val(data.id);
              layer.open({
                type: 1,
                title: false,
                closeBtn: false,
                shadeClose: true,
                isOutAnim: true,
                content: $('#ruleAddPoups'),
                area: ['auto', 'auto'],
                success: function () {
                	$("#ruleAddPoups .inform .lable:eq(0) .tit").html(data.courseName);
                	$("#ruleAddPoups .inform .lable:eq(1) .tit").html(data.className);
               		$("#ruleAddPoups select[name=module4]").val(data.scoreRule);
               		$("#ruleAddPoups select[name=totalScoreGrade]").val(data.totalScoreGrade);
					$("#ruleAddPoups select[name=module1]").val(data.creditRule);//公式
					$("#ruleAddPoups select[name=module2]").val(data.gpaRule);
					var str = "";
					if(data.inputTeachers != "[]" && data.inputTeachers != ''){
						var inputTeachers = JSON.parse(data.inputTeachers);
						inputTeachers.forEach(function (info){
                    		str += "<li uid='"+info.puid+"'><span>"+info.uname+"</span><em></em></li>";
                    	});
					}
		            $("#ruleAddPoups .person-list ul").html(str+"<li class='selects'><span>选择</span></li>");
					
                	if(data.scoreRule != ''){//用成绩计分级制的模板
                		mainTable1(data.scoreRule);
                	}else{
	                	$.ajax({
				            type: 'post',
				            dataType: 'json',
				            url: '/api/score/classcourse/getItemList?',
				            data: {classCourseId:data.id,pid:0},
				            success: function (res){
				            	if(res.data != ""){
				            		main_table3_dataArr = res.data;
				            	}
				            	if(res.data == "" && formula_flag == 1){
				            		table.reload('main-table3',{data:[{"itemName":"总分","totalType":1}]});
				            	}else if(formula_flag == 1){
				                    table.reload('main-table3',{data:res.data});
				            	}else {
				            		table.reload('main-table1',{data:res.data});
				            	}
				            }
				        });
                	}
                	form.render();
                },
              }, function () {

              });

            } else if (obj.event === "entry") {
              	window.open("/api/score/mainexamgrade/manageindex?id="+data.id);
            } else if (obj.event === "manage") {
              	window.location.href = "/api/score/mainexamgrade/scoreManage?id="+data.id;
            }
          })
		  //批量勾选
		  table.on("checkbox(materialTable)", function (obj) {
		    var checkStatus = obj.checked;
		    console.log(checkStatus);
            var data = obj.data; //获得当前行数据
            if(obj.type == 'all' && obj.checked){
            	courseData.forEach(function (){
		            selected += data.id+",";
            	});
            }else if(obj.type == 'all' && !obj.checked){
            	selected = ",";
            }else if(obj.checked){
            	selected += data.id+",";
            }else{
           	 	var delid = ","+data.id+",";
                selected = selected.replace(delid,",");
            }
          })
          table.on("tool(dataTable1)", function (obj) {
            var data = obj.data; //获得当前行数据
            if (obj.event === "del") {
              if($("#ruleAddPoups select[name=module4]").val() > 0){
          		layer.msg("已选择模板，不能删除！");
          		return ;
          	  }
              obj.del();
              layer.msg("删除");
            } else if (obj.event === "edit") {
              if($("#ruleAddPoups select[name=module4]").val() > 0){
          		layer.msg("已选择模板，不能再单独添加！");
          		return ;
          	  }
              layer.msg("编辑");
              $("#courseGradeId").val(data.id);
              layer.open({
                type: 1,
                title: false,
                closeBtn: false,
                shadeClose: true,
                isOutAnim: true,
                content: $('#subitemadd'),
                area: ['auto', 'auto'],
                success: function () {
                	$("#subitemadd select[name=item]").val(data.itemId);
                    $("#subitemadd select[name=grade]").val(data.gradeId);
                    $("#subitemadd input[name=proportion]").val(data.proportion);
                    $("#subitemadd .person-list ul li").each(function (){
                    	if(!$(this).hasClass("selects")){
                    		$(this).remove();
                    	}
                    });
                    if(data.inputTeachers != '' && data.inputTeachers != '[]'){
                    	data.inputTeachers.forEach(function (info){
                    		var str = "<li uid='"+info.puid+"'><span>"+info.uname+"</span><em></em></li>";
		                    $("#subitemadd .person-list ul li.selects").before(str);
                    	});
                    }
                    form.render();
                }
              })
            }
          })

          table.on("tool(dataTable2)", function (obj) {
            var data = obj.data; //获得当前行数据
            if (obj.event === "del") {
              obj.del();
              layer.msg("删除");
            } else if (obj.event === "edit") {
              layer.msg("编辑");
            }
          })

          table.on("tool(dataTable3)", function (obj) {
            var data = obj.data; //获得当前行数据
            if (obj.event === "del") {
              obj.del();
              layer.msg("删除");
            } else if (obj.event === "formulaEdit") {
			  var content = "gsbjsubitemTwo";
			  if(data.totalType == 1){
			  	  content = "gsbjsubitem";
			  }
              layer.open({
                type: 1,
                title: false,
                closeBtn: false,
                shadeClose: true,
                isOutAnim: true,
                content: $('#'+content),
                area: ['auto', 'auto'],
                success: function () {
                	$("#"+content+" select.item").val(data.itemId);
                    $("#"+content+" select.grade").val(data.gradeId);
                	gsbjsubitemTwo(data.totalType);
                	//初始化成绩分项下拉
                	$("#"+content+" .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list ul li").each(function () {
						  var layTitle = $(this).find("select").attr("lay-filter");
						  console.log(layTitle);
						  form.on('select(' + layTitle + ')', function (data) {
							  var e = data.elem;
							  var text = e[e.selectedIndex].text;
							  var ptext = $(this).parents("li").find(".name").text();
							  var grade_code = $(this).parents("li").attr("data-code");
							  $("#"+content+" .popup-con .lable .formula .f-con").addClass("cons");
							  $("#"+content+" .popup-con .lable .formula .f-con").append(
									  '<span markId="' + layTitle + '" class="asso" data-code="'+data.value+'" item_id="'+grade_code+'">' + ptext + " " +
									  text + '</span>');
						  })
					});
		          	form.render("select");
					$("#grdcalcuRuletempScoreruleid_1").val(data.id);
					//填充计算公式
					var formula = data.formulaText || "";
                    if(formula != ""){
                        var formulaText = JSON.parse(formula) || "";
                        var html = "";
                        for(var i in formulaText){
                           var type = formulaText[i]["type"];
                           var grade_code = formulaText[i]["grade_code"];
                           var pcode = formulaText[i]["pcode"];
                           var text = formulaText[i]["text"];
                           var className = getClaName(type);
                           if(type == "gl"){
                               html += "<span class='"+className+"' markid='selctOnchanges1' data-code='"+grade_code+"' pcode='"+pcode+"' >"+text+"</span>";
                               $("#"+content+" .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list ul li").each(function(){
                                   if($(this).attr("data-code") == pcode){
                                       $(this).find(".name").addClass("cur");
                                       $(this).find(".select").find("select").val(grade_code);
                                       $(this).find(".select").find("select").removeAttr("disabled");
                                   }
                                   
                               })
                           } else {
                               html += "<span class='"+className+"'>"+text+"</span>";
                           }
                        }
                        form.render();
                        if(html.length > 0){
                           $("#"+content+" .formula-content").addClass("cons")
                        }
                        $("#"+content+" .formula-content").html(html);                       
                    }
                },

              }, function () {});

            }
          })

          //删除教师
          $("#ruleAddPoups .popup-con").on("click", ".inputTeacher .it-con .person-list ul li em", function () {
            $(this).parent().remove();
          })
          //模板选择--添加方式选择事件
          form.on('select(module4)', function (data) {
            var e = data.elem;
            var vals = data.value;
            console.log(vals);
            mainTable1(vals);
            $("#ruleAddPoups .popup-con .score-list .item").eq(vals).addClass("active").siblings().removeClass("active");
          })
		  function mainTable1(vals){
            main_table3_dataArr = [];
		  	if (formula_flag != 1) {//非公式模板选择
            	tempList.forEach(function (info){
            		if(info.id == vals){
            			  var array = JSON.parse(info.formulaTextOff);
		                  for(var j in array){
		                  	  var formulaText = array[j];
		                  	  var formula = {"id":formulaText.id,"itemCode":formulaText.scoreSubCode,"itemName":formulaText.scoreSubName,
		                  	  	"gradeCode":formulaText.gradeMakeRuleid,"gradeName":formulaText.classificationSystem,"proportion":formulaText.proportion};
			                  main_table3_dataArr.push(formula);
		                  }
            		}
            	});
            }
            table.reload('main-table1',{data:main_table3_dataArr});
		  }
          //模板详情
          $("#ruleAddPoups .popup-con").on("click", ".layui-form.radio-item .layui-form-item .templat-details",
            function () {
              var flag = $(this).attr("flag");
              layer.open({
                type: 1,
                title: false,
                closeBtn: false,
                shadeClose: true,
                isOutAnim: true,
                content: $('#ruleTemplateDetails'),
                area: ['auto', 'auto'],
                success: function () {
                  table.reload('main-table2');
                  var array = JSON.parse(tempList[flag].formulaTextOn);
                  var html = ""
                  for(var j in array){
                  	  var formulaText = JSON.parse(array[j].formulaText);
	                  html += "<div class='formula'><div class='totals'>"+array[j].scoreSubName+" =</div><div class='inform'>";
	                  for(var i in formulaText){
	                      var type = formulaText[i]["type"];
	                      var text = formulaText[i]["text"];
	                      if(type == 'gl'){
	                      	  html += "<span class='tit'>"+text+"</span>";
	                      }else{
		                      html += "<span>"+text+"</span>";
	                      }
	                  }
	                  form.render();
	                  html += "</div></div>";
                  }
                  $("#ruleTemplateDetails .popup-con").html(html);
                  
                },
              }, function () {

              });
            })
		  $("#ruleTemplateDetails .exam-sure,#template-details .exam-sure").click(function (){
		  	$("#ruleTemplateDetails .close").click();
		  	$("#template-details .close").click();
		  });


          //选择
          $("#ruleAddPoups .popup-con,#subitemadd .popup-con").on("click", ".inputTeacher .it-con .person-list ul li.selects",
            function () {
              layer.open({
                type: 1,
                title: false,
                closeBtn: false,
                shadeClose: true,
                isOutAnim: true,
                content: $('#setInputTeacher'),
                area: ['auto', 'auto'],
                success: function () {
                	$("#setInputTeacher .cancle-select").click();
                	$("#setInputTeacher .person-list ul").html($(this).parent().html());
                	$("#setInputTeacher .person-list ul li.selects").remove();
                  	table.reload('main-table2');
                },
              }, function () {

              });
          })
		  
          //选中状态
          $("#setInputTeacher").on("click", " .result-box .r-con ul li", function () {
             $(this).toggleClass("cur");
             var str = "<li uid='"+$(this).attr("uid")+"'><span>"+$(this).find(".name").html()+"</span><em></em></li>";
             if($(this).hasClass("cur")){
             	if($("#setInputTeacher .inputTeacher .person-list ul li[uid="+$(this).attr("uid")+"]").length == 0){
             		$("#setInputTeacher .inputTeacher .person-list ul").append(str);
             	}
             }else{
             	$("#setInputTeacher .inputTeacher .person-list ul li[uid="+$(this).attr("uid")+"]").remove();
             }
          })

          //全选
          $("#setInputTeacher .result-box .r-top .l-box .all-select").click(function () {
            $("#setInputTeacher .result-box .r-con ul li").addClass("cur");
            $("#setInputTeacher .result-box .r-con ul li").each(function (){
            	var uid = $(this).attr("uid");
             	var str = "<li uid='"+uid+"'><span>"+$(this).find(".name").html()+"</span><em></em></li>";
            	if($("#setInputTeacher .inputTeacher .person-list ul li[uid="+uid+"]").length == 0){
            		$("#setInputTeacher .inputTeacher .person-list ul").append(str);
            	}
            });
          })

          //取消全选
          $("#setInputTeacher .result-box .r-top .l-box .cancle-select").click(function () {
            $("#setInputTeacher .result-box .r-con ul li").removeClass("cur");
            $("#setInputTeacher .result-box .r-con ul li").each(function (){
            	var uid = $(this).attr("uid");
             	var str = "<li uid='"+uid+"'><span>"+$(this).find(".name").html()+"</span><em></em></li>";
             	$("#setInputTeacher .inputTeacher .person-list ul li[uid="+uid+"]").remove();
            });
          })

          //删除录入教师
          $("#setInputTeacher").on("click", ".inputTeacher .it-con .person-list ul li em", function () {
            $(this).parent().remove();
          })
		  $("#setInputTeacher .exam-sure").click(function (){
		  	var str = "<li class='selects'><span>选择</span></li>";
		  	if($("#subitemadd").is(":hidden")){
			  	$("#ruleAddPoups .person-list ul").html($("#setInputTeacher .person-list ul").html()+str);
		  	}else{
		  		$("#subitemadd .person-list ul").html($("#setInputTeacher .person-list ul").html()+str);
		  	}
		  	$("#setInputTeacher .close").click();
		  });
          //二级分项弹窗
          $("#ruleAddPoups .popup-con .score-list .item .add-lab span").click(function () {
          	if($("#ruleAddPoups select[name=module4]").val() > 0){
          		layer.msg("已选择模板，不能再单独添加！");
          		return ;
          	}
          	if(teachereditFlag == 0 && role != 0){
          		layer.msg("老师不能单独添加规则！");
          		return ;
          	}
          	var content = "subitemadd";
          	if(formula_flag == 1){
          		content = "gsbjsubitemTwo";
          	}
            layer.open({
              type: 1,
              title: false,
              closeBtn: false,
              shadeClose: true,
              isOutAnim: true,
              content: $('#'+content),
              area: ['auto', 'auto'],
              success: function () {
              		if(formula_flag == 1){
	              		$("#gsbjsubitemTwo select.item").val("");
	                    $("#gsbjsubitemTwo select.grade").val("");
	                    $("#gsbjsubitemTwo .formula-content").html("");
	                    $("#gsbjsubitemTwo .score-breakdown .sb-list ul").html();
              		}else{
              			$("#courseGradeId").val("");
              			$("#subitemadd select[name=item]").val("");
	                    $("#subitemadd select[name=grade]").val("");
	                    $("#subitemadd input[name=proportion]").val("");
	                    $("#subitemadd .person-list ul li").each(function (){
	                    	if(!$(this).hasClass("selects")){
	                    		$(this).remove();
	                    	}
	                    });
              		}
                    form.render();
              },

            }, function () {});
          })
          form.on('select(itemSelected)', function (data) {
	          	gsbjsubitemTwo(0);
	          	form.render("select");
				$("#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list ul li").each(function () {
					  var layTitle = $(this).find("select").attr("lay-filter");
					  console.log(layTitle);
					  form.on('select(' + layTitle + ')', function (data) {
						  var e = data.elem;
						  var text = e[e.selectedIndex].text;
						  var ptext = $(this).parents("li").find(".name").text();
						  var item_id = $(this).parents("li").attr("data-code");
						  $("#gsbjsubitemTwo .popup-con .lable .formula .f-con").addClass("cons");
						  $("#gsbjsubitemTwo .popup-con .lable .formula .f-con").append(
								  '<span markId="' + layTitle + '" class="asso" data-code="'+data.value+'" item_id="'+item_id+'">' + ptext + " " +
								  text + '</span>');
					  })
				})
          })
          $("#formula-total-sure").click(function(){
              var formula_text = new Array();
              var glindex = 0;
              $("#gsbjsubitem .formula-content").children("span").each(function(){
                  var ctype = getformulaType($(this).prop("class"));
                  var obj = {};
                  obj["type"] = ctype;
                  obj["text"] = $(this).text();
                  if(ctype == 'gl'){
                      glindex++;
                      obj["grade_id"] = $(this).attr("data-code");
                      obj["item_id"] = $(this).attr("item_id");
				  }
                  formula_text.push(obj);
              })
              if(glindex == 0){
                  layer.msg("请选择成绩分项");
                  return;
              }
              var gradeId = $("#gsbjsubitem select.grade").find("option:selected").val();
              var gradeName = $("#gsbjsubitem select.grade").find("option:selected").text();
              var grdcalcuRuletempScoreruleid = $("#grdcalcuRuletempScoreruleid_1").val();
              if(U.isEmpty(gradeId)){
                  layer.msg("请选择计分级制");
                  return;
              }
              if(U.isEmpty(grdcalcuRuletempScoreruleid)){
                  var addObj = {};
                  addObj["id"] = guid();
                  addObj["itemName"] = "总分";
                  addObj["gradeCode"] = gradeId;
                  addObj["gradeName"] = gradeName;
                  addObj["formulaText"] = JSON.stringify(formula_text);
                  addObj["totalType"] = 0;
                  main_table3_dataArr.push(addObj);
                  table.reload('main-table3', {data: main_table3_dataArr})
              } else {
                  for(var i in main_table3_dataArr){
                      if(main_table3_dataArr[i].id == grdcalcuRuletempScoreruleid){
                          main_table3_dataArr[i]["gradeCode"] = gradeId;
                          main_table3_dataArr[i]["gradeName"] = gradeName;
                          main_table3_dataArr[i]["formulaText"] = JSON.stringify(formula_text);
                      }
                  }
                  table.reload('main-table3', {
                      data: main_table3_dataArr
                  })
                  $("#gsxz-filter").val("");
                  $("#jfjz-filter").val("");
                  form.render();
              }
              $("#gsbjsubitemTwo .close").click();
        })
          $("#formula-sure").click(function(){
              var formula_text = new Array();
              var glindex = 0;
              $("#gsbjsubitemTwo .formula-content").children("span").each(function(){
                  var ctype = getformulaType($(this).prop("class"));
                  var obj = {};
                  obj["type"] = ctype;
                  obj["text"] = $(this).text();
                  if(ctype == 'gl'){
                      glindex++;
                      obj["grade_id"] = $(this).attr("data-code");
                      obj["item_id"] = $(this).attr("item_id");
				  }
                  formula_text.push(obj);
              })
              if(glindex == 0){
                  layer.msg("请选择成绩分项");
                  return;
              }
              var optionId = $("#gsbjsubitemTwo select.item").find("option:selected").val();
              var optionname = $("#gsbjsubitemTwo select.item").find("option:selected").text();
              var optioncode = $("#gsbjsubitemTwo select.item").find("option:selected").attr("data-code");
              var gradeId = $("#gsbjsubitemTwo select.grade").find("option:selected").val();
              var gradeCode = $("#gsbjsubitemTwo select.grade").find("option:selected").attr("data-code");
              var gradeName = $("#gsbjsubitemTwo select.grade").find("option:selected").text();
              var grdcalcuRuletempScoreruleid = $("#grdcalcuRuletempScoreruleid_1").val();
              if(U.isEmpty(optionId)){
                  layer.msg("请选择公式");
                  return;
              }
              if(U.isEmpty(gradeId)){
                  layer.msg("请选择计分级制");
                  return;
              }
              if(U.isEmpty(grdcalcuRuletempScoreruleid)){
                  var addObj = {};
                  addObj["id"] = guid();
                  addObj["itemId"] = optionId;
                  addObj["itemName"] = optionname;
                  addObj["itemCode"] = optioncode;
                  addObj["gradeId"] = gradeId;
                  addObj["gradeCode"] = gradeCode;
                  addObj["gradeName"] = gradeName;
                  addObj["proportion"] = 0;
                  addObj["formulaText"] = JSON.stringify(formula_text);
                  addObj["totalType"] = 0;
                  main_table3_dataArr.push(addObj);
                  table.reload('main-table3', {data: main_table3_dataArr})
              } else {
                  for(var i in main_table3_dataArr){
                      if(main_table3_dataArr[i].id == grdcalcuRuletempScoreruleid){
                          main_table3_dataArr[i]["itemId"] = optionId;
                          main_table3_dataArr[i]["itemName"] = optionname;
                          main_table3_dataArr[i]["itemCode"] = optioncode;
                          main_table3_dataArr[i]["gradeId"] = gradeId;
                          main_table3_dataArr[i]["gradeCode"] = gradeCode;
                          main_table3_dataArr[i]["gradeName"] = gradeName;
                          main_table3_dataArr[i]["formulaText"] = JSON.stringify(formula_text);
                      }
                  }
                  table.reload('main-table3', {
                      data: main_table3_dataArr
                  })
                  $("#gsxz-filter").val("");
                  $("#jfjz-filter").val("");
                  form.render();
              }
              $("#gsbjsubitemTwo .close").click();
        	})
			$(".ruleSave").click(function (){
				var totalScoreGrade = $("#ruleAddPoups select[name=totalScoreGrade]").val();//非公式
				var creditRule = $("#ruleAddPoups select[name=module1]").val();//公式
				var gpaRule = $("#ruleAddPoups select[name=module2]").val();
				var scoreTempRule = $("#scoreTempRule").val();
				var classCourseId = $("#classCourseId").val();
				var inputTeacher = [];
				$("#ruleAddPoups .person-list ul li").each(function (){
					if(!$(this).hasClass("selects")){
						var tea = {"puid":$(this).attr("uid"),"uname":$(this).find("span").text()};
						inputTeacher.push(tea);
					}
				});
				if(classCourseId == ''){
					classCourseId = selected;
				}
				if(classCourseId == ','){
					alert("请勾选课程");
					return ;
				}
				$.ajax({
		            type: 'post',
		            dataType: 'json',
		            url: '/api/score/classcourse/saveRule?',
		            data: {classCourseIds:classCourseId,creditRule:creditRule,gpaRule:gpaRule,totalScoreGrade:totalScoreGrade,
		            	inputTeacher:JSON.stringify(inputTeacher),scoreRule:scoreTempRule,courseGradeRule:JSON.stringify(main_table3_dataArr)},
		            success: function (res){
	            		table.reload("materialTable",{});
				        $("#ruleAddPoups .close").click();
		            }
		        });
			});
			//非公式编辑分项，保存
			$(".noformulaSave").click(function (){
				var itemId = $("#subitemadd select[name=item]").val();
				var itemCode = $("#subitemadd select[name=item] option[value='"+itemId+"']").attr("data-code");
				var itemName = $("#subitemadd select[name=item] option[value='"+itemId+"']").text();
                var gradeId = $("#subitemadd select[name=grade]").val();
                var gradeCode = $("#subitemadd select[name=grade] option[value='"+gradeId+"']").attr("data-code");
                var gradeName = $("#subitemadd select[name=grade] option[value='"+gradeId+"']").text();
                var proportion = $("#subitemadd input[name=proportion]").val();
                var id = $("#courseGradeId").val();
                var inputTea = [];
                $("#subitemadd .person-list ul li").each(function (){
                	if(!$(this).hasClass("selects")){
                		var tea = {"puid":$(this).attr("uid"),"uname":$(this).find("span").text()};
						inputTea.push(tea);
                	}
                });
                
               	var courseGradeRule = {"id":id,"itemCode":itemCode,"itemId":itemId,inputTeacher:inputTea,"itemName":itemName,
               		"gradeId":gradeId,"gradeCode":gradeCode,"gradeName":gradeName,"proportion":proportion};
                if(id == ''){
                	id = guid();
                	courseGradeRule["id"] = id;
					main_table3_dataArr.push(courseGradeRule);
                }else{
                	main_table3_dataArr.forEach(function(info, e) {
                		if(info.id == id){
                			main_table3_dataArr[e] = courseGradeRule;
                		}
                	});
                }
                table.reload('main-table1',{data:main_table3_dataArr});
				$("#subitemadd .close").click();
			});
        })
    })
    function getClassCourseList(){
    	$.ajax({
            type: 'post',
            dataType: 'json',
            url: '/api/score/classcourse/getClassCourseList?',
            data: {xnxq:$("#xnxq").val()},
            async:false,
            success: function (res){
            	if(res.courseList != null && res.courseList != ''){
            		var str = "<option value=''>请选择</option>";
            		for(var key in res.courseList){
            			str += "<option value='"+key+"'>"+res.courseList[key][0].courseName+"</option>";
            		};
           			$("#courseSearch").html(str);
            	}
            	if(res.classList != null && res.classList != ''){
            		var str = "<option value=''>请选择</option>";
            		for(var key in res.classList){
            			str += "<option value='"+key+"'>"+res.classList[key][0].className+"</option>";
            		};
           			$("#classSearch").html(str);
            	}
            	if(res.teaList != null && res.teaList != ''){
            		var str = "<option value=''>请选择</option>";
            		for(var key in res.teaList){
            			str += "<option value='"+key+"'>"+res.teaList[key][0].teacher+"</option>";
            		};
           			$("#teacherSearch").html(str);
            	}
            }
        });
    }
    function getClassList(){
    	$.ajax({
            type: 'post',
            dataType: 'json',
            url: '/api/score/classcourse/getClassList?',
            data: {},
            async:false,
            success: function (res){
            	if(res.data != null && res.data != ''){
            		var str = "<option value=''>请选择</option>";
            		res.data.forEach(function(info){
            			str += "<option value='"+info.bjxx_bjbh+"'>"+info.bjxx_bjmc+"</option>";
            		});
           			$("#constituteClassSearch").html(str);
            	}
            }
        });
    }
    function getCreditSet(){
    	var i="";
    	$.ajax({
            type: 'post',
            dataType: 'json',
            url: '/new/credit/course/get/ruleSet?',
            data: {fid:fid,uid:uid,xnxq:$("#xnxq").val()},
            success: function (res){
            	if(res.data != null && res.data.zkData != ''){
            		var array = JSON.parse(res.data.zkData);
            		var str = "<option value=''>请选择</option>";
            		array.forEach(function(info){
            			str += "<option value='"+info.ruleCode+"'>"+info.ruleName+"</option>";
            		});
           			$("#ruleAddPoups select[name=module1]").html(str);
           			//$("#subitemadd select[name=grade]").html(str);
            	}
            }
        });
    }
    function getGrdepoiRuleList(){
    	$.ajax({
            type: 'post',
            dataType: 'json',
            url: '/api/score/gradePoint/getGrdepoiRuleList?',
            data: {fid:fid,xnxq:$("#xnxq").val()},
            success: function (res){
            	if(res.data != null && res.data.dataList != ''){
            		var str = "<option value=''>请选择</option>";
            		grdepoiRuleList = res.data.dataList;
            		res.data.dataList.forEach(function(info){
            			str += "<option value='"+info.id+"'>"+info.ruleName+"</option>";
            		});
           			$("#ruleAddPoups select[name=module2]").html(str);
           			$("#subitemadd select[name=item]").html(str);
            	}
            }
        });
    }
    function getGrdcalcuRuletempList(){
    	$.ajax({
            type: 'post',
            dataType: 'json',
            url: '/api/score/gradeCalculate/getGrdcalcuRuletempList?',
            data: {fid:fid,xnxq:$("#xnxq").val()},
            success: function (res){
            	if(res.data != null && res.data.dataList != ''){
            		var str = "";
            		var str2 = "<option value=''>请选择</option>";
            		if(teachereditFlag == 1){
            			str2 += "<option value='0'>新建计算规则</option>";
            		}
            		tempList = res.data.dataList;
            		res.data.dataList.forEach(function(info,e){
            			if(info.formulaType == formula_flag && info.markFlag==1){
	            			str += "<div class='layui-form-item'>";
	            			str += "<label class='layui-form-label'>"+info.templateName+"</label>";
	            			str += "<div class='layui-input-block'>";
	            			str += "<input type='radio' name='gradeType1' lay-filter='gradeType1' value='"+info.id+"' title='选择'>";
	            			str += "<div class='templat-details' flag='"+e+"'>模板详情</div>";
	            			str += "</div>";
	            			str += "</div>";
	            			
	            			str2 += "<option value='"+info.id+"'>"+info.templateName+"</option>";
            			}
            		});
           			$("#ruleAddPoups .score-list .temp").html(str);
           			if(formula_flag == 0){
		           		$("#ruleAddPoups select[name=module4]").html(str2);
           			}else if(teachereditFlag == 0){
           				$("#ruleAddPoups select[name=module4] option[value=0]").remove();
           			}
            	}
            }
        });
    }
    function getTeaList(){
    	var realname = $("#setInputTeacher input[name='realname']").val();
    	var uname = $("#setInputTeacher input[name='uname']").val();
    	$.ajax({
            type: 'post',
            dataType: 'json',
            url: '/api/score/classcourse/getTeaList?',
            data: {fid:fid,realname:realname,uname:uname},
            success: function (res){
            	if(res.data != null && res.data != ''){
            		var str = "";
            		res.data.forEach(function(info){
            			str += "<li uid='"+info.jsjbxx_xmlxr.puid+"'>";
            			str += "<div class='name'>"+info.jsjbxx_xm+"</div>";
            			str += "<div class='links'>"+info.jsjbxx_jsgh+"</div>";
            			str += "</li>";
            		});
           			$("#setInputTeacher .result-box ul").html(str);
            	}else{
            		$("#setInputTeacher .result-box ul").html("暂无数据");
            	}
            }
        });
    }
    function getItemDictionaryList(){
    	$.ajax({
            type: 'post',
            dataType: 'json',
            url: '/api/score/itemDictionary/getList?',
            data: {fid:fid,xnxq:$("#xnxq").val(),pid:0,status:1},
            success: function (res){
            	if(res.data != null && res.data != ''){
            		var str = "<option value=''>请选择</option>";
            		basicList = res.data;
            		res.data.forEach(function(info){
            			str += "<option value='"+info.id+"' data-code='"+info.itemCode+"'>"+info.itemName+"</option>";
            		});
           			$("#gsbjsubitemTwo select.item").html(str);
           			$("#subitemadd select[name=item]").html(str);
            	}
            }
        });
    }
    function getGradeMakeList(){
    	$.ajax({
            type: 'post',
            dataType: 'json',
            url: '/api/score/gradeMake/getList?',
            data: {fid:fid,xnxq:$("#xnxq").val(),pid:0},
            success: function (res){
            	if(res.data != null && res.data != ''){
            		var str = "<option value=''>请选择</option>";
            		gradeMakeList = res.data;
            		res.data.forEach(function(info){
            			str += "<option value='"+info.id+"' data-code='"+info.gradeCode+"'>"+info.gradeName+"</option>";
            		});
            		$("#subitemadd select[name=grade]").html(str);
           			$("#gsbjsubitemTwo select.grade").html(str);
           			$("#gsbjsubitem select.grade").html(str);
            	}
            }
        });
    }
    function gsbjsubitemTwo(totalType){
    	var itemCode = "";
    	if(totalType == 0){
	    	var id = $("#gsbjsubitemTwo select.item").val();
	    	itemCode = $("#gsbjsubitemTwo select.item option[value='"+id+"']").attr("data-code");
	    	var text = $('#gsbjsubitemTwo select.item option:checked').text();
		    $("#gsbjsubitemTwo .formula .name").html(text+" =");
    	}else{
    		$("#gsbjsubitem .score-breakdown .sb-list").html("");
    	}
    	basicList.forEach(function (info){
    		var code = info.itemCode;
    		if(totalType == 1){
	    		var str = "<div class='item' data-code='"+code+"'>";
	    		str += "<div class='i-con'>";
	    		str += "<div class='left'><div class='name'>"+info.itemName+"</div></div>";
	    		str += "<div class='right scrollBox1'><ul></ul></div>";
	    		str += "</div>";
	    		str += "<div class='i-bottom'>";
                str += "    <div class='select'>";
                str += "    <select name='modules' disabled lay-filter='oneSelect1' lay-verify='required'></select>";
                str += "    </div>";
                str += "  </div>";
	    		str += "</div>";
	    		$("#gsbjsubitem .score-breakdown .sb-list").append(str);
    		}

    		if(totalType == 1 || (totalType == 0 && code == itemCode)){
    			$.ajax({
		            type: 'post',
		            dataType: 'json',
		            url: '/api/score/itemDictionary/getListByCode?',
		            data: {fid:fid,itemCode:info.itemCode},
		            async:false,
		            success: function (res){
		            	if(res.data != null && res.data != ''){
		            		var str = "";
		            		var str2 = "<option value=''>请选择</option>";
		            		res.data.forEach(function(info,e){
			            		str += "<li data-code='"+info.id+"'>";
			            		str += "<div class='name'>"+info.itemName+"</div>";
			            		str += "<div class='select w102'>";
			            		if(gradeMakeList != ''){
				            		str += "<select name='modules' lay-filter='selctOnchange"+(e+1)+"' disabled lay-verify='required'>";
				            		str += "<option value=''>请选择</option>";
			            			gradeMakeList.forEach(function(grade){
			            				str += "<option value='"+grade.id+"'>"+grade.gradeName+"</option>";
			            			})
				            		str += "</select>";
			            		}
			            		str += "</div>";
			            		str += "</li>";
			            		
			            		str2 += "<option value='"+info.id+"'>"+info.itemName+"</option>";
			            		
		            		});
		            		if(totalType == 1){
		            			$("#gsbjsubitem .score-breakdown .sb-list .item[data-code="+code+"] ul").html(str);
		            			$("#gsbjsubitem .score-breakdown .sb-list .item[data-code="+code+"] .i-bottom select").html(str2);
		            		}else{
			           			$("#gsbjsubitemTwo .score-breakdown .sb-list ul").html(str);
		            		}
		            	}
		            }
		        });
    		
    		}
    	
    	
    	});
    	
    	
    }
    function guid () {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
            var r = Math.random() * 16 | 0
            var v = c === 'x' ? r : (r & 0x3 | 0x8)
            return v.toString(16)
        })
    }
    function getformulaType(content){
        var type = "";
        if( content.indexOf("asso") != -1){
            type = "gl"; // 关联数据
        } else if( content.indexOf("spot") != -1 ){
            type = "dian";// 小数点
        } else if( content.indexOf("lbracket") != -1 ){
            type = "zuokuohao"; // 左括号
        } else if( content.indexOf("sign-cancle") != -1 ){
            type = "jian"; // 减
        } else if( content.indexOf("sign-mul") != -1 ){
            type = "cheng"; // 乘
        } else if( content.indexOf("sign-except") != -1 ){
            type = "chu"; //除
        } else if( content.indexOf("sign-add") != -1 ){
            type = "jia"; //加
        } else if( content.indexOf("rbracket") != -1 ){
            type = "youkuohao"; //右括号
        } else if( content.indexOf("zero") != -1 ){
            type = "ling"; //0
        } else if( content == "num" ){
            type = "shuzi"; //数字
        }
        return type;
    }
	var formulaMap = {
		".":"num spot",
		"(":"sign lbracket",
		"-":"sign sign-cancle",
		"×":"sign sign-mul",
		"÷":"sign sign-except",
		"+":"sign sign-add",
		")":"sign rbracket",
		"0":"num zero"
	};
    function getClaName(type){
        var content = "";
        if(type == "gl"){
            content = "asso";
        } else if(type == "dian"){
            content = "num spot";
        } else if(type == "zuokuohao"){
            content = "sign lbracket";
        } else if(type == "jian"){
            content = "sign sign-cancle";
        } else if(type == "cheng"){
            content = "sign sign-mul";
        } else if(type == "chu"){
            content = "sign sign-except";
        } else if(type == "jia"){
            content = "sign sign-add";
        } else if(type == "youkuohao"){
            content = "sign rbracket";
        } else if(type == "ling"){
            content = "num zero";
        } else if(type == "shuzi"){
            content = "num";
        }
        return content;
    }

    function reasonFormula(id) {
        var formula = "";
        $("#" + id).find("span").each(function(){
            if($(this).hasClass("asso")){
                formula += "1";
            } else if($(this).hasClass("sign-mul")){
                formula += "*";
            } else {
                formula += $(this).text();
            }
        });
        return check_gongshi(formula);
    }

    function check_gongshi(cal) {
        if (cal.search(/^[\+\-\*\/\.\)]|[\+\-\*\/\.\(]$|[\+\-\*\/\.]{2}|[^\+\-\*\/\(\)\d\.]|([\d\.\)]\()|(\)[\d\.])|(\([\+\-\*\/\.\)])|([\+\-\*\/\.]\))|(\.\d+\.)/) > -1) {
            return false;
        } else {
            var num_left = 0;
            for (i = 0; i < cal.length; i++) {
                if (cal[i] == '(') {
                    num_left++;
                }
                if (cal[i] == ')') {
                    if (num_left > 0) {
                        num_left--;
                    } else {
                        return false;
                    }
                }
            }
            if (num_left > 0) {
                return false;
            } else {
                return true;
            }
        }
    }
    
    
    
  </script>

</body>

</html>