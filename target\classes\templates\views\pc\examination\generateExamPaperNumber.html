<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>考务</title>
  <link rel="stylesheet" th:href="@{~/css/examination/global2.css}">
  <link rel="stylesheet" th:href="@{~/layui/css/layui.css}">
  <link rel="stylesheet" th:href="@{~/css/examination/poup.css}">
  <style>
    .exam-popup .popup-con .from-wrapper .lable .textarea{
      height:132px;
      overflow-y: auto;
    }
    .exam-popup .popup-con .from-wrapper .lable .name i em:after{
      bottom:-5px;
    }
  </style>


</head>

<body>

  <div id="generateExamNumber" class="exam-popup popups" style="display: block">
    <div class="popup-con">
      <div class="from-wrapper">
        <div class="lable">
          <div class="name">
            <span>编号规则</span>
            <i class="tips"><em>考试编号是由自定义勾选标记组成</em></i>
          </div>
          <div class="checkbox">
            <ul>
              <li id="kkbm">开课院系</li>
              <li id="kecbh">课程编号</li>
              <li id="kcmc">课程名称</li>
<!--              <li class="cur">课程名称</li>-->
<!--              <li id="kcbh">课程编号</li>-->
              <li id="jxbmc">班级名称</li>
              <li id="skls">授课老师</li>
              <li id="xnxq">学年学期</li>
              <li id="kspc">考试批次</li>
              <li id="jxbbh">教学班编号</li>
              <li id="jxbzcbh">教学班组成编号</li>
              <li id="xsssyx">学生所属院系</li>
              <li id="xssszy">学生所属专业</li>
              <li id="xsssbj">学生所属班级</li>
              <li id="xq">校区</li>
              <li id="nj">年级</li>
            </ul>
          </div>
        </div>
        <div class="lable">
          <div class="name">
            <span>试卷编号</span>
            <i class="tips"><em>拖拽可调整编号优先级</em></i>
          </div>
          <div class="textarea">
            <ul id="list">
<!--              <li>-->
<!--                <i>01</i>-->
<!--                <span>课程名称</span>-->
<!--                <em class="delet"></em>-->
<!--              </li>-->
<!--              <li>-->
<!--                <i>02</i>-->
<!--                <span>考试批次</span>-->
<!--                <em class="delet"></em>-->
<!--              </li>-->
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div class="bottom" style="border:none;">
      <div class="cancle">取消</div>
      <div onclick="generateExamPaperNumber()" class="confirm">确定</div>
    </div>
  </div>


</body>

<script th:src="@{~/js/jquery-3.3.1.min.js}"></script>
<script th:src="@{~/plugin/layui-v2.8.18/layui/layui.js}" type="text/javascript" charset="utf-8"></script>
<script th:src="@{~/js/examination/Sortable.js}"></script>
<script th:src="@{~/js/my.util.js}"></script>
<script th:inline="javascript">
  var fid = [[${fid}]];
  var uid = [[${uid}]];
  var r = [[${r}]];
  $(function () {
    var form, table, laydate, layer;
    layui.use(["form", "table", "laydate"], function () {
      var $ = layui.jquery;
      form = layui.form;
      table = layui.table;
      laydate = layui.laydate;
      layer = layui.layer;





      // //隐藏弹窗
      // $('.close,.cancle').on("click", function () {
      //   var index = $(this).parents(".layui-layer").attr("times");
      //   layer.close(index);
      // })

      //选择编号规则
      $(".exam-popup").on("click", ".popup-con .from-wrapper .lable .checkbox ul li", function () {
        $(this).toggleClass("cur");
        let texts = $(this).text();
        if ($(this).hasClass("cur")) {
          let nums = $(".exam-popup .popup-con .from-wrapper .lable .textarea ul li").length + 1;

          let currentNums = nums < 10 ? '0' + nums : nums;

          // let khtml = '<li><i>' + currentNums + '</i><span>' + texts +
          let khtml = '<li id="'+$(this).attr("id") +'"><i>' + currentNums + '</i><span>' + texts +
            '</span><em class="delet"></em></li>'
          $(".textarea ul").append(khtml);
        } else {
          $(".textarea ul li").each(function (i) {
            if ($(this).find("span").text() == texts) {
              $(this).remove();
            }
          })
          computed();
        }
      })

      //计算编号

      function computed() {
        let nums = $(".textarea ul li").length;
        for (let i = 0; i < nums; i++) {
          let j = i + 1;
          let nums = j < 10 ? '0' + j : j;
          $(".textarea ul li").eq(i).find("i").text(nums);
        }
      }

      //删除
      $(".textarea").on("click", " ul li .delet", function () {
        let texts = $(this).parent().find("span").text();
        $(".exam-popup .popup-con .from-wrapper .lable .checkbox ul li").each(function () {
          if ($(this).text() == texts) {
            $(this).removeClass("cur");
          }
        })
        $(this).parent().remove();
        computed();
      })

      //拖拽

      var el = document.getElementById('list');
      var sortable = new Sortable(el, {
        animation: 300,
        onEnd: function (evt) {
          // evt.item 是被拖拽的DOM元素
          // evt.to 是放置的目标列表
          // evt.oldIndex 是移动之前的索引
          // evt.newIndex 是移动之后的索引

          // 调用你想要的方法

          computed();
        }
      });



    });
  });


  function generateExamPaperNumber() {
    const ulList = document.querySelectorAll('ul');
    var generateExamPaperNumberRule = '';
    // 遍历这些ul标签
    ulList.forEach(function(ul) {
      if (ul.id === "list") {
        // 遍历ul中的li标签
        ul.querySelectorAll('li').forEach(function (li) {
          // 处理每个li标签
          generateExamPaperNumberRule += li.id + ','
        });
      } else {
        return
      }
    });
    var data = {
      fid: fid,
      uid: uid,
      generateExamPaperNumberRule: generateExamPaperNumberRule,
      formUserId: JSON.stringify(r.data.formUserId).replaceAll("\"", ''),
    }
    $.post("/examination/result/generateExamPaperNumber", data, function (res) {
      if (res.code == 200) {
        U.success("保存成功", 2000)
        setTimeout(U.closePop, 2000)
      } else {
        U.fail("保存失败", 2000)
      }
    })
  }

  function f() {
    const ulList = document.querySelectorAll('ul');
    var s = '';
    // 遍历这些ul标签
    ulList.forEach(function(ul) {
      if (ul.id === "list") {
        // 遍历ul中的li标签
        ul.querySelectorAll('li').forEach(function(li) {
          // 处理每个li标签
          s += li.id+','
        });
      }else {
        return
      }
      U.success("保存成功", 2000)
      setTimeout(U.closePop, 2000)
    });
  }
</script>
<script>


</script>

</html>