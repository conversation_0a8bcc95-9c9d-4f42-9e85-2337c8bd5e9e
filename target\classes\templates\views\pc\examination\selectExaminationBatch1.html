<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>手动安排考试弹窗</title>
    <link rel="stylesheet" th:href="@{~/css/examination/global2.css}">
    <link rel="stylesheet" th:href="@{~/css/examination/dialog.css}">

</head>

<body>
<!-- 手动安排考试 -->
<div class="masker"></div>
<div class="dialog" id="invigilateMax">
    <div class="dialog-title" th:if="${type==1}">导出考表</div>
    <div class="dialog-title" th:unless="${type==1}">手动排考</div>
    <div class="dialog-con">
        <div class="item">
            <div class="label">选择学年学期</div>
            <div class="j-search-con single-box">
                <input type="text" name="teacherName" placeholder="请选择" readonly="" class="schoolSel" id="xnxq">
                <span class="j-arrow"></span>
                <div class="j-select-year ">
                    <div class="search">
                        <input type="text" placeholder="搜索">
                        <span></span>
                    </div>
                    <ul class="xnxqUl">

                    </ul>
                </div>
            </div>
        </div>
        <div class="item">
            <div class="label">选择考试批次</div>
            <div class="j-search-con single-box">
                <input type="text" name="teacherName" placeholder="请选择" readonly="" class="schoolSel"
                       id="examinationBatch">
                <span class="j-arrow"></span>
                <div class="j-select-year ">
                    <div class="search">
                        <input type="text" placeholder="搜索">
                        <span></span>
                    </div>
                    <ul class="batchUl">
                    </ul>
                </div>
            </div>
        </div>

    </div>
    <div class="dialog-btn" th:if="${type==1}">
        <!--        <button class="pu-cancel">取消</button>-->
        <button class="pu-sure" id="export">导出结果</button>
    </div>
    <div class="dialog-btn" th:unless="${type==1}">
        <button class="pu-sure" id="invigilateSure">开始排考</button>
    </div>
</div>
</body>
<script th:src="@{~/js/jquery-3.3.1.min.js}"></script>
<script th:src="@{~/js/my.util.js}"></script>
<script>
    $(document).ready(function () {
        // 下拉多选
        $(".j-search-con").on("click", ".schoolSel", function (e) {
            $(".j-select").hide();
            let parent = $(this).parent();
            parent.find('.j-arrow').toggleClass('j-arrow-slide');
            parent.find('.j-select-year').toggleClass('slideShow')
            stopBubble(e)
        })

        // 选择-单选
        $(".j-search-con.single-box").on("click",
            ".j-select-year li ",
            function (e) {
                $(this).addClass('active').siblings().removeClass();
                var parents = $(this).parents('.j-search-con');
                var schoolSelEle = parents.find('.schoolSel');
                var txt = $(this).text();
                schoolSelEle.val(txt);
                parents.find('.j-arrow').removeClass('j-arrow-slide');
                parents.find('.j-select-year').removeClass('slideShow')
                stopBubble(e);
            })
        // 搜索
        $(".j-search-con").on('click', '.search input', function (e) {
            stopBubble(e);
        })
        $(".j-search-con").on('input', '.search input', function (e) {
            let val = $(this).val();
            var nextEle = $(this).parents('.j-search-con').find('ul')
            if (val != "") {

                nextEle.find('li').each(function (i, ele) {
                    let txt = $(ele).text();
                    if (txt.indexOf(val) >= 0) {
                        $(ele).show();
                    } else {
                        $(ele).hide();
                    }
                })
            } else {
                nextEle.find('li').show();
            }
            stopBubble(e)
        })

        function stopBubble(e) {
            if (e && e.stopPropagation)
                e.stopPropagation();
            else {
                window.event.cancelBubble = true;
            }

        }

        $(document).on("click", function (event) {
            var _con = $('.select-input');
            if (!_con.is(event.target) && _con.has(event.target).length === 0) {
                $(".j-search-con").find('.j-arrow').removeClass('j-arrow-slide');
                $(".j-search-con").find('.j-select-year').removeClass('slideShow')
            }
        })

    })
</script>
<script th:inline="javascript">
    var fid = [[${fid}]];
    var uid = [[${uid}]];
    var formId = [[${formId}]];
    var xnxq = [[${xnxq}]];
    var batchCode = [[${batchCode}]];
    var curxnxq = '';
    $(function () {
        getXnxq();
    })

    function getXnxq() {
        $.get("/basic/xnxq", {fid: fid}, function (res) {
            if (res.code == 200) {
                var data = res.data;
                var html = '';
                for (let i = 0; i < data.length; i++) {
                    if (data[i].xnxq_xnxqh == xnxq) {
                        curxnxq = data[i].xnxq_xnxqh;
                        html += "<li class='active'>" + data[i].xnxq_xnxqh + "</li>"
                    } else {
                        html += "<li>" + data[i].xnxq_xnxqh + "</li>"
                    }
                }
                if (curxnxq == "") {
                    curxnxq = xnxq;
                }
                $("#xnxq").val(curxnxq);
                $(".xnxqUl").html(html);
                getExaminationBatch(curxnxq);
            }
        })
    }

    function getExaminationBatch(xnxq) {
        $.get("/examination/basic/batch", {fid: fid, uid: uid, xnxq: xnxq}, function (res) {
            if (res.code == 200) {
                var data = res.data;
                var html = '';
                if (data.length == 1) {
                    html += "<li data-value='" + data[0].kspcgl_kspcbh + "' class='active' title='" + data[0].kspcgl_kspcmc + "'>" + data[0].kspcgl_kspcmc + "</li>"
                    $(".batchUl").html(html);
                    $("#examinationBatch").attr("data-value", data[0].kspcgl_kspcbh)
                    $("#examinationBatch").val(data[0].kspcgl_kspcmc)
                    return;
                }
                for (let i = 0; i < data.length; i++) {
                    if (batchCode == data[i].kspcgl_kspcbh) {
                        html += "<li data-value='" + data[i].kspcgl_kspcbh + "' class='active' title='" + data[0].kspcgl_kspcmc + "'>" + data[i].kspcgl_kspcmc + "</li>"
                        $("#examinationBatch").attr("data-value", data[i].kspcgl_kspcbh)
                        $("#examinationBatch").val(data[i].kspcgl_kspcmc)
                    } else {
                        html += "<li data-value='" + data[i].kspcgl_kspcbh + "' title='" + data[0].kspcgl_kspcmc + "'>" + data[i].kspcgl_kspcmc + "</li>"
                    }
                }
                $(".batchUl").html(html);
            } else {
                U.fail(res.msg)
                $(".batchUl").html("");
            }
        })
    }

    $(".j-select-year").on("click", ".xnxqUl li", function (o) {
        if (!$(this).hasClass("active")) {
            var xnxq = $(this).text();
            $("#xnxq").val(xnxq);
            $("#examinationBatch").val("");
            $("#examinationBatch").removeAttr("data-value");
            getExaminationBatch(xnxq);
        }
    })

    $(".j-select-year").on("click", ".batchUl li", function (o) {
        if (!$(this).hasClass("active")) {
            var batch = $(this).attr("data-value");
            $("#examinationBatch").attr("data-value", batch);
        }
    })

    $("#export").click(function () {
        var flag = check();
        if (!flag) {
            return;
        }
        $.get("/examination/result/export", {
            fid: fid,
            uid: uid,
            formId: formId,
            xnxq: $("#xnxq").val(),
            batchCode: $("#examinationBatch").attr("data-value")
        }, function (res) {
            if (res.code == 200) {
                U.success("正在导出,请稍后在导出记录中下载")
                setTimeout(U.closePop, 2000)
            } else {
                U.fail(res.msg)
            }
        })
    })

    $("#invigilateSure").click(function () {
        var flag = check();
        if (!flag) {
            return;
        }
        $.get("/examination/basic/batchCheck", {
            fid: fid,
            bc: $("#examinationBatch").attr("data-value")
        }, function (res) {
            if (res.code == 200) {
                window.open("/examination/result/graphics1.html?fid=" + fid + "&bc=" + $("#examinationBatch").attr("data-value") + "&xnxq=" + $("#xnxq").val(), "_blank");
            } else {
                U.fail(res.msg)
            }
        })

    })

    function check() {
        if ($("#xnxq").val() == undefined || $("#xnxq").val() == '') {
            U.fail("请选择学年学期")
            return false;
        }
        if ($("#examinationBatch").attr("data-value") == undefined || $("#examinationBatch").attr("data-value") == '') {
            U.fail("请选择考试批次");
            return false;
        }

        return true;
    }
</script>

</html>