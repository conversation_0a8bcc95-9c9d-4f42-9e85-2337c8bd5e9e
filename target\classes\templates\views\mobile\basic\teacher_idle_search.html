<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1, user-scalable=no"/>
    <meta name="format-detection" content="telephone=no,email=no,adress=no">
    <meta http-equiv="pragma" content="no-cache"/>
    <meta http-equiv="cache-control" content="no-cache"/>
    <meta http-equiv="expires" content="0"/>
    <title>老师无课查询检索</title>
    <link rel="stylesheet" th:href="@{../../css/basic/common.css}">
    <link rel="stylesheet" th:href="@{../../css/basic/schedule.css}"/>
    <link rel="stylesheet" th:href="@{../../css/basic/iosSelect.css}">
    <link rel="stylesheet" th:href="@{../../css/basic/newStyle.css}"/>
</head>

<body class="">
<div class="tophead">
    <div class="head bottomLine">
        <div class="leftHead">
            <div class="back"></div>
        </div>
        <div class="centerHead">
            <div class="selectBox">
                <div class="selectWeek"><span>检索查询</span></div>
            </div>
        </div>

    </div>

</div>
<div class="list hislocationList">
    <div class="listItem ycenter">
        <div class="itemCon">
            <h1>选择角色</h1>
        </div>
        <div class="itemRight">
            <p class="arrowRight ycenter" style="height: 100%;" id="selectRole"><span>请选择角色</span> <img src="../../images/basic/mobile-arrow-gray-right.png"/></p>
        </div>
    </div>
    <div class="listItem ycenter">
        <div class="itemCon">
            <h1>选择院系</h1>
        </div>
        <div class="itemRight">
            <p class="arrowRight ycenter" id="selectDepart" style="height: 100%;"><span>请选择院系</span><img src="../../images/basic/mobile-arrow-gray-right.png"/></p>
        </div>
    </div>
    <div class="listItem ycenter">
        <div class="itemCon">
            <h1><span>*</span>选择周次</h1>
        </div>
        <div class="itemRight">
            <p class="arrowRight ycenter" id="week" style="height: 100%;"><span>请选择周次</span><img src="../../images/basic/mobile-arrow-gray-right.png"/></p>
        </div>
    </div>
    <div class="listItem ycenter">
        <div class="itemCon">
            <h1><span>*</span>选择老师</h1>
        </div>
        <div class="itemRight">
            <p class="arrowRight ycenter" style="height: 100%;" id="selectTeacher"><span>请选择老师</span><img src="../../images/basic/mobile-arrow-gray-right.png"/></p>
        </div>
    </div>
    <div class="listItem ycenter">
        <div class="itemCon">
            <h1><span>*</span>选择老师工号</h1>
        </div>
        <div class="itemRight">
            <p class="arrowRight ycenter" style="height: 100%;" id="selectTeacherNumber"><span>请选择老师工号</span><img src="../../images/basic/mobile-arrow-gray-right.png"/></p>
        </div>
    </div>
</div>

<div class="recall-bottom">
    <div class="refresh">重置</div>
    <div class="search">查询</div>
</div>


<div class="choosedepartment-dialog" id="choosedepartment">
    <div class="w-con">
        <div class="w-head">
            <div class="cancle">取消</div>
            <div class="name">选择院系</div>
            <div class="btns">
                <span class="acllSelect">全选</span>
                <span class="save">保存</span>
            </div>
        </div>
        <div class="w-box">
            <div class="search-inputs">
                <div class="s-con">
                    <img src="../../images/basic/mobile-search-icons.png" alt="">
                    <input type="search" placeholder="请输入" id="mSearch">
                    <span class="m-close" style="display: none;"></span>
                </div>
            </div>
            <ul>
            </ul>
        </div>

    </div>
</div>

<div class="choosedepartment-dialog" id="chooserole">
    <div class="w-con">
        <div class="w-head">
            <div class="cancle">取消</div>
            <div class="name">选择角色</div>
            <div class="btns">
                <span class="acllSelect">全选</span>
                <span class="save">保存</span>
            </div>
        </div>
        <div class="w-box">
            <div class="search-inputs">
                <div class="s-con">
                    <img src="../../images/basic/mobile-search-icons.png" alt="">
                    <input type="search" placeholder="请输入" id="mSearch">
                    <span class="m-close" style="display: none;"></span>
                </div>
            </div>
            <ul>
            </ul>
        </div>

    </div>
</div>
<div class="choosedepartment-dialog" id="chooseteacher">
    <div class="w-con">
        <div class="w-head">
            <div class="cancle">取消</div>
            <div class="name">选择老师</div>
            <div class="btns">
                <span class="acllSelect">全选</span>
                <span class="save">保存</span>
            </div>
        </div>
        <div class="w-box">
            <div class="search-inputs">
                <div class="s-con">
                    <img src="../../images/basic/mobile-search-icons.png" alt="">
                    <input type="search" placeholder="请输入" id="mSearch">
                    <span class="m-close" style="display: none;"></span>
                </div>
            </div>
            <ul>
            </ul>
        </div>

    </div>
</div>
<div class="choosedepartment-dialog" id="chooseteachernumer">
    <div class="w-con">
        <div class="w-head">
            <div class="cancle">取消</div>
            <div class="name">选择老师工号</div>
            <div class="btns">
                <span class="acllSelect">全选</span>
                <span class="save">保存</span>
            </div>
        </div>
        <div class="w-box">
            <div class="search-inputs">
                <div class="s-con">
                    <img src="../../images/basic/mobile-search-icons.png" alt="">
                    <input type="search" placeholder="请输入"/>
                    <span class="m-close" style="display: none;"></span>
                </div>
            </div>
            <ul>
            </ul>
        </div>

    </div>
</div>
<script>let _VR_ = "[[${_VR_}]]";</script>
<script th:src="@{../../js/jquery-3.3.1.min.js}"></script>
<script th:src="@{../../js/basic/mobile_teacher_idle.js}"></script>
<script th:src="@{../../js/iosSelect.js}"></script>
<script th:src="@{../../js/CXJSBridge.js}"></script>
<script th:src="@{../../js/app.utils.js}"></script>
<script th:src="@{../../js/tools.js}"></script>
<script th:src="@{../../js/my.util.js}"></script>
</body>
</html>
