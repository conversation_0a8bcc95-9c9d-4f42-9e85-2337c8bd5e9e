<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" li-content="IE=edge">
    <meta name="viewport" li-content="width=device-width, initial-scale=1.0">
    <title>微点名</title>
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/global.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/layui1/css/layui.css'">
    <link rel="stylesheet" href="/elective/rollcall/css/commonPC.css">
    <link rel="stylesheet" href="/elective/rollcall/css/reset.css">
    <link rel="stylesheet" href="/elective/rollcall/css/poup.css">
    <link rel="stylesheet" href="/elective/rollcall/css/microdot-name_pc_stu_index.css">
    <link rel="stylesheet" href="/elective/rollcall/css/new-microdot-name.css">
    <link rel="stylesheet" href="/elective/rollcall/css/name-calling-end.css">
    <script th:src="${_CPR_}+'/elective/layui1/layui.js'"></script>

    <style>
        body .main .con {
            height: auto;
            min-height: calc(100vh - 196px);
        }

        body .main .con .table {
            padding-left: 30px;
        }

        body .main .con .filter-box.filter-box {
            justify-content: flex-start;
        }

        body .main .con .filter-box.filter-box .layui-form .layui-form-item {
            margin-bottom: 24px;
        }

        .main .con .table .detail span i.color6 {
            color: rgba(175, 119, 255, 1);
        }

        .layui-form-select dl dd.layui-select-tips {
            display: block;
        }

        .layui-form-select dl dd.layui-select-tips {
            padding-left: 20px !important;
        }

        body .main .con .filter-box .layui-form .layui-form-label {
            width: auto;
            text-align: left;
        }

        .roll-call-record .layui-table-view .layui-table td:nth-child(3) .layui-table-cell {
            overflow: hidden;
        }

        body .main .con .filter-box .layui-form .layui-input-block .times {
            width: 240px;
            height: 34px;
            border-radius: 4px;
            box-sizing: border-box;
            cursor: pointer;
            background: url(../images/calendar-icons.png) no-repeat right 10px center;
            background-size: 12px;
        }

        body .main .con .filter-box .button-list {
            flex-direction: column;
            padding-right: 25px;
        }

        body .main .con .filter-box .button-list .searcher {
            margin-right: 0;
        }

        body .main .con .filter-box .button-list .reset {
            margin-top: 16px;
            margin-right: 0;
        }

        .layui-table-grid-down {
            display: none !important;
        }

        body .main .con .filter-box .inform {
            flex-wrap: wrap;
            height: auto;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(232, 235, 241, 1);
        }

        body .main .con .filter-box .layui-form {
            flex: 1;
        }

        .layui-table-view .layui-table tr th > div {
            height: 36px;
            line-height: 36px;
        }

        .layui-table-cell {
            height: 36px;
            line-height: 36px;
        }
    </style>

</head>

<body>
<div class="main">
    <div class="top">
        <div class="title">
            <div class="back">返回</div>
            <div class="levelone">教师点名统计</div>
        </div>
    </div>
    <div class="con">
        <div class="filter-box border">
            <div class="inform">
                <div class="i-item" style="margin-right:96px;">
                    <div class="name">学年学期：</div>
                    <div class="text" th:text="${xnxqh}"></div>
                </div>
                <div class="i-item" style="margin-right:96px;">
                    <div class="name">教师姓名：</div>
                    <div class="text" th:text="${teaName}"></div>
                </div>
                <div class="i-item" style="margin-right:96px;">
                    <div class="name">工号：</div>
                    <div class="text" th:text="${teaCode}"></div>
                </div>
                <div class="i-item" style="margin-right:96px;">
                    <div class="name">部门：</div>
                    <div class="text" th:text="${jsjbxx_bm}"></div>
                </div>
                <div class="i-item" style="margin-right:96px;">
                    <div class="name">系部：</div>
                    <div class="text" th:text="${jsjbxx_yx}"></div>
                </div>
                <div class="i-item" style="margin-right:96px;">
                    <div class="name">教研室：</div>
                    <div class="text" th:text="${jsjbxx_jysks}"></div>
                </div>
                <div class="i-item" style="margin-right:96px;">
                    <div class="name">日期区间：</div>
                    <div class="text" th:text="${courseTime}"></div>
                </div>
            </div>
        </div>
        <div class="filter-box" style="margin-bottom:40px;">
            <form class="layui-form" action="" id="example" lay-filter="myFormFilter">
                <div class="layui-form-item">
                    <label class="layui-form-label">课程名称</label>
                    <div class="layui-input-block w240 sel">
                        <div class="select-input">
                            <div class="name" data-name="请选择" id="pkjgsj_kcmcVal">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <div class="all-selects">全选</div>
                                <ul class="dropdown-lists" id="pkjgsj_kcmc">
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">教学班名称</label>
                    <div class="layui-input-block w240 sel">
                        <div class="select-input">
                            <div class="name" data-name="请选择" id="pkjgsj_jxbmcVal">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <div class="all-selects">全选</div>
                                <ul class="dropdown-lists" id="pkjgsj_jxbmc">
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">教学班组成</label>
                    <div class="layui-input-block w240 sel">
                        <div class="select-input">
                            <div class="name" data-name="请选择" id="pkjgsj_jxbzcVal">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <div class="all-selects">全选</div>
                                <ul class="dropdown-lists" id="pkjgsj_jxbzc">
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">点名状态</label>
                    <div class="layui-input-block w240">
                        <select name="isRc" lay-filter="isRc" id="isRc" lay-verify="required"
                                lay-search="">
                            <option value=''>请选择</option>
                            <option value='false'>未点名</option>
                            <option value='true'>已点名</option>
                        </select>
                    </div>
                </div>
            </form>
            <div class="button-list">
                <div class="searcher" lay-filter="formDemo">查询</div>
                <div class="reset">重置</div>
            </div>

        </div>
        <div class="table">
            <div class="tab-cons roll-call-record">
                <table class="layui-table" id="materialTable" lay-filter="materialTable"></table>
            </div>


        </div>


    </div>
    <div id="coursePage"></div>
</div>


</body>
<script type="text/html" id="tmplToolBar">
    <div class="oprate-table">
        <div class="seek" lay-event="seek" style="color:#3A8BFF;;cursor:pointer;">查看详情</div>
    </div>
</script>
<script th:src="${_CPR_}+'/elective/js/jquery-3.3.1.min.js'"></script>
<script src="/elective/rollcall/js/common1.js"></script>
<script th:inline="javascript">
    let pageIndex = 1;
    var pageSize = 10;
    var teaUid = [[${teaUid}]];
    var courseTime = [[${courseTime}]];
    var teaCode = [[${teaCode}]];
    var searchArr = [
        {formAlias: "pkjgsj", fieldAlias: "pkjgsj_kcmc"}
        , {formAlias: "pkjgsj", fieldAlias: "pkjgsj_jxbmc"}
        , {formAlias: "pkjgsj", fieldAlias: "pkjgsj_jxbzc"}
    ];
    layui.use(['table', 'jquery', 'laypage', 'laydate', 'form'], function () {

        var table = layui.table;
        var laypage = layui.laypage;
        var $ = layui.jquery;
        var layer = layui.layer;
        var laydate = layui.laydate;
        var form = layui.form;


        var table1 = table.render({
            elem: "#materialTable",
            id: 'materialTable',
            url: '/elective/rollcall/getTeachingClassListV1',
            where: {
                courseTime: courseTime,
                xgh: teaCode,
                page: pageIndex,
                limit: pageSize,
                teachingClassName: $('#pkjgsj_jxbmcVal').text(),
                courseName: $('#pkjgsj_kcmcVal').text(),
                jxbzc: $('#pkjgsj_jxbzcVal').text(),
                isRc:$('select[name="isRc"] option:selected').val(),
            },
            cols: [
                [{
                    field: "pkjgsj_kcmc",
                    align: "center",
                    title: "课程名称",
                    minWidth: 200
                },
                    {
                        field: "pkjgsj_jxbmc",
                        align: "center",
                        title: "教学班名称",
                        minWidth: 200
                    },
                    {
                        field: "pkjgsj_jxbzc",
                        align: "center",
                        title: "教学班组成",
                        minWidth: 200
                    },
                    {
                        field: "time",
                        align: "center",
                        title: "上课时间",
                        minWidth: 200,
                        templet: function (d) {
                            return "第" + d.pkjgsj_zc + "周" +" 周"+ d.pkjgsj_xq+" 第"+d.pkjgsj_kj+"节";
                        }
                    },
                    {
                        field: "pkjgsj_sfwcdm",
                        align: "center",
                        title: "点名状态",
                        minWidth: 200,
                        templet: function (d) {
                            return d.pkjgsj_sfwcdm == '是'?"已点":"未点";
                        }
                    },
                    {
                        field: "options",
                        align: "center",
                        title: "操作",
                        toolbar: "#tmplToolBar",
                        width: 88,
                        fixed: "right",
                    },

                ]
            ],
            done: function (res) {
                //分页
                laypage.render({
                    elem: 'coursePage',
                    curr: pageIndex,
                    groups: 5,
                    limit: pageSize,
                    limits: [10, 20, 30],
                    count: res.count,
                    layout: ['count', 'prev', 'page', 'next', 'limit', 'limits', 'skip'],
                    jump: function (obj, first) {
                        if (!first) {
                            pageIndex = obj.curr;
                            pageSize = obj.limit;
                            table.reload('materialTable', {
                                where: {
                                    courseTime: courseTime,
                                    xgh: teaCode,
                                    page: pageIndex,
                                    limit: pageSize,
                                    teachingClassName: $('#pkjgsj_jxbmcVal').text(),
                                    courseName: $('#pkjgsj_kcmcVal').text(),
                                    jxbzc: $('#pkjgsj_jxbzcVal').text(),
                                    isRc:$('select[name="isRc"] option:selected').val(),
                                },
                            }); //只重载数据
                        }
                    }
                });
            }
        })

        $(".searcher").click(function () {
            pageIndex = 1;
            table.reload('materialTable', {
                where: {
                    courseTime: courseTime,
                    xgh: teaCode,
                    page: pageIndex,
                    limit: pageSize,
                    teachingClassName: $('#pkjgsj_jxbmcVal').text(),
                    courseName: $('#pkjgsj_kcmcVal').text(),
                    jxbzc: $('#pkjgsj_jxbzcVal').text(),
                    isRc:$('select[name="isRc"] option:selected').val(),
                },
            }); //只重载数据
        })

        table.on("tool(materialTable)", function (obj) {
            var data = obj.data; //获得当前行数据
            if (obj.event === "seek") {
                window.open("/elective/rollcall/toJxbStaticDetail?mobile=pc&teachingClassCode=" + data.pkjgsj_jxbbh
                    + "&teachingClassName=" + data.pkjgsj_jxbmc +
                    + "&course=" + data.pkjgsj_kcmc + "&teacherName=" + data.pkjgsj_jsxm
                    + "&zc=" + data.pkjgsj_zc + "&xq=" + data.pkjgsj_xq + "&kj=" + data.pkjgsj_kj
                    , "_self");
                // zc=&xq=&kj=&&clazzName=&courseTime=
                console.log("查看详情")

            }
        })


        //返回

        $(".main .top .title .back").click(function () {
            window.history.go(-1);
        })


        resizeHh();

        $('.filter-box .button-list .reset').click(function () {
            console.log("重置");
            layui.form.render();
            $("#pkjgsj_jxbmcVal").text('请选择');
            $("#pkjgsj_kcmcVal").text('请选择');
            $("#pkjgsj_jxbzcVal").text('请选择');

        });

        function resizeHh() {
            let hh = $(".roll-call-record").offset().top + 106;
            let cuurent = $(window).height() - hh;
            $(".roll-call-record").css("min-height", cuurent);
        }

        $(window).resize(function () {
            resizeHh();
        });

        for (let ob of searchArr) {
            $.ajax({
                type: "get",
                url: "/elective/rollcall/getFormDistinctFiled",
                data: ob,
                dataType: 'json',
                // async: false,
                success: function (data) {
                    var html = "";
                    if (data.status) {
                        for (var i = 0; i < data.data.length; i++) {
                            if (data.data[i] == '') {
                                continue;
                            }
                            html += "<li><span>" + data.data[i] + "</span></li>";
                        }
                    }
                    $("#" + ob.fieldAlias).html(html);
                    layui.form.render("select");
                }
            })
        }


    });
</script>

</html>