<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微点名后台配置</title>
    <link rel="stylesheet" href="css/global.css">
    <link rel="stylesheet" href="css/reset.css">
    <link rel="stylesheet" href="css/rollcallIndex.css?v=2">
</head>
<body>
<div class="main">
    <div class="top">
        <div class="title">
            <div class="levelone">微点名后台配置</div>
        </div>
        <div class="btn">保存</div>
    </div>
    <div class="con">
        <div class="c-item default-item">
            <h3>连堂点名设置</h3>
            <div class="lab">
                <div class="name" style="width:238px;">是否允许连堂点名
                    <span class="notes" id="notes"></span>
                    <div class="tips">
                        <h4>开启后可一次对多节连堂课程进行统一点名</h4>
                    </div>
                </div>
                <div class="switc-con">
                    <div class="switch" onclick="beforeUpdateConfig('ltdm');"><span></span></div>
                    <div class="switch-con" id="ltdmdiv">关闭</div>
                </div>
            </div>
        </div>
        <div class="c-item default-item">
            <h3>点名时间设置</h3>
            <div class="lab">
                <div class="name" style="width:238px;">是否允许对今天之前的教学班补录点名</div>
                <div class="switc-con">
                    <div class="switch switch-open" onclick="beforeUpdateConfig('bldm');"><span></span></div>
                    <div class="switch-con" id="bldmdiv">开启</div>
                </div>
            </div>
            <!--                <div class="lab">-->
            <!--                    <div class="name" style="width:238px;">补录点名是否需要审批</div>-->
            <!--                    <div class="switc-con">-->
            <!--                        <div class="switch" onclick="beforeUpdateConfig('bldmsp');"><span></span></div>-->
            <!--                        <div class="switch-con" id="bldmspdiv">关闭</div>-->
            <!--                    </div>-->
            <!--                </div>-->
        </div>
        <div class="c-item default-item">
            <h3>通知设置</h3>
            <div class="lab">
                <div class="name" style="width:238px;">点名异常是否通知学生的班主任</div>
                <div class="switc-con">
                    <div class="switch" onclick="beforeUpdateConfig('dmyc');"><span></span></div>
                    <div class="switch-con" id="dmycdiv">关闭</div>
                </div>
            </div>
        </div>

        <!-- 2025.3.24 -->

        <div class="c-item status-item">
            <h3>点名状态设置</h3>
            <p>提示：默认点名状态为“正常”，设置并启用后可修改为其它状态，最多可启用5个其它状态</p>
            <div class="lab-list ">
                <div class="lab" th:each="st,userStat: ${rollcallStateList}">
                    <input type="hidden" name ="id" th:value="${st.id}">
                    <div class="txt" th:text="'状态'+${userStat.count}+':'"></div>
                    <div th:class="${st.type==0} ? 'input big-input disabled' : 'input big-input'">
                        <input type="text" th:disabled="${st.type==0}" name="stateNamePc" th:value="${st.stateNamePc}" placeholder="请输入">
                    </div>
                    <div class="mobile-name">移动端显示</div>
                    <div th:class="${st.type==0} ? 'input small-input disabled' : 'input small-input'">
                        <input type="text" th:disabled="${st.type==0}" name="stateNameMobile"  maxlength="1" th:value="${st.stateNameMobile}" placeholder="">
                    </div>
                    <div class="switc-con">
                        <div th:class="${st.display==0} ? 'switch switch-open' : 'switch'"><span></span></div>
                        <div class="switch-con" th:text="${st.display==0} ? '开启':'关闭'"></div>
                    </div>
                    <div class="delete" th:if="${userStat.count >4}"></div>
                    <div class="add" ></div>
                </div>
                <div class="lab" th:if="${#lists.size(rollcallStateList)==4}">
                    <input type="hidden" name ="id" value="">
                    <div class="txt">状态5：</div>
                    <div class="input">
                        <input type="text"  name="stateNamePc" placeholder="请输入">
                    </div>
                    <div class="mobile-name">移动端显示</div>
                    <div class="input small-input ">
                        <input type="text" name="stateNameMobile" maxlength="1" value="" placeholder="">
                    </div>
                    <div class="switc-con">
                        <div class="switch"><span></span></div>
                        <div class="switch-con">关闭</div>
                    </div>
                    <div class="delete"></div>
                    <div class="add"></div>
                </div>
            </div>
        </div>

        <div class="c-item default-item">
            <h3>晚自习点名设置</h3>
            <div class="lab">
                <div class="name" style="width:238px;">晚自习是否只点住宿学生</div>
                <div class="switc-con">
                    <div class="switch" onclick="beforeUpdateConfig('wzxzs');"><span></span></div>
                    <div class="switch-con" id="wzxzsdiv">关闭</div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="tips-box"></div>
<!--二次确认弹窗 2025.4.2 -->
<div class="mask"></div>
<div id="confirmDialog" class="tips-popup popups">
    <div class="title">
        <div class="name">提示</div>
    </div>
    <div class="popup-con">
        <p>禁用后新点名不可选择此状态，统计端将不显示该状态统计情况，确认禁用？</p>

    </div>
    <div class="bottom">
        <div class="cancle">取消</div>
        <div class="confirm">确定</div>
    </div>
</div>
</body>
<script src="js/jquery-3.3.1.min.js"></script>
<script src="js/rollcallCommon.js"></script>
<script th:inline="javascript">
    var fid = [[${fid}]];
    var ltdm = [[${ltdm}]];
    var bldm = [[${bldm}]];
    var bldmsp = [[${bldmsp}]];
    var dmyc = [[${dmyc}]];
    var wzxzs = [[${wzxzs}]];
    if (wzxzs == '1') {
        $("#wzxzsdiv").html("开启");
        $("#wzxzsdiv").parent().find(".switch").addClass("switch-open");
    }
    if (ltdm == '1') {
        $("#ltdmdiv").html("开启");
        $("#ltdmdiv").parent().find(".switch").addClass("switch-open");
    }
    if (ltdm == '2') {
        $("#ltdmdiv").html("关闭");
        $("#ltdmdiv").parent().find(".switch").removeClass("switch-open");
    }
    if (bldm == '1') {
        $("#bldmdiv").html("开启");
        $("#bldmdiv").parent().find(".switch").addClass("switch-open");
        $("#bldmspdiv").parents(".lab").show();
    }
    if (bldm == '2') {
        $("#bldmdiv").html("关闭");
        $("#bldmdiv").parent().find(".switch").removeClass("switch-open");
        $("#bldmspdiv").parents(".lab").hide();
    }
    if (bldmsp == '1') {
        $("#bldmspdiv").html("开启");
        $("#bldmspdiv").parent().find(".switch").addClass("switch-open");
    }
    if (bldmsp == '2') {
        $("#bldmspdiv").html("关闭");
        $("#bldmspdiv").parent().find(".switch").removeClass("switch-open");
    }
    if (dmyc == '1') {
        $("#dmycdiv").html("开启");
        $("#dmycdiv").parent().find(".switch").addClass("switch-open");
    }
    if (dmyc == '2') {
        $("#dmycdiv").html("关闭");
        $("#dmycdiv").parent().find(".switch").removeClass("switch-open");
    }
    $(function () {
        // 开关
        $(".main .con").on("click", ".c-item.default-item .lab .switc-con .switch", function () {
            if ($(this).hasClass("switch-open")) {
                $(this).removeClass("switch-open");
                $(this).next().text('关闭');
                $(this).parents(".lab").next().hide();
            } else {
                $(this).addClass("switch-open");
                $(this).next().text('开启');
                $(this).parents(".lab").next().show();
            }
        })
        // 提示
        $("#notes").mouseenter(function () {
            $(this).next().show();
        }).mouseleave(function () {
            $(this).next().hide();
        })

        //增加

        $(".main").on("click", ".con .c-item.status-item .lab-list .lab .add", function () {
            let number = $(".main .con .c-item.status-item .lab-list .lab").length + 1;
            console.log(number);
            if (number != 5) {
                $(".main .con .c-item.status-item .lab-list .lab").eq(4).find(".delete").show();
            }

            var phtml = '<div class="lab">' +
                '<input type="hidden" name="id" value="">' +
                '<div class="txt">状态' + number + '：</div>' +
                '<div class="input big-input">' +
                '<input type="text" name="stateNamePc" placeholder="请输入">' +
                '</div>' +
                '<div class="mobile-name">移动端显示</div>' +
                '<div class="input small-input ">' +
                '<input type="text" maxlength="1" name="stateNameMobile" value="" placeholder="">' +
                '</div>' +
                '<div class="switc-con">' +
                '<div class="switch"><span></span></div>' +
                '<div class="switch-con">关闭</div>' +
                '</div>' +
                ' <div class="delete"></div>' +
                '<div class="add"></div>' +
                '</div>'
            $(".main .con .c-item.status-item .lab-list").append(phtml);

            pdIcon();
        })

        $(".main .con").on("click", ".c-item.status-item .lab .switc-con .switch", function () {
            if ($(this).hasClass("switch-open")) {
                // $(this).removeClass("switch-open");
                // $(this).next().text('关闭');
                $("#targetId").removeAttr("id");
                $(this).attr("id", "targetId");
                $(".mask").fadeIn();
                $("#confirmDialog").fadeIn();
            } else {

                let lengths = $(".main .con .c-item.status-item .lab .switc-con .switch.switch-open")
                    .length;

                if (lengths == 5) {
                    tips("最多可以启用五个状态");
                    return false;
                }

                $(this).addClass("switch-open");
                $(this).next().text('开启');
            }
        })
        pdIcon();
        function pdIcon(){
            let number = $(".main .con .c-item.status-item .lab-list .lab").length;
            if (number==5) {
                $(".main .con .c-item.status-item .lab-list .lab").eq(4).find(".delete").hide();
            }
        }

        $(".main").on("click", " .con .c-item.status-item .lab-list .lab .delete", function () {

            $(this).parents(".lab").remove();
            $(".main .con .c-item.status-item .lab-list .lab").each(function (index, item) {
                $(item).find(".txt").text("状态" + (index + 1) + "：");
            })

            pdIcon();
        })

        $(".btn").on("click",function (){
            var stateArr = [];
            $(".main .con .c-item.status-item .lab-list .lab").each(function (index, item) {
                let id = $(item).find("input[name=id]").val();
                let stateNamePc = $(item).find("input[name=stateNamePc]").val();
                let stateNameMobile = $(item).find("input[name=stateNameMobile]").val();
                let display = $(item).find(".switc-con .switch").hasClass("switch-open");
                if (stateNamePc!=undefined &&stateNamePc!='' &&stateNameMobile!=undefined && stateNameMobile!=''){
                    stateArr.push({id:id,stateNamePc:stateNamePc,stateNameMobile:stateNameMobile,display:display?0:1});
                }
            })
            console.log(stateArr)
            $.ajax({
                type: "POST",
                url: "/elective/rollcall/editRollcallState",
                data: JSON.stringify(stateArr),
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    var msg = "设置成功";
                    if (!data.status){
                        msg = data.msg
                    }
                    tips(msg);
                }
            });
        })
    })

    //input blur 事件 2025.4.2 start
    $(".main").on("blur", ".con .c-item.status-item .lab-list .lab .big-input input", function () {
        let indexs = $(this).parents(".lab").index();
        identify('.big-input', $(this), indexs);
    })


    $(".main").on("keypress", ".con .c-item.status-item .lab-list .lab .big-input input", function (e) {
        if (e.which === 13) {
            let indexs = $(this).parents(".lab").index();
            identify('.big-input', $(this), indexs);
        }
    });

    $(".main").on("blur", ".con .c-item.status-item .lab-list .lab .small-input input", function () {
        let indexs = $(this).parents(".lab").index();
        identify('.small-input', $(this), indexs);
    })


    $(".main").on("keypress", ".con .c-item.status-item .lab-list .lab .small-input input", function (e) {
        if (e.which === 13) {
            let indexs = $(this).parents(".lab").index();
            identify('.small-input', $(this), indexs);
        }
    });

    function identify(target, obj, indexs) {
        let value = obj.val();

        let arr = [];

        $(".main .con .c-item.status-item .lab-list .lab").each(function () {
            let val = $(this).find(target + " input").val();
            arr.push(val);
        });

        let hasDuplicate = false;

        $.each(arr, function (index, value) {
            if ($.inArray(value, arr) !== index) {
                hasDuplicate = true;
                return false; // 跳出循环
            }
        });

        if (hasDuplicate) {
            if (target == '.big-input') {
                tips("点名状态已存在")
            } else {
                tips("移动端显示名称已存在")
            }

            return;
        }
    }

    let timers;

    function tips(texts) {
        $(".tips-box").text(texts).fadeIn();
        clearTimeout(timers);
        timers = setTimeout(function () {
            $(".tips-box").fadeOut();
        }, 2000);
    }

    //input blur 事件 2025.4.2 end

    $("#confirmDialog .bottom div.cancle").click(function () {
        $(".mask").hide();
        $("#confirmDialog").hide();
    })


    $("#confirmDialog .bottom div.confirm").click(function () {
        $(".mask").hide();
        $("#confirmDialog").hide();
        $("#targetId").removeClass("switch-open");
        $("#targetId").next().text('关闭');
    })

    function beforeUpdateConfig(str) {
        var temp = $("#" + str + "div").html();
        if (temp == '关闭') {
            var status = 1;
            if (str=='wzxzs'){
                status = 1;
            }
            updateRollcallConfig(str, status);
        }
        if (temp == '开启') {
            var status = 2;
            if (str=='wzxzs'){
                status = 0;
            }
            updateRollcallConfig(str, status);
        }
    }

    function updateRollcallConfig(str, status) {
        $.ajax({
            type: "POST",
            url: "/elective/rollcall/editRollcallConfig",
            data: {fid: fid, str: str, status: status},
            success: function () {
            }
        });
    }
</script>
</html>