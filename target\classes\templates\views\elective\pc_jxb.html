<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" li-content="IE=edge">
    <meta name="viewport" li-content="width=device-width, initial-scale=1.0">
    <title>微点名</title>
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/global.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/layui/css/layui.css'">
    <link rel="stylesheet" href="css/commonPC.css">
    <link rel="stylesheet" href="css/reset.css">
    <link rel="stylesheet" href="css/poup.css">
    <link rel="stylesheet" href="css/microdot-name_pc_jxb.css"> <!-- 2024.12.5 -->
    <script th:src="${_CPR_}+'/elective/layui/layui.js'"></script>

</head>

<body>
<div class="main">
    <div class="top">
        <div class="title">
            <div class="back"><a href="#" onclick="javascript:window.history.back();">返回</a></div>
            <div class="levelone">微点名统计</div>
            <div class="icon"></div>
            <div class="leveltwo">查看点名详情</div>
        </div>
    </div>
    <div class="con">
        <div class="filter-box">
            <div class="inform">
                <div class="i-item">
                    <div class="name">课程：</div>
                    <div class="text" th:inline="text">[[${courseName}]]</div>
                </div>
                <div class="i-item">
                    <div class="name">教学班：</div>
                    <div class="text" th:inline="text">[[${jxbmc}]]</div>
                </div>
                <div class="i-item">
                    <div class="name">上课时间：</div>
                    <div class="text" th:inline="text">
                        <span>第[[${zc}]]周</span><span>周[[${xq}]]</span><span>第[[${kj}]]节</span></div>
                </div>
            </div>
<!--            <div class="search">-->
<!--                <input type="text" id="keyWord" placeholder="搜索学生姓名或学号">-->
<!--                <span id="search"></span>-->
<!--            </div>-->
        </div>
        <!-- 2024.12.5 -->
        <div class="filter-box">
            <form class="layui-form" action="" id="example" lay-filter="myFormFilter">
                <div class="layui-form-item">
                    <label class="layui-form-label">学生姓名</label>
                    <div class="layui-input-block w240">
                        <input type="text" name="studentName" id="keyWord" class="layui-input" placeholder="请输入">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">学生学号</label>
                    <div class="layui-input-block w240">
                        <input type="text" name="studentId" id="uname" class="layui-input" placeholder="请输入">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">点名状态</label>
                    <div class="layui-input-block w240 sel">
                        <div class="select-input">
                            <div class="name" data-name="请选择点名状态" id="selectState">请选择点名状态</div>
                            <em></em>
                            <div class="select-dropdown">
                                <div class="all-selects">
                                    全选
                                </div>
                                <ul class="dropdown-lists">
                                    <li id="statezc">
                                        <span stateId = "zc">正常</span>
                                    </li>
                                    <li th:each="state:${rollcallStateList}">
                                        <span th:attr="stateId=${state.type ==0?state.stateAlias:state.id}" th:text="${state.stateNamePc}"></span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
            <div class="button-list">
                <div class="reset">重置</div>
                <div class="searcher" lay-filter="formDemo" id="search">查询</div>
            </div>

        </div>
        <div class="table">
            <div class="tab-cons roll-call-record">
                <table class="layui-table" id="materialTable" lay-filter="materialTable"></table>
            </div>

            <div id="coursePage"></div>
        </div>


    </div>
</div>


</body>

<script th:src="${_CPR_}+'/elective/js/jquery-3.3.1.min.js'"></script>
<script th:inline="javascript">
    let pageIndex = 1;
    let pageSize = 10;
    var jxb = [[${jxb}]];
    var kj = [[${kj}]];
    var courseTime = [[${courseTime}]];
    var sksj = [[${sksj}]];
    var state = [[${state}]];
    var rollcallStateList = [[${rollcallStateList}]];
    layui.use(['table', 'jquery', 'laypage', 'laydate', 'form'], function () {

        var table = layui.table;
        var laypage = layui.laypage;
        var $ = layui.jquery;
        var layer = layui.layer;
        var laydate = layui.laydate;
        var form = layui.form;
        //2024.12.5
        resizeHh();

        $('.filter-box .button-list .reset').click(function () {
            console.log("重置");

            form.val("myFormFilter", { // myFormFilter 是表单定义的lay-filter的值
                "studentName": '',
                "studentId": '',
                "callStatus": '',
            });

            layui.form.render();
            let placeholdertext = $(
                ".filter-box .layui-form .layui-form-item .sel .select-input .name.ckd").attr(
                "data-name");
            $(".filter-box .layui-form .layui-form-item .sel .select-input .name.ckd").removeClass(
                "ckd").text(placeholdertext);
            $(".filter-box .layui-form .layui-form-item .sel .select-input .select-dropdown .dropdown-lists li")
                .removeClass("cur");
            $(".filter-box .layui-form .layui-form-item .sel .select-input .select-dropdown .all-selects.cur")
                .removeClass("cur").text("全选");
        });

        function resizeHh() {
            let hh = $(".roll-call-record").offset().top + 106;
            let cuurent = $(window).height() - hh;
            $(".roll-call-record").css("min-height", cuurent);
        }

        $(window).resize(function () {
            resizeHh();
        });


        // 2024.2.4
        $(document).on("click", function (event) {
            var _con = $('.select-input');
            if (!_con.is(event.target) && _con.has(event.target).length === 0) {
                $(".select-input").removeClass("clicked");
            }
        })
        //下拉点击
        $(".select-input .name").click(function (e) {
            $(this).parent().toggleClass("clicked");
            stopBubble(e);
        })


        //多选下拉
        $(".sel .select-input").on("click", ".select-dropdown .dropdown-lists li", function () {
            if ($(this).parent().hasClass("dropdown-lists-single")) {
                let txt = $(this).text();
                $(this).parents(".select-input").find(".name").addClass("ckd").text(txt);
                $(this).addClass('cur').siblings().removeClass('cur');
                $(this).parents(".clicked").toggleClass("clicked");
            } else {
                let txtarr = [];
                $(this).toggleClass("cur");
                $(this).parent().find("li").each(function () {
                    if ($(this).hasClass("cur")) {
                        txtarr.push($(this).find("span").text());
                    }
                })
                let totals = $(this).parents(".dropdown-lists").find("li").length;
                if (txtarr.length == 0) {
                    let nameText = $(this).parents(".select-input").find(".name").attr('data-name');
                    $(this).parents(".select-input").find(".name").removeClass("ckd").text(nameText)
                } else if (totals == txtarr.length) {
                    $(this).parents(".select-dropdown").find(".all-selects").addClass("cur").text("取消全选");
                    $(this).parents(".select-input").find(".name").addClass("ckd").text(txtarr.join(","));
                } else {
                    $(this).parents(".select-dropdown").find(".all-selects").removeClass("cur").text("全选");
                    $(this).parents(".select-input").find(".name").addClass("ckd").text(txtarr.join(","));
                }
            }
        })


        //全选
        $("body").on("click", ".sel .select-input .select-dropdown .all-selects", function () {
            $(this).toggleClass("cur");
            if ($(this).hasClass("cur")) {
                $(this).text("取消全选");
                $(this).next().find("li").addClass("cur");
                let txtarr = [];
                $(this).next().find("li").each(function () {
                    txtarr.push($(this).find("span").text());
                })
                $(this).parents(".select-input").find(".name").addClass("ckd").text(txtarr.join(","));

            } else {
                $(this).text("全选");
                $(this).next().find("li").removeClass("cur");

                let nameText = $(this).parents(".select-input").find(".name").attr('data-name');
                $(this).parents(".select-input").find(".name").removeClass("ckd").text(nameText);
            }

        })

        //输入框筛选

        $(".sel .select-input .select-dropdown .search input").on("keyup", function () {
            var value = $(this).val().toLowerCase();
            var obj = $(this).parents(".select-dropdown").find(".dropdown-lists li");
            obj.filter(function () {
                $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
            });
        });
        if (state!=''){
            $(".dropdown-lists").find("span[stateId="+state+"]").click();
        }
        $("#keyWord,#uname").keyup(function (e) {
            if (e.keyCode == 13) {
                pageIndex = 1;
                table.reload('materialTable', {
                    where: {
                        jxb: jxb,
                        kj: kj,
                        curPage: pageIndex,
                        pageSize: pageSize,
                        courseTime: courseTime,
                        keyWord: $("#keyWord").val(),
                        uname: $("#uname").val(),
                        state: getState()
                    }
                }); //只重载数据
            }
        })

        $("#search").click(function () {
            pageIndex = 1;
            table.reload('materialTable', {
                where: {
                    jxb: jxb,
                    kj: kj,
                    curPage: pageIndex,
                    pageSize: pageSize,
                    courseTime: courseTime,
                    keyWord: $("#keyWord").val(),
                    uname: $("#uname").val(),
                    state: getState()
                }
            }); //只重载数据
        })
        //表格

        function getState(){
            let arr=[];
            $(".dropdown-lists li.cur").each(function(){
                arr.push($(this).find("span").attr("stateId"))
            })
            return arr.join(",")
        }
        // $(".dropdown-lists li span").eachobdiv=>$(obdiv).attr("stateId")).join(",")
        var table1 = table.render({
            elem: "#materialTable",
            id: 'materialTable',
            url: '/elective/rollcall/getTeachingClassRCStu', //数据接口
            where: {
                jxb: jxb,
                kj: kj,
                curPage: pageIndex,
                pageSize: pageSize,
                courseTime: courseTime,
                keyWord: $("#keyWord").val(),
                uname: $("#uname").val(),
                state: getState()
            },
            cols: [
                [{
                    field: "studentRealname",
                    align: "center",
                    title: "学生姓名",
                    width: 191
                },
                    {
                        field: "studentCodeDesensitize",
                        align: "center",
                        title: "学号",
                        width: 191
                    },
                    {
                        field: "administrationClassName",
                        align: "center",
                        title: "所属班级",
                        width: 191
                    },
                    {
                        field: "rollStatus",
                        align: "center",
                        title: "点名状态",
                        width: 191,
                        templet: function (d) {
                            let shtml = "";
                            if (d.cd == 0 && d.kk == 0 && d.zt == 0 && d.qj == 0 && d.state ==0) {
                                shtml += '<div class="normal1">正常</div>';
                            } else {
                                for (let i = 0; i < rollcallStateList.length; i++) {
                                    if ((rollcallStateList[i].type == 0 && rollcallStateList[i].stateAlias == 'cd' && d.cd == 1)||
                                        (rollcallStateList[i].type == 0 && rollcallStateList[i].stateAlias == 'kk' && d.kk == 1)||
                                        (rollcallStateList[i].type == 0 && rollcallStateList[i].stateAlias == 'zt' && d.zt == 1)||
                                        (rollcallStateList[i].type == 0 && rollcallStateList[i].stateAlias == 'qj' && d.qj == 1)||
                                        (rollcallStateList[i].type == 1 && rollcallStateList[i].id == d.state)){
                                        shtml +='<div class="normal'+(i+2)+'">'+rollcallStateList[i].stateNamePc+'</div>';
                                    }
                                }
                            }
                            return shtml;
                        },
                    },
                    {
                        field: "val",
                        align: "center",
                        title: "评价",
                        minWidth: 200,
                    },

                ]
            ],
            done: function (res) {
                laypage.render({
                    elem: 'coursePage',
                    curr: pageIndex,
                    groups: 5,
                    limit: pageSize,
                    limits: [10, 20, 30],
                    count: res.count,
                    layout: ['count', 'prev', 'page', 'next', 'limit', 'limits', 'skip'],
                    jump: function (obj, first) {
                        if (!first) {
                            pageIndex = obj.curr;
                            pageSize = obj.limit;
                            table.reload('materialTable', {
                                where: {
                                    jxb: jxb,
                                    kj: kj,
                                    curPage: pageIndex,
                                    pageSize: pageSize,
                                    courseTime: courseTime,
                                    keyWord: $("#keyWord").val(),
                                    uname: $("#uname").val(),
                                    state: getState()
                                }
                            });
                        }
                    }
                });
            }
        })



        function stopBubble(e) {
            if (e && e.stopPropagation)
                e.stopPropagation();
            else {
                window.event.cancelBubble = true;
            }
        }


    });
</script>

</html>