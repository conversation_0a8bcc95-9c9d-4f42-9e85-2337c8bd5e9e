<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <title>老师学时配置</title>
    <link rel="stylesheet" th:href="@{../css/cultivation/newStyle.css}"/>
    <link rel="stylesheet" th:href="@{../plugin/layui/css/layui.css}"/>
    <style>
        .multi-selected {
            background-color: #4C88FF !important;
        }

        .multi-cur {
            background-color: #4C88FF !important;
        }

        .th-cur {
            background-color: #4C88FF !important;
        }
    </style>
</head>
<body class="gray-bg">
<div class="popDiv popMove wid100b">
    <div class="popBody">
        <div class="popWeekly">
            <ul class="popWeekly_tab">
                <li class="active" onclick="dz();">单周</li>
                <li class="active" onclick="sz();">双周</li>
                <li class="active" onclick="qx();">全选</li>
                <li class="active" onclick="qbx();">取消选择</li>
                <li class="active" onclick="pjfpxs();" style="width: 100px;">平均分配学时</li>
                <li class="active" onclick="yjszxs();" style="width: 100px;">一键设置学时</li>
                <li class="active" onclick="dsz();" style="width: 100px;">单双周赋值</li>
            </ul>
            <div class="popWeekly_table">
                <table border="0" cellspacing="0" cellpadding="0">
                    <tbody></tbody>
                </table>
            </div>
            <!--<div class="popWeekly_detect">
                <label>检测周次冲突<input type="checkbox" name="" id="" value="" onclick="checkConflict()"/></label>
            </div>-->
        </div>
    </div>
</div>
<script th:src="@{../js/jquery-3.3.1.min.js}"></script>
<script th:src="@{../plugin/layui/layui.js}"></script>
<script th:src="@{../js/selection.js}"></script>
<script th:inline="javascript">
    let nowxs = [[${classStartsInfoDetail.totalClassHour}]];
    let semester = [[${classStartsInfoDetail.semester}]];
    let week = [[${week}]];
    let firstTime = 0;
    let lastTime = 0;
    document.domain = document.domain.split('.').slice(-2).join('.');
    $(document).ready(function () {
        const url = "../processData/getCurSemesterData";
        $.post(url, {name: semester}, function (result) {
            const weeks = result.basicSemesterInfo.xnxq_jsz;
            let html = "<tr><th>总学时</th><th>已分配学时</th>";
            let iptHtml = "";
            for (let i = 0; i < weeks; i++) {
                html += "<th class='th'>" + (i + 1) + "</th>";
                iptHtml += "<td><input class=\"tdInput\" name=\"jk\" type=\"text\" value='0'/></td>";
            }
            html += "</tr>";
            html += "<tr>";
            html += "<td><div class=\"tdNum\">" + nowxs + "</div></td>";
            html += "<td><div class=\"tdNum\" name = \"jkyfp\">0</div></td>";
            html += iptHtml;
            html += "</tr>";
            $(".popWeekly_table table tbody").html(html);
            $(function () {
                $(document).on('mousedown', function (e) {
                    let lengs = $("#layer-input-xs").length;
                    firstTime = new Date().getTime();
                    if (lengs > 0) {
                        return false;
                    }
                    var $tar = $(e.target);
                    if (!$tar.hasClass('selected')) {
                        $.extend(document, {
                            'multiLine': true,
                            'startPos': {
                                "x": e.pageX,
                                "y": e.pageY
                            },
                            'endPos': {
                                "x": e.pageX,
                                "y": e.pageY
                            }
                        })
                        $('body').append('<div class="multiLine"></div>');
                    }
                }).on('mousemove', function (e) {
                    if (this.multiLine) {
                        this.endPos = {
                            "x": e.pageX,
                            "y": e.pageY
                        }
                        let $mLine = $('.multiLine'),
                            startX = this.startPos.x,
                            startY = this.startPos.y,
                            endX = this.endPos.x,
                            endY = this.endPos.y,
                            width = Math.abs(endX - startX) + "px",
                            height = Math.abs(endY - startY) + "px",
                            left = endX > startX ? startX : endX,
                            top = endY > startY ? startY : endY;
                        $mLine.css({
                            position: 'absolute',
                            width: width,
                            height: height,
                            left: left,
                            top: top,
                            outline: "2px dashed transparent"
                        })
                        multiSelect()
                    }

                }).on('mouseup', function (e) {
                    this.multiLine = false;
                    $('.multiLine').remove();
                    lastTime = new Date().getTime();
                    let lengs = $("#layer-input-xs").length;
                    if ((lastTime - firstTime) < 200 && lengs > 0) {
                        $(".layui-layer-content input").focus();
                    }
                })


                function multiSelect() {
                    var domArr = $('.th'),
                        $mLine = $('.multiLine'),
                        mTop = $mLine.position().top,
                        mLeft = $mLine.position().left,
                        mTop2 = mTop + $mLine.height(),
                        mLeft2 = mLeft + $mLine.width();
                    domArr.each(function () {
                        var dom = $(this),
                            left = dom.offset().left,
                            top = dom.offset().top,
                            left2 = left + dom.width(),
                            top2 = top + dom.height();
                        if (!(left > mLeft2 || left2 < mLeft || top > mTop2 || top2 < mTop)) {
                            dom.addClass('multi-selected');
                            dom.addClass('multi-cur');
                        } else {
                            dom.removeClass('multi-selected');
                        }

                    })
                }
            })

            if (week && week.indexOf(",") === -1) {
                let w = week.split(":")[0];
                let num = week.split(":")[1];
                let start = w.split("-")[0] - 1;
                let idx = w.split("-")[1];

                for (let i = start; i < idx; i++) {
                    $(".popWeekly_table th").eq(i + 2).addClass("th-cur");
                    $("input[name='jk']").eq(i).val(num);
                }
            }
            if (week && week.indexOf(",") !== -1) {
                let weekArray = week.split(",");
                for (let i = 0; i < weekArray.length; i++) {
                    let wk = weekArray[i];
                    let w = wk.split(":")[0];
                    let num = wk.split(":")[1];
                    let idx = w.split("-")[1];
                    $(".popWeekly_table th").eq(parseInt(idx) + 1).addClass("th-cur");
                    $("input[name='jk']").eq(idx - 1).val(num);
                }
            }
            let jkyfp = 0;
            $("input[name='jk']").each(function () {
                jkyfp = jkyfp + parseInt($(this).val());
            })
            if (jkyfp) {
                $("div[name='jkyfp']").text(Math.round(jkyfp));
            }
        }, "json");
    })

    let layer = "";
    layui.use(['form', 'layer'], function () {
        layer = layui.layer;
    })

    //弹窗居中
    function MoveFixed(userClass) {
        $(userClass).css({
            top: function () {
                return 0;
            }, left: function () {
                return ($(window).width() - $(this).width()) / 2;
            }
        });
    }

    window.onresize = function () {
        MoveFixed('.popMove')
    }
    MoveFixed('.popMove')
    var totaldatajk = [];
    var ypxs = "";
    var detail = '';
    var begin = false;
    var csstemp = "aa";
    var rancolor = getRandomColor();
    var checkFlag = true;

    function getRandomColor() {
        return "#4C88FF";
    }

    function Trim(str) {
        return str.replace(/(^\s*)|(\s*$)/g, "");
    }

    function myblur(input, xs) {
        var total = 0;
        // 计算 分配学时  是否 已经 草超过 总额
        $(input).each(function () {
            if ($(this).val()) {
                total += parseInt($(this).val());
            }
        });
        $("div[name='jkyfp']").text(total);
        if (total > xs) {
            top.layer.msg("您分配的学时总数已经超过了该项总学时。");
        }
    }

    //获取安排周次，例如：1-1:2,2-21:0|分配学时:1-1:2,2-21:0
    function saveJk(xs, eobj, type, input) {
        var total = 0;
        let result = {};
        var cstr = '';
        var datajk = [];
        var jktemp = '';
        var minjkzc = 1;
        var maxjkzc = 1;
        var val = '';
        $(eobj).each(function (index) {
            val = Trim($(this).val());
            if (jktemp == '') {
                jktemp = val;
            } else {
                if (val == jktemp) {
                    maxjkzc = index + 1;
                    jktemp = val;
                } else {
                    if (jktemp != 0) {
                        datajk.push(minjkzc + '-' + maxjkzc + ':' + jktemp);
                        total += jktemp * (maxjkzc - minjkzc + 1);
                    }
                    minjkzc = index + 1;
                    maxjkzc = index + 1;
                    jktemp = val;
                }
            }

        });
        if (val != 0) {
            datajk.push(minjkzc + '-' + maxjkzc + ':' + val);
            total += val * (maxjkzc - minjkzc + 1);
        }
        //if(total>xs){
        //	top.layer.alert("该学时分配已经超过了总学时!");
        //	return;
        //}
        $(input).val(datajk);
        var d1 = $("#datajk").val();
        cstr += datajk;
        result.cstr = cstr;
        result.total = total;
        return result;
    }


    /**
     *
     * @param str  周次配置str
     * @param zyzc 已经占用周次
     * @param yapxs 已经安排的学时
     * @param nowxs 总学时
     * @param func 用于关闭弹出页的函数
     */
    function getdata(func) {
        detail = saveJk(nowxs, $("input[name='jk']"), 'jk', $("#datajk"));
        return detail;
    }

    // 全选
    function qx() {
        rancolor = getRandomColor();
        $(".popWeekly_table th").each(function () {
            var idx = $(this).index();
            if (idx > 1) {
                $(this).css({"background": rancolor});
            }
        })
    }

    // 全不选
    function qbx(title, url, gridId) {
        $(".popWeekly_table th").each(function () {
            var idx = $(this).index();
            if (idx > 1) {
                $(this).css({"background": "#f2f2f2"});
                $(this).removeClass('multi-selected').removeClass('multi-cur').removeClass("th-cur");
            }
        })
        $(".popWeekly_table input").each(function () {
            $(this).val(0);
        })
        $("div[name='jkyfp']").text(0);
    }

    // 单选
    function dz(title, url, gridId) {
        $(".popWeekly_table th").removeClass("th-cur")
        rancolor = getRandomColor();
        var count = 0;
        var val = $(".popWeekly_table").find('input').first().val();
        $(".popWeekly_table").find('input').each(function (index) {
            count = index + 1;
            if ($(this).attr("readonly") == undefined) {
                var id = $(this).parents().filter("td").attr("aria-describedby");
                if (count % 2 == 0) {
                    // 偶数
                    $(".popWeekly_table").find('th').eq(count + 1).css({"background": "#f2f2f2"});
                    $(".popWeekly_table").find('td').eq(count + 1).find("input").val(0);
                } else {
                    // 奇数
                    $(".popWeekly_table").find('th').eq(count + 1).css({"background": rancolor})
                }

            }
        });
    }

    // 双选
    function sz(title, url, gridId) {
        $(".popWeekly_table th").removeClass("th-cur");
        rancolor = getRandomColor();
        var count = 0;
        var val = $(".popWeekly_table").find('input').first().val();
        $(".popWeekly_table").find('input').each(function (index) {
            count = index + 1;
            if ($(this).attr("readonly") == undefined) {
                var id = $(this).parents().filter("td").attr("aria-describedby");
                if (count % 2 == 0) {
                    // 偶数
                    $(".popWeekly_table").find('th').eq(count + 1).css({"background": rancolor});
                } else {
                    // 奇数
                    $(".popWeekly_table").find('td').eq(count + 1).find("input").val(0);
                    $(".popWeekly_table").find('th').eq(count + 1).css({"background": "#f2f2f2"});
                }
            }
        });
    }


    //检查冲突
    /*function checkConflict(){
        zyzc = ${zyzc};
        if(checkFlag == false){
            $(".popWeekly_table").find("input").each(function(index,element){
                for(var i=0;i<=zyzc.length;i++){
                    if((index+1)==zyzc[i]){
                        $(this).removeAttr("readonly");
                    }
                }
            });
            checkFlag = true;
        }else{
            $(".popWeekly_table").find("input").each(function(index,element){
                for(var i=0;i<=zyzc.length;i++){
                    if((index+1)==zyzc[i]){
                        $(this).attr("readonly","readonly");
                    }
                }
            });
            checkFlag = false;
        }
    }*/

    //平均分配学时
    function pjfpxs() {
        zcArray = [];
        var flag = verifySel();
        if (!flag) {
            return false;
        }
        const xs = nowxs / zcArray.length;
        if (xs < 1) {
            layer.msg("总学时不能小于所选个数");
            return false;
        }
        $(".popWeekly_table td input").val(0);
        $(".popWeekly_table td").eq(1).find("div").text(nowxs);
        for (let i = 0; i < zcArray.length; i++) {
            const zc = parseInt(zcArray[i]) + 1;
            const iptVal = i === zcArray.length - 1 ? nowxs % zcArray.length + Math.floor(xs) : Math.floor(xs);
            $(".popWeekly_table td").eq(zc).find("input").val(iptVal);
        }
    }

    //一键设置学时
    function yjszxs() {
        zcArray = [];
        var flag = verifySel();
        if (!flag) {
            return false;
        }
        layer.open({
            type: 1,
            shift: 7,
            shadeClose: true,
            title: '请输入学时',
            btn: ['确定', '取消'], //按钮，
            btnAlign: 'c',
            content: "<div class='layui-layer-content' style='padding: 20px;'><input type='text' id = 'layer-input-xs' style=' background-color: #fff; display: block;width: 230px;height: 36px;margin: 0 auto;line-height: 30px;padding-left: 10px;border: 1px solid #e6e6e6;color: #333;' value=''/></div>",
            yes: function (index) {
                var value = $('#layer-input-xs').val() * zcArray.length;
                if (!value) {
                    layer.msg("学时不能为空。");
                    return false;
                }
                if (value > nowxs) {
                    flag = false;
                    layer.confirm("当前分配的学时已超过计划总学时，是继续设置？", {
                        btn: ['确定', '取消'],
                        yes: function (index1) {
                            flag = true;
                            fill($('#layer-input-xs').val(), value);
                            layer.close(index1);
                            layer.close(index);
                        }
                    });
                }
                if (flag) {
                    fill($('#layer-input-xs').val(), value);
                    layer.close(index);
                }
            },
            cancel: function (index) {
                layer.close(index);
            }
        })
    }


    $("body").on("click", "#layui-layer-content", function () {
        return false;
    })

    //单双周赋值
    function dsz() {
        zcArray = [];
        var flag = verifySel();
        if (!flag) {
            return false;
        }
        layer.open({
            type: 1,
            shift: 7,
            shadeClose: false,
            title: '请输入学时',
            btn: ['确定', '取消'], //按钮，
            btnAlign: 'c',
            content: "<div class='layui-layer-content' style='width:350px;'><div style=' display:flex; margin:15px 0 15px;display:-webkit-flex;align-items: center;justify-content: flex-start; width:320px;margin-left: 6%;' class='form-group has-feedback'><p>单周</p><input type='text' id = 'layer-input-dz' style='display: block;width: 230px;height: 36px;margin: 0 auto;line-height: 30px;padding-left: 10px;border: 1px solid #e6e6e6;color: #333;' value=''/></div>" +
                "<div style='display:flex; margin:15px 0 15px;display:-webkit-flex;align-items: center;justify-content: flex-start; width:320px;margin-left: 6%;' class='form-group has-feedback'><p>双周</p><input type='text' id = 'layer-input-sz' style='display: block;width: 230px;height: 36px;margin: 0 auto;line-height: 30px;padding-left: 10px;border: 1px solid #e6e6e6;color: #333;' value=''/></div></div>"
            ,
            yes: function (index) {
                var dzValue = $('#layer-input-dz').val();
                var szValue = $('#layer-input-sz').val();
                if (!dzValue && !szValue) {
                    layer.msg("单周、双周学时不能同时为空。");
                    return false;
                }
                var odd = 0, even = 0;
                for (let i = 0; i < zcArray.length; i++) {
                    zcArray[i] % 2 === 0 ? even++ : odd++;
                }
                var total = odd * dzValue + szValue * even;
                if (total > nowxs) {
                    flag = false;
                    layer.confirm("当前分配的学时已超过计划总学时，是继续设置？", {
                        btn: ['确定', '取消'],
                        yes: function (index1) {
                            flag = true;
                            fillds(szValue, dzValue, total);
                            layer.close(index1);
                            layer.close(index);
                        }
                    });
                }
                if (flag) {
                    fillds(szValue, dzValue, total);
                    layer.close(index);
                }
            },
            cancel: function (index) {
                layer.close(index);
            }
        })
    }

    $(".popWeekly_table").on("click", "th", function () {
        //$(".popWeekly_table th").attr("style","");
        let idx = $(this).index();
        console.log($(this).attr("class"))
        if ($(this).attr("class").indexOf("th-cur") === -1 && $(this).attr("class").indexOf("multi-cur") === -1) {
            $(this).addClass('th-cur');
        } else {
            $(this).removeClass('th-cur');
            let curVal = $(this).parents("tr").next().find("td").eq(idx).find("input").val();
            $("div[name='jkyfp']").text($("div[name='jkyfp']").text() - curVal);
            $(this).parents("tr").next().find("td").eq(idx).find("input").val(0);
        }
        $(this).removeClass("multi-selected").removeClass("multi-cur");
    })
    $(".popWeekly_table").on("focus", "input[name='jk']", function () {
        var val = $(this).val();
        if (Trim(val) == '' || Trim(val) == '0') {
            $(this).val("");
        }
    });
    $(".popWeekly_table").on("blur", "input[name='jk']", function () {
        var id = $(this).parents().filter("td").attr("aria-describedby");
        var val = $(this).val();
        let idx = $(this).parent().index();
        if (Trim(val) == '' || Trim(val) == '0') {
            $(this).parents("tr").prev().find("th").eq(idx).removeClass("th-cur");
            $(this).val("0");
        } else if (parseInt(Trim(val)) > 0) {
            $(this).parents("tr").prev().find("th").eq(idx).addClass("th-cur");
        }

        myblur($("input[name='jk']"), nowxs);
    })

    var zcArray = [];

    function verifySel() {
        $(".popWeekly_table th").each(function () {
            const bgVal = $(this).css('background');
            if (bgVal.indexOf("rgb(242, 242, 242)") == -1) {
                zcArray.push($(this).text());
            }
        })
        if (zcArray.length == 0) {
            layer.msg("请先选教学周");
            return false;
        }
        return true;
    }

    function fill(iptVal, yfp) {
        $(".popWeekly_table td input").val(0);
        $(".popWeekly_table td").eq(1).find("div").text(yfp);
        for (let i = 0; i < zcArray.length; i++) {
            const zc = parseInt(zcArray[i]) + 1;
            $(".popWeekly_table td").eq(zc).find("input").val(iptVal);
        }
    }

    function fillds(szValue, dzValue, total) {
        szValue = szValue ? szValue : 0;
        dzValue = dzValue ? dzValue : 0;
        $(".popWeekly_table td input").val(0);
        $(".popWeekly_table td").eq(1).find("div").text(total);
        for (let i = 0; i < zcArray.length; i++) {
            const zc = parseInt(zcArray[i]) + 1;
            const iptVal = zcArray[i] % 2 === 0 ? szValue : dzValue;
            $(".popWeekly_table td").eq(zc).find("input").val(iptVal);
        }
    }
</script>
</body>
</html>
