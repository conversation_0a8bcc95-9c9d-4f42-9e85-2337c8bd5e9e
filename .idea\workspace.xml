<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AnalysisUIOptions">
    <option name="GROUP_BY_SEVERITY" value="true" />
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="338d4c2d-1b2f-4ef8-a895-9127d1a4fabe" name="Changes" comment="同步开课信息表部分字段来源变更">
      <change afterPath="$PROJECT_DIR$/.kiro/specs/startclass-improvements/design.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.kiro/specs/startclass-improvements/requirements.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.kiro/specs/startclass-improvements/tasks.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.vscode/settings.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/resources/logback-spring.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/config/XxlJobConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/config/XxlJobConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/api/form/basic/ApiBasicTopBtnController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/api/form/basic/ApiBasicTopBtnController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/api/form/basic/ApiFormInfoUploadController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/api/form/basic/ApiFormInfoUploadController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/api/form/basic/ApiHistoryDataTransferController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/api/form/basic/ApiStudentEngineerController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/api/form/basic/ApiStudentEngineerController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/api/form/cultivation/ApiProcessController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/api/form/cultivation/ApiProcessController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/api/form/cultivation/ApiTrainingProgramController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/api/form/cultivation/ApiTrainingProgramController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/api/form/material/ApiMaterialTopBtnController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/api/form/material/ApiMaterialTopBtnController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/api/form/material/ApiMaterialVerifyController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/api/form/material/ApiMaterialVerifyController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/common/LoginController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/common/LoginController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/pc/basic/BasicController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/pc/basic/BasicController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/pc/basic/BasicPopController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/pc/basic/BasicPopController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/pc/calendar/CalendarController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/pc/calendar/CalendarController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/pc/cultivation/ClazzSpeedMatchController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/pc/cultivation/ClazzSpeedMatchController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/pc/cultivation/CultivationMoocController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/pc/cultivation/CultivationMoocController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/pc/cultivation/CultivationPlateClassController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/pc/cultivation/CultivationPlateClassController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/pc/cultivation/CultivationSetController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/pc/cultivation/CultivationSetController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/pc/cultivation/ProcessDataController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/pc/cultivation/ProcessDataController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/pc/cultivation/ProcessViewController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/pc/cultivation/ProcessViewController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/pc/material/MaterialPopController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/pc/material/MaterialPopController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/pc/material/MaterialReceiptFormController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/pc/material/MaterialReceiptFormController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/design/strategy/impl/cultivation/BatchAddTeachClassStudentTask.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/design/strategy/impl/cultivation/BatchAddTeachClassStudentTask.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/design/template/bo/FormDataPushTopBtnErrorRecordBO.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/design/template/bo/FormDataPushTopBtnErrorRecordBO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/design/template/impl/basic/CalculateClazzNumberTask.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/design/template/impl/basic/CalculateClazzNumberTask.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/design/template/impl/cultivation/SaveTeachPlanTask.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/design/template/impl/cultivation/SaveTeachPlanTask.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/design/template/impl/cultivation/UgSubmitStartClassApproveTask.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/design/template/impl/cultivation/UgSubmitStartClassApproveTask.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/entity/po/cultivation/CultivationPlateClass.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/entity/po/cultivation/CultivationPlateClass.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/entity/po/smartbrain/SmartBrainTargetTable.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/entity/po/smartbrain/SmartBrainTargetTable.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/handle/GlobalExceptionHandler.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/handle/GlobalExceptionHandler.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/interceptor/GlobalInterceptor.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/interceptor/GlobalInterceptor.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/service/basic/impl/BasicDataPushServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/service/basic/impl/BasicDataPushServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/service/cultivation/impl/ClassStartsInfoServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/service/cultivation/impl/ClassStartsInfoServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/service/cultivation/impl/CultivationPlateCommonServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/service/cultivation/impl/CultivationPlateCommonServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/utils/task/thread/ThreadTaskUtils.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/utils/task/thread/ThreadTaskUtils.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/utils/vc3/Vc3Utils.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/chaoxing/academic/utils/vc3/Vc3Utils.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/bootstrap.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/bootstrap.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/mapper/cultivation/CultivationPlateClassMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/mapper/cultivation/CultivationPlateClassMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/mapper/cultivation/CultivationPlateTypeCourseMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/mapper/cultivation/CultivationPlateTypeCourseMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/static/css/cultivation/index3.0.css" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/static/css/cultivation/index3.0.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/static/js/cultivation/course.js" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/static/js/cultivation/course.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/static/js/cultivation/plateCommon.js" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/static/js/cultivation/plateCommon.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/static/js/cultivation/plateItemType.js" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/static/js/cultivation/plateItemType.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/templates/views/pc/cultivation/plate_class_index.html" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/templates/views/pc/cultivation/plate_class_index.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/com/chaoxing/academic/form/AbutmentTest.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/test/java/com/chaoxing/academic/form/AbutmentTest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/com/chaoxing/academic/form/Tform.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/test/java/com/chaoxing/academic/form/Tform.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/test/java/com/chaoxing/academic/form/UniversalFormTests.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/test/java/com/chaoxing/academic/form/UniversalFormTests.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="directory" />
      <option value="module" />
    </option>
  </component>
  <component name="CodeInsightWorkspaceSettings">
    <option name="optimizeImportsOnTheFly" value="true" />
  </component>
  <component name="CompilerWorkspaceConfiguration">
    <option name="COMPILER_PROCESS_HEAP_SIZE" value="1024" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Enum" />
        <option value="CSS File" />
        <option value="spring-beans.schema" />
        <option value="JavaScript File" />
        <option value="HTML File" />
        <option value="Interface" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="FindBugs-IDEA-Workspace">
    <toolWindowEditorPreview>false</toolWindowEditorPreview>
    <toolWindowGroupBy>BugRank</toolWindowGroupBy>
  </component>
  <component name="Git.Settings">
    <option name="PATH_TO_GIT" value="D:\Git\cmd\git.exe" />
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="master" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/src/main/java/com/chaoxing/academic/entity/form/cultivation/CourseLibraryForm.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/chaoxing/academic/service/cultivation/impl/MajorCourseSetServiceImpl.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="jar://$PROJECT_DIR$/../../repository/maven_jar/com/baomidou/mybatis-plus-core/3.5.2/mybatis-plus-core-3.5.2-sources.jar!/com/baomidou/mybatisplus/core/mapper/BaseMapper.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../../repository/maven_jar/com/baomidou/mybatis-plus-core/3.5.2/mybatis-plus-core-3.5.2-sources.jar!/com/baomidou/mybatisplus/core/override/MybatisMapperProxy.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../../repository/maven_jar/com/baomidou/mybatis-plus-extension/3.5.2/mybatis-plus-extension-3.5.2-sources.jar!/com/baomidou/mybatisplus/extension/conditions/query/ChainQuery.java" root0="SKIP_INSPECTION" />
  </component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\apache-maven-3.9.5" />
        <option name="localRepository" value="E:\repository\maven_jar" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="useMavenConfig" value="false" />
        <option name="userSettingsFile" value="D:\apache-maven-3.9.5\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2eo5Gh3b9ZetqcAh9Kxzv4LGMA2" />
  <component name="ProjectLevelVcsManager">
    <OptionsSetting value="false" id="Update" />
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ASKED_ADD_EXTERNAL_FILES&quot;: &quot;true&quot;,
    &quot;Application.AesUtil.executor&quot;: &quot;Run&quot;,
    &quot;Application.CourseApi.executor&quot;: &quot;Run&quot;,
    &quot;Application.CourseTableExport.executor&quot;: &quot;Run&quot;,
    &quot;Application.CourseTeacherExportMain.executor&quot;: &quot;Run&quot;,
    &quot;Application.CourseTeacherExportServiceImpl.executor&quot;: &quot;Run&quot;,
    &quot;Application.DateUtils.executor&quot;: &quot;Run&quot;,
    &quot;Application.DynamicVc3SecretKey.executor&quot;: &quot;Debug&quot;,
    &quot;Application.ExcelA4Fitter.executor&quot;: &quot;Run&quot;,
    &quot;Application.NoticeApi.executor&quot;: &quot;Run&quot;,
    &quot;Application.TalentTrainingDockingHandler.executor&quot;: &quot;Run&quot;,
    &quot;Application.Tform.executor&quot;: &quot;Run&quot;,
    &quot;Application.UgSubmitStartClassApproveTask.executor&quot;: &quot;Run&quot;,
    &quot;Application.UserApi.executor&quot;: &quot;Run&quot;,
    &quot;Application.Vc3Utils.executor&quot;: &quot;Run&quot;,
    &quot;DefaultHtmlFileTemplate&quot;: &quot;HTML File&quot;,
    &quot;JUnit.ExcelA4Fitter.v.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.ExcelA4LandscapeFitter.v.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.Tform.t.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.Tform.t1.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.Tform.t10.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.Tform.t11.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.Tform.t12.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.Tform.t13 (1).executor&quot;: &quot;Run&quot;,
    &quot;JUnit.Tform.t13.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.Tform.t14.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.Tform.t15.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.Tform.t16.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.Tform.t2.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.Tform.t6.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.Tform.t7.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.Tform.t8.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.Tform.t9.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.Tform.testGetCourseStatus.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.UniversalFormTests.generateApproveEntity.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.UniversalFormTests.generateFormEntity.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.UniversalFormTests.generateFormEntity2.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.UniversalFormTests.generateFormEntity3.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.UserApiTest.userInfoTest.executor&quot;: &quot;Run&quot;,
    &quot;Maven.academic [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.academic [compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.academic [deploy].executor&quot;: &quot;Run&quot;,
    &quot;Maven.academic [package].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.AcademicApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.local.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.pro.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.test.executor&quot;: &quot;Debug&quot;,
    &quot;StatusDashboardGroupingRule&quot;: &quot;true&quot;,
    &quot;dev.sweep.assistant.data.RecentlyUsedFiles&quot;: &quot;src\\main\\resources\\templates\\views\\pc\\cultivation\\pop\\course_allocation_pop.html&quot;,
    &quot;git-widget-placeholder&quot;: &quot;test&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/IdeaProjects/academic/src/main/resources/static/js/cultivation&quot;,
    &quot;list.type.of.created.stylesheet&quot;: &quot;CSS&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Modules&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;run.code.analysis.last.selected.profile&quot;: &quot;pProject Default&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;settings.editor.splitter.proportion&quot;: &quot;0.24096386&quot;,
    &quot;sweep.chatMode&quot;: &quot;Agent&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\IdeaProjects\academic\src\main\resources\static\js\cultivation" />
      <recent name="E:\IdeaProjects\academic\src\main\resources\static\css\cultivation" />
      <recent name="E:\IdeaProjects\academic\src\main\resources\templates\views\pc\cultivation" />
      <recent name="E:\IdeaProjects\academic\src\main\resources\static\images\cultivation" />
      <recent name="E:\IdeaProjects\academic\src\main\resources\templates\views\pc\cultivation\pop" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="E:\IdeaProjects\academic\src\main\resources\static\js\cultivation\startClass" />
      <recent name="E:\IdeaProjects\academic\src\main\resources\static\css\cultivation" />
      <recent name="E:\IdeaProjects\academic\src\main\resources\static\teacherIdle" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.chaoxing.academic.mapper.cultivation" />
      <recent name="com.chaoxing.academic.entity.form.cultivation.subform" />
      <recent name="com.chaoxing.academic.entity.form.cultivation" />
      <recent name="com.chaoxing.academic.service.basic.excel" />
      <recent name="com.chaoxing.academic.entity.dto.cultivation" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.test">
    <configuration default="true" type="JUnit" factoryName="JUnit">
      <shortenClasspath name="MANIFEST" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="Application" factoryName="Application">
      <shortenClasspath name="MANIFEST" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="Tform.t1" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="academic" />
      <shortenClasspath name="MANIFEST" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.chaoxing.academic.form.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.chaoxing.academic.form" />
      <option name="MAIN_CLASS_NAME" value="com.chaoxing.academic.form.Tform" />
      <option name="METHOD_NAME" value="t1" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="Tform.t13" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="academic" />
      <shortenClasspath name="MANIFEST" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.chaoxing.academic.form.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.chaoxing.academic.form" />
      <option name="MAIN_CLASS_NAME" value="com.chaoxing.academic.form.Tform" />
      <option name="METHOD_NAME" value="t13" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="Tform.t14" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="academic" />
      <shortenClasspath name="MANIFEST" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.chaoxing.academic.form.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.chaoxing.academic.form" />
      <option name="MAIN_CLASS_NAME" value="com.chaoxing.academic.form.Tform" />
      <option name="METHOD_NAME" value="t14" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="UniversalFormTests.generateFormEntity" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="academic" />
      <shortenClasspath name="MANIFEST" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.chaoxing.academic.form.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.chaoxing.academic.form" />
      <option name="MAIN_CLASS_NAME" value="com.chaoxing.academic.form.UniversalFormTests" />
      <option name="METHOD_NAME" value="generateFormEntity" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="UniversalFormTests.generateFormEntity" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="academic" />
      <shortenClasspath name="MANIFEST" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.chaoxing.academic.form.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.chaoxing.academic.form" />
      <option name="MAIN_CLASS_NAME" value="com.chaoxing.academic.form.UniversalFormTests" />
      <option name="METHOD_NAME" value="generateFormEntity" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="JUnit" factoryName="JUnit">
      <shortenClasspath name="MANIFEST" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="AcademicApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="academic" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.chaoxing.academic.AcademicApplication" />
      <option name="VM_PARAMETERS" value="-XX:MetaspaceSize=200m" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.chaoxing.academic.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="local" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="academic" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.chaoxing.academic.AcademicApplication" />
      <option name="VM_PARAMETERS" value="-Dspring.cloud.nacos.discovery.namespace=bf43efe5-beb4-4630-825e-6223ee82643d -Dspring.cloud.nacos.config.namespace=bf43efe5-beb4-4630-825e-6223ee82643d -Dspring.application.name=academic-local -Dspring.application.group=academic -Dserver.port=80" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.chaoxing.academic.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="pro" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="academic" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.chaoxing.academic.AcademicApplication" />
      <option name="VM_PARAMETERS" value="-Dspring.cloud.nacos.discovery.namespace=46337114-ede8-45f8-955d-0cbeef0b9229 -Dspring.cloud.nacos.config.namespace=46337114-ede8-45f8-955d-0cbeef0b9229 -Dspring.application.name=academic -Dspring.application.group=academic -Dserver.port=80" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.chaoxing.academic.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="test" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="academic" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.chaoxing.academic.AcademicApplication" />
      <option name="VM_PARAMETERS" value="-Dspring.cloud.nacos.discovery.namespace=56afd7eb-7a76-4bc7-9a8c-bec13fda0c98 -Dspring.cloud.nacos.config.namespace=56afd7eb-7a76-4bc7-9a8c-bec13fda0c98 -Dspring.application.name=academic-test -Dspring.application.group=academic -Dserver.port=80 -Dformapi.monitoring.enabled=true -Dformapi.auto.init=true" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.chaoxing.academic.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="JUnit.Tform.t1" />
      <item itemvalue="JUnit.Tform.t13" />
      <item itemvalue="JUnit.Tform.t14" />
      <item itemvalue="JUnit.UniversalFormTests.generateFormEntity" />
      <item itemvalue="Spring Boot.local" />
      <item itemvalue="Spring Boot.pro" />
      <item itemvalue="Spring Boot.test" />
      <item itemvalue="Spring Boot.AcademicApplication" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="JUnit.Tform.t13" />
        <item itemvalue="Spring Boot.AcademicApplication" />
        <item itemvalue="JUnit.UniversalFormTests.generateFormEntity" />
        <item itemvalue="JUnit.Tform.t14" />
        <item itemvalue="JUnit.Tform.t1" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="ShelveChangesManager">
    <option name="groupingKeys">
      <option value="directory" />
      <option value="module" />
    </option>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="StructureViewState">
    <option name="selectedTab" value="Logical" />
  </component>
  <component name="SvnConfiguration">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
    <option name="runUnderTerminal" value="true" />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="338d4c2d-1b2f-4ef8-a895-9127d1a4fabe" name="Changes" comment="" />
      <created>1712557995605</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1712557995605</updated>
      <workItem from="1712557996537" duration="6183000" />
      <workItem from="1712573327651" duration="7135000" />
      <workItem from="1712621615329" duration="31484000" />
      <workItem from="1712708521425" duration="34906000" />
      <workItem from="1712794329580" duration="12595000" />
      <workItem from="1712815329472" duration="67144000" />
      <workItem from="1713140088576" duration="17344000" />
      <workItem from="1713165511109" duration="117000" />
      <workItem from="1713165672292" duration="382000" />
      <workItem from="1713166086584" duration="22000" />
      <workItem from="1713166124331" duration="58000" />
      <workItem from="1713166193575" duration="21000" />
      <workItem from="1713166246122" duration="297000" />
      <workItem from="1713166550906" duration="10366000" />
      <workItem from="1713226284150" duration="15714000" />
      <workItem from="1713251442082" duration="12004000" />
      <workItem from="1713272340592" duration="2399000" />
      <workItem from="1713312498752" duration="7978000" />
      <workItem from="1713321933212" duration="9695000" />
      <workItem from="1713336743169" duration="19551000" />
      <workItem from="1713398960368" duration="42110000" />
      <workItem from="1713485344003" duration="40919000" />
      <workItem from="1713578965108" duration="24099000" />
      <workItem from="1713605385238" duration="19000" />
      <workItem from="1713605451726" duration="68000" />
      <workItem from="1713605556868" duration="40000" />
      <workItem from="1713605686776" duration="32000" />
      <workItem from="1713605831760" duration="1608000" />
      <workItem from="1713608133246" duration="38205000" />
      <workItem from="1713831489513" duration="36266000" />
      <workItem from="1713917455616" duration="34005000" />
      <workItem from="1713961184987" duration="124000" />
      <workItem from="1714003936132" duration="34087000" />
      <workItem from="1714090397813" duration="35736000" />
      <workItem from="1714134561438" duration="24000" />
      <workItem from="1714136196629" duration="412000" />
      <workItem from="1714139958096" duration="731000" />
      <workItem from="1714263142811" duration="29569000" />
      <workItem from="1714349299426" duration="26036000" />
      <workItem from="1714435832881" duration="15941000" />
      <workItem from="1714955059873" duration="28626000" />
      <workItem from="1715040543317" duration="1053000" />
      <workItem from="1715041606935" duration="72647000" />
      <workItem from="1715213208634" duration="29951000" />
      <workItem from="1715299889560" duration="35607000" />
      <workItem from="1715350708993" duration="2648000" />
      <workItem from="1715387151028" duration="24530000" />
      <workItem from="1715559701730" duration="20506000" />
      <workItem from="1715590478472" duration="1365000" />
      <workItem from="1715591858610" duration="8541000" />
      <workItem from="1715646227834" duration="38418000" />
      <workItem from="1715732222918" duration="36318000" />
      <workItem from="1715818554980" duration="30844000" />
      <workItem from="1715904746528" duration="30988000" />
      <workItem from="1715956214315" duration="406000" />
      <workItem from="1716010038606" duration="393000" />
      <workItem from="1716010443348" duration="40000" />
      <workItem from="1716010517686" duration="1693000" />
      <workItem from="1716012224327" duration="39000" />
      <workItem from="1716012269087" duration="5638000" />
      <workItem from="1716019403529" duration="1922000" />
      <workItem from="1716097452698" duration="64972000" />
      <workItem from="1716249118979" duration="4311000" />
      <workItem from="1716257133641" duration="352000" />
      <workItem from="1716257495317" duration="10000" />
      <workItem from="1716257550127" duration="5747000" />
      <workItem from="1716277675217" duration="11866000" />
      <workItem from="1716336523144" duration="36757000" />
      <workItem from="1716422763544" duration="3000" />
      <workItem from="1716426905091" duration="2396000" />
      <workItem from="1716431541000" duration="27200000" />
      <workItem from="1716509165474" duration="36195000" />
      <workItem from="1716611512493" duration="2323000" />
      <workItem from="1716768422068" duration="36368000" />
      <workItem from="1716850891098" duration="37614000" />
      <workItem from="1716915914608" duration="305000" />
      <workItem from="1716935978790" duration="27697000" />
      <workItem from="1716988214346" duration="1929000" />
      <workItem from="1717023243567" duration="37980000" />
      <workItem from="1717076234892" duration="958000" />
      <workItem from="1717109782100" duration="31151000" />
      <workItem from="1717373676575" duration="34682000" />
      <workItem from="1717459758689" duration="25287000" />
      <workItem from="1717507196226" duration="695000" />
      <workItem from="1717546570390" duration="22518000" />
      <workItem from="1717586683036" duration="4879000" />
      <workItem from="1717632959180" duration="241000" />
      <workItem from="1717633283080" duration="3000" />
      <workItem from="1717634456596" duration="9503000" />
      <workItem from="1717655034351" duration="211000" />
      <workItem from="1717658238585" duration="16693000" />
      <workItem from="1717719263574" duration="27974000" />
      <workItem from="1718065020246" duration="19063000" />
      <workItem from="1718151518141" duration="31340000" />
      <workItem from="1718238008169" duration="23707000" />
      <workItem from="1718275429627" duration="8174000" />
      <workItem from="1718324048160" duration="39264000" />
      <workItem from="1718583311920" duration="25808000" />
      <workItem from="1718669660294" duration="27258000" />
      <workItem from="1718756300502" duration="26593000" />
      <workItem from="1718842502430" duration="32532000" />
      <workItem from="1718929001454" duration="36188000" />
      <workItem from="1719035172540" duration="10782000" />
      <workItem from="1719188950036" duration="29955000" />
      <workItem from="1719274879791" duration="24692000" />
      <workItem from="1719360463686" duration="18229000" />
      <workItem from="1719386681212" duration="11379000" />
      <workItem from="1719447334205" duration="36756000" />
      <workItem from="1719534030557" duration="38772000" />
      <workItem from="1719793375432" duration="27870000" />
      <workItem from="1719879387576" duration="38338000" />
      <workItem from="1719966042558" duration="27990000" />
      <workItem from="1720052360979" duration="1237000" />
      <workItem from="1720053611787" duration="35967000" />
      <workItem from="1720138657294" duration="35829000" />
      <workItem from="1720243148478" duration="8406000" />
      <workItem from="1720326735976" duration="332000" />
      <workItem from="1720398108494" duration="11461000" />
      <workItem from="1720410128716" duration="5146000" />
      <workItem from="1720422106126" duration="575000" />
      <workItem from="1720422838761" duration="453000" />
      <workItem from="1720423795389" duration="10562000" />
      <workItem from="1720484248627" duration="31038000" />
      <workItem from="1720570475981" duration="31545000" />
      <workItem from="1720657069186" duration="38850000" />
      <workItem from="1720743375715" duration="31851000" />
      <workItem from="1720933846948" duration="42277000" />
      <workItem from="1721088922050" duration="29130000" />
      <workItem from="1721175383341" duration="55000" />
      <workItem from="1721175868690" duration="306000" />
      <workItem from="1721180686272" duration="864000" />
      <workItem from="1721183198122" duration="32000" />
      <workItem from="1721183235493" duration="2000" />
      <workItem from="1721187531945" duration="3331000" />
      <workItem from="1721197019026" duration="10662000" />
      <workItem from="1721262033988" duration="29415000" />
      <workItem from="1721348590554" duration="30736000" />
      <workItem from="1721449281402" duration="13000" />
      <workItem from="1721607822925" duration="29611000" />
      <workItem from="1721693879201" duration="32086000" />
      <workItem from="1721780066004" duration="11169000" />
      <workItem from="1721799770413" duration="2183000" />
      <workItem from="1721803729904" duration="19383000" />
      <workItem from="1721866598462" duration="39561000" />
      <workItem from="1721952768584" duration="36284000" />
      <workItem from="1722211077313" duration="21149000" />
      <workItem from="1722243115569" duration="10329000" />
      <workItem from="1722298595914" duration="30073000" />
      <workItem from="1722385031727" duration="41430000" />
      <workItem from="1722471247748" duration="32785000" />
      <workItem from="1722557773866" duration="25236000" />
      <workItem from="1722816834839" duration="34654000" />
      <workItem from="1722903320815" duration="24963000" />
      <workItem from="1722989773519" duration="32985000" />
      <workItem from="1723076122902" duration="6443000" />
      <workItem from="1723100129123" duration="163000" />
      <workItem from="1723100298474" duration="1051000" />
      <workItem from="1723162807598" duration="221000" />
      <workItem from="1723163124457" duration="269000" />
      <workItem from="1723163402175" duration="109000" />
      <workItem from="1723163518300" duration="21000" />
      <workItem from="1723163547213" duration="4142000" />
      <workItem from="1723176216664" duration="8006000" />
      <workItem from="1723191793809" duration="13278000" />
      <workItem from="1723422145497" duration="13000" />
      <workItem from="1723422175179" duration="38097000" />
      <workItem from="1723508245073" duration="33587000" />
      <workItem from="1723552598703" duration="332000" />
      <workItem from="1723553165217" duration="1545000" />
      <workItem from="1723594603014" duration="7956000" />
      <workItem from="1723607184687" duration="21417000" />
      <workItem from="1723681315058" duration="36575000" />
      <workItem from="1723767589460" duration="42224000" />
      <workItem from="1723974365269" duration="4456000" />
      <workItem from="1724027646782" duration="24709000" />
      <workItem from="1724113092879" duration="181328000" />
      <workItem from="1724505648255" duration="250000" />
      <workItem from="1724507391687" duration="1000" />
      <workItem from="1724507409119" duration="25341000" />
      <workItem from="1724653785909" duration="46255000" />
      <workItem from="1724747568790" duration="136041000" />
      <workItem from="1725236541971" duration="20219000" />
      <workItem from="1725266216700" duration="18700000" />
      <workItem from="1725326939141" duration="3000" />
      <workItem from="1725326956914" duration="55000" />
      <workItem from="1725327110327" duration="127531000" />
      <workItem from="1725610580474" duration="50610000" />
      <workItem from="1725879012560" duration="4977000" />
      <workItem from="1725885540082" duration="35116000" />
      <workItem from="1726014069492" duration="102892000" />
      <workItem from="1726273140222" duration="141000" />
      <workItem from="1726273301518" duration="44000" />
      <workItem from="1726273661081" duration="9152000" />
      <workItem from="1726285626546" duration="33815000" />
      <workItem from="1726646322906" duration="2862000" />
      <workItem from="1726651370538" duration="13836000" />
      <workItem from="1726709314336" duration="49600000" />
      <workItem from="1726831625892" duration="31562000" />
      <workItem from="1727074846937" duration="126632000" />
      <workItem from="1727436311107" duration="708000" />
      <workItem from="1727437029033" duration="13770000" />
      <workItem from="1727573902692" duration="14843000" />
      <workItem from="1727598596617" duration="3238000" />
      <workItem from="1727601998014" duration="29367000" />
      <workItem from="1728371470322" duration="4949000" />
      <workItem from="1728376631428" duration="436000" />
      <workItem from="1728384458326" duration="18064000" />
      <workItem from="1728443337957" duration="25431000" />
      <workItem from="1728478118878" duration="19387000" />
      <workItem from="1728543173834" duration="85649000" />
      <workItem from="1728865181066" duration="23346000" />
      <workItem from="1728904157883" duration="3599000" />
      <workItem from="1728909958830" duration="93022000" />
      <workItem from="1729214033242" duration="149313000" />
      <workItem from="1729748402955" duration="23197000" />
      <workItem from="1729815411719" duration="7937000" />
      <workItem from="1729842818505" duration="382000" />
      <workItem from="1729859849328" duration="62239000" />
      <workItem from="1730201681294" duration="26236000" />
      <workItem from="1730273951187" duration="534000" />
      <workItem from="1730274497913" duration="34522000" />
      <workItem from="1730358383028" duration="232475000" />
      <workItem from="1731327380314" duration="132000" />
      <workItem from="1731328567938" duration="2923000" />
      <workItem from="1731378895535" duration="360000" />
      <workItem from="1731379980697" duration="11579000" />
      <workItem from="1731464644706" duration="186983000" />
      <workItem from="1732279289672" duration="168296000" />
      <workItem from="1733099088160" duration="264270000" />
      <workItem from="1733908796348" duration="8007000" />
      <workItem from="1733923838708" duration="289014000" />
      <workItem from="1734755532114" duration="22151000" />
      <workItem from="1734938678272" duration="25000" />
      <workItem from="1734939143335" duration="382000" />
      <workItem from="1734940038631" duration="44000" />
      <workItem from="1734940237238" duration="10594000" />
      <workItem from="1734958350375" duration="17614000" />
      <workItem from="1735026414739" duration="62199000" />
      <workItem from="1735183597727" duration="22001000" />
      <workItem from="1735265438518" duration="177000" />
      <workItem from="1735265646043" duration="35000" />
      <workItem from="1735266177683" duration="621000" />
      <workItem from="1735278188745" duration="1012000" />
      <workItem from="1735288364764" duration="2661000" />
      <workItem from="1735295971389" duration="3156000" />
      <workItem from="1735301989771" duration="650000" />
      <workItem from="1735305219111" duration="1827000" />
      <workItem from="1735363674602" duration="75432000" />
      <workItem from="1735739469656" duration="25344000" />
      <workItem from="1735864248973" duration="68784000" />
      <workItem from="1736160947956" duration="556000" />
      <workItem from="1736161518714" duration="7810000" />
      <workItem from="1736169381888" duration="1335000" />
      <workItem from="1736209276362" duration="9158000" />
      <workItem from="1736218828082" duration="6377000" />
      <workItem from="1736230816471" duration="10110000" />
      <workItem from="1736241500550" duration="87350000" />
      <workItem from="1736428295941" duration="34313000" />
      <workItem from="1736511756546" duration="77258000" />
      <workItem from="1736773700319" duration="124152000" />
      <workItem from="1737085219675" duration="14911000" />
      <workItem from="1737333804565" duration="18856000" />
      <workItem from="1737450008236" duration="1036000" />
      <workItem from="1737451088917" duration="314000" />
      <workItem from="1737451415984" duration="96754000" />
      <workItem from="1739103433834" duration="11899000" />
      <workItem from="1739159378834" duration="108399000" />
      <workItem from="1739449140027" duration="84233000" />
      <workItem from="1739682973118" duration="129690000" />
      <workItem from="1740030633654" duration="96705000" />
      <workItem from="1740396319690" duration="24335000" />
      <workItem from="1740468472817" duration="119256000" />
      <workItem from="1740742309455" duration="12000" />
      <workItem from="1740986755728" duration="14000" />
      <workItem from="1740990219282" duration="72732000" />
      <workItem from="1741221115322" duration="105069000" />
      <workItem from="1741604601003" duration="611000" />
      <workItem from="1741606801187" duration="20210000" />
      <workItem from="1741682617584" duration="2541000" />
      <workItem from="1741691830702" duration="55291000" />
      <workItem from="1741845337232" duration="769000" />
      <workItem from="1741853113283" duration="2890000" />
      <workItem from="1741857037974" duration="78039000" />
      <workItem from="1742261288211" duration="20268000" />
      <workItem from="1742346501248" duration="24653000" />
      <workItem from="1742381044290" duration="21250000" />
      <workItem from="1742450010593" duration="122407000" />
      <workItem from="1742900083788" duration="141000" />
      <workItem from="1742900359145" duration="535000" />
      <workItem from="1742901998869" duration="16000" />
      <workItem from="1742902096987" duration="883000" />
      <workItem from="1742903930088" duration="171000" />
      <workItem from="1742905612893" duration="421000" />
      <workItem from="1742909408045" duration="59499000" />
      <workItem from="1743079828254" duration="673000" />
      <workItem from="1743080667514" duration="278000" />
      <workItem from="1743084161889" duration="725000" />
      <workItem from="1743124094498" duration="2000" />
      <workItem from="1743126563338" duration="300000" />
      <workItem from="1743133209958" duration="386000" />
      <workItem from="1743133680803" duration="382000" />
      <workItem from="1743142821656" duration="148000" />
      <workItem from="1743143505710" duration="474000" />
      <workItem from="1743146419322" duration="326000" />
      <workItem from="1743168913195" duration="52008000" />
      <workItem from="1743489106058" duration="5769000" />
      <workItem from="1743495533063" duration="33335000" />
      <workItem from="1743578117047" duration="8459000" />
      <workItem from="1743591770063" duration="649000" />
      <workItem from="1743592435797" duration="58000" />
      <workItem from="1743600524566" duration="46055000" />
      <workItem from="1744006875316" duration="754000" />
      <workItem from="1744008610801" duration="493000" />
      <workItem from="1744009282976" duration="311000" />
      <workItem from="1744009707565" duration="97000" />
      <workItem from="1744027438576" duration="1185000" />
      <workItem from="1744073613193" duration="1641000" />
      <workItem from="1744075920582" duration="21372000" />
      <workItem from="1744110877549" duration="3000" />
      <workItem from="1744165875272" duration="1954000" />
      <workItem from="1744168187178" duration="6100000" />
      <workItem from="1744182274890" duration="2014000" />
      <workItem from="1744190029547" duration="55000" />
      <workItem from="1744248133809" duration="1247000" />
      <workItem from="1744251318568" duration="2202000" />
      <workItem from="1744253628759" duration="12000" />
      <workItem from="1744256042682" duration="21352000" />
      <workItem from="1744335422842" duration="118000" />
      <workItem from="1744335603755" duration="55000" />
      <workItem from="1744337427179" duration="157000" />
      <workItem from="1744380141894" duration="304000" />
      <workItem from="1744590448469" duration="94175000" />
      <workItem from="1744805695945" duration="16099000" />
      <workItem from="1744874423423" duration="174000" />
      <workItem from="1744879697011" duration="20804000" />
      <workItem from="1744954923339" duration="64000" />
      <workItem from="1744955001339" duration="226000" />
      <workItem from="1744956271959" duration="1922000" />
      <workItem from="1744959000744" duration="1921000" />
      <workItem from="1744961010883" duration="2660000" />
      <workItem from="1744963701385" duration="21000" />
      <workItem from="1744971987009" duration="8464000" />
      <workItem from="1745194918609" duration="112280000" />
      <workItem from="1745540232928" duration="2579000" />
      <workItem from="1745547009754" duration="99057000" />
      <workItem from="1745905978397" duration="104000" />
      <workItem from="1745906096311" duration="3066000" />
      <workItem from="1745918378483" duration="71546000" />
      <workItem from="1746527918307" duration="185000" />
      <workItem from="1746529682508" duration="16000" />
      <workItem from="1746529867556" duration="86000" />
      <workItem from="1746530281388" duration="212000" />
      <workItem from="1746530704090" duration="152000" />
      <workItem from="1746531038875" duration="94000" />
      <workItem from="1746578918391" duration="19958000" />
      <workItem from="1746606487492" duration="3000" />
      <workItem from="1746606754942" duration="35828000" />
      <workItem from="1746689925483" duration="82000" />
      <workItem from="1746690080141" duration="2939000" />
      <workItem from="1746700761771" duration="2055000" />
      <workItem from="1746704284011" duration="3385000" />
      <workItem from="1746749896812" duration="5827000" />
      <workItem from="1746757889531" duration="9625000" />
      <workItem from="1746774113037" duration="21926000" />
      <workItem from="1747009060325" duration="50184000" />
      <workItem from="1747129708973" duration="1029000" />
      <workItem from="1747131958114" duration="37388000" />
      <workItem from="1747221973167" duration="67272000" />
      <workItem from="1747391432705" duration="53269000" />
      <workItem from="1747706538941" duration="8914000" />
      <workItem from="1747724286781" duration="464000" />
      <workItem from="1747725786298" duration="31519000" />
      <workItem from="1747808237062" duration="13296000" />
      <workItem from="1747824689936" duration="31981000" />
      <workItem from="1747904413860" duration="14044000" />
      <workItem from="1747959528681" duration="4117000" />
      <workItem from="1747964184386" duration="10315000" />
      <workItem from="1747981298100" duration="177000" />
      <workItem from="1747981789258" duration="71000" />
      <workItem from="1747986997094" duration="25999000" />
      <workItem from="1748245014683" duration="22911000" />
      <workItem from="1748312188290" duration="29890000" />
      <workItem from="1748391340620" duration="2340000" />
      <workItem from="1748393710558" duration="386000" />
      <workItem from="1748396096400" duration="16111000" />
      <workItem from="1748421492184" duration="1462000" />
      <workItem from="1748423663018" duration="12607000" />
      <workItem from="1748478998841" duration="1109000" />
      <workItem from="1748480139956" duration="24283000" />
      <workItem from="1748567080150" duration="27880000" />
      <workItem from="1748910421739" duration="39034000" />
      <workItem from="1749001109495" duration="26933000" />
      <workItem from="1749042216185" duration="12434000" />
      <workItem from="1749106402770" duration="36557000" />
      <workItem from="1749190948162" duration="31588000" />
      <workItem from="1749440065867" duration="42202000" />
      <workItem from="1749536748764" duration="8390000" />
      <workItem from="1749548729828" duration="39052000" />
      <workItem from="1749638871848" duration="21453000" />
      <workItem from="1749708777616" duration="91000" />
      <workItem from="1749708911988" duration="1000" />
      <workItem from="1749709806104" duration="10068000" />
      <workItem from="1749721409682" duration="2403000" />
      <workItem from="1749730389616" duration="2455000" />
      <workItem from="1749734092402" duration="921000" />
      <workItem from="1749785012936" duration="1327000" />
      <workItem from="1749794077825" duration="405000" />
      <workItem from="1749794945027" duration="6768000" />
      <workItem from="1749811127620" duration="17767000" />
      <workItem from="1750041675299" duration="1080000" />
      <workItem from="1750054474195" duration="939000" />
      <workItem from="1750055853952" duration="3506000" />
      <workItem from="1750063511045" duration="10675000" />
      <workItem from="1750076040481" duration="15566000" />
      <workItem from="1750129518427" duration="11435000" />
      <workItem from="1750147921962" duration="44640000" />
      <workItem from="1750247697071" duration="64764000" />
      <workItem from="1750415861832" duration="13518000" />
      <workItem from="1750483679431" duration="219000" />
      <workItem from="1750489336352" duration="56000" />
      <workItem from="1750489646186" duration="64426000" />
      <workItem from="1750763066223" duration="5370000" />
      <workItem from="1750768518088" duration="3584000" />
      <workItem from="1750810636968" duration="53525000" />
      <workItem from="1750906563959" duration="26656000" />
      <workItem from="1751009319470" duration="37000" />
      <workItem from="1751014881086" duration="2191000" />
      <workItem from="1751019905194" duration="779000" />
      <workItem from="1751022064631" duration="554000" />
      <workItem from="1751032846828" duration="1370000" />
      <workItem from="1751242857631" duration="7126000" />
      <workItem from="1751251897268" duration="2898000" />
      <workItem from="1751265924655" duration="7119000" />
      <workItem from="1751273717837" duration="22030000" />
      <workItem from="1751356879606" duration="69641000" />
      <workItem from="1751510555071" duration="26769000" />
      <workItem from="1751595915611" duration="7011000" />
      <workItem from="1751614242107" duration="6943000" />
      <workItem from="1751625615628" duration="804000" />
      <workItem from="1751626685025" duration="2842000" />
      <workItem from="1751635454021" duration="34000" />
      <workItem from="1751637674845" duration="642000" />
      <workItem from="1751694191688" duration="4337000" />
      <workItem from="1751703860046" duration="2398000" />
      <workItem from="1751847850465" duration="26656000" />
      <workItem from="1751934874457" duration="40877000" />
      <workItem from="1752036838787" duration="56304000" />
      <workItem from="1752135663983" duration="33184000" />
      <workItem from="1752203192635" duration="21488000" />
      <workItem from="1752226338810" duration="19128000" />
      <workItem from="1752281672373" duration="92617000" />
      <workItem from="1752495612188" duration="32093000" />
      <workItem from="1752564982981" duration="28634000" />
      <workItem from="1752634856445" duration="744000" />
      <workItem from="1752635665114" duration="23805000" />
      <workItem from="1752664070012" duration="52688000" />
      <workItem from="1752797603138" duration="30711000" />
      <workItem from="1752832503582" duration="78113000" />
      <workItem from="1753062118108" duration="32683000" />
      <workItem from="1753098687291" duration="4154000" />
      <workItem from="1753168222550" duration="49461000" />
      <workItem from="1753269632280" duration="4740000" />
      <workItem from="1753329179495" duration="847000" />
      <workItem from="1753402491747" duration="18842000" />
      <workItem from="1753423370398" duration="385000" />
      <workItem from="1753441097579" duration="657000" />
      <workItem from="1753452066945" duration="2230000" />
      <workItem from="1753667753830" duration="5139000" />
      <workItem from="1753674306107" duration="19244000" />
      <workItem from="1753705797841" duration="3358000" />
      <workItem from="1753753892111" duration="892000" />
      <workItem from="1753756007897" duration="16000" />
      <workItem from="1753761565445" duration="527000" />
      <workItem from="1753778785976" duration="81000" />
      <workItem from="1753780029698" duration="3038000" />
      <workItem from="1753793622848" duration="2307000" />
      <workItem from="1753796342600" duration="307000" />
      <workItem from="1753796897842" duration="14327000" />
      <workItem from="1753876045210" duration="5155000" />
    </task>
    <task id="LOCAL-00264" summary="周学时安排解析优化">
      <option name="closed" value="true" />
      <created>1749090231255</created>
      <option name="number" value="00264" />
      <option name="presentableId" value="LOCAL-00264" />
      <option name="project" value="LOCAL" />
      <updated>1749090231255</updated>
    </task>
    <task id="LOCAL-00265" summary="开课统计周学时类型改造">
      <option name="closed" value="true" />
      <created>1749123832299</created>
      <option name="number" value="00265" />
      <option name="presentableId" value="LOCAL-00265" />
      <option name="project" value="LOCAL" />
      <updated>1749123832300</updated>
    </task>
    <task id="LOCAL-00266" summary="异动选课处理更换数据源">
      <option name="closed" value="true" />
      <created>1749126097885</created>
      <option name="number" value="00266" />
      <option name="presentableId" value="LOCAL-00266" />
      <option name="project" value="LOCAL" />
      <updated>1749126097885</updated>
    </task>
    <task id="LOCAL-00267" summary="中职人培教学进度特殊周学时拆分同步">
      <option name="closed" value="true" />
      <created>1749190517772</created>
      <option name="number" value="00267" />
      <option name="presentableId" value="LOCAL-00267" />
      <option name="project" value="LOCAL" />
      <updated>1749190517772</updated>
    </task>
    <task id="LOCAL-00268" summary="教学进程表增加额外学时">
      <option name="closed" value="true" />
      <created>1749211193448</created>
      <option name="number" value="00268" />
      <option name="presentableId" value="LOCAL-00268" />
      <option name="project" value="LOCAL" />
      <updated>1749211193450</updated>
    </task>
    <task id="LOCAL-00269" summary="是否纯实践课程增加默认值">
      <option name="closed" value="true" />
      <created>1749217212771</created>
      <option name="number" value="00269" />
      <option name="presentableId" value="LOCAL-00269" />
      <option name="project" value="LOCAL" />
      <updated>1749217212771</updated>
    </task>
    <task id="LOCAL-00270" summary="1.人培智能体对接&#10;2.异动类型转班级 (学籍班级)改造">
      <option name="closed" value="true" />
      <created>1749458127776</created>
      <option name="number" value="00270" />
      <option name="presentableId" value="LOCAL-00270" />
      <option name="project" value="LOCAL" />
      <updated>1749458127776</updated>
    </task>
    <task id="LOCAL-00271" summary="镜像配置改造">
      <option name="closed" value="true" />
      <created>1749466615719</created>
      <option name="number" value="00271" />
      <option name="presentableId" value="LOCAL-00271" />
      <option name="project" value="LOCAL" />
      <updated>1749466615719</updated>
    </task>
    <task id="LOCAL-00272" summary="1.自动配课、手动配课&#10;2.班课速配审批版">
      <option name="closed" value="true" />
      <created>1749523144925</created>
      <option name="number" value="00272" />
      <option name="presentableId" value="LOCAL-00272" />
      <option name="project" value="LOCAL" />
      <updated>1749523144925</updated>
    </task>
    <task id="LOCAL-00273" summary="镜像配置改造">
      <option name="closed" value="true" />
      <created>1749543527536</created>
      <option name="number" value="00273" />
      <option name="presentableId" value="LOCAL-00273" />
      <option name="project" value="LOCAL" />
      <updated>1749543527538</updated>
    </task>
    <task id="LOCAL-00274" summary="镜像css、js引用部分改造">
      <option name="closed" value="true" />
      <created>1749556479360</created>
      <option name="number" value="00274" />
      <option name="presentableId" value="LOCAL-00274" />
      <option name="project" value="LOCAL" />
      <updated>1749556479361</updated>
    </task>
    <task id="LOCAL-00275" summary="自动配课、手动配课">
      <option name="closed" value="true" />
      <created>1749556633818</created>
      <option name="number" value="00275" />
      <option name="presentableId" value="LOCAL-00275" />
      <option name="project" value="LOCAL" />
      <updated>1749556633818</updated>
    </task>
    <task id="LOCAL-00276" summary="1.福州财政金融职业中专学校教学任务书导出">
      <option name="closed" value="true" />
      <created>1749819194147</created>
      <option name="number" value="00276" />
      <option name="presentableId" value="LOCAL-00276" />
      <option name="project" value="LOCAL" />
      <updated>1749819194148</updated>
    </task>
    <task id="LOCAL-00277" summary="添加/更新板块类型课程">
      <option name="closed" value="true" />
      <created>1750054928622</created>
      <option name="number" value="00277" />
      <option name="presentableId" value="LOCAL-00277" />
      <option name="project" value="LOCAL" />
      <updated>1750054928622</updated>
    </task>
    <task id="LOCAL-00278" summary="授课教师明细、授课教师汇总增加学年学期字段">
      <option name="closed" value="true" />
      <created>1750060631931</created>
      <option name="number" value="00278" />
      <option name="presentableId" value="LOCAL-00278" />
      <option name="project" value="LOCAL" />
      <updated>1750060631932</updated>
    </task>
    <task id="LOCAL-00279" summary="授课教师明细、授课教师汇总查重条件更新">
      <option name="closed" value="true" />
      <created>1750124095880</created>
      <option name="number" value="00279" />
      <option name="presentableId" value="LOCAL-00279" />
      <option name="project" value="LOCAL" />
      <updated>1750124095881</updated>
    </task>
    <task id="LOCAL-00280" summary="lunar升级">
      <option name="closed" value="true" />
      <created>1750126201666</created>
      <option name="number" value="00280" />
      <option name="presentableId" value="LOCAL-00280" />
      <option name="project" value="LOCAL" />
      <updated>1750126201666</updated>
    </task>
    <task id="LOCAL-00281" summary="部分页面改造">
      <option name="closed" value="true" />
      <created>1750143900510</created>
      <option name="number" value="00281" />
      <option name="presentableId" value="LOCAL-00281" />
      <option name="project" value="LOCAL" />
      <updated>1750143900512</updated>
    </task>
    <task id="LOCAL-00282" summary="1.人培智能体接口增加数据权限&#10;2.开课审批流转部分字段补充">
      <option name="closed" value="true" />
      <created>1750248200509</created>
      <option name="number" value="00282" />
      <option name="presentableId" value="LOCAL-00282" />
      <option name="project" value="LOCAL" />
      <updated>1750248200509</updated>
    </task>
    <task id="LOCAL-00283" summary="1.陕西省机械高级技工学校教学任务通知书导出&#10;2.板块课管理部分页面优化">
      <option name="closed" value="true" />
      <created>1750389772183</created>
      <option name="number" value="00283" />
      <option name="presentableId" value="LOCAL-00283" />
      <option name="project" value="LOCAL" />
      <updated>1750389772183</updated>
    </task>
    <task id="LOCAL-00284" summary="优化重复推送学籍异动">
      <option name="closed" value="true" />
      <created>1750425078268</created>
      <option name="number" value="00284" />
      <option name="presentableId" value="LOCAL-00284" />
      <option name="project" value="LOCAL" />
      <updated>1750425078269</updated>
    </task>
    <task id="LOCAL-00285" summary="优化重复推送学籍异动">
      <option name="closed" value="true" />
      <created>1750481463740</created>
      <option name="number" value="00285" />
      <option name="presentableId" value="LOCAL-00285" />
      <option name="project" value="LOCAL" />
      <updated>1750481463742</updated>
    </task>
    <task id="LOCAL-00286" summary="教学计划进度对接方案内课程部分字段数据源更新">
      <option name="closed" value="true" />
      <created>1750659676449</created>
      <option name="number" value="00286" />
      <option name="presentableId" value="LOCAL-00286" />
      <option name="project" value="LOCAL" />
      <updated>1750659676449</updated>
    </task>
    <task id="LOCAL-00287" summary="河北公职教学计划导出表格标题调整">
      <option name="closed" value="true" />
      <created>1750659753546</created>
      <option name="number" value="00287" />
      <option name="presentableId" value="LOCAL-00287" />
      <option name="project" value="LOCAL" />
      <updated>1750659753546</updated>
    </task>
    <task id="LOCAL-00288" summary="指定授课教师增加系部筛选功能">
      <option name="closed" value="true" />
      <created>1750728903329</created>
      <option name="number" value="00288" />
      <option name="presentableId" value="LOCAL-00288" />
      <option name="project" value="LOCAL" />
      <updated>1750728903329</updated>
    </task>
    <task id="LOCAL-00289" summary="维护教学计划">
      <option name="closed" value="true" />
      <created>1750908817272</created>
      <option name="number" value="00289" />
      <option name="presentableId" value="LOCAL-00289" />
      <option name="project" value="LOCAL" />
      <updated>1750908817273</updated>
    </task>
    <task id="LOCAL-00290" summary="绥阳县中等职业学校同步更新班级毕业时间">
      <option name="closed" value="true" />
      <created>1750943900300</created>
      <option name="number" value="00290" />
      <option name="presentableId" value="LOCAL-00290" />
      <option name="project" value="LOCAL" />
      <updated>1750943900300</updated>
    </task>
    <task id="LOCAL-00291" summary="学籍异动同步更新学生毕业时间">
      <option name="closed" value="true" />
      <created>1751022594434</created>
      <option name="number" value="00291" />
      <option name="presentableId" value="LOCAL-00291" />
      <option name="project" value="LOCAL" />
      <updated>1751022594434</updated>
    </task>
    <task id="LOCAL-00292" summary="银川职业技术学院教学任务书导出">
      <option name="closed" value="true" />
      <created>1751276719198</created>
      <option name="number" value="00292" />
      <option name="presentableId" value="LOCAL-00292" />
      <option name="project" value="LOCAL" />
      <updated>1751276719198</updated>
    </task>
    <task id="LOCAL-00293" summary="人培全流程增加专业方向">
      <option name="closed" value="true" />
      <created>1751276914834</created>
      <option name="number" value="00293" />
      <option name="presentableId" value="LOCAL-00293" />
      <option name="project" value="LOCAL" />
      <updated>1751276914834</updated>
    </task>
    <task id="LOCAL-00294" summary="人培全流程增加专业方向">
      <option name="closed" value="true" />
      <created>1751279671271</created>
      <option name="number" value="00294" />
      <option name="presentableId" value="LOCAL-00294" />
      <option name="project" value="LOCAL" />
      <updated>1751279671271</updated>
    </task>
    <task id="LOCAL-00295" summary="开班设置跨专业开班">
      <option name="closed" value="true" />
      <created>1751524856179</created>
      <option name="number" value="00295" />
      <option name="presentableId" value="LOCAL-00295" />
      <option name="project" value="LOCAL" />
      <updated>1751524856179</updated>
    </task>
    <task id="LOCAL-00296" summary="开班设置跨专业开班">
      <option name="closed" value="true" />
      <created>1751597930004</created>
      <option name="number" value="00296" />
      <option name="presentableId" value="LOCAL-00296" />
      <option name="project" value="LOCAL" />
      <updated>1751597930004</updated>
    </task>
    <task id="LOCAL-00297" summary="周学时安排弹窗调整">
      <option name="closed" value="true" />
      <created>1751637732344</created>
      <option name="number" value="00297" />
      <option name="presentableId" value="LOCAL-00297" />
      <option name="project" value="LOCAL" />
      <updated>1751637732346</updated>
    </task>
    <task id="LOCAL-00298" summary="去除班级人数大于0的条件">
      <option name="closed" value="true" />
      <created>1751885497046</created>
      <option name="number" value="00298" />
      <option name="presentableId" value="LOCAL-00298" />
      <option name="project" value="LOCAL" />
      <updated>1751885497046</updated>
    </task>
    <task id="LOCAL-00299" summary="开课设置本地存储">
      <option name="closed" value="true" />
      <created>1752128281639</created>
      <option name="number" value="00299" />
      <option name="presentableId" value="LOCAL-00299" />
      <option name="project" value="LOCAL" />
      <updated>1752128281639</updated>
    </task>
    <task id="LOCAL-00300" summary="开课设置本地存储">
      <option name="closed" value="true" />
      <created>1752134122955</created>
      <option name="number" value="00300" />
      <option name="presentableId" value="LOCAL-00300" />
      <option name="project" value="LOCAL" />
      <updated>1752134122955</updated>
    </task>
    <task id="LOCAL-00301" summary="开课设置审批数据本地存储">
      <option name="closed" value="true" />
      <created>1752288160703</created>
      <option name="number" value="00301" />
      <option name="presentableId" value="LOCAL-00301" />
      <option name="project" value="LOCAL" />
      <updated>1752288160703</updated>
    </task>
    <task id="LOCAL-00302" summary="板块课部分页面调整及优化">
      <option name="closed" value="true" />
      <created>1752298220571</created>
      <option name="number" value="00302" />
      <option name="presentableId" value="LOCAL-00302" />
      <option name="project" value="LOCAL" />
      <updated>1752298220571</updated>
    </task>
    <task id="LOCAL-00303" summary="开课设置审批数据本地存储">
      <option name="closed" value="true" />
      <created>1752312506817</created>
      <option name="number" value="00303" />
      <option name="presentableId" value="LOCAL-00303" />
      <option name="project" value="LOCAL" />
      <updated>1752312506818</updated>
    </task>
    <task id="LOCAL-00304" summary="开课设置审批数据本地存储">
      <option name="closed" value="true" />
      <created>1752370340965</created>
      <option name="number" value="00304" />
      <option name="presentableId" value="LOCAL-00304" />
      <option name="project" value="LOCAL" />
      <updated>1752370340965</updated>
    </task>
    <task id="LOCAL-00305" summary="本科版提交开课设置">
      <option name="closed" value="true" />
      <created>1752639725313</created>
      <option name="number" value="00305" />
      <option name="presentableId" value="LOCAL-00305" />
      <option name="project" value="LOCAL" />
      <updated>1752639725314</updated>
    </task>
    <task id="LOCAL-00306" summary="人培流程增加班级容量">
      <option name="closed" value="true" />
      <created>1752670677399</created>
      <option name="number" value="00306" />
      <option name="presentableId" value="LOCAL-00306" />
      <option name="project" value="LOCAL" />
      <updated>1752670677400</updated>
    </task>
    <task id="LOCAL-00307" summary="教学计划教学班审核状态回写优化">
      <option name="closed" value="true" />
      <created>1752729183951</created>
      <option name="number" value="00307" />
      <option name="presentableId" value="LOCAL-00307" />
      <option name="project" value="LOCAL" />
      <updated>1752729183953</updated>
    </task>
    <task id="LOCAL-00308" summary="开课设置优化">
      <option name="closed" value="true" />
      <created>1752847057159</created>
      <option name="number" value="00308" />
      <option name="presentableId" value="LOCAL-00308" />
      <option name="project" value="LOCAL" />
      <updated>1752847057159</updated>
    </task>
    <task id="LOCAL-00309" summary="开课设置页面改版">
      <option name="closed" value="true" />
      <created>1753099042273</created>
      <option name="number" value="00309" />
      <option name="presentableId" value="LOCAL-00309" />
      <option name="project" value="LOCAL" />
      <updated>1753099042274</updated>
    </task>
    <task id="LOCAL-00310" summary="教学进程表增加一个是否校验总学时的配置功能">
      <option name="closed" value="true" />
      <created>1753246407421</created>
      <option name="number" value="00310" />
      <option name="presentableId" value="LOCAL-00310" />
      <option name="project" value="LOCAL" />
      <updated>1753246407423</updated>
    </task>
    <task id="LOCAL-00311" summary="拆分教学班同步教学班学生处理">
      <option name="closed" value="true" />
      <created>1753423059935</created>
      <option name="number" value="00311" />
      <option name="presentableId" value="LOCAL-00311" />
      <option name="project" value="LOCAL" />
      <updated>1753423059937</updated>
    </task>
    <task id="LOCAL-00312" summary="同步开课信息表部分字段来源变更">
      <option name="closed" value="true" />
      <created>1753762037969</created>
      <option name="number" value="00312" />
      <option name="presentableId" value="LOCAL-00312" />
      <option name="project" value="LOCAL" />
      <updated>1753762037970</updated>
    </task>
    <option name="localTasksCounter" value="313" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
        <option value="Space.CommitStatus" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="master" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="test" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/master" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
        <entry key="User">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="*" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="曹风" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="张继云" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="王宇鹏" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="COLUMN_ID_WIDTH">
                <map>
                  <entry key="Table.Default.Author.ColumnIdWidth" value="100" />
                  <entry key="Table.Default.Date.ColumnIdWidth" value="166" />
                  <entry key="Table.GitHub.CommitStatus.ColumnIdWidth" value="53" />
                  <entry key="Table.Space.CommitStatus.ColumnIdWidth" value="59" />
                </map>
              </option>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="master" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <option name="ADD_EXTERNAL_FILES_SILENTLY" value="true" />
    <option name="SHELVE_DETAILS_PREVIEW_SHOWN" value="true" />
    <MESSAGE value="1.人培智能体接口增加数据权限&#10;2.开课审批流转部分字段补充" />
    <MESSAGE value="1.陕西省机械高级技工学校教学任务通知书导出&#10;2.板块课管理部分页面优化" />
    <MESSAGE value="优化重复推送学籍异动" />
    <MESSAGE value="教学计划进度对接方案内课程部分字段数据源更新" />
    <MESSAGE value="河北公职教学计划导出表格标题调整" />
    <MESSAGE value="指定授课教师增加系部筛选功能" />
    <MESSAGE value="维护教学计划" />
    <MESSAGE value="绥阳县中等职业学校同步更新班级毕业时间" />
    <MESSAGE value="学籍异动同步更新学生毕业时间" />
    <MESSAGE value="银川职业技术学院教学任务书导出" />
    <MESSAGE value="人培全流程增加专业方向" />
    <MESSAGE value="开班设置跨专业开班" />
    <MESSAGE value="周学时安排弹窗调整" />
    <MESSAGE value="去除班级人数大于0的条件" />
    <MESSAGE value="开课设置本地存储" />
    <MESSAGE value="板块课部分页面调整及优化" />
    <MESSAGE value="开课设置审批数据本地存储" />
    <MESSAGE value="本科版提交开课设置" />
    <MESSAGE value="人培流程增加班级容量" />
    <MESSAGE value="教学计划教学班审核状态回写优化" />
    <MESSAGE value="开课设置优化" />
    <MESSAGE value="开课设置页面改版" />
    <MESSAGE value="教学进程表增加一个是否校验总学时的配置功能" />
    <MESSAGE value="拆分教学班同步教学班学生处理" />
    <MESSAGE value="同步开课信息表部分字段来源变更" />
    <option name="LAST_COMMIT_MESSAGE" value="同步开课信息表部分字段来源变更" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/chaoxing/academic/controller/api/form/material/ApiMaterialDataPushController.java</url>
          <line>61</line>
          <option name="timeStamp" value="7" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/chaoxing/academic/service/material/impl/MaterialServiceImpl.java</url>
          <line>2763</line>
          <option name="timeStamp" value="10" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/test/java/com/chaoxing/academic/form/Tform.java</url>
          <line>1124</line>
          <option name="timeStamp" value="17" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/chaoxing/academic/design/template/impl/cultivation/TeachPlanSyncClazzTask.java</url>
          <line>71</line>
          <option name="timeStamp" value="18" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <pin-to-top-manager>
      <pinned-members>
        <PinnedItemInfo parentTag="com.chaoxing.office.app.entity.forms.vo.data.approve.flow.ApproveNodeDetailInfo" memberName="children" />
        <PinnedItemInfo parentTag="okhttp3.Request" memberName="tags" />
      </pinned-members>
    </pin-to-top-manager>
  </component>
  <component name="XPathView.XPathProjectComponent">
    <history />
    <find-history />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>