import{e as r,f as H,g as J,o as K,c as Q,a as s,b as o,w as c,h as x,v as C,F as W,d as g,p as X,i as Y}from"./index-f3df8255.js";import{t as R,p as Z,s as ee,R as n,a as te,b as ae,m as se,E as le,c as oe,d as ne,M as V,e as ue,f as re,g as ce,h as ie,i as pe}from"./basic.setting-61aeaadd.js";import{_ as de}from"./_plugin-vue_export-helper-c27b6911.js";const h=f=>(X("data-v-6e3f66fd"),f=f(),Y(),f),me={class:"data-push-wrap"},he=h(()=>s("div",{class:"label-info"},"基础设置",-1)),fe={class:"form-item"},ve={class:"form-item-score"},_e={class:"form-item"},be={class:"push-top"},ge=h(()=>s("div",{class:"label-info"},"推送设置",-1)),Se={class:"btn"},Te={class:"oprate"},Pe=["onClick"],ye=h(()=>s("div",{class:"tip-box"},[s("div",{class:"icons loading"}),s("h3",null,[g("数据准备中..."),s("br")]),s("p",null,"数据推送任务开启成功，请稍后再“推送日志”中查看进度")],-1)),we=h(()=>s("div",{class:"icons warning"},null,-1)),Ee=h(()=>s("h3",{style:{"margin-bottom":"24px"}},"确认是否开启推送？",-1)),xe={__name:"SmartDataPush",setup(f){const k=r("default"),v=r({}),A=r([{show:!0,label:"目标表",value:e=>{let t="";return p.value.forEach(l=>{if(e.formAlias===l.sourceAlias)return t=l.value,!1}),t}}]),d=r(1),a=r({enablePush:0,multipleState:!0,targetTable:[],semester:""}),L=()=>{ee(d.value).then(e=>{if(n.ecode(e)){n.errorTip(e.msg);return}const t=e.data||{};a.value.id=t.id,v.value.fid=t.fid,a.value.enablePush=t.enable===1,a.value.semester=t.semester,a.value.targetTable=t.targetTable?t.targetTable.split(","):[],I()}).catch(e=>{n.errorTip(e.message)})},S=r([]),B=async()=>{const e=await te(d.value);if(n.ecode(e)){n.errorTip(e.msg);return}let t=[];e.data.forEach(l=>t.push({label:l,value:l})),S.value=t},p=r([]),I=async()=>{const e=await ae(d.value);if(n.ecode(e)){n.errorTip(e.msg);return}const t=e.data,l=[];t.forEach(u=>{l.push({label:u.id.toString(),value:u.name,sourceAlias:u.sourceAlias,targetAlias:u.targetAlias})}),p.value=l},N=async()=>{let e=a.value.enablePush?1:0,t=a.value.semester;S.value.forEach(b=>{if(t===b.label)return t=b.value,!1});let l=a.value.targetTable;if(e&&(!t||!l))return n.errorTip("请选择学年学期和目标表"),!1;let u=l?l.join(","):"";const m=await ue({id:a.value.id,enable:e,semester:t,targetTable:u,type:d.value});n.ecode(m)?n.errorTip(m.msg):n.successTip("保存成功")},D=H(()=>{let e=[],t=a.value.targetTable?a.value.targetTable.slice(","):[];return t&&p.value&&p.value.forEach(l=>{t.forEach(u=>{l.label===u&&e.push(l)})}),e}),y=r([]),F=e=>{y.value=e},w=r(null),E=r(null),_=r(null),T=r({batchPush:{}}),M=async()=>{let e=a.value.semester;if(!e){n.errorTip("请选择学年学期");return}let t="";if(y.value.forEach(l=>t+=l.label+","),!t){n.errorTip("请选择目标表");return}T.value.batchPush={semester:e,targetTableIds:t},w.value.open()},z=async()=>{T.value.batchPush.type=d.value;const e=await se(T.value.batchPush);n.ecode(e)?n.errorTip(e.msg):E.value.open()},U=()=>{_.value.open()},j=()=>{},O=()=>{let e="";p.value.forEach(t=>{e+=t.sourceAlias+","}),v.value.tableName=e.substring(0,e.lastIndexOf(",")),_.value.open()},$=e=>{v.value.tableName=e.sourceAlias,_.value.open()};return J(()=>{L(),B()}),(e,t)=>{const l=re,u=ce,m=le,b=oe,P=ie,q=pe,G=ne;return K(),Q(W,null,[s("div",me,[o(b,{ref:"basicSettingRef",model:a.value,"label-width":"auto",class:"demo-basicSetting",size:k.value,"label-position":"left","status-icon":""},{default:c(()=>[he,s("div",fe,[s("div",ve,[g(" 是否开启智慧大脑推送： "),o(l,{modelValue:a.value.enablePush,"onUpdate:modelValue":t[0]||(t[0]=i=>a.value.enablePush=i),"inline-prompt":"","active-text":"是","inactive-text":"否"},null,8,["modelValue"])])]),x(s("div",_e,[o(u,{label:"学年学期",prop:"semester"},{default:c(()=>[o(V,{modelValue:a.value.semester,"onUpdate:modelValue":t[1]||(t[1]=i=>a.value.semester=i),width:"350px",optionSource:S.value},null,8,["modelValue","optionSource"])]),_:1}),o(u,{label:"目标表",prop:"targetTable"},{default:c(()=>[o(V,{multiple:a.value.multipleState,modelValue:a.value.targetTable,"onUpdate:modelValue":t[2]||(t[2]=i=>a.value.targetTable=i),width:"350px",optionSource:p.value},null,8,["multiple","modelValue","optionSource"])]),_:1}),o(u,{style:{"margin-top":"60px"}},{default:c(()=>[o(m,{type:"primary",onClick:N},{default:c(()=>[g(" 保存设置 ")]),_:1})]),_:1})],512),[[C,a.value.enablePush]])]),_:1},8,["model","size"]),x(s("div",null,[s("div",be,[ge,s("div",Se,[o(m,{type:"primary",onClick:M},{default:c(()=>[g(" 批量推送 ")]),_:1})])]),o(G,{data:D.value,style:{width:"100%"},stripe:"",border:"",onSelectionChange:F},{empty:c(()=>[o(q,{description:"没有数据呀"})]),default:c(()=>[o(P,{type:"selection",width:"55"}),o(P,{label:"目标表",prop:"value",width:"auto",align:"center"}),o(P,{label:"操作",align:"center",width:"152",fixed:"right"},{default:c(({row:i})=>[s("div",Te,[s("span",{class:"primary",onClick:Ce=>$(i)},"推送日志",8,Pe)])]),_:1})]),_:1},8,["data"])],512),[[C,a.value.enablePush]])]),o(R,{ref_key:"pushResultPopupRef",ref:E,cancleState:!1,onSuccess:U},{default:c(()=>[ye]),_:1},512),o(R,{ref_key:"pushConfirmPopupRef",ref:w,cancleState:!0,onSuccess:z},{default:c(()=>[s("div",{class:"tip-box"},[s("div",{class:"push-logs",onClick:O},"推送日志"),we,Ee])]),_:1},512),o(Z,{ref_key:"pushLogPopupRef",ref:_,RPS:v.value,ECS:A.value,onSuccess:j},null,8,["RPS","ECS"])],64)}}},Ae=de(xe,[["__scopeId","data-v-6e3f66fd"]]);export{Ae as default};
