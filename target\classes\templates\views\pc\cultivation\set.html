<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>开课设置</title>

    <!-- 样式文件 -->
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/global.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/plugin/layui-v2.9.13/layui/css/layui.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/reset.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/index3.0.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/dialogCourse.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/slideCommon.css'}">

    <!-- 页面特定样式 -->
    <style>
        .j-search-con.j-search-error input { border: 1px solid #ff5e5e !important; }
        .layui-table-cell .add { display: inline-block; color: #4c88ff; cursor: pointer; }
        #selTextbook .dialog-con .j-search-con,
        #selClass .dialog-con .j-search-con { width: 170px; }
    </style>

    <!-- JavaScript文件 -->
    <script th:src="${_CPR_+_VR_+'/plugin/layui-v2.9.13/layui/layui.js'}"></script>
</head>

<body>
<!-- 主容器 -->
<div class="main">
    <div class="wrap-item">
        <!-- 页面标题 -->
        <header class="m-top">
            <div class="title">开课</div>
        </header>

        <!-- 开课信息区域 -->
        <section class="item">
            <div class="i-top" style="margin-top: 40px;">
                <h3>开课信息</h3>
                <span class="arrow slide"></span>
            </div>

            <div class="i-con">
                <!-- 课程基本信息 -->
                <div class="course-inform">
                    <h4>课程信息</h4>
                    <ul>
                        <li><div class="name">开课学期：</div><div class="tit" th:title="${teachPlan.jxjhgl_xnxq}" th:text="${teachPlan.jxjhgl_xnxq}"></div></li>
                        <li><div class="name">开课院系：</div><div class="tit" th:title="${teachPlan.jxjhgl_kkxb}" th:text="${teachPlan.jxjhgl_kkxb}"></div></li>
                        <li><div class="name">所属专业：</div><div class="tit" th:title="${teachPlan.jxjhgl_zy}" th:text="${teachPlan.jxjhgl_zy}"></div></li>
                        <li><div class="name">培养层次：</div><div class="tit" th:title="${teachPlan.jxjhgl_pycc}" th:text="${teachPlan.jxjhgl_pycc}"></div></li>
                        <li><div class="name">课程名称：</div><div class="tit" th:title="${teachPlan.jxjhgl_kcmc}" th:text="${teachPlan.jxjhgl_kcmc}"></div></li>
                        <li><div class="name">课程性质：</div><div class="tit" th:title="${teachPlan.jxjhgl_kcxz}" th:text="${teachPlan.jxjhgl_kcxz}"></div></li>
                        <li><div class="name">是否为纯实践环节：</div><div class="tit" th:title="${teachPlan.jxjhgl_sfcsjhj}" th:text="${teachPlan.jxjhgl_sfcsjhj}"></div></li>
                        <li><div class="name">选必修：</div><div class="tit" th:title="${teachPlan.jxjhgl_xbx}" th:text="${teachPlan.jxjhgl_xbx}"></div></li>
                        <li><div class="name">总学分：</div><div class="tit" th:title="${teachPlan.jxjhgl_xf}" th:text="${teachPlan.jxjhgl_xf}"></div></li>
                        <li><div class="name">总学时：</div><div class="tit" th:title="${teachPlan.jxjhgl_zxs}" th:text="${teachPlan.jxjhgl_zxs}"></div></li>
                        <li><div class="name">理论学时：</div><div class="tit" th:title="${teachPlan.jxjhgl_llxs}" th:text="${teachPlan.jxjhgl_llxs}"></div></li>
                        <li><div class="name">实践学时：</div><div class="tit" th:title="${teachPlan.jxjhgl_sjxs}" th:text="${teachPlan.jxjhgl_sjxs}"></div></li>
                        <li><div class="name">上机学时：</div><div class="tit" th:title="${teachPlan.jxjhgl_sjxss}" th:text="${teachPlan.jxjhgl_sjxss}"></div></li>
                        <li><div class="name">实验学时：</div><div class="tit" th:title="${teachPlan.jxjhgl_syxs}" th:text="${teachPlan.jxjhgl_syxs}"></div></li>
                        <li><div class="name">其他学时：</div><div class="tit" th:title="${teachPlan.jxjhgl_qtxs}" th:text="${teachPlan.jxjhgl_qtxs}"></div></li>
                        <li><div class="name">计划人数：</div><div class="tit" th:title="${teachPlan.jxjhgl_zrs}" th:text="${teachPlan.jxjhgl_zrs}"></div></li>
                        <li><div class="name">分配人数：</div><div class="tit" id="yfprs"></div></li>
                        <li><div class="name">考试形式：</div><div class="tit" th:title="${teachPlan.jxjhgl_ksxs}" th:text="${teachPlan.jxjhgl_ksxs}"></div></li>
                    </ul>
                </div>

                <!-- 已生成教学班 -->
                <div class="class-box">
                    <div class="cb-top">
                        <div class="tit">已生成教学班</div>
                        <div class="i-mes">
                            <button id="resetClass">重置所有教学班</button>
                        </div>
                    </div>
                    <div class="j-table" style="border:none;">
                        <table class="layui-table" id="materialTable" lay-filter="materialTable"></table>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <footer class="footer">
                <button class="sure">保存</button>
                <button class="cancel">取消</button>
            </footer>
        </section>
    </div>
        <!-- 开班设置区域 -->
        <section class="item" style="background: #ffffff;border-radius: 8px;padding-top: 16px;overflow: hidden;">
            <div class="i-top">
                <h3>开班</h3>
                <span class="arrow slide"></span>
            </div>

            <div class="i-con" style="display: flex;overflow: unset;">
                <!-- 可选行政班 -->
                <div class="class-box" style="width: 47.5%;">
                    <div class="cb-top">
                        <div class="tit" style="margin-right:24px;">可选行政班</div>
                        <div class="i-mes">
                            <button class="singleClassTeaching">行政班单班开课</button>
                            <button id="selOtherClass">选择其他教学计划班级</button>
                        </div>
                    </div>
                    <div class="j-table" style="border:none;">
                        <table class="layui-table" id="classTable" lay-filter="classTable"></table>
                    </div>
                </div>

                <!-- 操作按钮区域 -->
                <div class="mutate">
                    <div class="up" id="up"></div>
                    <div class="down" id="down"></div>
                </div>

                <!-- 已生成教学班 -->
                <div class="class-box" style="width: 47.5%;overflow: unset;">
                    <div class="cb-top" style="justify-content: space-between;">
                        <div class="tit">已生成教学班</div>
                        <div class="i-mes">
                            <h5>教学班人数：<em id="classStuNum">0</em>人</h5>
                            <button id="confirmClass">确认生成教学班</button>
                        </div>
                    </div>
                    <div class="j-table" style="border:none;">
                        <table class="layui-table" id="teachingClassForm" lay-filter="teachingClassForm"></table>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>
</body>
<!-- ==================== 弹窗模块区域 ==================== -->

<!-- 选择其他专业班级弹窗 -->
<div id="selClass" class="dialog" style="display: none;">
    <div class="dialog-title">选择其他专业班级</div>
    <div class="tips">只能选择同一开课学年学期、开课课程一致、周学时一致的教学计划</div>

    <div class="dialog-con">
        <!-- 筛选表单 -->
        <form action="" class="layui-form form-textbook" style="margin-top: 24px;">
            <div class="layui-inline">
                <label class="layui-form-label" style="width: 56px;">专业名称</label>
                <div class="layui-input-inline">
                    <div class="j-search-con single-box">
                        <input type="text" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search"><input type="text" placeholder="搜索"><span></span></div>
                            <ul class="majorList"></ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label" style="width: 56px;">年级</label>
                <div class="layui-input-inline">
                    <div class="j-search-con single-box">
                        <input type="text" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search"><input type="text" placeholder="搜索"><span></span></div>
                            <ul class="gradeList"></ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label" style="width: 56px;">系部</label>
                <div class="layui-input-inline">
                    <div class="j-search-con single-box">
                        <input type="text" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search"><input type="text" placeholder="搜索"><span></span></div>
                            <ul class="deptList"></ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-inline">
                <button type="reset" class="layui-btn layui-btn-primary classForm">重置</button>
                <button type="submit" class="layui-btn classForm">查询</button>
            </div>
        </form>

        <!-- 班级列表 -->
        <div class="courseList">
            <table class="layui-hide" id="classList" lay-filter="classList"></table>
        </div>
    </div>

    <div class="dialog-footer">
        <button class="btnCancel">取消</button>
        <button id="classSureBtn">确定</button>
    </div>
</div>
<!-- 选择学生 -->
<div id="selTextbook" class="dialog" style="display: none;">
    <div class="dialog-title">选择学生</div>
    <div class="tips">当前可选学生[<span id="distributionStu"></span>]位，若不指定学生，系统则按照分配人数随机进行学生拆分
    </div>
    <div class="dialog-con">
        <form action="" class="layui-form form-textbook" style="margin-top: 24px;">
            <input type="hidden" id="bjbh">
            <div class="layui-inline">
                <label class="layui-form-label">学生学号</label>
                <div class="layui-input-inline">
                    <div class="j-search-con single-box">
                        <input type="text" name="courseName" placeholder="请输入" autocomplete="off" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <ul id="xhList">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">学生姓名</label>
                <div class="layui-input-inline">
                    <div class="j-search-con single-box">
                        <input type="text" name="bookNo" placeholder="请输入" autocomplete="off" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <ul id="xmList">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label mu" style="width: 56px;">学生性别</label>
                <div class="layui-input-inline">
                    <div class="j-search-con  multiple-box">
                        <input type="text" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="all-selects" style="margin-top: 6px;">全选</div>
                            <ul id="xbList">
                                <li>男</li>
                                <li>女</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label" style="width: 56px;">培养层次</label>
                <div class="layui-input-inline">
                    <div class="j-search-con single-box">
                        <input type="text" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <ul id="pyccList">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-inline">
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                <button type="submit" class="layui-btn " lay-submit lay-filter="selTextbookTable">查询</button>
            </div>
        </form>
        <div class="courseList">
            <table class="layui-hide" id="TextbookList" lay-filter="TextbookList"></table>
            <!--            <div class="z-check">-->
            <!--                <span class="check" id="checkAllStu"></span>选择全部数据-->
            <!--            </div>-->
            <!--            <div class="selCourse" id="selTextbookCount">已选中<em>0</em>条</div>-->
        </div>
    </div>
    <div class="dialog-footer">
        <button class="btnCancel">取消</button>
        <button id="TextbookSureBtn">确定</button>
    </div>
</div>
<!-- 错误提示 -->
<div class="dialog" id="dialogTip" style="display: none;">
    <div class="dialog-title">
        <h5>错误提示</h5><span class="close"></span>
    </div>
    <div class="dialog-con">
        <div class="error-con">
            <img src="/images/cultivation/error-tip1.png" alt="">
            <p class="error-title">指定学生人数大于分配人数，请重新指定学生</p>
        </div>
    </div>
    <div class="dialog-footer">
        <button class="btnTipCancel">取消</button>
        <button class="btnTipCancel">确定</button>
    </div>
</div>
<!-- 删除 -->
<script type="text/html" id="tmplToolBar2">
    {{# if(d.selTeachClassroomType == '父教学班' || !d.selTeachClassroomType){ }}
    <div class="add" lay-event="add" style="margin-right: 8px;">添加</div>
    {{# } }}
    <div class="delet" lay-event="del">删除</div>
</script>

<!-- 已安排人数 -->
<script type="text/html" id="titleTpl">
    <div class="stu">
        {{# if(d.arrangedNumber !=0 && d.arrangedNumber!=undefined){ }}
        <span lay-event="sel-stu">已指定{{d.arrangedNumber}}人</span>
        {{# } else { }}
        <span class="selStu" lay-event="sel-stu">点击选择人员</span>
        {{# } }}
    </div>
</script>
<!--未安排人数-->
<script type="text/html" id="notarrangedNumber">
    <div class="stu">
        {{# if(d.notarrangedNumber!=undefined){ }}
        <span>{{= d.notarrangedNumber}}</span>
        {{# } else { }}
        <span>{{= d.bjxx_njrs}}</span>
        {{# } }}
    </div>
</script>

<!-- 任课教师 -->
<script type="text/html" id="selectTeacher">
    <div class="j-search-con multiple-box" style="width: 110px;">
        <input type="text" name="selTeacher" placeholder="请选择" readonly="" class="schoolSel">
        <span class="j-arrow"></span>
        <div class="j-select-year">
            <div class="search">
                <input type="text" placeholder="搜索" class="teacher teacherSearch">
                <span></span>
            </div>
            <ul class="teacherList">
                {{# layui.each(d.teacherList, function(i, v){ }}
                <li data-puid="{{= v.puid }}" data-uname = "{{= v.uname }}" data-no = "{{= d.selTeacherNo }}">{{= v.uname }}</li>
                {{# }); }}
            </ul>
        </div>
    </div>
</script>
<!-- 教室类型 -->
<script type="text/html" id="selectClassroomType">
    <div class="j-search-con single-box " style="width: 110px;">
        <input type="text" name="selClassroomType" placeholder="请选择" readonly="" class="schoolSel">
        <span class="j-arrow"></span>
        <div class="j-select-year">
            <ul class="classType">
                {{# layui.each(d.selectClassroomType, function(i, v){ }}
                <li data-value="{{= v }}">{{= v }}</li>
                {{# }); }}
            </ul>
        </div>
    </div>
</script>
<script type="text/html" id="approveStatus">
    {{# if(d.status==0){ }}
    <span>待审批</span>
    {{# } else if(d.status==1){ }}
    <span>已通过</span>
    {{# }else if(d.status==2){ }}
    <span>已拒绝</span>
    {{# }else if(d.status==3){ }}
    <span>已撤销</span>
    {{# } }}

</script>
<!-- 指定教室 -->
<script type="text/html" id="selectClassroom">
    <div class="j-search-con single-box" style="width: 110px;">
        <input type="text" name="selClassroom" placeholder="请选择" readonly="" class="schoolSel">
        <span class="j-arrow"></span>
        <div class="j-select-year">
            <div class="search">
                <input type="text" placeholder="搜索" class="classRoom search-fuzzy">
                <span></span>
            </div>
            <ul class="classList">
                {{# layui.each(d.classroom, function(i, v){ }}
                <li data-value="{{=v.jsbh}}" data-type="{{= v.jslx }}" style="display: none;">{{= v.jsmc }}</li>
                {{# }); }}
            </ul>
        </div>
    </div>
</script>
<!-- 周学时安排 -->
<script type="text/html" id="weekHour">
    <div class="weekbox">
        <input type="text" name="weekHour" class="layui-input classIpt weekOpt" placeholder="请输入"
               value="{{d.weekHour}}">
        <span class="set" lay-event="set">设置</span>
    </div>
</script>
<!-- 连排节次 -->
<script type="text/html" id="festivals">
    <input type="number" name="festivals" class="layui-input classIpt festivals" min="0" placeholder="请输入"
           value="{{d.festivals}}">
</script>
<!-- 助教 -->
<script type="text/html" id="selectAssistant">
    <div class="j-search-con  multiple-box" style="width: 110px;">
        <input type="text" name="selAssistant" placeholder="请选择" readonly="" class="schoolSel">
        <span class="j-arrow"></span>
        <div class="j-select-year">
            <div class="search">
                <input type="text" placeholder="搜索" class="teacherSearch">
                <span></span>
            </div>
            <ul class="assistantList">
                {{# layui.each(d.assistant, function(i, v){ }}
                <li data-puid="{{= v.puid }} " data-uname = "{{= v.uname }}" data-no = "{{= d.selAssistantNo }}">{{= v.uname }}</li>
                {{# }); }}
            </ul>
        </div>
    </div>
</script>
<!-- 教学班组成 -->
<script type="text/html" id="selectCompose">
    {{# if(d.selTeachClassroomType === '子教学班'){ }}
    <div class="j-search-con multiple-box" style="width: 100%;">
        <input type="text" isRequire="true" name="selCompose" placeholder="请选择"  readonly="" class="schoolSel">
        <span class="j-arrow"></span>
        <div class="j-select-year">
            <ul class="composeList">
                {{#
                    var composeArray = [];
                    var composeCodeArray = [];
                    if(typeof d.compose === 'string' && d.compose) {
                        composeArray = d.compose.split(',');
                        composeCodeArray = d.composeNo ? d.composeNo.split(',') : [];
                    } else if(Array.isArray(d.compose)) {
                        composeArray = d.compose;
                        composeCodeArray = d.composeCode || [];
                    }
                    layui.each(composeArray, function(i, v){
                }}
                <li data-value="{{= composeCodeArray[i] || '' }}">{{= v }} </li>
                {{#  }); }}
            </ul>
        </div>
    </div>
    {{# } else { }}
    <span>{{= d.compose}}</span>
    {{# } }}
</script>
<!-- 学时类型 -->
<script type="text/html" id="selectHourType">
    <div class="j-search-con single-box " style="width: 110px;">
        <input type="text" isRequire="true" name="selHourType" placeholder="请选择"  readonly="" class="schoolSel">
        <span class="j-arrow"></span>
        <div class="j-select-year">
            <ul class="classType">
                {{#  layui.each(d.hourType, function(i, v){ }}
                <li data-value="{{= v }}">{{= v }} </li>
                {{#  }); }}
            </ul>
        </div>
    </div>
</script>
<!-- 教学班类型 -->
<script type="text/html" id="selectTeachClassroomType">
    <div class="j-search-con single-box " style="width: 110px;">
        <input type="text" isRequire="true" name="selTeachClassroomType" placeholder="请选择"  readonly="" class="schoolSel">
        <span class="j-arrow"></span>
        <div class="j-select-year">
            <ul class="classType">
                <li data-value="父教学班">父教学班</li>
                <li data-value="子教学班">子教学班</li>
            </ul>
        </div>
    </div>
</script>
<!-- 选择父教学班 -->
<script type="text/html" id="selectParentClassroom">
    <div class="j-search-con single-box " style="width: 110px;">
        <input type="text" name="selParentClassroom" placeholder="请选择"  readonly="" class="schoolSel">
        <span class="j-arrow"></span>
        <div class="j-select-year">
            <div class="search">
                <input type="text" placeholder="搜索">
                <span></span>
            </div>
            <ul class="parentClassroom">
                {{#  layui.each(d.parentClassroom, function(i, v){ }}
                <li data-value="{{= v.teachClassNo }}">{{= v.teachClassName }} </li>
                {{#  }); }}
            </ul>
        </div>
    </div>
</script>
<!-- 是否排课 -->
<script type="text/html" id="isArrange">
    <div class="j-search-con single-box " style="width: 110px;">
        <input type="text" th:value="${teachPlan?.jxjhgl_zxsbq != 'w' ? '是' : '否'}" name="isArrange" isRequire="true" placeholder="请选择"  readonly="" class="schoolSel">
        <span class="j-arrow"></span>
        <div class="j-select-year">
            <ul class="classType" id="course">
                <li data-value="0" th:class="${teachPlan?.jxjhgl_zxsbq != 'w'} ? 'active' : ''">是</li>
                <li data-value="1" th:class="${teachPlan?.jxjhgl_zxsbq == 'w'} ? 'active' : ''">否</li>
            </ul>
        </div>
    </div>
</script>
<!-- 是否选课 -->
<script type="text/html" id="isArrange1">
    <div class="j-search-con single-box " style="width: 110px;">
        <input type="text" th:value="${teachPlan?.jxjhgl_xbx == '选修' ? '是' : (teachPlan?.jxjhgl_xbx == '必修' ? '否' : '')}" name="isArrange1" isRequire="true" placeholder="请选择"  readonly="" class="schoolSel">
        <span class="j-arrow"></span>
        <div class="j-select-year">
            <ul class="classType" id="selectCourse">
                <li data-value="0" th:class="${teachPlan?.jxjhgl_xbx == '选修'} ? 'active' : ''">是</li>
                <li data-value="1" th:class="${teachPlan?.jxjhgl_xbx == '必修'} ? 'active' : ''">否</li>
            </ul>
        </div>
    </div>
</script>
<!-- 是否排考 -->
<script type="text/html" id="isArrange2">
    <div class="j-search-con single-box " style="width: 110px;">
        <input type="text" th:value="${teachPlan?.jxjhgl_ksxs == '考试' ? '是' : (teachPlan?.jxjhgl_ksxs == '考查' ? '否' : '')}" name="isArrange2" isRequire="true" placeholder="请选择"  readonly="" class="schoolSel">
        <span class="j-arrow"></span>
        <div class="j-select-year">
            <ul class="classType" id="exam">
                <li data-value="0" th:class="${teachPlan?.jxjhgl_ksxs == '考试'} ? 'active' : ''">是</li>
                <li data-value="1" th:class="${teachPlan?.jxjhgl_ksxs == '考查'} ? 'active' : ''">否</li>
            </ul>
        </div>
    </div>
</script>
<!-- 是否录入成绩 -->
<script type="text/html" id="isArrange3">
    <div class="j-search-con single-box " style="width: 110px;">
        <input type="text" value = "是" name="isArrange3" isRequire="true" placeholder="请选择"  readonly="" class="schoolSel">
        <span class="j-arrow"></span>
        <div class="j-select-year">
            <ul class="classType" id="score">
                <li data-value="0" class="active">是</li>
                <li data-value="1">否</li>
            </ul>
        </div>
    </div>
</script>
<script th:inline="javascript">
    document.domain = "chaoxing.com";
    const _VR_ = [[${_VR_}]];
    var fid = [[${fid}]]
    var uid = [[${uid}]]
    var formUserId = [[${formUserId}]]
    var semester = [[${teachPlan?.jxjhgl_xnxq}]];
    var totalClassHour = [[${teachPlan?.jxjhgl_zxs}]];
    var teachPlan = [[${teachPlan}]];
    let startWeek = [[${semester?.xnxq_qsz}]];
    let endWeek = [[${semester?.xnxq_jsz}]];
</script>
<script th:src="@{~/js/jquery-3.3.1.min.js}"></script>
<script th:src="${_CPR_+_VR_+'/js/my.util.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/cultivation/slideCommon2.js'}"></script>
<script th:src="@{${_CPR_+_VR_}+'/js/cultivation/course.js'(v=${new java.util.Date().getTime()})}"></script>
<script th:inline="javascript">
    //
    // var page1=1;
    // //滚动加载
    // $('.z-classroom .con-list').scroll(function () {
    //     if ($(this).scrollTop() + $(this).innerHeight() >= $(this)[0].scrollHeight - 20) {
    //         if (!classroom) {
    //             classroom = true;
    //             $('#load-more-button-classroom').show();
    //             // 这里调用你的加载更多内容的函数
    //             page1 += 1
    //             getClassroom(kw1)
    //         }
    //     }
    // });

    //
    $(".j-table ").on("click", " .classType li", function () {
        var classType = $(this).html();
        var classList = $(this).parents("tr").find(".classList").eq(0);
        classList.children("li").each((index, item) => {
            var type = $(item).attr("data-type");
            if (type == classType) {
                $(item).show();
            } else {
                $(item).hide();
            }
        })
    })
    //
    // $('.j-table ').on('keypress', ".teacher", function (event) {
    //     if (event.keyCode == "13") {
    //         var tempValue = $(this).val();
    //
    //         $(".teacherList").html("");
    //         getTeacherInfoData(this, tempValue, 1);
    //     }
    // })
    //
    // $('.j-table').on('keypress', ".classRoom", function (event) {
    //     if (event.keyCode == "13") {
    //
    //         var tempValue = $(this).val();
    //         $(".classList").html("");
    //         getClassRoomInfoData(this, tempValue, 1);
    //     }
    // })
    // $('.j-table').on('keypress', ".assistant", function (event) {
    //     if (event.keyCode == "13") {
    //         var tempValue = $(this).val();
    //         $(".assistantList").html("");
    //         getAssistantData(this, tempValue, 1);
    //     }
    // })

</script>

</html>