<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>配课</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/global.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/plugin/layui/css/layui2.8.2.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/slideCommon.css'}"/>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/teachPlan.css'}"/>
</head>
<body>
<!-- 添加课程 -->
<div id="addCourse" class="dialog">
    <div style="height: 54px;"></div>
    <div class="dialog-con">
        <form action="" class="layui-form form-course">
            <div class="layui-inline-wrap">
                <div class="layui-inline">
                    <label class="layui-form-label">开课学期</label>
                    <div class="j-search-con multiple-box">
                        <input
                                type="text"
                                name="pk_kkxq"
                                placeholder="请选择"
                                readonly=""
                                class="schoolSel"
                        />
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索"/>
                                <span></span>
                            </div>
                            <ul name="kkxxb_kkxq" formAlias="kkxxb">
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">课程名称</label>
                    <div class="j-search-con multiple-box">
                        <input
                                type="text"
                                name="pk_kcmc"
                                placeholder="请选择"
                                readonly=""
                                class="schoolSel"
                        />
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索"/>
                                <span></span>
                            </div>
                            <ul name="kck_kcmc" formAlias="kck">
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">教学班名称</label>
                    <div class="j-search-con multiple-box">
                        <input
                                type="text"
                                name="pk_jxbmc"
                                placeholder="请选择"
                                readonly=""
                                class="schoolSel"
                        />
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索"/>
                                <span></span>
                            </div>
                            <ul name="kkxxb_jxbmc" formAlias="kkxxb">
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">配课状态</label>
                    <div class="j-search-con multiple-box">
                        <input
                                type="text"
                                name="pk_pkzt"
                                placeholder="请选择"
                                readonly=""
                                class="schoolSel"
                        />
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索"/>
                                <span></span>
                            </div>
                            <ul name="kkxxb_pkzt">
                                <li value="未配课">未配课</li>
                                <li value="部分配课">部分配课</li>
                                <li value="完成配课">完成配课</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">是否本专业课程</label>
                    <div class="j-search-con single-box">
                        <input
                                type="text"
                                name="pk_sfbzyrw"
                                placeholder="请选择"
                                readonly=""
                                class="schoolSel"
                        />
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <ul name="kkxxb_zykc">
                                <li value="是">是</li>
                                <li value="否">否</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">授课教师</label>
                    <div class="j-search-con multiple-box">
                        <input
                                type="text"
                                name="pk_skjs"
                                placeholder="请选择"
                                readonly=""
                                class="schoolSel"
                        />
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索"/>
                                <span></span>
                            </div>
                            <ul name="jsjbxx_xm" formAlias="jsjbxx">
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">教学班组成</label>
                    <div class="j-search-con multiple-box">
                        <input
                                type="text"
                                name="pk_jxbzc"
                                placeholder="请选择"
                                readonly=""
                                class="schoolSel"
                        />
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索"/>
                                <span></span>
                            </div>
                            <ul name="bjxx_bjmc" formAlias="bjxx">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-inline layui-inline-button">
                <button type="submit" class="layui-btn" lay-submit lay-filter="selTable">
                    筛选
                </button>
                <button type="reset" class="layui-btn layui-btn-primary">
                    清空
                </button>
            </div>
        </form>
        <div class="courseList">
            <table
                    class="layui-hide"
                    id="courseList"
                    lay-filter="courseList"
            ></table>
            <div class="selCourse" id="selCourse">
                <div class="z-check">
                    <span class="check" id="checkAllCourse"></span>选择全部数据
                </div>
                <span>共<i></i>条，已选<em>0</em>条</span>
            </div>
            <div class="refresh">刷新</div>
        </div>
    </div>
    <div class="dialog-footer">
        <button id="cancelBtn">取消</button>
        <button id="sureBtn">确定</button>
    </div>
</div>
</body>
<script th:src="${_CPR_+_VR_+'/plugin/layui/layui2.8.2.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/cultivation/slideCommon.js'}"></script>
<script th:inline="javascript">
    const _VR_ = [[${_VR_}]];
    const queryId = [[${formTopBtnVO.queryId}]];
    const selectTotal = [[${formTopBtnVO.selectTotal}]];
    const uid = [[${formTopBtnVO.uid}]];
    const fid = [[${formTopBtnVO.fid}]];
    const formId = [[${formTopBtnVO.formId}]];
    layui.use(
        ["table", "jquery", "util", "form", "layer", "upload"],
        function () {
            const util = layui.util,
                form = layui.form,
                layer = layui.layer,
                table = layui.table,
                $ = layui.jquery;
            // 渲染
            table.render({
                elem: "#courseList",
                url: _VR_ + '/cultivation/getCourseAllocationData',
                height: 471,
                page: {
                    prev: "上一页",
                    next: "下一页",
                    limit: 10,
                    layout: ["prev", "page", "next", "skip", "limit"],
                },
                cols: [
                    [
                        {
                            type: "checkbox",
                            fixed: "left",
                            width: 80,
                        },

                        {
                            field: "pk_kkxq",
                            title: "开课学期",
                            align: "center",
                            minWidth: 144,
                        },
                        {
                            field: "pk_jxbmc",
                            title: "教学班名称",
                            minWidth: 164,
                            align: "center",
                        },
                        {
                            field: "pk_kkxy",
                            title: "开课学院",
                            align: "center",
                            minWidth: 154,
                        },
                        {
                            field: "pk_kcmc",
                            title: "课程名称",
                            align: "center",
                            minWidth: 90,
                        },
                        {
                            field: "pk_kcxz",
                            title: "课程性质",
                            align: "center",
                            minWidth: 80,
                        },
                        {
                            field: "pk_xf",
                            title: "学分",
                            align: "center",
                            minWidth: 80,
                        },
                        {
                            field: "pk_skjslxr",
                            title: "授课教师",
                            align: "center",
                            minWidth: 144,
                            templet: function (d) {
                                return d.pk_skjslxr
                                    ? d.pk_skjslxr.map(i => i.uname).join(',')
                                    : "";
                            }
                        },
                        {
                            field: "pk_rnrs",
                            title: "容纳人数",
                            align: "center",
                            minWidth: 104,
                        },
                        {
                            field: "pk_yprs",
                            title: "已配人数",
                            align: "center",
                            minWidth: 104,
                        },
                        {
                            field: "pk_pkzt",
                            title: "配课状态",
                            align: "center",
                            minWidth: 164,
                        },
                        {
                            field: "pk_skdd",
                            title: "上课地点",
                            align: "center",
                            minWidth: 164,
                        },
                        {
                            field: "kkxxb_sksj",
                            title: "上课时间",
                            align: "center",
                            minWidth: 174,
                            templet: function (d) {
                                return d.pk_skxq + d.pk_skjc;
                            }
                        },
                        {
                            field: "pk_jxbzc",
                            title: "教学班组成",
                            align: "center",
                            minWidth: 164,
                        },
                    ],
                ],
                parseData: function (res) {
                    if (res.status) {
                        return {
                            "code": 0, //解析接口状态
                            "msg": res.msg, //解析提示文本
                            "count": res.data.totalCount, //解析数据长度
                            "data": res.data.data //解析数据列表
                        }
                    } else {
                        return {
                            "code": 1, //解析接口状态
                            "msg": "数据获取失败", //解析提示文本
                            "count": 0, //解析数据长度
                            "data": [] //解析数据列表
                        }
                    }
                },
                done: function (res, curr, count) {
                    courseTotal = res.count;
                    $("#selCourse i").text(courseTotal);
                    let table_data = res.data;
                    if (isSelAll) {
                        $('div[lay-id="courseList"] input[type=checkbox]').prop("checked", true);
                        $('div[lay-id="courseList"] .layui-form-checkbox').addClass("layui-form-checked");
                        if (cancelSelCourseAry.length > 0) {
                            cancelSelCourseAry.forEach((item, index) => {
                                var selIndex = table_data.findIndex(
                                    (item1) => item1.pk_jxbbh == item.pk_jxbbh
                                );
                                $('div[lay-id="courseList"] thead input[type=checkbox]').prop("checked", false);
                                $('div[lay-id="courseList"] thead .layui-form-checkbox').removeClass("layui-form-checked");
                                if (selIndex > -1) {
                                    var tr = $('div[lay-id="courseList"] .layui-table-body tbody tr').eq(selIndex);
                                    tr.find("input[type=checkbox]").prop("checked", false);
                                    tr.find(".layui-form-checkbox").removeClass("layui-form-checked");
                                }
                            });
                        }
                    } else {
                        selCourseAry.forEach((item, index) => {
                            var selIndex = table_data.findIndex(
                                (item1) => item1.pk_jxbbh == item.pk_jxbbh
                            );
                            $('div[lay-id="courseList"] thead input[type=checkbox]').prop("checked", false);
                            $('div[lay-id="courseList"] thead .layui-form-checkbox').removeClass("layui-form-checked");
                            if (selIndex !== -1) {
                                var tr = $('div[lay-id="courseList"] .layui-table-body tbody tr').eq(selIndex);
                                tr.find("input[type=checkbox]").prop("checked", true);
                                tr.find(".layui-form-checkbox").addClass("layui-form-checked");
                            }
                        });
                    }
                },
            });

            // 筛选
            // 提交事件
            form.on("submit(selTable)", function (data) {
                var field = data.field; // 获取表单字段值
                // 重新加载table
                table.reload('courseList', {where: field, page: {curr: 1}});
                return false; // 阻止默认 form 跳转
            });
            $("#addCourse button[type='reset']").click(function () {
                const field = {};
                field.pk_kkxq = "";
                field.pk_pkzt = "";
                field.pk_kcmc = "";
                field.pk_jxbmc = "";
                field.pk_skjs = "";
                field.pk_jxbzc = "";
                $("#addCourse .active").removeClass("active");
                table.reload('courseList', {where: field, page: {curr: 1}});
            })
            // 选择课程
            var selCourseAry = [],
                cancelSelCourseAry = [],
                courseTotal = 0,
                isSelAll = false; //是否全选;
            table.on("checkbox(courseList)", function (obj) {
                if (obj) {
                    var checkedStatus = obj.checked;
                    if (obj.type == "one") {
                        if (checkedStatus) {
                            selCourseAry.push(obj.data);
                            if (isSelAll) {
                                var selIndex = cancelSelCourseAry.findIndex(
                                    (item) => item.kkxxb_jxbbh == obj.data.kkxxb_jxbbh
                                );
                                if (selIndex > -1) {
                                    cancelSelCourseAry.splice(selIndex, 1);
                                }
                            } else {
                                selCourseAry.push(obj.data);
                            }
                        } else {
                            $("#checkAll").removeClass("checked");
                            if (isSelAll) {
                                cancelSelCourseAry.push(obj.data);
                            } else {
                                var selIndex = selCourseAry.findIndex(
                                    (item) => item.kkxxb_jxbbh == obj.data.kkxxb_jxbbh
                                );
                                if (selIndex > -1) {
                                    selCourseAry.splice(selIndex, 1);
                                }
                            }
                        }
                    } else if (obj.type == "all") {
                        var checkStatus = [];
                        if (checkedStatus) {
                            checkStatus = table.checkStatus("courseList").data;
                        } else {
                            checkStatus = table.getData("courseList");
                        }

                        checkStatus.forEach((element) => {
                            if (isSelAll) {
                                var selCancelIndex = cancelSelCourseAry.findIndex(
                                    (item) => element.kkxxb_jxbbh == item.kkxxb_jxbbh
                                );
                                if (checkedStatus) {
                                    if (selCancelIndex > -1) {
                                        cancelSelCourseAry.splice(selIndex, 1);
                                    }
                                } else {
                                    if (selCancelIndex < 0) {
                                        cancelSelCourseAry.push(element);
                                    }
                                }
                            } else {
                                var selIndex = selCourseAry.findIndex(
                                    (item) => element.kkxxb_jxbbh == item.kkxxb_jxbbh
                                );
                                if (checkedStatus) {
                                    if (selIndex < 0) {
                                        selCourseAry.push(element);
                                    }
                                } else {
                                    if (selIndex > -1) {
                                        selCourseAry.splice(selIndex, 1);
                                    }
                                }
                            }
                        });
                    }
                    if (isSelAll) {
                        if (cancelSelCourseAry.length == 0) {
                            $("#checkAll").addClass("checked");
                            selCourseAry = [];
                            cancelSelCourseAry = [];
                            isSelAll = true;
                            $('div[lay-id="courseList"] thead input[type=checkbox]').prop("checked", true);
                            $('div[lay-id="courseList"] thead .layui-form-checkbox').addClass("layui-form-checked");
                        }
                        var len = courseTotal - cancelSelCourseAry.length;
                        $("#selCourse em").text(len);
                    } else {
                        $("#selCourse em").text(selCourseAry.length);
                    }
                    if ($("#selCourse em").text() == courseTotal) {
                        $("#checkAllCourse").addClass("checked");
                    } else {
                        $("#checkAllCourse").removeClass("checked");
                    }
                }
            });
            // 选择全部
            $("#checkAllCourse").click(function () {
                $(this).toggleClass("checked");
                if ($(this).hasClass("checked")) {
                    $('div[lay-id="courseList"] input[type=checkbox]').prop(
                        "checked",
                        true
                    );
                    $('div[lay-id="courseList"] .layui-form-checkbox').addClass(
                        "layui-form-checked"
                    );
                    isSelAll = true;
                    $("#selCourse em").text(courseTotal);
                } else {
                    $('div[lay-id="courseList"] input[type=checkbox]').prop(
                        "checked",
                        false
                    );
                    $('div[lay-id="courseList"] .layui-form-checkbox').removeClass(
                        "layui-form-checked"
                    );
                    isSelAll = false;
                    $("#selCourse em").text(0);
                }
                form.render("checkbox");
                selCourseAry = [];
                cancelSelCourseAry = [];
            });
            // 确定
            $("#sureBtn").click(function () {
                if (selCourseAry.length === 0) {
                    layer.msg("请选择要分配的课程");
                    return false;
                }
                // 选择的内容
                let teachClassNos = [
                    ...new Set(selCourseAry.map(i => i.pk_jxbbh))
                ].join(',');
                // 表单中关闭弹窗
                location.href = _VR_ + "/api/form-btn/gm/outpost.popup?code=2T10018" +
                    "&teachClassNos=" + teachClassNos +
                    "&queryId=" + queryId +
                    "&selectTotal=" + selectTotal +
                    "&uid=" + uid +
                    "&fid=" + fid +
                    "&formId=" + formId;
            });
            // 取消
            $("#cancelBtn").click(function () {
                // 表单中关闭弹窗
                window.parent.postMessage(JSON.stringify({action: 1}), "*");
            });
            // 刷新
            $(".refresh").click(function () {
                table.reload("courseList");
            });

            $(".j-search-con").on("click", ".schoolSel, .fuzzy-query-input", function (e) {
                let fieldAlias = $(this).parent().find("ul").attr("name");
                let formAlias = $(this).parent().find("ul").attr("formAlias");
                getFormDistinctFiled(fieldAlias, formAlias);
            });

            function getFormDistinctFiled(fieldAlias, formAlias) {
                if ($("ul[name=" + fieldAlias + "] li").length === 0) {
                    let selectHtml = "";
                    $.post(_VR_ + '/teacherIdle/getFormDistinctFiled', {
                        formAlias: formAlias,
                        fieldAlias: fieldAlias
                    }, function (result) {
                        if (result.list) {
                            for (let i = 0; i < result.list.length; i++) {
                                let spanTxt = result.list[i];
                                if (spanTxt) {
                                    selectHtml += "<li>" + spanTxt + "</li>";
                                }
                            }
                            layui.$("ul[name=" + fieldAlias + "]").html(selectHtml);
                        }
                    }, 'json');
                }
            }
        }
    );
</script>
</html>
