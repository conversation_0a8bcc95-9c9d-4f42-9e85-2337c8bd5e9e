<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width,initial-scale=1.0, maximum-scale=1.0, user-scalable=0, viewport-fit=cover">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta content="telephone=no" name="format-detection"/>
    <title>校历</title>
    <script th:src="@{/js/responsive-1.0.js}"></script>
    <link rel="stylesheet" th:href="@{/css/global.css}">
    <link rel="stylesheet" th:href="@{/css/calendar/header.css}">
    <link rel="stylesheet" th:href="@{/css/basic/iosSelect.css}">
    <link rel="stylesheet" th:href="@{/css/calendar/add-schedule.css}">
</head>

<body>
<div class="header">
    <div class="head-signal"></div>
    <div class="head-con">
        <a href="javascript:void(0)" class="head-return">
            <img th:src="@{/images/back-icon.png}" alt="">
        </a>
        <span class="head-title">添加日程</span>
        <span class="head-right" style="display:none;">
                <img th:src="@{/images/add-icons.png}" alt="">
            </span>
    </div>
</div>
<div class="main">
    <div class="lab-list">
        <div class="lable">
            <div class="title">标题</div>
            <div class="input">
                <input type="text" placeholder="请输入" id="scheduleName">
            </div>
        </div>
        <div class="lable">
            <div class="title">地点</div>
            <div class="input">
                <input type="text" placeholder="请输入" id="schedulePlace">
            </div>
        </div>
        <div class="lable">
            <div class="title">日期</div>
            <div class="input disabled">
                <span>2022-09-01</span>
            </div>
        </div>
        <div class="lable lab-switch">
            <div class="title">是否停课</div>
            <div class="switch-wrap">
                <em>否</em>
                <span class="switch close-school" id="suspendStudy"></span>
            </div>
        </div>
        <div class="suspend-classes">
            <div class="lable lab-switch">
                <div class="title">停课时间</div>
                <div class="switch-wrap">
                    <em>全天</em>
                    <span class="switch active tktime"></span>
                </div>
            </div>
            <div class="lable" style="display:none;">
                <div class="title">停课节选范围</div>
                <div class="select-section">
                    <div class="sel">
                        <span>请选择开始节次</span>
                        <em></em>
                    </div>
                    <div class="symbol">-</div>
                    <div class="sel">
                        <span>请选择结束节次</span>
                        <em></em>
                    </div>
                </div>
            </div>
            <div class="lable lab-switch">
                <div class="title">是否补课</div>
                <div class="switch-wrap">
                    <em>是</em>
                    <span class="switch bkIf"></span>
                </div>
            </div>
            <div class="lable sup-lesson-time">
                <div class="title">补课时间</div>
                <div class="inputs" data-year="" data-month="" data-date="" id="selectDate">
                    <span>请选择</span>
                    <em></em>
                </div>
            </div>
            <div class="lable sup-lesson-range">
                <div class="title">补课节选范围</div>
                <div class="select-section">
                    <div class="sel">
                        <span>请选择开始节次</span>
                        <em></em>
                    </div>
                    <div class="symbol">-</div>
                    <div class="sel">
                        <span>请选择结束节次</span>
                        <em></em>
                    </div>
                </div>
            </div>
        </div>

        <div class="lable">
            <div class="title">备注</div>
            <div class="texatarea">
                <div class="kalamu-area" id="textareaaddress" contenteditable="true" placeholder="请输入"></div>
            </div>
        </div>
    </div>

</div>

<div class="school-bottom">
    <div class="handle">
        <div class="btn cancle">取消</div>
        <div class="btn confirm">确定</div>
    </div>
</div>
<div class="markers" style="display: none;"></div>

<div class="bot-window category" id="sectionly">
    <div class="bot-list">
        <ul>
            <li class="">第一节</li>
            <li>第二节</li>
            <li>第三节</li>
            <li>第四节</li>
            <li>第五节</li>
            <li>第六节</li>
            <li>第七节</li>
            <li>第八节</li>
            <li>第九节</li>
            <li>第十节</li>
            <li class="cur">第十一节</li>
        </ul>
    </div>
    <div class="bot-close">取消</div>
</div>
<script th:src="@{/js/jquery-3.3.1.min.js}"></script>
<script th:src="@{/js/CXJSBridge.js}"></script>
<script th:src="@{/js/iosSelect.js}"></script>
<script th:src="@{/js/mobileVersion.js}"></script>
<script th:src="@{/js/calendar/school_calendar_add.js}"></script>
</body>
</html>