<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学生选课</title>
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/global.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/stuSelCourse.css?v=1'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/reset.css'">
    <style>
        .z-main .z-title h3::after {
            height: 27px;
        }
    </style>
</head>

<body>
<div class="z-main">
    <div class="z-title">
        <h3>
            <div class="j-search-con single-box">
                <input type="text" name="teacherName" id="xnxqVal" placeholder="请选择" readonly="" class="schoolSel">
                <span class="j-arrow"></span>
                <div class="j-select-year">
                    <ul name="teacherName" id="pcIndeXxnxqDiv">
                    </ul>
                </div>
            </div>
        </h3>
        <ul id="selType">
            <li class="active">课表显示</li>
            <li>列表显示</li>
        </ul>
    </div>
    <div class="main-con " id="Curriculum">
        <div class="selectBox">
            <div class="selectWeek">
                <div class="prevWeek disabled"></div>
                <span class="week" week='1'>第1周</span>
                <div class="nextWeek"></div>
            </div>
            <div class="selectMask">
                <ul class="selectList"></ul>
            </div>
        </div>
        <div class="z-table">
            <div class="thead">
                <ul>
                    <li><span class="week"></span>
                        <span class="weekdate"></span>
                    </li>
                    <li><span class="week">周一</span>
                        <span class="weekdate" id="week1"></span>
                    </li>
                    <li><span class="week">周二</span>
                        <span class="weekdate" id="week2"></span>
                    </li>
                    <li><span class="week">周三</span>
                        <span class="weekdate" id="week3"></span>
                    </li>
                    <li><span class="week">周四</span>
                        <span class="weekdate" id="week4"></span>
                    </li>
                    <li><span class="week">周五</span>
                        <span class="weekdate" id="week5"></span>
                    </li>
                    <li><span class="week">周六</span>
                        <span class="weekdate" id="week6"></span>
                    </li>
                    <li><span class="week">周日</span>
                        <span class="weekdate" id="week7"></span>
                    </li>

                </ul>
            </div>
            <div class="tbody">
            </div>
        </div>
    </div>
    <div class="main-con" id="list" style="display: none;">
        <div class="z-search">
            <div class="z-item">
                <div class="item-title">开课年级</div>
                <div class="j-search-con multiple-box">
                    <input type="text" name="teacherName" id="njVal2" placeholder="请选择" readonly="" class="schoolSel">
                    <span class="j-arrow"></span>
                    <div class="j-select-year">
                        <div class="search">
                            <input type="text" placeholder="搜索">
                            <span></span>
                        </div>
                        <ul name="teacherName" id="njDiv2">
                            <li data-id="" class="all">全部</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="z-item">
                <div class="item-title">开课院系</div>
                <div class="j-search-con multiple-box">
                    <input type="text" name="teacherName" id="yxVal2" placeholder="请选择" readonly="" class="schoolSel">
                    <span class="j-arrow"></span>
                    <div class="j-select-year">
                        <div class="search">
                            <input type="text" placeholder="搜索">
                            <span></span>
                        </div>
                        <ul name="teacherName" id="yxDiv2">
                            <li data-id="" class="all">全部</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="z-item">
                <div class="item-title">开课专业</div>
                <div class="j-search-con multiple-box">
                    <input type="text" name="teacherName" id="zyVal2" placeholder="请选择" readonly="" class="schoolSel">
                    <span class="j-arrow"></span>
                    <div class="j-select-year">
                        <div class="search">
                            <input type="text" placeholder="搜索">
                            <span></span>
                        </div>
                        <ul name="teacherName" id="zyDiv2">
                            <li data-id="" class="all">全部</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="z-item">
                <div class="item-title">开设课程</div>
                <div class="j-search-con multiple-box">
                    <input type="text" name="teacherName" id="kcVal2" placeholder="请选择" readonly="" class="schoolSel">
                    <span class="j-arrow"></span>
                    <div class="j-select-year">
                        <div class="search">
                            <input type="text" placeholder="搜索">
                            <span></span>
                        </div>
                        <ul name="teacherName" id="kcDiv2">
                            <li data-id="" class="all">全部</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="z-item">
                <div class="item-title">开课校区</div>
                <div class="j-search-con multiple-box">
                    <input type="text" name="teacherName" id="xqVal2" placeholder="请选择" readonly="" class="schoolSel">
                    <span class="j-arrow"></span>
                    <div class="j-select-year">
                        <div class="search">
                            <input type="text" placeholder="搜索">
                            <span></span>
                        </div>
                        <ul name="teacherName" id="xqDiv2">
                            <li data-id="" class="all">全部</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="z-item">
                <div class="item-title">课程类型</div>
                <div class="j-search-con multiple-box">
                    <input type="text" name="teacherName" id="lxVal2" placeholder="请选择" readonly="" class="schoolSel">
                    <span class="j-arrow"></span>
                    <div class="j-select-year">
                        <div class="search">
                            <input type="text" placeholder="搜索">
                            <span></span>
                        </div>
                        <ul name="teacherName" id="lxDiv2">
                            <li data-id="" class="all">全部</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="z-item">
                <div class="item-title">所属选课计划</div>
                <div class="j-search-con multiple-box">
                    <input type="text" name="teacherName" id="jhVal2" placeholder="请选择" readonly="" class="schoolSel">
                    <span class="j-arrow"></span>
                    <div class="j-select-year">
                        <div class="search">
                            <input type="text" placeholder="搜索">
                            <span></span>
                        </div>
                        <ul name="teacherName" id="jhDiv2">
                            <li data-id="" class="all">全部</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="btn-search btn-search2">查询</div>
        </div>
        <div class="z-tab-search">
            <input type="text" placeholder="请输入" id="xxkmc2">
            <img th:src="${_CPR_}+'/elective/images/search-icon.png'" alt="">
        </div>
        <div class="z-list" id="selCourseList">
        </div>
    </div>
</div>
<!-- 进入提示 -->
<div class="dialog-wrap dialog-plan" style="display: none;">
    <div class="dialog ">
        <div class="dialog-con">
            <!--                <div class="plan">-->
            <!--                    <h1>选课计划</h1>-->
            <!--                    <p>2023-08-04 00:00:00~2023-08-10 00:00:00</p>-->
            <!--                    <p>选课说明文字</p>-->
            <!--                </div>-->
            <!--                <div class="plan">-->
            <!--                    <h1>选课计划</h1>-->
            <!--                    <p>2023-08-04 00:00:00~2023-08-10 00:00:00</p>-->
            <!--                    <p>选课说明文字</p>-->
            <!--                </div>-->
        </div>
        <div class="dialog-btn">
            <button class="sure" id="planSel">确定</button>
        </div>
    </div>
</div>
<!-- 可选课程 -->
<div class="dialog-wrap dialog-course">
    <div class="dialog">
        <div class="dialog-title">可选课程</div>
        <div class="dialog-con">
            <div class="z-search">
                <div class="z-item">
                    <div class="item-title">开课年级</div>
                    <div class="j-search-con multiple-box">
                        <input type="text" name="teacherName" id="njVal1" placeholder="请选择" readonly=""
                               class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <ul name="teacherName" id="njDiv1">
                                <li data-id="" class="all">全部</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="z-item">
                    <div class="item-title">开课院系</div>
                    <div class="j-search-con multiple-box">
                        <input type="text" name="teacherName" id="yxVal1" placeholder="请选择" readonly=""
                               class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <ul name="teacherName" id="yxDiv1">
                                <li data-id="" class="all">全部</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="z-item">
                    <div class="item-title">开课专业</div>
                    <div class="j-search-con multiple-box">
                        <input type="text" name="teacherName" id="zyVal1" placeholder="请选择" readonly=""
                               class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <ul name="teacherName" id="zyDiv1">
                                <li data-id="" class="all">全部</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="z-item">
                    <div class="item-title">开设课程</div>
                    <div class="j-search-con multiple-box">
                        <input type="text" name="teacherName" id="kcVal1" placeholder="请选择" readonly=""
                               class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <ul name="teacherName" id="kcDiv1">
                                <li data-id="" class="all">全部</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="z-item">
                    <div class="item-title">开课校区</div>
                    <div class="j-search-con multiple-box">
                        <input type="text" name="teacherName" id="xqVal1" placeholder="请选择" readonly=""
                               class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <ul name="teacherName" id="xqDiv1">
                                <li data-id="" class="all">全部</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="z-item">
                    <div class="item-title">课程类型</div>
                    <div class="j-search-con multiple-box">
                        <input type="text" name="teacherName" id="lxVal1" placeholder="请选择" readonly=""
                               class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <ul name="teacherName" id="lxDiv1">
                                <li data-id="" class="all">全部</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="z-item">
                    <div class="item-title">所属选课计划</div>
                    <div class="j-search-con multiple-box">
                        <input type="text" name="teacherName" id="jhVal1" placeholder="请选择" readonly=""
                               class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <ul name="teacherName" id="jhDiv1">
                                <li data-id="" class="all">全部</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="btn-search btn-search1">查询</div>
            </div>
            <div class="z-tab-search">
                <input type="text" placeholder="请输入">
                <img th:src="${_CPR_}+'/elective/images/search-icon.png'" alt="">
            </div>
            <div class="z-list" id="zList">
            </div>
        </div>
        <div class="dialog-btn">
            <button class="cancel">取消</button>
            <button class="sure" id="courseSel">确定</button>
        </div>
    </div>
</div>
<!-- 详情 -->
<div class="dialog-wrap dialog-mes">
    <div class="dialog">
        <div class="dialog-con">
            <!--                <h3>22数据库</h3>-->
            <!--                <p><span style="width: 103px;">所属选课计划 ：</span>2023-2024-1选课</p>-->
            <!--                <p><span>开设课程 ：</span>数据库数据库数据库数据库数据库数据库数据库数据库数据库数据库数据库</p>-->
            <!--                <p><span>开课年级 ：</span>2022级</p>-->
            <!--                <p><span>开课院系 ：</span>计算机学院</p>-->
            <!--                <p><span>开课专业 ：</span>计算机专业</p>-->
            <!--                <p><span>课程类型 ：</span>专业选修课</p>-->
            <!--                <p><span>上课时间 ：</span>1-20周，周一，第七节</p>-->
            <!--                <p><span>上课地点 ：</span>南校区</p>-->
            <!--                <p><span>授课教师 ：</span>李四</p>-->
            <!--                <p><span>学分 ：</span>2分</p>-->
            <!--                <p><span>课程容量：</span>50</p>-->
            <!--                <p><span>可选性别：</span>全部</p>-->
            <!--                <p><span>编组：</span>计算机组</p>-->
        </div>
        <div class="dialog-btn">
            <button class="cancel">取消</button>
            <button class="sure" id="exitCourse">退课</button>
        </div>
    </div>
</div>
<div id="tipsBox"></div>
<div id="captcha"></div>
</body>
<script th:inline="javascript">
    const _VR_ = [[${_VR_}]] || '';
</script>
<script th:src="${_CPR_}+'/elective/js/jquery-3.3.1.min.js'"></script>
<script th:src="${_CPR_}+'/elective/js/common.js'"></script>
<script th:src="${_CPR_}+'/elective/layui/layui.js'"></script>
<script th:src="${_CPR_}+'/js/my.util.js'"></script>
<script type='text/javascript' src='https://captcha.chaoxing.com/load.min.js?t='></script>
<script th:inline="javascript">
    layui.use(['laypage', 'jquery', 'layer', 'layedit'], function () {
        var laypage = layui.laypage, $ = layui.jquery, layedit = layui.layedit, layer = layui.layer;
        $(document).ready(function () {
            var finaltaskBdid = 0;
            var finaltaskcid = 0;
            var opCourse = 1;
            var captchaIns = null;
            var listtab = 1;
            var yzm = false;
            let r = [[${r}]];
            let fidEnc = [[${fidEnc}]];
            let academicYear = [[${academicYear}]];
            let dates = [[${dates}]];
            var xnxqh = "";
            if (academicYear) {
                xnxqh = academicYear.xnxq_xnxqh;
                if (academicYear.xnxq_xnxqmc) {
                    $("#xnxqVal").val(academicYear.xnxq_xnxqmc);
                } else {
                    $("#xnxqVal").val(academicYear.xnxq_xnxqh);
                }
            }
            let allAcademicYear = [[${allAcademicYear}]];
            if (allAcademicYear) {
                var html = "";
                for (let i = 0; i < allAcademicYear.length; i++) {
                    if (academicYear && academicYear.xnxq_xnxqmc && academicYear.xnxq_xnxqmc == allAcademicYear[i].xnxq_xnxqmc) {
                        html += "<li data-id='" + allAcademicYear[i].xnxq_xnxqh + "' class='all active'>" + allAcademicYear[i].xnxq_xnxqmc + "</li>";
                    } else if (academicYear && academicYear.xnxq_xnxqh && academicYear.xnxq_xnxqh == allAcademicYear[i].xnxq_xnxqh) {
                        html += "<li data-id='" + allAcademicYear[i].xnxq_xnxqh + "' class='all active'>" + allAcademicYear[i].xnxq_xnxqh + "</li>";
                    } else if (allAcademicYear[i].xnxq_xnxqmc) {
                        html += "<li data-id='" + allAcademicYear[i].xnxq_xnxqh + "' class='all '>" + allAcademicYear[i].xnxq_xnxqmc + "</li>";
                    } else {
                        html += "<li data-id='" + allAcademicYear[i].xnxq_xnxqh + "' class='all '>" + allAcademicYear[i].xnxq_xnxqh + "</li>";
                    }
                }
                $("#pcIndeXxnxqDiv").html(html);
            }
            $(".j-search-con.single-box").on("click", ".j-select-year li ", function (e) {
                if ($(this).parent().attr("id") == 'pcIndeXxnxqDiv') {
                    window.location.href = "/elective/task/pc/index?fidEnc=" + fidEnc + "&xnxqh=" + $(this).attr("data-id");
                }
            })
            let fid = [[${fid}]];
            var zc = 1;
            var stuOptional;
            var stuSelectCourseCid = [];
            var jcArr = [];
            initCXCaptcha({
                captchaId: 'dRgtnuKwnxSvXXOl0btdNZqATWH8Kmjv',
                element: '#captcha',
                mode: 'popup',
                // type: 'iconclick',
                onVerify: function (err, data) {
                    /**
                     * 第一个参数是err（Error的实例），验证失败才有err对象
                     * 第二个参数是data对象，验证成功后的相关信息，data数据结构为key-value，如下：
                     * {
                     * validate: 'xxxxx' // 二次验证信息
                     * }
                     **/
                    if (err) return; // 当验证失败时，内部会自动refresh方法，无需手动再调用一次
                    if (opCourse == 1) {
                        electiveCourses(data.validate)
                    } else {
                        dropCourses(data.validate);
                    }
                    captchaIns.refresh()
                }
            }, function onload(instance) {
                captchaIns = instance;
            }, function onerror(err) {
            });

            function yzmdropCourses(taskBdid, cid) {
                opCourse = 2;
                finaltaskBdid = taskBdid;
                finaltaskcid = cid;
                if (captchaIns && yzm) {
                    captchaIns.popUp();
                } else {
                    dropCourses("");
                }
            }

            function yzmelectiveCourses(taskBdid, cid) {
                opCourse = 1;
                finaltaskBdid = taskBdid;
                finaltaskcid = cid;
                if (captchaIns && yzm) {
                    captchaIns.popUp();
                } else {
                    electiveCourses("");
                }
            }

            function electiveCourses(validate) {
                var succ = true;
                U.ajax({
                    type: 'post',
                    url: "../elective/task/elective/courses",
                    data: {
                        fid: fid,
                        taskBdid: finaltaskBdid,
                        cid: finaltaskcid,
                        validate: validate,
                        captchaIns: !!captchaIns
                    },
                    dataType: 'json',
                    async: false,
                    success: function (result) {
                        yzm = result.data;
                        if (result.code !== 200) {
                            layer.msg(result.msg)
                            succ = false;
                            return
                        }
                        if (listtab == 1) {
                            refreshData1()
                            $(".dialog-course").hide();
                        } else {
                            refreshData2()
                        }
                        layer.msg("报名成功")
                    }
                })
                return succ;
            }

            function dropCourses(validate) {
                var succ = true;
                U.ajax({
                    type: 'post',
                    url: "../elective/task/drop/courses",
                    data: {
                        fid: fid,
                        taskBdid: finaltaskBdid,
                        cid: finaltaskcid,
                        validate: validate,
                        captchaIns: !!captchaIns
                    },
                    dataType: 'json',
                    async: false,
                    success: function (result) {
                        if (result.code !== 200) {
                            if ('不在计划时间范围内报名不允许退课' == result.msg) {
                                //姓名 tksqb_xm
                                //学号 tksqb_xh  0009
                                //uid tksqb_uid
                                //所属退课计划 13
                                //退选选修课名称 tksqb_txxxkmc
                                var param = {
                                    'data': [
                                        {
                                            'alias': 'tksqb_xm',
                                            'val': ['' + result.data.tksqb_xm + ''],
                                            'compt': 'editinput'
                                        }
                                        , {
                                            'alias': 'tksqb_xh',
                                            'val': ['' + result.data.tksqb_xh + ''],
                                            'compt': 'editinput'
                                        }
                                        , {
                                            'alias': 'tksqb_uid',
                                            'val': ['' + result.data.tksqb_uid + ''],
                                            'compt': 'editinput'
                                        }
                                        , {'alias': '13', 'val': ['' + result.data.taskname + ''], 'compt': 'editinput'}
                                        , {
                                            'alias': 'tksqb_txxxkmc',
                                            'val': ['' + result.data.tksqb_txxxkmc + ''],
                                            'compt': 'editinput'
                                        }
                                    ]
                                }
                                // 创建表单
                                var temp_form = document.createElement('form')
                                // 填写表单数据
                                temp_form.action = result.data.url
                                temp_form.target = '_blank'
                                temp_form.method = 'post'
                                temp_form.style.display = 'none'
                                // 添加参数
                                var opt = document.createElement('textarea')
                                opt.name = 'precast'
                                opt.value = JSON.stringify(param)
                                temp_form.appendChild(opt)
                                document.body.appendChild(temp_form)
                                // 提交数据
                                temp_form.submit()
                                return
                            }
                            yzm = result.data;
                            layer.msg(result.msg)
                            succ = false;
                            return
                        }
                        layer.msg("退课成功")
                        yzm = result.data;
                        if (listtab == 1) {
                            refreshData1()
                            $(".dialog-mes").hide();
                        } else {
                            refreshData2()
                            $(".dialog-mes").hide();
                        }
                    }
                })
                return succ;
            }

            U.ajax({
                type: 'post',
                url: "../elective/task/getTask",
                data: {fid: fid, xnxqh: xnxqh},
                dataType: 'json',
                async: false,
                success: function (result) {
                    if (result.code !== 200 || !result.data || result.data.length === 0) {
                        return false;
                    }
                    var html = "";
                    if (result.data.length == 0) {
                        html += "<div class='plan'>";
                        html += "<h1>当前无选课计划</h1>";
                        html += "</div>";
                        $(".dialog-plan .dialog .dialog-con").html(html);
                        $(".dialog-plan").show();
                        return;
                    }
                    var sxhtml = "";//筛选计划
                    for (let i = 0; i < result.data.length; i++) {
                        html += "<div class='plan'>";
                        html += "<h1>" + result.data[i].xkjhbJhmc + "</h1>";
                        html += "<p>" + result.data[i].xkjhbKssj + "~" + result.data[i].xkjhbJssj + "</p>";
                        html += "<p>" + result.data[i].xkjhbXksm + "</p>";
                        html += "</div>";
                        sxhtml += "<li data-id=\"" + result.data[i].taskBdid + "\">" + result.data[i].xkjhbJhmc + "</li>";
                    }
                    $("#jhDiv1").append(sxhtml);
                    $("#jhDiv2").append(sxhtml);
                    $(".dialog-plan .dialog .dialog-con").html(html);
                    $(".dialog-plan").show();
                }
            })

            function refreshData2() {
                optionalCourse()
                optional_courses(2);
            }

            function refreshData1() {
                optionalCourse()
                stuOptionalCou()
                // optional_courses(1);
            }

            function optionalCourse() {
                U.ajax({
                    type: 'post',
                    url: "../elective/task/optional/course",
                    data: {fid: fid, xnxqh: xnxqh},
                    dataType: 'json',
                    async: false,
                    success: function (result) {
                        if (result.code !== 200) {
                            return false;
                        }
                        if (!result.data) {
                            $(".dialog-plan").hide();
                            return false;
                        }
                        stuSelectCourseCid = [];
                        stuOptional = result.data;
                        if (stuOptional) {
                            for (let z = 0; z < stuOptional.length; z++) {
                                var selected_course = $.parseJSON(stuOptional[z].selectedCourse);
                                for (let y = 0; y < selected_course.length; y++) {
                                    stuSelectCourseCid.push(selected_course[y].cid)
                                }
                            }

                        }
                    }
                })
            }

            if (r) {
                if (r.code !== 200) {
                    layer.msg(r.msg);
                } else {
                    optionalCourse()
                    stuOptionalCou();
                }
            } else {
                layer.msg("课表数据异常");
            }

            function stuOptionalCou() {
                var djj = 0; //第几节课
                var html = "";
                for (let i = 0; i < r.data.lessons.length; i++) {
                    jcArr[r.data.lessons[i][0].actualNum] = r.data.lessons[i][0].lessonNumName;
                    html += "<ul>";
                    html += "<li><span class='section'> " + r.data.lessons[i][0].lessonNumName + "</span>";
                    html += " <span class='time'>" + r.data.lessons[i][0].begin + "-" + r.data.lessons[i][0].end + "</span>";
                    html += "</li>";
                    if (r.data.lessons[i][0].period === 2 || r.data.lessons[i][0].period === 3 || r.data.lessons[i][0].period === 1 || r.data.lessons[i][0].period === 4) {
                        djj++;
                    }
                    for (let j = 1; j <= 7; j++) {
                        if (r.data.lessons[i][0].period !== 2 && r.data.lessons[i][0].period !== 3 && r.data.lessons[i][0].period !== 1 && r.data.lessons[i][0].period !== 4) {
                            html += "<li></li>";
                            continue;
                        }
                        if (!stuOptional) {
                            html += "<li></li>";
                            continue;
                        }
                        var xknum = 0; //0灰色
                        var xkfalse = true;
                        var temphtml = "";
                        one :for (let z = 0; z < stuOptional.length; z++) {
                            var selected_course = $.parseJSON(stuOptional[z].selectedCourse);
                            var optional_course = $.parseJSON(stuOptional[z].optionalCourse);
                            if (selected_course.length > 0) {
                                two: for (let y = 0; y < selected_course.length; y++) {
                                    var allSj = selected_course[y].allSj;
                                    for (let l = 0; l < allSj.length; l++) {
                                        if (allSj[l] == (zc + ',' + j + ',' + djj)) {
                                            temphtml = "<li class='selCourse' djj = '" + djj + "' zc = '" + j + "'  cid='" + selected_course[y].cid + "'>" + selected_course[y].kcmc + "</li>";
                                            xkfalse = false;
                                            break one;
                                        }
                                    }
                                }
                            }
                            if (optional_course.length > 0) {
                                two: for (let y = 0; y < optional_course.length; y++) {
                                    var allSj = optional_course[y].allSj;
                                    for (let l = 0; l < allSj.length; l++) {
                                        if (allSj[l] == (zc + ',' + j + ',' + djj)) {
                                            temphtml = "<li class='selCourse' djj = '" + djj + "' zc = '" + j + "'>待选课</li>";
                                            xkfalse = false;
                                            break two;
                                        }
                                    }
                                }
                            }
                            if (xkfalse) {
                                temphtml = "<li ></li>";
                            }
                        }
                        if (xkfalse) {
                            temphtml = "<li ></li>";
                        }
                        html += temphtml;
                    }
                    html += "</ul>";
                }
                $(".tbody").html(html);
            }

            // 切换显示形式
            $("#selType li").click(function () {
                var idx = $(this).index();
                $(this).addClass('active').siblings().removeClass('active');
                if (idx == 0) {
                    $("#Curriculum").show();
                    $("#list").hide();
                    refreshData1()
                    listtab = 1;
                } else {
                    $("#Curriculum").hide();
                    $("#list").show();
                    refreshData2()
                    listtab = 2;
                }
            })
            //初始化select列表
            initSelectlist();

            function initSelectlist() {
                if (!academicYear) {
                    return;
                }
                var selectHtml = '';
                for (var i = 1; i <= Number(academicYear.xnxq_jsz); i++) {
                    selectHtml += '<li><p>' + i + '</p></li>';
                }
                $('.selectList').html(selectHtml);
                $('.selectList li:first p').addClass('active');
            }

            zcdate();
            //课表顶部点击第几周
            $('.selectMask').on('click', '.selectList li p', function (e) {
                if (!academicYear) {
                    return;
                }
                e.stopPropagation();
                $('.selectMask .selectList li p').removeClass('active');
                $(this).addClass('active');
                var newweek = parseInt($(this).text());
                $('.selectMask').hide();
                $('.selectWeek span.week').html('第' + newweek + '周').attr('week', newweek);
                $('.nextWeek').removeClass('disabled');
                $('.prevWeek').removeClass('disabled');
                if (newweek == 1) {
                    $('.prevWeek').addClass('disabled');
                } else if (newweek == Number(academicYear.xnxq_jsz)) {
                    $('.nextWeek').addClass('disabled');
                }
                zc = newweek;
                zcdate();
                stuOptionalCou();
            })
            //上一周
            $('.prevWeek').click(function () {
                var week = $('.selectWeek span').attr('week');
                var newweek;
                if (week > 1) {
                    newweek = parseInt(week) - 1;
                    $('.selectWeek span.week').html('第' + newweek + '周').attr('week', newweek);
                    zc = newweek;
                    zcdate();
                    stuOptionalCou();
                }
                $('.nextWeek').removeClass('disabled');
                if (newweek == 1) {
                    $(this).addClass('disabled');
                }
            })
            //下一周
            $('.nextWeek').click(function () {
                if (!academicYear) {
                    return;
                }
                var week = $('.selectWeek span').attr('week');
                var newweek;
                if (week < Number(academicYear.xnxq_jsz)) {
                    newweek = parseInt(week) + 1;
                    $('.selectWeek span.week').html('第' + newweek + '周').attr('week', newweek);
                    zc = newweek;
                    zcdate();
                    stuOptionalCou();
                }
                $('.prevWeek').removeClass('disabled');
                if (newweek == Number(academicYear.xnxq_jsz)) {
                    $(this).addClass('disabled');
                }
            })

            function zcdate() {
                var date = dates[zc];
                let split = date.split(",");
                for (let i = 1; i <= split.length; i++) {
                    $("#week" + i).text(split[i - 1]);
                }
            }

            $('.selectWeek .week').click(function () {
                $('.selectMask').show();
            })
            $('.selectMask').click(function () {
                $(this).hide();
            })
            $('.selectMask').on('click', 'ul', function (e) {
                e.stopPropagation();
            })

            // 选课计划确定
            $("#planSel").click(function () {
                $(".dialog-plan").hide();
            })
            // 点击待选课
            var selCourseEle;
            $(".tbody").on('click', '.selCourse', function () {
                selCourseEle = $(this);
                if ($(this).text() == '待选课') {
                    $(".dialog-course").show();
                    optional_courses(1);
                } else {
                    $(".dialog-mes").show();
                    courseDetail($(this).attr("cid"))
                }
            })
            var taskBdid = "";
            var cid = "";
            // 选择课程
            $("#zList").on('click', '.z-course:not(".z-full")', function () {
                taskBdid = $(this).attr("taskBdid");
                cid = $(this).attr("id");
                $(this).addClass('active').siblings().removeClass('active');
            })
            $(".cancel").click(function () {
                $(".dialog-wrap").hide();
            })
            // 课程确定
            $("#courseSel").click(function () {
                if ($(".z-course.active").length > 0) {
                    // var courseTxt = $(".z-course.active h3").text();
                    // selCourseEle.text(courseTxt);
                    // $(".dialog-course").hide();
                    if (!taskBdid || !cid) {
                        $("#tipsBox").text('异常').fadeIn().delay(1000).fadeOut();
                        return false;
                    }
                    opcid = cid;
                    yzmelectiveCourses(taskBdid, cid);
                    // if (succ) {
                    // var courseTxt = $("#zList .z-course.active h3").text();
                    // selCourseEle.text(courseTxt);
                    // $(".dialog-course").hide();
                    // refreshData1()
                    // }
                } else {
                    $("#tipsBox").text('请选择课程').fadeIn().delay(1000).fadeOut();
                }

            });

            // 点击详情
            var detailEle;
            // $(".btn-detail").click(function () {
            $("body").on('click', '.btn-detail', function () {
                detailEle = $(this);
                // $(".dialog-mes").show();
                courseDetail($(this).attr("cid"));
            })
            var opcid;
            // 退课
            $("#exitCourse").click(function () {
                var taskBdid = $("#" + opcid + "").attr("taskBdid");
                yzmdropCourses(taskBdid, opcid);
                // if (succ) {
                //     $(".dialog-mes").hide();
                // refreshData2();
                // refreshData1();
                // }
            })

            // 列表选择课程
            $("#selCourseList").on('click', '.z-course .btn-sel', function () {
                var taskBdid = $(this).attr("taskBdid");
                var cid = $(this).attr("cid");
                opcid = cid;
                yzmelectiveCourses(taskBdid, cid);
                // if (succ) {
                //     refreshData2()
                // }
            })
            // 退课
            $("#selCourseList").on('click', '.z-course .btn-exit', function () {
                var taskBdid = $(this).attr("taskBdid");
                var cid = $(this).attr("cid");
                opcid = cid;
                yzmdropCourses(taskBdid, cid);
                // if (succ) {
                //     refreshData2()
                // }
            })

            function courseDetail(cid) {
                // if (opcid === cid) {
                //     $(".dialog-mes").show();
                //     return false;
                // }
                opcid = cid;
                U.ajax({
                    type: 'post',
                    url: "../elective/task/course/detail",
                    data: {fid: fid, cid: cid},
                    dataType: 'json',
                    async: false,
                    success: function (result) {
                        if (result.code !== 200) {
                            layer.msg(result.msg)
                            return false;
                        }
                        var html = "";
                        var d = result.data;
                        html += "<h3 id='" + d.id + "'  taskBdid = '" + d.taskBdid + "'>" + d.xxkmc + "</h3>";
                        html += "<p><span style='width: 103px;'>所属选课计划 ：</span>" + d.xxkmc + "</p>";
                        html += "<p><span>开设课程 ：</span>" + d.xxkXxkmc + "</p>";
                        html += "<p><span>开课年级 ：</span>" + d.xxkKknj + "</p>";
                        html += " <p><span>开课院系 ：</span>" + d.xxkKkyx + "</p>";
                        html += "<p><span>开课专业 ：</span>" + d.xxkKkzy + "</p>";
                        html += " <p><span>课程类型 ：</span>" + d.xxkKclx + "</p>";
                        html += " <p><span>上课时间 ：</span>" + getsksjStr(d.xxkSksj) + "</p>";
                        html += "<p><span>上课地点 ：</span>" + d.xxkSkdd + "</p>";
                        html += "<p><span>授课教师 ：</span>" + d.xxkSkjs + "</p>";
                        html += "<p><span>学分 ：</span>" + d.xxkXxkxf + "分</p>";
                        if (d.xxkXxkrl == -1) {
                            html += "<p><span>课程容量：</span>无上限</p>";
                        } else {
                            html += "<p><span>课程容量：</span>" + d.xxkXxkrl + "</p>";
                        }
                        html += "<p><span>可选性别：</span>" + d.xxkKxxsxb + "</p>";
                        html += "<p><span>编组：</span>" + d.courseGroupName + "</p>";
                        $(".dialog-mes .dialog .dialog-con").html(html);
                        $(".dialog-mes").show();
                    }
                })
            }

            $(".btn-search1").on('click', function () {
                optional_courses(1);
            })
            $("#xxkmc1").keyup(function (e) {
                if (e.keyCode == 13) {
                    optional_courses(1);
                }
            })

            $(".btn-search2").on('click', function () {
                optional_courses(2);
            })
            $("#xxkmc2").keyup(function (e) {
                if (e.keyCode == 13) {
                    optional_courses(2);
                }
            })
            //开课年级 xxk_kknj
            //开课院系 xxk_kkyx
            //开课专业  xxk_kkzy
            //开设课程  kskc
            //开课校区  xxk_kkxiaoqu
            //课程类型  xxk_kclx
            //选修课名称  xxk_xxkmc
            function optional_courses(divv) {
                var xxk_kknj = '';
                var xxk_kkyx = '';
                var xxk_kkzy = '';
                var kskc = '';
                var xxk_kkxiaoqu = '';
                var xxk_kclx = '';
                var xxk_xxkmc = '';
                var jc = '';
                var jh = '';
                if (divv === 2) {
                    xxk_kknj = $("#njVal2").val();
                    xxk_kkyx = $("#yxVal2").val();
                    xxk_kkzy = $("#zyVal2").val();
                    kskc = $("#kcVal2").val();
                    xxk_kkxiaoqu = $("#xqVal2").val();
                    xxk_kclx = $("#lxVal2").val();
                    xxk_xxkmc = $("#xxkmc2").val();
                    jh = $("#jhVal2").val();
                } else {
                    xxk_kknj = $("#njVal1").val();
                    xxk_kkyx = $("#yxVal1").val();
                    xxk_kkzy = $("#zyVal1").val();
                    kskc = $("#kcVal1").val();
                    xxk_kkxiaoqu = $("#xqVal1").val();
                    xxk_kclx = $("#lxVal1").val();
                    xxk_xxkmc = $("#xxkmc1").val();
                    jh = $("#jhVal1").val();
                    if (selCourseEle) {
                        jc = zc + ',' + selCourseEle.attr("zc") + ',' + selCourseEle.attr("djj");
                    }
                }
                U.ajax({
                    type: 'post',
                    url: "../elective/task/optional/courses/list",
                    data: {
                        fid: fid,
                        xxk_kknj: xxk_kknj,
                        xxk_kkyx: xxk_kkyx,
                        xxk_kkzy: xxk_kkzy,
                        kskc: kskc,
                        xxk_kkxiaoqu: xxk_kkxiaoqu,
                        xxk_kclx: xxk_kclx,
                        xxk_xxkmc: xxk_xxkmc,
                        jc: jc,
                        jh: jh,
                        xnxqh: xnxqh
                    },
                    dataType: 'json',
                    async: false,
                    success: function (result) {
                        if (result.code !== 200) {
                            layer.msg(result.msg)
                            return false;
                        }
                        if (!result.data) {
                            return false;
                        }
                        var html = "";
                        for (let i = 0; i < result.data.length; i++) {
                            let d = result.data[i];
                            if (d.selectNum == d.xxkXxkrl) {
                                html += "<div class='z-course z-full' taskBdid = '" + d.taskBdid + "' id='" + d.id + "'>";
                            } else {
                                html += "<div class='z-course' taskBdid = '" + d.taskBdid + "' id='" + d.id + "'>";
                            }
                            if (divv === 2) {
                                html += "<div class='z-course-mes'>";
                            }
                            html += "<div class='z-course-title'>";
                            html += "<h3>" + d.xxkXxkmc + "</h3>";
                            html += "<span>" + d.taskName + "</span>";
                            html += "</div>";
                            html += "<div class='z-mes'>";
                            html += "<p>课程类型：<span>" + d.xxkKclx + "</span></p>";
                            html += " <p>学分：<span>" + d.xxkXxkxf + "分</span></p>";
                            html += "<p>上课时间：<span>" + getsksjStr(d.xxkSksj) + "</span></p>";
                            html += "<p>上课地点：<span>" + d.xxkSkdd + "</span></p>";
                            html += "<p>授课教师：<span>" + d.xxkSkjs + "</span></p>";
                            if (d.xxkXxkrl == -1) {
                                html += "<p>课程余量：<span>" + d.selectNum + "/无上限</span></p>";
                            } else {
                                html += "<p>课程余量：<span>" + d.selectNum + "/" + d.xxkXxkrl + "</span></p>";
                            }
                            html += "</div>";
                            html += "</div>";
                            if (divv === 2) {
                                html += "<div class='z-btn'>";
                                if (stuSelectCourseCid.indexOf(d.id) != -1) {
                                    html += "<span class='btn-detail' cid='" + d.id + "' style='display: inline;'>详情</span>";
                                    html += "<span class='btn-exit' taskBdid = '" + d.taskBdid + "' cid = '" + d.id + "' style='display: inline;'>退课</span>";
                                    html += "<span class='btn-sel' taskBdid = '" + d.taskBdid + "' cid = '" + d.id + "' style='display: none'>选课</span>";
                                } else {
                                    html += "<span class='btn-detail' cid='" + d.id + "' >详情</span>";
                                    html += "<span class='btn-exit' taskBdid = '" + d.taskBdid + "' cid = '" + d.id + "' >退课</span>";
                                    html += "<span class='btn-sel' taskBdid = '" + d.taskBdid + "' cid = '" + d.id + "'>选课</span>";
                                }

                                html += "</div>";
                                html += "</div>";
                            }
                        }
                        if (divv === 2) {
                            $("#selCourseList").html(html);
                        } else {
                            $("#zList").html(html);
                        }
                    }
                })
            }

            U.ajax({
                type: "POST",
                url: "../elective/getNj",
                data: {fid: fid},
                dataType: 'json',
                success: function (result) {
                    var html = "";
                    if (result) {
                        for (var i = 0; i < result.data.length; i++) {
                            var name = result.data[i].name;
                            var id = result.data[i].id;
                            html += "<li data-id=\"" + id + "\">" + name + "</li>";
                        }

                    }
                    $("#njDiv1").append(html);
                    $("#njDiv2").append(html);
                }
            });

            U.ajax({
                type: "POST",
                url: "../elective/getYX",
                data: {fid: fid},
                dataType: 'json',
                success: function (result) {
                    var html = "";
                    if (result) {
                        for (var i = 0; i < result.data.length; i++) {
                            var name = result.data[i].name;
                            var id = result.data[i].id;
                            html += "<li data-id=\"" + id + "\">" + name + "</li>";
                        }

                    }
                    $("#yxDiv2").append(html);
                    $("#yxDiv1").append(html);
                }
            });

            U.ajax({
                type: "POST",
                url: "../elective/getZY",
                data: {fid: fid},
                dataType: 'json',
                success: function (result) {
                    var html = "";
                    if (result) {
                        for (var i = 0; i < result.data.length; i++) {
                            var name = result.data[i].name;
                            var id = result.data[i].id;
                            html += "<li data-id=\"" + id + "\">" + name + "</li>";
                        }

                    }
                    $("#zyDiv2").append(html);
                    $("#zyDiv1").append(html);
                }
            });

            U.ajax({
                type: "POST",
                url: "../elective/getTaskKC",
                data: {fid: fid},
                dataType: 'json',
                success: function (result) {
                    var html = "";
                    if (result) {
                        for (var i = 0; i < result.data.length; i++) {
                            var name = result.data[i].name;
                            var id = result.data[i].id;
                            html += "<li data-id=\"" + id + "\">" + name + "</li>";
                        }

                    }
                    $("#kcDiv2").append(html);
                    $("#kcDiv1").append(html);
                }
            });

            U.ajax({
                type: "POST",
                url: "../elective/getXQ",
                data: {fid: fid},
                dataType: 'json',
                success: function (result) {
                    var html = "";
                    if (result) {
                        for (var i = 0; i < result.data.length; i++) {
                            var name = result.data[i].name;
                            var id = result.data[i].id;
                            html += "<li data-id=\"" + id + "\">" + name + "</li>";
                        }

                    }
                    $("#xqDiv2").append(html);
                    $("#xqDiv1").append(html);
                }
            });

            U.ajax({
                type: "POST",
                url: "../elective/getLX",
                data: {fid: fid},
                dataType: 'json',
                success: function (result) {
                    var html = "";
                    if (result) {
                        for (var i = 0; i < result.data.length; i++) {
                            var name = result.data[i].name;
                            var id = result.data[i].id;
                            html += "<li data-id=\"" + id + "\">" + name + "</li>";
                        }

                    }
                    $("#lxDiv2").append(html);
                    $("#lxDiv1").append(html);
                }
            });
            //[{"xxk_skjc":"3","xxk_skxq":"2","xxk_skzc":"1-5,10"}]
            //获取上课时间
            function getsksjStr(sj) {
                let sjsjon = $.parseJSON(sj);
                var sksjstr = "";
                for (let i = 0; i < sjsjon.length; i++) {
                    let split = sjsjon[i].xxk_skzc.split(",");
                    for (let j = 0; j < split.length; j++) {
                        sksjstr += split[j] + "周、";
                    }
                    sksjstr += "周" + getxq(sjsjon[i].xxk_skxq) + "、";
                    sksjstr += getjc(sjsjon[i].xxk_skjc);
                    sksjstr += ";";
                }
                return sksjstr;
            }

            function getjc(xxk_skjc) {
                var skxqstr = "";
                let split = xxk_skjc.split(",");
                for (let i = 0; i < split.length; i++) {
                    if (split[i].indexOf("-") != -1) {
                        if (skxqstr != '') {
                            skxqstr += ",";
                        }
                        let split1 = split[i].split("-");
                        skxqstr += getjcStr(split1[0]) + "-" + getjcStr(split1[1]);
                    } else {
                        if (skxqstr != '') {
                            skxqstr += ",";
                        }
                        skxqstr += getjcStr(split[i]);
                    }
                }
                return skxqstr;
            }

            function getxq(xxk_skxq) {
                var skxqstr = "";
                let split = xxk_skxq.split(",");
                for (let i = 0; i < split.length; i++) {
                    if (split[i].indexOf("-") != -1) {
                        if (skxqstr != '') {
                            skxqstr += ",";
                        }
                        let split1 = split[i].split("-");
                        skxqstr += getxqStr(split1[0]) + "-" + getxqStr(split1[1]);
                    } else {
                        if (skxqstr != '') {
                            skxqstr += ",";
                        }
                        skxqstr += getxqStr(split[i]);
                    }
                }
                return skxqstr;
                // for (let i = 0; i < xxk_skxq.length; i++) {
                //     let split = xxk_skxq[i].split(",");
                //     for (let j = 0; j < split.length; j++) {
                //         skxqstr += split[j] + "周、";
                //     }
                // }
            }

            function getxqStr(xq) {
                if (xq == 1) {
                    return "一";
                } else if (xq == 2) {
                    return "二";
                } else if (xq == 3) {
                    return "三";
                } else if (xq == 4) {
                    return "四";
                } else if (xq == 5) {
                    return "五";
                } else if (xq == 6) {
                    return "六";
                } else if (xq == 7) {
                    return "日";
                } else {
                    return "";
                }
            }

            function getjcStr(jc) {
                return jcArr[jc];
                // if (jc == 1) {
                //     return "一";
                // } else if (jc == 2) {
                //     return "二";
                // } else if (jc == 3) {
                //     return "三";
                // } else if (jc == 4) {
                //     return "四";
                // } else if (jc == 5) {
                //     return "五";
                // } else if (jc == 6) {
                //     return "六";
                // } else if (jc == 7) {
                //     return "七";
                // } else if (jc == 8) {
                //     return "八";
                // } else if (jc == 9) {
                //     return "九";
                // } else if (jc == 10) {
                //     return "十";
                // } else if (jc == 11) {
                //     return "十一";
                // } else if (jc == 12) {
                //     return "十二";
                // } else {
                //     return "";
                // }
            }
        })
    })
</script>


</html>