<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" li-content="IE=edge">
    <meta name="viewport" li-content="width=device-width, initial-scale=1.0">
    <title>微点名</title>
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/global.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/layui1/css/layui.css'">
    <link rel="stylesheet" href="/elective/rollcall/css/commonPC.css">
    <link rel="stylesheet" href="/elective/rollcall/css/reset.css">
    <link rel="stylesheet" href="/elective/rollcall/css/poup.css">
    <link rel="stylesheet" href="/elective/rollcall/css/microdot-name_pc_stu_index.css">
    <link rel="stylesheet" href="/elective/rollcall/css/new-microdot-name.css">
    <link rel="stylesheet" href="/elective/rollcall/css/name-calling-end.css">
    <script th:src="${_CPR_}+'/elective/layui1/layui.js'"></script>

    <style>
        body .main .con {
            height: auto;
            min-height: calc(100vh - 196px);
        }

        body .main .con .table {
            padding-left: 30px;
        }

        body .main .con .filter-box.filter-box {
            justify-content: flex-start;
        }

        body .main .con .filter-box.filter-box .layui-form .layui-form-item {
            margin-bottom: 24px;
        }

        .main .con .table .detail span i.color6 {
            color: rgba(175, 119, 255, 1);
        }

        .layui-form-select dl dd.layui-select-tips {
            display: block;
        }

        .layui-form-select dl dd.layui-select-tips {
            padding-left: 20px !important;
        }

        body .main .con .filter-box .layui-form .layui-form-label {
            width: 70px;
            text-align: right;
        }

        .roll-call-record .layui-table-view .layui-table td:nth-child(3) .layui-table-cell {
            overflow: hidden;
        }

        body .main .con .filter-box .layui-form .layui-input-block .times {
            width: 240px;
            height: 34px;
            border-radius: 4px;
            box-sizing: border-box;
            cursor: pointer;
            background: url(../images/calendar-icons.png) no-repeat right 10px center;
            background-size: 12px;
        }

        body .main .con .filter-box .button-list {
            flex-direction: column;
            padding-right: 25px;
        }

        body .main .con .filter-box .button-list .searcher {
            margin-right: 0;
        }

        body .main .con .filter-box .button-list .reset {
            margin-top: 16px;
            margin-right: 0;
        }

        .layui-table-grid-down {
            display: none !important;
        }

        .layui-table-view .layui-table tr th > div {
            height: 36px;
            line-height: 36px;
        }

        .layui-table-cell {
            height: 36px;
            line-height: 36px;
        }

        .main .top .export {
            width: 96px;
            height: 36px;
            line-height: 36px;
            color: #4D88FF;
            font-size: 14px;
            cursor: pointer;
            text-align: center;
        }

        .main .top .export span {
            display: inline-block;
            padding-left: 20px;
            background: url(/elective/images/export-icons.png) no-repeat left center;
        }

        /* 2025.7.10 */

        .main .top .btn-box {
            display: flex;
            display: -webkit-flex;
            align-items: center;
            justify-content: flex-start;
        }

        .main .top .export-record {
            width: auto;
            height: 36px;
            line-height: 36px;
            color: #4D88FF;
            font-size: 14px;
            cursor: pointer;
            text-align: center;
            margin-left: 24px;
        }

        .main .top .export-record span {
            display: inline-block;
            padding-left: 20px;
            background: url(/elective/images/export-record.png) no-repeat left center;
        }

    </style>

</head>

<body>
<div class="main">
    <div class="top">
        <div class="title">
            <div class="back" th:if="${back}">返回</div>
            <div class="levelone">教师点名统计</div>
        </div>
        <div class="btn-box">
            <div class="btn export"><span>导出</span></div>
            <div class="btn export-record"><span>导出记录</span></div>
        </div>
    </div>
    <div class="con">
        <div class="filter-box">
            <form class="layui-form" action="" id="example" lay-filter="myFormFilter">
                <div class="layui-form-item">
                    <label class="layui-form-label">学年学期</label>
                    <div class="layui-input-block w240">
                        <select name="yearSem" lay-filter="yearSem" lay-verify="required" lay-search="" id="semester">
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">教师姓名</label>
                    <div class="layui-input-block w240">
                        <input type="text" name="teaName" class="layui-input" placeholder="请输入">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">工号</label>
                    <div class="layui-input-block w240">
                        <input type="text" name="teaCode" class="layui-input" placeholder="请输入">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">部门</label>
                    <div class="layui-input-block w240 sel">
                        <div class="select-input">
                            <div class="name" data-name="请选择" id="jsjbxx_bmVal">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <div class="all-selects">全选</div>
                                <ul class="dropdown-lists" id="jsjbxx_bm">
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">系部</label>
                    <div class="layui-input-block w240 sel">
                        <div class="select-input">
                            <div class="name" data-name="请选择" id="jsjbxx_yxVal">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <div class="all-selects">全选</div>
                                <ul class="dropdown-lists" id="jsjbxx_yx">
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">教研室</label>
                    <div class="layui-input-block w240 sel">
                        <div class="select-input">
                            <div class="name" data-name="请选择" id="jsjbxx_jysksVal">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <div class="all-selects">全选</div>
                                <ul class="dropdown-lists" id="jsjbxx_jysks">
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">日期选择</label>
                    <div class="layui-input-block w240">
                        <div class="times w240">
                            <input type="text" id="startTime" placeholder="请选择" readonly class="layui-input times">
                        </div>
                    </div>
                </div>
            </form>
            <div class="button-list">
                <div class="searcher" lay-filter="formDemo">查询</div>
                <div class="reset">重置</div>
            </div>

        </div>
        <div class="table">
            <div class="tab-cons roll-call-record">
                <table class="layui-table" id="materialTable" lay-filter="materialTable"></table>
            </div>


        </div>


    </div>
    <div id="coursePage"></div>
</div>

</div>

</body>
<script type="text/html" id="tmplToolBar">
    <div class="oprate-table">
        <div class="seek" lay-event="seek" style="color:#3A8BFF;;cursor:pointer;">查看详情</div>
    </div>
</script>
<script th:src="${_CPR_}+'/elective/js/jquery-3.3.1.min.js'"></script>
<script src="/elective/rollcall/js/common1.js"></script>
<script th:inline="javascript">
    let pageIndex = 1;
    var pageSize = 10;
    let xqMap = new Map();
    var current = [[${current}]];
    var fid = [[${fid}]];
    var uid = [[${uid}]];
    var searchArr = [
        {formAlias: "jsjbxx", fieldAlias: "jsjbxx_bm"}
        , {formAlias: "jsjbxx", fieldAlias: "jsjbxx_yx"}
        , {formAlias: "jsjbxx", fieldAlias: "jsjbxx_jysks"}
    ];
    layui.use(['table', 'jquery', 'laypage', 'laydate', 'form'], function () {

        var table = layui.table;
        var laypage = layui.laypage;
        var $ = layui.jquery;
        var layer = layui.layer;
        var laydate = layui.laydate;
        var form = layui.form;


        //日期选择
        var dateInst = laydate.render({
            elem: '#startTime',
            format: 'yyyy-MM-dd',
            min: current.xnxq_xqkssj,
            max: current.xnxq_xqjssj,
            range: '~',
        });
        form.on('select(yearSem)', function (data) {
            var elem = document.querySelector("#startTime");
            if (elem) {
                elem.outerHTML = "<input type=\"text\" id=\"testDate\" placeholder=\"请选择\" readonly class=\"layui-input times\">";
            }
            dateInst = laydate.render({
                elem: '#startTime',
                format: 'yyyy-MM-dd',
                type: 'date',
                range: '~',
                min: xqMap.get(data.value).xnxq_xqkssj,
                max: xqMap.get(data.value).xnxq_xqjssj
            });
        });


        var table1 = table.render({
            elem: "#materialTable",
            id: 'materialTable',
            url: '/elective/rollcall/getStaticTeaPage',
            where: {
                xnxqh: current.xnxq_xnxqh,
                courseTime: $("#startTime").val(),
                teaName: $('input[name="teaName"]').val(),
                teaCode: $('input[name="teaCode"]').val(),
                jsjbxx_bm: $('#jsjbxx_bmVal').text(),
                jsjbxx_yx: $('#jsjbxx_yxVal').text(),
                jsjbxx_jysks: $('#jsjbxx_jysksVal').text()
            },
            cols: [
                [{
                    field: "jsjbxx_xm",
                    align: "center",
                    title: "教师姓名",
                    minWidth: 100
                },
                    {
                        field: "jsjbxx_jsgh",
                        align: "center",
                        title: "工号",
                        minWidth: 100
                    },
                    {
                        field: "jsjbxx_bm",
                        align: "center",
                        title: "部门",
                        minWidth: 100
                    },
                    {
                        field: "jsjbxx_yx",
                        align: "center",
                        title: "系部",
                        minWidth: 100
                    },
                    {
                        field: "jsjbxx_jysks",
                        align: "center",
                        title: "教研室",
                        minWidth: 100
                    },
                    {
                        field: "subRCNum",
                        align: "center",
                        title: "应提交点名课程数",
                        minWidth: 100
                    },
                    {
                        field: "hasSubRCNum",
                        align: "center",
                        title: "已提交点名课程数",
                        minWidth: 100
                    },
                    {
                        field: "submissionRate",
                        align: "center",
                        title: "提交率",
                        minWidth: 100,
                        templet: function (d) {
                            return (((parseInt(d.hasSubRCNum) / parseInt(d.subRCNum)) * 100).toFixed(1) + "%");
                        }
                    },
                    {
                        field: "options",
                        align: "center",
                        title: "操作",
                        toolbar: "#tmplToolBar",
                        width: 88,
                        fixed: "right",
                    },

                ]
            ],
            done: function (res) {
                //分页
                laypage.render({
                    elem: 'coursePage',
                    curr: pageIndex,
                    groups: 5,
                    limit: pageSize,
                    limits: [10, 20, 30],
                    count: res.count,
                    layout: ['count', 'prev', 'page', 'next', 'limit', 'limits', 'skip'],
                    jump: function (obj, first) {
                        if (!first) {
                            pageIndex = obj.curr;
                            pageSize = obj.limit;
                            table.reload('materialTable', {
                                where: {
                                    xnxqh: $('#semester').val(),
                                    courseTime: $("#startTime").val(),
                                    teaName: $('input[name="teaName"]').val(),
                                    teaCode: $('input[name="teaCode"]').val(),
                                    jsjbxx_bm: $('#jsjbxx_bmVal').text(),
                                    jsjbxx_yx: $('#jsjbxx_yxVal').text(),
                                    jsjbxx_jysks: $('#jsjbxx_jysksVal').text(),
                                    page: pageIndex,
                                    limit: pageSize
                                }
                            }); //只重载数据
                        }
                    }
                });
            }
        })

        $(".searcher").click(function () {
            pageIndex = 1;
            table.reload('materialTable', {
                where: {
                    xnxqh: $('#semester').val(),
                    courseTime: $("#startTime").val(),
                    teaName: $('input[name="teaName"]').val(),
                    teaCode: $('input[name="teaCode"]').val(),
                    jsjbxx_bm: $('#jsjbxx_bmVal').text(),
                    jsjbxx_yx: $('#jsjbxx_yxVal').text(),
                    jsjbxx_jysks: $('#jsjbxx_jysksVal').text(),
                    page: pageIndex,
                    limit: pageSize
                }
            }); //只重载数据
        })

        table.on("tool(materialTable)", function (obj) {
            var data = obj.data; //获得当前行数据
            if (obj.event === "seek") {
                console.log("查看详情")
                window.open("/elective/rollcall/staticIndex/tea/detail?mobile=pc&xnxqh=" + $('#semester').val()
                    + "&jsjbxx_bm=" + data.jsjbxx_bm + "&jsjbxx_yx=" + data.jsjbxx_yx
                    + "&jsjbxx_jysks=" + data.jsjbxx_jysks + "&teaUid=" + data.jsjbxx_uid
                    + "&teaName=" + data.jsjbxx_xm + "&teaCode=" + data.jsjbxx_jsgh
                    + "&courseTime=" + $("#startTime").val()
                    , "_self");

            }
        })


        //返回

        $(".main .top .title .back").click(function () {
            window.history.go(-1);
        })

        $(".export").click(function () {
            $.ajax({
                type: 'post',
                url: "/elective/rollcall/exportTeaRecord",
                data: {
                    xnxqh: $('#semester').val(),
                    jsjbxx_bm: $('#jsjbxx_bmVal').text(),
                    jsjbxx_yx: $('#jsjbxx_yxVal').text(),
                    jsjbxx_jysks: $('#jsjbxx_jysksVal').text(),
                    courseTime: $("#startTime").val(),
                    teaName: $('input[name="teaName"]').val(),
                    teaCode: $('input[name="teaCode"]').val()
                },
                dataType: 'json',
                success: function (data) {
                    layer.msg(data.msg);
                }
            });
        })
        $(".export-record").click(function () {
            layer.open({
                type: 2,  // 2表示弹出的是iframe，1表示弹出的是层
                offset: 'auto',
                title: [''],
                area: ['858px', '570px'],
                scrollbar: true,
                content: "/downloadCenter/list.html?fid=" + fid + "&uid=" + uid + "&formId=40000002",   // 弹出iframe的页面链接
                btn: '',
                shade: 0.3 //显示遮罩
            });
        })

        resizeHh();

        $('.filter-box .button-list .reset').click(function () {
            console.log("重置");

            form.val("myFormFilter", { // myFormFilter 是表单定义的lay-filter的值
                "teaName": '',
                "teaCode": '',
            });

            layui.form.render();

            $("#startTime").val('');
            $("#jsjbxx_bmVal").text('请选择');
            $("#jsjbxx_yxVal").text('请选择');
            $("#jsjbxx_jysksVal").text('请选择');

        });

        function resizeHh() {
            let hh = $(".roll-call-record").offset().top + 106;
            let cuurent = $(window).height() - hh;
            $(".roll-call-record").css("min-height", cuurent);
        }

        $(window).resize(function () {
            resizeHh();
        });

        $.ajax({
            type: "get",
            url: "/elective/rollcall/getSemesters",
            dataType: 'json',
            async: false,
            success: function (data) {
                var html = "<option value=''>请选择</option>";
                if (data.status) {
                    for (var i = 0; i < data.data.length; i++) {
                        xqMap.set(data.data[i].xnxq_xnxqh, data.data[i]);
                        if (current.xnxq_xnxqh == data.data[i].xnxq_xnxqh) {
                            html += "<option value='" + data.data[i].xnxq_xnxqh + "' selected = 'selected'>" + data.data[i].xnxq_xnxqh + "</option>";
                        } else {
                            html += "<option value='" + data.data[i].xnxq_xnxqh + "'>" + data.data[i].xnxq_xnxqh + "</option>";
                        }
                    }
                }
                $("#semester").html(html);
                layui.form.render("select");
            }
        })

        for (let ob of searchArr) {
            $.ajax({
                type: "get",
                url: "/elective/rollcall/getFormDistinctFiled",
                data: ob,
                dataType: 'json',
                // async: false,
                success: function (data) {
                    var html = "";
                    if (data.status) {
                        for (var i = 0; i < data.data.length; i++) {
                            if (data.data[i] == '') {
                                continue;
                            }
                            html += "<li><span>" + data.data[i] + "</span></li>";
                        }
                    }
                    $("#" + ob.fieldAlias).html(html);
                    layui.form.render("select");
                }
            })
        }


    });
</script>

</html>