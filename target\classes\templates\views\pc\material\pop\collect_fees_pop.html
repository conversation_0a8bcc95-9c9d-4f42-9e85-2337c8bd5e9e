<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>开课信息</title>
    <link rel="stylesheet" th:href="@{/plugin/layui/css/layui.css}">
    <script th:src="@{/plugin/layui/layui.js}"></script>
</head>
<body>
<blockquote class="layui-elem-quote" style="border-left: 5px solid #f2f2f2;">
    为选中数据批量设置收费金额
</blockquote>
<form class="layui-form" style="margin: 40px 40px;">
    <div class="layui-form-item">
        <label class="layui-form-label">收费金额</label>
        <div class="layui-input-inline">
            <input type="text" name="cfMoney" required lay-verify="required" placeholder="请输入收费金额"
                   autocomplete="off" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-input-block">
            <button class="layui-btn" lay-submit lay-filter="btn-complate" style="background-color:#2589e9!important;">提交</button>
            <button type="reset" class="layui-btn layui-btn-primary">取消</button>
        </div>
    </div>
</form>
</body>
<script th:src="@{/js/jquery-1.11.3.min.js}"></script>
<script th:inline="javascript">
    layui.use(['form', 'layer'], function () {
        const form = layui.form;
        const layer = layui.layer;
        //监听提交
        form.on('submit(btn-complate)', function (data) {
            let loading = layer.load(1);
            $.post("/material/pop/collectFees", {
                queryId: [[${formTopBtnBO.queryId}]],
                fid: [[${formTopBtnBO.fid}]],
                uid: [[${formTopBtnBO.uid}]],
                cfMoney: data.field.cfMoney,
            }, function (result) {
                if (result.success) {
                    window.parent.postMessage(JSON.stringify({action: 1}), "*");
                } else {
                    layer.msg(result.message, {icon: 2, time: 2000});
                }
                layer.close(loading);
            }, "json");
            return false;
        })
    })
    $(".layui-btn-primary").click(function () {
        window.parent.postMessage(JSON.stringify({action: 1}), "*");
    })
</script>
</html>