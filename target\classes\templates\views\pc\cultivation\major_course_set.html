<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教学进程表</title>
    <link rel="stylesheet" th:href="@{../../css/cultivation/global.css(v=${new java.util.Date().getTime()})}">
    <link rel="stylesheet" th:href="@{../../plugin/layui/css/layui2.8.2.css}">
    <link rel="stylesheet" th:href="@{../../css/cultivation/major_course_set.css(v=${new java.util.Date().getTime()})}">
    <link rel="stylesheet" th:href="@{/css/cultivation/batchEditPop.css}">
    <link rel="stylesheet" th:href="@{/css/cultivation/slideCommon.css}">
    <style>
        .container .remark .intro {
            display: block;
            width: 100%;
            padding: 10px 24px;
            font-size: 14px;
            box-sizing: border-box;
            background-color: #ebf1fd;
            color: #818181;
        }

        .container .remark .intro span {
            width: 16px;
            height: 16px;
            display: inline-block;
            background: url(/images/cultivation/tips-icon.png) no-repeat left center;
            background-size: 14px;
            vertical-align: text-top;
            padding-right: 4px;
        }

        .layui-tr-total td.layui-total {
            border-color: #cbd9f6 !important;
        }

        .layui-week {
            border-color: #cbd9f6 !important;
        }

        .layui-table-edit {
            z-index: 2 !important;
        }

        .layui-table-view .layui-table td {
            color: #1D2129 !important;
        }

        .layui-table-view .layui-table th {
            color: #6581BA !important;
            font-weight: bold;
        }

        div[lay-id=courseList] .laytable-cell-2-0-2 {
            width: auto;
        }

        .container .major-opt .major-button {
            flex-wrap: wrap;
            float: unset;
            margin-bottom: 0;
            position: relative;
        }

        .container .major-opt .major-button button {
            margin-bottom: 20px;
        }

        #optLog {
            position: absolute;
            right: 0;
            bottom: 20px;
            color: #4c88ff;
            padding-left: 17px;
            background: url(/images/cultivation/record1.png) no-repeat left center;
            background-size: 15px;
        }

        .replaceBtn {
            color: #4c88ff;
            margin-right: 16px;
        }

        .replaceBtn:hover {
            color: #4c88ff;
            cursor: pointer;
        }

        #tabBox .layui-table-view .layui-table td {
            height: 48px;
        }

        .container #tabBox .amountTo {
            line-height: 48px;
        }
    </style>
    <script th:src="@{../../plugin/layui/layui2.8.2.js}"></script>
    <script th:src="@{/js/cultivation/slideCommon.js}"></script>
</head>

<body>
<div class="container">
    <div class="top">
        <div class="title">
            <!--<div class="back">返回</div>-->
            <div class="levelone">培养方案管理</div>
            <div class="icon"></div>
            <div class="leveltwo">教学进程表</div>
        </div>
    </div>
    <div class="remark" th:if="${auditStatus!=1}">
        <div class="intro">
            <span></span>
            提示：本页面用于编辑人才培养方案中各门课程在各学期的周学时安排、学时分配、学分设置等信息，完成编辑后需要点击右上角【发布教学进程】按钮，将本页面的信息同步至培养方案。发布后的教学进程表如有变更，需在调整后再次点击【发布教学进程】按钮进行同步。
        </div>
    </div>
    <div class="major-opt">
        <div class="major-mes">
            <span th:text="'年级：'+${form?.pyfagl_nj}"></span>
            <span th:text="'所属系部：'+${form?.pyfagl_suxb}"></span>
            <span th:text="'专业：'+${form?.pyfagl_zy}"></span>
            <span th:text="'专业编号：'+${form?.pyfagl_zybh}"></span>
            <span th:text="'培养层次：'+${form?.pyfagl_pycc}"></span>
            <span th:text="'学制：'+${form?.pyfagl_xz}"></span>
        </div>
        <div class="major-button">
            <button id="addCourseBtn">添加课程</button>
            <button id="delCourseBtn" th:if="${auditStatus!=1}">删除课程</button>
            <button id="batchEdit">批量编辑</button>
            <!--<button id="syncSettingToForm">同步到专业课设置</button>-->
            <button class="importBtn" th:if="${auditStatus!=1}" id="importBtn">导入教学进程表</button>
            <button id="exportTeachPlan">导出教学进程表</button>
            <button id="saveTeachPlan" th:if="${auditStatus!=1}">保存教学进程表</button>
            <button id="saveSetting" style="background-color: #4d88ff;color: #fff;">发布教学进程</button>
            <a href="/cultivation/majorCourseSet/majorCourseSetOperationLog" id="optLog"
               rel="noopener noreferrer">操作日志</a>
        </div>
    </div>
    <div id="tabBox">
        <table class="layui-hide" id="courseTab" lay-filter="courseTab"></table>
    </div>
</div>
<!-- 添加课程 -->
<div id="addCourse" class="dialog course" style="display: none;">
    <div class="dialog-title">
        <h5>选择课程</h5>
        <div class="close-btn"></div>
    </div>
    <div class="dialog-con">
        <form action="" class="layui-form form-course">
            <div class="layui-inline-wrap">
                <div class="layui-inline">
                    <label class="layui-form-label">课程编号</label>
                    <div class="j-search-con single-box">
                        <input type="text" class="fuzzy-query-input" name="kck_kcbh" placeholder="请输入"
                               autocomplete="off">
                        <div class="j-select-search">
                            <ul class="kck_kcbh">
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">课程名称</label>
                    <div class="j-search-con single-box">
                        <input type="text" class="fuzzy-query-input" name="kck_kcmc" placeholder="请输入"
                               autocomplete="off">
                        <div class="j-select-search">
                            <ul class="kck_kcmc">
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">[[${kck_kkjys}]]</label>
                    <div class="j-search-con multiple-box">
                        <input type="text" name="kck_kkjys" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <ul class="kck_kkjys">
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">开课部门</label>
                    <div class="j-search-con multiple-box">
                        <input type="text" name="kck_kkyx" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <ul class="kck_kkyx">
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">课程性质</label>
                    <div class="j-search-con multiple-box">
                        <input type="text" name="kck_kcxz" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <ul class="kck_kcxz">
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">选必修</label>
                    <div class="j-search-con multiple-box">
                        <input type="text" name="kck_kcsx" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <ul>
                                <li value="选修">选修</li>
                                <li value="必修">必修</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-inline layui-inline-button">
                <button type="submit" class="layui-btn " lay-submit lay-filter="selTable">筛选</button>
                <button type="reset" class="layui-btn layui-btn-primary">清空</button>
            </div>
        </form>
        <div class="courseList">
            <table class="layui-hide" id="courseList" lay-filter="courseList"></table>
            <div class="selCourse" id="selCourse">
                <div class="z-check">
                    <span class="check" id="checkAllCourse"></span>选择全部数据
                </div>
                <span>共<i></i>条，已选<em>0</em>条</span>
            </div>
        </div>
    </div>
    <div class="dialog-footer">
        <button id="cancelBtn">取消</button>
        <button id="sureBtn">确定</button>
    </div>
</div>
<!-- 替换课程 -->
<div id="replaceCourse" class="dialog course" style="display: none;">
    <div class="dialog-title">
        <h5>选择课程</h5>
        <div class="close-btn"></div>
    </div>
    <div class="dialog-con">
        <form action="" class="layui-form form-course">
            <div class="layui-inline-wrap">
                <div class="layui-inline">
                    <label class="layui-form-label">课程编号</label>
                    <div class="j-search-con single-box">
                        <input type="text" class="fuzzy-query-input" name="kck_kcbh" placeholder="请输入"
                               autocomplete="off">
                        <div class="j-select-search">
                            <ul class="kck_kcbh">
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">课程名称</label>
                    <div class="j-search-con single-box">
                        <input type="text" class="fuzzy-query-input" name="kck_kcmc" placeholder="请输入"
                               autocomplete="off">
                        <div class="j-select-search">
                            <ul class="kck_kcmc">
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">[[${kck_kkjys}]]</label>
                    <div class="j-search-con multiple-box">
                        <input type="text" name="kck_kkjys" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <ul class="kck_kkjys">
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">开课部门</label>
                    <div class="j-search-con multiple-box">
                        <input type="text" name="kck_kkyx" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <ul class="kck_kkyx">
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">课程性质</label>
                    <div class="j-search-con multiple-box">
                        <input type="text" name="kck_kcxz" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <ul class="kck_kcxz">
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">选必修</label>
                    <div class="j-search-con multiple-box">
                        <input type="text" name="kck_kcsx" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <ul>
                                <li value="选修">选修</li>
                                <li value="必修">必修</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-inline layui-inline-button">
                <button type="submit" class="layui-btn " lay-submit lay-filter="replaceSelTable">筛选</button>
                <button type="reset" class="layui-btn layui-btn-primary">清空</button>
            </div>
        </form>
        <div class="courseList">
            <table class="layui-hide" id="replaceCourseList" lay-filter="replaceCourseList"></table>
        </div>
    </div>
    <div class="dialog-footer">
        <button id="replaceCancelBtn">取消</button>
        <button id="replaceSureBtn">确定</button>
    </div>
</div>
<!-- 批量编辑 -->
<div class="mould_pop batch_editor_pop" style="display: none;">
    <div class="pop_cont">
        <div class="bep_cont">
            <p class="bep_tip mb20"></p>
            <div class="bep_per mb20">
                <div class="bep_per_left fl">修改字段</div>
                <div class="bep_per_right fl">
                    <div class="bep_per_sel"><input type="text" readonly="readonly" class="bep_per_input">
                        <div class="bep_search_sel one" style="display: none;">
                            <ul class="bep_per_uls">
                                <li title="credit" class="bep_per_lis">[[${kck_xf}]]</li>
                                <li title="examType" class="bep_per_lis">[[${kck_ksxs}]]</li>
                                <li title="openDept" class="bep_per_lis">[[${kck_kkyx}]]</li>
                                <li title="courseNature" class="bep_per_lis">[[${kck_kcxz}]]</li>
                                <li title="chooseCompulsory" class="bep_per_lis">[[${kck_kcsx}]]</li>
                                <li title="courseName" class="bep_per_lis">[[${kck_kcmc}]]</li>
                            </ul>
                        </div>
                    </div>
                    <p class="bep_per_tip">修改已选数据，如需修改所有数据请先勾选表格下方“选中所有数据”</p></div>
                <div class="clear"></div>
            </div>
            <div class="bep_per">
                <div class="bep_per_left fl">修改为</div>
                <div class="bep_per_right fl">
                    <div class="bep_per_sel formField">
                        <input type="text" placeholder="请输入" class="bep_per_text">
                    </div>
                    <p class="bep_per_tip">如需清空字段，保留此项为空即可</p></div>
                <div class="clear"></div>
            </div>
        </div>
    </div>
    <div class="pop_btm"><span class="pop_cal">取消</span> <span class="pop_sure">提交</span></div>
</div>
<!-- 指定教材 -->
<div id="selTextbook" class="dialog">
    <div class="dialog-title">指定教材</div>
    <div class="tips">使用说明：不勾选数据直接点击 [确定] 提交，即可清空已有教材。</div>
    <div class="dialog-con">
        <form action="" class="layui-form form-textbook">
            <div class="layui-inline">
                <label class="layui-form-label">教材名称</label>
                <div class="layui-input-inline">
                    <input type="text" name="jc_jcmc" placeholder="请输入" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">教材号</label>
                <div class="layui-input-inline">
                    <input type="text" name="jc_jcbh" placeholder="请输入" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label" style="width: 76px;">出版号ISBN</label>
                <div class="layui-input-inline">
                    <input type="text" name="jc_isbn" placeholder="请输入" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                <button type="submit" class="layui-btn " lay-submit lay-filter="selTextbookTable">筛选</button>
            </div>
        </form>
        <div class="courseList">
            <table class="layui-hide" id="textbookList" lay-filter="textbookList"></table>
            <div class="z-check">
                <span class="check" id="checkAll"></span>选择全部数据
            </div>
            <div class="selCourse" id="selTextbookCount">已选中<em>0</em>条</div>
        </div>
    </div>
    <div class="dialog-footer">
        <button id="textbookCancelBtn">取消</button>
        <button id="textbookSureBtn">确定</button>
    </div>
</div>
<div id="textbookTips" class="textbookBot">
    <div class="textbook-con-wrap"></div>
</div>
<!-- 导入教学进程表  -->
<div id="importTeachProcess" class="dialog">
    <div class="dialog-title">
        <h5>导入</h5>
        <div class="opt-right">
            <a class="btn-export" style="display: none;">导出上次错误数据项</a>
            <div class="close-btn"></div>
        </div>
    </div>
    <div class="dialog-con">
        <h5>注意事项:</h5>
        <p>1.表格第一行是表头，对应系统字段，请不要轻易删改。</p>
        <p>2.表头与设置的显示字段一致，更改导入模板表头，请前往教学进程表参数设置。</p>
        <p>3.子表单明细同样占用行数。</p>
        <p>4.为保证数据导入顺利,请<a class="down-template">下载excel模板</a>，并仔细阅读其中的导入示例和说明。</p>
        <button type="button" class="layui-btn demo-class-accept" id="uploadBtn">
            上传文件
        </button>
    </div>
</div>
<!-- 处理导入文件 -->
<div class="dialog" id="importTeachProcessFile">
    <div class="dialog-title">
        <h5>导入</h5>
        <div class="close-btn"></div>
    </div>
    <div class="dialog-con">
        <img class="loding" src="/images/cultivation/mooc/loading.gif" alt="">
        <div class="importTips importTipsSuccess importTipsError">
            <p><span class="icon"></span>处理中...成功上传<em class="green">0</em>条数据<span class="error">，失败<em
                    class="yellow">0</em>条数据，</span></p>
            <p>请修改后再次导入</p>

        </div>
    </div>
    <div class="dialog-footer">
        <button id="importTeachCancelBtn">取消</button>
        <button id="importTeachSureBtn">确定</button>
    </div>
</div>
<!-- 导入结果 失败增加-->
<div class="dialog importSuccess" id="importResult">
    <div class="dialog-title">
        <h5>导入</h5>
        <div class="close-btn"></div>
    </div>
    <div class="dialog-con ">
        <div class="importIcon"></div>
        <div class="importTips ">
            <p>处理完成，成功上传<em class="green">0</em>条数据<span class="error">，失败<em
                    class="yellow">0</em>条数据，</span>
            </p>
            <p>请修改后再次导入</p>
        </div>
    </div>
    <div class="dialog-footer">
        <a href="" id="importErrorData">导出错误数据项</a>
        <button id="importSureBtn">确定</button>
    </div>
</div>
<div class="marker"></div>
<!--发布二次确认弹窗-->
<div class="dialogPop dialog" id="offerCourseDialog" style="display: none;">
    <div class="dialog-title">
        <h5>确认发布教学进程表</h5>
        <div class="opt-right">
            <div class="close-btn"></div>
        </div>
    </div>
    <div class="dialog-con">
        <img src="/images/cultivation/tips-icon1.png" alt="">
        <p>发布后，设置的课程将同步至方案内课程，请确认是否发布？</p>
    </div>
    <div class="dialog-footer">
        <button class="btn-cancel">取消</button>
        <button class="btn-sure">确定</button>
    </div>
</div>
<!--发布成功失败弹窗-->
<div class="dialogPop dialog" id="setTipDialog" style="display: none;">
    <div class="dialog-con">
        <img src="/images/cultivation/error-tip1.png" alt="">
        <p></p>
        <button id="tipBtn" class="btn-sure">确定</button>
    </div>
</div>
<div class="dialogPop dialog" id="exportDialog" style="display: none;">
    <div class="dialog-title">
        <h5>确认导出教学进程表</h5>
        <div class="opt-right">
            <div class="close-btn"></div>
        </div>
    </div>
    <div class="dialog-con">
        <img src="/images/cultivation/tips-icon1.png" alt="">
        <p>请确认是否已保存教学进程表</p>
    </div>
    <div class="dialog-footer">
        <button class="btn-cancel">取消</button>
        <button class="btn-sure">确定</button>
    </div>
</div>
</body>
<script type="text/html" id="toolBarDel">
    <div class="layui-clear-space">
        <a class="replaceBtn" lay-event="replace">替换</a>
        <a class="delBtn" lay-event="del">删除</a>
    </div>
</script>
<script type="text/html" id="examType">
    {{#  if("[[${auditStatus}]]" === "1"){ }}
    <span>{{= d.examType }}</span>
    {{# } else { }}
    <select name="examType" class="layui-border select-demo-primary" lay-ignore lay-filter="sel">
        <option value="">请选择</option>
        {{# layui.each(d.data, function(i, v){ }}
        <option value="{{= v }}">{{= v }}</option>
        {{# }); }}
    </select>
    {{# } }}
</script>

<script type="text/html" id="openDept">
    {{#  if("[[${auditStatus}]]" === "1"){ }}
    <span>{{= d.openDept }}</span>
    {{# } else { }}
    <select name="openDept" class="layui-border select-demo-primary" lay-ignore lay-filter="sel">
        <option value="">请选择</option>
        {{# layui.each(d.data, function(i, v){ }}
        <option value="{{= v }}">{{= v}}</option>
        {{# }); }}
    </select>
    {{# } }}
</script>
<script>
    let courseTabData = [];
    let resultData = [];
    let courseTabArray = [];
    let xz = "[[${form?.pyfagl_xz}]]";
    let auditStatus = "[[${auditStatus}]]";
    let selDataArray = [], uniqueArray = [];
    let textBookSetting = "", courseNo = "", dimension = "courseNature", displaySetting = "";
    let kck_kcsx = "[[${kck_kcsx}]]" ? "[[${kck_kcsx}]]" : "选必修";
    // 周学时长度
    let week_len = 0;
    layui.use(['table', 'jquery', 'util', 'form', 'layer', 'laytpl', 'upload'], function () {
        const util = layui.util, form = layui.form, layer = layui.layer, upload = layui.upload;
        const table = layui.table, $ = layui.jquery, laytpl = layui.laytpl;
        if (auditStatus === "1") {
            $("#addCourseBtn,#saveSetting,#syncSettingToForm,#batchEdit").remove();
        }
        const editable = function (d) {
            if (d.editable && auditStatus === "0") return 'text'; // 这里假设以 editable 字段为判断依据
        };
        const practiceEdit = function (d) {
            if (d.purePractical) return 'text'; // 这里假设以 editable 字段为判断依据
        };
        // 渲染
        const tabHeight = $('#tabBox').offset().top + 40;

        courseTabArray = [
            [{
                type: 'checkbox',
                fixed: 'left',
                width: 80,
                rowspan: 4,
                hide: auditStatus === "1"
            }, {
                field: 'courseCategory',
                title: '[[${kck_kclb}]]',
                width: 112,
                rowspan: 4,
                align: "center",
                fixed: 'left'
            }, {
                field: 'courseNature',
                title: '[[${kck_kcxz}]]',
                width: 112,
                rowspan: 4,
                align: "center",
                fixed: 'left'
            }, {
                field: 'courseClassify',
                title: '[[${kck_kcfl}]]',
                width: 100,
                rowspan: 4,
                align: "center",
                fixed: 'left',
            }, {
                field: 'courseType',
                title: '[[${kck_kclx}]]',
                width: 100,
                rowspan: 4,
                align: "center",
                fixed: 'left',
            }, {
                field: 'courseAttribute',
                title: '[[${kck_kcshx}]]',
                width: 100,
                rowspan: 4,
                align: "center",
                fixed: 'left',
            }, {
                field: 'chooseCompulsory',
                title: kck_kcsx,
                width: 92,
                rowspan: 4,
                align: "center",
                fixed: 'left',
            }, {
                field: 'courseIndex',
                title: '序号',
                width: 68,
                rowspan: 4,
                align: "center",
                fixed: 'left',
            }, {
                field: 'courseId',
                title: '[[${kck_kcbh}]]',
                width: 150,
                rowspan: 4,
                align: "center",
                fixed: 'left',
                sort: true
            }, {
                field: 'courseGroupName',
                title: '课程名称',
                colspan: 2,
                align: 'center',
                width: 280,
                fixed: 'left'
            }, {
                field: 'credit',
                title: '[[${kck_xf}]]',
                width: 48,
                rowspan: 4,
                align: "center",
                totalRow: true,
                edit: editable,
            }, {
                colspan: 6,
                title: '各学期周学时安排',
                align: "center",
            }, {
                colspan: 5,
                field: 'hoursDistribute',
                title: '学时分配',
                align: "center",
            }, {
                field: 'extraClassHour',
                title: '额外学时',
                width: 82,
                rowspan: 4,
                align: "center",
                totalRow: true,
                edit: editable
            }, {
                field: 'totalClassHour',
                title: '[[${kck_zxs}]]',
                width: 82,
                rowspan: 4,
                align: "center",
                totalRow: true,
                edit: practiceEdit
            }, {
                field: 'examType',
                title: '[[${kck_ksxs}]]',
                width: 120,
                rowspan: 4,
                templet: '#examType',
                align: "center",
            }, {
                field: 'courseCriterion',
                title: '是否有课标',
                minWidth: 120,
                rowspan: 4,
                align: "center",
                totalRow: true
            }, {
                field: 'openDept',
                templet: '#openDept',
                title: '[[${kck_kkyx}]]',
                width: 120,
                rowspan: 4,
                align: "center",
            }, {
                field: 'notes',
                title: '备注',
                minWidth: 100,
                rowspan: 4,
                align: "center",
                edit: editable,
            }, {
                title: '操作',
                width: 110,
                rowspan: 4,
                align: "center",
                fixed: 'right',
                toolbar: '#toolBarDel',
                hide: auditStatus === "1"
            },
            ],
            [{
                field: 'courseGroup',
                title: '[[${kck_kcmc}]]',
                width: 80,
                rowspan: 3,
                align: 'center',
                fixed: 'left'
            }, {
                field: 'courseName',
                title: '[[${kck_kcmc}]]',
                width: 200,
                rowspan: 3,
                align: 'center',
                fixed: 'left'
            }, {
                field: 'theory',
                title: '理论',
                width: 68,
                rowspan: 3,
                align: "center",
                totalRow: true,
                edit: editable,
            }, {
                field: 'practice',
                title: '实践',
                width: 68,
                rowspan: 3,
                align: "center",
                totalRow: true,
                edit: editable,
            }, {
                field: 'experiment',
                title: '实验',
                width: 68,
                rowspan: 3,
                align: "center",
                totalRow: true,
                edit: editable,
            }, {
                field: 'computer',
                title: '上机',
                width: 68,
                rowspan: 3,
                align: "center",
                totalRow: true,
                edit: editable,
            }, {
                field: 'other',
                title: '其他',
                width: 68,
                rowspan: 3,
                align: "center",
                totalRow: true,
                edit: editable,
            },],
            [],
            []
        ];
        if (xz) {
            //动态计算周学时
            if (!$.isNumeric(xz)) {
                xz = xz.includes('-') ? xz.split('-')[1] : xz;
                xz = xz.replace(/[^\d+]/g, '').split('+').reduce((acc, val) => acc + parseFloat(val), 0);
            }
            week_len = xz * 2;
            //动态插入单元格
            const weekArray = [];
            const hourArray = [];
            const weekCountArray = [];
            for (let i = 0; i < week_len; i++) {
                let year = parseInt("[[${form?.pyfagl_nj}]]") + Math.floor(i / 2);
                let semester = i % 2 + 1;
                let term = year + "-" + (year + 1) + "-" + semester;
                weekArray.push({
                    title: i + 1,
                    title1: term,
                    minWidth: 90,
                    colGroup: true,
                    colspan: 2,
                    align: "center",
                });
                weekCountArray.push({
                    title: "18周",
                    title1: term,
                    field: `week${i + 1}`,
                    minWidth: 90,
                    colGroup: true,
                    colspan: 2,
                    align: "center",
                });
                hourArray.push({
                    field: `classHourWeek${i + 1}`,
                    title: "周学时",
                    minWidth: 90,
                    rowspan: 2,
                    align: "center",
                    edit: editable
                });
                hourArray.push({
                    field: `textBook${i + 1}`,
                    title: '学期教材',
                    minWidth: 104,
                    align: "center",
                    event: 'selTB'
                });
            }
            courseTabArray[1].splice(2, 0, ...weekArray);
            courseTabArray[2].push(...weekCountArray);
            courseTabArray[3].push(...hourArray);
        }
        courseTabArray[0][11].colspan = week_len * 2;
        $.ajax({
            url: '/cultivation/moocActivationSett/getTeachingProgressParamSet',
            type: 'post',
            dataType: 'json',
            async: false,
            success: function (result) {
                if (result.data) {
                    displaySetting = $.parseJSON(result.data.displaySetting);
                    textBookSetting = $.parseJSON(result.data.textBookSetting);
                    dimension = result.data.dimension ? result.data.dimension : "courseNature";
                    if (!displaySetting.textBook) {
                        courseTabArray[0][11].colspan = week_len;
                        for (let i = 0; i < week_len; i++) {
                            courseTabArray[1][i + 2].colspan = 0;
                            courseTabArray[2][i].colspan = 0;
                        }
                    }
                    const fields = courseTabArray.map(function (row) {
                        return row.map(function (cell) {
                            return cell.field;
                        });
                    });
                    if (!displaySetting.hoursDistribute) {
                        courseTabArray[1].splice(-5);
                    }
                    fields.forEach(row => {
                        row.filter(field => field).forEach(filteredField => {
                            $.each(displaySetting, function (key, value) {
                                if (key === "courseGroup") {
                                    return true;
                                }
                                if (filteredField.indexOf(key) !== -1 && !value) {
                                    courseTabArray.forEach(row => {
                                        row.forEach((cell, index) => {
                                            if (cell.field === filteredField) {
                                                row.splice(index, 1);
                                            }
                                        });
                                    });
                                }
                            })
                        });
                    });
                    if (courseTabArray[0][12]) {
                        courseTabArray[0][12].colspan = courseTabArray[1].length - (week_len + 2);
                    }
                    if (displaySetting.hoursDistribute) {
                        const index = courseTabArray[0].findIndex(item => item.field === 'hoursDistribute');
                        const lastIndex = courseTabArray[1].reduce((acc, item, index) => {
                            if (item.title1 !== undefined) return index;
                            return acc;
                        }, -1);
                        courseTabArray[0][index].colspan = courseTabArray[1].length - (lastIndex + 1);
                    }
                }
            }
        })
        table.render({
            elem: '#courseTab',
            data: auditStatus === "1" ? resultData : window.localStorage.getItem("tableData[[${form.rowInfo.formUserId}]]") ?
                JSON.parse(window.localStorage.getItem("tableData[[${form.rowInfo.formUserId}]]")) :
                resultData,
            maxHeight: 'full-' + tabHeight,
            totalRow: true,
            cols: courseTabArray,
            autoSort: false,
            done: function (res, curr, count) {
                let weekC = window.localStorage.getItem("header[[${form.rowInfo.formUserId}]]");
                if (res.data.length === 0) {
                    $("#tabBox .layui-table-header th[data-field=courseGroupName]").attr('rowspan', 4).css('width', '281px')
                    $("#tabBox .layui-table-header th[data-field=courseName],#tabBox .layui-table-header  th[data-field=courseGroup]").addClass('layui-hide')
                }
                if (res.data.length > 0) {
                    const result = res.data.find(item => {
                        try {
                            const obj = JSON.parse(item.hoursArrange);
                            return Object.keys(obj).some(key => key.startsWith('week'));
                        } catch (e) {
                            return false;
                        }
                    });
                    if (result) {
                        weekC = weekC ? weekC : result.hoursArrange;
                    }
                }
                let totalRowElem = this.elem.next('.layui-table-view').find('.layui-table-total');
                totalRowElem.find('td').each(function () {
                    let $div = $(this).find('div');
                    let num = parseFloat($div.text());
                    if (!isNaN(num)) {
                        $div.text(num % 1 === 0 ? num : num.toFixed(1));
                    }
                });
                $("#tabBox .layui-table-fixed-l .layui-table-header th[data-field=courseGroupName]").attr('rowspan', 4).css('width', '281px')
                $("#tabBox .layui-table-fixed-l .layui-table-header th[data-field=courseName],#tabBox .layui-table-fixed-l .layui-table-header  th[data-field=courseGroup]").addClass('layui-hide')
                //设置结果
                if (!displaySetting.total || !displaySetting.polymerizationSta) {
                    $(".layui-table-total").hide();
                }
                $('.layui-table-total td').eq(0).find("div").text("合计");
                /* 从第几行开始遍历合并 去除表头就是 1 咯 */
                let merge_column_infos = [dimension];
                if (displaySetting.courseGroup) {
                    merge_column_infos.push("courseGroup");
                }
                /* 查找到需要合并表格所有的 tr */
                const tr_s = $(".layui-table-fixed-l tbody").find("tr");
                for (const merge_item of merge_column_infos) {
                    /* 需要合并栏目的数量 */
                    let merge_num = 0;
                    /* 需要合并栏目的 td */
                    let merge_tds = [];
                    /* 开始遍历需要合并表格的所有 tr */
                    for (let i = 0; i < tr_s.length; i++) {
                        /* 当前 td */
                        const m_td = tr_s.eq(i).find("td");
                        let index = m_td.filter('[data-field="courseIndex"]').index();
                        const cur_td = tr_s.eq(i).find("td[data-field=" + merge_item + "]"); /* 下一个 td */
                        /* 上一个 */
                        const next_td = tr_s.eq(i + 1).find("td[data-field=" + merge_item + "]"); /* 当前 td 的 text */
                        const cur_text = $(cur_td).text();
                        /* 下一个 td 的 text 当遍历到最后默认为空 */
                        const next_text = $(next_td).text(); /* 如果当前 td=下一个 td */
                        if (cur_text && next_text && cur_text === next_text) {
                            /*放入到合并 td 的集合中 */
                            merge_tds.push(cur_td); /* 需要合并的 td 数量加 1 */
                            merge_num++;
                        } else {
                            if (tr_s.eq(i).find("td").eq(index).text() === '小计') {
                                tr_s.eq(i).addClass('layui-tr-total')
                                tr_s.eq(i).find("td").eq(0).removeClass("layui-hide");
                                let leftLen = $("#tabBox .layui-table-fixed-l .layui-table-header tr:eq(0) th").length;
                                let idx = displaySetting.subtotalProportion ? leftLen - (parseInt(auditStatus) + 1) : leftLen;
                                tr_s.eq(i).find("td").eq(0).attr("colspan", idx).children().remove();
                                tr_s.eq(i).find("td").eq(0).append('<div class="layui-table-cell laytable-cell-1-0-0" align="center">小计</div>')
                                if (displaySetting.subtotalProportion) {
                                    tr_s.eq(i).find("td").eq(0).nextAll().not('[data-field="courseName"]').addClass("layui-hide");
                                } else {
                                    tr_s.eq(i).find("td").eq(0).nextAll().addClass("layui-hide");
                                }
                                $("#tabBox .layui-table-fixed-r .layui-table-body tr").eq(i).addClass('layui-tr-total').find('.layui-clear-space').hide();
                                $("#tabBox .layui-table-body tr").eq(i).find('.layui-clear-space').hide();
                            }
                            /* 如果 如果当前 td !=下一个 td 且要合并的 td 数量不等于 0 */
                            if (merge_num !== 0 && cur_text && displaySetting.polymerizationSta) {
                                /* 第一个 td 合并 因为动态添加 rowspan 属性是向下延申 */
                                $(merge_tds[0]).attr("rowspan", merge_num + 1); /* 遍历所有的需要合并的 td 将他们的属性设置为 不可见 */
                                for (let d = 1; d < merge_tds.length; d++) {
                                    $(merge_tds[d]).addClass("layui-hide");
                                } /* 当前 td 属性也需要设置为不可见 */
                                $(cur_td).addClass("layui-hide");
                            }
                            /* 重置合并 td 数据 */
                            merge_num = 0;
                            merge_tds = [];
                        }
                        let courseGroupVal = tr_s.eq(i).find("td[data-field='courseGroup']").text();
                        if (!displaySetting.courseGroup || !courseGroupVal) {
                            tr_s.eq(i).find("td[data-field='courseGroup']").addClass("layui-hide");
                            tr_s.eq(i).find("td[data-field='courseGroup']").next().attr('colspan', 2).css('width', '281px').find('.layui-table-cell').css('width', '281px')
                        }
                    }
                }

                // 横向合并单元格
                // layui-table-main
                const tr_m = $("#tabBox .layui-table-main").find("tr");
                tr_m.each(function () {
                    let isIncludes;
                    const m_td = $(this).find("td");
                    const td_len = m_td.length;
                    const td_text = m_td.eq(m_td.filter('[data-field="courseIndex"]').index()).text();
                    let start = m_td.filter('[data-field="classHourWeek1"]').index();
                    for (let i = start; i < td_len; i++) {
                        if (td_text === '小计') {
                            isIncludes = m_td.eq(i).attr('data-field').indexOf('classHourWeek');
                            if (isIncludes > -1) {
                                m_td.eq(i).addClass('layui-total')
                            }
                            $(this).addClass('layui-tr-total')
                        } else {
                            isIncludes = m_td.eq(i).attr('data-field').indexOf('classHourWeek');
                            if (isIncludes > -1) {
                                m_td.eq(i).addClass('layui-week')
                            }
                        }
                    }
                })
                if (auditStatus === "0") {
                    getFormDataByAlias("218617", "exam_form", "examType");
                    getFormDataByAlias("yxsj", "yxsj_yxmc", "openDept");
                    $('.select-demo-primary').on('change', function () {
                        let tableCache = table.cache['courseTab'];  // 获得数据表格的缓存数据
                        let field = $(this).parents('td').attr('data-field');  // 获得下拉列表的父级td标签的字段名称
                        let dataIndex = parseInt($(this).parents('tr').attr('data-index'));  // 获得变化的下拉列表所在的行index
                        tableCache[dataIndex][field] = this.value;  // 将修改后的数据更新到数据表格的缓存数据中
                        window.localStorage.setItem("tableData[[${form.rowInfo.formUserId}]]", JSON.stringify(tableCache));
                    });
                }
                /* **************** 下拉内容 ************** */
                const options = this;
                // 获取当前行数据
                table.getRowData = function (elem) {
                    const index = $(elem).closest('tr').data('index');
                    return table.cache[options.id][index] || {};
                };
                // 合并合计列
                const w0 = $('.layui-table-total tr td[data-field="0"]').outerWidth(true);
                const w1 = $('.layui-table-total tr td[data-field="courseNature"]').outerWidth(true);
                const w2 = $('.layui-table-total tr td[data-field="chooseCompulsory"]').outerWidth(true);
                const w3 = $('.layui-table-total tr td[data-field="courseIndex"]').outerWidth(true);
                const w4 = $('.layui-table-total tr td[data-field="courseId"]').outerWidth(true);
                const w5 = $('.layui-table-total tr td[data-field="courseName"]').outerWidth(true);
                const w6 = $('.layui-table-total tr td[data-field="courseGroup"]').outerWidth(true);
                const total_width = w0 + w1 + w2 + w3 + w4 + w5 + w6; // 获取到总计里固定定位的单元格的总宽度
                // 给td都加上背景色，这样z-index才起作用
                $('.layui-table-total tr td').css('background-color', '#F2F2F2');
                $(".layui-table-total").after('<div class="amountTo" style="width:' + total_width + 'px"><span>合计</span></div>')
                let totalElements = $(".layui-table-total td[data-field^='classHourWeek'] div");
                let rows = $('.layui-table-box').find("tr");
                let courseCriterionCount = 0;
                for (let i = 0; i < week_len; i++) {
                    let total = 0;
                    let year = parseInt("[[${form?.pyfagl_nj}]]") + Math.floor(i / 2);
                    let semester = i % 2 + 1;
                    let term = year + "-" + (year + 1) + "-" + semester;
                    rows.each(function () {
                        let row = $(this);
                        let courseNature = row.find("td[data-field=courseNature] div").text();
                        let courseIndex = row.find("td[data-field=courseIndex] div").text();
                        let classHourWeek = row.find("td[data-field='classHourWeek" + (i + 1) + "'] div");
                        let textBook = row.find("td[data-field='textBook" + (i + 1) + "'] div");
                        let textBookVal = row.find("td[data-field='textBook" + (i + 1) + "'] div").text();
                        let totalClassHour = row.find("td[data-field='totalClassHour'] div");
                        ["theory", "practice", "experiment", "computer", "other"].forEach(field => {
                            row.find(`td[data-field=${field}] div`).attr("title", "此处为各类型学时数，标准格式为纯数字。学时分配之和需要等于课程总学时。");
                            let value = row.find(`td[data-field=${field}] div`).text();
                            if (value === "0") {
                                row.find(`td[data-field=${field}] div`).text("");
                            }
                        });
                        let val = parseInt(classHourWeek.text());
                        if (classHourWeek.text() === "0") {
                            classHourWeek.text("");
                        }
                        if (courseIndex === "小计" && val) {
                            val = classHourWeek.text() % 1 !== 0 ? parseFloat(classHourWeek.text()).toFixed(1) : parseInt(classHourWeek.text());
                            total += parseFloat(val);
                        }
                        if (textBookSetting.modifyTextbook === 0 && textBookVal) {
                            textBook.html("<span class=\"txt-more\" onmouseout=\"hideTB(event)\" onmouseenter=\"showTB(event)\">" + textBookVal + "</span>");
                        } else {
                            if (textBookVal && (!checkModifyPermission() || (textBookSetting.termRange && textBookSetting.termRange.indexOf(term) === -1))) {
                                textBook.html("<span class=\"txt-more\" onmouseout=\"hideTB(event)\" onmouseenter=\"showTB(event)\">" + textBookVal + "</span>");
                                return true;
                            }
                            if (auditStatus === "1") {
                                textBookVal = textBookVal ? textBookVal : "";
                                textBook.html("<span>" + textBookVal + "</span>");
                            } else {
                                if (textBookVal) {
                                    textBook.html("<span class=\"txt-blue\">" + textBookVal + "</span>");
                                } else if (!textBookVal && courseIndex !== "小计" && auditStatus === "0") {
                                    textBook.html("<span class=\"tab-link\"></span>");
                                }
                            }
                        }
                        if (courseIndex !== "小计") {
                            classHourWeek.attr("title", "此处为周学时数，整学期开课时（从学期开始周上到结束周），标准格式为纯数字。" +
                                "非整学期开课时，标准格式为：“周次:(英文冒号)学时”。其中周次如果为连续周用\"-\"（英文连字符）相连，例\"1-10:2\"表示1至10周每周2学时；不连续周次用\",\"（英文逗号）隔开，" +
                                "例\"1-3:2,4-4:3\"表示1至3周每周2学时，第4周3学时。两种符号可以结合使用。" +
                                "如果单双周上课，请输入周次单（双）:周学时，例如“1-10单:2,11-28双:2\"，表示1到10周单周上课，每周2课时，11到18周双周上课，每周2课时。");
                        }
                    });
                    totalElements.eq(i).text(total ? total : "");
                    $('#courseTab').next().find("th[data-field='classHourWeek" + (i + 1) + "'] div span").attr("weekCount", 18);
                    $.ajax({
                        type: "post",
                        url: "/cultivation/majorCourseSet/getSemesterWeek",
                        data: {fid: "[[${fid}]]", term: term},
                        dataType: 'json',
                        success: function (result) {
                            if (weekC && JSON.parse(weekC)["week" + (i + 1)]) {
                                let weekCount = JSON.parse(weekC)["week" + (i + 1)];
                                $('#courseTab').next().find("th[data-field='classHourWeek" + (i + 1) + "'] div span").attr("weekCount", weekCount);
                                $('#courseTab').next().find("th[data-field='week" + (i + 1) + "'] div span").text(weekCount + "周");
                                return;
                            }
                            if (result.data) {
                                let title = result.data.xnxq_jsz - result.data.xnxq_qsz + 1;
                                $('#courseTab').next().find("th[data-field='classHourWeek" + (i + 1) + "'] div span").attr("weekCount", title);
                                $('#courseTab').next().find("tr:eq(2) th").each(function () {
                                    let t = $(this).attr("title");
                                    if (t === term) {
                                        $(this).find("span").text(title + "周");
                                    }
                                })
                            }
                        }
                    });
                }
                rows.each(function () {
                    let row = $(this);
                    if (row.find("td[data-field=courseCriterion] div").text() === "是") {
                        courseCriterionCount++;
                    }
                })
                $(".layui-table-total td[data-field^='courseCriterion'] div").text(courseCriterionCount);
                if (!displaySetting.subtotal) {
                    $(".layui-tr-total").hide();
                }
            }
        });

        $("#tabBox").on('click', ".layui-table-view .layui-table-header thead  th[data-field^='week']", function (e) {
            if ($(this).find('.layui-input').length === 0) {
                const txt = $(this).find('span').text().replace('周', '');
                const term = $(this).attr('title');
                const idx = $(this).index() + 1;
                let start = 1;
                let end = 18;
                $(this).append('<input type="number" min="1" class="layui-input layui-table-edit">');
                $(this).find('.layui-input').focus().val(txt).blur(function () {
                    const val = $(this).val();
                    const reg = /^[1-9]\d*$/
                    if (!reg.test(val)) {
                        layer.tips('请填写正整数', this, {
                            tips: 1,
                            tipsMore: true,
                        })
                        return;
                    }
                    if (displaySetting.weekCountTerm || !displaySetting) {
                        $.ajax({
                            type: "post",
                            url: "/cultivation/majorCourseSet/getSemesterWeek",
                            data: {fid: "[[${fid}]]", term: term},
                            dataType: 'json',
                            async: false,
                            success: function (result) {
                                if (result.data) {
                                    start = result.data.xnxq_qsz;
                                    end = result.data.xnxq_jsz;
                                }
                            }
                        });
                        if (val > end || val < start) {
                            layer.tips('周数不能超出学年学期中设置的开始周和结束周', this, {
                                tips: 1,
                                tipsMore: true,
                            })
                            return;
                        }
                    }
                    if (/^[1-9]\d*$/.test(val)) {
                        $(this).prev().find('span').text(val + '周');
                        $(`th[data-field="classHourWeek${idx}"] span`).attr("weekCount", val);
                        $(this).remove();
                    }
                    let arr = layui.table.cache["courseTab"];
                    let hoursArrange = {};
                    for (let i = 0; i < week_len; i++) {
                        hoursArrange["week" + (i + 1)] = $(`th[data-field="classHourWeek${i + 1}"] span`).attr("weekCount");
                    }
                    window.localStorage.setItem("header[[${form.rowInfo.formUserId}]]", JSON.stringify(hoursArrange));
                    if (arr.length > 0) {
                        for (let i = 0; i < arr.length; i++) {
                            let simulatedData = arr[i];
                            if (simulatedData.courseIndex === "小计" || !simulatedData["classHourWeek" + idx]) {
                                continue;
                            }
                            let simulatedObj = {
                                value: simulatedData.classHourWeek1,
                                data: simulatedData,
                                field: "classHourWeek1",
                                index: i
                            };
                            onEditCallback(simulatedObj);
                        }
                    }
                });
            }
            e.stopPropagation();
        })

        // 单元格编辑后的事件
        table.on('edit(courseTab)', onEditCallback);

        // 同步版本的周学时处理函数（用于totalClassHour计算）
        async function handleWeekHourCalculationSync(obj, val, fieldName, weekCount) {
            const tableIns = table.cache['courseTab'];
            const rowData = tableIns[obj.index];
            const weekMatch = val.match(/(\d+)周/);
            if (!weekMatch) return 0;
            const extractedWeekCount = weekMatch[1];
            const $cell = $(`.layui-table tr[data-index=${obj.index}] td[data-field="${fieldName}"]`);
            let calculatedValue = $cell.data('calculated-value');
            if (calculatedValue) {
                return parseFloat(calculatedValue * extractedWeekCount * 5);
            }
            try {
                const result = await calculateWeekHourValueAsync(val, extractedWeekCount, fieldName);
                $cell.data('calculated-value', result);
                $cell.data('week-count', extractedWeekCount);
                $cell.find('div').addClass('week-hour-calculated');
                return parseFloat(result * extractedWeekCount * 5);;
            } catch (error) {
                console.error('周学时计算失败:', error);
                // 降级处理：使用周数 * 默认值
                const fallbackValue = weekCount * parseFloat(extractedWeekCount);
                $cell.data('calculated-value', fallbackValue);
                $cell.data('week-count', extractedWeekCount);
                return fallbackValue;
            }
        }

        // 异步版本的接口调用函数
        function calculateWeekHourValueAsync(originalValue, weekCount, fieldName) {
            return new Promise((resolve, reject) => {
                const fieldIndex = parseInt(fieldName.match(/\d+/)[0]) - 1;
                const baseYear = parseInt("[[${form?.pyfagl_nj}]]");
                const year = baseYear + Math.floor(fieldIndex / 2);
                const semester = fieldIndex % 2 + 1;
                const term = year + "-" + (year + 1) + "-" + semester;
                const apiUrl = '/cultivation/majorCourseSet/getTableStructure';
                $.ajax({
                    url: apiUrl,
                    type: 'POST',
                    data: {semester: term},
                    success: function (response) {
                        if (response && response.data) {
                            const calculatedValue = response.data.allCurricNums;
                            resolve(calculatedValue);
                        } else {
                            reject(new Error('接口返回错误: ' + JSON.stringify(response)));
                        }
                    },
                    error: function (xhr, status, error) {
                        reject(new Error('接口调用失败: ' + error));
                    }
                });
            });
        }

        async function onEditCallback(obj) {
            const fieldName = obj.field; // 得到修改的字段
            const data = obj.data // 得到所在行所有键值
            const val = obj.value;
            const tableIns = table.cache['courseTab'];
            if (fieldName.includes("notes")) {
                return false;
            }
            // 计算分数总和，并更新总计行中的数据
            if (fieldName.includes("classHourWeek")) {
                $(`.layui-table tr[data-index=${obj.index}] td[data-field="${fieldName}"] div`).text(convertRange(val));
                tableIns[obj.index][fieldName] = convertRange(val);
            }
            if (checkTxtFormat()) {
                return false;
            }
            let totalValue = 0;
            let idx = -1;
            let classHour = 0;
            let credit = 0;
            if (fieldName.includes("classHourWeek") || fieldName.includes("extraClassHour")) {
                let totalClassHour = 0;
                let hoursArrange = {};
                let credit = 0;
                let creditFlag = true;
                for (let i = 0; i < week_len; i++) {
                    let weekCount = parseInt($(`th[data-field="week${i + 1}"] span`).text().replace('周', ''));
                    let val = tableIns[obj.index][`classHourWeek${i + 1}`];
                    hoursArrange[`classHourWeek${i + 1}`] = val || 0;
                    let num = 0;
                    // 处理包含"周"字符的值的特殊逻辑
                    if (typeof val === "string" && val.includes("周")) {
                        num = await handleWeekHourCalculationSync(obj, val, `classHourWeek${i + 1}`, weekCount);
                    } else if ($.isNumeric(val)) {
                        num = weekCount * val;
                    } else if (typeof val === "string" && val.includes("-")) {
                        creditFlag = false;
                        num = val.split(",").reduce((accumulator, v) => {
                            let [start, end, hour] = v.split(":")[0].split("-").concat(v.split(":")[1]);
                            return accumulator + ((end - start + 1) * hour);
                        }, 0);
                    }
                    totalClassHour += num;
                    if ($.isNumeric(val)) {
                        credit = credit + parseInt(val);
                    }
                    if (!val) {
                        $(`div[lay-id="courseTab"] tr[data-index=${obj.index}] td[data-field="textBook${i + 1}"] div`).html("<span class=\"tab-link\"></span>");
                    } else {
                        $(`div[lay-id="courseTab"] tr[data-index=${obj.index}] td[data-field="textBook${i + 1}"] div`).text(tableIns[obj.index][`textBook${i + 1}`]);
                    }
                }
                tableIns[obj.index].hoursArrange = JSON.stringify(hoursArrange);
                tableIns[obj.index].totalClassHour = totalClassHour;
                if (textBookSetting.defaultCredit || "[[${fid}]]" === "18855") {
                    credit = !creditFlag ? 0 : credit;
                    tableIns[obj.index].credit = credit;
                    $(`.layui-table tr[data-index=${obj.index}] td[data-field="credit"] div`).text(credit);
                }
                let extraClassHour = $(`.layui-table tr[data-index=${obj.index}] td[data-field="extraClassHour"] div`).text();
                extraClassHour = extraClassHour ? parseFloat(Number(extraClassHour).toFixed(1)) : 0
                totalClassHour = totalClassHour + extraClassHour;
                totalClassHour = Number.isInteger(totalClassHour) ? totalClassHour : totalClassHour.toFixed(1);
                $(`.layui-table tr[data-index=${obj.index}] td[data-field="totalClassHour"] div`).text(totalClassHour);
                tableIns[obj.index].totalClassHour = totalClassHour;
            }
            tableIns[obj.index].hoursDistribute = JSON.stringify({
                theory: data.theory,
                practice: data.practice,
                experiment: data.experiment,
                computer: data.computer,
                other: data.other
            });
            let subtotalsIndexes = tableIns.reduce((indexes, element, index) => {
                if (element.courseIndex === "小计") {
                    indexes.push(index);
                }
                return indexes;
            }, []);

            let interval = subtotalsIndexes.findIndex(value => obj.index < value);
            let start = subtotalsIndexes[interval - 1] ? subtotalsIndexes[interval - 1] + 1 : 0;
            for (let i = start; i < tableIns.length; i++) {
                const item = tableIns[i];
                if (item.courseIndex === "小计") {
                    idx = i;
                    break;
                }
                let num = item[fieldName];
                if (typeof num === "string" && num.includes("-") && fieldName.includes("classHourWeek")) {
                    num = num.split(",").reduce((acc, v) => {
                        return acc + parseFloat(v.split(":")[1] || 0);
                    }, 0).toFixed(1);
                }
                if ($.isNumeric(num)) {
                    num = Number(num);
                    totalValue += num % 1 !== 0 ? parseFloat(num.toFixed(1)) : parseInt(num) || 0;
                }
                classHour += parseFloat(Number(item.totalClassHour).toFixed(1)) || 0;
                credit += parseFloat(Number(item.credit).toFixed(1)) || 0;
            }

            if (idx !== -1) {
                totalValue = Number.isInteger(totalValue) ? totalValue : totalValue.toFixed(1);
                credit = Number.isInteger(credit) ? credit : credit.toFixed(1);
                classHour = Number.isInteger(classHour) ? classHour : classHour.toFixed(1);
                tableIns[idx][fieldName] = totalValue;
                tableIns[idx]["totalClassHour"] = classHour;
                $(`.layui-table tr[data-index=${idx}] td[data-field=${fieldName}] div`).text(totalValue);
                $(`.layui-table tr[data-index=${idx}] td[data-field="totalClassHour"] div`).text(classHour);
                tableIns[idx]["credit"] = credit;
                $(`.layui-table tr[data-index=${idx}] td[data-field="credit"] div`).text(credit);
            }
            let courseTab = $('#courseTab');
            const fields = [
                {name: 'credit', total: 0, selector: ".layui-table-total td[data-field='credit'] div"},
                {name: 'theory', total: 0, selector: ".layui-table-total td[data-field='theory'] div"},
                {name: 'practice', total: 0, selector: ".layui-table-total td[data-field='practice'] div"},
                {name: 'experiment', total: 0, selector: ".layui-table-total td[data-field='experiment'] div"},
                {name: 'computer', total: 0, selector: ".layui-table-total td[data-field='computer'] div"},
                {name: 'other', total: 0, selector: ".layui-table-total td[data-field='other'] div"},
                {name: 'totalClassHour', total: 0, selector: ".layui-table-total td[data-field='totalClassHour'] div"},
                {name: 'extraClassHour', total: 0, selector: ".layui-table-total td[data-field='extraClassHour'] div"}
            ];
            const rows = courseTab.next().find(".layui-table-main tr");
            for (let i = 0; i < week_len; i++) {
                let totalClassHourWeek = 0;
                $(`.layui-table td[data-field='classHourWeek${i + 1}']`).each(function () {
                    const txt = $(this).parent().find("td[data-field='courseIndex']").text();
                    let classHourWeek = $(this).text();
                    if (txt === "小计" && classHourWeek) {
                        totalClassHourWeek += parseFloat(classHourWeek);
                    }
                });
                totalClassHourWeek = Number.isInteger(totalClassHourWeek) ? totalClassHourWeek : totalClassHourWeek.toFixed(1);
                $(`.layui-table-total td[data-field='classHourWeek${i + 1}'] div`).text(totalClassHourWeek);
            }
            rows.each(function () {
                const row = $(this);
                const courseIndex = row.find("td[data-field=courseIndex] div").text();
                if (courseIndex === "小计") {
                    fields.forEach(field => {
                        const value = Number(row.find(`td[data-field='${field.name}'] div`).text().trim());
                        if (!isNaN(value)) {
                            field.total += value;
                            $(field.selector).text(field.total % 1 === 0 ? field.total : field.total.toFixed(1));
                        }
                    });
                }
            });
            let subIdx = 0;
            let total = $(".layui-table-total td[data-field='totalClassHour'] div").text();
            rows.each(function () {
                const row = $(this);
                const courseIndex = row.find("td[data-field=courseIndex] div").text();
                if (courseIndex === "小计") {
                    let value = row.find("td[data-field='totalClassHour'] div").text();
                    let percent = total != 0 && value ? Math.round((value / total) * 100 * 100) / 100 + "%" : 0;
                    $(`.layui-table tr[data-index=${subIdx}] td[data-field="courseName"] div`).text(percent);
                    tableIns[subIdx]["courseName"] = percent;
                }
                subIdx++;
            })
            window.localStorage.setItem("tableData[[${form.rowInfo.formUserId}]]", JSON.stringify(tableIns));
            //table.reload('courseTab', {data: tableIns});
        }

        //排序
        table.on('sort(courseTab)', function (obj) {
            resultData = [];
            courseTabData = JSON.parse(window.localStorage.getItem(`tableData[[${form.rowInfo.formUserId}]]`));
            subtotal(obj.type);
            table.reload('courseTab', {data: resultData});
        });

        function convertRange(input) {
            if (!input) {
                return "";
            }
            const ranges = input.split(',');
            let allResults = [];
            ranges.forEach(range => {
                const match = range.match(/^(\d+)-(\d+)(单|双):(\d+)$/);
                if (!match) {
                    allResults.push(range);
                    return;
                }
                const start = parseInt(match[1], 10);
                const end = parseInt(match[2], 10);
                const parity = match[3];
                const value = match[4];
                let results = [];
                for (let i = start; i <= end; i++) {
                    if ((parity === '单' && i % 2 !== 0) || (parity === '双' && i % 2 === 0)) {
                        results.push(`${i}-${i}:${value}`);
                    }
                }
                allResults.push(results.join(','));
            });

            return allResults.join(',');
        }

        // 工具栏事件
        var editObj, othis;
        table.on('tool(courseTab)', function (obj) {
            othis = lay(this);
            if (obj.event === 'del') {
                layer.confirm('真的删除该行吗？', function (index) {
                    layer.close(index);
                    //删除选中元素
                    selCourseAry = $.grep(selCourseAry, function (item) {
                        return item.courseId !== obj.courseId;
                    });
                    if (obj.data.id) {
                        $.post('../../cultivation/majorCourseSet/delDataById', {
                            ids: obj.data.id,
                            grade: "[[${form?.pyfagl_nj}]]",
                            major: "[[${form?.pyfagl_zybh}]]"
                        }, function (result) {
                        });
                    }
                    let dataArray = layui.table.cache["courseTab"];
                    let result = [], group = [];
                    $.each(dataArray, function (index, item) {
                        if (item.courseId !== obj.data.courseId) group.push(item);
                        if (item.courseIndex === "小计" || index === dataArray.length - 1) {
                            if (group.length === 1 && group[0].courseIndex === "小计") {
                                group = [];
                            } else {
                                result = result.concat(group);
                                group = [];
                            }
                        }
                    });
                    resultData = result;
                    table.reload('courseTab', {data: result});
                    window.localStorage.setItem("tableData[[${form.rowInfo.formUserId}]]", JSON.stringify(result));
                });
            } else if (obj.event === 'selTB') {
                if ($(this).find("span").attr("class") !== "txt-blue" && $(this).find("span").attr("class") !== "tab-link") {
                    return false;
                }
                table.render({
                    elem: '#textbookList',
                    url: '/cultivation/majorCourseSet/getMaterialInfoData',
                    where: {deptId: "[[${fid}]]", courseNo: courseNo},
                    height: 480,
                    page: true,
                    cols: [
                        [{
                            type: 'checkbox',
                            width: 80,
                        }, {
                            field: 'jc_jcmc',
                            title: '教材名称',
                            align: "center",
                            width: 372,
                        }, {
                            field: 'jc_jcbh',
                            title: '教材号',
                            align: "center",
                            width: 372
                        }, {
                            field: 'jc_isbn',
                            title: '出版号ISBN',
                            align: "center",
                            width: 372
                        }
                        ],
                    ],
                    done: function (res, curr, count) {
                        $("#selTextbookCount").attr("count", res.count);
                        if ($("#selTextbook .checked").length > 0) {
                            $('div[lay-id="textbookList"] input[type=checkbox]').prop("checked", true);
                            $('div[lay-id="textbookList"] .layui-form-checkbox').addClass("layui-form-checked");
                            return false;
                        }
                        uniqueArray = $.grep(selDataArray, function (item, index) {
                            return index === $.inArray(item.jc_jcbh, $.map(selDataArray, function (obj) {
                                return obj.jc_jcbh;
                            }));
                        });
                        if (!res.data) {
                            return false;
                        }
                        for (let i = 0; i < res.data.length; i++) {
                            for (let j = 0; j < uniqueArray.length; j++) {
                                //数据id和要勾选的id相同时checkbox选中
                                if (res.data[i].jc_jcbh === uniqueArray[j].jc_jcbh) {
                                    //这里才是真正的有效勾选
                                    res.data[i]["LAY_CHECKED"] = 'true';
                                    //找到对应数据改变勾选样式，呈现出选中效果
                                    const index = res.data[i]['LAY_TABLE_INDEX'];
                                    $('div[lay-id="textbookList"] tr[data-index=' + index + '] input[type="checkbox"]').prop('checked', true).next().addClass('layui-form-checked');
                                }
                            }
                        }
                    }
                });
                layer.open({
                    type: 1,
                    title: false,
                    closeBtn: false,
                    shadeClose: true,
                    isOutAnim: true,
                    content: $('#selTextbook'),
                    area: ['auto', 'auto'],
                    success: function () {
                        $("#checkAll").removeClass('checked');
                        $("#selTextbookCount em").text(0);
                        selDataArray = [];
                        $('div[lay-id="textbookList"] input[type=checkbox]').prop("checked", false);
                        $('div[lay-id="textbookList"] .layui-form-checkbox').removeClass("layui-form-checked");
                        table.reload('textbookList');
                    },
                });
            } else if (obj.event === "replace") {
                $("#replaceCourse").attr("index", obj.index);
                table.render({
                    elem: '#replaceCourseList',
                    url: '/processData/getCourseLibData?formUserId=[[${form.rowInfo.formUserId}]]&deptId=[[${fid}]]', // 此处为静态模拟数据，实际使用时需换成真实接口
                    height: '471',
                    cols: [
                        [{
                            type: 'radio',
                            fixed: 'left',
                            width: 80,
                        }, {
                            field: 'kck_kcbh',
                            title: '[[${kck_kcbh}]]',
                            width: 125,
                            align: "center",

                        }, {
                            field: 'kck_kcmc',
                            title: '[[${kck_kcmc}]]',
                            align: "center",

                        }, {
                            field: 'kck_kcxz',
                            title: '[[${kck_kcxz}]]',
                            align: "center",
                        }, {
                            field: 'kck_kcsx',
                            title: kck_kcsx,
                            align: "center",
                        }, {
                            field: 'kck_xf',
                            title: '[[${kck_xf}]]',
                            // width: 80,
                            align: "center",

                        }, {
                            field: 'kck_zxs',
                            title: '[[${kck_zxs}]]',
                            align: "center",

                        }, {
                            field: 'kck_ksxs',
                            title: '[[${kck_ksxs}]]',
                            align: "center"
                        }, {
                            field: 'kck_kkyx',
                            title: '[[${kck_kkyx}]]',
                            align: "center"
                        }, {
                            field: 'kck_kkjys',
                            title: '[[${kck_kkjys}]]',
                            align: "center"
                        }, {
                            field: 'rowInfo',
                            hide: true
                        }, {
                            field: 'kck_kcfl',
                            hide: true
                        }, {
                            field: 'kck_kclx',
                            hide: true
                        }, {
                            field: 'kck_kclb',
                            hide: true
                        }, {
                            field: 'kck_kcsxnew',
                            hide: true
                        }, {
                            field: 'kck_fjkcmc',
                            hide: true
                        }, {
                            field: 'courseGroup',
                            hide: true
                        }, {
                            field: 'purePractical',
                            hide: true
                        }]
                    ],
                    done: function (res, curr, count) {
                        const tdw = $('div[lay-id="replaceCourseList"] .layui-table-main td[data-field="kck_kcmc"]').width();
                        $('div[lay-id="replaceCourseList"] .layui-table-header th[data-field="kck_kcmc"] .laytable-cell-2-0-2').width(tdw - 20);
                    }
                });
                layer.open({
                    type: 1,
                    title: false,
                    closeBtn: false,
                    isOutAnim: true,
                    content: $('#replaceCourse'),
                    area: ['auto', 'auto'],
                    success: function () {
                        const tdw = $('div[lay-id="replaceCourseList"] .layui-table-main td[data-field="kck_kcmc"]').width();
                        $('div[lay-id="replaceCourseList"] .layui-table-header th[data-field="kck_kcmc"] .laytable-cell-2-0-2').width(tdw - 20);
                    },
                });
                const field = {};
                field.kck_kcxz = "";
                field.kck_kcsx = "";
                field.kck_kkyx = "";
                field.kck_kcmc = "";
                field.kck_kkjys = "";
                table.reload('replaceCourseList', {
                    where: field, page: {
                        prev: "上一页",
                        next: "下一页",
                        curr: 1,
                        limit: 10,
                        layout: ['prev', 'page', 'next', 'skip', 'limit']
                    }
                });
            }
            editObj = obj;
        });

        /* ******************* 添加课程 ******************* */
        $("#addCourseBtn").click(function () {
            table.render({
                elem: '#courseList',
                url: '/processData/getCourseLibData?formUserId=[[${form.rowInfo.formUserId}]]&deptId=[[${fid}]]', // 此处为静态模拟数据，实际使用时需换成真实接口
                height: '471',
                cols: [
                    [{
                        type: 'checkbox',
                        fixed: 'left',
                        width: 80,
                    }, {
                        field: 'kck_kcbh',
                        title: '[[${kck_kcbh}]]',
                        width: 125,
                        align: "center",

                    }, {
                        field: 'kck_kcmc',
                        title: '[[${kck_kcmc}]]',
                        align: "center",

                    }, {
                        field: 'kck_kcxz',
                        title: '[[${kck_kcxz}]]',
                        align: "center",
                    }, {
                        field: 'kck_kcsx',
                        title: kck_kcsx,
                        align: "center",
                    }, {
                        field: 'kck_xf',
                        title: '[[${kck_xf}]]',
                        // width: 80,
                        align: "center",

                    }, {
                        field: 'kck_zxs',
                        title: '[[${kck_zxs}]]',
                        align: "center",

                    }, {
                        field: 'kck_ksxs',
                        title: '[[${kck_ksxs}]]',
                        align: "center"
                    }, {
                        field: 'kck_kkyx',
                        title: '[[${kck_kkyx}]]',
                        align: "center"
                    }, {
                        field: 'kck_kkjys',
                        title: '[[${kck_kkjys}]]',
                        align: "center"
                    }, {
                        field: 'rowInfo',
                        hide: true
                    }, {
                        field: 'kck_kcfl',
                        hide: true
                    }, {
                        field: 'kck_kclx',
                        hide: true
                    }, {
                        field: 'kck_kclb',
                        hide: true
                    }, {
                        field: 'kck_kcsxnew',
                        hide: true
                    }, {
                        field: 'kck_fjkcmc',
                        hide: true
                    }, {
                        field: 'courseGroup',
                        hide: true
                    }, {
                        field: 'kck_sfcsjhj',
                        hide: true
                    }]
                ],
                done: function (res, curr, count) {
                    const tdw = $('div[lay-id="courseList"] .layui-table-main td[data-field="kck_kcmc"]').width();
                    $('div[lay-id="courseList"] .layui-table-header th[data-field="kck_kcmc"] .laytable-cell-2-0-2').width(tdw - 20);
                    setCheckStatus();
                    $("#selCourse span i").text(res.count);
                    if ($("#selCourse .checked").length > 0) {
                        res.data.forEach((item) => {
                            item.LAY_CHECKED = true;
                            const index = item.LAY_INDEX;
                            $('div[lay-id="courseList"] tr[data-index=' + index + '] input[type="checkbox"]').prop('checked', true).next().addClass('layui-form-checked');
                        })
                        return false;
                    }
                    for (let i = 0; i < res.data.length; i++) {
                        for (let j = 0; j < selCourseAry.length; j++) {
                            //数据id和要勾选的id相同时checkbox选中
                            if (res.data[i].kck_kcbh === selCourseAry[j].kck_kcbh) {
                                //这里才是真正的有效勾选
                                res.data[i]["LAY_CHECKED"] = 'true';
                                //找到对应数据改变勾选样式，呈现出选中效果
                                const index = res.data[i]['LAY_INDEX'];
                                $('div[lay-id="courseList"] tr[data-index=' + index + '] input[type="checkbox"]').prop('checked', true).next().addClass('layui-form-checked');
                            }
                        }
                        $.post('/cultivation/majorCourseSet/getMajorCourseNature', {
                            fid: "[[${fid}]]",
                            majorCode: "[[${form?.pyfagl_zybh}]]",
                            courseId: res.data[i].kck_kcbh
                        }, function (result) {
                            if (result && result.zykcxzwh_kcxz) {
                                res.data[i].kck_kcxz = result.zykcxzwh_kcxz;
                                $('div[lay-id="courseList"] .layui-table-main tr[data-index=' + res.data[i]['LAY_INDEX'] + '] td[data-field="kck_kcxz"] div').text(result.zykcxzwh_kcxz);
                            }
                        });
                    }
                }
            });
            layer.open({
                type: 1,
                title: false,
                closeBtn: false,
                isOutAnim: true,
                content: $('#addCourse'),
                area: ['auto', 'auto'],
                success: function () {
                    const tdw = $('div[lay-id="courseList"] .layui-table-main td[data-field="kck_kcmc"]').width();
                    $('div[lay-id="courseList"] .layui-table-header th[data-field="kck_kcmc"] .laytable-cell-2-0-2').width(tdw - 20);
                },
            });
            const field = {};
            field.kck_kcxz = "";
            field.kck_kcsx = "";
            field.kck_kkyx = "";
            field.kck_kcmc = "";
            field.kck_kkjys = "";
            table.reload('courseList', {
                where: field, page: {
                    prev: "上一页",
                    next: "下一页",
                    curr: 1,
                    limit: 10,
                    layout: ['prev', 'page', 'next', 'skip', 'limit']
                }
            });
        });

        //删除课程
        $("#delCourseBtn").click(function () {
            let data = layui.table.checkStatus('courseTab').data;
            if (data.length === 0) {
                layer.msg("请选择要删除的数据", {icon: 2, time: 3000});
                return false;
            }
            let dataArray = layui.table.cache["courseTab"];
            layer.confirm('真的删除该行吗？', function (index) {
                layer.close(index);
                let ids = [];
                for (let i = 0; i < data.length; i++) {
                    let id = data[i].id;
                    if (id) {
                        ids.push(id);
                    }
                }
                if (ids.length > 0) {
                    $.post('../../cultivation/majorCourseSet/delDataById', {
                        ids: ids.join(","),
                        grade: "[[${form?.pyfagl_nj}]]",
                        major: "[[${form?.pyfagl_zybh}]]"
                    }, function (result) {
                    });
                }
                let result = [], group = [];
                $.each(dataArray, function (index, item) {
                    const match = data.some(function (deleteItem) {
                        return item.courseId === deleteItem.courseId;
                    });
                    if (!match) group.push(item);
                    if (item.courseIndex === "小计" || index === data.length - 1) {
                        if (group.length === 1 && group[0].courseIndex === "小计") {
                            group = [];
                        } else {
                            result = result.concat(group);
                            group = [];
                        }
                    }
                });
                resultData = result;
                table.reload('courseTab', {data: result});
                window.localStorage.setItem("tableData[[${form.rowInfo.formUserId}]]", JSON.stringify(result));
            });
        })

        //发布
        $("#saveSetting").click(function () {
            $("#offerCourseDialog,.marker").show();
        })
        $("#offerCourseDialog .btn-cancel,.close-btn").click(function () {
            $("#offerCourseDialog,.marker").hide();
        })
        $("#offerCourseDialog .btn-sure").click(function () {
            $("#offerCourseDialog").hide();
            release(1);
        })
        //保存
        $("#saveTeachPlan").click(function () {
            $(".marker").show();
            release(2);
        })

        $("#tipBtn").click(function () {
            location.reload();
        })

        async function release(type) {
            let dataArray = layui.table.cache["courseTab"];
            dataArray = dataArray.filter(data => {
                return data.courseIndex !== "小计" && Object.keys(data).length > 0;
            });
            const newArray = dataArray.map((obj) => {
                if (obj.length === 0) {
                    return true;
                }
                const hoursArrange = {};
                const textBook = {};
                const parsedTextBook = obj.textBook ? $.parseJSON(obj.textBook) : "";
                let total = 0;
                for (let j = 0; j < week_len; j++) {
                    let classHourWeek = obj["classHourWeek" + (j + 1)];
                    const week = $('#courseTab').next().find("th[data-field='week" + (j + 1) + "'] span").text().replace('周', '');
                    total += week * classHourWeek;
                    hoursArrange["classHourWeek" + (j + 1)] = classHourWeek || 0;
                    hoursArrange["week" + (j + 1)] = week;
                    if (parsedTextBook) {
                        textBook["textBook" + (j + 1) + "jc_jcmc"] = classHourWeek !== 0 && classHourWeek ? parsedTextBook["textBook" + (j + 1) + "jc_jcmc"] || "" : "";
                        textBook["textBook" + (j + 1) + "jc_jcbh"] = classHourWeek !== 0 && classHourWeek ? parsedTextBook["textBook" + (j + 1) + "jc_jcbh"] || "" : "";
                        textBook["textBook" + (j + 1) + "jc_isbn"] = classHourWeek !== 0 && classHourWeek ? parsedTextBook["textBook" + (j + 1) + "jc_isbn"] || "" : "";
                    }
                }
                const hoursDistribute = {
                    theory: obj.theory,
                    practice: obj.practice,
                    experiment: obj.experiment,
                    computer: obj.computer,
                    other: obj.other
                };
                return {
                    ...obj,
                    formUserId: "[[${form.rowInfo.formUserId}]]",
                    fid: "[[${fid}]]",
                    hoursDistribute: hoursDistribute,
                    hoursArrange: hoursArrange,
                    textBook: textBook
                };
            });
            if (checkTxtFormat()) {
                $(".marker").hide();
                return false;
            }
            if (displaySetting.verifyTotalClassHours && subCheck(newArray)) {
                $(".marker").hide();
                return false;
            }
            if (displaySetting.verifyTotalClassHours && await checkHour()) {
                $(".marker").hide();
                return false;
            }
            let loading = layer.load(1);
            const uniqueArray = removeDuplicatesByProperty(newArray, 'courseId');
            $.post('/cultivation/majorCourseSet/saveSetting', {
                dataArray: JSON.stringify(uniqueArray),
                formUserId: "[[${form.rowInfo.formUserId}]]"
            }, function (result) {
                let msg = type === 1 ? "发布成功<br/>计划课程已同步至该专业培养方案管理" : "保存成功";
                if (result.code !== 200) {
                    msg = type === 1 ? "发布失败" : "保存失败";
                    $("#setTipDialog").show().find("p").text(msg);
                    return false;
                }
                if (type) {
                    $("#setTipDialog img").attr("src", "/images/form/icon-success.png");
                    $("#setTipDialog").show().find("p").html(msg);
                } else {
                    location.reload();
                }
                if (type === 1) {
                    syncSettingToForm();
                    syncDelToForm();
                }
                layer.close(loading);
            }, "json");
        }

        const removeDuplicatesByProperty = (array, property) =>
            array.reduce((uniqueArray, item) => {
                const value = item[property];
                return uniqueArray.some(obj => obj[property] === value) ? uniqueArray : [...uniqueArray, item];
            }, []);

        //同步专业课信息
        function syncSettingToForm() {
            $.post('/cultivation/majorCourseSet/syncSettingToForm',
                {
                    openDept: "[[${form?.pyfagl_suxb}]]",
                    major: "[[${form?.pyfagl_zy}]]",
                    majorNumber: "[[${form?.pyfagl_zybh}]]",
                    level: "[[${form?.pyfagl_pycc}]]",
                    formUserId: "[[${form.rowInfo.formUserId}]]",
                    grade: "[[${form?.pyfagl_nj}]]",
                    fid: "[[${fid}]]",
                    uid: "[[${uid}]]",
                }, function (result) {
                    if (result.code === 200) {
                        //layer.msg("同步完成");
                    } else {
                        layer.msg(result.message);
                    }
                });
        }

        function syncDelToForm() {
            $.post('/cultivation/majorCourseSet/syncDelToForm',
                {
                    openDept: "[[${form?.pyfagl_suxb}]]",
                    major: "[[${form?.pyfagl_zy}]]",
                    majorNumber: "[[${form?.pyfagl_zybh}]]",
                    level: "[[${form?.pyfagl_pycc}]]",
                    formUserId: "[[${form.rowInfo.formUserId}]]",
                    grade: "[[${form?.pyfagl_nj}]]",
                    fid: "[[${fid}]]",
                    uid: "[[${uid}]]",
                }, function (result) {
                    if (result.code === 200) {
                        //layer.msg("同步完成");
                    } else {
                        layer.msg(result.message);
                    }
                });
        }


        //导出教学计划表
        $("#exportTeachPlan").click(function () {
            $("#exportDialog,.marker").show();
        })

        $("#exportDialog .btn-cancel,.close-btn").click(function () {
            $("#exportDialog,.marker").hide();
        })
        $("#exportDialog .btn-sure").click(function () {
            $("#exportDialog,.marker").hide();
            exportTeachPlan();
        })

        function exportTeachPlan() {
            layer.load(0, {shade: [0.1, '#fff']});
            $.post('/cultivation/majorCourseSet/exportTeachPlan', {
                fid: "[[${fid}]]",
                formUserId: "[[${form.rowInfo.formUserId}]]",
                year: week_len
            }, function (response) {
                // 关闭加载提示
                layer.closeAll('loading');
                if (response.code !== 200) {
                    layer.msg(response.msg, {icon: 2, time: 3000});
                    return false;
                }
                fetch("/down/" + response.data.file + ".xls")
                    .then(response => response.blob())
                    .then(blob => {
                        // 创建 Blob 对象
                        const url = window.URL.createObjectURL(blob);

                        // 创建隐藏的 a 标签，并设置属性
                        const downloadLink = document.createElement('a');
                        downloadLink.style.display = 'none';
                        downloadLink.href = url;
                        downloadLink.download = "[[${form.pyfagl_nj}]]级[[${form.pyfagl_zy}]]专业教学进程表.xls";
                        // 将 a 标签加入到页面中并触发点击事件
                        document.body.appendChild(downloadLink);
                        downloadLink.click();

                        // 完成后移除下载链接
                        document.body.removeChild(downloadLink);
                    });
            }, "json");
        }

        // 渲染

        // 筛选
        // 提交事件
        form.on('submit(selTable)', function (data) {
            const field = data.field; // 获取表单字段值
            field.kck_kcxz = field.kck_kcxz === "请选择" || !field.kck_kcxz ? "" : field.kck_kcxz;
            field.kck_kkyx = field.kck_kkyx === "请选择" || !field.kck_kkyx ? "" : field.kck_kkyx;
            field.kck_kcsx = field.kck_kcsx === "请选择" || !field.kck_kcsx ? "" : field.kck_kcsx;
            field.kck_kkjys = field.kck_kkjys === "请选择" || !field.kck_kkjys ? "" : field.kck_kkjys;
            // 重新加载table
            table.reload('courseList', {where: field, page: {curr: 1}});
            return false; // 阻止默认 form 跳转
        });
        //清空
        $("#addCourse button[type='reset']").click(function () {
            const field = {};
            field.kck_kcxz = "";
            field.kck_kcsx = "";
            field.kck_kkyx = "";
            field.kck_kcmc = "";
            table.reload('courseList', {where: field, page: {curr: 1}});
        })
        // 选择课程
        let selCourseAry = [];
        table.on('checkbox(courseList)', function (obj) {
            const checkStatus = table.checkStatus('courseList');
            let dataArray = layui.table.cache["courseList"];
            checkStatus.data.forEach(function (obj) {
                if (!selCourseAry.some(function (item) {
                    return item.kck_kcbh === obj.kck_kcbh;
                })) {
                    selCourseAry.push(obj);
                }
            });
            if (!obj.checked) {
                selCourseAry = selCourseAry.filter(function (data) {
                    return data.kck_kcbh !== obj.data.kck_kcbh;
                });
                if (obj.type === "all") {
                    selCourseAry = selCourseAry.filter(function (item1) {
                        return !dataArray.some(function (item2) {
                            return item1.kck_kcbh === item2.kck_kcbh;
                        });
                    });
                }
            }
            $("#selCourse em").text(selCourseAry.length);
        });
        // 确定
        $("#sureBtn").click(function () {
            resultData = [];
            courseTabData = selCourseAry.map((selCourse, index) => {
                const {
                    kck_kcxz, kck_kcbh, kck_kcmc, kck_xf, kck_kcsx, kck_zxs,
                    kck_ksxs, kck_kkyx, kck_llxs, kck_sjxs, kck_syxs, kck_shangjxs,
                    kck_qtxs, kck_mzxs, rowInfo, kck_sfcsjhj, kck_sfykb, kck_jc, kck_kcfl,
                    kck_kclx, kck_kclb, kck_kcsxnew, kck_fjkcmc, courseGroup
                } = selCourse;
                const data = {
                    courseIndex: index + 1,
                    courseNature: kck_kcxz,
                    courseId: kck_kcbh,
                    courseName: kck_kcmc,
                    credit: kck_xf || 0,
                    chooseCompulsory: kck_kcsx,
                    totalClassHour: 0,
                    examType: kck_ksxs,
                    openDept: kck_kkyx,
                    theory: kck_llxs,
                    practice: displaySetting.practice ? kck_sjxs : 0,
                    experiment: displaySetting.experiment ? kck_syxs : 0,
                    computer: displaySetting.computer ? kck_shangjxs : 0,
                    other: displaySetting.other ? kck_qtxs : 0,
                    courseCriterion: kck_sfykb,
                    courseFormUserId: rowInfo.formUserId,
                    practiceFlag: kck_sfcsjhj,
                    courseClassify: kck_kcfl,
                    courseType: kck_kclx,
                    courseCategory: kck_kclb,
                    courseAttribute: kck_kcsxnew,
                    courseParentName: kck_fjkcmc,
                    courseGroup: courseGroup,
                    purePractical: kck_sfcsjhj === "是",
                    editable: true
                };
                let textBook = {};
                let hoursArrange = {};
                let totalClassHour = 0;
                for (let j = 0; j < week_len; j++) {
                    let weekCount = parseInt($(`th[data-field="classHourWeek${j + 1}"] span`).attr("weekCount"));
                    let num = $.isNumeric(kck_mzxs) ? weekCount * kck_mzxs : 0;
                    if (typeof kck_mzxs === "string" && kck_mzxs.includes("-")) {
                        num = kck_mzxs.split(",").reduce((accumulator, v) => {
                            let [start, end, hour] = v.split(":")[0].split("-").concat(v.split(":")[1]);
                            return accumulator + ((end - start + 1) * hour);
                        }, 0);
                    }
                    totalClassHour += num;
                    data["classHourWeek" + (j + 1)] = kck_mzxs === "0" ? "" : kck_mzxs;
                    hoursArrange["classHourWeek" + (j + 1)] = kck_mzxs === "0" ? "" : kck_mzxs;
                    if (kck_jc && textBookSetting.range === "0") {
                        let filteredResults = kck_jc.filter(obj => obj.kck_syxnxq && obj.kck_syxnxq.includes((j + 1 + "")));
                        data["textBook" + (j + 1)] = filteredResults.map(info => info.kck_jcmc).join(',');
                        textBook["textBook" + (j + 1) + "jc_jcmc"] = filteredResults.map(info => info.kck_jcmc).join(',');
                        textBook["textBook" + (j + 1) + "jc_jcbh"] = filteredResults.map(info => info.kck_jch).join(',');
                        textBook["textBook" + (j + 1) + "jc_isbn"] = filteredResults.map(info => info.kck_isbn).join(',');
                    }
                }
                data["totalClassHour"] = totalClassHour;
                data["hoursDistribute"] = JSON.stringify({
                    theory: data.theory,
                    practice: data.practice,
                    experiment: data.experiment,
                    computer: data.computer,
                    other: data.other
                });
                data["hoursArrange"] = JSON.stringify(hoursArrange);
                if (textBookSetting.range === "0") {
                    data["textBook"] = JSON.stringify(textBook);
                }
                return data;
            });
            let dataArray = layui.table.cache["courseTab"];
            dataArray = dataArray.filter(data => {
                return data.courseIndex !== "小计";
            });
            courseTabData = courseTabData.concat(dataArray);
            //过滤重复数据
            courseTabData = Object.values(courseTabData.reduce((acc, cur) => {
                acc[cur.courseId] = cur;
                return acc;
            }, {}));
            $("#addCourse").hide();
            layer.closeAll();
            subtotal();
            table.reload('courseTab', {data: resultData});
            window.localStorage.setItem("tableData[[${form.rowInfo.formUserId}]]", JSON.stringify(resultData));
        })
        // 取消
        $("#cancelBtn,.close-btn").click(function () {
            $("#addCourse").hide();
            layer.closeAll();
        })

        getListData();

        function getListData() {
            resultData = [];
            $.post("/cultivation/majorCourseSet/getListData", {
                formUserId: "[[${form.rowInfo.formUserId}]]",
                fid: "[[${fid}]]"
            }, function (response) {
                const array = response.data;
                const uniqueArray = auditStatus === "0" ? [] : array;
                if (auditStatus === "0") {
                    let localData = JSON.parse(window.localStorage.getItem(`tableData[[${form.rowInfo.formUserId}]]`) || '[]')
                        .filter(item => item.courseIndex !== "小计");
                    const combinedArray = [...array, ...localData];
                    const ids = new Set();
                    combinedArray.forEach(item => {
                        if (!ids.has(item.courseId)) {
                            ids.add(item.courseId);
                            uniqueArray.push(item);
                        }
                    });
                }
                if (uniqueArray.length > 0) {
                    courseTabData = uniqueArray.map((data, index) => {
                        if (data.length === 0) {
                            return true;
                        }
                        const {hoursDistribute, hoursArrange, textBook} = data;
                        const parsedHoursDistribute = $.parseJSON(hoursDistribute);
                        const parsedHoursArrange = $.parseJSON(hoursArrange);
                        const parsedTextBook = textBook ? $.parseJSON(textBook) : "";
                        const weekClassHour = {};
                        const textBookInfo = {};
                        let totalClassHour = data.totalClassHour;
                        for (let i = 0; i < week_len; i++) {
                            weekClassHour["classHourWeek" + (i + 1)] = parsedHoursArrange["classHourWeek" + (i + 1)] === "0" ? 0 : parsedHoursArrange["classHourWeek" + (i + 1)];
                            textBookInfo["textBook" + (i + 1)] = parsedTextBook ? parsedTextBook["textBook" + (i + 1) + "jc_jcmc"] : "";
                        }
                        return {
                            ...data,
                            courseIndex: index + 1,
                            totalClassHour: totalClassHour,
                            theory: parsedHoursDistribute.theory === "0" ? 0 : parsedHoursDistribute.theory,
                            practice: parsedHoursDistribute.practice === "0" ? 0 : parsedHoursDistribute.practice,
                            experiment: parsedHoursDistribute.experiment === "0" ? 0 : parsedHoursDistribute.experiment,
                            computer: parsedHoursDistribute.computer === "0" ? 0 : parsedHoursDistribute.computer,
                            other: parsedHoursDistribute.other === "0" ? 0 : parsedHoursDistribute.other,
                            editable: true,
                            ...weekClassHour,
                            ...textBookInfo
                        };
                    });
                    subtotal();
                    table.reload('courseTab', {data: resultData});
                    if (auditStatus === "0") {
                        window.localStorage.setItem("tableData[[${form.rowInfo.formUserId}]]", JSON.stringify(resultData));
                    }
                }
            })
        }

        function subtotal(sortType) {
            if (!displaySetting.polymerizationSta) {
                let idx = 1;
                resultData = courseTabData;
                if (sortType) {
                    resultData = courseTabData.sort((a, b) => sortType === "desc" ? b.courseId.localeCompare(a.courseId) : a.courseId.localeCompare(b.courseId));
                    resultData.forEach((item) => {
                        item.courseIndex = idx++; // 设置新的序号，从 1 开始计数
                    });
                }
                return false;
            }
            const formatNum = (current, value) => {
                const result = current + (Number(value) || 0);
                return result % 1 === 0 ? result : Number(result.toFixed(1));
            };
            const summary = courseTabData.reduce((c, course) => {
                let {
                    credit = 0,
                    totalClassHour = 0,
                    theory = 0,
                    practice = 0,
                    experiment = 0,
                    computer = 0,
                    other = 0,
                    extraClassHour = 0,
                    courseNature,
                    courseClassify,
                    courseType,
                    courseCategory,
                    courseAttribute,
                    ...classHourWeek
                } = course;
                courseNature = course[dimension];
                // 使用对象解构和 || 运算符简化代码
                c[courseNature] = c[courseNature] || {
                    credit: 0,
                    totalClassHour: 0,
                    extraClassHour: 0,
                    theory: 0,
                    practice: 0,
                    experiment: 0,
                    computer: 0,
                    other: 0
                };
                c[courseNature].credit = formatNum(c[courseNature].credit, credit);
                c[courseNature].totalClassHour = formatNum(c[courseNature].totalClassHour, totalClassHour);
                c[courseNature].extraClassHour = formatNum(c[courseNature].extraClassHour, extraClassHour);
                c[courseNature].theory += parseInt(theory) || 0;
                c[courseNature].practice += parseInt(practice) || 0;
                c[courseNature].experiment += parseInt(experiment) || 0;
                c[courseNature].computer += parseInt(computer) || 0;
                c[courseNature].other += parseInt(other) || 0;
                for (let j = 0; j < week_len; j++) {
                    let num = classHourWeek["classHourWeek" + (j + 1)] || 0;
                    if (num && typeof num === "string" && (num.indexOf("周") !== -1 || num.indexOf("√") !== -1)) {
                        continue;
                    }
                    if (typeof num === "string" && num.includes("-")) {
                        num = num.split(",").reduce((acc, v) => {
                            return acc + parseFloat(v.split(":")[1] || 0);
                        }, 0).toFixed(1);
                    }
                    if ($.isNumeric(num)) {
                        num = Number(num);
                        num = num % 1 !== 0 ? parseFloat(num.toFixed(1)) : num;
                    }
                    let result = (parseFloat(c[courseNature]["classHourWeek" + (j + 1)]) || 0) + parseFloat(num);
                    result = result && result % 1 !== 0 ? parseFloat(result.toFixed(1)) : result;
                    c[courseNature]["classHourWeek" + (j + 1)] = result ? result : "";
                }
                return c;
            }, {});
            // 使用单个循环遍历 summary 对象
            let idx = 1;
            const total = Object.entries(summary).reduce((sum, [key, item]) => sum + item.totalClassHour, 0);
            Object.entries(summary).forEach(([courseNature, data]) => {
                let course = courseTabData.filter(
                    (nature) => nature[dimension] === courseNature
                );
                if (course.length === 0) {
                    return false;
                }
                if (sortType) {
                    course = course.sort((a, b) => sortType === "desc" ? b.courseId.localeCompare(a.courseId) : a.courseId.localeCompare(b.courseId));
                }
                course.forEach((item) => {
                    item.courseIndex = idx++; // 设置新的序号，从 1 开始计数
                });
                data.courseName = total !== 0 ? Math.round((data.totalClassHour / total) * 100 * 100) / 100 + "%" : 0;
                data.courseIndex = "小计";
                data.ignoreTotalRow = true;
                resultData.push(...course, data);
            });
        }

        let checkCount = 0;

        function setCheckStatus() {
            let dataArray = layui.table.cache["courseTab"];
            let courseArray = layui.table.cache["courseList"];
            dataArray = dataArray.filter(data => {
                return data.courseIndex !== "小计";
            });
            for (let i = 0; i < dataArray.length; i++) {
                const d = dataArray[i].courseId;
                for (let j = 0; j < courseArray.length; j++) {
                    const c = courseArray[j].kck_kcbh;
                    if (c === d) {
                        checkCount++;
                        const curObj = $("#addCourse .layui-table tr[data-index=" + j + "] input[type='checkbox']");
                        curObj.prop('disabled', true);
                        curObj.next().addClass('layui-checkbox-disabled').addClass("layui-btn-disabled");
                        curObj.next().find("i").removeClass("layui-icon-ok");
                    }
                }
            }
        }

        $(".j-search-con").on("click", ".schoolSel, .fuzzy-query-input", function (e) {
            let fieldAlias = $(this).parent().find("ul").attr("class");
            getFormDistinctFiled(fieldAlias);
        });

        function getFormDistinctFiled(fieldAlias) {
            if ($("." + fieldAlias + " li").length === 0) {
                let selectHtml = "";
                $.post('/teacherIdle/getFormDistinctFiled', {
                    formAlias: "kck",
                    fieldAlias: fieldAlias
                }, function (result) {
                    if (result.list) {
                        for (let i = 0; i < result.list.length; i++) {
                            let spanTxt = result.list[i];
                            if (spanTxt) {
                                selectHtml += "<li>" + spanTxt + "</li>";
                            }
                        }
                        layui.$("." + fieldAlias).html(selectHtml);
                    }
                }, 'json');
            }
        }

        function getFormDataByAlias(alias, filedAlias, dom) {
            $.post('/cultivation/majorCourseSet/getFormDataByAlias', {
                alias: alias,
                fid: "[[${fid}]]",
                filedAlias: filedAlias
            }, function (res) {
                // 将请求成功后返回的数据渲染到模板中
                let result = laytpl($('#' + dom).html()).render(res);
                $("select[name=" + dom + "]").html(result);
                let dataArray = layui.table.cache["courseTab"];
                dataArray.forEach((data, index) => {
                    let defaultVal = data[dom]; // 默认选中项的值
                    if (data.courseIndex === "小计") {
                        return true;
                    }
                    $("select[name=" + dom + "]").eq(index).val(defaultVal); // 设置默认选中项的值
                });
            });
        }

        function subCheck(array) {
            let errorMsg = '';
            for (let i = 0; i < array.length; i++) {
                let hoursDistribute = array[i].hoursDistribute;
                if (Object.values(hoursDistribute).some(val => val)) {
                    let hoursDistrTotal = 0;
                    if (displaySetting.theory && hoursDistribute.theory) {
                        hoursDistrTotal += parseInt(hoursDistribute.theory) || 0;
                    }
                    if (displaySetting.practice && hoursDistribute.practice) {
                        hoursDistrTotal += parseInt(hoursDistribute.practice) || 0;
                    }
                    if (displaySetting.experiment && hoursDistribute.experiment) {
                        hoursDistrTotal += parseInt(hoursDistribute.experiment) || 0;
                    }
                    if (displaySetting.computer && hoursDistribute.computer) {
                        hoursDistrTotal += parseInt(hoursDistribute.computer) || 0;
                    }
                    if (displaySetting.other && hoursDistribute.other) {
                        hoursDistrTotal += parseInt(hoursDistribute.other) || 0;
                    }

                    if (hoursDistrTotal !== array[i].totalClassHour && displaySetting.hoursDistribute) {
                        errorMsg += array[i].courseName + "、";
                        ["theory", "practice", "experiment", "computer", "other"].forEach(field => {
                            const dataIndex = array[i].LAY_INDEX;
                            const fieldSelector = `.layui-table tr[data-index=${dataIndex}] td[data-field=${field}]`;
                            $(fieldSelector).filter((index, element) => $(element).text()).attr("style", "color:#F76560 !important;background-color:#FFF0EC !important;");
                        });
                    }
                }
            }
            if (errorMsg) {
                errorMsg = errorMsg.slice(0, -1);
                layer.msg(errorMsg + "\t学时分配有误，请重新分配", {icon: 2, time: 5000});
            }
            return errorMsg;
        }


        async function checkHour() {
            let errorArray = [];
            let flag = false;
            for (let i = 0; i < week_len; i++) {
                let obj = $(".layui-table-total td[data-field=classHourWeek" + (i + 1) + "] div");
                let num = obj.text();
                if (num === "0") {
                    continue;
                }
                let year = parseInt("[[${form?.pyfagl_nj}]]") + Math.floor(i / 2);
                let semester = i % 2 + 1;
                let term = year + "-" + (year + 1) + "-" + semester;
                $.ajax({
                    type: "post",
                    url: "/cultivation/majorCourseSet/getTimeTableCourseHour",
                    data: {fid: "[[${fid}]]", term: term},
                    dataType: 'json',
                    async: false,
                    success: function (result) {
                        if (num > result.data && result.data > 0) {
                            errorArray.push({term: "第" + (i + 1) + "学期", hour: result.data});
                            $(".layui-table-total td[data-field=classHourWeek" + (i + 1) + "]").attr("style", "color:#F76560 !important;background-color:#FFF0EC !important;");
                        }
                    }
                });
            }
            let errorMsg = '';
            if (errorArray.length > 0) {
                flag = true;
                let terms = errorArray.map(error => error.term).join('、');
                let hours = errorArray.map(error => error.hour).join('、');
                errorMsg = terms + "周课时总数大于课表结构总课时数（" + hours + "），请确认是否提交？";
                let confirmed = await new Promise(resolve => {
                    layer.confirm(errorMsg, {icon: 0, btn: ['确定', '取消'], title: '警告'}, function (index) {
                        layer.close(index);
                        $(".layui-table-total td div").attr("style", "");
                        resolve(true); // 用户点击了确定
                    }, function () {
                        resolve(false); // 用户点击了取消
                    });
                });
                flag = !confirmed;
            }
            return flag;
        }

        function checkTxtFormat() {
            $(".layui-table-main td").attr("style", "");
            let error = 0;
            let dataArray = table.cache["courseTab"];
            dataArray.forEach((data) => {
                const dataIndex = data.LAY_INDEX;
                ["theory", "practice", "experiment", "computer", "other"].forEach(field => {
                    const fieldSelector = `.layui-table tr[data-index=${dataIndex}] td[data-field="${field}"]`;
                    if (data[field] && !/^\d+$/.test(data[field])) {
                        error++;
                        $(fieldSelector).filter((index, element) => $(element).text()).attr("style", "color:#F76560 !important;background-color:#FFF0EC !important;");
                    }
                })
                if (data["extraClassHour"] && !/^\d+(\.\d)?$/.test(data["extraClassHour"])) {
                    error++;
                    const dataIndex = data.LAY_INDEX;
                    const fieldSelector = `.layui-table tr[data-index=${dataIndex}] td[data-field="extraClassHour"]`;
                    $(fieldSelector).filter((index, element) => $(element).text()).attr("style", "color:#F76560 !important;background-color:#FFF0EC !important;");
                }
            })
            let notPracticeArray = dataArray.filter(data => !data.purePractical && data.courseIndex !== "小计");

            for (let i = 0; i < week_len; i++) {
                notPracticeArray.forEach((data) => {
                    if (data["classHourWeek" + (i + 1)]
                        && (!/^(\d+)(\.\d)?(-(\d+)(\.\d)?:(\d+)(\.\d)?)?(,\d+(\.\d)?(-\d+(\.\d)?(:\d+(\.\d)?)?)?)*$/.test(data["classHourWeek" + (i + 1)])
                            && !/^\d+(\.\d)?$/.test(data["classHourWeek" + (i + 1)]))
                        && data["classHourWeek" + (i + 1)] !== '√'
                    ) {
                        error++;
                        const dataIndex = data.LAY_INDEX;
                        const fieldSelector = `.layui-table tr[data-index=${dataIndex}] td[data-field="classHourWeek${i + 1}"]`;
                        $(fieldSelector).filter((index, element) => $(element).text()).attr("style", "color:#F76560 !important;background-color:#FFF0EC !important;");
                    }
                })
            }
            let practiceArray = dataArray.filter(data => data.purePractical);
            practiceArray.forEach((data) => {
                for (let i = 0; i < week_len; i++) {
                    /*if (data["classHourWeek" + (i + 1)] && !/^\d+周$/.test(data["classHourWeek" + (i + 1)])) {
                        error++;
                        const dataIndex = data.LAY_INDEX;
                        const fieldSelector = `.layui-table tr[data-index=${dataIndex}] td[data-field="classHourWeek${i + 1}"] div`;
                        $(fieldSelector).filter((index, element) => $(element).text()).css({
                            "color": "#F76560",
                            "background-color": "#FFF0EC"
                        });
                    }*/
                    if (data["totalClassHour"] && !/^\d+(\.\d)?$/.test(data["totalClassHour"])) {
                        error++;
                        const dataIndex = data.LAY_INDEX;
                        const fieldSelector = `.layui-table tr[data-index=${dataIndex}] td[data-field="totalClassHour"]`;
                        $(fieldSelector).filter((index, element) => $(element).text()).attr("style", "color:#F76560 !important;background-color:#FFF0EC !important;");
                    }
                }
            })
            return error > 0 ? layer.msg('<span style="display:block;margin: auto;text-align: center;">数据格式错误，请更正。<br/>鼠标悬浮对应单元格可查看具体格式要求。<br/>若使用对号请复制“√”</span>', {
                icon: 2,
                time: 5000,
                closeBtn: 1
            }) : "";
        }

        //批量编辑
        $("#batchEdit").click(function () {
            let data = layui.table.checkStatus('courseTab').data;
            if (data.length === 0) {
                layer.msg("请选择要更新的数据", {icon: 2, time: 3000});
                return false;
            }
            layer.open({
                type: 1,
                title: false,
                closeBtn: false,
                content: $('.batch_editor_pop'),
                area: ['auto', 'auto'],
                success: function () {
                },
            });
        })
        $(".mb20 .bep_per_right").click(function () {
            $(".one").show();
            $(".formFieldSel").hide();
        })
        // 选择
        $(".bep_per_sel").on('click', "ul li", function (event) {
            const txt = $(this).text();
            const title = $(this).attr("title");
            $(this).parents(".bep_per_sel").find(".bep_per_input").attr("title", title);
            $(this).parents(".bep_per_sel").find(".bep_per_input").val(txt);
            $(this).parents(".bep_per_sel").find(".bep_per_text").val(txt);
            $(".bep_search_sel").hide();
            $(this).addClass('active').siblings().removeClass('active');
            const examTypeHtml = $("select[name='examType']").eq(0).find("option").not(":contains('请选择')")
                .map(function () {
                    return "<li class=\"bep_per_lis\">" + $(this).text() + "</li>";
                }).get().join("");
            const openDeptHtml = $("select[name='openDept']").eq(0).find("option").not(":contains('请选择')")
                .map(function () {
                    return "<li class=\"bep_per_lis\">" + $(this).text() + "</li>";
                }).get().join("");
            if ($(this).parents(".formField").length === 0) {
                let flag = title !== "credit" && title !== "courseName";
                let readonlyAttr = flag ? "readonly" : "";
                let html = `<input type="text" placeholder="请输入" class="bep_per_text" ${readonlyAttr}>`;
                if (flag) {
                    html += "<div class=\"bep_search_sel formFieldSel\" style=\"display: none;\"><ul class=\"bep_per_uls\">";
                    if (title === "examType") {
                        html += examTypeHtml;
                    } else if (title === "openDept") {
                        html += openDeptHtml;
                    } else if (title === "courseNature") {
                        $.ajax({
                            url: '/teacherIdle/getFormDistinctFiled',
                            type: 'post',
                            dataType: 'json',
                            async: false,
                            data: {
                                formAlias: "xt_kcxz",
                                fieldAlias: "kcxz"
                            },
                            success: function (response) {
                                if (response.list) {
                                    for (let i = 0; i < response.list.length; i++) {
                                        let item = response.list[i];
                                        html += "<li class=\"bep_per_lis\">" + item + "</li>";
                                    }
                                }
                            }
                        })
                    } else if (title === "chooseCompulsory") {
                        html += "<li class=\"bep_per_lis\">必修</li><li class=\"bep_per_lis\">选修</li>";
                    }
                    html += "</ul></div>";
                }
                $(".formField").html(html);
            }
            event.stopPropagation();
        })

        $(document).on("click", function (event) {
            const target = event.target;
            const isChildElement = $(target).hasClass('bep_per_input') || $(target).hasClass('bep_per_text');
            if (!isChildElement) {
                $(".bep_search_sel").hide();
            }
            event.stopPropagation();
        })

        $(".pop_sure").click(function () {
            layer.load(1);
            const formField = $(".bep_per_input").attr("title");
            const formFieldVal = $(".bep_per_text").val();
            let checkData = layui.table.checkStatus('courseTab').data;
            let dataArray = layui.table.cache["courseTab"];
            if (formField === "credit") {
                let subtotal = 0;
                dataArray.forEach((data) => {
                    data.LAY_CHECKED = false;
                    checkData.forEach((check) => {
                        if (check.courseId === data.courseId) {
                            data.credit = formFieldVal;
                        }
                    });
                    if (data.courseIndex === "小计") {
                        data.credit = subtotal;
                        subtotal = 0;
                    } else {
                        subtotal += parseInt(data.credit);
                    }
                });
            } else if (formField === "examType") {
                dataArray.forEach((data) => {
                    data.LAY_CHECKED = false;
                    checkData.forEach((check) => {
                        if (check.courseId === data.courseId) {
                            data.examType = formFieldVal;
                        }
                    });
                });
            } else if (formField === "openDept") {
                dataArray.forEach((data) => {
                    data.LAY_CHECKED = false;
                    checkData.forEach((check) => {
                        if (check.courseId === data.courseId) {
                            data.openDept = formFieldVal;
                        }
                    });
                });
            } else if (formField === "courseNature") {
                dataArray.forEach((data) => {
                    data.LAY_CHECKED = false;
                    checkData.forEach((check) => {
                        if (check.courseId === data.courseId) {
                            data.courseNature = formFieldVal;
                        }
                    });
                });
            } else if (formField === "chooseCompulsory") {
                dataArray.forEach((data) => {
                    data.LAY_CHECKED = false;
                    checkData.forEach((check) => {
                        if (check.courseId === data.courseId) {
                            data.chooseCompulsory = formFieldVal;
                        }
                    });
                });
            } else if (formField === "courseName") {
                dataArray.forEach((data) => {
                    data.LAY_CHECKED = false;
                    checkData.forEach((check) => {
                        if (check.courseId === data.courseId) {
                            data.courseName = formFieldVal;
                        }
                    });
                });
            }
            dataArray = dataArray.filter(data => {
                return data.courseIndex !== "小计";
            });
            resultData = [];
            courseTabData = dataArray.map((data, index) => {
                if (data.length === 0) {
                    return true;
                }
                return {
                    ...data,
                    courseIndex: index + 1
                };
            });
            subtotal();
            table.reload('courseTab', {data: resultData});
            window.localStorage.setItem("tableData[[${form.rowInfo.formUserId}]]", JSON.stringify(resultData));
            release();
            layer.closeAll();
        })

        $(".pop_cal").click(function () {
            $(".batch_editor_pop").hide();
            layer.closeAll();
        })

        $(".formField").click(function () {
            $(".formFieldSel").show();
        })

        // 筛选
        // 提交事件
        form.on('submit(selTextbookTable)', function (data) {
            const field = data.field; // 获取表单字段值
            field.deptId = "[[${fid}]]";
            // 重新加载table
            table.reload('textbookList', {where: field, page: {curr: 1}});
            return false; // 阻止默认 form 跳转
        });

        $("#selTextbook button[type='reset']").click(function () {
            const field = {};
            field.jc_jcmc = "";
            field.jc_jcbh = "";
            field.jc_isbn = "";
            field.deptId = "[[${fid}]]";
            table.reload('textbookList', {where: field, page: {curr: 1}});
        })

        table.on('checkbox(textbookList)', function (obj) {
            const checkStatus = table.checkStatus('textbookList');
            let dataArray = layui.table.cache["textbookList"];
            checkStatus.data.forEach(function (obj) {
                if (!selDataArray.some(function (item) {
                    return item.jc_jcbh === obj.jc_jcbh;
                })) {
                    selDataArray.push(obj);
                }
            });
            if (!obj.checked) {
                selDataArray = selDataArray.filter(function (data) {
                    return data.jc_jcbh !== obj.data.jc_jcbh;
                });
                if (obj.type === "all") {
                    selDataArray = selDataArray.filter(function (item1) {
                        return !dataArray.some(function (item2) {
                            return item1.jc_jcbh === item2.jc_jcbh;
                        });
                    });
                }
            }
            $("#selTextbookCount em").text(selDataArray.length);
        });

        $("#checkAll").click(function () {
            $(this).toggleClass("checked");
            if ($(this).hasClass("checked")) {
                $('div[lay-id="textbookList"] input[type=checkbox]').prop("checked", true);
                $('div[lay-id="textbookList"] .layui-form-checkbox').addClass("layui-form-checked");
                $("#selTextbookCount em").text($("#selTextbookCount").attr("count"));
            } else {
                $('div[lay-id="textbookList"] input[type=checkbox]').prop("checked", false);
                $('div[lay-id="textbookList"] .layui-form-checkbox').removeClass("layui-form-checked");
                $("#selTextbookCount em").text(0);
            }
        });

        // 确定
        $("#textbookSureBtn").click(function () {
            layer.closeAll();
            let txKey = othis.attr('data-field');
            const editObj1 = {};
            const filterCourse = selDataArray.map(item => item.jc_jcmc);
            editObj1[txKey] = filterCourse.join(',');
            editObj1[txKey + 'jc_jcmc'] = filterCourse.join(',');
            editObj1[txKey + 'jc_jcbh'] = selDataArray.map(item => item.jc_jcbh).join(',');
            editObj1[txKey + 'jc_isbn'] = selDataArray.map(item => item.jc_isbn).join(',');
            let newTextBook = {};
            if (editObj["data"]["textBook"]) {
                let textBook = $.parseJSON(editObj["data"]["textBook"]);
                textBook[txKey + 'jc_jcmc'] = filterCourse.join(',');
                textBook[txKey + 'jc_jcbh'] = selDataArray.map(item => item.jc_jcbh).join(',');
                textBook[txKey + 'jc_isbn'] = selDataArray.map(item => item.jc_isbn).join(',');
                newTextBook = {textBook: JSON.stringify(textBook)};
            }
            editObj.update(editObj1);
            editObj.update(newTextBook);
            let textBookVal = filterCourse.join(',');
            const numberPart = txKey.match(/\d+/);
            const numericValue = parseInt(numberPart[0]) - 1;
            let year = parseInt("[[${form?.pyfagl_nj}]]") + Math.floor(numericValue / 2);
            let semester = numericValue % 2 + 1;
            let term = year + "-" + (year + 1) + "-" + semester;
            if (textBookSetting.modifyTextbook === 0 && textBookVal) {
                othis.find("div").html("<span class=\"txt-more\" onmouseout=\"hideTB(event)\" onmouseenter=\"showTB(event)\">" + textBookVal + "</span>");
            } else {
                if (textBookVal && (!checkModifyPermission() || (textBookSetting.termRange && textBookSetting.termRange.indexOf(term) === -1))) {
                    othis.find("div").html("<span class=\"txt-more\" onmouseout=\"hideTB(event)\" onmouseenter=\"showTB(event)\">" + textBookVal + "</span>");
                    return true;
                }
                if (auditStatus === "1") {
                    textBookVal = textBookVal ? textBookVal : "";
                    othis.find("div").html("<span>" + textBookVal + "</span>");
                } else {
                    if (textBookVal) {
                        othis.find("div").html("<span class=\"txt-blue\">" + textBookVal + "</span>");
                    } else if (!textBookVal) {
                        othis.find("div").html("<span class=\"tab-link\"></span>");
                    }
                }
            }
            let tableCache = table.cache['courseTab'];
            window.localStorage.setItem("tableData[[${form.rowInfo.formUserId}]]", JSON.stringify(tableCache));
        })
        // 取消
        $("#textbookCancelBtn").click(function () {
            layer.closeAll();
        })

        showTB = function (event) {
            let tableCache = table.cache['courseTab'];
            let field = $(event.target).parents("td").attr("data-field");
            let index = $(event.target).parents("tr").attr("data-index");
            let eventIndex = field.replace(/[^0-9]/g, '');
            let textBook = tableCache[index]?.textBook ? JSON.parse(tableCache[index].textBook) : "";
            let jc_jcbh = tableCache[index][field + "jc_jcbh"] || textBook[field + "jc_jcbh"];
            let jc_isbn = tableCache[index][field + "jc_isbn"] || textBook[field + "jc_isbn"];
            let jc_jcmc = tableCache[index][field] || textBook[field];
            let jcmcArray = jc_jcmc?.split(",") || [];
            let jcbhArray = jc_jcbh?.split(",") || [];
            let isbnArray = jc_isbn?.split(",") || [];
            let html = "";
            for (let i = 0; i < jcmcArray.length; i++) {
                html += "<div class=\"textbook-con\">";
                html += "<div class=\"textbook-item\"><h5>教材名称：</h5><p>" + (jcmcArray[i] || "") + "</p></div>";
                html += "<div class=\"textbook-item\"><h5>教材号：</h5><p>" + (jcbhArray[i] || "") + "</p></div>";
                html += "<div class=\"textbook-item\"><h5>ISBN：</h5><p>" + (isbnArray[i] || "") + "</p></div>";
                html += "</div>";
            }
            $(".textbook-con-wrap").html(html);
            const offset = $(event.target).offset();
            const pageX = offset.left;
            const pageY = offset.top + 40;
            const allHeight = pageY + $("#textbookTips").outerHeight(true);
            const winH = $(window).height();
            if (allHeight < winH) {
                $("#textbookTips").removeClass('textbookBot').css({left: pageX, top: pageY}).fadeIn('fast');
            } else {
                $("#textbookTips").addClass('textbookBot').css({
                    left: pageX,
                    top: pageY - $("#textbookTips").outerHeight(true) - 42
                }).fadeIn('fast');
            }

        }
        hideTB = function () {
            $("#textbookTips").fadeOut('fast');
        }

        $("#textbookTips").mouseenter(function () {
            $(this).stop().show();
        }).mouseleave(function () {
            $(this).hide();
        });

        function checkModifyPermission() {
            if (textBookSetting.permission === "0" && textBookSetting.permissionDetail.member.uid !== "[[${uid}]]") {
                return false;
            }
            if (textBookSetting.permission === "1" && textBookSetting.permissionDetail.role.roleId !== "[[${roleid}]]") {
                return false;
            }
            if (textBookSetting.timeLimit) {
                let timeArray = textBookSetting.timeLimit.split(' ~ ');
                const startDate = new Date(timeArray[0] + ":00");
                const endDate = new Date(timeArray[1] + ":59");
                const now = new Date();
                if (now < startDate || now > endDate) {
                    return false;
                }
            }
            return true;
        }

        let layerIndex, layerIndex1, layerIndex2;
        $('#importBtn').click(function () {
            $("#importTeachProcessFile .green,#importResult .green").text(0);
            $("#importTeachProcessFile .yellow,#importResult .yellow").text(0);
            $.post('/cultivation/majorCourseSet/getErrorDataFile', {
                fid: "[[${fid}]]",
                uid: "[[${uid}]]"
            }, function (response) {
                if (response) {
                    $(".btn-export").show();
                }
                layerIndex2 = layer.open({
                    type: 1,
                    title: false,
                    closeBtn: false,
                    shadeClose: true,
                    isOutAnim: true,
                    content: $('#importTeachProcess'),
                    area: ['auto', 'auto'],
                    success: function () {

                    },
                });
            });
        })

        upload.render({
            elem: "#uploadBtn",//导入id
            url: "/cultivation/majorCourseSet/importData",
            accept: "file",
            exts: 'xls|excel|xlsx',
            acceptMime: 'application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            before: function () {
                this.data = {
                    fid: "[[${fid}]]",
                    formUserId: "[[${form.rowInfo.formUserId}]]",
                    year: week_len,
                    uid: "[[${uid}]]"
                }
                const timer = setInterval(function () {
                    $.post('/cultivation/majorCourseSet/getImportProcess', {
                        fid: "[[${fid}]]",
                        uid: "[[${uid}]]"
                    }, function (response) {
                        $("#importTeachProcessFile .green,#importResult .green").text(response.successCount);
                        $("#importTeachProcessFile .yellow,#importResult .yellow").text(response.failCount);
                        if (response.failCount > 0) {
                            $("#importResult").addClass("importError");
                        }
                        if (response.successCount + response.failCount === response.totalCount) {
                            clearInterval(timer);
                            layer.close(layerIndex);
                            layer.close(layerIndex2);
                            layerIndex1 = layer.open({
                                type: 1,
                                title: false,
                                closeBtn: false,
                                shadeClose: true,
                                isOutAnim: true,
                                content: $('#importResult'),
                                area: ['auto', 'auto'],
                                success: function () {
                                },
                            });
                        }
                    })
                }, 2000);
                layerIndex = layer.open({
                    type: 1,
                    title: false,
                    closeBtn: false,
                    shadeClose: true,
                    isOutAnim: true,
                    content: $('#importTeachProcessFile'),
                    area: ['auto', 'auto'],
                    success: function () {
                    },
                });
            },
            done: function (res) {
            }
        });
        // 导入关闭
        $("#importTeachProcess .close-btn").click(function () {
            layer.close(layerIndex2);
        })
        // 上传成功和失败关闭弹窗
        $('#importResult #importSureBtn').on('click', function () {
            layer.close(layerIndex1);
            getListData();
        });
        $('#importResult .close-btn').on('click', function () {
            layer.close(layerIndex1);
        });
        // 上传中取消
        $('#importTeachProcessFile .close-btn,#importTeachProcessFile #importTeachCancelBtn').on('click', function () {
            layer.close(layerIndex);
        });
        // 上传中确定
        $("#importTeachSureBtn").click(function () {
            layer.close(layerIndex);
        })
        //导出上次错误数据
        $(".btn-export,#importErrorData").click(function () {
            let fid = "[[${fid}]]";
            let uid = "[[${uid}]]";
            fetch("/down/major_course_set_error_" + uid + "_" + fid + ".xls")
                .then(response => response.blob())
                .then(blob => {
                    // 创建 Blob 对象
                    const url = window.URL.createObjectURL(blob);

                    // 创建隐藏的 a 标签，并设置属性
                    const downloadLink = document.createElement('a');
                    downloadLink.style.display = 'none';
                    downloadLink.href = url;
                    downloadLink.download = "[[${form.pyfagl_nj}]]级[[${form.pyfagl_zy}]]专业教学进程表（错误数据）.xls";

                    // 将 a 标签加入到页面中并触发点击事件
                    document.body.appendChild(downloadLink);
                    downloadLink.click();

                    // 完成后移除下载链接
                    document.body.removeChild(downloadLink);
                });
        })
        //下载模板
        $(".down-template").click(function () {
            layer.load(0, {shade: [0.1, '#fff']});
            $.post('/cultivation/majorCourseSet/downTemplate', {
                fid: "[[${fid}]]",
                formUserId: "[[${form.rowInfo.formUserId}]]",
                year: week_len
            }, function (response) {
                layer.closeAll('loading');
                fetch("/down/" + response.data.file + ".xls")
                    .then(response => response.blob())
                    .then(blob => {
                        // 创建 Blob 对象
                        const url = window.URL.createObjectURL(blob);

                        // 创建隐藏的 a 标签，并设置属性
                        const downloadLink = document.createElement('a');
                        downloadLink.style.display = 'none';
                        downloadLink.href = url;
                        downloadLink.download = "[[${form.pyfagl_nj}]]级[[${form.pyfagl_zy}]]专业[[${form.pyfagl_xz}]]教学进程表导入模板.xls";

                        // 将 a 标签加入到页面中并触发点击事件
                        document.body.appendChild(downloadLink);
                        downloadLink.click();

                        // 完成后移除下载链接
                        document.body.removeChild(downloadLink);
                    });
            });
        })

        $("#checkAllCourse").click(function () {
            $(this).toggleClass("checked");
            if ($(this).hasClass("checked")) {
                $("#selCourse em").text($("#selCourse span i").text());
                $('div[lay-id="courseList"] input[type=checkbox]').prop("checked", true);
                $('div[lay-id="courseList"] .layui-form-checkbox').addClass("layui-form-checked");
            } else {
                $("#selCourse em").text(0);
                $('div[lay-id="courseList"] input[type=checkbox]').prop("checked", false);
                $('div[lay-id="courseList"] .layui-form-checkbox').removeClass("layui-form-checked");
            }
            form.render('checkbox');
        })

        // 提交事件
        form.on('submit(replaceSelTable)', function (data) {
            const field = data.field; // 获取表单字段值
            field.kck_kcxz = field.kck_kcxz === "请选择" || !field.kck_kcxz ? "" : field.kck_kcxz;
            field.kck_kkyx = field.kck_kkyx === "请选择" || !field.kck_kkyx ? "" : field.kck_kkyx;
            field.kck_kcsx = field.kck_kcsx === "请选择" || !field.kck_kcsx ? "" : field.kck_kcsx;
            field.kck_kkjys = field.kck_kkjys === "请选择" || !field.kck_kkjys ? "" : field.kck_kkjys;
            // 重新加载table
            table.reload('replaceCourseList', {where: field, page: {curr: 1}});
            return false; // 阻止默认 form 跳转
        });
        //清空
        $("#replaceCourse button[type='reset']").click(function () {
            const field = {};
            field.kck_kcxz = "";
            field.kck_kcsx = "";
            field.kck_kkyx = "";
            field.kck_kcmc = "";
            table.reload('replaceCourseList', {where: field, page: {curr: 1}});
        })
        $("#replaceCancelBtn").click(function () {
            $("#replaceCourse").hide();
            layer.closeAll();
        })
        $("#replaceSureBtn").click(function () {
            let checkData = layui.table.checkStatus('replaceCourseList').data;
            if (checkData.length === 0) {
                layer.msg("请选择要替换的课程");
                return;
            }
            let dataArray = layui.table.cache["courseTab"];
            let idx = $("#replaceCourse").attr("index");
            Object.assign(dataArray[idx], {
                courseNature: checkData[0].kck_kcxz,
                courseId: checkData[0].kck_kcbh,
                courseName: checkData[0].kck_kcmc,
                credit: checkData[0].kck_xf,
                chooseCompulsory: checkData[0].kck_kcsx,
                examType: checkData[0].kck_ksxs,
                openDept: checkData[0].kck_kkyx,
                theory: checkData[0].kck_llxs,
                practice: checkData[0].kck_sjxs,
                experiment: checkData[0].kck_syxs,
                computer: checkData[0].kck_shangjxs,
                other: checkData[0].kck_qtxs,
                courseCriterion: checkData[0].kck_sfykb,
                courseFormUserId: checkData[0].rowInfo.formUserId,
                practiceFlag: checkData[0].kck_sfcsjhj,
                courseClassify: checkData[0].kck_kcfl,
                courseType: checkData[0].kck_kclx,
                courseCategory: checkData[0].kck_kclb,
                courseAttribute: checkData[0].kck_kcsxnew,
                courseParentName: checkData[0].kck_fjkcmc,
                courseGroup: checkData[0].courseGroup,
                purePractical: checkData[0].kck_sfcsjhj === "是"
            });
            dataArray = dataArray.filter(data => data.courseIndex !== "小计");
            resultData = [];
            courseTabData = dataArray.map((data, index) => ({
                ...data,
                courseIndex: index + 1
            }));
            subtotal();
            table.reload('courseTab', {data: resultData});
            $("#replaceCourse").hide();
            layer.closeAll();
        })
    })

</script>
</html>