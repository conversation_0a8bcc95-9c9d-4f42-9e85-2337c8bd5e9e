<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>班教材签收单</title>
    <link rel="stylesheet" th:href="@{/css/material/global.css}">
    <link rel="stylesheet" th:href="@{/plugin/layui/css/layui.css}">
    <link rel="stylesheet" th:href="@{/css/material/noClassSearch.css}">
    <link rel="stylesheet" th:href="@{/css/material/poup.css}">
    <script th:src="@{/plugin/layui/layui.js}"></script>
</head>
<style>
    .main .top .r-title {
        padding-right: 34px;
    }

    .main .top .r-title span {
        cursor: pointer;
        margin-left: 29px;
        color: rgba(49, 143, 197, 0.73);
        font-size: 14px;
    }

    .popups {
        background: #FFFFFF;
        border-radius: 10px;
        overflow: hidden;
        display: none;
    }

    .popups .title {
        height: 60px;
        line-height: 60px;
        font-size: 16px;
        font-weight: bold;
        padding: 0 30px;
        display: flex;
        display: -webkit-flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid #F2F2F2;
    }

    .popups .title .name {
        font-size: 18px;
        font-style: normal;
        font-weight: 500;
        color: #131B26;
        text-align: left;
    }

    .popups .title .close {
        width: 20px;
        height: 20px;
        background: url(../images/close-icon.png) no-repeat center;
        cursor: pointer;
    }

    .popups .popups-con {
        padding: 30px;
    }

    .popups .bottom {
        border-top: 1px solid #F2F2F2;
        height: 70px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin-bottom: 0;
        padding: 0 30px;
        box-sizing: border-box;
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        -ms-box-sizing: border-box;
        -o-box-sizing: border-box;
    }

    .popups .bottom .layui-btn-primary {
        width: 92px;
        height: 36px;
        border-radius: 6px;
        border: 1px solid #8CBBFF;
        box-sizing: border-box;
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        -ms-box-sizing: border-box;
        -o-box-sizing: border-box;
        text-align: center;
        line-height: 34px;
        cursor: pointer;
        font-size: 14px;
        color: #3A8BFF;
        margin-right: 30px;
    }

    .popups .bottom .exam-sure {
        width: 92px;
        height: 36px;
        border-radius: 6px;
        background: #3A8BFF;
        text-align: center;
        line-height: 36px;
        font-size: 14px;
        color: #FFFFFF;
        cursor: pointer;
        margin-left: 0;
    }

    .addInform {
        width: 602px;
    }
    .addInform .popups-con h3{
        font-size:14px;
        color:rgba(0, 0, 0, 1);
        line-height: 18px;
        margin-bottom:22px;
    }
    .layui-layer {
        border-radius: 10px !important;
    }
    .kalamu-area{
        padding:10px 15px;
        min-height: 250px;
        overflow-y: auto;
        font-size: 16px;
        outline: none;
        color: #666;
        position:relative;
        border-radius: 5px;
        border: 1px solid #E5E6EB;
        background: #FFF;
    }

    .kalamu-area:empty:before{
        content: attr(placeholder);
        font-size: 16px;
        color: #b7bbbf;
        line-height: normal;
    }

    .kalamu-area:focus:before{
        content:none;
    }
</style>
<body>
    <div class="main">
        <div class="top">
            <h4>班教材签收单</h4>
            <div class="r-title">
                <span class="set">设置领取注意</span>
                <span class="export">导出</span>
                <span class="exportRecord" title="多个班级时异步导出，从导出记录里面下载文件">导出记录</span>
                <span class="print">打印</span>
            </div>
        </div>
        <div class="form-con">
            <div class="sel-box">
                <div class="sel-row">
                    <div class="sel-item">
                        <div class="sel-title">学期</div>
                        <div class="sel" style="margin-right:0;">
                            <div class="select-input">
                                <div class="name" data-name="请选择学期">请选择学期</div>
                                <i></i>
                                <div class="select-dropdown">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <ul class="dropdown-lists dropdown-lists-single">
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="sel-item">
                        <div class="sel-title">年级</div>
                        <div class="sel" style="margin-right:0;">
                            <div class="select-input">
                                <div class="name" data-name="请选择年级">请选择年级</div>
                                <i></i>
                                <div class="select-dropdown">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <ul class="dropdown-lists dropdown-lists-single">
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="sel-item">
                        <div class="sel-title">院系</div>
                        <div class="sel" style="margin-right:0;">
                            <div class="select-input">
                                <div class="name" data-name="请选择院系">请选择院系</div>
                                <i></i>
                                <div class="select-dropdown">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <ul class="dropdown-lists dropdown-lists-single">
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="sel-item">
                        <div class="sel-title"><span></span>选择专业</div>
                        <div class="sel" style="margin-right:0;">
                            <div class="select-input">
                                <div class="name" data-name="请选择专业">请选择专业</div>
                                <i></i>
                                <div class="select-dropdown">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <div class="all-selects">
                                        全选
                                    </div>
                                    <ul class="dropdown-lists">
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="sel-item">
                        <div class="sel-title"><span></span>选择班级</div>
                        <div class="sel" style="margin-right:0;">
                            <div class="select-input">
                                <div class="name" data-name="请选择班级">请选择班级</div>
                                <i></i>
                                <div class="select-dropdown">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <div class="all-selects">
                                        全选
                                    </div>
                                    <ul class="dropdown-lists">
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="tableDetail" style="min-height: 576px;">
            <table>
                <thead>
                    <tr>
                        <td>学年学期</td>
                        <td>年级</td>
                        <td>院系</td>
                        <td>专业</td>
                        <td>班级</td>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
        <div id="coursePage"></div>
    </div>
    <div id="levelManagement" class="addInform popups">
        <div class="popups-con">
            <h3>说明：设置注意事项后将会对所有的班级教材签收单进行注意事项的修改，在打印时显示在结束页的左下方位置，详见打印页</h3>
            <div class="kalamu-area" id="textareaaddress" contenteditable="true" placeholder="">[[${content}]]</div>
        </div>
        <div class="bottom">
            <button type="reset" class="layui-btn layui-btn-primary exam-cancle">取消</button>
            <button class="layui-btn exam-sure">确定</button>
        </div>
    </div>
</body>
<script th:inline="javascript">
    let fid = "[[${fid}]]";
    let term = [[${term}]];
    let uid = "[[${uid}]]";
    let content = [[${content}]];
    const _VR_ = [[${_VR_}]] || '';
</script>
<script th:src="@{/js/jquery-3.3.1.min.js}"></script>
<script th:src="@{/js/material/receipt_form.js(v=${new java.util.Date().getTime()})}"></script>
<script th:src="@{/js/my.util.js}"></script>
<script>
</script>

</html>