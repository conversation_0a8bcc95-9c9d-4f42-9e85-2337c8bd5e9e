<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>成绩管理</title>
  	<link rel="stylesheet" href="/score/css/global.css">
    <link rel="stylesheet" href="/layui/css/layui.css">
    <link rel="stylesheet" href="/score/css/common.css">
    <link rel="stylesheet" href="/score/css/reset.css">
    <link rel="stylesheet" href="/score/css/indexRule.css">
</head>

<body>

  <div class="main">
    <div class="content">
      <div class="c-top">
        <h2>录入时间设置</h2>
      </div>

      <form class="layui-form" action="">
        <div class="layui-form-item">
          <label class="layui-form-label">成绩录入方式</label>
          <div class="layui-input-block">
            <div class="lab">
              <input type="radio" value="2" name="enter" title="分批次录入成绩" checked="" lay-filter="enter-lay">
              <span class="introIcon">
                <div class="bubble">
                  <span>对全部成绩的录入起止时间进行配置，若超出该时间范围，成绩不可编辑，默认同步学期起止时间<em>点此同步</em></span>
                </div>
              </span>

            </div>
            <div class="lab">
              <input type="radio" value="1" name="enter" title="一次性录入成绩" lay-filter="enter-lay">
              <span class="introIcon">
                <div class="bubble">
                  <span>对各分项成绩的录入起止时间进行配置，若超出该时间范围，成绩不可编辑</span>
                </div>
              </span>
            </div>
          </div>
        </div>

      </form>

      <div class="lable-list">
         
         <div class="lable cur">
          <div class="add-lab">
            <span>添加</span>
          </div>
          <div class="tabWrap">
            <table class="layui-table" id="main-table1" lay-filter="dataTable1" lay-data='{page: true}'>
            </table>
          </div>
         </div>
         <div class="lable">
          <form class="layui-form" action="">
          	<input type="hidden" name="id" th:value="${basic !=null?basic.id:''}">
            <div class="layui-form-item">
              <label class="layui-form-label">录入开始时间</label>
              <div class="times">
                <input type="text" name="time" readonly placeholder="请选择" class="layui-input" id="startTime" th:value="${basic !=null?basic.startTime:''}"/>
              </div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label">录入结束时间</label>
              <div class="times">
                <input type="text" name="time" readonly placeholder="请选择" class="layui-input" id="endTime" th:value="${basic !=null?basic.endTime:''}" />
              </div>
            </div>
          </form>
         </div>
      </div>

      <div class="save-settings">
        <span>保存设置</span>
      </div>

      
    </div>
  </div>

  <!-- 添加 -->
  <div id="addPoup" class="addPoup popup">
    <div class="title">
      <div class="name">添加</div>
      <div class="close"></div>
    </div>
    <div class="popup-con">
      <form class="layui-form" lay-filter="editForm">
      	<input type="hidden" name="id" />
        <div class="layui-form-item" id="extra">
          <label class="layui-form-label"><em>*</em>成绩分项</label>
          <div class="layui-input-block w240">
            <select name="itemCode" lay-verify="required">
              <option value="">请选择</option>
              <option value="1">平时</option>
              <option value="2">期中</option>
              <option value="3">期末</option>
              <option value="4">月考</option>
            </select>
          </div>
          <div class="error">请先配置成绩分项</div>
        </div>
        <div class="layui-form-item">
          <label class="layui-form-label"><em>*</em>录入开始时间</label>
          <div class="times">
            <input type="text" name="startTime" readonly placeholder="请选择" class="layui-input" id="startTimes"/>
          </div>
        </div>
        <div class="layui-form-item">
          <label class="layui-form-label"><em>*</em>录入结束时间</label>
          <div class="times">
            <input type="text" name="endTime" readonly placeholder="请选择" class="layui-input" id="endTimes"/>
          </div>
        </div>

      </form>
    </div>
    <div class="bottom">
      <button type="reset" class="layui-btn layui-btn-primary exam-cancle">取消</button>
      <button class="layui-btn exam-sure">确定</button>
    </div>

  </div>


  <script type="text/html" id="barDemo3">
    <a style="color:#3A8BFF;;cursor:pointer;margin-right:16px;" lay-event="edit">编辑</a>
    <a style="color:#F33131;cursor:pointer;" lay-event="del">删除</a>
  </script>
  <script src="/js/jquery1.11.1.min.js"></script>
  <script src="/js/jquery.mCustomScrollbar.concat.min.js"></script>
  <script src="/layui/layui.js"></script>
  <script th:inline="javascript">
  	var fid = [[${fid}]]+"";
  	var xnxq = [[${xnxq}]]+"";
  	var enterType = [[${enterType}]]+"";
    $(function () {
      var form, table, laydate, layer;
      layui.use(['form', 'table', 'laydate'],
        function () {
          var $ = layui.jquery;
          form = layui.form;
          table = layui.table;
          laydate = layui.laydate;
          layer = layui.layer;

          
          //日期选择
          laydate.render({
            elem: '#startTime',
            format: 'yyyy-MM-dd HH:mm:ss',
            type: 'datetime',
          });

          //日期选择
          laydate.render({
            elem: '#endTime',
            format: 'yyyy-MM-dd HH:mm:ss',
            type: 'datetime',
          });

          //日期选择
          laydate.render({
            elem: '#startTimes',
            format: 'yyyy-MM-dd HH:mm:ss',
            type: 'datetime',
          });

          //日期选择
          laydate.render({
            elem: '#endTimes',
            format: 'yyyy-MM-dd HH:mm:ss',
            type: 'datetime',
          });
		  if(enterType == 1){
		  	  $(".layui-form-item .lab input:eq(1)").attr("checked","checked");
		  	  $(".lable-list .lable").eq(1).addClass("cur").siblings().removeClass("cur");
		  	  form.render();
		  }
          form.on('radio(enter-lay)', function(data){
              if(data.value=='2'){
	              $(".lable-list .lable").eq(0).addClass("cur").siblings().removeClass("cur");
                  table.reload('main-table1');
              	 //$(".save-settings").hide();
              }else{
              	  $(".lable-list .lable").eq(1).addClass("cur").siblings().removeClass("cur");
                 //$(".save-settings").show();
              }
          });


          var table2 = table.render({
            elem: "#main-table1",
            id: 'main-table1',
            url:"/api/score/basic/getList?pid=0&enterType=2&fid="+fid+"&xnxq="+xnxq,
            page: {
              limit: 10,
              limits: [10, 20, 30, 40, 50],
              layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
            },
            cols: [
              [{
                  field: "itemName",
                  align: "center",
                  title: "成绩分项",
                  minWidth: 120
                },
                {
                  field: "startTime",
                  align: "center",
                  title: "录入开始时间",
                  minWidth: 120,
                  templet:function(d){
                  	return d.startTime.substring(0,d.startTime.length-2)
                  }
                },
                {
                  field: "endTime",
                  align: "center",
                  title: "录入截止时间",
                  minWidth: 120,
                  templet:function(d){
                  	return d.endTime.substring(0,d.endTime.length-2)
                  }
                },
                {
                  field: "options",
                  align: "center",
                  title: "操作",
                  toolbar: "#barDemo3",
                  minWidth: 120
                },
              ]
            ],
		    parseData:function(data){
		       $.ajax({
		           type: 'post',
		           dataType: 'json',
		           url: '/api/score/itemDictionary/getList?pid=0&deleteFlag=0&fid='+fid+"&xnxq="+xnxq,
		           data: {},
		           success: function (res){
		           		$("#addPoup select[name='itemCode']").html("<option value=''>请选择</option>");
		               if(res.data.length > 0){
			               res.data.forEach(function(info){
			               		var b = true;
			               		data.data.forEach(function (basic){
			               			if(info.itemCode == basic.itemCode){
			               				b = false;
			               			}
			               		
			               		});
			               		var str = "";
			               		if(b){
			               			str = "<option value='"+info.itemCode+"'>"+info.itemName+"</option>";
			               		}else{
			               			str = "<option value='"+info.itemCode+"' disabled>"+info.itemName+"</option>";
			               		}
			               		$("#addPoup select[name='itemCode']").append(str);
			               });
		               }
		           }
		       });
		    },
            done: function (res) {}
          })

          /* 监听工具条 */
          table.on("tool(dataTable1)", function (obj) {
            var data = obj.data; //获得当前行数据
            if (obj.event === "del") {
              	obj.del();
              	layer.msg("删除")
              	$.ajax({
		            type: 'post',
		            dataType: 'json',
		            url: '/api/score/basic/deleteById?',
		            data: {id:data.id},
		            success: function (res){
		                if (res.code == 200){
		                    table.reload("main-table1",{page: {curr: 1}});
		                }
		            }
		        });
            } else if (obj.event === "edit") {
              	layer.msg("修改")
              	console.log(data);
                $("#addPoup .title .name").html("编辑");
                form.val("editForm", {
                    "startTime": data.startTime.substring(0,data.startTime.length-2),
                    "itemCode": data.itemCode,
                    "endTime":data.endTime.substring(0,data.endTime.length-2),
                    "id":data.id
                });

                layer.open({
                    type: 1,
                    title: false,
                    closeBtn: false,
                    shadeClose: true,
                    isOutAnim: true,
                    content: $('#addPoup'),
                    area: ['auto', 'auto'],
                    success: function () {
                    },
                    
                }, function () {
                });
            }
          })

          //添加
          $(".add-lab span").on("click", function () {
          		$("#addPoup .title .name").html("添加");
                form.val("editForm", {
                    "startTime": "",
                    "itemName": "",
                    "endTime":"",
                    "id":""
                });
	            layer.open({
	              type: 1,
	              title: false,
	              closeBtn: false,
	              shadeClose: true,
	              isOutAnim: true,
	              content: $('#addPoup'),
	              area: ['auto', 'auto'],
	              success: function () {
	
	              },
	            }, function () {
	
	            });
          })
		  //添加确认
		  $("#addPoup .bottom .exam-sure").click(function (obj){
				var data = form.val("editForm");
				var itemName = $("#addPoup .popup-con select[name=itemCode] option[value="+data.itemCode+"]").html();
				data['itemName'] = itemName;
				data['fid'] = fid;
				data['enterType'] = 2;
				data['xnxq'] = xnxq;
				data['pid'] = 0;
				$.ajax({
		            type: 'get',
		            dataType: 'json',
		            url: '/api/score/basic/saveEntity',
		            data: data,
		            success: function (res){
		                if (res.code == 200){
		                	$("#addPoup .close").click();
		                    table.reload("main-table1",{page: {curr: 1}});
		                }
		            }
		        });
		  });
          //隐藏弹窗
          $('.close,.exam-cancle').on("click", function () {
              layer.closeAll();
          })

          //组件切换
          /* $(".main").on("click",".m-tab ul li",function(){
            // $(this).addClass("cur").siblings().removeClass("cur");
            layer.msg("当前操作未保存，是否离开此界面!");
          }) */

          //保存设置
           $(".save-settings span").click(function(){
           		var startTime = $("#startTime").val();
           		var endTime = $("#endTime").val();
           		var id = $(".layui-form input[name='id']").val();
           		var enterType = $(".layui-form-item .lab input[name='enter']:checked").val();
           		$.ajax({
		            type: 'get',
		            dataType: 'json',
		            url: '/api/score/basic/saveEntity',
		            data: {enterType:enterType,id:id,fid:fid,pid:0,xnxq:xnxq,startTime:startTime,endTime:endTime},
		            success: function (res){
		                if (res.code == 200){
		                	$(".content .lable.cur input[name=id]").val(res.id);
			                layer.msg("保存成功");
		                }
		            }
		        });
           })
        })
    })
  </script>

</body>

</html>