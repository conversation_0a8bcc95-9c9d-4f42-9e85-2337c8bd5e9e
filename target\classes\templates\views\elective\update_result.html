<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>选修课退课</title>
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/global.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/layui/css/layui.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/dropClass.css'">

</head>

<body>
    <div class="marker"></div>
    <div class="dialog" id="dropClass" style="display: none;">
        <div class="dialog-title">
<!--            <h3>XXXXXX</h3>-->
<!--            <span class="pu-close"></span>-->
        </div>
        <div class="dialog-con">
            <div class="tip-mes"><span></span>以下选修课不符合设置的选课数量规则，请修改调整或退课后再调整。</div>
            <ul class="course-list">
            </ul>
        </div>
        <div class="dialog-btn">
            <button class="pu-cancel" id="stuCancel">取消</button><button class="pu-sure" id="stuSure">确定</button>
        </div>
    </div>
    <!-- 成功 -->
     <div class="dialog" id="success" style="display: none;">
        <img src="/elective/images/success1.png" alt="">
        <p class="tip-mes">处理成功</p>
       
        <button class="pu-sure">确定</button>
     </div>
</body>
<script th:src="${_CPR_}+'/elective/js/jquery-3.3.1.min.js'"></script>
<script th:inline="javascript">
    let str = [[${str}]];
    let list = JSON.parse(str);
    if (list.length==0){
        $("#success").show();
    }else {
        var html = "";
        for (let i = 0; i < list.length; i++) {
            html+="<li>"+list[i]+"</li>";
        }
        $(".course-list").html(html);
        $("#dropClass").show();
    }
    $("#stuCancel,.pu-sure,.pu-close").click(function () {
        window.parent.postMessage(JSON.stringify({action: 1}), "*");
    })
</script>
</html>
