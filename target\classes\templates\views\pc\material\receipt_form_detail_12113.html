<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教材签收单</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/material/global.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/layui/css/layui.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/material/receipt_form_new.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/material/newPrints.css'}" media="print">
    <script th:src="${_CPR_+_VR_+'/plugin/layui/layui.js'}"></script>
    <style>
        .main .top .title {
            position: relative;
            width: 100%;
        }

        .main .top .title .print {
            position: absolute;
            top: 0;
            right: 46px;
            line-height: 36px;
            font-size: 14px;
            color: rgba(49, 143, 197, 0.73);
            cursor: pointer;
        }
    </style>
    <style type="text/css" media=print>
        .noprint {
            display: none
        }
        .lab-box {
            overflow: hidden;
            margin: 0 auto;
        }
    </style>
</head>

<body>
    <div class="main">
        <div class="top noprint">
            <div class="titles">
                <div class="back">返回</div>
                <div class="levelone">班教材签收单</div>
                <div class="icon"></div>
                <div class="leveltwo"></div>
            </div>
            <div class="r-title">
                <span class="export">导出</span>
                <span class="print" id="prints">打印</span>
            </div>
        </div>
        <div id="st-scroll">
            <div class="lab-box">
                <div class="tableDetail print-box" id="printArea" style="padding-top:24px;">
                    <div class="lab-title">
                        <h1></h1>
                        <div class="subtitle">
                            <div class="s-lab">
                                <div class="name">专业：</div>
                                <div class="title"></div>
                            </div>
                            <div class="s-lab">
                                <div class="name">班级：</div>
                                <div class="title"></div>
                            </div>
                            <div class="s-lab">
                                <div class="name">班主任：</div>
                                <div class="title"></div>
                            </div>
                            <div class="s-lab">
                                <div class="name">人数：</div>
                                <div class="title"></div>
                            </div>
                        </div>
                    </div>
                    <div class="cons tables">
                         <table>
                            <thead>
                                <tr>
                                    <th>教材名称</th>
                                    <th>ISBN号</th>
                                    <th>码洋</th>
                                    <th>折扣</th>
                                    <th>实洋</th>
                                    <th>学生用书数量</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                         </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
<script th:src="${_CPR_+_VR_+'/js/jquery-3.3.1.min.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/print.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/unitChange.js'}"></script>
<script th:src="@{${_CPR_+_VR_}+'/js/material/receipt_form_detail_12113.js'(v=${new java.util.Date().getTime()})}"></script>
<script th:src="${_CPR_+_VR_+'/js/my.util.js'}"></script>
<script th:inline="javascript">
    let fid = [[${fid}]];
    let indexId = [[${indexId}]];
    const _VR_ = [[${_VR_}]] || '';
</script>
</html>