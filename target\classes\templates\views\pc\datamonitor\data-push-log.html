<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">

<head>
    <link rel="stylesheet" th:href="${_CPR_}+'/css/global.css'">
    <th:block th:include="common :: header('数据推送日志')"/>
    <th:block th:include="common :: layui-css"/>
    <th:block th:include="common :: jquery-mCustomScrollbar-css"/>
    <link rel="stylesheet" th:href="${_CPR_+'/css/datamonitor/common.css?v=1.1.0'}">
    <link rel="stylesheet" th:href="${_CPR_+'/css/datamonitor/reset.css?v=1.1.0'}">
    <link rel="stylesheet" th:href="${_CPR_}+'/css/datamonitor/data.push.log.css?v=2.0.3'">
</head>

<body>
    <div class="main">
        <div class="form-con">
            <form class="layui-form layui-form-stu" id="logForm" action="" lay-filter="logForm">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">单位名称</label>
                        <div class="layui-input-inline">
                            <select name="fid" class="unit" lay-filter="unit" lay-search="">
                                <option value="">请选择</option>

                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">模块名称</label>
                        <div class="layui-input-inline">
                            <select name="module" class="module" lay-filter="module" lay-search="">
                                <option value="">请选择</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">表单名称</label>
                        <div class="layui-input-inline">
                            <select name="alias" class="alias" lay-filter="alias" lay-search="">
                                <option value="">请选择</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">目标表单</label>
                        <div class="layui-input-inline">
                            <select name="targetAlias" class="targetAlias" lay-filter="targetAlias" lay-search="">
                                <option value="">请选择</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">操作类型</label>
                        <div class="layui-input-inline">
                            <select name="opType" class="opType" lay-filter="opType" lay-search="">
                                <option value="">请选择</option>
                                <option value="1">新增</option>
                                <option value="2">修改</option>
                                <option value="3">删除</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">日志类型</label>
                        <div class="layui-input-inline">
                            <select name="type" class="type" lay-filter="type" lay-search="">
                                <option value="">请选择</option>
                                <option value="1">数据推送</option>
                                <option value="2">顶部按钮</option>
                                <option value="3">右侧按钮</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">触发人</label>
                        <div class="layui-input-inline">
                            <select name="uid" class="uid" lay-filter="uid" lay-search="">
                                <option value="">请选择</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">执行失败时间</label>
                        <div class="layui-input-inline">
                            <div class="times">
                                <input type="text" name="time" readonly="" placeholder="请选择" class="layui-input" id="time" lay-key="1">
                            </div>
                        </div>
                    </div>
                    <div class="layui-inline" style="display: none;">
                        <button type="reset" class="layui-btn layui-btn-primary layui-btn-reset">重置</button>
                    </div>
                </div>
            </form>
            <div class="form-btn">
                <button class="btn btn-search">查询</button>
                <button class="btn btn-reset">重置</button>
            </div>
        </div>
        <div class="tab-con">
            <div class="log-table">
                <table class="layui-hide" id="logTable" lay-filter="logTable"></table>
            </div>
            <div class="no-data" style="display: block;">
                <img th:src="${_CPR_}+'/images/datamonitor/no-data.png'" alt="">
                <span>请先选择需要查询的单位</span>
            </div>
        </div>
    </div>
<!--详情页 start-->
    <div class="main details-main" style="display:none;">
        <div class="top">
            <div class="title">
                <div class="back" onclick="back()">返回</div>
                <div class="levelone">数据推送日志</div>
                <div class="icon"></div>
                <div class="leveltwo">查看详情</div>
            </div>

        </div>
        <div class="con">
            <div class="failure-reason">
                <p>
                    <span>推送失败原因：</span>
                    <em class="error-code"></em>
                    <em class="error-msg"></em>
                </p>
                <div class="fr-inform">
                    <div class="f-lab">
                        <div class="name">失败原因：</div>
                        <div class="blurb error">

                        </div>
                    </div>
                </div>
            </div>
            <div class="tab">
                <ul>
                    <li class="cur">班级信息</li>
                    <li>教学班学生</li>
                    <li>课程库</li>
                </ul>
            </div>
            <div class="iframe-box">
                <iframe src="" style="display:block;" marginheight="0" marginwidth="0" frameborder="0" scrolling="yes" width="100%" height="100%" id="iframepage" name="iframepage"></iframe>
            </div>
            <div class="table-box" style="display: none;">
                <span class="name"></span>
                <span class="data"></span>
            </div>
        </div>
    </div>
<!--详情页 end-->

</body>
<script th:inline="javascript">
    document.domain = "chaoxing.com";
    let height = $(window).height() - 144 - 56;
    //获取页面高度，因为是三栏式布局，我把头部和底部的高度全部加上然后减掉
    $("#iframepage").attr("height", height);
    const referer = document.referrer ? document.referrer.match(/^(https?:\/\/)?(?:[^@\n]+@)?(?:www\.)?([^:\/\n?]+)/im)[2] : '';
    const academic = /^academic(-test)?(-gray)?\.chaoxing\.com$/;
    const args = !referer || referer && academic.test(referer) ? [[${query}]] || {} : {};
    const MODULE_DATA = {"1": "基础信息管理", "2": "培养过程管理", "3": "排课管理", "4": "选课管理", "5": "成绩管理", "6": "考务管理",
        "7": "学分管理", "8": "教材管理", "9": "评价系统", "10": "教师工作量", "11": "毕业管理", "12": "教学常规管理", "13": "教务对接"};
</script>
<th:block th:include="common :: layui-js"/>
<script th:src="${_CPR_}+'/js/datamonitor/data.push.log.js?v=2.6.1'"></script>
<script type="text/html" id="tmplToolBar">
    <div class="operate-column">
        <div lay-event="detail" class="detail" data-id="{{d.id}}">查看详情</div>
    </div>
</script>
</html>