<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

    <head>
        <meta charset="UTF-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>生成过程重修名单</title>
        <link rel="stylesheet" th:href="@{~/css/credit/global1.css}">
        <link rel="stylesheet" th:href="@{~/plugin/layui/css/layui.css}">
        <link rel="stylesheet" th:href="@{~/css/credit/reset.css}">
        <link rel="stylesheet" th:href="@{~/css/credit/index.css}">
        <link rel="stylesheet" th:href="@{~/css/credit/jquery.toast.css}">
        <script th:src="@{~/plugin/layui/layui.js}"></script>
    </head>
    <body>

    </body>
    <script th:src="@{~/js/jquery-3.3.1.min.js}"></script>
    <script th:src="@{~/js/jquery.toast.min.js}"></script>
    <script th:src="@{~/js/jquery.cookie.js}"></script>
    <script th:src="@{~/js/my.util.js}"></script>
    <script>
        var vo = {
            "formId": [[${vo.formId}]],
            "fid": [[${vo.fid}]],
            "uid": [[${vo.uid}]],
            "roleid": [[${vo.roleid}]],
            "queryId": "[[${vo.queryId}]]",
            "selectTotal": [[${vo.selectTotal}]]
        };
        layui.use(['jquery', 'laydate', "form"], function () {
            var form = layui.form;
            var $ = layui.jquery;

            // 先检查是否生成过重修名单
            $.ajax({
                type: "GET",
                url: "/api/new/credit/process/reset/checked/is/generated",
                data: vo,
                dataType: 'json',
                success: function (result) {
                    if (result && result.code === 200) {
                        var msg = "确认生成过程重修名单吗？";
                        if (result.data) {
                            msg = '本学期的过程重修名单已生成，是否重新生成？';
                        }
                        U.confirm({
                            title: "提示",
                            msg: msg,
                            sureBtnTxt: '确定',
                            cancelBtnTxt: '取消',
                            sure: function () {
                                // 点击 确定 按钮时执行的方法
                                generateProcessResetList();
                            },
                            cancel: function () {
                                U.closePop();
                            }
                        });
                    }
                }
            });
        });

        /**
         * 生成重修名单数据
         */
        function generateProcessResetList() {
            $.ajax({
                type: "GET",
                url: "/api/new/credit/process/reset/generate/list",
                data: vo,
                dataType: 'json',
                success: function (result) {
                    if (result) {
                        if (result.code === 200) {
                            layer.msg(result.msg, {icon: 1, time: 2000});
                            setTimeout(U.closePop, 2000);
                        } else {
                            layer.msg(result.msg, {icon: 2, time: 2000});
                        }
                    }
                }
            });
        }

    </script>

</html>