<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1, user-scalable=no"/>
    <meta name="format-detection" content="telephone=no,email=no,adress=no">
    <meta http-equiv="pragma" content="no-cache"/>
    <meta http-equiv="cache-control" content="no-cache"/>
    <meta http-equiv="expires" content="0"/>
    <title>教师考表</title>
    <script th:src="@{~/js/responsive-1.0.js}"></script>
    <link rel="stylesheet" th:href="@{~/css/examination/mobile/common.css}"/>
    <link rel="stylesheet" th:href="@{~/css/swiper.min.css}"/>
    <link rel="stylesheet" th:href="@{~/css/examination/mobile/schedule.css}"/>
    <link rel="stylesheet" th:href="@{~/css/examination/mobile/iosSelect.css}">
    <link rel="stylesheet" th:href="@{~/css/examination/mobile/newStyle.css?v=20240313}"/>
</head>

<body>
<!--<div class="tophead">-->
<!--    <div class="head bottomLine">-->
<!--        <div class="leftHead">-->
<!--            <div class="back"></div>-->
<!--        </div>-->
<!--        <div class="centerHead">-->
<!--            <div class="selectBox">-->
<!--                <div class="selectWeek"><span class="week" week='1'>我的考表</span></div>-->
<!--            </div>-->
<!--        </div>-->
<!--    </div>-->
<!--</div>-->
<div class="batchSel" id="batchSel">
    <h3>当前批次：<em></em></h3> <span class="arrow"></span>
</div>
<div class="table week-table">
    <div class="tips">滑动日期切换</div>
    <div class="swiper-container week-swiper">
        <div class="swiper-wrapper">
            <!-- <div class="swiper-slide">
                <table class="scheduleHead"></table>
            </div> -->
        </div>
    </div>
    <div class="scheduleWrap">
        <!--        <table id="scheduleTable"></table>-->
    </div>
</div>
<div class="batchDetail">
    <div class="batchCon">
        <div class="item-list">
            <div class="item">
                <h3>考试科目：</h3>
                <div id="kskm">线性代数</div>
            </div>
            <div class="item">
                <h3>考试时间：</h3>
                <div id="kssj">周一/08:00/第1-2节</div>
            </div>
            <div class="item">
                <h3>考试时长：</h3>
                <div id="kssc">周一/08:00/第1-2节</div>
            </div>
            <div class="item">
                <h3>监考教师：</h3>
                <div id="jkjs">张三</div>
            </div>
            <div class="item">
                <h3>考场：</h3>
                <div id="kc">科研楼401</div>
            </div>
            <div class="item">
                <h3>教学楼：</h3>
                <div id="jxl">科研楼</div>
            </div>
            <div class="item">
                <h3>班级：</h3>
                <div id="bj">科研楼401</div>
            </div>
            <div class="item">
                <h3>年级：</h3>
                <div id="nj">2021级/2022级</div>
            </div>
            <div class="item">
                <h3>备注：</h3>
                <div id="remark"></div>
            </div>
        </div>
        <a class="nameList" id="nameList">查看考生名单</a>
    </div>
</div>

<script type="text/javascript" th:src="@{~/js/jquery-3.3.1.min.js}"></script>
<script type="text/javascript" th:src="@{~/js/swiper.min.js}"></script>
<script type="text/javascript" th:src="@{~/js/iosSelect.js}"></script>
<script type="text/javascript" th:src="@{~/js/CXJSBridge.js}"></script>
<script type="text/javascript" th:src="@{~/js/app.utils.js}"></script>
<script type="text/javascript" th:src="@{~/js/tools.js}"></script>
<script type="text/javascript" th:src="@{~/js/base.js}"></script>
<script type="text/javascript" th:src="@{~/js/my.util.js}"></script>

<script th:inline="javascript">
    //适配iphone头部
    addIphoneHeight();

    function addIphoneHeight() {
        var isIphone = /iphone/gi.test(navigator.userAgent);
        if (isIphone && (screen.height == 812 && screen.width == 375)) {
            $('body').addClass('iosxwrapMax');
        } else if (isIphone && (screen.width == 414)) {
            $('body').addClass('iospluswrapMax');
        } else if (isIphone) {
            $('body').addClass('ioswrapMax');
        }
    }

    var datelist = []
    var stime = [];
    var courseData = {};


    //初始化tbody
    function initTbale() {
        var weekLen = Math.ceil(datelist.length / 7);
        var th = "";
        for (var w = 0; w < weekLen; w++) {
            th += '<div class="swiper-slide"><table class="scheduleHead"><thead><tr><th><div class="weeks"></div></th>';
            for (var k = 0; k < 7; k++) {
                var idxK = k + w * 7;
                if (idxK >= datelist.length) {
                    break;
                }
                th += '<th class="th"><div class="thdiv"><span class="week">' +
                    datelist[idxK].weekDay + '</span><span class="weekdate">' + datelist[idxK].date +
                    '</span></div></th>'
            }
            th += '</tr></table></div>';
            var td = '<table class="scheduleTable"><tbody style="display: table;width: 100%">';
            for (var i = 0; i < stime.length; i++) {
                td += '<tr data-row="' + (i + 1) + '">';
                td += '<td class="col0"><p class="stime">' + stime[i].time.split('-')[0] + '</p><p class="stime">' + stime[i].time.split('-')[1] + '</p></td>';
                for (var j = 0; j < 7; j++) {
                    td += '<td></td>';
                }
                td += '</tr>';
            }
            td += '</tbody></table>'
            if (weekLen>1){
                $(".scheduleWrap").append(td);
            }else {
                $(".scheduleWrap").html(td);
            }
            for (var i = 0; i < 7; i++) {
                var idx = i + w * 7;
                if (idx >= datelist.length) {
                    break;
                }
                var date = datelist[idx].date1;
                var courseDatum = courseData[date];
                if (courseDatum != undefined) {
                    courseData[date].map(item => {
                        var cla = Math.floor(Math.random() * 11 + 1)
                        $(".scheduleTable").eq(w).find('tr[data-row=' + item.row + '] td').eq(i + 1).html('<div class="scheduleCon bg' + cla + '" data-value="' + item.pkjgbh + '"><h3>' + item.kskm + '</h3><h5>@' + item.kc + '</h5><h5>' + item.remark + '</h5></div>')
                    })
                }
            }
        }

        $(".week-swiper .swiper-wrapper").html(th)
        layoutFn();
        var tabHeight = $(window).height() - $(".scheduleWrap").offset().top;
        $(".scheduleWrap").css({
            'height': tabHeight,
            "overflow-y": "auto"
        })
        swiper1 = new Swiper('.week-swiper', {
            slidesPerView: 1,
            centeredSlides: true,
            spaceBetween: 38,
            on: {
                slideChangeTransitionEnd: function () {
                    var activeIndex = this.activeIndex;
                    $(".scheduleWrap .scheduleTable").eq(activeIndex).show().siblings().hide();
                    layoutFn();
                }
            },
        })
    }
</script>
<script th:inline="javascript">
    var fid = [[${fid}]];
    var uid = [[${uid}]];
    var kspcbh = [[${kspcbh}]];

    var xnxq = [[${xnxq}]];

    $(function () {
        getExaminationBatch();
    })
    //选择考试
    var data = [];
    function getExaminationBatch() {
        $.get("/examination/basic/batch", {fid: fid, sffb: '1', uid: uid, role: 1}, function (res) {
            if (res.code == 200) {
                var tempData = res.data;
                var exist = 'false';
                for (let tempDataKey in tempData) {
                    if (tempData[tempDataKey].kspcgl_kspcbh == kspcbh){
                        exist = 'true';
                        break;
                    }
                }
                if (kspcbh == undefined || kspcbh == '' || exist == "false") {
                    kspcbh = tempData[0].kspcgl_kspcbh;
                }
                if (xnxq == undefined || xnxq == '') {
                    xnxq = tempData[0].kspcgl_xnxq;
                }

                for (let i = 0; i < tempData.length; i++) {
                    let json = {
                        id: tempData[i].kspcgl_kspcbh,
                        value: tempData[i].kspcgl_kspcmc,
                        xnxq: tempData[i].kspcgl_xnxq,
                        ispublish: tempData[i].kspcgl_sffb,
                        kssj: tempData[i].kspcgl_kssj,
                        jssj: tempData[i].kspcgl_jssj
                    }
                    if (kspcbh != undefined && kspcbh != '') {
                        if (kspcbh == tempData[i].kspcgl_kspcbh && tempData[i].kspcgl_sffb == '已发布') {
                            $("#batchSel em").text(tempData[i].kspcgl_kspcmc)
                            $("#batchSel em").attr("data-value", tempData[i].kspcgl_kspcbh)
                            $("#batchSel em").attr("data-xnxq", tempData[i].kspcgl_xnxq)
                            $("#batchSel em").attr("data-kssj", tempData[i].kspcgl_kssj)
                            $("#batchSel em").attr("data-jssj", tempData[i].kspcgl_jssj)
                            updateTime(kspcbh, xnxq);

                        }
                    }
                    data.push(json);
                }
            }
        })
    }

    var showBankDom = document.querySelector('#batchSel');
    showBankDom.addEventListener('click', function () {
        var bankId = showBankDom.dataset['id'];
        var bankName = showBankDom.dataset['value'];
        var bankSelect = new IosSelect(1,
            [data], {
                // container: '.container',
                title: '选择考试批次',
                itemShowCount: 7,
                oneLevelId: bankId,
                callback: function (selectOneObj) {
                    if (selectOneObj.ispublish == undefined || selectOneObj.ispublish == "" || selectOneObj.ispublish == "未发布") {
                        U.fail("所选批次尚未发布")
                    } else {
                        $("#batchSel em").text(selectOneObj.value)
                        $("#batchSel em").attr("data-value", selectOneObj.id)
                        $("#batchSel em").attr("data-xnxq", selectOneObj.xnxq)
                        $("#batchSel em").attr("data-kssj", selectOneObj.kssj)
                        $("#batchSel em").attr("data-jssj", selectOneObj.jssj)
                        updateTime(selectOneObj.id, selectOneObj.xnxq);

                    }
                },

            });
    });

    function updateTime(bc, xnxq) {
        var kssj = $("#batchSel em").attr("data-kssj").replace(/-/g, "/");
        var jssj = $("#batchSel em").attr("data-jssj").replace(/-/g, "/");

        //　新日期数组
        let newDateArr = [];
        newDateArr.push(format1(kssj), format1(jssj));
        let startDate = newDateArr[newDateArr.length - 2].dateTime;
        let endDate = newDateArr[newDateArr.length - 1].dateTime;
        let startStamp = new Date(startDate).getTime();
        let endStamp = new Date(endDate).getTime();
        let diffCounts = Math.abs(startStamp - endStamp) / 1000 / 60 / 60 / 24;
        // 得出倒数第二天和倒数第一天的相差天数，依次递减，直到相差１天，表示是相邻的两天
        for (let index = diffCounts; index > 1; index--) {
            //　根据时间戳得出应该往后加的日期,并把新的日期从倒数第二个位置追加到新日期数组
            newDateArr.splice(newDateArr.length - 1, 0, format1(getNewDate(newDateArr[newDateArr.length - 2].dateTime)))
        }
        datelist = newDateArr;
        getExaminationSessions(bc, xnxq)

    }

    function getNewDate(time) {
        let date = new Date(time).getTime() + 1000 * 60 * 60 * 24;
        let Y = new Date(date).getFullYear() + '-';
        let M = (new Date(date).getMonth() + 1 < 10 ? '0' + (new Date(date).getMonth() + 1) : new Date(date).getMonth() + 1) + '-';
        let D = new Date(date).getDate() < 10 ? '0' + new Date(date).getDate() : new Date(date).getDate();
        return Y + M + D;
    }


    function getExaminationSessions(bc, xnxq) {
        $.get("/examination/basic/sessions", {fid: fid, xnxq: xnxq, bc: bc}, function (res) {
            if (res.code == 200) {
                stime = [];
                var tempData = res.data;
                var map = new Map();
                for (let i = 0; i < tempData.length; i++) {
                    var time = format2(tempData[i].ksccgl_kssj, tempData[i].ksccgl_jssj);
                    let json1 = {
                        time: time,
                        kssj: time.split('-')[0],
                        jssj: time.split('-')[1],
                        ccmc: tempData[i].ksccgl_ccmc
                    }
                    var value = map.get(time);
                    if (value == undefined) {
                        map.set(time, json1)
                        stime.push(json1)
                    }
                }
                if (stime.length > 1) {
                    for (let i = 0; i < stime.length; i++) {
                        for (let j = 0; j < stime.length - i - 1; j++) {
                            if (stime[j].kssj > stime[j + 1].kssj) {
                                let temp = stime[j];
                                stime[j] = stime[j + 1];
                                stime[j + 1] = temp
                            }
                        }
                    }
                }
                getExaminationResult(bc, xnxq)
            }else {
                U.fail("场次信息异常")
            }
        })
    }

    function getExaminationResult(bc, xnxq) {
        $.get("/examination/basic/result", {
            fid: fid,
            bc: bc,
            xnxq: xnxq,
            uid: uid
        }, function (res) {
            if (res.code == 200) {
                //置空
                courseData = []
                for (let i = 0; i < res.data.length; i++) {
                    let data = res.data[i];

                    var kssj = format1(data.pkjgcx_kssj);
                    var time = format2(data.pkjgcx_kssj, data.pkjgcx_jssj);
                    var courseDatum = courseData[kssj.date1];
                    let row = 0;
                    for (let j = 0; j < stime.length; j++) {
                        if (stime[j].kssj == time.split("-")[0] && stime[j].jssj == time.split("-")[1]) {
                            row = j + 1;
                            break;
                        }
                    }
                    let jkjs = ""
                    for (let j = 0; j < data.pkjgcx_jkjs.length; j++) {
                        jkjs += data.pkjgcx_jkjs[j].uname + ",";
                    }
                    let json = {
                        row: row,
                        kskm: data.pkjgcx_kskm,
                        kssj: kssj.date1 + " " + (fid == 147140 || fid == 250119 || 251502 == fid ? format3(data.pkjgcx_kssj) : format2(data.pkjgcx_kssj, data.pkjgcx_jssj)),
                        kssc: data.pkjgcx_kssc,
                        jkjs: jkjs.slice(0, jkjs.length - 1),
                        kc: data.pkjgcx_kc,
                        jxl: data.pkjgcx_kcsjxl,
                        bj: data.pkjgcx_xsssbj,
                        nj: data.pkjgcx_nj,
                        pkjgbh: data.pkjgcx_pkjgbh,
                        remark: data.pkjgcx_msg,
                    }
                    if (courseDatum == undefined) {
                        let tempArr = [];
                        tempArr.push(json)
                        courseData[kssj.date1] = tempArr
                    } else {
                        courseData[kssj.date1].push(json)
                    }
                }
                initTbale();
            } else {
                U.fail("当前无监考任务")
            }
        })
    }

    function format1(date) {
        if (date == undefined || date == null || date == '') {
            return '';
        }
        date = date.replace(/-/g, "/");
        var dateTime = new Date(date);
        var month = dateTime.getMonth() + 1;
        var day = dateTime.getDate();
        var weekDay = dateTime.getDay();
        var temp = "";
        switch (weekDay) {
            case 0:
                temp = "日";
                break;
            case 1:
                temp = "一";
                break;
            case 2:
                temp = "二";
                break;
            case 3:
                temp = "三";
                break;
            case 4:
                temp = "四";
                break;
            case 5:
                temp = "五";
                break;
            case 6:
                temp = "六";
                break;
            default:
                break
        }
        if (month < 10) {
            month = '0' + month;
        }
        if (day < 10) {
            day = '0' + day;
        }
        return {
            dateTime: dateTime,
            date: month + "/" + day,
            date1: month + "-" + day,
            weekDay: temp
        };

    }

    function format2(d1, d2) {
        if (d1 == undefined || d1 == null || d1 == '' || d2 == undefined || d2 == null || d2 == '') {
            return '';
        }
        d1 = d1.replace(/-/g, "/");
        d2 = d2.replace(/-/g, "/");
        var dateTime1 = new Date(d1);
        var dateTime2 = new Date(d2);
        var hours1 = dateTime1.getHours();
        var hours2 = dateTime2.getHours();

        if (hours1 < 10) {
            hours1 = "0" + hours1;
        }
        if (hours2 < 10) {
            hours2 = "0" + hours2;
        }
        var minutes1 = dateTime1.getMinutes();
        if (minutes1 < 10) {
            minutes1 = "0" + minutes1;
        }
        var minutes2 = dateTime2.getMinutes();
        if (minutes2 < 10) {
            minutes2 = "0" + minutes2;
        }
        return hours1 + ":" + minutes1 + "-" + hours2 + ":" + minutes2;

    }

    function format3(d1) {
        if (d1 == undefined || d1 == null || d1 == '') {
            return '';
        }
        d1 = d1.replace(/-/g, "/");
        var dateTime1 = new Date(d1);
        var hours1 = dateTime1.getHours();
        if (hours1 < 10) {
            hours1 = "0" + hours1;
        }
        var minutes1 = dateTime1.getMinutes();
        if (minutes1 < 10) {
            minutes1 = "0" + minutes1;
        }
        return hours1 + ":" + minutes1;
    }


    // 查看考场详情
    $(".scheduleWrap").on("click", "table tbody tr td .scheduleCon", function () {
        var row = $(this).parents("tr").attr("data-row");
        var key = datelist[$(this).parent().index() - 1].date1;
        var courseDatum = courseData[key];
        var dataJson = {};
        for (let i = 0; i < courseDatum.length; i++) {
            if (courseDatum[i].row == row) {
                dataJson = courseDatum[i];
                break;
            }
        }
        $("#kskm").html(dataJson.kskm)
        $("#kssj").html(dataJson.kssj)
        $("#kssc").html(dataJson.kssc)
        $("#jkjs").html(dataJson.jkjs)
        $("#kc").html(dataJson.kc)
        $("#jxl").html(dataJson.jxl)
        $("#bj").html(dataJson.bj)
        $("#nj").html(dataJson.nj)
        $("#remark").html(dataJson.remark)
        $(".batchCon").attr("data-value", $(this).attr("data-value"))

        $(".batchDetail").show();
    })
    $(".batchClose").click(function () {
        $(".batchDetail").hide();
    })
    $(".batchDetail").click(function (e) {
        if ($(e.target).hasClass('batchDetail')) {
            $(".batchDetail").hide();
        }
        e.stopPropagation();
        e.preventDefault();
    })

    $("#nameList").click(function () {
        var ua = navigator.userAgent.toLowerCase();
        if (ua && ua.indexOf("chaoxingstudy") != -1 && ua.indexOf('chaoxingstudypc') == -1) {
            // 在客户端里面才进行相关操作
            openA("考生名单", "/mobile/examination/stu?fid=" + fid + "&pkjgbh=" + $(".batchCon").attr("data-value"))
        } else {
            window.open("/mobile/examination/stu?fid=" + fid + "&pkjgbh=" + $(".batchCon").attr("data-value"))
        }
    })

    function layoutFn() {
        $(".week-table .week-swiper .swiper-slide").each(function () {
            let nums = $(this).find("table thead tr th").length;

            $(this).find("table thead tr th.th").css({"width": 90 / (nums - 1) + '%'})

            $(".scheduleWrap .scheduleTable").each(function () {
                if ($(this).css("display") == "block") {
                    $(this).find("tbody tr td:not(.col0)").css({"width": 90 / (nums - 1) + '%'})
                    $(this).find("tbody tr td:not(.col0)");
                    let start = nums;

                    $(this).find("tbody tr").each(function () {
                        for (var i = start; i <= 7; i++) {
                            $(this).find("td").eq(i).hide();
                        }
                    })


                }
            })

        })
    }
</script>
</body>
</html>