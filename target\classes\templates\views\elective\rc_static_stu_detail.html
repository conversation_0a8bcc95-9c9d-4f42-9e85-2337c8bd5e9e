<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no,initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <title>学生端-统计-详情</title>
    <script src="/elective/rollcall/js/responsive.js"></script>
    <link rel="stylesheet" href="/elective/rollcall/css/global.css">
    <link rel="stylesheet" href="/elective/rollcall/css/iosSelect.css">
    <link rel="stylesheet" href="/elective/rollcall/css/common.css">
    <link rel="stylesheet" href="/elective/rollcall/css/head.css">
    <link rel="stylesheet" href="/elective/rollcall/css/mescroll.min.css">
    <link rel="stylesheet" href="/elective/rollcall/css/student-statistics.css">
</head>

<body>
<div class="header">
    <div class="head-signal"></div>
    <div class="head-con">
        <a href="javascript:void(0)"  onclick="AppUtils.closeView();" class="head-return">
            <img src="/elective/rollcall/images/back-icon1.png" alt="">
        </a>
        <span class="head-title">
        <span th:text="${uname}">周传洋</span>
      </span>
    </div>
</div>

<div class="con-select">
    <ul>
        <li data-cla="course" class="course"><span>课程</span> <i></i></li>
        <li data-cla="date" id="selDate" data-start_id="10029" data-end_id="20029"
            class=""><span>日期</span> <i></i>
        </li>
    </ul>
</div>

<div class="stu-list-wrap mescroll" id="mescroll" style="padding-top:0;">
    <div class="class-mes">
        <ul>
            <li>正常:<i>0</i></li>
            <li th:each="state:${rollcallStateList}" th:inline="text">[[${state.stateNamePc}]]:<i>0</i></li>
        </ul>
    </div>
    <div class="total">
        <span>共<i>0</i>条记录</span>
    </div>

    <div class="stu-list">
    </div>
</div>

<div class="select-courses-dialog dialog-box" id="selectCourse">
    <div class="w-con active">
        <div class="w-head">
            <div class="cancle">取消</div>
            <div class="name">选择课程</div>
            <div class="btns">
                <span class="save">保存</span>
            </div>
        </div>
        <div class="w-box">
            <div class="all-select">全选</div>
            <ul>
            </ul>
        </div>

    </div>
</div>

</body>
<script src="/elective/rollcall/js/CXJSBridge.js"></script>
<script src="/elective/rollcall/js/jquery-3.3.1.min.js"></script>
<script src="/elective/rollcall/js/headHeight.js"></script>
<script src="/elective/rollcall/js/iosSelect.js"></script>
<script src="/elective/rollcall/js/mescroll.min.js"></script>
<script type="text/javascript" src="/js/app.utils.js"></script>
<script th:inline="javascript">
    var rollcallStateList = [[${rollcallStateList}]];
    var stuUid = [[${stuUid}]];
    var current = [[${current}]];

    var stateCss = ["late", "absenteeism", "leave", "early-departure", "others"];

    function _jsBridgeReady() {

        jsBridge.postNotification('CLIENT_TOOLBAR_TYPE', {
            'toolbarType': 0
        });

        jsBridge.postNotification('CLIENT_WEB_BOUNCES', {
            "forbiddenFlag": 1
        });

    }

    $.ajax({
        type: "POST",
        url: "/elective/rollcall/getCourseListV1",
        dataType: 'json',
        success: function (data) {
            var html = "";
            if (data.status) {
                for (var i = 0; i < data.list.length; i++) {
                    html += "<li>" + data.list[i].mc + "</li>";
                }
            }
            $(".w-box ul").html(html);
        }
    });


    $(document).ready(function () {

        setTimeout(function () {
            var stuH = $(window).height() - $(".stu-list-wrap").offset().top;
            $(".stu-list-wrap").css({
                height: stuH + "px"
            });
        }, 500)

        // 下拉加载 2025.4.23
        var mescroll = new MeScroll("mescroll", {
            up: {
                auto: true, //是否在初始化时以上拉加载的方式自动加载第一页数据; 默认false
                isBounce: true, //此处禁止ios回弹,解析(务必认真阅读,特别是最后一点): http://www.mescroll.com/qa.html#q10
                callback: getList, //上拉回调,此处可简写; 相当于 callback: function (page) { upCallback(page); }
                htmlNodata: '<p class="upwarp-nodata">-- 没有更多数据了 --</p>',
            }

        });


        $(".select-courses-dialog").on("click", ".w-con .w-box ul li", function () {
            $(this).toggleClass("cur");
            let currentlen = $(this).parent().find("li.cur").length;
            let allLen = $(this).parent().find("li").length;
            if (currentlen == allLen) {
                $(this).parent().siblings(".all-select").addClass("cur");
            } else {
                $(this).parent().siblings(".all-select").removeClass("cur");
            }
        })

        //全选

        $(".select-courses-dialog .w-con .w-box .all-select").click(function () {
            $(this).toggleClass("cur");
            if ($(this).hasClass("cur")) {
                $(this).parent().find("li").addClass("cur");
            } else {
                $(this).parent().find("li").removeClass("cur");
            }
        })


        //取消
        $(".select-courses-dialog .w-con .w-head .cancle").click(function () {
            $(".select-courses-dialog").hide();
            $(".con-select ul li.course").removeClass("active");
        })

        //保存
        $(".select-courses-dialog .w-con .w-head .btns").click(function () {
            $(".select-courses-dialog").hide();
            $(".con-select ul li.course").removeClass("active");

            let arr = [];
            $(".select-courses-dialog .w-con .w-box ul li.cur").each(function () {
                arr.push($(this).text());
            })
            if (arr.length==0){
                arr.push("课程");
            }


            $(".con-select ul li.course span").text(arr.join(","));
            mescroll.setPageNum(1);
            mescroll.triggerDownScroll();
        })


        //选择学期
        $(".con-select ul li.course").click(function () {
            $(this).addClass("active").siblings().removeClass("active");
            $(".select-courses-dialog").show();
        })

        $(".dialog-box").on('click', function (event) {
            if (!$(event.target).closest('.w-con').length) {
                // 如果点击的元素不是弹窗或其子元素，则隐藏弹窗
                $(".con-select ul li.course").removeClass("active");
                $(".con-select ul li.date").removeClass("active");
                $('.dialog-box').hide();
            }

        });






        function getList(page){
            getStuNum();
            let courseName = $(".con-select ul .course span").text();
            if (courseName=='课程'){
                courseName = '';
            }
            $.ajax({
                type: "get",
                url: "/elective/rollcall/getStaticStudentsDetailPage",
                data: {
                    xnxqh: current.xnxq_xnxqh,
                    stuUid:stuUid,
                    courseName:courseName,
                    courseTime: $("#selDate").find("span").text().replaceAll("日期","").replaceAll(/[年月]/g,"-").replaceAll("日",""),
                    page:page.num,
                },
                dataType: 'json',
                success: function (data) {
                    var html = '';
                    for (var i = 0; i < data.data.length; i++) {
                        html+='<div class="stu">';
                        html+='<div class="grace"></div>';
                        html+='<div class="course-name">'+data.data[i].course+'</div>';
                        html+='<div class="inform">';
                        html+='<span>第'+data.data[i].zc+'周</span>';
                        html+='<span>周'+data.data[i].xq+'</span>';
                        html+='<span>第'+data.data[i].kj+'节</span>';
                        html+='</div>';
                        if (data.data[i].val!=''){
                            html+='<div class="evaluate">';
                            html+='<div class="name">评价：</div>';
                            html+='<div class="text">'+data.data[i].val+'</div>';
                            html+='</div>';
                        }
                        if (data.data[i].cd == 0 && data.data[i].kk == 0 && data.data[i].zt == 0 && data.data[i].qj == 0 && data.data[i].state ==0) {
                            html+='<div class="state normal"><span>正常</span> <i></i></div>';
                        } else {
                            for (let j = 0; j < rollcallStateList.length; j++) {
                                if (rollcallStateList[j].type == 0 && (rollcallStateList[j].stateAlias == 'cd'|| rollcallStateList[j].stateAlias == 'zt') && data.data[i].cd == 1&& data.data[i].zt == 1){
                                    html+='<div class="state '+stateCss[j]+'"><span>迟到,早退</span> <i></i></div>';
                                    break;
                                }else if ((rollcallStateList[j].type == 0 && rollcallStateList[j].stateAlias == 'cd' && data.data[i].cd == 1)||
                                    (rollcallStateList[j].type == 0 && rollcallStateList[j].stateAlias == 'kk' && data.data[i].kk == 1)||
                                    (rollcallStateList[j].type == 0 && rollcallStateList[j].stateAlias == 'zt' && data.data[i].zt == 1)||
                                    (rollcallStateList[j].type == 0 && rollcallStateList[j].stateAlias == 'qj' && data.data[i].qj == 1)||
                                    (rollcallStateList[j].type == 1 && rollcallStateList[j].id == data.data[i].state)){
                                    html+='<div class="state '+stateCss[j]+'"><span>'+rollcallStateList[j].stateNamePc+'</span> <i></i></div>';
                                }
                            }
                        }
                        html+='</div>';
                    }
                    if (page.num==1){
                        $(".stu-list").html(html);
                    }else {
                        $(".stu-list").append(html);
                    }
                    $(".total span i").text(data.count);
                    mescroll.endSuccess(data.data.length,data.count);
                }
            })
        }
        function getStuNum() {
            let courseName = $(".con-select ul .course span").text();
            if (courseName=='课程'){
                courseName = '';
            }
            $.ajax({
                type: "POST",
                url: "/elective/rollcall/getStuDetailNum",
                data: {
                    xnxqh: current.xnxq_xnxqh,
                    stuUid:stuUid,
                    courseName:courseName,
                    courseTime:$("#selDate").find("span").text().replaceAll("日期","").replaceAll(/[年月]/g,"-").replaceAll("日",""),
                },
                dataType: 'json',
                success: function (data) {
                    for (let i = 0; i < data.data.length; i++) {
                        $(".class-mes ul").find("li").eq(i).find("i").text(data.data[i]);
                    }
                }
            });
        }






        //时间

        var showDataDom = document.querySelector('#selDate');

        const today = new Date(); // 获取今天的日期
        const dates = []; // 用于存储最近30天的日期

        for (let i = 0; i < 30; i++) {
            const date = new Date(today);
            date.setDate(today.getDate() - i); // 逐天减去天数

            // 格式化日期为 "YYYY年MM月DD日"
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1
            const day = String(date.getDate()).padStart(2, '0');

            const formattedDate = `${year}年${month}月${day}日`;
            dates.unshift(formattedDate);
        }

// 输出结果


        var startDate = dates.map(function (item, index) {
            return {'id': "100" + index, 'value': item}
        });
        var endData = dates.map(function (item, index) {
            return {'id': "200" + index, 'value': item}
        });


        $(document).on('touchstart', ".ios-select-widget-box", function (event) {
            $(".con-select ul li").removeClass("active");
        });

        showDataDom.addEventListener('click', function () {
            $(this).addClass('active').siblings().removeClass('active');
            var startId = showDataDom.dataset['start_id'];
            var endId = showDataDom.dataset['end_id'];

            var sanguoSelect = new IosSelect(2,
                [startDate, endData],
                {
                    title: '选择日期',
                    sureText: '保存',
                    itemHeight: 35,
                    oneLevelId: startId,
                    twoLevelId: endId,
                    callback: function (selectOneObj, selectTwoObj) {

                        $("#selDate").find("span").text(selectOneObj.value + ' ~' + selectTwoObj.value)
                        showDataDom.dataset['start_id'] = selectOneObj.id;
                        showDataDom.dataset['end_id'] = selectTwoObj.id;
                        mescroll.setPageNum(1);
                        mescroll.triggerDownScroll();
                    }
                });
        });

        //时间end


    })
</script>

</html>