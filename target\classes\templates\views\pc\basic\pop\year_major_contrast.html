<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>复制</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/basic/global.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/basic/slideCommon.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/basic/dialog.css'}">
</head>

<body>
<div class="mask"></div>
<div class="dialog" id="inheritDialog">
    <div class="dialog-con">
        <div class="set-con">
            <div class="set-item">
                <h4>源开设年度</h4>
                <div class="set-block">
                    <div class="j-search-con single-box">
                        <input type="text" placeholder="请选择" readonly="" class="schoolSel" formAlias = "ndzydz" fieldAlias = "ndzydz_nj">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <ul data-name="ndzydz_nj">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="set-item">
                <h4>源所属系部</h4>
                <div class="set-block">
                    <div class="j-search-con single-box">
                        <input type="text" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <ul data-name="ndzydz_ssxb">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="set-item">
                <h4>源专业名称</h4>
                <div class="set-block">
                    <div class="j-search-con single-box">
                        <input type="text" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <ul data-name="ndzydz_zymc">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="set-item">
                <h4>目标开设年度</h4>
                <div class="set-block">
                    <div class="j-search-con single-box">
                        <input type="text" placeholder="请选择" readonly="" class="schoolSel" formAlias = "njsj" fieldAlias = "nj_njmc">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <ul data-name="grade">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="dialog-footer">
        <button class="btnCancel">取消</button>
        <button class="btnSure" id="btnInheritSure">确定</button>
    </div>
</div>
<div class="dialog" id="tipDialog">
    <div class="dialog-title">
        <h5>复制</h5><span class="close"></span>
    </div>
    <div class="dialog-con">
        <span></span> 该专业在该年度已经存在对照数据
    </div>
    <div class="dialog-footer">
        <button class="btnSure" id="tipSure">确定</button>
    </div>
</div>
</body>
<script th:src="${_CPR_+_VR_+'/js/jquery-1.11.3.min.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/basic/slideCommon.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/my.util.js'}"></script>
<script th:inline="javascript">
    const _VR_ = [[${_VR_}]];
    $(function () {
        $("#btnInheritSure").click(function () {
            const selParams = {};
            $("#inheritDialog ul").each(function () {
                const key = $(this).attr("data-name");
                selParams[key] = $(this).find(".active").text();
            })
            const requiredFields = [
                { key: 'ndzydz_nj', message: '请选择源开设年度' },
                { key: 'ndzydz_ssxb', message: '请选择源所属系部' },
                { key: 'ndzydz_zymc', message: '请选择源专业名称' },
                { key: 'grade', message: '请选择目标开设年度' }
            ];
            for (const field of requiredFields) {
                if (!selParams[field.key]) {
                    U.fail(field.message);
                    return false;
                }
            }
            $.post(_VR_ + "/basic/pop/copyYearMajorContrast", $.param(selParams), function (result) {
                if (!result.status) {
                    U.fail(result.msg);
                    return false;
                }
                U.success("复制成功");
                setTimeout(function () {
                    window.parent.postMessage(JSON.stringify({action: 1}), "*");
                }, 2000);
            }, "json");
        })
        $("#tipSure,#tipDialog .close").click(function () {
            $("#inheritDialog").show();
            $("#tipDialog").hide();
        })
        $("#inheritDialog .close,.btnCancel").click(function () {
            $("#inheritDialog,.mask").hide();
            window.parent.postMessage(JSON.stringify({action: 1}), "*");
        })
        let data = [];
        $.post(_VR_ + "/basic/pop/getMajorInfoData", function (result) {
            if (result.data) {
                data = result.data;
                const departments = [...new Set(data.map(item => item.zysj_ssyx))];
                const majors = [...new Set(data.map(item => item.zysj_zymc))];
                let deptHtml = "", majorHtml = "";
                for (let i = 0; i < departments.length; i++) {
                    deptHtml += `<li>${departments[i]}</li>`;
                }
                for (let i = 0; i < majors.length; i++) {
                    majorHtml += `<li>${majors[i]}</li>`;
                }
                $("ul[data-name='ndzydz_ssxb']").html(deptHtml);
                $("ul[data-name='ndzydz_zymc']").html(majorHtml);
            }
        }, "json");
        $(".j-search-con.single-box").on("click", ".j-select-year li ", function (e) {
            let idx = $(this).parents(".set-item").index();
            if (idx === 1) {
                $(".set-con .set-item:eq(2) input").val("");
                let dept = $(this).text();
                const majors = data.filter(item => item.zysj_ssyx === dept).map(item => item.zysj_zymc);
                let majorHtml = "";
                for (let i = 0; i < majors.length; i++) {
                    majorHtml += `<li>${majors[i]}</li>`;
                }
                $("ul[data-name='ndzydz_zymc']").html(majorHtml);
            }
            stopBubble(e);
        });
    })
</script>

</html>