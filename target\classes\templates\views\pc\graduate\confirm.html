<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <title>Title</title>
</head>
<body>

</body>
<script th:src="@{~/js/jquery-3.3.1.min.js}"></script>
<script th:src="@{~/js/my.util.js}"></script>
<script th:inline="javascript">
    const _VR_ = [[${_VR_}]] || '';
    let viewType = [[${viewType}]];
    var bo = [[${bo}]];
    $(function () {
        //移除
        if (viewType == 1) {
            U.confirm({
                title: "提示",
                msg: "该生已到达毕业学年学期，确定移除？",
                sureBtnTxt: '确定',
                cancelBtnTxt: '取消',
                sure: function () {
                    $.get("/api/graduate/remove", JSON.parse(bo), function (res) {
                        res.code === 200 ? U.success("正在移除") : U.fail("移除失败");
                        setTimeout(U.closePop, 2000)
                    })
                },
                cancel: function () {
                    U.closePop();
                }
            });
        }
        if (viewType == 2) {
            U.confirm({
                title: "提示",
                msg: "请确定已在【班级信息表】中维护毕业学年学期",
                sureBtnTxt: '确定',
                cancelBtnTxt: '取消',
                sure: function () {
                    $.get("/api/graduate/generator", JSON.parse(bo), function (res) {
                        res.code === 200 ? U.success("正在生成") : U.fail("正在生成，请勿重复点击");
                        setTimeout(U.closePop, 2000)
                    })
                },
                cancel: function () {
                    U.closePop();
                }
            });
        }
        if (viewType == 3) {
            U.ajax({
                type: 'post',
                url: "/graduate/rule/checkRangeData",
                dataType: 'json',
                success: function (res) {
                    if (res.code == 200) {
                        $.get("/api/graduate/init", JSON.parse(bo), function (res) {
                            res.success ? U.success("正在生成") : U.fail("正在生成，请勿重复点击");
                            setTimeout(U.closePop, 2000)
                        })
                    }else {
                        U.confirm({
                            title: "提示",
                            msg: "当前毕业年度已初始化毕业专业，是否继续？（不会覆盖已设置的毕业条件）",
                            sureBtnTxt: '确定',
                            cancelBtnTxt: '取消',
                            sure: function () {
                                $.get("/api/graduate/init", JSON.parse(bo), function (res) {
                                    res.success  ? U.success("正在生成") : U.fail("正在生成，请勿重复点击");
                                    setTimeout(U.closePop, 2000)
                                })
                            },
                            cancel: function () {
                                U.closePop();
                            }
                        });

                    }
                }
            })
        }
        if (viewType == 4) {
            U.ajax({
                type: 'post',
                url: "/graduate/rule/checkQualificationData",
                dataType: 'json',
                success: function (res) {
                    if (res.code == 200) {
                        $.get("/api/graduate/qualification/init", JSON.parse(bo), function (res) {
                            res.success ? U.success("正在生成") : U.fail("正在生成，请勿重复点击");
                            setTimeout(U.closePop, 2000)
                        })
                    }else {
                        U.confirm({
                            title: "提示",
                            msg: "当前毕业年度已生成毕业名单，是否继续？（不会覆盖审核通过的毕业名单）",
                            sureBtnTxt: '确定',
                            cancelBtnTxt: '取消',
                            sure: function () {
                                $.get("/api/graduate/qualification/init", JSON.parse(bo), function (res) {
                                    res.success ? U.success("正在生成") : U.fail("正在生成，请勿重复点击");
                                    setTimeout(U.closePop, 2000)
                                })
                            },
                            cancel: function () {
                                U.closePop();
                            }
                        });

                    }
                }
            })

        }
        if (viewType == -1) {
            U.fail("当前页面陷入了的未知领域", 10_000)
        }
    })
</script>
</html>