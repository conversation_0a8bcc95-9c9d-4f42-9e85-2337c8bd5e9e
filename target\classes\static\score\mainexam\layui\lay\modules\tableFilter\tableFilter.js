/**
 TABLEFILTER
 **/

layui.define(['table', 'jquery', 'form','tree','laydate'], function (   exports) {

    var MOD_NAME = 'tableFilter',
        $ = layui.jquery,
        table = layui.table,
        form = layui.form,
        laydate = layui.laydate;
        tree=layui.tree;

    var treeId;
    var tableFilter = {
        "v" : '1.0.0'
    };

    var formtTreeObj=function(obj,selectedDatas,selectIds){

        if(obj["childClassifyList"]!=null&&obj["childClassifyList"].length>0){
            obj["title"]=obj["name"];
            obj["id"]=obj["classifyId"];
            var childClassifyList=obj["childClassifyList"];
            var children=[];
            for(var i=0;i<childClassifyList.length;i++){
                children.push(formtTreeObj(childClassifyList[i],selectedDatas,selectIds));
            }
            // if(selectedDatas.indexOf(obj["classifyId"])>=0){
            //     obj["prechecked"]=true;
            // }
            obj["children"]=children;
            return obj;
        }else{

            obj["title"]=obj["name"];
            obj["id"]=obj["classifyId"];
            obj["children"]=[];
            if(selectedDatas.indexOf(obj["classifyId"])>=0){
                //obj["prechecked"]=true;
                selectIds.push(obj["id"]);
            }

        }
        return obj;
    }


    var getChildrenTreeNode=function(treeData,rootNode, id){

        if(treeData!=null){
            for(var i=0;i<treeData.length;i++){
                if(treeData[i]["id"]==id)return rootNode;
                else{
                    var data=getChildrenTreeNode(treeData[i]["children"],rootNode,id);
                    if(data!=null)return data;
                }
                //else return getChildrenTreeNode(treeData[i]["children"],rootNode,id);
            }

        }
        return null;
    }

    var getTreeRootNodeById=function(parentTreeDatas, id){
        if(parentTreeDatas!=null){
            for(var i=0;i<parentTreeDatas.length;i++){
                if(parentTreeDatas[i]["id"]==id)return parentTreeDatas[i];
                else{
                    var data=getChildrenTreeNode(parentTreeDatas[i]["children"],parentTreeDatas[i],id);
                    if(data!=null)return data;
                }



            }
        }


    }

    var getTreeData=function(obj,selectedIds){
        var allDatas=[];

        if(selectedIds.length == 0){
            return allDatas;
        }
        if(obj["childClassifyList"]!=null&&obj["childClassifyList"].length>0){

            var  classifyId= obj["classifyId"];
            if(selectedIds.indexOf(classifyId)>=0){

                for(var i=0;i<obj["childClassifyList"].length;i++){
                    var data=[];
                    data.push(classifyId);
                    var childData=getTreeData(obj["childClassifyList"][i],selectedIds);
                    if(childData!=null&&childData.length>0){

                        data.push.apply(data,childData);
                        allDatas.push(data.join(","));
                    }

                }



            }

        }else{

            var  classifyId= obj["classifyId"];
            if(selectedIds.indexOf(classifyId)>=0){
                allDatas.push(obj["classifyId"]);
            }
        }

        return allDatas;

    }

    //缓存
    tableFilter.cache = {}

    //渲染
    tableFilter.render = function(opt){

        //配置默认值
        var elem = $(opt.elem || '#table'),
            elemId = elem.attr("id") || "table_" + new Date().getTime(),
            filters = opt.filters || [],
            parent = opt.parent || 'body',
            mode = opt.mode || "local";

        //写入默认缓存
        // tableFilter.cache[elemId]={};
        var cacheKey = 'cacheKey';
		var cache = JSON.parse(window.sessionStorage.getItem(cacheKey)) || {};
		tableFilter.cache[elemId]=cache;

        //主运行
        var main = function (){

            //默认过滤
            if(mode == "local"){
                var trsIndex = tableFilter.getShowTrIndex(elem, elemId, filters);
                if(trsIndex.length > 0){
                    var trs = elem.next().find('.layui-table-body tr');
                    trs.each(function(i, tr){
                        if($.inArray($(tr).data("index"), trsIndex) != -1){
                            $(tr).removeClass("layui-hide")
                        }else{
                            $(tr).addClass("layui-hide")
                        }
                    })
                }else{
                    elem.next().find('.layui-table-body tr').removeClass("layui-hide")
                }
            }

            //遍历过滤项
            layui.each(filters, function(i, filter){
                var filterField = filter.field,
                    filterName = filter.name || filter.field,
                    filterType = filter.type || "input",
                    filterData = filter.data || [],
                    filterUrl = filter.url || "";

                //插入图标
                var th = elem.next().find('.layui-table-header th[data-field="'+filterField+'"]');
                var icon = filterType == 'input' ? 'layui-icon-search' : 'layui-icon-down';
                var filterIcon = $('<span class="layui-table-filter layui-inline"><i class="layui-icon '+icon+'"></i></span>');
                th.find('.layui-table-cell').append(filterIcon)

                //图标默认高亮
                if(tableFilter.cache[elemId][filterName]){
                    filterIcon.addClass("tableFilter-has")
                }else{
                    filterIcon.removeClass("tableFilter-has")
                }

                //图标点击事件
                filterIcon.on("click", function(e) {
                    e.stopPropagation();
                    //得到过滤项的选项
                    //如果开启本地 并且没设置数据 就读本地数据
                    //console.log(mode == "local", filterData.length <= 0, !filterUrl, filterType != "input")
                    if(filterData.length <= 0 && !filterUrl && filterType != "input"){
                        filterData = tableFilter.eachTds(elem, filterField);
                    }

                    //弹出层
                    var t = $(this).offset().top + $(parent).scrollTop() + $(this).outerHeight() +"px";
                    var l = $(this).offset().left - ($('body').outerWidth(true) - $(parent).outerWidth(true)) - 64 +"px";
                    var filterBox = $('<div class="layui-table-filter-view" style="top:'+t+';left:'+l+';"><div class="layui-table-filter-box"><form class="layui-form" lay-filter="table-filter-form"></form></div></div>');
                    if(filterType == "input"){
                        filterBox.find('form').append('<input type="search" name="'+filterName+'" lay-verify="required" lay-verType="tips" placeholder="关键词" class="layui-input">');
                    }
                    if(filterType == "checkbox"){
                        filterBox.find('form').append('<ul class="check"></ul>');
                        if(!filterUrl){
                            layui.each(filterData, function(i, item){
                                filterBox.find('ul').append('<li><input type="checkbox" name="'+filterName+'['+item.key+']" value="'+item.key+'" title="'+item.value+'" lay-skin="primary"></li>');
                            })
                        }
                    }
                    if(filterType == "radio"){
                        filterBox.find('form').append('<ul class="radio"></ul>');
                        if(!filterUrl){
                            filterBox.find('ul').append('<li><input type="radio" lay-filter="selectType" name="'+filterName+'" value="" title="全部" checked></li>');
                            layui.each(filterData, function(i, item){
                                filterBox.find('ul').append('<li><input lay-filter="selectType" type="radio" name="'+filterName+'" value="'+item.key+'" title="'+item.value+'"></li>');
                            })
                        }
                    }
                    if(filterType == "tree"){
                        treeId="tree_"+new Date().getTime();
                        filterBox.find('form').append('<ul id="'+treeId+'" class="filter-box-tree"></ul>');
                        if(!filterUrl){
                            tree.render({
                                elem: '#'+id, //指定元素，生成的树放到哪个元素上
                                check: 'checkbox', //勾选风格
                                skin: 'as', //设定皮肤
                                drag: true,//点击每一项时是否生成提示信息
                                checkboxName: 'aa[]',//复选框的name属性值
                                checkboxStyle: "",//设置复选框的样式，必须为字符串，css样式怎么写就怎么写
                                click: function(item) { //点击节点回调
                                    //alert(item);
                                },
                                change: function (item){//当当前input发生变化后所执行的回调

                                    resourceIds=item;
                                },
                                data: {//为元素添加额外数据，即在元素上添加data-xxx="yyy"，可选
                                    hasChild: true
                                },
                                nodes:filterData
                            });
                            // filterBox.find('.filter-tree').append('<li><input type="radio" lay-filter="selectType" name="'+filterName+'" value="" title="全部" checked></li>');
                            // layui.each(filterData, function(i, item){
                            //     filterBox.find('ul').append('<li><input lay-filter="selectType" type="radio" name="'+filterName+'" value="'+item.key+'" title="'+item.value+'"></li>');
                            // })
                        }
                    }

                    if(filterType == "date"){
                        let years = new Date().getFullYear();
						filterBox.find('form').append('<div class="layui-table-filter-date"></div>');
						filterBox.find('form').append('<input type="text" style="display:none;" name="'+filterName+'" lay-verify="required" lay-verType="tips" value="'+years+'" placeholder="请选择日期" class="layui-input">');
						
					}

                    // if(filterType == "input"){
                    filterBox.find('form').append('<button class="layui-btn layui-btn-normal layui-btn-sm" lay-submit lay-filter="tableFilter">确定</button>');
                    filterBox.find('form').append('<button type="button" class="layui-btn layui-btn-primary layui-btn-sm filter-del layui-btn-disabled" disabled>取消过滤</button>');
                    // }

                    //设置清除是否可用
                    $(this).hasClass('tableFilter-has') && filterBox.find('.filter-del').removeClass("layui-btn-disabled").removeAttr("disabled","disabled");

                    //加入DOM
                    $(parent).append(filterBox);
                    setTimeout(function(){$(".layui-table-filter-view").addClass("show");},0);

                    //赋值FORM
                    form.val("table-filter-form", tableFilter.toLayuiFrom(elemId, filterName, filterType));

                    //渲染layui form
                    form.render(null, 'table-filter-form');
                    //渲染日期
                    if(filterType == "date"){
                        laydate.render({
                            elem: '.layui-table-filter-date',
                            // range: true,
                            type: 'year',
                            value: new Date().getFullYear().toString(),
                            position: 'static',
                            showBottom: false,
                            change: function(value, date, endDate){
                                $('.layui-table-filter-date').next().val(value)
                            }
                        });
                    }
               

                    //渲染FORM 如果是searchInput 就默认选中
                    var searchInput = filterBox.find('form input[type="search"]');
                    searchInput.focus().select();

                    //处理异步filterData
                    if((filterType == 'checkbox' || filterType == 'radio'|| filterType == 'tree') && filterUrl){
                        var filterBoxUl = filterBox.find('.layui-table-filter-box ul');
                        filterBoxUl.append('<div class="loading"><i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i></div>');
                        $.getJSON(filterUrl + "?_t=" + new Date().getTime(), function(res, status, xhr){
                            filterBoxUl.empty();
                            if(filterType=="tree"){
                                var datas=[];
                                var selectedDatas=tableFilter.cache[elemId][filterName];
                                var selectIds=[];
                                layui.each(res.data, function(i, item){
                                    var data=formtTreeObj(item,!!selectedDatas?selectedDatas:"",selectIds);
                                    datas.push(data);

                                })
                                console.log(selectIds);

                                tree.render({
                                    elem: '#'+treeId, //指定元素，生成的树放到哪个元素上
                                    showCheckbox:true,
                                    onlyIconControl:true,
                                    id:treeId,
                                    data:datas,
                                    accordion:true,
                                    oncheck:function (obj) {

                                        setTimeout(function(){
                                            console.log(obj);

                                            var rootNode=getTreeRootNodeById(datas,obj.data["id"]);
                                            var rootInput= $("#"+treeId+" input[same=\"layuiTreeCheck\"][value=\""+rootNode["id"]+"\"]");
                                            var inputs = rootInput.closest(".layui-tree-set").siblings().find("input[same=\"layuiTreeCheck\"]:checked").prop("checked",false);

                                            //var inputs = $("#"+treeId+" input[same=\"layuiTreeCheck\"][value!=\""+obj.data["id"]+"\"]:checked");

                                            console.log(inputs)
                                            form.render("checkbox")
                                        },0)




                                    }
                                });

                                setTimeout(function(){
                                    tree.setChecked(treeId,selectIds);
                                },0)


                                // filterBox.find('.filter-tree').append('<li><input type="radio" lay-filter="selectType" name="'+filterName+'" value="" title="全部" checked></li>');
                                // layui.each(filterData, function(i, item){
                                //     filterBox.find('ul').append('<li><input lay-filter="selectType" type="radio" name="'+filterName+'" value="'+item.key+'" title="'+item.value+'"></li>');
                                // })
                            }else{
                                filterType == "radio" && filterBoxUl.append('<li><input type="radio" name="'+filterName+'" value="" title="全部" checked></li>');
                                layui.each(res.data, function(i, item){
                                    filterType == "checkbox" && filterBoxUl.append('<li><input type="checkbox" name="'+filterName+'['+item.key+']" value="'+item.key+'" title="'+item.value+'" lay-skin="primary"></li>');
                                    filterType == "radio" && filterBoxUl.append('<li><input lay-filter="selectType" type="radio" name="'+filterName+'" value="'+item.key+'" title="'+item.value+'"></li>');
                                })
                            }

                            form.render(null, 'table-filter-form');
                            form.val("table-filter-form", tableFilter.toLayuiFrom(elemId, filterName, filterType));
                        });
                    }

                    form.on('radio(selectType)',function(data){
                        //过滤项写入缓存
                        tableFilter.cache[elemId][filterName] = data.value
                        //如果有过滤项 icon就高亮
                        if(tableFilter.cache[elemId][filterName].length > 0){
                            filterIcon.addClass("tableFilter-has")
                        }else{
                            filterIcon.removeClass("tableFilter-has")
                        }
                        //写入回调函数

                        opt.done(tableFilter.cache[elemId]);

                        // $(".layui-table-filter-view").removeClass("show");
                        // setTimeout(function(){filterBox.remove();},300);

                    })

                    //点击确认开始过滤
                    form.on('submit(tableFilter)', function(data){
                        //重构复选框结果
                        if(filterType == "checkbox"){
                            var NEWfield = [];
                            for(var key in data.field){
                                NEWfield.push(data.field[key])
                            }
                            data.field[filterName] = NEWfield
                        }

                        if(filterType == "tree"){
                            var checkData=tree.getChecked(treeId);
                            var selectedIds=[];
                            for(var i=0;i<$("#"+treeId+" input[same=\"layuiTreeCheck\"]:checked").length;i++){
                                var value=$("#"+treeId+" input[same=\"layuiTreeCheck\"]:checked").eq(i).val();
                                selectedIds.push(value);
                            }
                            console.log(selectedIds);

                            //	var datas=getTreeData(checkData,selectedIds,[]);

                            var allDatas=getTreeData(checkData[0],selectedIds);

                            console.log(allDatas);
                            data.field[filterName] = allDatas.join(";");

                        }

                        //过滤项写入缓存
                        tableFilter.cache[elemId][filterName] = data.field[filterName];

                        //如果有过滤项 icon就高亮
                        if(tableFilter.cache[elemId][filterName].length > 0){
                            filterIcon.addClass("tableFilter-has")
                        }else{
                            filterIcon.removeClass("tableFilter-has")
                        }

                        if(mode == "local"){
                            //本地交叉过滤
                            var trsIndex = tableFilter.getShowTrIndex(elem, elemId, filters);
                            if(trsIndex.length > 0 || data.field[filterName].length > 0){
                                var trs = elem.next().find('.layui-table-body tr');
                                trs.each(function(i, tr){
                                    if($.inArray($(tr).data("index"), trsIndex) != -1){
                                        $(tr).removeClass("layui-hide")
                                    }else{
                                        $(tr).addClass("layui-hide")
                                    }
                                })
                            }else{
                                elem.next().find('.layui-table-body tr').removeClass("layui-hide")
                            }
                        }else if(mode == "api"){
                            //服务端交叉过滤
                            //将数组转字符串
                            var new_where = {};
                            for (var key in tableFilter.cache[elemId]) {
                                var filterKey = key,
                                    filterValue = tableFilter.cache[elemId][key];
                                if($.isArray(filterValue)){
                                    new_where[filterKey] = filterValue.join(",");
                                }else{
                                    new_where[filterKey] = filterValue;
                                }
                            }
                            table.reload(elemId,{ page: {curr: 1},"where":new_where});
                            window.sessionStorage.setItem(cacheKey,JSON.stringify(tableFilter.cache[elemId]));
                        }

                        //写入回调函数
                        opt.done(tableFilter.cache[elemId]);

                        $(".layui-table-filter-view").removeClass("show");
                        setTimeout(function(){filterBox.remove();},300);
                        return false;
                    })

                    //点击清除此项过滤
                    filterBox.find('.layui-table-filter-box .filter-del').on('click', function(e) {
                        delete tableFilter.cache[elemId][filterName];
                        filterIcon.removeClass("tableFilter-has");
                        if(mode == "local"){
                            var trsIndex = tableFilter.getShowTrIndex(elem, elemId, filters);
                            if(trsIndex.length > 0){
                                var trs = elem.next().find('.layui-table-body tr');
                                trs.each(function(i, tr){
                                    if($.inArray($(tr).data("index"), trsIndex) != -1){
                                        $(tr).removeClass("layui-hide")
                                    }else{
                                        // $(tr).addClass("layui-hide")
                                    }
                                })
                            }else{
                                elem.next().find('.layui-table-body tr').removeClass("layui-hide")
                            }
                        }else if(mode == "api"){
                            //需要清除where里的对应的值
                            var where = {};
                            where[filterName] = ''
                            table.reload(elemId,{"where" : where})
                        }

                        opt.done(tableFilter.cache[elemId]);
                        $(".layui-table-filter-view").removeClass("show");
                        setTimeout(function(){filterBox.remove();},50);
                    })

                    //点击其他区域关闭
                    $(document).mouseup(function(e){
                        var userSet_con = $('.layui-table-filter-view');
                        if(!userSet_con.is(e.target) && userSet_con.has(e.target).length === 0){
                            $(".layui-table-filter-view").removeClass("show");
                            setTimeout(function(){filterBox.remove();},50);
                        }
                    });

                })
            })

        };
        main();

        //函数返回
        var returnObj = {
            'config' : opt,
            'reload' : function(opt){
                main();
            }
        }
        return returnObj
    }

    //遍历行获取本地列集合 return tdsArray[]
    tableFilter.eachTds = function(elem, filterField){
        var tdsText = [],
            tdsArray = [];
        var tds = elem.next().find('.layui-table-body td[data-field="'+filterField+'"]');
        tds.each(function(i, td){
            tdsText.push($.trim(td.innerText))
        })
        tdsText = tableFilter.tool.uniqueObjArray(tdsText);
        layui.each(tdsText, function(i, item){
            tdsArray.push({'key':item, 'value':item})
        })
        return tdsArray;
    }

    //获取匹配的TR的data-index  return trsIndex[]
    tableFilter.getShowTrIndex = function(elem, elemId, filters){
        var trsIndex = [];
        var filterValues = tableFilter.cache[elemId];

        for (var key in filterValues) {
            var filterKey = key,
                filterValue = filterValues[key];

            //如果有name比对filterField
            layui.each(filters, function(i, item){
                if(filterKey == item.name){
                    filterKey = item.field
                }
            })

            var tds = elem.next().find('.layui-table-body td[data-field="'+filterKey+'"]');
            //获取这一次过滤的匹配
            var this_trsIndex = [];
            tds.each(function(i, td){
                if($.isArray(filterValue)){
                    //过滤值=数组 inArray 复选框
                    if($.inArray($.trim(td.innerText), filterValue) >= 0 && filterValue && filterValue.length > 0){
                        this_trsIndex.push($(td).parent().data("index"))
                    }
                }else{
                    //过滤值=字符串 indexOf 单选框 输入框
                    if($.trim(td.innerText).indexOf(filterValue) >= 0){
                        this_trsIndex.push($(td).parent().data("index"))
                    }
                }
            })
            //取最终结果 合并数组后去相同值
            //第一次 不合并
            if(trsIndex.length <= 0){
                trsIndex = this_trsIndex
            }else{
                if(this_trsIndex.length > 0){
                    //这一次有值 和前面N次取相同值
                    trsIndex = tableFilter.tool.getSameArray(trsIndex, this_trsIndex);
                }else{
                    //这一次没值 前面N次有值,如果字符串过滤未有值 就显示空
                    trsIndex = $.isArray(filterValue) ? trsIndex : [];
                }
            }
        }
        return tableFilter.tool.uniqueObjArray(trsIndex);
    }

    //JSON 数据转layuiFOMR 可用的 处理checkbox
    tableFilter.toLayuiFrom = function(elemId, filterName, filterType){
        var form_val = JSON.stringify(tableFilter.cache[elemId]);
        form_val = JSON.parse(form_val);
        if(filterType == "checkbox"){
            layui.each(form_val[filterName], function(i, value){
                form_val[filterName + "["+value+"]"] = true;
            })
            delete form_val[filterName];
        }
        return form_val;
    }

    //隐藏选择器
    tableFilter.hide = function(){
        $('.layui-table-filter-view').remove();
    }

    //工具
    tableFilter.tool = {
        //数组&对象数组去重
        'uniqueObjArray' : function(arr, type){
            var newArr = [];
            var tArr = [];
            if(arr.length == 0){
                return arr;
            }else{
                if(type){
                    for(var i=0;i<arr.length;i++){
                        if(!tArr[arr[i][type]]){
                            newArr.push(arr[i]);
                            tArr[arr[i][type]] = true;
                        }
                    }
                    return newArr;
                }else{
                    for(var i=0;i<arr.length;i++){
                        if(!tArr[arr[i]]){
                            newArr.push(arr[i]);
                            tArr[arr[i]] = true;
                        }
                    }
                    return newArr;
                }
            }
        },
        //合并数组取相同项
        'getSameArray' : function(arry1, arry2){
            var newArr = [];
            for (var i = 0; i < arry1.length; i++) {
                for (var j = 0; j < arry2.length; j++) {
                    if(arry2[j] === arry1[i]){
                        newArr.push(arry2[j]);
                    }
                }
            }
            return newArr;
        }
    }

    //输出接口
    exports(MOD_NAME, tableFilter);
});    