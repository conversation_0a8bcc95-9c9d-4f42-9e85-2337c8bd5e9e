<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">

<head>
    <th:block th:include="common :: header('提示')"/>
    <style>
        * {
            margin: 0;
            padding: 0;
        }

        #jwTips {
            width: 600px;
            height: 100%;
            overflow: hidden;
            font-family: PingFang SC;
            position: fixed;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            z-index: 9999;
            font-weight: 400;
            background-color: #fff;
        }

        #jwTips .tip-con-wrap {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 340px;
        }

        #jwTips .tip-con {
            margin: 40px 100px;
            font-size: 16px;
        }

        #jwTips .tip-con img {
            display: block;
            margin: 0 auto 24px;
        }

        #jwTips .tip-con h4 {
            color: #1D2129;
            text-align: center;
            margin-bottom: 4px;
            font-weight: 400;
            font-size: 16px;
        }

        #jwTips .tip-con p {
            color: #4E5969;
            text-align: center;
            line-height: 1.5;
            font-size: 16px;
        }

        #jwTips  .btn {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            cursor: pointer;
            border-top: 1px solid #ddd;
            height: 60px;
        }

        #jwTips  .btn .btn-cancel {
            border: 1px solid #E5E6EB;
            padding: 0 30px;
            height: 36px;
            font-size: 14px;
            border-radius: 18px;
            color: #4E5969;
            margin-right: 16px;
            background-color: #fff;
        }

        #jwTips  .btn .btn-sure {
            color: #fff;
            background: #4d88ff;
            border: 1px solid #4d88ff;
            padding: 0 30px;
            height: 36px;
            font-size: 14px;
            border-radius: 18px;
            display: block;
            margin-right: 30px;
        }

        #tipLoading {
            animation: rotate 2s linear infinite;
        }

        @keyframes rotate {
            0% {
                transform: rotate(0);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    </style>
</head>

<body>
<!-- 取消提示 -->
<div id="jwTips" style="display: none;">
    <div class="tip-con-wrap">
        <div class="tip-con">
            <img id="tipLoading" th:src="${_CPR_}+'/images/form/icon-loading.png'" alt="">
            <!--            <h4>提示提示提示提示提示提示提示提示提示提示提示</h4>-->
            <p>是否开始查重是否开始查重是否开始查重是否开始查重是否开始查重</p>
        </div>
    </div>
    <div class="btn" style=" display: none; ">
        <button class="btn btn-cancel" onclick="cancel()">取消</button>
        <button class="btn btn-sure" onclick="sure()">确定</button>
    </div>
</div>

</body>
<script th:inline="javascript">
    window.parent.postMessage(JSON.stringify({"command": "urlPopWndClose", "type": "register"}), '*');
    $(window).on('message', function (e) {
        const message = e.originalEvent.data; // 获取 postMessage 传递的数据
        const data = JSON.parse(message);
        if (data.command === "urlPopWndClose" && data.appName === "officeApp") {
            window.parent.postMessage(JSON.stringify({action: 1}), "*");
        }
    });
    const CPR = [[${_CPR_}]];
    const icons = {
        "verifying": CPR + '/images/form/icon-loading.png',
        "hint": CPR + '/images/form/icon-tips.png',
        "success": CPR + '/images/form/icon-success.png',
        "error": CPR + '/images/form/icon-error.png'
    };
    const $jwTips = $("#jwTips");
    const $img = $("#jwTips img");
    const $p = $("#jwTips p");
    const $btn = $("#jwTips .btn");
    const args = [[${bo}]] || {}, vars = {};
    const verifying = [[${verifying}]] || '安全检测中';
    $p.text(verifying);
    $jwTips.show();
    U.ajax({
        type: 'post',
        url: '/api/form-btn/gm/verify',
        loading: false,
        data: args,
        success: function (data) {
            if (U.er(data)) {
                resetIconMsg(icons.error, data.msg);
            } else {
                let res = data.data;
                if (!res.pass) {
                    resetIconMsg(icons.error, res.msg);
                } else {
                    args.token = res.token;
                    vars.wait = res.wait;
                    if (!res.confirm) {
                        exe();
                    } else {
                        resetIconMsg(icons.hint, res.msg || '检测通过，确认执行？');
                        $btn.show();
                    }
                }
            }
        }
    });

    function exe() {
        $img.attr("id", "tipLoading");
        $img.attr("src", icons.verifying);
        $p.text('正在请求服务');
        U.ajax({
            type: 'post',
            url: '/api/form-btn/gm/exe',
            loading: false,
            data: args,
            success: function (data) {
                if (U.er(data)) {
                    resetIconMsg(icons.error, data.msg);
                } else {
                    if (vars.wait) {
                        $p.text('执行中');
                        vars.acceptedMsg = data.msg;
                        // 查询执行进度
                        let res = data.data;
                        vars.tid = res.tid;
                        setTimeout(fetchTaskState, 1000);
                    } else {
                        resetIconMsg(icons.success, data.msg || '执行成功');
                    }
                }
            }
        });
    }

    /**
     * 查看任务执行进度
     */
    function fetchTaskState() {
        U.ajax({
            type: 'get',
            url: '/api/form-btn/gm/state',
            loading: false,
            data: {
                tid: vars.tid,
            },
            success: function (data) {
                if (U.er(data)) {
                    resetIconMsg(icons.error, data.msg);
                } else {
                    if (data.data) {
                        resetIconMsg(icons.success, vars.acceptedMsg || '执行成功');
                    } else {
                        setTimeout(fetchTaskState, 1000);
                    }
                }
            },
        });
    }

    /**
     * 点击确定按钮
     */
    function sure() {
        $btn.hide();
        exe();
    }

    function resetIconMsg(src, text) {
        $img.removeAttr('id');
        $img.attr('src', src);
        $p.text(text);
    }

    /**
     * 点击取消按钮
     */
    function cancel() {
        window.parent.postMessage(JSON.stringify({action: 1}), "*");
    }
</script>

</html>