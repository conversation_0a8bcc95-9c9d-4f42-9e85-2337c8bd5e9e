<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">


<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导出选课结果数据</title>
    <th:block th:include="common :: header('导出选课结果数据')"/>
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/global.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/reset.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/exportData.css'">
</head>

<body>
    <div class="marker"></div>
    <div class="dialog" id="resultData">
        <div class="dialog-title">
            <h3>导出选课结果数据</h3>
        </div>
        <div class="dialog-con">
            <div class="from-wrap">
                <ul class="data-nav">
                    <li class="active">按行政班导出</li>
                    <li>按教学班导出</li>
                </ul>
            </div>
            <div class="plan-sel">
                <div class="plan-label">选课计划</div>
                <div class="j-search-con single-box">
                    <input type="text" name="teacherName"  id="taskval" placeholder="请选择选课计划" readonly="" class="schoolSel">
                    <span class="j-arrow"></span>
                    <div class="j-select-year">
                        <div class="search">
                            <input type="text" placeholder="搜索">
                            <span></span>
                        </div>
                        <ul name="teacherName" id="taskList">
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="dialog-btn"><button class="pu-cancel" id="stuCancel">取消</button><button class="pu-sure"
                id="stuSure">确定</button>
        </div>
    </div>
    <div class="dialog" id="exporting">
        <div class="dialog-title">
            <h3>导出选课结果数据</h3>
        </div>
        <div class="dialog-con">
            <img class="loading" th:src="${_CPR_}+'/elective/images/loading.png'" alt="">
            <p>数据准备中<br>请前往导出记录下载</p>
        </div>
        <div class="dialog-btn"><button class="pu-cancel">取消</button><button class="pu-sure">确定</button>
        </div>
    </div>
</body>
<script th:src="${_CPR_}+'/elective/js/jquery-3.3.1.min.js'"></script>
<script th:src="${_CPR_}+'/elective/js/common.js'"></script>
<script th:src="${_CPR_}+'/elective/layui/layui.js'"></script>
<script th:inline="javascript">
    var fid = [[${fid}]];
    var uid = [[${uid}]];
    var formId = [[${formId}]];
    var type = 0;//导出文件类型
    // tab切换
    $(".data-nav li").click(function () {
        type = $(this).index();
        $(this).addClass("active").siblings().removeClass("active");
    });
    layui.use(['layer'], function () {
        var layer = layui.layer;});
    // 导出确定
    $("#stuSure").click(function () {
        var taskval =  $("#taskval").val();
        if (taskval==''){
            layer.msg("请选择导出的选课计划");
            return false;
        }
        $.ajax({
            type: 'post',
            url: "/elective/task/result/download",
            data: {fid:fid,formId:formId,uid:uid,taskval:taskval,type:type},
            dataType: 'json',
            success: function (data) {
                $("#exporting").show();
                $("#resultData").hide();
            }
        });
    })
    // 导出取消
    $("#stuCancel").click(function () {
        $("#resultData,.marker").hide();
        window.parent.postMessage(JSON.stringify({action: 1}), "*");
    })
    // 关闭导出中弹窗
    $("#exporting .dialog-btn button").click(function () {
        $("#exporting,#exporting,.marker").hide();
        window.parent.postMessage(JSON.stringify({action: 1}), "*");
    })
    $.ajax({
        type: 'post',
        url: "/elective/task/getResultTask",
        data: {fid:fid,formId:formId},
        dataType: 'json',
        success: function (data) {
            if (data.data) {
                var html = "";
                for (let i = 0; i < data.data.length; i++) {
                    html += "<li data-id='"+data.data[i]+"' class=''>"+data.data[i]+"</li>";
                }
                $("#taskList").html(html);
            }
        }
    });
</script>

</html>