<!DOCTYPE html>
<html lang="en">
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按征订单入库</title>
    <link rel="stylesheet" href="/distribution/css/global1.css">
    <link rel="stylesheet" href="/distribution/layui/css/layui.css">
    <link rel="stylesheet" href="/distribution/css/reset.css">
    <link rel="stylesheet" href="/distribution/css/reviewSub.css">
    <script src="/distribution/layui/layui.js"></script>
    <style>
        .j-material .j-search input {
            width: 180px;
        }

        .j-material .j-search-item .j-select-year {
            width: 178px;
        }

        .j-material .j-search-item .j-search-con {
            margin-right: 0;
        }

        .j-material .j-search-item .button {
            margin-left: 40px;
        }

        .j-material {
            min-height: auto;
        }

        .j-material .j-table {
            margin-bottom: 0;
        }

        .j-material .j-table {
            margin: 0;
        }
    </style>
</head>

<body>
<div class="j-material-wrap">
    <div class="j-material">
        <div class="j-title">
            <h4>按征订单入库</h4>
            <div class="j-btns">
                <button class="btn-cancel">取消</button>
                <button class="gray" onclick="submitForm()" id="confirm">确认</button>
            </div>
        </div>
        <div class="j-review-con " style="padding-top:20px;">

            <div class="j-search" style="justify-content:flex-start;">
                <div class="j-search-item">
                    <h5>学年学期</h5>
                    <div class="j-search-con">
                        <input type="text" placeholder="选择学年学期" id="schoolYear" readonly class="schoolSel year"
                               name="xnxq">
                        <span class="j-arrow"></span>
                        <div class="j-select-year xnxq">
                            <ul>
                                <li th:each="data:${xnxq}" th:text="${data}"></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="j-search-item">
                    <h5>入库状态</h5>
                    <div class="j-search-con">
                        <input type="text" placeholder="选择状态" readonly class="schoolSel status" name="status">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <ul>
                                <li>全部类型</li>
                                <li>已入库</li>
                                <li>待入库</li>
                            </ul>
                        </div>
                    </div>
                    <div class="button" onclick="search()">查询</div>
                </div>
            </div>
            <div class="j-table" style="border:none;">
                <table class="layui-table" id="materialTable" lay-filter="materialTable">
                </table>
            </div>
        </div>
        <div id=orderPage"></div>
    </div>

    <div class="j-material  material" style="display:none;">
        <div class="j-review-con " style="padding-top:30px; padding-bottom: 30px;">
            <div class="j-table">
                <div class="j-table-thead">
                    <div class="cell">教材编号</div>
                    <div class="cell">提交人</div>
                    <div class="cell">院系</div>
                    <div class="cell">教材类型</div>
                    <div class="cell">教材名称</div>
                    <div class="cell">ISBN</div>
                    <div class="cell">出版社</div>
                    <div class="cell">教材作者</div>
                    <div class="cell">出版年月</div>
                    <div class="cell">单价</div>
                </div>
                <div class="j-table-tr" id="material">
                </div>
            </div>
        </div>
        <div id="coursePage"></div>
    </div>

</div>
</body>
<script src="/distribution/js/jquery-3.3.1.min.js"></script>
<script src="/distribution/js/common.js"></script>
<script src="/distribution/js/jquery-form.js"></script>
<script src="/js/alert.js"></script>
<script type="text/html" id="tmplToolBar">

    <div class=" warehouse" lay-event="warehouse">入库</div>

</script>

<script th:inline="javascript">
    var fid = [[${fid}]];
    var uid = [[${uid}]];
    var formId = [[${formId}]];
    var xnxq = '';
    var orderNum = '';

    $(function () {
        getOrderList()
    })

    var orderList = null;
    var count = 0;

    function getOrderList(page, pageSize, xnxq, status) {
        $.ajax({
            type: 'get',
            dataType: 'json',
            url: '/api/material/getOrderList',
            async: false,
            data: {
                fid: fid,
                page: page,
                pageSize: pageSize,
                xnxq: xnxq,
                status: status
            },
            success: function (res) {
                if (res.status) {
                    orderList = res.data;
                    updateOrderPage(res.count)
                    $(".material").hide();
                    reloadData();
                } else {
                    myAlert(res.msg)
                }
            },
            error: function () {
                myAlert("系统繁忙，请稍后再试");
            }
        })
    }


    function search() {
        $("#confirm").removeAttr("class").addClass("gray")
        var status = $("input[name=status]").val();
        var xnxq = $("input[name=xnxq]").val();
        if (status === "已入库") {
            status = 1;
        } else if (status === "待入库") {
            status = 0;
        } else {
            status = null;
        }
        getOrderList('', '', xnxq, status)
    }

    function submitForm() {
        var flag = $("#confirm").hasClass("gray");
        if (flag) {
            myAlert("已完成入库")
            return false;
        }
        var opt = $(".jc").find(".c-opt");
        for (let i = 0; i < opt.length; i++) {
            var yzdsl = parseInt($(opt).find("input[name=yzdsl]").val());
            if (yzdsl == undefined || yzdsl == 0) {
                myAlert("请检查已征订数量")
                return false;
            }
        }
        myAlert("正在保存，请稍等")
        let error = 0;
        var children = $(".j-table-tr").children("form");
        for (let i = 0; i < children.length; i++) {
            $(children[i]).ajaxSubmit(function (res) {
                if (!res.status) {
                    error++;
                }
            })
        }
        setTimeout(function () {
            if (error > 0) {
                myAlert("保存失败，请稍后再试");
            } else {
                myAlert("保存成功")
                location.reload();
                $("#confirm").removeAttr("class").addClass("gray")
            }
        }, 2000)
    }

    function material(orderNum) {
        $.ajax({
            type: 'get',
            dataType: 'json',
            url: '/api/material/getMaterialList',
            async: false,
            data: {
                fid: fid,
                orderNum: orderNum
            },
            success: function (res) {
                if (res.status) {
                    var data = res.data;
                    updatePage(data.length);
                    let html = '';
                    for (let i = 0; i < data.length; i++) {

                        if (i < 5) {
                            html += '  <form action="/api/material/toStorage" method="post" class="jc" onsubmit="return false;">\n';
                        } else {
                            html += '  <form action="/api/material/toStorage" method="post" class="jc" style="display: none" onsubmit="return false;">\n';
                        }
                        html += '<input type="hidden" name="jcbh" value="' + data[i].jcbh + '">\n' +
                            '<input type="hidden" name="jcmc" value="' + data[i].jcmc + '">\n' +
                            '<input type="hidden" name="isbn" value="' + data[i].isbn + '">\n' +
                            '<input type="hidden" name="cbs" value="' + data[i].cbs + '">\n' +
                            '<input type="hidden" name="jczz" value="' + data[i].jczz + '">\n' +
                            '<input type="hidden" name="cbny" value="' + data[i].cbny + '">\n' +
                            '<input type="hidden" name="dj" value="' + data[i].dj + '">\n' +
                            '<input type="hidden" name="gys" value="' + data[i].gys + '">\n';
                        if (data[i].zk == null || data[i].zk == 'null') {
                            html += '<input type="hidden" name="zk" value="1">\n' +
                                '<input type="hidden" name="zkj" value="' + parseFloat(data[i].dj).toFixed(2) + '">\n';
                        } else {
                            html += '<input type="hidden" name="zk" value="' + data[i].zk + '">\n' +
                                '<input type="hidden" name="zkj" value="' + parseFloat(data[i].zk * data[i].dj).toFixed(2) + '">\n';
                        }
                        html += '<input type="hidden" name="fid" value="' + fid + '">\n' +
                            '<input type="hidden" name="formId" value="' + formId + '">\n' +
                            '<input type="hidden" name="xnxq" value="' + xnxq + '">\n' +
                            '<input type="hidden" name="uid" value="' + uid + '">\n' +
                            '<input type="hidden" name="orderNum" value="' + orderNum + '">\n' +
                            '<div class="cell">\n' +
                            '                        <div class="c-main">\n' +
                            '                            <div class="c-inform">\n' +
                            '                                <div class="item">' + data[i].jcbh + '</div>\n' +
                            '                                <div class="item">' + data[i].creator + '</div>\n' +
                            '                                <div class="item">' + data[i].faculty + '</div>\n' +
                            '                                <div class="item">' + data[i].bookType + '</div>\n' +
                            '                                <div class="item">' + data[i].jcmc + '</div>\n' +
                            '                                <div class="item">' + data[i].isbn + '</div>\n' +
                            '                                <div class="item">' + data[i].cbs + '</div>\n' +
                            '                                <div class="item">' + data[i].jczz + '</div>\n' +
                            '                                <div class="item">' + data[i].cbny + '</div>\n' +
                            '                                <div class="item">' + data[i].dj + '</div>\n' +
                            '                            </div>\n' +
                            '                            <div class="c-opt">\n' +
                            '                                <div class="j-search-item">\n' +
                            '                                    <h5>供应商</h5>\n' +
                            '                                    <div class="txt">' + (data[i].gys == null ? "暂无" : data[i].gys == '' ? "暂无" : data[i].gys) + '</div>\n' +
                            '                                </div>\n' +
                            '                                <div class="number">\n' +
                            '                                    <div class="name">待征订数量</div>\n' +
                            '                                    <div class="txt">' + data[i].dzdsl + '</div>\n' +
                            '<input type="hidden" name="dzdsl" value="' + data[i].dzdsl + '">\n' +
                            '                                </div>\n' +
                            '                                <div class="housNus">\n' +
                            '                                    <div class="name">已征订数量</div>\n' +
                            '                                    <input name="yzdsl" type="number" min="0" max="' + data[i].dzdsl + '" stylele="width:80px;" value="' + (data[i].yzdsl == null ? 0 : data[i].yzdsl) + '" class="layui-input"\n' +
                            '                                           name="title" lay-verify="required" lay-verType="tips" placeholder=""\n' +
                            '                                           autocomplete="off" >\n' +
                            '                                </div>\n' +
                            '                            </div>\n' +
                            '                        </div>\n' +
                            '                    </div>\n' +
                            '                 </form>';
                    }
                    if (html != '') {
                        $(".material").show();
                        $("#material").html(html);
                    }
                } else {
                    $("#confirm").removeAttr("class").addClass("gray")
                    myAlert("征订计划不存在或未审核")
                }
            },
            error: function () {
                myAlert("系统繁忙，请稍后再试");
            }
        })
    }

    let pageIndex = 1;

    function updateOrderPage(size) {
        layui.use(['laypage'], function () {
            var laypage = layui.laypage;
            laypage.render({
                elem: 'orderPage'
                , curr: pageIndex//让起始页,也就是点击的页码
                , groups: 5// 默认的连续出现的页码数
                , limit: 5//每页展示条数
                , count: size//数据总数
                , layout: ['count', 'prev', 'page', 'next', 'skip']
                , jump: function (obj, first) {
                    if (!first) {
                        pageIndex = obj.curr;
                        getOrderList(obj.curr, obj.limit);
                        $("#confirm").removeAttr("class").addClass("gray")
                    }
                }
            });
        })
    }

    let pageIndex2 = 1;

    function updatePage(size) {
        layui.use(['laypage'], function () {
            var laypage = layui.laypage;
            //分页
            laypage.render({
                elem: 'coursePage'
                , curr: pageIndex2//让起始页,也就是点击的页码
                , groups: 5// 默认的连续出现的页码数
                , limit: 5//每页展示条数
                , count: size//数据总数
                , layout: ['count', 'prev', 'page', 'next', 'skip']
                , jump: function (obj, first) {
                    if (first || obj.curr == 1) {
                        $("#material").find('.cell:lt(' + (obj.limit) + ')').show();
                        $("#material").find('.cell:gt(' + (obj.limit - 1) + ')').hide();
                    } else {
                        pageIndex2 = obj.curr;
                        $("#material").find('.cell:lt(' + (obj.curr - 1) * obj.limit + ')').hide();
                        $("#material").find('.cell:gt(' + ((obj.curr - 1) * obj.limit - 1) + '):lt(' + (obj.curr) * obj.limit + ')').show();
                        $("#material").find('.cell:gt(' + (obj.curr * obj.limit - 1) + ')').hide();
                    }
                }

            });
        })
    }

    layui.use(['table', 'jquery'], function () {
        var table = layui.table;
        var $ = layui.jquery;
        table.render({
            id: 'coursePage',
            elem: '#materialTable',
            // url: '/static/json/table/user.json', //数据接口
            data: orderList, //静态数据，真实数据用url接口
            cols: [
                [ //表头
                    // {
                    //     type: 'checkbox',
                    //     width: 100
                    // },
                    {
                        field: 'orderNum',
                        title: '征订单批次',
                        align: 'center',
                        minWidth: 352
                    }, {
                    field: 'status',
                    title: '入库状态',
                    align: 'center',
                    width: 152,
                    templet: function (d) {
                        if (d.status == "1") {
                            return '<span class="wstatus already">已入库</span>';
                        } else {
                            return '<span class="wstatus wait">待入库</span>';
                        }
                    }
                }, {
                    field: 'xnxq',
                    title: '学年学期',
                    align: 'center',
                    width: 152
                }, {
                    field: 'sum',
                    title: '待入库',
                    align: 'center',
                    width: 102
                }, {
                    field: 'money',
                    title: '总金额',
                    align: 'center',
                    width: 152
                }, {
                    field: 'warehounus',
                    title: '入库数',
                    align: 'center',
                    width: 102
                }, {
                    field: 'college',
                    title: '院系',
                    align: 'center',
                    minWidth: 152
                }, {
                    field: 'zy',
                    title: '专业',
                    align: 'center',
                    minWidth: 152
                }, {
                    title: '操作',
                    width: 100,
                    align: 'center',
                    fixed: 'right',
                    // toolbar:'#tmplToolBar',
                    templet: function (d) {
                        if (d.status == "0") {
                            return '<div className=" warehouse" lay-event="warehouse" style="color: #4C85FA;cursor:pointer">入库</div>';
                        } else {
                            return '<div className=" warehouse" lay-event="warehouse" style="color: #4C85FA;cursor:pointer">详情</div>';
                        }
                    },
                }
                ]
            ]
        });

        table.on('tool(materialTable)', function (obj) {
            if ('warehouse' == obj.event) {
                //可以在这里写触发事件希望执行的代码
                // console.log()
                if (obj.data.status == 1) {
                    $("#confirm").removeAttr("class").addClass("gray")
                } else {
                    $("#confirm").removeAttr("class").addClass("btn-complate")
                }
                $(".material").hide();
                $("#material").html('');
                xnxq = obj.data.xnxq;
                orderNum = obj.data.orderNum;
                material(obj.data.orderNum)
            }
        });

        table.on('checkbox(materialTable)', function (obj) {
            if (obj.type == 'all' && obj.checked) {
                $(".layui-table tr").addClass("trSel");
            }
            if (obj.type == 'all' && !obj.checked) {
                $(".layui-table tr").removeClass("trSel");
            }
            // // 当前行数据
            // console.log(obj);
            // // 所有选中数据
            // let selData = layui.table.checkStatus('courseTable');
            // console.log(selData);

            // //判断是否被选中
            if (obj.tr.find('.layui-form-checkbox').hasClass('layui-form-checked')) {
                obj.tr.addClass("trSel");
            } else {
                // 取消选中
                obj.tr.removeClass("trSel");
            }
        });
        reloadData = function () {
            table.reload("coursePage", {
                data: orderList   // 将新数据重新载入表格
            })
        }
    });


    $(".j-material .j-search .radio").click(function () {
        $(this).toggleClass("cur");
    })
    //全选
    $(".j-material .j-table").on("click", ".j-table-thead .cell span", function () {
        $(this).toggleClass("cur");
        if ($(this).hasClass("cur")) {
            $(".j-material .j-table .j-table-tr .cell .lable span").addClass("cur");
        } else {
            $(".j-material .j-table .j-table-tr .cell .lable span").removeClass("cur");
        }
    })


</script>

</html>