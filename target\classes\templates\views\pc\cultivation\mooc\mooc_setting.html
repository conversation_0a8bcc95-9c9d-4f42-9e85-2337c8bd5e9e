<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <th:block th:include="common :: header('参数设置')"/>
    <link rel="stylesheet" th:href="${_CPR_}+'/css/cultivation/global.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/plugin/layui/css/layui2.8.2.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/css/cultivation/mooc/poup.css'">
    <link rel="stylesheet"
          th:href="@{${_CPR_}+'/css/cultivation/mooc/mooc_setting.css'(v=${new java.util.Date().getTime()})}">
    <script th:src="${_CPR_}+'/plugin/layui/layui2.8.2.js'"></script>
</head>
<body>
<div class="z-nav">
    <ul>
        <li class="active">泛雅对接参数设置</li>
        <li>教学进程表参数设置</li>
        <li th:if="${fid != '299360' && fid != '1035'}">开课信息表参数设置</li>
    </ul>
    <button id="saveSet">保存设置</button>
</div>
<div class="z-main-wrap">
    <div class="z-main">
        <div class="box-con">
            <div class="box-title">泛雅网络教学平台教务课程激活方式</div>
            <form action="" class="layui-form" lay-filter="sett">

                <div class="layui-form-item">
                    <label class="layui-form-label">激活方式</label>
                    <div class="layui-input-block item-radio">
                        <input type="radio" lay-filter="activation" name="activation" value="0" title="自动激活"
                               checked>
                        <input type="radio" lay-filter="activation" name="activation" value="1" title="手动激活">
                    </div>
                </div>
                <!-- 手动激活 -->
                <div class="layui-form-item" id="activationRule">
                    <label class="layui-form-label">激活规则</label>
                    <div class="layui-input-block item-radio">
                        <div class="radio-list">
                            <div class="radio-modal">
                                <input type="radio" lay-filter="name1" name="name1" value="0"
                                       title="模式一：相同授课教师跨学期沿用相同课程编号的网络课程。" checked>
                                <p>
                                    即一位授课教师每门课单独建设一门网络课程。
                                    <br>
                                    例：张三和李四老师本学期教授《中职语文一》这门课，张三老师教授A班和B班，李四老师教授C班，张三和李四老师共同教授D班。系统为张三老师新建一门网络课，课程中有A班和B班；为李四老师新建一门网络课，课程中有C班；为张三和李四老师新建另一门课，课程中有D班。张三和李四老师下学期教授《中职语文一》这门课时，沿用本学期各自的网络课程授课。
                                </p>
                            </div>
                            <div class="radio-modal">
                                <input type="radio" lay-filter="name1" name="name1" value="1"
                                       title="模式二：所有授课教师跨学期沿用相同课程编号的网络课程。">
                                <p>
                                    即所有授课教师每门课共建一门网络课程。
                                    <br>
                                    例：张三和李四老师本学期教授《中职语文一》这门课，张三老师教授A班和B班，李四老师教授C班，张三和李四老师共同教授D班。系统为张三和李四老师共同新建一门网络课，课程中有A班、B班、C班和D班。张三和李四老师下学期教授《中职语文一》这门课时，沿用本学期共同的网络课程授课。
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item" id="creatorItem">
                    <label class="layui-form-label">建课人</label>
                    <div class="layui-input-block item-radio">
                        <div class="raido-create">
                            <input type="radio" lay-filter="creator" name="creator" value="1"
                                   title="授课教师（多人取首位）">
                        </div>
                        <div class="raido-create">
                            <input type="radio" lay-filter="creator" name="creator" value="0" title="建课管理员"
                                   checked>
                            <div class="sel-member selMember"><img src="/images/cultivation/mooc/add.png" alt="">选择人员
                            </div>
                            <ul class="member-list" id="memberList">
                            </ul>
                        </div>
                    </div>
                </div>
                <!-- 自动激活 -->
                <div class="handleActivated" id="handleActivated">
                    <label class="layui-form-label" style="width: 64px;">建课方式</label>
                    <div class="layui-input-block" style="margin-left: 78px;">
                        <!--<div class="clone-set">
                            <div class="layui-form-item">
                                <label class="layui-form-label">允许克隆已有课程<span class="layui-tips"
                                        data-tip="字段字段字段字段字段字段字段字段字段字段字段字段字段字段字段字段字段字段"></span></label>
                                <div class="layui-input-block item-radio">
                                    <input type="checkbox" name="cloneTermCourse" lay-skin="switch"
                                        lay-filter="switch-clone" checked> <span class="swtichText">开启</span>
                                </div>
                            </div>
                            <div class="course-set" id="cloneCourse">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">允许克隆过往学期课程</label>
                                    <div class="layui-input-block">
                                        <input type="checkbox" name="cloneTermCourse" lay-skin="switch" checked
                                            lay-filter="switch-change"> <span class="swtichText">开启</span>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">允许克隆不同编号课程</label>
                                    <div class="layui-input-block">
                                        <input type="checkbox" name="cloneOtherCourse" lay-skin="switch" checked
                                            lay-filter="switch-change"> <span class="swtichText">开启</span>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">允许克隆他人所属课程</label>
                                    <div class="layui-input-block">
                                        <input type="checkbox" name="cloneNumCourse" lay-skin="switch" checked
                                            lay-filter="switch-change"> <span class="swtichText">开启</span>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">需要申请授权</label>
                                    <div class="layui-input-block">
                                        <input type="checkbox" name="cloneApplyEmpower" lay-skin="switch" checked
                                            lay-filter="switch-change"> <span class="swtichText">开启</span>
                                    </div>
                                </div>
                            </div>
                        </div>-->
                        <div class="join-set">
                            <div class="layui-form-item">
                                <label class="layui-form-label">允许加入已有课程<span class="layui-tips"
                                                                                      data-tip="管理员设置允许加入已有课程后，授课教师在泛雅平台手动激活教务课程时可以选择加入泛雅平台已有课程。"></span></label>
                                <div class="layui-input-block item-radio">
                                    <input type="checkbox" name="joinExistingCourses" lay-skin="switch" checked
                                           lay-filter="switch-join"> <span class="swtichText">开启</span>
                                </div>
                            </div>
                            <!--<div class="course-set">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">允许加入过往学期课程</label>
                                    <div class="layui-input-block">
                                        <input type="checkbox" name="cloneTermCourse" lay-skin="switch" checked
                                            lay-filter="switch-change"> <span class="swtichText">开启</span>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">允许加入他人所属课程</label>
                                    <div class="layui-input-block">
                                        <input type="checkbox" name="cloneOtherCourse" lay-skin="switch" checked
                                            lay-filter="switch-change"> <span class="swtichText">开启</span>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">允许加入不同编号课程</label>
                                    <div class="layui-input-block">
                                        <input type="checkbox" name="cloneNumCourse" lay-skin="switch" checked
                                            lay-filter="switch-change"> <span class="swtichText">开启</span>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">需要申请授权</label>
                                    <div class="layui-input-block">
                                        <input type="checkbox" name="cloneApplyEmpower" lay-skin="switch" checked
                                            lay-filter="switch-change"> <span class="swtichText">开启</span>
                                    </div>
                                </div>
                            </div>-->
                        </div>
                        <!--<div class="layui-form-item" style="margin-bottom: 32px;">
                            <label class="layui-form-label">允许交叉建课<span class="layui-tips"
                                    data-tip="字段字段字段字段字段字段字段字段字段字段字段字段字段字段字段字段字段字段"></span></label>
                            <div class="layui-input-block item-radio">
                                <input type="checkbox" name="allowCreateCourse" lay-skin="switch" checked> <span
                                    class="swtichText">开启</span>
                            </div>
                        </div>-->
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">私有班<span class="layui-tips"
                                                                data-tip="当网络课程教师团队中有多位教师时，私有班模式下每位教师只能看到自己教授的班级学生及相关数据，非私有班模式下每位教师可以看到课程中所有班级的学生及相关数据。"></span></label>
                    <div class="layui-input-block item-radio">
                        <input type="radio" lay-filter="privateCla" name="privateCla" value="1" title="是">
                        <input type="radio" lay-filter="privateCla" name="privateCla" value="0" title="否" checked>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="z-main">
        <div class="box-con">
            <div class="box-title">基础设置</div>
            <form action="" class="layui-form form-set teach-set" lay-filter="basicSet">
                <div class="layui-form-item">
                    <label class="layui-form-label layui-label-time">教学计划录入时间</label>
                    <div class="layui-input-block time-block" style="width: 320px">
                        <input type="text" class="layui-input" name = "teachPlanTime" id="enterTime" placeholder="请选择"/>
                        <img th:src="${_CPR_+_VR_+'/images/cultivation/mooc/icon-time.png'}" alt=""/>
                    </div>
                </div>
            </form>
        </div>
        <div class="box-con">
            <div class="box-title">教学进程表参数设置</div>
            <form action="" class="layui-form form-set teach-set" id="TeachSet" lay-filter="teachingProgressParamSet">
                <div class="layui-form-item">
                    <label class="layui-form-label">课程信息字段设置<span class="layui-tips"
                                                                          data-tip="教学进程表需要显示的课程信息字段，在此处勾选"></span></label>
                    <div class="layui-input-block" style="width: 912px;">
                        <input type="checkbox" name="courseId" title="课程编号" checked lay-filter="showSet">
                        <input type="checkbox" name="courseNature" title="课程性质" checked lay-filter="showSet">
                        <input type="checkbox" name="courseCategory" title="课程类别" checked lay-filter="showSet">
                        <input type="checkbox" name="chooseCompulsory" title="选必修" checked lay-filter="showSet">
                        <input type="checkbox" name="credit" title="学分" checked lay-filter="showSet">
                        <input type="checkbox" name="totalClassHour" title="总学时" checked lay-filter="showSet">
                        <input type="checkbox" name="extraClassHour" title="额外学时" lay-filter="showSet">
                        <input type="checkbox" name="examType" title="考试形式" checked lay-filter="showSet">
                        <input type="checkbox" name="courseGroup" title="课程编组" lay-filter="showSet">
                        <input type="checkbox" name="courseClassify" title="课程分类" lay-filter="showSet">
                        <input type="checkbox" name="courseAttribute" title="课程属性" lay-filter="showSet">
                        <input type="checkbox" name="courseType" title="课程类型" lay-filter="showSet">
                        <input type="checkbox" name="openDept" title="开课部门" lay-filter="showSet">
                        <input type="checkbox" name="courseCriterion" title="是否有课标" lay-filter="showSet">
                        <input type="checkbox" name="notes" title="备注" lay-filter="showSet">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">周数关联学年学期<span class="layui-tips"
                                                                          data-tip="开启后，教学周数只能设定在开课学期的起始周以及结束周范围内，超出范围无法提交"></span></label>
                    <div class="layui-input-block" style="padding-top: 4px;">
                        <input type="checkbox" name="total" lay-skin="switch" checked
                               lay-filter="switch-change">
                        <span class="swtichText">关闭</span>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">是否显示学时分配<span class="layui-tips"
                                                                          data-tip="开启后，可在教学进程表中分配理论学时、实践学时、上机学时等所占具体学时"></span></label>
                    <div class="layui-input-block" style="padding-top: 4px;">
                        <input type="checkbox" name="hoursDistribute" lay-skin="switch"
                               lay-filter="switch-learning">
                        <span class="swtichText">关闭</span>
                    </div>
                </div>
                <div class="layui-form-item" id="classHourSet" style="display: none;">
                    <label class="layui-form-label">学时分配子集设置</label>
                    <div class="layui-input-block">
                        <input type="checkbox" name="theory" title="理论" checked>
                        <input type="checkbox" name="practice" title="实践" checked>
                        <input type="checkbox" name="experiment" title="实验">
                        <input type="checkbox" name="computer" title="上机">
                        <input type="checkbox" name="other" title="其他">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">是否开启聚合统计<span class="layui-tips"
                                                                          data-tip="开启后，教学进度表将按照设置的统计维度进行学时、学分的总计和小计以及小计所占所有课程总学时比例"></span></label>
                    <div class="layui-input-block" style="padding-top: 4px;">
                        <input type="checkbox" name="polymerizationSta" lay-skin="switch"
                               lay-filter="switch-polymerization">
                        <span class="swtichText">关闭</span>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">是否校验总学时</label>
                    <div class="layui-input-block" style="padding-top: 4px;">
                        <input type="checkbox" name="verifyTotalClassHours" lay-skin="switch" lay-filter="switch-change">
                        <span class="swtichText">关闭</span>
                    </div>
                </div>
                <div id="polymerizationSet" style="display: none;">
                    <div class="layui-form-item">
                        <label class="layui-form-label">聚合统计维度<span class="layui-tips"
                                                                          data-tip="根据设置的字段聚合统计总学时、总学分、学期周学时的小计和合计、小计学时/所有课程总学时比例，课程信息字段必须勾选聚合统计维度的课程标签"></span></label>
                        <div class="layui-input-block">
                            <div class="j-search-con single-box">
                                <input type="text" name="dimension" value="课程性质" placeholder="请选择" readonly=""
                                       class="schoolSel">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <ul class="dimension">
                                        <li data-id="courseCategory" class="">课程类别</li>
                                        <li data-id="courseClassify" class="">课程分类</li>
                                        <li data-id="courseNature" class="active">课程性质</li>
                                        <li data-id="courseAttribute" class="">课程属性</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">聚合统计显示设置</label>
                        <div class="layui-input-block" style="width: 812px;">
                            <input type="checkbox" name="subtotal" title="小计" checked lay-filter="staSet">
                            <input type="checkbox" name="total" title="合计" checked lay-filter="staSet">
                            <input type="checkbox" name="subtotalProportion" title="小计总学时占比" lay-filter="staSet">
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">是否显示学期教材<span class="layui-tips"
                                                                          data-tip="开启后，可在教学进程表中设置学期教材"></span></label>
                    <div class="layui-input-block" style="padding-top: 4px;">
                        <input type="checkbox" name="textBook" lay-skin="switch" lay-filter="switch-termBook">
                        <span class="swtichText">关闭</span>
                    </div>
                </div>
                <div class="tb-set" id="tbSet" style="display: none;">
                    <div class="layui-form-item">
                        <label class="layui-form-label">学期教材范围<span class="layui-tips"
                                                                          data-tip="“教材库中全部教材”指在设置教学进程表时，手动选择每门课程每学期上课使用的教材；“课程库中指定教材”指在设置教学进程表时，课程每学期使用教材默认为课程库中提前维护的教材，并支持手动修改"></span></label>
                        <div class="layui-input-block item-radio">
                            <input type="radio" lay-filter="textBookRange" name="range" value="1"
                                   title="教材库中全部教材"
                                   checked>
                            <input type="radio" lay-filter="textBookRange" name="range" value="0"
                                   title="课程库中指定教材">
                        </div>
                    </div>
                    <div id="termBookSet" style="display: none;">
                        <div class="layui-form-item">
                            <label class="layui-form-label">是否允许修改指定教材</label>
                            <div class="layui-input-block" style="padding-top: 4px;">
                                <input type="checkbox" name="modifyTextbook" lay-skin="switch"
                                       lay-filter="switch-Textbook">
                                <span class="swtichText">关闭</span>
                            </div>
                        </div>
                        <div class="layui-form-item" style="display: none;">
                            <label class="layui-form-label" style="padding: 7px 0;">修改教材学期范围</label>
                            <div class="layui-input-block">
                                <div class="j-search-con multiple-box">
                                    <input type="text" name="termRange" placeholder="请选择" readonly=""
                                           class="schoolSel">
                                    <span class="j-arrow"></span>
                                    <div class="j-select-year">
                                        <div class="search">
                                            <input type="text" placeholder="搜索">
                                            <span></span>
                                        </div>
                                        <ul name="termRange">
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item" style="display: none;">
                            <label class="layui-form-label" style="padding: 7px 0;">修改教材操作时限</label>
                            <div class="layui-input-block time-block" style="width: 310px;">
                                <input type="text" class="layui-input" name="timeLimit" id="timeLimit"
                                       placeholder="请选择">
                                <img src="/images/cultivation/mooc/icon-time.png" alt="">
                            </div>
                        </div>
                        <div class="layui-form-item" style="display: none;">
                            <label class="layui-form-label">权限分配</label>
                            <div class="layui-input-block item-radio">
                                <div class="raido-create">
                                    <input type="radio" lay-filter="permission" name="permission" value="1"
                                           title="指定角色"
                                           checked>
                                    <div class="sel-member" id="selRole"><img src="/images/cultivation/mooc/add.png"
                                                                              alt="">选择角色
                                    </div>
                                    <ul class="member-list" id="roleList">
                                    </ul>
                                </div>
                                <div class="raido-create">
                                    <input type="radio" lay-filter="permission" name="permission" value="0"
                                           title="指定人员">
                                    <div class="sel-member selMember"><img src="/images/cultivation/mooc/add.png"
                                                                           alt="">选择人员
                                    </div>
                                    <ul class="member-list memberList">
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="z-main" style="background-color: rgba(247, 248, 250, 1);">
        <div class="py-table">
            <h3>
                <span>教学班名称生成设置</span>
            </h3>
            <table class="layui-hide" id="className" lay-filter="className-filter"></table>

        </div>
        <div class="py-table" id="tables">
            <h3>
                <span>教学班编号生成设置</span>
            </h3>
            <table class="layui-hide" id="className1" lay-filter="className-filter1"></table>
        </div>
    </div>
</div>

<!-- <div class="marsk"></div> -->
<!-- 选择人员弹窗 -->
<div id="dialogSel" style="display: none;">
    <div class="selHead">选择人员</div>
    <div class="selCon">
        <div class="fl-person">
            <div class="tab-search">
                <input type="text" placeholder="搜索教师" id="keyword">
                <span class="search-btn">确定</span>
            </div>
            <div class="kex-box" id="kex-box">
                <div class="moreBoxs">
                    <img src="/images/cultivation/mooc/timg.gif" alt="" class="mCS_img_loaded">
                </div>
            </div>

        </div>
        <div class="fr-pitch">
            <h2>已选用户</h2>
            <div class="kex-box" id="seKex-box">
                <ul>
                </ul>
            </div>
        </div>
    </div>
    <div class="selFooter">
        <button class="btnCancel">取消</button>
        <button class="btnSure">确定</button>
    </div>
</div>
<!-- 选择角色弹窗 -->
<div class="pouop popAddRoleGroup" id="popAddRoleGroup" style="display: none">
    <div class="pup-title">
        <h3>添加角色</h3>
        <span class="close"></span>
    </div>
    <div class="popup-con">
        <div class="popup-search">
            <img src="/images/cultivation/mooc/icon-search.png" alt="">
            <input type="text" placeholder="输入角色名称">
            <span id="searchBtn">搜索</span>
        </div>
        <div class="cla-con" style="margin-top:10px;">
            <div class="cla-head">
                <ul>
                    <li>分类名</li>
                    <li>全选<span id="claCheckAll"></span></li>
                </ul>
            </div>
            <div class="cla-body">
                <div class="mail-level" id="mail-level">
                    <ul class="level-list" id="mailLevel">
                    </ul>
                    <div class="no-data">没有检索到相关内容</div>
                </div>
            </div>
        </div>
    </div>
    <div class="popup-btn">
        <span id="roleSure">确认</span>
    </div>
</div>
<!-- 提示 -->
<div class="pouop" id="tipDialog">
    <img src="/images/cultivation/mooc/tips-error.png" alt="">
    <p><span></span>参数未设置，请设置后保存</p>
    <button id="tipDialogSure">确定</button>
</div>
<!--教学班名称生成设置-->
<div id="edit-poups" class="popups">
    <div class="title">
        <div class="name">编辑教学班名称生成规则</div>
        <div class="close"></div>
    </div>
    <div class="popup-con">
        <div class="lable">
            <h3><span>教学班名称生成示例</span></h3>
            <div class="inputs">2023-2024-2语文课21计算机1班cxwfw001</div>
        </div>
        <div class="lable" id="generate">
            <div class="lab-top">
                <h3><i>*</i>生成公式</h3>
                <div class="clear-rule">清空规则</div>
            </div>
            <div class="lab-con">
                <h4>教学班名称 &nbsp;&nbsp;=</h4>
                <div class="cell-group">
                    <ul>
                        <li class="default" alias="kkgl_kkxq,kkxxb_kkxq" table="kkgl,kkxxb" value="2023-2024-2">
                            开课学期
                        </li>
                        <li class="symbol">+</li>
                        <li class="default" alias="kkgl_kcmc,kkxxb_kcmc" table="kkgl,kkxxb" value="语文课">课程名称</li>
                        <li class="symbol">+</li>
                        <li class="default" alias="kkgl_jxbzc,kkxxb_jxbzc" table="kkgl,kkxxb" value="2023电子信息1班">
                            教学班组成名称
                        </li>
                        <li class="symbol">+</li>
                        <li class="default" alias="3位,3位" table="kkgl,kkxxb">3位流水号</li>
                        <li class="symbol">+</li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="lable">
            <div class="lab-top">
                <h3>可用参数</h3>
            </div>
            <div class="lab-con">
                <div class="cell-group parameter">
                    <ul>
                        <li class="default" alias="kkgl_kkxq,kkxxb_kkxq" table="kkgl,kkxxb" value="2023-2024-2">
                            开课学期
                        </li>
                        <li class="default" alias="kkgl_kcmc,kkxxb_kcmc" table="kkgl,kkxxb" value="语文课">课程名称</li>
                        <li class="default" alias="kck_kcjc,kck_kcjc" table="kkgl,kkxxb" value="语文">课程简称</li>
                        <li class="default" alias="zysj_ssyx,zysj_ssyx" table="kkgl,kkxxb" value="电子信息技术">
                            系部名称
                        </li>
                        <li class="default" alias="kkgl_zymc,kkxxb_zymc" table="kkgl,kkxxb" value="语文专业">专业名称
                        </li>
                        <li class="default" alias="kkgl_jxbzc,kkxxb_jxbzc" table="kkgl,kkxxb" value="2023电子信息1班">
                            教学班组成
                        </li>
                        <li class="default" alias="kkgl_nj,kkxxb_njnj" table="kkgl,kkxxb" value="2023">年级</li>
                        <li class="serial-number">
                            <div class="name">流水号</div>
                            <em></em>
                            <div class="select-dropdown">
                                <ul class="dropdown-lists dropdown-lists-single">
                                    <li class="cur" alias="1位,1位" table="kkgl,kkxxb">1位</li>
                                    <li class="" alias="2位,2位" table="kkgl,kkxxb">2位</li>
                                    <li class="" alias="3位,3位" table="kkgl,kkxxb">3位</li>
                                    <li class="" alias="4位,4位" table="kkgl,kkxxb">4位</li>
                                    <li class="" alias="5位,5位" table="kkgl,kkxxb">5位</li>
                                </ul>
                            </div>
                        </li>
                        <li class="fixed">固定字符</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <div class="bottom">
        <div class="cancle">取消</div>
        <div class="confirm">保存</div>
    </div>
</div>
<!--教学班编号生成设置-->
<div id="edit-ref-poups" class="popups">
    <div class="title">
        <div class="name">编辑教学班编号生成规则</div>
        <div class="close"></div>
    </div>
    <div class="popup-con">
        <div class="lable">
            <h3>
                    <span>
                        教学班编号生成示例
                    </span>
                <em>
                    <div class="tip">
                        <i>教学班编号生成示例说明</i>
                    </div>
                </em>
            </h3>
            <div class="inputs">2023-2024-2语文课21计算机1班cxwfw001</div>
        </div>
        <div class="lable" id="generate1">
            <div class="lab-top">
                <h3><i>*</i>生成公式</h3>
                <div class="clear-rule">清空规则</div>
            </div>
            <div class="lab-con">
                <h4>教学班编号 &nbsp;&nbsp;=</h4>
                <div class="cell-group">
                    <ul>
                        <li class="default" alias="kkgl_kkxq,kkxxb_kkxq" table="kkgl,kkxxb" value="2023-2024-2">
                            开课学期
                        </li>
                        <li class="symbol">+</li>
                        <li class="default" alias="kkgl_kcmc,kkxxb_kcmc" table="kkgl,kkxxb" value="kc001">课程编号</li>
                        <li class="symbol">+</li>
                        <li class="default" alias="kkgl_jxbzc,kkxxb_jxbzc" table="kkgl,kkxxb" value="202301">
                            教学班组成编号
                        </li>
                        <li class="symbol">+</li>
                        <li class="default" alias="3位,3位" table="kkgl,kkxxb">3位流水号</li>
                        <li class="symbol">+</li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="lable">
            <div class="lab-top">
                <h3>可用参数</h3>
            </div>
            <div class="lab-con">
                <div class="cell-group parameter">
                    <ul>
                        <li class="default" alias="kkgl_kkxq,kkxxb_kkxq" table="kkgl,kkxxb" value="2023-2024-2">
                            开课学期号
                        </li>
                        <li class="default" alias="kkgl_kcbh,kkxxb_kcbh" table="kkgl,kkxxb" value="kc001">课程编号</li>
                        <li class="default" alias="zysj_xbdm,zysj_xbdm" table="kkgl,kkxxb" value="xb001">系部代码</li>
                        <li class="default" alias="kkgl_zybh,kkxxb_zybh" table="kkgl,kkxxb" value="zy001">专业编号</li>
                        <li class="default" alias="kkgl_jxbzcbh,kkxxb_jxbzcbh" table="kkgl,kkxxb" value="202301">
                            教学班组成编号
                        </li>
                        <li class="serial-number">
                            <div class="name">流水号</div>
                            <em></em>
                            <div class="select-dropdown">
                                <ul class="dropdown-lists dropdown-lists-single">
                                    <li class="cur" alias="1位,1位" table="kkgl,kkxxb">1位</li>
                                    <li class="" alias="2位,2位" table="kkgl,kkxxb">2位</li>
                                    <li class="" alias="3位,3位" table="kkgl,kkxxb">3位</li>
                                    <li class="" alias="4位,4位" table="kkgl,kkxxb">4位</li>
                                    <li class="" alias="5位,5位" table="kkgl,kkxxb">5位</li>
                                </ul>
                            </div>
                        </li>
                        <li class="fixed">固定字符</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <div class="bottom">
        <div class="cancle">取消</div>
        <div class="confirm">保存</div>
    </div>
</div>
</body>
<script type="text/html" id="tmplToolBar1">
    <div class="edit" style="color: #0c72f4;cursor: pointer" lay-event="edit">编辑</div>
</script>
<script type="text/html" id="tmplToolBar2">
    <div class="edit" style="color: #0c72f4;cursor: pointer" lay-event="edit">编辑</div>
</script>
<script th:src="${_CPR_}+'/js/cultivation/select.js'"></script>
<script th:src="@{${_CPR_}+'/js/cultivation/setting.js'(v=${new java.util.Date().getTime()})}"></script>
<script th:inline="javascript">
    let fid = [[${fid}]];
</script>
</html>