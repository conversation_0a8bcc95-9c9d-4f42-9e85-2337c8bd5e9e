<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编组校验</title>
    <th:block th:include="common :: header('编组校验')"/>
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/global.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/layui/css/layui.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/reset.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/selCourseSet.css'">
</head>
<body>
    <div class="z-main">
        <div class="z-title">
            <h3>编组校验</h3>
        </div>
        <div class="z-box">
            <div class="box-con box-common" style="display: block;">
                <div class="z-btns" style="margin: 24px 0 18px;">
                    <div class="addRule" id="addScoreLimit"><img th:src="${_CPR_}+'/elective/images/add-icon.png'">添加同组门数要求</div>
                    <div class="del">删除</div>
                </div>

                <div class="z-table">
                    <table class="layui-hide materialTable4" id="materialTable4" lay-filter="materialTable4">
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div id="marshalling" class="dialog">
        <div class="dialog-title">
            <h3>同组门数要求</h3><span class="pu-close"></span>
        </div>
        <div class="dialog-con">
            <div class="tips">限制同一个编组内选修课的可选数量</div>
            <form action="" class="layui-form">
            <input type="hidden" id="editCourseGroupId">
                <div class="layui-form-item">
                    <label class="layui-form-label">编组名称 </label>
                    <div class="layui-input-block">
                        <div class="j-search-con single-box">
                            <input type="text" data-name="marName" name="teacherName" placeholder="请选择" readonly=""
                                class="schoolSel" id="courseGroupNameIp">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <ul name="teacherName" id="courseGroupListUl">
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">可选门数 </label>
                    <div class="layui-input-block selCount">
                        <input type="number" data-name="min" placeholder="最少" min="0" id="courseGroupMinIp">
                        <img th:src="${_CPR_}+'/elective/images/range.png'" alt="">
                        <input type="number" data-name="max" placeholder="最多" min="0" id="courseGroupMaxIp">
                    </div>
                </div>
            </form>
        </div>
        <div class="dialog-btn">
            <button class="pu-cancel">取消</button><button class="pu-sure" id="scoreSure">确定</button>
        </div>
    </div>
    <!-- 学分限制-查看 -->

    <div id="selScoreCheck" class="dialog dialogCheck">
        <div class="dialog-title">
            <h3>学分限制详情</h3><span class="pu-close"></span>
        </div>
        <div class="dialog-con">
            <dl>
                <dt>编组名称：</dt>
                <dd>计算机</dd>
            </dl>
            <dl>
                <dt>可选门数</dt>
                <dd>1年级</dd>
            </dl>
        </div>
    </div>
</body>
<script type="text/html" id="tmplToolBar">
    <div class="oprate-table">
        <span class="edit" lay-event="check">查看</span>
        <span class="edit" lay-event="edit">编辑</span>
        <span class="delete" lay-event="delete">删除</span>
    </div>
</script>
<script type="text/javascript">
	var fid = [[${fid}]];
</script>
<script th:src="${_CPR_}+'/elective/js/jquery-3.3.1.min.js'"></script>
<script th:src="${_CPR_}+'/elective/js/common.js'"></script>
<script th:src="${_CPR_}+'/elective/layui/layui.js'"></script>
<script th:src="${_CPR_}+'/elective/js/marshalling.js'"></script>
</html>