<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能体内容</title>
    <link rel="stylesheet" type="text/css" th:href="@{~/css/agenttalk/global.css}"/>
    <link rel="stylesheet" type="text/css" th:href="@{~/css/agenttalk/index.css}"/>
    <style>
        body {
            background: transparent;
            padding: 0;
        }

    </style>
</head>

<body>
<div id="loading" class="chat-item-content-message">
    <div class="content">
        <div class="loadings" style="height:40px;">
            <div class="chat-item-content-message-loading">正在输出中...</div>
            <span class="chat-item-content-svg-loading">
                    <svg xmlns="http://www.w3.org/2000/svg" width="40" height="16" viewBox="0 0 120 30" fill="#409eff">
                        <circle cx="15" cy="15" r="15">
                            <animate attributeName="r" from="15" to="15" begin="0s" dur="0.8s" values="15;9;15"
                                     calcMode="linear" repeatCount="indefinite"/>
                            <animate attributeName="fill-opacity" from="1" to="1" begin="0s" dur="0.8s" values="1;.5;1"
                                     calcMode="linear" repeatCount="indefinite"/>
                        </circle>
                        <circle cx="60" cy="15" r="9" fill-opacity="0.3">
                            <animate attributeName="r" from="9" to="9" begin="0s" dur="0.8s" values="9;15;9"
                                     calcMode="linear" repeatCount="indefinite"/>
                            <animate attributeName="fill-opacity" from="0.5" to="0.5" begin="0s" dur="0.8s"
                                     values=".5;1;.5" calcMode="linear" repeatCount="indefinite"/>
                        </circle>
                        <circle cx="105" cy="15" r="15">
                            <animate attributeName="r" from="15" to="15" begin="0s" dur="0.8s" values="15;9;15"
                                     calcMode="linear" repeatCount="indefinite"/>
                            <animate attributeName="fill-opacity" from="1" to="1" begin="0s" dur="0.8s" values="1;.5;1"
                                     calcMode="linear" repeatCount="indefinite"/>
                        </circle>
                    </svg>
                </span>
            <div class="stop-output" onclick="stopAjax()">停止输出</div>
        </div>
    </div>
</div>


<div id="title-image-link" class="chat-item-content-message" style="display: none">
    <div class="content">
        <div class="card">
            <h3>为您找到以下内容：</h3>
            <a id="title-image-link-link"
               href="https://auth.chaoxing.com/connect/oauth2/authorize?appid=05e2caedf85b43098eaaaa4b4f4a67a5&amp;redirect_uri=https%3A%2F%2Fcourse.chaoxing.com%2Fsvform%2Foption%2FnewBaseData%3Fuid%3D74536248%26mappId%3D8335504%26from_type%3Dspace%26fidEnc%3D5ffbf5828619a172%26mappIdEnc%3Dc5091b01325c280432d546aa4d7ed916&amp;response_type=code&amp;scope=snsapi_base&amp;state=213181"
               class="item" target="_blank">
                <div class="img">
                    <img id="title-image-link-image"
                         src="http://p.ananas.chaoxing.com/star3/origin/8d3c1b3f4f6df19d45243ba39fb5fb7b" alt="">
                </div>
                <div id="title-image-link-title" class="name">排课系统</div>
            </a>
        </div>
    </div>
</div>


<div id="info" class="chat-item-content-message" style="display: none">
    <div class="content">
        <div class="pure-text">
            <div class="types"><span id="info-info" style="white-space: pre-wrap;">您今天下午第三节的课程是：二年级三班-数学</span>
            </div>
        </div>
    </div>
</div>

<div id="table" class="chat-item-content-message" style="display: none">
    <div class="content">
        <div class="table">
            <h3>为您找到以下内容：</h3>
            <div class="t-con">
                <table id="table-table">

                    <!--                    <thead>-->
                    <!--                    <tr>-->
                    <!--                        <th th:each="header, iterStat : ${headers}" th:text="${header}">学号</th>-->
                    <!--                    </tr>-->
                    <!--                    </thead>-->
                    <!--                    <tbody>-->
                    <!--                    <tr th:each="row, iterStat : ${rows}">-->
                    <!--                        <td th:each="info, iterStat : ${row}" th:text="${info}">T112233</td>-->
                    <!--                    </tr>-->

                    <!--                    </tbody>-->
                </table>
            </div>
        </div>
    </div>
</div>
<div id="table-update" class="chat-item-content-message" style="display: none">
    <div class="content">
        <div class="table">
            <h3>为您找到以下内容：</h3>
            <div class="t-con">
                <table id="table-update-table">
<!--                    <thead>-->
<!--                    <tr>-->
<!--                        <th th:each="header, iterStat : ${headers}" th:text="${header}">学号</th>-->
<!--                    </tr>-->
<!--                    </thead>-->
<!--                    <tbody>-->
<!--                    <tr th:each="row, iterStat : ${rows}">-->
<!--                        <td th:each="info, iterStat : ${row}" th:text="${info}">T112233</td>-->
<!--                        <td><a href="https://16q.cn/I2GGNX" target="_blank" th:id="${iterStat.index}">修改</a></td>-->
<!--                    </tr>-->

<!--                    </tbody>-->
                </table>
            </div>
        </div>
    </div>
</div>
<div id="table-title-image-link" class="chat-item-content-message" style="display: none">
    <div class="content">
        <div class="table">
            <h3>为您找到以下内容：</h3>
            <div class="t-con">
                <table id="table-title-image-link-table">
<!--                    <thead>-->
<!--                    <tr>-->
<!--                        <th th:each="header, iterStat : ${headers}" th:text="${header}">学号</th>-->
<!--                    </tr>-->
<!--                    </thead>-->
<!--                    <tbody>-->
<!--                    <tr th:each="row, iterStat : ${rows}">-->
<!--                        <td th:each="info, iterStat : ${row}" th:text="${info}">T112233</td>-->
<!--                    </tr>-->
<!--                    </tbody>-->
                </table>
            </div>
            <h3 id="table-title-image-link-prompt" style="margin-top: 20px;">点击下方应用添加禁排老师</h3><a
                id="table-title-image-link-link"
                href="https://betacourse.chaoxing.com/cxai/data/rule/aiRulePage?taskId=10076&amp;type=7&amp;ruleJson=%7B%22conditionName%22%3A%22%22%2C%22conflictInfoVoList%22%3A%5B%5D%2C%22fid%22%3A216830%2C%22ids%22%3A%22153795%22%2C%22ifConflict%22%3Afalse%2C%22isShow%22%3A0%2C%22names%22%3A%22%E7%99%BD%E6%99%93%E8%95%8A%22%2C%22objectNum%22%3A1%2C%22periods%22%3A%22505%2C506%2C507%2C508%22%2C%22presenter%22%3A%22%E5%BC%A0%E5%AF%8C%E8%BF%9B%22%2C%22ruleName%22%3A%22%E7%A6%81%E6%8E%92%E8%80%81%E5%B8%88-%E5%91%A8%E4%B8%89%22%2C%22taskId%22%3A10076%2C%22teacherId%22%3A%22153795%22%2C%22teachingClassIdSet%22%3A%5B%5D%2C%22termId%22%3A4349%2C%22type%22%3A7%2C%22typeShow%22%3A%22%E7%A6%81%E6%8E%92%E8%80%81%E5%B8%88%22%7D&amp;s=1721202754051"
                class="item" target="_blank">
            <div class="img"><img id="table-title-image-link-img"
                                  src="https://v1.chaoxing.com/vBack/images/default.png" alt=""></div>
            <div id="table-title-image-link-title" class="name">排课规则</div>
        </a>
        </div>
    </div>

</div>
<div id="table-title-image-link-update" class="chat-item-content-message" style="display: none">
    <div class="content">
        <div class="table">
            <h3>为您找到以下内容：</h3>
            <div class="t-con">
                <table id="table-title-image-link-update-table">
<!--                    <thead>-->
<!--                    <tr>-->
<!--                        <th th:each="header, iterStat : ${headers}" th:text="${header}">学号</th>-->
<!--                    </tr>-->
<!--                    </thead>-->
<!--                    <tbody>-->
<!--                    <tr th:each="row, iterStat : ${rows}">-->
<!--                        <td th:each="info, iterStat : ${row}" th:text="${info}">T112233</td>-->
<!--                    </tr>-->
<!--                    </tbody>-->
                </table>
            </div>
            <h3 id="table-title-image-link-update-prompt" style="margin-top: 20px;">点击下方应用添加禁排老师</h3><a
                id="table-title-image-link-update-link"
                href="https://betacourse.chaoxing.com/cxai/data/rule/aiRulePage?taskId=10076&amp;type=7&amp;ruleJson=%7B%22conditionName%22%3A%22%22%2C%22conflictInfoVoList%22%3A%5B%5D%2C%22fid%22%3A216830%2C%22ids%22%3A%22153795%22%2C%22ifConflict%22%3Afalse%2C%22isShow%22%3A0%2C%22names%22%3A%22%E7%99%BD%E6%99%93%E8%95%8A%22%2C%22objectNum%22%3A1%2C%22periods%22%3A%22505%2C506%2C507%2C508%22%2C%22presenter%22%3A%22%E5%BC%A0%E5%AF%8C%E8%BF%9B%22%2C%22ruleName%22%3A%22%E7%A6%81%E6%8E%92%E8%80%81%E5%B8%88-%E5%91%A8%E4%B8%89%22%2C%22taskId%22%3A10076%2C%22teacherId%22%3A%22153795%22%2C%22teachingClassIdSet%22%3A%5B%5D%2C%22termId%22%3A4349%2C%22type%22%3A7%2C%22typeShow%22%3A%22%E7%A6%81%E6%8E%92%E8%80%81%E5%B8%88%22%7D&amp;s=1721202754051"
                class="item" target="_blank">
            <div class="img"><img id="table-title-image-link-update-img"
                                  src="https://v1.chaoxing.com/vBack/images/default.png" alt=""></div>
            <div id="table-title-image-link-update-title" class="name">排课规则</div>
        </a>
        </div>
    </div>

</div>
</body>
<script th:src="@{~/js/agenttalk/jquery-3.3.1.min.js}"></script>
<script th:inline="javascript">
    var questions = [[${questions}]];
    var headers = '';
    var rows = '';
    var type = '';
    var request = $.ajax({
        url: "/agent-talk/questionapi?questions=" + questions,
        type: "get",
        success: function (result) {
            type = result.type;
            document.getElementById(type).style.display = ''
            var appLink = '';
            var imageUrl = '';
            var title = '';
            if (type == 'title-image-link') {
                hiddenLoading()
                appLink = result.appLink;
                var link = document.getElementById("title-image-link-link");
                link.href = appLink;
                imageUrl = result.imageUrl;
                document.getElementById('title-image-link-image').src = imageUrl;
                title = result.data.title;
                document.getElementById('title-image-link-title').innerHTML = title
            }


            var links = '';
            var linksName = '';
            if (type == 'table-update') {
                hiddenLoading()
                links = result.links;
                linksName = result.linksName;
                headers = result.headers
                rows = result.rows
                var html = ' <thead>\n' +
                    '                    <tr>\n';
                for (let i = 0; i < headers.length; i++) {
                    html += '                        <th>' + headers[i] + '</th>\n';
                }

                html += '                    </tr>\n' +
                    '                    </thead>\n' +
                    '                    <tbody>\n';
                if (rows.length>0){
                    for (let i = 0; i < rows.length; i++) {
                        html +=
                            '                    <tr>\n';
                        for (let j = 0; j < rows[i].length; j++) {
                            html += '                        <td>' + rows[i][j] + '</td>\n';
                        }
                        html += '<td><a id="'+i+'"  href="'+links[i]+'" target="_blank" >修改</a></td>';
                        html +=
                            '                    </tr>\n';
                    }
                }

                html += '\n' +
                    '                    </tbody>'

                document.getElementById('table-update-table').innerHTML = html
                if (links.length>0){
                    for (let i = 0; i < links.length; i++) {
                        var alink = document.getElementById(i);
                        document.getElementById(i).innerHTML = linksName
                        alink.href = links[i];
                    }
                }

            }

            if (type == 'table') {
                hiddenLoading()
                headers = result.headers
                rows = result.rows
                var html = ' <thead>\n' +
                    '                    <tr>\n';
                for (let i = 0; i < headers.length; i++) {
                    html += '                        <th>' + headers[i] + '</th>\n';
                }

                html += '                    </tr>\n' +
                    '                    </thead>\n' +
                    '                    <tbody>\n';
                if (rows.length>0){
                    for (let i = 0; i < rows.length; i++) {
                        html +=
                            '                    <tr>\n';
                        for (let j = 0; j < rows[i].length; j++) {
                            html +=
                                '                        <td>' + rows[i][j] + '</td>\n';
                        }
                        html +=
                            '                    </tr>\n';
                    }
                }

                html += '\n' +
                    '                    </tbody>'

                document.getElementById('table-table').innerHTML = html

            }


            var data = '';
            if (type == 'info') {
                hiddenLoading()
                data = result.data;
                document.getElementById('info-info').innerHTML = data
            }

            var data = '';
            var prompt = '';
            if (type == 'table-title-image-link') {
                hiddenLoading()
                appLink = result.appLink;
                var link = document.getElementById("table-title-image-link-link");
                link.href = appLink;
                imageUrl = result.imageUrl;
                document.getElementById('table-title-image-link-img').src = imageUrl;
                title = result.title;
                document.getElementById('table-title-image-link-title').innerHTML = title
                prompt = result.prompt;
                document.getElementById('table-title-image-link-prompt').innerHTML = prompt

                headers = result.headers;
                rows = result.rows;

                var html = ' <thead>\n' +
                    '                    <tr>\n';
                for (let i = 0; i < headers.length; i++) {
                    html += '                        <th>' + headers[i] + '</th>\n';
                }

                html += '                    </tr>\n' +
                    '                    </thead>\n' +
                    '                    <tbody>\n';
                if (rows.length>0){
                    for (let i = 0; i < rows.length; i++) {
                        html +=
                            '                    <tr>\n';
                        for (let j = 0; j < rows[i].length; j++) {
                            html +=
                                '                        <td>' + rows[i][j] + '</td>\n';
                        }
                        html +=
                            '                    </tr>\n';
                    }
                }

                html += '\n' +
                    '                    </tbody>'

                document.getElementById('table-title-image-link-table').innerHTML = html
            }

            if (type == 'table-title-image-update-link') {
                hiddenLoading()
                appLink = result.appLink;
                var link = document.getElementById("table-title-image-link-update-link");
                link.href = appLink;
                imageUrl = result.imageUrl;
                document.getElementById('table-title-image-link-update-img').src = imageUrl;
                title = result.title;
                document.getElementById('table-title-image-link-update-title').innerHTML = title
                prompt = result.prompt;
                document.getElementById('table-title-image-link-update-prompt').innerHTML = prompt

                links = result.links;
                linksName = result.linksName;
                headers = result.headers
                rows = result.rows
                var html = ' <thead>\n' +
                    '                    <tr>\n';
                for (let i = 0; i < headers.length; i++) {
                    html += '                        <th>' + headers[i] + '</th>\n';
                }

                html += '                    </tr>\n' +
                    '                    </thead>\n' +
                    '                    <tbody>\n';
                if (rows.length>0){
                    for (let i = 0; i < rows.length; i++) {
                        html +=
                            '                    <tr>\n';
                        for (let j = 0; j < rows[i].length; j++) {
                            html += '                        <td>' + rows[i][j] + '</td>\n';
                        }
                        html += '<td><a id="'+i+'"  href="'+links[i]+'" target="_blank" >修改</a></td>';
                        html +=
                            '                    </tr>\n';
                    }
                }

                html += '\n' +
                    '                    </tbody>'

                document.getElementById('table-title-image-link-update').innerHTML = html
                if (links.length>0){
                    for (let i = 0; i < links.length; i++) {
                        var alink = document.getElementById(i);
                        document.getElementById(i).innerHTML = linksName
                        alink.href = links[i];
                    }
                }
            }
            $(function () {

                if (navigator.userAgent.toLowerCase().indexOf('firefox') > -1) {
                    // 这是火狐浏览器
                    $(".chat-item-content-message .content .table .t-con table thead").addClass("firefox");
                }

                $(".chat-item-content-message .content .table .t-con table tbody").each(function () {
                    if ($(this).find("tr").length > 5) {
                        $(this).parent().addClass('scroll-tbody');
                    }
                })

                function sendHeight() {
                    //var height = document.documentElement.scrollHeight;
                    window.parent.postMessage({
                        'frameHeight2': $("#" + type).height()
                    }, '*'); // 可以设置为父页面的域名，以增加安全性
                }
                sendHeight();
            })
        }
    })


    function stopAjax() {
        request.abort();
        document.getElementById('info').style.display = ''
        document.getElementById('info-info').innerHTML = '已停止输出'
        hiddenLoading()
    }

    function hiddenLoading() {
        document.getElementById('loading').style.display = 'none'
    }




</script>


</html>