<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学生排考安排</title>
    <link rel="stylesheet" th:href="@{~/css/examination/global.css}">
    <link rel="stylesheet" th:href="@{~/plugin/layui-v2.9.13/layui/css/layui.css}">
    <link rel="stylesheet" th:href="@{~/css/examination/reset.css}">
    <link rel="stylesheet" th:href="@{~/css/examination/student-schedule.css}">
    <script th:src="@{~/plugin/layui-v2.9.13/layui/layui.js}"></script>

    <style>
        .layui-form-select dl dd.layui-this {
            color: #ffffff;
        }
        .layui-form-radio:hover>i{
            color:#c2c2c2;
        }
        .layui-form-radioed:hover>i{
            color:#4D88FF;
        }
        .layui-form-radio:hover div{
            color: #4E5969
        }
        .layui-form-radio > i:hover{
            color:#4D88FF;
        }
        .layui-input:focus, .layui-textarea:focus{
            border-color: #C9C9C9 !important;
            box-shadow: none;
        }
        .hide-element {
            display: none !important;
        }

        .student-wrapper .layui-form .layui-form-item .item-con .lable{

            padding-left: 0;
        }
        .a .layui-form .layui-form-item .item-con .lable {
            display: flex;
            display: -webkit-flex;
            align-items: center;
            justify-content: flex-start;
            padding-left: 0px;
            margin-bottom: 16px;
        }
        .a .layui-form .layui-form-item .item-con .lable .name span {
            width: 16px;
            height: 17px;
            background: url(../../images/examination/tips-icon.png) no-repeat right center;
            background-size: 16px;
            display: inline-block;
            vertical-align: top;
            padding-left: 6px;
            cursor: pointer;
        }
    </style>
</head>

<body>
<div>
    &nbsp;
</div>
</div>
说明：作用于生成考试班
<div>
    &nbsp;
</div>
<div class="a">
    <div class="lay-form">
        <div class="layui-form" id="makeUp">
            <div class="layui-form-item">
                <div class="item-con">
                    <div class="lable">
                        <label style="width: 240px; !important;" class="layui-form-label">排考类型</label>
                        <div class=" item-radio">
                            <div id="pktype" class="radio-list">
                            </div>
                        </div>
                    </div>
<!--                    <div class="lable b">-->
<!--                        <label style="width: 240px; !important;" class="layui-form-label">是否按照同年级同科目拆分补考数据</label>-->
<!--                        <div class=" item-radio">-->
<!--                            <div id="makeupSplit">-->
<!--                                <input type="radio" lay-filter="makeupSplit" name="makeupSplit" value="1" title="是" checked>-->
<!--                                <input type="radio" lay-filter="makeupSplit" name="makeupSplit" value="0" title="否" >-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                    <div class="lable b">-->
<!--                        <label style="width: 255px; !important;" class="name">考场内考试人数设置<span class="layui-tips" data-tip="仅可输入数字"></span></label>-->
<!--                        <div class="item-radio">-->
<!--                            <input type="text" name="makeupCapacity" lay-verify="number" placeholder="请输入数字" autocomplete="off"-->
<!--                                                  onblur="value=zhzs(this.value)" class=" layui-input" value="10"/>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                    <div class="lable b">-->
<!--                        <label style="width: 240px; !important;" class="layui-form-label">剩余学生处理规则</label>-->
<!--                        <div class=" item-radio">-->
<!--                            <div id="makeUpRemainStudentHandleRule">-->
<!--                                <input type="radio" lay-filter="makeUpRemainStudentHandleRule" name="makeUpRemainStudentHandleRule" value="1" title="手动排考" checked>-->
<!--                                <input type="radio" lay-filter="makeUpRemainStudentHandleRule" name="makeUpRemainStudentHandleRule" value="0" title="平均安排到同年级同科目考场" >-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                    <div class="lable b">-->
<!--                        <label style="width: 240px; !important;" class="layui-form-label">考生顺序</label>-->
<!--                        <div class=" item-radio">-->
<!--                            <div id="makeUpOrder">-->
<!--                                <input type="radio" lay-filter="makeUpOrder" name="makeUpOrder" value="1" title="按学号排序" checked>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
                </div>
            </div>
        </div>
    </div>
</div>
<div class="student-wrapper" style="padding-top:1px !important;">

    <div class="layui-form">

    </div>
    <div class="add-exam-schedule">
        <span>+新增一组学生排考安排</span>
    </div>
    <div class="save-settings">
        <span>保存设置</span>
    </div>
</div>
</body>
<script th:src="@{~/js/jquery-3.6.0.min.js}" type="text/javascript" charset="utf-8"></script>
<script th:src="@{~/js/my.util.js}"></script>
<script th:inline="javascript">

    var fid = [[${fid}]];
    var formUserId = [[${formUserId}]];
    var curxnxq = [[${curxnxq}]]
    var curbatch = [[${curbatch}]]
    var checkedJxb = new Set();
    var checkedCourse0 = new Set();
    var checkedCourse1 = new Set();
    var checkedCourse2 = new Set();
    var checkedCourse3 = new Set();
    var curIndex = 0;


    layui.use(['jquery', 'table', 'layer'], function () {
        var table = layui.table,
            form = layui.form,
            layer = layui.layer;
        $ = layui.jquery;
        var util = layui.util;
        let selArr = [];
        $(function () {
            $.get("/examination/rule/getScoreItem", function (res) {
                if (res.code == 200) {
                    selArr = res.data;
                    initSelect();
                }
                $.get("/examination/studentRule/list", {term: curxnxq, bc: curbatch}, function (res) {
                    if (res.code == 200) {
                        var data = res.data;
                        //判断是正考还是补考
                        if (data[0].pktype == 1){
                            $('#pktype').append('\n' +
                                '                        <input type="radio" lay-filter="pktype" name="pktype" value="1" title="正考" checked>\n' +
                                '                        <input type="radio" lay-filter="pktype" name="pktype" value="2" title="补考" >')
                            appendMakeUpRule(data[0])
                            $('#makeUp').children().children().children('.b').hide()
                        }else {
                            $('#pktype').append('\n' +
                                '                        <input type="radio" lay-filter="pktype" name="pktype" value="1" title="正考" >\n' +
                                '                        <input type="radio" lay-filter="pktype" name="pktype" value="2" title="补考" checked>')
                            appendMakeUpRule(data[0])
                            $('.student-wrapper .layui-form').hide()
                        }

                        // $("input[name=pktype][value='" + data[0].pktype + "']").prop("checked", true);
                        if (data != null && data[0].courseRange == 1) {

                            $(".add-exam-schedule span").addClass("show");
                            $(".item0 .add-custom").addClass("show");
                            for (let i = 0; i < data.length; i++) {
                                appendRule(data[i])
                            }
                        } else {
                            appendRule(data[0])
                        }
                    } else {
                        $('#pktype').append('&nbsp;\n' +
                            '                        <input type="radio" lay-filter="pktype" name="pktype" value="1" title="正考" checked>\n' +
                            '                        <input type="radio" lay-filter="pktype" name="pktype" value="2" title="补考" >')
                        appendMakeUpRule()
                        $('#makeUp').children().children().children('.b').hide()
                        appendRule()
                    }

                })
            })


        })
        function appendMakeUpRule(data) {
            var makeupCapacity = 0;
            if (undefined != data ){
                makeupCapacity = data.makeupCapacity ;
            }
            var html =
                '                    <div class="lable b">\n' +
                '                        <label style="width: 240px; !important;" class="layui-form-label">是否按照同年级同科目合并补考数据</label>\n' +
                '                        <div class=" item-radio">\n' +
                '                            <div id="makeupSplit">\n' +
                '                                <input type="radio" lay-filter="makeupSplit" name="makeupSplit" value="1" title="是" '
            if (data != undefined && data.makeupSplit == 1){
                html += 'checked' ;
            }
            html +='>\n' +
                '                                <input type="radio" lay-filter="makeupSplit" name="makeupSplit" value="0" title="否" ' ;
            if (data == undefined || data.makeupSplit == 0){
                html += 'checked' ;
            }
            html +='>\n' +
                '                            </div>\n' +
                '                        </div>\n' +
                '                    </div>\n' +
                '                    <div class="lable b" '
            if (data == undefined || data.makeupSplit == 0){
                html +='style="display: none"'
            }
            html +=    '>\n' +
                '                        <label style="width: 255px; !important;" class="name">考场内考试人数设置<span class="layui-tips" data-tip="仅可输入数字"></span></label>\n' +
                '                        <div id="makeupCapacity" class="item-radio">\n' +
                '                            <input type="text" name="makeupCapacity" lay-verify="number" placeholder="请输入数字" autocomplete="off"\n' +
                '                                                  onblur="value=zhzs(this.value)" class=" layui-input" value="'+makeupCapacity+'"/>\n' +
                '                        </div>\n' +
                '                    </div>\n' +
                '                    <div ' ;
                if(data == undefined || data.makeupSplit == 0){
                    html += 'style="display: none"' ;
                }


            html += ' class="lable b">\n' +
                '                        <label style="width: 255px; !important;" class="name">剩余学生处理规则<span class="layui-tips" data-tip="不足设置人数，则直接生成考试班。如设置人数为30人，实际共有17人需要考试，则17人为一个考试班"></span></label>\n' +
                '                        <div class=" item-radio">\n' +
                '                            <div id="makeupRemainStudentHandleRule">\n' +
                '                                <input type="radio" lay-filter="makeupRemainStudentHandleRule" name="makeupRemainStudentHandleRule" value="1" title="手动排考" ' ;
            if (data == undefined || data.makeupRemainStudentHandleRule == 1){
                html += 'checked'

            }


            html +='>\n' +
                '                                <input type="radio" lay-filter="makeupRemainStudentHandleRule" name="makeupRemainStudentHandleRule" value="3" title="生成新考场" '
            if (data != undefined && data.makeupRemainStudentHandleRule == 3){
                html += 'checked'
            }
            html +='>\n' +
                '                                <input type="radio" lay-filter="makeupRemainStudentHandleRule" name="makeupRemainStudentHandleRule" value="2" title="平均安排到同年级同科目考场" '
            if (data != undefined && data.makeupRemainStudentHandleRule == 2){
                html += 'checked'
            }
            html +=    '>\n' +
                '                            </div>\n' +
                '                        </div>\n' +
                '                    </div>\n' +
                '                    <div class="lable b">\n' +
                '                        <label style="width: 240px; !important;" class="layui-form-label">考生顺序</label>\n' +
                '                        <div class=" item-radio">\n' +
                '                            <div id="makeUpOrder">\n' +
                '                                <input type="radio" lay-filter="makeupOrder" name="makeupOrder" value="1" title="按学号排序" checked>\n' +
                '                            </div>\n' +
                '                        </div>\n' +
                '                    </div>'
            $('#makeUp').children().children().append(html)
        }
        form.on('radio(pktype)', function (data) {
            if (data.value == 2){
                $('.student-wrapper .layui-form').hide()
                $('#makeUp').children().children().children('.b').show()
                if ($('input[name="makeupSplit"]:checked').val() == 0){
                    $('#makeupCapacity').parent().hide()
                    $('#makeupRemainStudentHandleRule').parent().parent().hide()
                }
            }else {
                $('.student-wrapper .layui-form').show()
                $('#makeUp').children().children().children('.b').hide()
            }
        });
        form.on('radio(makeupSplit)', function (data) {
            if (data.value == 1){
                $('#makeupCapacity').parent().show()
                $('#makeupRemainStudentHandleRule').parent().parent().show()
            }else {
                $('#makeupCapacity').parent().hide()
                $('#makeupRemainStudentHandleRule').parent().parent().hide()
            }
        });
        function initSelect() {
            for (var i = 0; i < selArr.length; i++) {
                var item = selArr[i];
                var select = $('#semester0');
                var option = $('<option>').val(item.term).text(item.term);
                select.append(option);
            }
            form.render('select');
        }


        form.on('radio(courseScope0)', function (data) {

            if (data.value == 0) {
                $(".add-exam-schedule span").removeClass("show");
                $(".item0 .add-custom").removeClass("show");
                $(".item0 .delet").removeClass("show");
            } else if (data.value == 1) {
                $(".add-exam-schedule span").addClass("show");
                $(".item0 .add-custom").addClass("show");

            }
        });

        // 添加自定义课程范围
        let num = -1;
        $(".student-wrapper .add-exam-schedule span").click(function () {
            appendRule();
        })


        function appendRule(data) {
            num++;
            let optionHtml = '  <option value="">选择学年学期</option>';
            for (var i = 0; i < selArr.length; i++) {
                var item = selArr[i];
                optionHtml += '<option value="' + item.term + '" ';
                if (data != null && data.term == item.term) {
                    optionHtml += 'selected';
                }
                optionHtml += '>' + item.term + '</option>'
            }
            let itemHtml = '  <option value="">选择成绩分项</option>';
            if (data != undefined && selArr.length > 0) {
                let filterArr = selArr.filter(item => item.term == data.term);
                if (filterArr.length > 0) {
                    for (var i = 0; i < filterArr[0].itemName.length; i++) {
                        let item = filterArr[0].itemName[i];
                        itemHtml += '<option value="' + item + '" ';
                        if (data.itemName == item) {
                            itemHtml += 'selected';
                        }
                        itemHtml += ' >' + item + '</option>'

                    }
                }

            }
            let pkHtml = '<div class="layui-form-item item' + num + '">' +
                '<div class="item-radio">' +
                '<input type="radio" lay-filter="courseScope' + num +
                '" name="courseScope' + num +
                '" value="0" title="全部课程范围" '
            if (data == undefined || data.courseRange == 0) {
                pkHtml += 'checked';
            }
            pkHtml += '>' +
                '<input type="radio" lay-filter="courseScope' + num +
                '" name="courseScope' + num +
                '" value="1" title="自定义课程范围" ';
            if ((data != undefined && data.courseRange == 1)||num>0) {
                pkHtml += 'checked';
            }
            pkHtml += '>' +
                '<div class="add-custom " lay-on="iframe-course" >+添加自定义课程范围</div>' +
                ' <div class="delet">删除</div>' +
                '</div>' +
                '<div class="item-con">' +
                '<div class="lable">' +
                '<div class="name"> 排考方式<span class="layui-tips" data-tip="自动排考学生按照行政班或打乱顺序排考"></span></div>' +
                '<div class="radio-tab">' +

                '<input type="radio" lay-filter="pkway' + num +
                '" name="pkway' + num +
                '" value="-1" title="优先按照教学班排考"' ;
                if (data == null || data.rule1 == -1){
                    pkHtml += ' checked ';
                }
            pkHtml +=  '>' +
                '<input type="radio" lay-filter="pkway' + num +
                '" name="pkway' + num +
                '" value="0" title="优先按照行政班排考" ';
            if (data != null && data.rule1 == 0) {
                pkHtml += 'checked';
            }
            pkHtml += '>' +
                '<input type="radio" lay-filter="pkway' + num +
                '" name="pkway' + num +
                '" value="1" title="优先按课程排考" ';
            if (data != undefined && data.rule1 == 1) {
                pkHtml += 'checked';
            }
            pkHtml += '>' +
                '</div>' +
                '</div>'



            //

            pkHtml +=            '<div'
            if (data != undefined && data != null && (data.rule1 == 0 ||data.rule1 == 1 )){
                pkHtml += ' style="display: none"'
            }

            pkHtml += ' class="lable">\n' +
                '                    <label class="name">按考场内考试人数拆分考试数据<span class="layui-tips"\n' +
                '                                                                       data-tip="适用于考试任务人数较多需要安排多个考场进行考试的情况，如：一条任务为100人的考试，考场容量参数设置为50，则拆分为2条50人的考试数据进行排考"></span></label>\n' +
                '                    <div class="radio-list">\n' +
                '                        <div onclick="splitOnclick('+num+')" class="radio-list">\n' +
                '                            <input type="radio" lay-filter=split'+num +'" name="split'+num +'" value="1" title="是"';
            if(data != undefined && data.split != null && data.split == '1'){
                pkHtml += ' checked' ;
            }
            pkHtml += '>\n' +
                '                            <input type="radio" lay-filter="split'+num +'" name="split'+num +'" value="0" title="否" ';
            if(data == undefined || data.split == null || data.split =='' || data.split == '0'){
                pkHtml += 'checked' ;
            }
            pkHtml += '>\n' +
                '                        </div>\n' +
                '                    </div>\n' +
                '                </div>\n' +


                '                <div class="lable" '
            if(data == undefined || (data.rule1 != 1 && (data.split == null || data.split =='' || data.split == '0' || data.rule1 == 0))){
                pkHtml += 'style="display: none"';
            }

            var capacity
            if (data == undefined){
                capacity = ''
            }else {
                capacity = data.capacity
            }
            pkHtml +=    ' >\n' +
                '                    <label class="name">考场内考试人数设置<span class="layui-tips" data-tip="仅可输入数字"></span></label>\n' +
                '                    <div class="radio-list">\n' +
                '                        <input type="text" name="capacity'+ num +'" lay-verify="number" placeholder="请输入数字" autocomplete="off"\n' +
                '                               onblur="value=zhzs(this.value)" class=" layui-input" value="'+capacity +'"/>\n' +
                '                    </div>\n' +
                '                </div>\n' +


                '                <div class="lable" '
            if(data == null || data.split == null || data.split =='' || data.split == '0'|| data.rule1 == 0|| data.rule1 == 1){
                pkHtml += 'style="display: none"';
            }
            pkHtml +=    '>\n' +
                '                    <label class="name">剩余学生处理规则<span class="layui-tips"\n' +
                '                                                                 data-tip="剩余学生处理规则"></span></label>\n' +
                '                    <div class="radio-list">\n' +
                '                        <div class="radio-list">\n' +
                '                            <input type="radio" lay-filter="remainStudentHandleRule" name="remainStudentHandleRule'+num+'" value="0" title="手动安排"'
            if (data == undefined || data.remainStudentHandleRule == '0'){
                pkHtml += ' checked'
            }
            pkHtml +='>\n' +
                '                            <input type="radio" lay-filter="remainStudentHandleRule" name="remainStudentHandleRule'+num+'" value="1" title="生成新的考场"'
            if (data != undefined && data.remainStudentHandleRule == '1'){
                pkHtml += ' checked'
            }
            pkHtml +='>\n' +
                '                            <input type="radio" lay-filter="remainStudentHandleRule" name="remainStudentHandleRule'+num+'" value="2" title="均分到所有考场"'
            if (data != undefined && data.remainStudentHandleRule == '2'){
                pkHtml += ' checked'
            }
            pkHtml +='>\n' +
                '                            <input type="radio" lay-filter="remainStudentHandleRule" name="remainStudentHandleRule'+num+'" value="3" title="平均安排到同年级同科目考场"'
            if (data != undefined && data.remainStudentHandleRule == '3'){
                pkHtml += ' checked'
            }
            pkHtml +='>\n' +
                '                        </div>\n' +
                '                    </div>\n' +
                '                </div>'







            pkHtml +=  '<div class="lable">' +
                '<div class="name"> 考生顺序<span class="layui-tips"';
            if (data == null || data.rule1 == 0 || data.rule1 == -1) {
                pkHtml += 'data-tip="自动排考分配学生排考的顺序，按学号排序：从小到大排序；按成绩排序：按照行政班最近一次成绩从高到低排序"></span>'
            } else {
                pkHtml += 'data-tip="自动排考分配学生排考的顺序，随机平均分配到所有考场：例如：3个班级各35人需均匀分配至五个考场（每场最多25人），每考场同班学生不超过7人且尽量分散。按成绩排序：按照同年级同系部最近一次成绩从高到低排序"></span>';
            }
            pkHtml += '</div>' +
                '<div class="radio-list">' +
                '<div class="radio '
            if (data == null || data.rule1 == 0 || data.rule1 == -1) {
                pkHtml += ' cur'
            }
            pkHtml += '">' +
                '<input type="radio" lay-filter="order' + num +
                '" name="order' + num +
                '" value="1" title="按学号排序" '
            if (data == null || data.rule2 == null || data.rule2 == '' || data.rule2 == 1) {
                pkHtml += 'checked';
            }
            pkHtml += '>'

            pkHtml +=        '<input id="dlpx'+num+'" type="radio" lay-filter="order' + num +
                '" name="order' + num +
                '" value="3" title="打乱排序" '
            if (data != null && data.rule2 != null && data.rule2 == 3) {
                pkHtml += 'checked';
            }
            pkHtml += '>' ;
                pkHtml +=    '<input id="acjpx'+num+'" type="radio" lay-filter="order' + num +
                    '" name="order' + num +
                    '" value="2" title="按成绩排序" ';
                if (data != undefined && data.rule2 == 2) {
                    pkHtml += 'checked';
                }
                pkHtml += '>' ;


            pkHtml += '</div>' +
                '<div class="radio'
            if (data != null && data.rule1 == 1) {
                pkHtml += ' cur'
            }
            pkHtml += '">'
            pkHtml +=
                '<input type="radio" lay-filter="corder' + num +
                '" name="corder' + num +
                '" value="3" title="打乱排序" ';
            if (data != undefined && data.rule2 == 3) {
                pkHtml += 'checked';
            }
            pkHtml += '>';

            pkHtml +=
                '<input type="radio" lay-filter="corder' + num +
                '" name="corder' + num +
                '" value="1" title="按学号排序" ';
            if (data != undefined && data.rule2 == 1) {
                pkHtml += 'checked';
            }
            pkHtml += '>';

            // if(data != undefined && data.rule1 != 1){
                pkHtml +=    '<input id="xsacjpx'+num+'"  type="radio" lay-filter="corder' + num +
                    '" name="corder' + num +
                    '" value="4" title="学生按成绩排名排序"'
                if (data != undefined && data.rule2 == 4) {
                    pkHtml += 'checked';
                }
                pkHtml += '>';
            // }




            pkHtml +=    '</div>' +
                '</div>' +
                ' <div class="select select1'
            if (data != undefined && (data.rule2 == 2 || data.rule2 == 4)) {
                pkHtml += ' show';
            }
            pkHtml += '"/>' +
                '<select lay-filter="semester' + num +
                '" id="semester' + num + '">' +
                optionHtml +
                '</select>' +
                '</div>' +
                '<div class="select select2'
            if (data != undefined && (data.rule2 == 2 || data.rule2 == 4)) {
                pkHtml += ' show';
            }
            pkHtml += '">' +
                '<select lay-filter="score' + num +
                '" id="score' + num + '">' +
                itemHtml +
                '</select>' +
                '</div>' +
                '</div>' ;


            //rule3 考场与座位安排 1 所有安排均按照第一场考试排序 2 随机排序
            pkHtml += '<div class="lable" id="rule3'+num+'" ' ;
            if (data!=null&&(data.rule2 == 3 || data.rule2 == 1) && data.rule1 == 1) {
            }else {
                pkHtml +=  'style="display: none"' ;
            }
            pkHtml +=   '>' +
                '<div class="name"> 考场与座位安排';
                // '<div class="name"> 考场与座位安排<span class="layui-tips"';
            // if (data == null || data.rule1 == 0) {
            //     pkHtml += 'data-tip="自动排考分配学生排考的顺序，按学号排序：从小到大排序；按成绩排序：按照行政班最近一次成绩从高到低排序"></span>'
            // } else {
            //     pkHtml += 'data-tip="自动排考分配学生排考的顺序，随机平均分配到所有考场：例如：3个班级各35人需均匀分配至五个考场（每场最多25人），每考场同班学生不超过7人且尽量分散。按成绩排序：按照同年级同系部最近一次成绩从高到低排序"></span>';
            // }
            pkHtml += '</div>' +
                '<div class="radio-list">' +
                '<div class="radio '

            pkHtml += ' cur'
            pkHtml += '">' +
                '<input type="radio" lay-filter="rule3' + num +
                '" name="rule3' + num +
                '" value="1" title="所有安排均固定按照第一场考试排序" '
            if (data == null || data.rule3 == 1) {
                pkHtml += 'checked';
            }
            pkHtml += '>' +
                '<input type="radio" lay-filter="rule3' + num +
                '" name="rule3' + num +
                '" value="2" title="不固定排序" ';
            if (data != undefined && data.rule3 == 2) {
                pkHtml += 'checked';
            }
            pkHtml += '>' +
                '</div>' +
                '<div class="radio"'
            pkHtml +=   '</div>'
            pkHtml += '</div>';
            pkHtml += '</div>';
            pkHtml += '</div>';



            $(".student-wrapper .layui-form").append(pkHtml);
            // $(".student-wrapper .layui-form").append('<div id="123123"> 123123</div>');

            let itemLength = $(".student-wrapper .layui-form-item").length;
            if ((data != undefined && data.courseRange == 1 )|| itemLength > 1) {
                $(".add-custom").addClass("show");
                $(".delet").addClass("show");
            }

            $(".student-wrapper .layui-form .layui-form-item").eq(0).find(".delet").removeClass("show");
            if (itemLength == 4) {
                $(".student-wrapper .add-exam-schedule span").removeClass("show");
            }

            computed();
            form.render('radio');
            form.render('select');
            if (num > 0) {
                for (let i = 0; i < num + 1; i++) {
                    $("input[name='courseScope" + i + "'][value=0]").attr("disabled", "disabled");
                    $("input[name='courseScope" + i + "'][value=0]").next().addClass('layui-radio-disabled layui-disabled');
                }
            }

            for (let i = 0; i < num + 1; i++) {
                //隐藏学生按成绩排名排序
                if (data != null && data.rule1 == '1'){
                    $("#xsacjpx"+num).parent().find(".layui-form-radio:last-child").hide();
                }

                if ($("input[name=split"+num+"]:checked").val() == 1 && data.rule1 != '1'){
                    if ($("input[name=pkway"+num+"]:checked").val() != 0){
                        $("#score"+num).parent().parent().children('.radio-list').children('.cur').find(".layui-unselect:last-child").hide()
                        $("#acjpx"+num).parent().find(".layui-form-radio:last-child").hide();
                    }
                }
            }


        }

        //自定义课程弹窗
        $(".student-wrapper ").on("click", ".layui-form-item .add-custom", function () {
            var idx = $(this).parents(".layui-form-item").index();
            curIndex = idx;
            util.on('lay-on', {
                'iframe-course': function () {
                    var index = layer.open({
                        title: '课程范围',
                        type: 2,
                        area: ['100%', '100%'],
                        content: '/examination/rule/init147140.html',
                        fixed: true, // 不固定
                        maxmin: true,
                        shadeClose: false
                    });
                },
            })
        })


        function computed() {
            form.on('select(semester' + num + ')', function (data) {
                let kol = $(data.elem).parents(".layui-form-item").attr("class").split(' ')[1].split("item")[1];
                let kols = parseInt(kol);
                let filterArr = selArr.filter(item => item.term == data.value);

                $('#score' + kols + '').empty();
                $('#score' + kols + '').append('<option value="">选择成绩分项</option>');

                for (var i = 0; i < filterArr[0].itemName.length; i++) {
                    let item = filterArr[0].itemName[i];
                    let select = $('#score' + kols + '');
                    let option = $('<option>').val(item).text(item);


                    select.append(option);
                }
                $("#score" + kols + "").parent().addClass("show");
                form.render('select');
            });

            form.on('radio(pkway' + num + ')', function (data) {

                let kol = $(data.elem).parents(".layui-form-item").attr("class").split(' ')[1].split("item")[1];
                let kols = parseInt(kol);
                if (data.value == 0 || data.value == -1) {
                    document.getElementById('rule3'+kols).style.display='none'
                    $(this).parents(".item-con").find(".lable").eq(1).find(".name span").eq(0).attr("data-tip", "自动排考分配学生排考的顺序，按学号排序：从小到大排序；按成绩排序：按照行政班最近一次成绩从高到低排序")

                    $(".item" + kols + " .radio-list").find(".radio").eq(0).addClass("cur").siblings().removeClass("cur");

                    let values = $('input[name="order' + kols + '"]:checked').val();
                    if (values == 1) {
                        $(".item" + kols + "").find(".select").removeClass("show");
                    } else if (values == 2) {
                        // $("#semester" + kols + "").val('');
                        form.render('select');
                        $(".item" + kols + "").find(".select1").addClass("show");
                        $(".item" + kols + "").find(".select2").removeClass("show");
                    } else {
                        $(".item" + kols + "").find(".select1").removeClass("show");
                        $(".item" + kols + "").find(".select2").removeClass("show");
                    }

                    if (data.value == 0) {
                        $("input[name=split"+num+"]").parent(".radio-list").parent(".radio-list").parent(".lable").hide()
                        $("input[name=capacity"+num+"]").parent(".radio-list").parent(".lable").hide()
                        $("input[name=remainStudentHandleRule"+num+"]").parent(".radio-list").parent(".radio-list").parent(".lable").hide()
                        $("#acjpx"+num).parent().find(".layui-form-radio").show();
                        // saveReport();
                        return;
                    }else {
                        if ($("input[name=split"+num+"]:checked").val() == 0){
                            $("input[name=split"+num+"]").parent(".radio-list").parent(".radio-list").parent(".lable").show()
                            $("#acjpx"+num).parent().find(".layui-form-radio").show();
                            $("input[name=capacity"+num+"]").parent(".radio-list").parent(".lable").hide()
                        }else {
                            $("input[name=split"+num+"]").parent(".radio-list").parent(".radio-list").parent(".lable").show()
                            $("input[name=capacity"+num+"]").parent(".radio-list").parent(".lable").show()
                            $("input[name=remainStudentHandleRule"+num+"]").parent(".radio-list").parent(".radio-list").parent(".lable").show()
                            $("#acjpx"+num).parent().find(".layui-form-radio:last-child").hide();
                        }
                    }

                } else if (data.value == 1) {
                    $("input[name=corder"+num+"]").parent('.radio').children('.layui-form-radio')[0].click()
                    // if ($("input[name=corder"+num+"]").parent('.radio').children('.layui-form-radio').length > 1){
                    //     $("input[name=corder"+num+"]").parent('.radio').children('.layui-form-radio')[1].classList.add('hide-element');
                    // }

                    if (document.getElementById('rule3'+kols).name=='show'){
                        document.getElementById('rule3'+kols).style.display=''
                    }
                    $(this).parents(".item-con").find(".lable").eq(1).find(".name span").eq(0).attr("data-tip", "自动排考分配学生排考的顺序，随机平均分配到所有考场：例如：3个班级各35人需均匀分配至五个考场（每场最多25人），每考场同班学生不超过7人且尽量分散。按成绩排序：按照同年级同系部最近一次成绩从高到低排序；")
                    $(".item" + kols + " .radio-list").find(".radio").eq(1).addClass("cur").siblings().removeClass("cur");
                    let values1 = $('input[name="corder' + kols + '"]:checked').val();

                    if (values1 == 3) {
                        $(".item" + kols + "").find(".select").removeClass("show");
                    } else if (values1 == 4) {
                        // $("#semester" + kols + "").val('');
                        form.render('select');
                        $(".item" + kols + "").find(".select1").addClass("show");
                        $(".item" + kols + "").find(".select2").removeClass("show");
                    } else {
                        $(".item" + kols + "").find(".select1").removeClass("show");
                        $(".item" + kols + "").find(".select2").removeClass("show");

                    }
                    if ($("input[name=split"+num+"]:checked").val() == 0){
                        $("input[name=split"+num+"]").parent(".radio-list").parent(".radio-list").parent(".lable").show()

                        // $("#semester"+num).parent().addClass('show')
                        // $("#score"+num).parent().addClass('show')
                        $("#score"+num).parent().parent().children('.radio-list').children('.cur').find(".layui-unselect:last-child").show()
                    }else {
                        $("input[name=rule3"+num+"]").parent(".radio").parent(".radio-list").parent(".lable").show()
                        $("input[name=remainStudentHandleRule"+num+"]").parent(".radio-list").parent(".radio-list").parent(".lable").hide()

                        $("#semester"+num).parent().removeClass('show')
                        $("#score"+num).parent().removeClass('show')
                        $("#score"+num).parent().parent().children('.radio-list').children('.cur').find(".layui-unselect:last-child").hide()
                    }
                    $("input[name=capacity"+num+"]").parent(".radio-list").parent(".lable").show()
                    $("input[name=split"+num+"]").parent(".radio-list").parent(".radio-list").parent(".lable").hide()
                }
            });

            form.on('radio(order' + num + ')', function (data) {
                let kol = $(data.elem).parents(".layui-form-item").attr("class").split(' ')[1].split("item")[1];
                let kols = parseInt(kol);

                if (data.value == 1) {
                    $(".item" + kols).find(".select").removeClass("show");

                } else if (data.value == 2) {
                    $(".item" + kols).find(".select").addClass("show");
                }
            });

            form.on('radio(corder' + num + ')', function (data) {
                let kol = $(data.elem).parents(".layui-form-item").attr("class").split(' ')[1].split("item")[1];
                let kols = parseInt(kol);

                if (data.value == 3) {
                    $(".item" + kols).find(".select").removeClass("show");
                    document.getElementById('rule3'+kols).style.display=''
                    document.getElementById('rule3'+kols).name='show'
                } else if (data.value == 4) {
                    $(".item" + kols).find(".select").addClass("show");
                    document.getElementById('rule3'+kols).style.display='none'
                    document.getElementById('rule3'+kols).name=''
                }
            });

        }


        //删除

        $(".student-wrapper ").on("click", ".layui-form-item .delet", function () {
            var idx = $(this).parents(".layui-form-item").index();
            if (idx == 1) {
                checkedJxb = new Set([...checkedJxb].filter(x => !checkedCourse1.has(x)));
                checkedCourse1 = new Set();
            }
            if (idx == 2) {
                checkedJxb = new Set([...checkedJxb].filter(x => !checkedCourse2.has(x)));
                checkedCourse2 = new Set();
            }
            if (idx == 3) {
                checkedJxb = new Set([...checkedJxb].filter(x => !checkedCourse3.has(x)));
                checkedCourse3 = new Set();
            }
            $(this).parents(".layui-form-item").remove();
            let itemLength = $(".student-wrapper .layui-form-item").length;
            $(".student-wrapper .add-exam-schedule span").addClass("show");
            if (itemLength == 1) {
                $(".add-exam-schedule span").addClass("show");
                $("input[name='courseScope0'][value='0']").removeAttr("disabled");
                $("input[name='courseScope0'][value='0']").next().removeClass('layui-radio-disbaled layui-disabled');
                form.render('radio');
            }
        })


        var layTips = "";

        $(document).on('mouseenter', '.layui-tips', function () {
            var that = this;
            var con = $(this).attr('data-tip')
            layTips = layer.tips(con, that, {
                tips: 1,
                time: 50000
            });
        });

        $(document).on('mouseleave', '.layui-tips', function () {
            layer.close(layTips);
        });

        //保存设置
        $(".save-settings").click(function () {
            // form.render();
            let num = $(".student-wrapper .layui-form .layui-form-item").length;

            let submitArr = [];
            for (var i = 0; i < num; i++) {
                var item = $(".student-wrapper .layui-form .layui-form-item").eq(i);
                $(item).attr("id")
                let pol = item.attr("class").split(' ')[1].split("item")[1];

                let pols = parseInt(pol);
                let radioValue = $('input[name="courseScope' + pols + '"]:checked').val();
                let radioValue1 = $('input[name="pkway' + pols + '"]:checked').val();
                let rule3 = $('input[name="rule3' + pols + '"]:checked').val();
                let pkway = $('input[name="pkway' + pols + '"]:checked').val();
                let split = $('input[name="split' + pols + '"]:checked').val();
                let capacity = $('input[name="capacity' + pols + '"]').val();
                let pktype = $("input[name=pktype]:checked").val();
                let remainStudentHandleRule = $('input[name="remainStudentHandleRule' + pols + '"]:checked').val();
                let makeupRemainStudentHandleRule = $('input[name="makeupRemainStudentHandleRule"]:checked').val();
                let makeupSplit = $('input[name="makeupSplit"]:checked').val();
                let makeupCapacity = $('input[name="makeupCapacity"]').val();
                let makeupOrder = $('input[name="makeupOrder"]:checked').val();
                if (pkway == 1){
                    if (capacity == undefined || capacity == '' || capacity == 0) {
                        if (num == 1){
                            layer.msg("请填写考场容量", {icon: 2, time: 3000});
                        }else {
                            layer.msg("请填写第【" + (pols + 1) + "】个规则的考场容量", {icon: 2, time: 3000});
                        }

                        return false;
                    }
                }
                if (split == 1) {
                    if (capacity == undefined || capacity == '' || capacity == 0) {
                        if (num == 1){
                            layer.msg("请填写考场容量", {icon: 2, time: 3000});
                        }else {
                            layer.msg("请填写第【" + (pols + 1) + "】个规则的考场容量", {icon: 2, time: 3000});
                        }

                        return false;
                    }
                }
                let params = {};

                params["courseRange"] = radioValue;
                params["rule1"] = radioValue1;
                params["rule3"] = rule3;
                params["batchCode"] = curbatch;
                params["batchTerm"] = curxnxq;
                params["split"] = split;
                params["capacity"] = capacity;
                params["pktype"] = pktype;
                params["remainStudentHandleRule"] = remainStudentHandleRule;
                params["makeupRemainStudentHandleRule"] = makeupRemainStudentHandleRule;
                params["makeupSplit"] = makeupSplit;
                params["makeupCapacity"] = makeupCapacity;
                params["makeupOrder"] = makeupOrder;


                let kols = $(".item" + pols + " .radio-list").find(".cur").index();

                if (kols == 0) {
                    let radioValue2 = $('input[name="order' + pols + '"]:checked').val();
                    params["rule2"] = radioValue2;
                    if (radioValue2 == 2) {
                        let selectValue = $('#score' + pols + '').val();
                        let selectValue1 = $('#semester' + pols + '').val();
                        params["term"] = selectValue1;
                        params["itemName"] = selectValue;
                    }
                } else {
                    let radioValue3 = $('input[name="corder' + pols + '"]:checked').val();
                    params["rule2"] = radioValue3;
                    if (radioValue3 == 4) {
                        let selectValue = $('#score' + pols + '').val();
                        let selectValue1 = $('#semester' + pols + '').val();
                        params["term"] = selectValue1;
                        params["itemName"] = selectValue;
                    }
                }
                if (radioValue == 1) {

                    if (pols == 0) {
                        if (checkedCourse0.size == 0) {
                            U.fail("请选择第【" + (pols + 1) + "】个规则的课程范围")
                            return false;
                        }
                        params["classCode"] = [...checkedCourse0].join(",");
                    }
                    if (pols == 1) {
                        if (checkedCourse1.size == 0) {
                            U.fail("请选择第【" + (pols + 1) + "】个规则的课程范围")
                            return false;
                        }
                        params["classCode"] = [...checkedCourse1].join(",");
                    }
                    if (pols == 2) {
                        if (checkedCourse2.size == 0) {
                            U.fail("请选择第【" + (pols + 1) + "】个规则的课程范围")
                            return false;
                        }
                        params["classCode"] = [...checkedCourse2].join(",");
                    }
                    if (pols == 3) {
                        if (checkedCourse3.size == 0) {
                            U.fail("请选择第【" + (pols + 1) + "】个规则的课程范围")
                            return false;
                        }
                        params["classCode"] = [...checkedCourse3].join(",");
                    }

                    if (params.term == "" || params.itemName == "") {
                        U.fail("请选择第【" + (pols + 1) + "】个规则的历史成绩")
                        return false
                    }
                }
                if ((params.rule2==2||params.rule2==4)&&(params.term == "" || params.itemName == "")) {
                    U.fail("请选择第【" + (pols + 1) + "】个规则的历史成绩")
                    return false
                }
                submitArr.push(params);
            }
            $.post("/examination/studentRule/save", {data: JSON.stringify(submitArr)}, function (res) {
                if (res.code == 200) {
                    U.success("保存成功")
                } else {
                    U.fail(res.msg)
                }
            })
        });



    })

    function zhzs(value) {
        value = value.replace(/[^\d]/g, '').replace(/^0{1,}/g, '');
        if (value != '') {
            value = parseInt(value);
        }
        return value;
    }

    function splitOnclick(num) {
        //隐藏按成绩排序
        if ($("input[name=split" + num + "]:checked").val() == 1){
            $("#acjpx"+num).parent().find(".layui-form-radio:last-child").hide();
            $("#score"+num).parent().parent().children('.radio-list').children('.cur').find(".layui-unselect:last-child").hide()
            $("#semester"+num).parent().removeClass('show')
            $("#score"+num).parent().removeClass('show')
        }else {
            $("#acjpx"+num).parent().find(".layui-form-radio").show();
            $("#score"+num).parent().parent().children('.radio-list').children('.cur').find(".layui-unselect:last-child").show()
            // $("#semester"+num).parent().addClass('show')
            // $("#score"+num).parent().addClass('show')
        }
        if ($("input[name=split"+num+"]:checked").val() == 0) {
            $("input[name=capacity"+num+"]").parent(".radio-list").parent(".lable").hide()
            $("input[name=remainStudentHandleRule"+num+"]").parent(".radio-list").parent(".radio-list").parent(".lable").hide()
            // saveReport();
            return;
        }
        $("input[name=capacity"+num+"]").parent(".radio-list").parent(".lable").show()
        $("input[name=remainStudentHandleRule"+num+"]").parent(".radio-list").parent(".radio-list").parent(".lable").show()
    }
</script>

</html>