<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" th:href="@{~/css/creditManage/global.css}">
    <link rel="stylesheet" th:href="@{~/plugin/layui/css/layui.css}">
    <link rel="stylesheet" th:href="@{~/css/creditManage/common.css}">
    <link rel="stylesheet" th:href="@{~/css/creditManage/reset.css}">
    <link rel="stylesheet" th:href="@{~/css/creditManage/index.css}">
    <link rel="stylesheet" th:href="@{~/css/creditManage/scoreApply.css}">
    <style>
        @keyframes layui-upbit {
            /*from {*/
            /*    transform: translate3d(0, 30px, 0);*/
            /*    opacity: .3*/
            /*}*/
            /*to {*/
            /*    transform: translate3d(0, 0, 0);*/
            /*    opacity: 1*/
            /*}*/
        }
        .j-search-con .j-select-year {
            max-height: 160px;
        }

        .downloadTemplate .popup-box .lab-list {
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .dialog-stu {
            width: 1240px;
            margin-left: auto;
            margin-right: auto;
        }

        .popup .popup-box .pu-con {
            padding: 0;
        }

        .j-search-con .mySelect ul {
            margin-top: 47px;
        }
        .j-search-con .mySelect .mySearch {
            position: fixed;
            width: 228px;
            border-radius: 0px;
            z-index: 999;
            border-bottom: #f5f7fa 2px solid;
            margin: 0;
            background: #fff;
        }
        .dialog {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translateY(-50%) translateX(-50%);
            background-color: #f9fafb;
        }

    </style>
</head>

<body>
<div class="popup downloadTemplate" style="display:block;">
    <div class="popup-box" style="width:460px;border-radius:10px">
        <div class="pu-title">下载模板</div>
        <div class="pu-con">
            <div class="lab-list" style="height: 208px;">
                <div class="lab level level1">
                    <div class="name">选择下载模板</div>
                    <div class="j-search-con single-box">
                        <input type="text" placeholder="请选择" readonly="" class="schoolSel" />
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <ul id="template">
                                <li id="clazz">按班级下载模板</li>
                                <li id="stu">按学生下载模板</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="lab level level2 hide">
                    <div class="name">选择下载班级</div>
                    <div class="j-search-con multiple-box">
                        <input type="text" placeholder="请选择" readonly="" class="schoolSel" name="className"/>
                        <span class="j-arrow"></span>
                        <div class="j-select-year mySelect">
                            <div class="search mySearch">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <ul id="classes"></ul>
                        </div>
                    </div>
                </div>
                <div class="lab level level3 hide">
                    <div class="name">选择下载学生</div>
                    <div class="j-search-con single-box">
                        <input type="text" name="stu" readonly required lay-verify="required"
                               placeholder="点击选择申请学生" id="stuApply" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
        </div>
        <div class="pu-btn">
            <button class="pu-cancel">取消</button>
            <button class="pu-sure">确定</button>
        </div>
    </div>
</div>
<div class="dialog-wrap" style="display:none;z-index: 999;border-radius: 0">
    <div class="dialog dialog-stu">
        <div class="dialogCon">
            <form class="layui-form form-stu" id="formStu" action="" lay-filter="formStu">
                <div class="item-search-con">
                    <div class="layui-form-item">
                        <label class="layui-form-label">年级</label>
                        <div class="layui-input-block">
                            <select name="grade" class="grade" lay-search="">
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">院系</label>
                        <div class="layui-input-block">
                            <select name="depth" class="depth" lay-search="">
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">专业</label>
                        <div class="layui-input-block">
                            <select name="stuMajor" class="stuMajor" lay-search="">

                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">班级</label>
                        <div class="layui-input-block">
                            <select name="stuClass" class="stuClass" lay-search="">

                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">姓名</label>
                        <div class="layui-input-block">
                            <select name="xsxm" class="stuName" lay-search="">
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">学号</label>
                        <div class="layui-input-block">
                            <select name="xsxh" class="stuXh" lay-search="">
                            </select>

                        </div>
                    </div>
                </div>
                <div class="item-search-btn">
                    <div class="layui-form-item">
                        <button type="submit" class="layui-btn set-button layui-btn layui-bg-blue" lay-submit
                                lay-filter="searchBtn" id="search3">查询
                        </button>

                        <button type="reset" class="set-button layui-btn layui-btn-primary layui-border-blue"
                                id="reset3">重置
                        </button>
                    </div>
                </div>

            </form>
            <div class="j-table">
                <table class="layui-table" id="stuList" lay-filter="stuList">
                </table>
            </div>
        </div>
        <div class="dialog-btn">
            <button class="dialog-cancel" id="btnSelCancel">取消</button>
            <button class="dialog-submit" id="btnSelSure">确定
            </button>
        </div>
    </div>
</div>
</body>

<script th:src="@{~/js/jquery-3.3.1.min.js}"></script>
<script th:src="@{~/plugin/layui/layui2.8.12.js}"></script>
<script th:src="@{~/js/creditManage/common.js}"></script>
<script th:src="@{~/js/my.util.js}"></script>

<script th:inline="javascript">
    var fid = [[${fid}]];
    var alias = [[${alias}]];
    var formId = [[${formId}]];
    //隐藏弹窗
    $(".downloadTemplate .pu-cancel").click(function () {
        window.parent.postMessage(JSON.stringify({action: 0}), '*')
    })

    $(function () {

        // 下拉
        $(".single-box .schoolSel").on("click", function (e) {
            $(".j-select").hide();
            $(".j-select-year").removeClass("slideShow");
            $(".j-arrow").removeClass("j-arrow-slide");
            let parent = $(this).parent();
            parent.find(".j-arrow").toggleClass("j-arrow-slide");
            parent.find(".j-select-year").toggleClass("slideShow");
            e.stopPropagation();
        });
        $(".single-box .j-select-year").on("click", "ul li", function () {
            $(this).addClass("active").siblings().removeClass("active");
            let txt;

            if ($(this).find(".tips").length > 0) {
                txt = $(this).find("span").text();
            } else {
                txt = $(this).text();
            }
            let parent = $(this).parents(".j-search-con");
            parent.find(".schoolSel").val(txt.trim());
            parent.find(".j-arrow").removeClass("j-arrow-slide");
            $(this).parents(".j-select-year").removeClass("slideShow");
        });

        // 点击页面其他地方消失
        $(document).on("click", function (e) {
            if (
                $(e.target).closest(".j-select").length > 0 ||
                $(e.target).closest(".j-select-year").length > 0 ||
                $(e.target).closest(".j-search-vague").length > 0 ||
                $(e.target).closest(".j-select-list").length > 0
            ) {
                // alert('弹出框内部被点击了');
            } else {
                $(".j-select").hide();
                $(".j-select-year").removeClass("slideShow");
                $(".j-arrow").removeClass("j-arrow-slide");
                $(".j-select-list").hide();
            }
        });
    });



    function getClasses(){
        if ($("#classes").html()!=''){
            return;
        }
        $.get("/basic/classes", {fid: fid}, function (res) {
            var html = "";
            if (res.code == 200) {
                var data = res.data;
                for (let i = 0; i < data.length; i++) {
                    html += '<li data-value="' + data[i].bjxx_bjbh + '">' + data[i].bjxx_bjmc + '</li>'
                }
            } else {
                U.fail("班级信息异常")
            }
            $("#classes").html(html);
        })
    }

    layui.use(["table", "jquery", "form", 'laypage', 'layer'], function () {
        var form = layui.form;
        var table = layui.table;
        var $ = layui.jquery;

        $(function () {
            updateTable();
            $.get("/new/credit/search/bar2", {}, function (res) {
                if (res.code == 200) {
                    var result = res.data;
                    var grade = result.grade == null ? [] : result.grade;
                    var depth = result.depth == null ? [] : result.depth;
                    var stuMajor = result.stuMajor == null ? [] : result.stuMajor;
                    var stuClass = result.stuClass == null ? [] : result.stuClass;
                    var stuName = result.stuName == null ? [] : result.stuName;
                    var stuXh = result.stuXh == null ? [] : result.stuXh;
                    var gradeHtml = "<option value=\"\">请选择</option>";
                    for (let i = 0; i < grade.length; i++) {
                        gradeHtml += '<option data-value="' + grade[i] + '">' + grade[i] + '</option>'
                    }
                    var depthHtml = "<option value=\"\">请选择</option>";
                    for (let i = 0; i < depth.length; i++) {
                        depthHtml += "<option value=\"" + depth[i] + "\">" + depth[i] + "</option>";
                    }
                    var majorHtml = "<option value=\"\">请选择</option>";
                    for (let i = 0; i < stuMajor.length; i++) {
                        majorHtml += "<option value=\"" + stuMajor[i] + "\">" + stuMajor[i] + "</option>";
                    }
                    var classHtml = "<option value=\"\">请选择</option>";
                    for (let i = 0; i < stuClass.length; i++) {
                        classHtml += "<option value=\"" + stuClass[i] + "\">" + stuClass[i] + "</option>";
                    }

                    var nameHtml = "<option value=\"\">请选择</option>";
                    for (let i = 0; i < stuName.length; i++) {
                        nameHtml += "<option value=\"" + stuName[i] + "\">" + stuName[i] + "</option>";
                    }
                    var xhHtml = "<option value=\"\">请选择</option>";
                    for (let i = 0; i < stuXh.length; i++) {
                        xhHtml += "<option value=\"" + stuXh[i] + "\">" + stuXh[i] + "</option>";
                    }

                    $(".grade").html(gradeHtml);
                    $(".depth").html(depthHtml);
                    $(".stuMajor").html(majorHtml);
                    $(".stuClass").html(classHtml);
                    $(".stuName").html(nameHtml);
                    $(".stuXh").html(xhHtml);

                    form.render("select", "formStu");
                }
            })

        })

        // 查询
        $("#search3").click(function () {
            // 提交事件
            updateTable();
            return false;
        })
        // 重置
        $("#reset3").click(function () {
            setTimeout(function () {
                updateTable();
            }, 50)
        })

        function updateTable() {
            var formValue = form.val('formStu');
            var param = {
                fid: fid,
                nj: formValue.grade,
                yx: formValue.depth,
                zy: formValue.stuMajor,
                bj: formValue.stuClass,
                xm: formValue.xsxm,
                xh: formValue.xsxh,
                bzruid: formValue.bzruid
            }

            table.render({
                elem: '#stuList',
                url: '/basic/getStudent', //数据接口
                where: param,
                parseData: function (res) {
                    if (res.code == 200) {
                        return {
                            "code": 0, //解析接口状态
                            "msg": res.msg, //解析提示文本
                            "count": res.data.totalCount, //解析数据长度
                            "data": res.data.data //解析数据列表
                        }
                    } else {
                        return {
                            "code": 1, //解析接口状态
                            "msg": "数据获取失败", //解析提示文本
                            "count": 0, //解析数据长度
                            "data": [] //解析数据列表
                        }
                    }
                },
                cols: [
                    [
                        {
                            type: 'checkbox',
                            width: 70,
                            fixed: 'center',
                        },
                        {
                            field: 'xsjbxx_xm',
                            title: '学生姓名',
                            align: 'center',
                            minWidth: 110
                        }, {
                        field: 'xsjbxx_xh',
                        title: '学生学号',
                        align: 'center',
                        minWidth: 100
                    }, {
                        field: 'xsjbxx_sznj',
                        title: '年级',
                        align: 'center',
                        minWidth: 80
                    }, {
                        field: 'xsjbxx_yxxx',
                        title: '系部',
                        align: 'center',
                        minWidth: 110
                    }, {
                        field: 'xsjbxx_zyxx',
                        title: '专业',
                        align: 'center',
                        minWidth: 110
                    }, {
                        field: 'xsjbxx_bjxx',
                        title: '班级',
                        align: 'center',
                        minWidth: 80
                    }, {
                        field: 'xsjbxx_xssfzx',
                        title: '是否在校',
                        align: 'center',
                        minWidth: 80
                    }, {
                        field: 'xsjbxx_xsdqzt',
                        title: '当前状态',
                        align: 'center',
                        minWidth: 80
                    }
                    ]
                ],
                page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip'],
                    curr: 1
                },
                limit: 10,
                done:function (res){
                    layui.each(res.data, function(index,item){
                        stuData.forEach(data=>{
                            if( data==item.rowInfo.formUserId){
                                $("div[lay-id='stuList'] td .layui-form-checkbox").eq(index).click();
                            }
                        })
                    });
                }
            });
        }

        $(".dialog-btn button,.dialog-close").click(function () {
            $(".downloadTemplate").show();
            $(".dialog-wrap").hide();
        })

        $("#btnSelSure").click(function () {
            $("#stuApply").val("已勾选 "+stuData.size+" 名学生")
        })

        var type=0;
        $("#clazz").click(function () {
            stuData= new Set()
            $("#stuApply").val("")
            updateTable();
            type=1;
            getClasses();
            $(".level3").addClass("hide")
            $(".level2").removeClass("hide")
        })
        $("#stu").click(function () {
            classData=[]
            $("#classes li").each((index,item)=>{
                $(item).removeClass("active")
            })
            $("input[name=className]").val("")

            type=2;
            $(".level2").addClass("hide")
            $(".level3").removeClass("hide")
        })

        $("#stuApply").click(function () {
            $(".downloadTemplate").hide();
            $(".dialog-wrap").show();
        })

        var stuData = new Set();
        table.on('checkbox(stuList)', function (obj) {
            if (obj.checked) {
                // 选中
                if (obj.type == 'one') {
                    stuData.add(obj.data.rowInfo.formUserId)
                } else {
                    table.checkStatus('stuList').data.forEach(element => {
                        stuData.add(element.rowInfo.formUserId)
                    });
                }
            } else {
                // 取消选中
                if (obj.type == 'one') {
                    stuData.delete(obj.data.rowInfo.formUserId)
                } else {
                    table.getData("stuList").forEach(element => {
                        stuData.delete(element.rowInfo.formUserId)
                    });
                }
            }
        });

        var classData=[];
        $("ul#classes").on("click","li",function (){
            var bjbh = $(this).attr("data-value");
            if ($(this).hasClass("active")){
                classData=  classData.filter(item=>item!==bjbh)
            }else {
                classData.push(bjbh)
            }
        })

        $(".downloadTemplate .pu-sure").click(function () {
            if (type==0){
                U.fail("请选择具体数据");
                return false;
            }
            $.post("/new/credit/exportTemplate", {
                fid: fid,
                formId: formId,
                alias: alias,
                type:type,
                classData:classData.join(","),
                stuData:[...stuData].join(",")
            }, function (res) {
                if (res.code == 200) {
                    U.success("请稍后在下载记录中查看")
                } else {
                    U.fail(res.msg)
                }
            })
        })
    })
</script>

</html>