<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>导出教学任务书</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/global.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/layui/css/layui.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/slideCommon.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/teachPlan.css'}"/>
    <style>
        .error-tips {
            color: #f56c6c;
            font-size: 12px;
            margin-top: 5px;
            display: none;
            position: relative;
            padding-left: 20px;
        }
        
        .error-tips::before {
            content: "!";
            position: absolute;
            left: 0;
            top: 0;
            width: 16px;
            height: 16px;
            background-color: #f56c6c;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 16px;
            font-weight: bold;
        }
        
        .error-tips.visible {
            display: block;
        }

        #loadingDialog {
            text-align: center;
            padding: 20px;
        }

        #loadingDialog .loading {
            width: 50px;
            height: 50px;
            animation: rotate 1.5s linear infinite;
            margin: 20px auto;
            display: block;
        }
        
        #loadingDialog p {
            margin-top: 15px;
            color: #606266;
            font-size: 14px;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        @keyframes pulse {
            0% { opacity: 0.6; }
            50% { opacity: 1; }
            100% { opacity: 0.6; }
        }

        /* 优化对齐样式 */
        .item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .item .label {
            width: 140px;
            text-align: right;
            padding-right: 15px;
            flex-shrink: 0;
        }
        
        .item .label.required::after {
            content: "*";
            color: #f56c6c;
            margin-left: 4px;
        }
        
        .schoolSel.error {
            border-color: #f56c6c;
        }

        .item .j-search-con {
            flex: 1;
            position: relative;
        }

        .error-tips {
            margin-left: 140px;
        }

        /* 弹窗大小设置 */
        .dialog {
            width: 500px;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
            display: none;
        }

        /* 下拉框样式优化 */
        .j-select-year {
            position: absolute;
            width: 100%;
            background: white;
            border: 1px solid #e4e7ed;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
            z-index: 100;
            max-height: 250px;
            display: none;
        }
        
        .j-select-year ul {
            max-height: 200px;
            overflow-y: auto;
            margin: 0;
            padding: 5px 0;
            list-style: none;
        }
        
        .schoolSel {
            width: 100%;
            padding: 8px 10px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .schoolSel.error {
            border-color: #f56c6c;
            background-color: #fff0f0;
        }
        
        .loading-item {
            text-align: center;
            color: #909399;
            padding: 10px 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .loading-spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #409eff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .no-data {
            text-align: center;
            color: #909399;
            padding: 10px 0;
        }
        
        .marker {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999;
            display: none;
        }
        
        .dialog {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1000;
            background-color: #fff;
        }
    </style>
</head>

<body>
<div class="marker"></div>

<!-- 导出教学任务书对话框 -->
<div class="dialog" id="teachPlanDialog">
    <div class="dialog-con">
        <!-- 学年学期选择 -->
        <div class="item">
            <div class="label required">选择导出学年学期</div>
            <div class="j-search-con single-box">
                <input type="text" name="termSel" placeholder="请选择" readonly class="schoolSel" id="termSelect">
                <span class="j-arrow"></span>
                <div class="j-select-year">
                    <div class="search">
                        <input type="text" placeholder="搜索" id="termSearchInput">
                        <span></span>
                    </div>
                    <ul name="term"></ul>
                </div>
            </div>
        </div>
        <p class="error-tips" id="termError">请选择学年学期</p>

        <!-- 授课教师选择 -->
        <div class="item">
            <div class="label required">选择授课教师</div>
            <div class="j-search-con single-box">
                <input type="text" name="teacherSel" placeholder="请选择" readonly class="schoolSel" id="teacherSelect">
                <span class="j-arrow"></span>
                <div class="j-select-year">
                    <div class="search">
                        <input type="text" placeholder="搜索" id="teacherSearchInput">
                        <span></span>
                    </div>
                    <ul name="teacher"></ul>
                </div>
            </div>
        </div>
        <p class="error-tips" id="teacherError">请选择授课教师</p>
    </div>
    <div class="dialog-footer">
        <button class="btn-cancel" id="cancelBtn">取消</button>
        <button class="btn-sure" id="confirmBtn">确定</button>
    </div>
</div>

<!-- 加载中对话框 -->
<div class="dialog" id="loadingDialog">
    <div class="dialog-con" style="text-align: center;">
        <div style="padding: 30px 20px;">
            <img class="loading" th:src="${_CPR_+_VR_+'/images/cultivation/loading.png'}" alt="加载中">
            <p style="animation: pulse 1.5s infinite;">教学任务书生成中，请稍后至导出记录中查看</p>
        </div>
    </div>
</div>
</body>

<script th:src="${_CPR_+_VR_+'/plugin/layui/layui.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/cultivation/slideCommon.js'}"></script>
<script th:inline="javascript">
    // 常量和配置
    const _VR_ = /*[[${_VR_}]]*/ '';
    const uid = /*[[${formTopBtnVO.uid}]]*/ '';
    const fid = /*[[${formTopBtnVO.fid}]]*/ '';
    const queryId = /*[[${formTopBtnVO.queryId}]]*/ '';
    const formId = /*[[${formTopBtnVO.formId}]]*/ '';
    const enc = /*[[${enc}]]*/ '';

    // DOM 元素
    const $teachPlanDialog = document.getElementById('teachPlanDialog');
    const $loadingDialog = document.getElementById('loadingDialog');
    const $termSelect = document.getElementById('termSelect');
    const $teacherSelect = document.getElementById('teacherSelect');
    const $termError = document.getElementById('termError');
    const $teacherError = document.getElementById('teacherError');
    const $confirmBtn = document.getElementById('confirmBtn');
    const $marker = document.querySelector('.marker');

    // 初始化
    document.addEventListener('DOMContentLoaded', function() {
        initEventListeners();
        $confirmBtn.disabled = false;
        showDialog($teachPlanDialog);
    });

    // 显示对话框
    function showDialog(dialog) {
        $marker.style.display = 'block';
        dialog.style.display = 'block';
    }

    // 初始化事件监听
    function initEventListeners() {
        // 下拉框点击事件
        document.querySelectorAll('.schoolSel').forEach(function(element) {
            element.addEventListener('click', handleSelectClick);
        });

        // 搜索框输入事件
        document.getElementById('termSearchInput').addEventListener('input', function() {
            filterOptions('term', this.value);
        });

        document.getElementById('teacherSearchInput').addEventListener('input', function() {
            filterOptions('teacher', this.value);
        });

        // 确定按钮点击事件
        $confirmBtn.addEventListener('click', handleConfirm);

        // 取消和关闭按钮点击事件
        document.querySelectorAll('.btn-cancel, .close-btn').forEach(function(element) {
            element.addEventListener('click', hideDialogs);
        });
        
        // 点击外部关闭下拉框
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.j-search-con')) {
                hideDropdowns();
            }
        });
        
        // 按ESC键关闭弹窗
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                hideDialogs();
            }
        });
    }
    
    // 隐藏所有下拉框
    function hideDropdowns() {
        document.querySelectorAll('.j-select-year').forEach(function(dropdown) {
            dropdown.style.display = 'none';
        });
    }

    // 处理下拉框点击
    function handleSelectClick(e) {
        e.stopPropagation();
        
        const parent = this.parentElement;
        const selectYear = parent.querySelector('.j-select-year');
        const ulElement = parent.querySelector('ul');
        const name = ulElement.getAttribute('name');
        
        // 如果当前下拉框已显示，则隐藏它
        if (selectYear.style.display === 'block') {
            selectYear.style.display = 'none';
            return;
        }
        
        // 隐藏所有下拉框，显示当前下拉框
        hideDropdowns();
        selectYear.style.display = 'block';
        
        // 如果没有选项，加载数据
        if (ulElement.children.length === 0) {
            showLoading(ulElement);
            loadData(name);
        }
        
        // 聚焦搜索框
        focusSearchInput(selectYear);
    }
    
    // 显示加载中状态
    function showLoading(element) {
        element.innerHTML = '<li class="loading-item"><span class="loading-spinner"></span>加载中...</li>';
    }
    
    // 聚焦搜索输入框
    function focusSearchInput(container) {
        const searchInput = container.querySelector('input');
        if (searchInput) {
            setTimeout(() => searchInput.focus(), 100);
        }
    }
    
    // 根据类型加载数据
    function loadData(type) {
        if (type === 'teacher') {
            loadTeacherData();
        } else if (type === 'term') {
            loadTermData();
        }
    }

    // 加载教师数据
    function loadTeacherData() {
        const teacherUl = document.querySelector('ul[name="teacher"]');
        
        fetch(`${_VR_}/teacherIdle/getTeacherInfoData`, {
            method: 'POST',
            headers: {'Content-Type': 'application/x-www-form-urlencoded'}
        })
        .then(response => response.json())
        .then(data => {
            const hasData = data && data.data && data.data.length > 0;
            if (hasData) {
                renderOptions('teacher', data);
            } else {
                showNoData(teacherUl);
            }
        })
        .catch(error => {
            console.error('加载教师数据失败:', error);
            showLoadError(teacherUl);
        });
    }

    // 加载学期数据
    function loadTermData() {
        const termUl = document.querySelector('ul[name="term"]');
        
        fetch(`${_VR_}/teacherIdle/getFormDistinctFiled`, {
            method: 'POST',
            headers: {'Content-Type': 'application/x-www-form-urlencoded'},
            body: new URLSearchParams({
                formAlias: 'xnxq',
                fieldAlias: 'xnxq_xnxqh'
            })
        })
        .then(response => response.json())
        .then(data => {
            const hasData = data && data.list && data.list.length > 0;
            if (hasData) {
                renderOptions('term', data);
            } else {
                showNoData(termUl);
            }
        })
        .catch(error => {
            console.error('加载学期数据失败:', error);
            showLoadError(termUl);
        });
    }
    
    // 显示无数据提示
    function showNoData(element) {
        element.innerHTML = '<li class="no-data">暂无数据</li>';
    }
    
    // 显示加载错误提示
    function showLoadError(element) {
        element.innerHTML = '<li class="no-data">加载失败，请重试</li>';
    }

    // 渲染选项
    function renderOptions(name, data) {
        const ulElement = document.querySelector(`ul[name="${name}"]`);
        if (!ulElement) return;
        
        // 生成HTML
        const html = generateOptionsHtml(name, data);
        
        if (!html) {
            showNoData(ulElement);
            return;
        }

        ulElement.innerHTML = html;
        
        // 添加选项点击事件
        attachOptionClickEvents(ulElement, name);
    }
    
    // 生成选项HTML
    function generateOptionsHtml(name, data) {
        if (name === 'teacher') {
            return generateTeacherOptionsHtml(data);
        } else if (name === 'term') {
            return generateTermOptionsHtml(data);
        }
        return '';
    }
    
    // 生成教师选项HTML
    function generateTeacherOptionsHtml(data) {
        if (!data.data || !Array.isArray(data.data) || data.data.length === 0) {
            return '';
        }
        
        // 按姓名排序
        const sortedData = [...data.data].sort((a, b) => 
            a.jsjbxx_xm.localeCompare(b.jsjbxx_xm, 'zh-CN')
        );
        
        return sortedData
            .filter(item => item.jsjbxx_xmlxr && item.jsjbxx_xmlxr.puid && item.jsjbxx_xm)
            .map(item => `<li data-uid="${item.jsjbxx_xmlxr.puid}">${item.jsjbxx_xm}</li>`)
            .join('');
    }
    
    // 生成学期选项HTML
    function generateTermOptionsHtml(data) {
        if (!data.list || !Array.isArray(data.list) || data.list.length === 0) {
            return '';
        }
        
        // 按学期降序排列，最新学期在前
        const sortedList = [...data.list].sort((a, b) => 
            String(b).localeCompare(String(a))
        );
        
        return sortedList
            .map(item => `<li>${item}</li>`)
            .join('');
    }
    
    // 添加选项点击事件
    function attachOptionClickEvents(container, name) {
        container.querySelectorAll('li').forEach(li => {
            // 跳过特殊项
            if (li.classList.contains('no-data') || li.classList.contains('loading-item')) {
                return;
            }
            
            li.addEventListener('click', function(e) {
                handleOptionClick(e, this, name);
            });
        });
    }
    
    // 处理选项点击
    function handleOptionClick(e, element, name) {
        e.stopPropagation();
        
        const input = element.closest('.j-search-con').querySelector('.schoolSel');
        input.value = element.textContent;
        
        // 如果是教师，保存uid
        if (name === 'teacher' && element.dataset.uid) {
            input.dataset.uid = element.dataset.uid;
        }
        
        // 隐藏下拉框
        element.closest('.j-select-year').style.display = 'none';
        
        // 移除错误状态
        input.classList.remove('error');
        
        // 隐藏错误提示
        const errorElement = name === 'term' ? $termError : $teacherError;
        errorElement.classList.remove('visible');
    }

    // 过滤选项
    function filterOptions(name, keyword) {
        const ulElement = document.querySelector(`ul[name="${name}"]`);
        if (!ulElement) return;

        const lowercaseKeyword = keyword.toLowerCase();
        let hasMatches = false;
        
        // 过滤项目
        ulElement.querySelectorAll('li').forEach(item => {
            // 跳过特殊项
            if (item.classList.contains('no-data') || item.classList.contains('loading-item')) {
                return;
            }
            
            const text = item.textContent.toLowerCase();
            const isMatch = text.includes(lowercaseKeyword);
            
            item.style.display = isMatch ? '' : 'none';
            if (isMatch) hasMatches = true;
        });
        
        // 更新无匹配结果提示
        updateNoMatchMessage(ulElement, hasMatches, keyword);
    }
    
    // 更新无匹配结果提示
    function updateNoMatchMessage(container, hasMatches, keyword) {
        let noMatchElem = container.querySelector('.no-match');
        
        // 如果没有匹配且有关键词，显示无匹配提示
        if (!hasMatches && keyword) {
            if (!noMatchElem) {
                noMatchElem = document.createElement('li');
                noMatchElem.className = 'no-data no-match';
                noMatchElem.textContent = '无匹配结果';
                container.appendChild(noMatchElem);
            } else {
                noMatchElem.style.display = '';
            }
        } else if (noMatchElem) {
            noMatchElem.style.display = 'none';
        }
    }

    // 处理确认按钮点击
    function handleConfirm() {
        // 验证表单
        if (!validateFormWithFeedback()) {
            return;
        }

        // 显示加载对话框
        $teachPlanDialog.style.display = 'none';
        $loadingDialog.style.display = 'block';

        // 提交导出请求
        submitExportRequest();
    }

    // 带反馈的表单验证
    function validateFormWithFeedback() {
        const termValid = validateField($termSelect, $termError);
        const teacherValid = validateField($teacherSelect, $teacherError);

        const isValid = termValid && teacherValid;

        if (!isValid) {
            // 添加抖动效果
            addShakeEffect();
        }

        return isValid;
    }
    
    // 验证单个字段
    function validateField(inputElement, errorElement) {
        const isValid = !!inputElement.value;
        
        if (isValid) {
            errorElement.classList.remove('visible');
            inputElement.classList.remove('error');
        } else {
            errorElement.classList.add('visible');
            inputElement.classList.add('error');
        }
        
        return isValid;
    }
    
    // 添加抖动效果
    function addShakeEffect() {
        document.querySelectorAll('.schoolSel.error').forEach(input => {
            input.animate([
                { transform: 'translateX(-5px)' },
                { transform: 'translateX(5px)' },
                { transform: 'translateX(-5px)' },
                { transform: 'translateX(5px)' },
                { transform: 'translateX(0)' }
            ], {
                duration: 400,
                easing: 'ease-in-out'
            });
        });
    }
    
    // 提交导出请求
    function submitExportRequest() {
        fetch(`${_VR_}/api/form/cultivation/topBtn/exportTeachTaskBook19489`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: new URLSearchParams({
                uid: uid,
                fid: fid,
                queryId: queryId,
                formId: formId,
                enc: enc,
                term: $termSelect.value,
                teacher: $teacherSelect.dataset.uid || $teacherSelect.value
            })
        })
        .then(response => response.json())
        .then(data => {
            console.log('导出请求已提交');
            // 可以在这里添加成功提示
        })
        .catch(error => {
            console.error('导出请求失败:', error);
            alert('导出请求失败，请重试');
            hideDialogs();
        });
    }

    // 隐藏所有对话框
    function hideDialogs() {
        document.querySelectorAll('.dialog').forEach(function(dialog) {
            dialog.style.display = 'none';
        });
        document.querySelector('.marker').style.display = 'none';
    }
</script>
</html>