<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>学生选课</title>
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/global.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/layui/css/layui.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/stuSelCourse3.0.css?v=5'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/resetV3.css'">
</head>

<body>
<div class="z-main">
    <div class="z-title">
        <div class="left">
            <h3>学生选课</h3>
<!--            <div class="j-search-con single-box">-->
<!--                <input type="text" name="teacherName" id="xnxqVal" placeholder="请选择" readonly="" class="schoolSel">-->
<!--                <span class="j-arrow"></span>-->
<!--                <div class="j-select-year">-->
<!--                    <ul name="teacherName" id="pcIndeXxnxqDiv">-->
<!--                    </ul>-->
<!--                </div>-->
<!--            </div>-->
        </div>
<!--        <ul id="selType">-->
<!--            <li class="active">课表模式选课</li>-->
<!--            <li>列表模式选课</li>-->
<!--        </ul>-->
    </div>
    <div class="z-title">
        <div class="z-item-wrap">
            <div class="z-item">
                <div class="item-title">选课学期</div>
                <div class="j-search-con single-box" style="width: 240px;margin-left:14px;">
                    <input type="text" name="teacherName" placeholder="请选择" id="xnxqVal" readonly="" class="schoolSel">
                    <span class="j-arrow"></span>
                    <div class="j-select-year">
                        <ul name="teacherName" id="pcIndeXxnxqDiv">
<!--                            <li data-id="2024-2025-2" class="all ">2024-2025-2</li>-->
<!--                            <li data-id="2024-2025-1" class="all active">2024-2025-1</li>-->
<!--                            <li data-id="2023-2024-2" class="all ">2023-2024-2</li>-->
<!--                            <li data-id="2023-2024-1" class="all ">2023秋季学期</li>-->
<!--                            <li data-id="2022-2023-2" class="all ">2022-2023-2</li>-->
<!--                            <li data-id="2022-2023-2" class="all ">2022春季学期</li>-->
<!--                            <li data-id="2022-2023-1" class="all ">2022-2023-1</li>-->
<!--                            <li data-id="2021-2022-2" class="all ">2021-2022-2</li>-->
<!--                            <li data-id="2021-2022-1" class="all ">2021-2022-1</li>-->
<!--                            <li data-id="2020-2021-2" class="all ">2020-2021-2</li>-->
<!--                            <li data-id="2020-2021-1" class="all ">2020-2021-1</li>-->
<!--                            <li data-id="2019-2020-2" class="all ">2019-2020-2</li>-->
<!--                            <li data-id="2019-2020-1" class="all ">2019-2020-1</li>-->
<!--                            <li data-id="2018-2019-2" class="all ">2018-2019-2</li>-->
<!--                            <li data-id="2018-2019-1" class="all ">2018-2019-1</li>-->
<!--                            <li data-id="2017-2018-2" class="all ">2017-2018-2</li>-->
<!--                            <li data-id="2017-2018-1" class="all ">2017-2018-1</li>-->
<!--                            <li data-id="2016-2017-2" class="all ">2016-2017-2</li>-->
<!--                            <li data-id="2016-2017-1" class="all ">2016-2017-1</li>-->
<!--                            <li data-id="2015-2016-2" class="all ">2015-2016-2</li>-->
<!--                            <li data-id="2015-2016-1" class="all ">2015-2016-1</li>-->
                        </ul>
                    </div>
                </div>
            </div>
            <div class="z-item">
                <div class="item-title">选课计划</div>
                <div class="j-search-con single-box" style="width: 240px;margin-left:14px;">
                    <input type="text" name="plan" placeholder="请选择" id="jhval" readonly="" class="schoolSel">
                    <span class="j-arrow"></span>
                    <div class="j-select-year">
                        <ul name="plan" class="select-course" id="pcIndejhDiv">
<!--                            <li class="not-finished">测试不在校学生</li>-->
<!--                            <li class="not-finished">测试选课对象的uid123</li>-->
<!--                            <li class="not-finished">9.13测试测试测试</li>-->
<!--                            <li class="finished">测试选课上限234</li>-->
<!--                            <li class="finished">9.20测试面向对象</li>-->
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="main-con" id="Curriculum" style="display: none">
        <ul class="category">
            <li><span></span>未选课课程</li>
            <li><span></span>已选课课程</li>
        </ul>
        <div class="selectBox">
            <div class="selectWeek">
                <div class="prevWeek disabled"></div>
                <span class="week" week="1">第<strong>01</strong>周</span>
                <div class="nextWeek"></div>
            </div>
            <div class="selectMask">
                <ul class="selectList"></ul>
            </div>
        </div>
        <div class="z-table">
            <table>
                <thead class="thead">
                <tr>
                    <td><span class="weekdate"></span><span class="week"></span></td>
                    <td>
                        <span class="weekdate" id="week1"></span><span class="week">周一</span>
                    </td>
                    <td>
                        <span class="weekdate" id="week2"></span><span class="week">周二</span>
                    </td>
                    <td>
                        <span class="weekdate" id="week3"></span><span class="week">周三</span>
                    </td>
                    <td>
                        <span class="weekdate" id="week4"></span><span class="week">周四</span>
                    </td>
                    <td>
                        <span class="weekdate" id="week5"></span><span class="week">周五</span>
                    </td>
                    <td>
                        <span class="weekdate" id="week6"></span><span class="week">周六</span>
                    </td>
                    <td>
                        <span class="weekdate" id="week7"></span><span class="week">周日</span>
                    </td>
                </tr>
                </thead>
                <tbody class="tbody">


                </tbody>
            </table>
            <div class="total-sel-course">本周共<span id="dxknum">0</span>节待选课</div>
        </div>
    </div>

<!--    <div class="main-con" id="list" style="display: none;">-->
    <div class="main-con" id="list">
        <div class="z-search" style="padding: 24px 30px 8px;">
            <div class="z-item-wrap">
                <div class="z-item">
                    <div class="item-title">开课年级</div>
                    <div class="j-search-con multiple-box">
                        <input type="text" name="grade" id="njVal2" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <div class="all-selects">全选</div>
                            <ul name="grade" id="njDiv2">
                            </ul>
                        </div>
                    </div>

                </div>
                <div class="z-item">
                    <div class="item-title">开课院系</div>
                    <div class="j-search-con multiple-box">
                        <input type="text" name="depart" id="yxVal2" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <div class="all-selects">全选</div>
                            <ul name="depart" id="yxDiv2">
                            </ul>
                        </div>
                    </div>

                </div>
                <div class="z-item">
                    <div class="item-title">开课专业</div>
                    <div class="j-search-con multiple-box">
                        <input type="text" name="major" id="zyVal2" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <div class="all-selects">全选</div>
                            <ul name="major" id="zyDiv2">
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="z-item">
                    <div class="item-title">开课课程</div>
                    <div class="j-search-con multiple-box">
                        <input type="text" name="course" id="kcVal2" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <div class="all-selects">全选</div>
                            <ul name="course" id="kcDiv2">
                            </ul>
                        </div>
                    </div>

                </div>
                <div class="z-item">
                    <div class="item-title">开课校区</div>
                    <div class="j-search-con multiple-box">
                        <input type="text" name="school" id="xqVal2" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <div class="all-selects">全选</div>
                            <ul name="school" id="xqDiv2">
                            </ul>
                        </div>
                    </div>

                </div>
                <div class="z-item">
                    <div class="item-title">课程类型</div>
                    <div class="j-search-con multiple-box">
                        <input type="text" name="type" id="lxVal2" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <div class="all-selects">全选</div>
                            <ul name="type" id="lxDiv2">
                            </ul>
                        </div>
                    </div>

                </div>
<!--                <div class="z-item">-->
<!--                    <div class="item-title">所属选课计划</div>-->
<!--                    <div class="j-search-con multiple-box">-->
<!--                        <input type="text" name="plan" id="jhVal2" placeholder="请选择" readonly="" class="schoolSel">-->
<!--                        <span class="j-arrow"></span>-->
<!--                        <div class="j-select-year">-->
<!--                            <div class="search">-->
<!--                                <input type="text" placeholder="搜索">-->
<!--                                <span></span>-->
<!--                            </div>-->
<!--                            <div class="all-selects">全选</div>-->
<!--                            <ul name="plan" id="jhDiv2">-->
<!--                            </ul>-->
<!--                        </div>-->
<!--                    </div>-->

<!--                </div>-->
                <div class="z-item">
                    <div class="item-title">授课教师</div>
                    <div class="j-search-con multiple-box">
                        <input type="text" name="teacher" id="skjsVal2" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <div class="all-selects">全选</div>
                            <ul name="teacher" id="skjsDiv2">
                            </ul>
                        </div>
                    </div>

                </div>
            </div>
            <div class="search-btn">
                <button type="button" class="btn-search">查询</button>
                <button type="button" class="btn-reset" lay-submit lay-filter="selTable">重置</button>
            </div>
        </div>
        <div class="z-tab-search" style="margin: 0 30px 20px;">
            <div class="z-search-switch">查看已选课程
                <div class="z-switch">
                    <div class="switch" id="courseSwitch"><span></span></div>
                    <span class="witchState">关闭</span>
                </div>
            </div>
            <input type="text" placeholder="搜索选修课" style="display: none"/>
            <img th:src="${_CPR_}+'/elective/images/search-icon1.png'" alt="" style="display: none"/>
        </div>
        <div class="z-list" id="selCourseList">
        </div>
        <div class="page-con">
            <div class="course-total">共<span>0</span>条</div>
            <div id="coursePage1"></div>
        </div>

    </div>
</div>
<!-- 进入提示 -->
<div class="dialog-wrap dialog-plan" style="display: block">
    <div class="dialog">
        <div class="dialog-title">
            <h3>参与的选课计划</h3>
                <span class="dialog-close"></span>
        </div>
        <div class="dialog-con">
            <!--            <div class="plan">-->
            <!--                <h1>选课计划</h1>-->
            <!--                <p>2023-08-04 00:00:00&nbsp;&nbsp;-&nbsp;&nbsp;2023-08-10 00:00:00</p>-->
            <!--                <p>选课说明文字</p>-->
            <!--            </div>-->
            <!--            <div class="plan">-->
            <!--                <h1>选课计划</h1>-->
            <!--                <p>2023-08-04 00:00:00&nbsp;&nbsp;-&nbsp;&nbsp;2023-08-10 00:00:00</p>-->
            <!--                <p>请务必在规定的选课时间内完成课程选择。逾期将无法选课，敬请留意学校活学院发布的选课通知。</p>-->
            <!--            </div>-->
            <!--            <div class="plan">-->
            <!--                <h1>选课计划</h1>-->
            <!--                <p>2023-08-04 00:00:00&nbsp;&nbsp;-&nbsp;&nbsp;2023-08-10 00:00:00</p>-->
            <!--                <p>请务必在规定的选课时间内完成课程选择。逾期将无法选课，敬请留意学校活学院发布的选课通知。</p>-->
            <!--            </div>-->
        </div>
        <div class="dialog-btn">
                <button class="cancel">取消</button>
            <button class="sure" id="planSel">确定</button>
        </div>
    </div>
</div>
<!-- 可选课程 -->
<div class="dialog-wrap dialog-course" style="display: none;">
    <div class="dialog">
        <div class="dialog-title">
            <h3>可选课程</h3>
            <span class="dialog-close"></span>
        </div>
        <div class="dialog-con">
            <div class="z-search">
                <div class="z-item-wrap">
                    <div class="z-item">
                        <div class="item-title">开课年级</div>
                        <div class="j-search-con multiple-box">
                            <input type="text" name="grade" id="njVal1" placeholder="请选择" readonly="" class="schoolSel">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <div class="all-selects">全选</div>
                                <ul name="grade" id="njDiv1">

                                </ul>
                            </div>
                        </div>

                    </div>
                    <div class="z-item">
                        <div class="item-title">开课院系</div>
                        <div class="j-search-con multiple-box">
                            <input type="text" name="depart" id="yxVal1" placeholder="请选择" readonly=""
                                   class="schoolSel">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <div class="all-selects">全选</div>
                                <ul name="depart" id="yxDiv1">

                                </ul>
                            </div>
                        </div>

                    </div>
                    <div class="z-item">
                        <div class="item-title">开课专业</div>
                        <div class="j-search-con multiple-box">
                            <input type="text" name="major" id="zyVal1" placeholder="请选择" readonly="" class="schoolSel">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <div class="all-selects">全选</div>
                                <ul name="major" id="zyDiv1">
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="z-item">
                        <div class="item-title">开课课程</div>
                        <div class="j-search-con multiple-box">
                            <input type="text" name="course" id="kcVal1" placeholder="请选择" readonly=""
                                   class="schoolSel">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <div class="all-selects">全选</div>
                                <ul name="course" id="kcDiv1">
                                </ul>
                            </div>
                        </div>

                    </div>
                    <div class="z-item">
                        <div class="item-title">开课校区</div>
                        <div class="j-search-con multiple-box">
                            <input type="text" name="school" id="xqVal1" placeholder="请选择" readonly=""
                                   class="schoolSel">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <div class="all-selects">全选</div>
                                <ul name="school" id="xqDiv1">
                                </ul>
                            </div>
                        </div>

                    </div>
                    <div class="z-item">
                        <div class="item-title">课程类型</div>
                        <div class="j-search-con multiple-box">
                            <input type="text" name="type" id="lxVal1" placeholder="请选择" readonly="" class="schoolSel">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <div class="all-selects">全选</div>
                                <ul name="type" id="lxDiv1">
                                </ul>
                            </div>
                        </div>

                    </div>
<!--                    <div class="z-item">-->
<!--                        <div class="item-title">所属选课计划</div>-->
<!--                        <div class="j-search-con multiple-box">-->
<!--                            <input type="text" name="plan" id="jhVal1" placeholder="请选择" readonly="" class="schoolSel">-->
<!--                            <span class="j-arrow"></span>-->
<!--                            <div class="j-select-year">-->
<!--                                <div class="search">-->
<!--                                    <input type="text" placeholder="搜索">-->
<!--                                    <span></span>-->
<!--                                </div>-->
<!--                                <div class="all-selects">全选</div>-->
<!--                                <ul name="plan" id="jhDiv1">-->
<!--                                </ul>-->
<!--                            </div>-->
<!--                        </div>-->

<!--                    </div>-->
                    <div class="z-item">
                        <div class="item-title">授课教师</div>
                        <div class="j-search-con multiple-box">
                            <input type="text" name="teacher" id="skjsVal1" placeholder="请选择" readonly=""
                                   class="schoolSel">
                            <span class="j-arrow"></span>
                            <div class="j-select-year">
                                <div class="search">
                                    <input type="text" placeholder="搜索">
                                    <span></span>
                                </div>
                                <div class="all-selects">全选</div>
                                <ul name="teacher" id="skjsDiv1">
                                </ul>
                            </div>
                        </div>

                    </div>
                </div>
                <div class="search-btn">
                    <button type="button" class="btn-search">查询</button>
                    <button type="button" class="btn-reset" lay-submit lay-filter="selTable">重置</button>
                </div>
            </div>
            <div class="z-tab-search" style="display:none;">
                <input type="text" placeholder="搜索选修课"/>
                <img th:src="${_CPR_}+'/elective/images/search-icon1.png'" alt=""/>
            </div>
            <div class="z-list" id="zList" style="min-height: 145px;">


            </div>
            <!--            <div class="page-con">-->
            <!--                <div class="course-total" style="left: 80px;">共<span>4</span>条</div>-->
            <!--                <div id="coursePage"></div>-->
            <!--            </div>-->
        </div>
        <div class="dialog-btn">
            <button class="cancel">取消</button>
            <button class="sure" id="courseSel">确定</button>
        </div>
    </div>
</div>
<!-- 详情 -->
<div class="dialog-wrap dialog-mes" style="display: none;">
    <div class="dialog">
        <div class="dialog-title">
            <h3>22数据库</h3>
            <span class="dialog-close"></span>
        </div>
        <div class="dialog-con">
            <p>
                <span>所属选课计划：</span>测试测试
            </p>
            <p><span>开设课程：</span>数据库</p>
            <p><span>开课年级：</span>2022级</p>
            <p><span>开课院系：</span>计算机学院</p>
            <p><span>开课专业：</span>计算机专业</p>
            <p><span>课程类型：</span>专业选修课</p>
            <p><span>上课时间：</span>1-20周，周一，第七节</p>
            <p><span>上课地点 ：</span>南校区</p>
            <p><span>授课教师 ：</span>李四</p>
            <p><span>学分 ：</span>2分</p>
            <p><span>课程容量：</span>50</p>
            <p><span>性别：</span>男</p>
            <p><span>编组：</span>第5梯队</p>
        </div>
        <div class="dialog-btn">
            <button class="cancel">取消</button>
            <button class="sure" id="exitCourse">退课</button>
            <button class="sure" id="cancelCourse" style="display: none;">确定</button>
        </div>
    </div>
</div>
<div id="tipsBox"></div>
<div id="captcha"></div>
</body>
<script th:inline="javascript">
    const _VR_ = [[${_VR_}]] || '';
</script>
<script th:src="${_CPR_}+'/elective/layui/layui.js'"></script>
<script th:src="${_CPR_}+'/elective/js/jquery-3.3.1.min.js'"></script>
<script th:src="${_CPR_}+'/elective/js/commonV3.js'"></script>
<script type='text/javascript' src='https://captcha.chaoxing.com/load.min.js?t='></script>
<script th:src="${_CPR_}+'/js/my.util.js'"></script>
<script th:inline="javascript">
    layui.use(['laypage', 'jquery', 'layer', 'layedit'], function () {
        var laypage = layui.laypage, $ = layui.jquery, layedit = layui.layedit, layer = layui.layer;

        $(document).ready(function () {
            var currentDate = new Date();
            var currentMonth = currentDate.getMonth() + 1;
            var currentDay = currentDate.getDate();
            var finaltaskBdid = 0;
            var finaltaskcid = 0;
            var finalzx = false;
            var opCourse = 1;
            var captchaIns = null;
            var listtab = 1;
            listtab = 2;
            var yzm = false;
            let r = [[${r}]];
            let fidEnc = [[${fidEnc}]];
            let academicYear = [[${academicYear}]];
            let dates = [[${dates}]];
            var xnxqh = "";
            let fid = [[${fid}]];
            var zc = 1;
            var stuOptional;
            var stuSelectCourseCid = [];
            var stuAppluSelectCourseCid = [];
            var jcArr = [];
            var curPage = 1;
            var pageSize = 10;
            var thisxkjhid = '';
            if (academicYear) {
                xnxqh = academicYear.xnxq_xnxqh;
                if (academicYear.xnxq_xnxqmc) {
                    $("#xnxqVal").val(academicYear.xnxq_xnxqmc);
                } else {
                    $("#xnxqVal").val(academicYear.xnxq_xnxqh);
                }
            }
            let allAcademicYear = [[${allAcademicYear}]];
            if (allAcademicYear) {
                var html = "";
                for (let i = 0; i < allAcademicYear.length; i++) {
                    if (academicYear && academicYear.xnxq_xnxqmc && academicYear.xnxq_xnxqmc == allAcademicYear[i].xnxq_xnxqmc) {
                        html += "<li data-id='" + allAcademicYear[i].xnxq_xnxqh + "' class='all active'>" + allAcademicYear[i].xnxq_xnxqmc + "</li>";
                    } else if (academicYear && academicYear.xnxq_xnxqh && academicYear.xnxq_xnxqh == allAcademicYear[i].xnxq_xnxqh) {
                        html += "<li data-id='" + allAcademicYear[i].xnxq_xnxqh + "' class='all active'>" + allAcademicYear[i].xnxq_xnxqh + "</li>";
                    } else if (allAcademicYear[i].xnxq_xnxqmc) {
                        html += "<li data-id='" + allAcademicYear[i].xnxq_xnxqh + "' class='all '>" + allAcademicYear[i].xnxq_xnxqmc + "</li>";
                    } else {
                        html += "<li data-id='" + allAcademicYear[i].xnxq_xnxqh + "' class='all '>" + allAcademicYear[i].xnxq_xnxqh + "</li>";
                    }
                }
                $("#pcIndeXxnxqDiv").html(html);
            }
            $(".j-search-con.single-box").on("click", ".j-select-year li ", function (e) {
                if ($(this).parent().attr("id") == 'pcIndeXxnxqDiv') {
                    window.location.href = "/elective/task/pcV3/index?fidEnc=" + fidEnc + "&xnxqh=" + $(this).attr("data-id");
                }
            })

            initCXCaptcha({
                captchaId: 'dRgtnuKwnxSvXXOl0btdNZqATWH8Kmjv',
                element: '#captcha',
                mode: 'popup',
                // type: 'iconclick',
                onVerify: function (err, data) {
                    /**
                     * 第一个参数是err（Error的实例），验证失败才有err对象
                     * 第二个参数是data对象，验证成功后的相关信息，data数据结构为key-value，如下：
                     * {
                     * validate: 'xxxxx' // 二次验证信息
                     * }
                     **/
                    if (err) return; // 当验证失败时，内部会自动refresh方法，无需手动再调用一次
                    if (opCourse == 1) {
                        electiveCourses(data.validate)
                    } else {
                        dropCourses(data.validate);
                    }
                    captchaIns.refresh()
                }
            }, function onload(instance) {
                captchaIns = instance;
            }, function onerror(err) {
            });

            function yzmdropCourses(taskBdid, cid) {
                opCourse = 2;
                finaltaskBdid = taskBdid;
                finaltaskcid = cid;
                if (captchaIns && yzm) {
                    captchaIns.popUp();
                } else {
                    dropCourses("");
                }
            }

            function yzmelectiveCourses(taskBdid, cid,zx) {
                opCourse = 1;
                finaltaskBdid = taskBdid;
                finaltaskcid = cid;
                finalzx = zx;
                if (captchaIns && yzm) {
                    captchaIns.popUp();
                } else {
                    electiveCourses("");
                }
            }

            function electiveCourses(validate) {
                var succ = true;
                U.ajax({
                    type: 'post',
                    url: "../elective/task/elective/courses",
                    data: {
                        fid: fid,
                        taskBdid: finaltaskBdid,
                        cid: finaltaskcid,
                        zx: finalzx,
                        validate: validate,
                        captchaIns: !!captchaIns
                    },
                    dataType: 'json',
                    async: false,
                    success: function (result) {
                        yzm = result.data;
                        if (result.code == 119){
                            var param = {
                                'data': [
                                    {
                                        'alias': 'xkzxsq_skls',
                                        'val': result.supplementary.xkzxsq_skls,
                                        'compt': 'contact'
                                    }
                                    , {
                                        'alias': 'xkzxsq_xm',
                                        'val': ['' + result.supplementary.xkzxsq_xm + ''],
                                        'compt': 'editinput'
                                    }
                                    , {
                                        'alias': 'xkzxsq_xh',
                                        'val': ['' + result.supplementary.xkzxsq_xh + ''],
                                        'compt': 'editinput'
                                    }
                                    , {'alias': 'xkzxsq_uid', 'val': ['' + result.supplementary.xkzxsq_uid + ''], 'compt': 'editinput'}
                                    , {
                                        'alias': 'xkzxsq_ssnj',
                                        'val': ['' + result.supplementary.xkzxsq_ssnj + ''],
                                        'compt': 'editinput'
                                    }, {
                                        'alias': 'xkzxsq_ssyx',
                                        'val': ['' + result.supplementary.xkzxsq_ssyx + ''],
                                        'compt': 'editinput'
                                    }, {
                                        'alias': 'xkzxsq_sszy',
                                        'val': ['' + result.supplementary.xkzxsq_sszy + ''],
                                        'compt': 'editinput'
                                    }, {
                                        'alias': 'xkzxsq_szbj',
                                        'val': ['' + result.supplementary.xkzxsq_szbj + ''],
                                        'compt': 'editinput'
                                    }, {
                                        'alias': 'xkzxsq_szxq',
                                        'val': ['' + result.supplementary.xkzxsq_szxq + ''],
                                        'compt': 'editinput'
                                    }, {
                                        'alias': 'xkzxsq_ssxkjh',
                                        'val': ['' + result.supplementary.xkzxsq_ssxkjh + ''],
                                        'compt': 'editinput'
                                    }, {
                                        'alias': 'xkzxsq_jxbmc',
                                        'val': ['' + result.supplementary.xkzxsq_jxbmc + ''],
                                        'compt': 'editinput'
                                    }, {
                                        'alias': 'xkzxsq_jxbbh',
                                        'val': ['' + result.supplementary.xkzxsq_jxbbh + ''],
                                        'compt': 'editinput'
                                    }, {
                                        'alias': 'xkzxsq_kkbm',
                                        'val': ['' + result.supplementary.xkzxsq_kkbm + ''],
                                        'compt': 'editinput'
                                    }, {
                                        'alias': 'xkzxsq_xshid',
                                        'val': ['' + result.supplementary.xkzxsq_xshid + ''],
                                        'compt': 'editinput'
                                    }, {
                                        'alias': 'xkzxsq_xxkhid',
                                        'val': ['' + result.supplementary.xkzxsq_xxkhid + ''],
                                        'compt': 'editinput'
                                    }, {
                                        'alias': 'xkzxsq_xkjhhid',
                                        'val': ['' + result.supplementary.xkzxsq_xkjhhid + ''],
                                        'compt': 'editinput'
                                    }, {
                                        'alias': 'xkzxsq_id',
                                        'val': ['' + result.supplementary.xkzxsq_id + ''],
                                        'compt': 'editinput'
                                    }
                                ]
                            }
                            // 创建表单
                            var temp_form = document.createElement('form')
                            // 填写表单数据
                            temp_form.action = result.supplementary.url
                            temp_form.target = '_blank'
                            temp_form.method = 'post'
                            temp_form.style.display = 'none'
                            // 添加参数
                            var opt = document.createElement('textarea')
                            opt.name = 'precast'
                            opt.value = JSON.stringify(param)
                            temp_form.appendChild(opt)
                            document.body.appendChild(temp_form)
                            // 提交数据
                            temp_form.submit()
                            // window.open(result.supplementary.url + "&precast=" + encodeURIComponent(JSON.stringify(param)));
                            succ = false;
                            return
                        }else if (result.code !== 200) {
                            layer.msg(result.msg)
                            succ = false;
                            return
                        }
                        if (listtab == 1) {
                            refreshData1()
                            $(".dialog-course").hide();
                        } else {
                            refreshData2()
                        }
                        layer.msg("报名成功")
                    }
                })
                return succ;
            }

            function dropCourses(validate) {
                var succ = true;
                U.ajax({
                    type: 'post',
                    url: "../elective/task/drop/courses",
                    data: {
                        fid: fid,
                        taskBdid: finaltaskBdid,
                        cid: finaltaskcid,
                        validate: validate,
                        captchaIns: !!captchaIns
                    },
                    dataType: 'json',
                    async: false,
                    success: function (result) {
                        if (result.code !== 200) {
                            if ('不在计划时间范围内报名不允许退课' == result.msg) {
                                //姓名 tksqb_xm
                                //学号 tksqb_xh  0009
                                //uid tksqb_uid
                                //所属退课计划 13
                                //退选选修课名称 tksqb_txxxkmc
                                var param = {
                                    'data': [
                                        {
                                            'alias': 'tksqb_xm',
                                            'val': ['' + result.data.tksqb_xm + ''],
                                            'compt': 'editinput'
                                        }
                                        , {
                                            'alias': 'tksqb_xh',
                                            'val': ['' + result.data.tksqb_xh + ''],
                                            'compt': 'editinput'
                                        }
                                        , {
                                            'alias': 'tksqb_uid',
                                            'val': ['' + result.data.tksqb_uid + ''],
                                            'compt': 'editinput'
                                        }
                                        , {'alias': '13', 'val': ['' + result.data.taskname + ''], 'compt': 'editinput'}
                                        , {
                                            'alias': 'tksqb_txxxkmc',
                                            'val': ['' + result.data.tksqb_txxxkmc + ''],
                                            'compt': 'editinput'
                                        }
                                    ]
                                }
                                // 创建表单
                                var temp_form = document.createElement('form')
                                // 填写表单数据
                                temp_form.action = result.data.url
                                temp_form.target = '_blank'
                                temp_form.method = 'post'
                                temp_form.style.display = 'none'
                                // 添加参数
                                var opt = document.createElement('textarea')
                                opt.name = 'precast'
                                opt.value = JSON.stringify(param)
                                temp_form.appendChild(opt)
                                document.body.appendChild(temp_form)
                                // 提交数据
                                temp_form.submit()
                                return
                            }
                            yzm = result.data;
                            layer.msg(result.msg)
                            succ = false;
                            return
                        }
                        layer.msg("退课成功")
                        yzm = result.data;
                        if (listtab == 1) {
                            refreshData1()
                            $(".dialog-mes").hide();
                        } else {
                            refreshData2()
                            $(".dialog-mes").hide();
                        }
                    }
                })
                return succ;
            }

            U.ajax({
                type: 'post',
                url: "../elective/task/getTask",
                data: {fid: fid, xnxqh: xnxqh},
                dataType: 'json',
                async: false,
                success: function (result) {
                    if (result.code !== 200 || !result.data) {
                        return false;
                    }
                    var html = "";
                    if (result.data.length == 0) {
                        html += "<div class='plan'>";
                        html += "<h1>当前无选课计划</h1>";
                        html += "</div>";
                        $(".dialog-plan .dialog .dialog-con").html(html);
                        $(".dialog-plan").show();
                        return;
                    }
                    // var sxhtml = "";//筛选计划
                    var dbsxhtml = "";
                    var newjh = [[${jh}]];
                    for (let i = 0; i < result.data.length; i++) {
                        html += "<div class='plan' data-id = '"+result.data[i].taskBdid+"'>";
                        html+="<div class='text'>";
                        html += "<h1>" + result.data[i].xkjhbJhmc + "</h1>";
                        html += "<p>" + result.data[i].xkjhbKssj + "~" + result.data[i].xkjhbJssj + "</p>";
                        html += "<p>" + result.data[i].xkjhbXksm + "</p>";
                        html += "</div>";
                        if (newjh ==''){
                            newjh = result.data[i].xkjhbJhmc;
                        }
                        if ('未结束'==result.data[i].xkjhbXkjhzt){
                            html += "<div class='state not-finished'>未结束</div>";
                            dbsxhtml += "<li class=\"not-finished\" data-id=\"" + result.data[i].taskBdid + "\">" + result.data[i].xkjhbJhmc + "</li>";
                        }else {
                            html += "<div class='state finished'>已结束</div>";
                            dbsxhtml += "<li class=\"finished\" data-id=\"" + result.data[i].taskBdid + "\">" + result.data[i].xkjhbJhmc + "</li>";
                        }
                        html += "</div>";
                        // sxhtml += "<li data-id=\"" + result.data[i].taskBdid + "\">" + result.data[i].xkjhbJhmc + "</li>";
                    }
                    // $("#jhDiv1").append(sxhtml);
                    // $("#jhDiv2").append(sxhtml);
                    $("#pcIndejhDiv").append(dbsxhtml);
                    $("#jhval").val(newjh);
                    $(".dialog-plan .dialog .dialog-con").html(html);
                    $(".dialog-plan").show();
                }
            })

            function refreshData2() {
                optionalCourse()
                optional_courses(2);
            }

            function refreshData1() {
                optionalCourse()
                stuOptionalCou()
                // optional_courses(1);
            }

            function optionalCourse() {
                U.ajax({
                    type: 'post',
                    url: "../elective/task/optional/course",
                    data: {fid: fid, xnxqh: xnxqh},
                    dataType: 'json',
                    async: false,
                    success: function (result) {
                        if (result.code !== 200) {
                            return false;
                        }
                        if (!result.data) {
                            $(".dialog-plan").hide();
                            return false;
                        }
                        stuSelectCourseCid = [];
                        stuAppluSelectCourseCid = [];
                        stuOptional = result.data;
                        if (stuOptional) {
                            for (let z = 0; z < stuOptional.length; z++) {
                                var selected_course = $.parseJSON(stuOptional[z].selectedCourse);
                                for (let y = 0; y < selected_course.length; y++) {
                                    if (selected_course[y].zx){
                                        stuAppluSelectCourseCid.push(selected_course[y].cid);
                                    }else {
                                        stuSelectCourseCid.push(selected_course[y].cid)
                                    }
                                }
                            }

                        }
                    }
                })
            }


            if (r) {
                if (r.code !== 200) {
                    layer.msg(r.msg);
                } else {
                    // optionalCourse()
                    stuOptionalCou();
                }
            } else {
                layer.msg("课表数据异常");
            }
            refreshData2()


            function stuOptionalCou() {
                var dxknum = 0;
                var djj = 0; //第几节课
                var html = "";
                for (let i = 0; i < r.data.lessons.length; i++) {
                    jcArr[r.data.lessons[i][0].actualNum] = r.data.lessons[i][0].lessonNumName;
                    html += "<tr>";
                    html += "<td>";
                    html += "<span class='section'> " + r.data.lessons[i][0].lessonNumName + "</span>";
                    html += " <span class='time'>" + r.data.lessons[i][0].begin + "-" + r.data.lessons[i][0].end + "</span>";
                    html += "</td>";

                    if (r.data.lessons[i][0].period === 2 || r.data.lessons[i][0].period === 3 || r.data.lessons[i][0].period === 1 || r.data.lessons[i][0].period === 4) {
                        djj++;
                    }
                    for (let j = 1; j <= 7; j++) {
                        if (r.data.lessons[i][0].period !== 2 && r.data.lessons[i][0].period !== 3 && r.data.lessons[i][0].period !== 1 && r.data.lessons[i][0].period !== 4) {
                            html += " <td></td>";
                            continue;
                        }
                        if (!stuOptional) {
                            html += "<td></td>";
                            continue;
                        }
                        var xknum = 0; //0灰色
                        var xkfalse = true;
                        var temphtml = "";
                        one :for (let z = 0; z < stuOptional.length; z++) {
                            var selected_course = $.parseJSON(stuOptional[z].selectedCourse);
                            var optional_course = $.parseJSON(stuOptional[z].optionalCourse);
                            if (selected_course.length > 0) {
                                two: for (let y = 0; y < selected_course.length; y++) {
                                    var allSj = selected_course[y].allSj;
                                    for (let l = 0; l < allSj.length; l++) {
                                        if (allSj[l] == (zc + ',' + j + ',' + djj)) {
                                            temphtml = "<td><div class='course' djj = '" + djj + "' zc = '" + j + "'  cid='" + selected_course[y].cid + "'><h5>" + selected_course[y].kcmc + "</h5></div></td>";
                                            xkfalse = false;
                                            break one;
                                        }
                                    }
                                }
                            }
                            if (optional_course.length > 0) {
                                two: for (let y = 0; y < optional_course.length; y++) {
                                    var allSj = optional_course[y].allSj;
                                    for (let l = 0; l < allSj.length; l++) {
                                        if (allSj[l] == (zc + ',' + j + ',' + djj)) {
                                            temphtml = "<td><div class='course selCourse'  djj = '" + djj + "' zc = '" + j + "'><h5>待选课</h5><span class='btn-jump'></span></div></td>";
                                            xkfalse = false;
                                            dxknum++;
                                            break two;
                                        }
                                    }
                                }
                            }
                            if (xkfalse) {
                                temphtml = " <td></td>";
                            }
                        }
                        if (xkfalse) {
                            temphtml = " <td></td>";
                        }
                        html += temphtml;
                    }
                    html += " </tr>";
                }
                $("#dxknum").text(dxknum);
                $(".tbody").html(html);
                rowSpanCell($(".z-table tbody"))
            }

            // 切换显示形式
            $('#selType li').click(function () {
                var idx = $(this).index()
                $(this).addClass('active').siblings().removeClass('active')
                if (idx == 0) {
                    $('#Curriculum').show()
                    $('#list').hide()
                    refreshData1()
                    listtab = 1;
                } else {
                    $('#Curriculum').hide()
                    $('#list').show()
                    refreshData2()
                    listtab = 2;
                }
            })

            // 合并单元格
            function rowSpanCell(el) {
                var tr = el.find('tr');
                var tdAry = JSON.parse(JSON.stringify(new Array(8).fill([])));
                for (let i = 0; i < tr.length; i++) {
                    var curTr = tr.eq(i);
                    var tds = curTr.find('td');
                    var nextTR = tr.eq(i + 1);
                    for (let j = 1; j < tds.length; j++) {
                        var curTxt = tds.eq(j).find('.course:not(.selCourse) h5').text();
                        var nextTxt = nextTR.find('td').eq(j).find('.course:not(.selCourse) h5').text();
                        if (curTxt != "") {
                            if (curTxt == nextTxt) {
                                tdAry[j].push(tds.eq(j))
                            } else {
                                tdAry[j].push(tds.eq(j))
                                var tdLen = tdAry[j].length;
                                if (tdLen > 1) {
                                    tdAry[j].forEach((element, index) => {
                                        if (index == 0) {
                                            $(element).attr('rowspan', tdLen).addClass('line')
                                        } else {
                                            $(element).hide();
                                        }

                                    });
                                }
                                tdAry[j] = []
                            }
                        }
                    }
                }
            }


            //初始化select列表
            initSelectlist()

            function initSelectlist() {
                if (!academicYear) {
                    return;
                }
                var selectHtml = ''
                for (var i = 1; i <= Number(academicYear.xnxq_jsz); i++) {
                    selectHtml += '<li><p>' + i + '</p></li>'
                }
                $('.selectList').html(selectHtml)
                $('.selectList li:first p').addClass('active')
            }

            zcdate();
            //课表顶部点击第几周
            $('.selectMask').on('click', '.selectList li p', function (e) {
                if (!academicYear) {
                    return;
                }
                e.stopPropagation()
                $('.selectMask .selectList li p').removeClass('active')
                $(this).addClass('active')
                var newweek = parseInt($(this).text())
                $('.selectMask').hide()
                $('.selectWeek span.week').attr('week', newweek);
                $('.selectWeek span.week strong').html(
                    newweek > 10 ? newweek : '0' + newweek
                )
                $('.nextWeek').removeClass('disabled')
                $('.prevWeek').removeClass('disabled')
                if (newweek == 1) {
                    $('.prevWeek').addClass('disabled')
                } else if (newweek == Number(academicYear.xnxq_jsz)) {
                    $('.nextWeek').addClass('disabled')
                }
                zc = newweek;
                zcdate();
                stuOptionalCou();
            })
            //上一周
            $('.prevWeek').click(function () {
                var week = $('.selectWeek span').attr('week')
                var newweek
                if (week > 1) {
                    newweek = parseInt(week) - 1
                    $('.selectWeek span.week').attr('week', newweek)
                    $('.selectWeek span.week strong').html(
                        newweek >= 10 ? newweek : '0' + newweek
                    )
                    zc = newweek;
                    zcdate();
                    stuOptionalCou();
                }
                $('.nextWeek').removeClass('disabled')
                if (newweek == 1) {
                    $(this).addClass('disabled')
                }
            })
            //下一周
            $('.nextWeek').click(function () {
                if (!academicYear) {
                    return;
                }
                var week = $('.selectWeek span').attr('week')
                var newweek
                if (week < Number(academicYear.xnxq_jsz)) {
                    newweek = parseInt(week) + 1
                    $('.selectWeek span.week').attr('week', newweek)
                    $('.selectWeek span.week strong').html(
                        newweek >= 10 ? newweek : '0' + newweek
                    )
                    zc = newweek;
                    zcdate();
                    stuOptionalCou();
                }
                $('.prevWeek').removeClass('disabled')
                if (newweek == Number(academicYear.xnxq_jsz)) {
                    $(this).addClass('disabled')
                }
            })

            function zcdate() {
                $(".z-table .thead .week").removeClass("today");
                var date = dates[zc];
                let split = date.split(",");
                for (let i = 1; i <= split.length; i++) {
                    $("#week" + i).text(split[i - 1]);
                    $("#week" + i).next().text("周" + getxqStr(i));
                    let zcdateday = split[i - 1].split("/");
                    if (zcdateday.length == 2 && zcdateday[0] == currentMonth && zcdateday[1] == currentDay) {
                        $("#week" + i).next().addClass("today")
                        $("#week" + i).next().text("今天");
                    }

                }
            }

            $('.selectWeek .week').click(function () {
                $('.selectMask').show()
            })
            $('.selectMask').click(function () {
                $(this).hide()
            })
            $('.selectMask').on('click', 'ul', function (e) {
                e.stopPropagation()
            })

            // 选课计划确定
            $('#planSel').click(function () {
                thisxkjhid = $(".dialog-con .plan.active").attr("data-id");
                $("#jhval").val($(".dialog-con .plan.active").find("h1").text());
                if (listtab == 2){
                    optional_courses(2)
                }
                $('.dialog-plan').hide()
            })
            // 点击待选课
            var selCourseEle
            $('.tbody').on('click', '.course', function () {
                selCourseEle = $(this)
                if ($(this).hasClass('selCourse')) {
                    // 待选课
                    $('.dialog-course').show()
                    optional_courses(1);
                } else {
                    // 详情
                    // var txt = $(this).find('h5').text();
                    // $(".dialog-mes").show().find('.dialog-title h3').text(txt);
                    // $('.dialog-mes .dialog-btn #cancelCourse').hide()
                    $('.dialog-mes .dialog-btn #exitCourse').show();
                    courseDetail($(this).attr("cid"))
                }

            })
            var taskBdid = "";
            var cid = "";
            // 选择课程
            $('#zList').on('click', '.z-course:not(".z-full")', function () {
                taskBdid = $(this).attr("taskBdid");
                cid = $(this).attr("id");
                $(this).addClass('active').siblings().removeClass('active')
            })
            $('.cancel,#cancelCourse').click(function () {
                $('.dialog-wrap').hide()
            })
            $('.dialog-wrap').on('click', '.dialog-close', function () {
                $('.dialog-wrap').hide()
            })
            // 课程确定
            $('#courseSel').click(function () {
                if ($('.z-course.active').length > 0) {
                    if (!taskBdid || !cid) {
                        $("#tipsBox").text('异常').fadeIn().delay(1000).fadeOut();
                        return false;
                    }
                    opcid = cid;
                    yzmelectiveCourses(taskBdid, cid,false);
                    // var courseTxt = $('.z-course.active h3').text()
                    // selCourseEle.find('h5').text(courseTxt)
                    // selCourseEle.removeClass('selCourse')
                    // $('.dialog-course').hide()
                } else {
                    $('#tipsBox').text('请选择课程').fadeIn().delay(1000).fadeOut()
                }
            })


            // 点击详情
            var detailEle
            // $('.btn-detail').click(function () {
            $("body").on('click', '.btn-detail', function () {
                detailEle = $(this)
                // var txt = $(this).parent().prev().find('.z-course-title h3').text()
                // $('.dialog-mes').show().find('.dialog-title h3').text(txt);
                // $('.dialog-mes .dialog-btn #cancelCourse').show()
                // $('.dialog-mes .dialog-btn #exitCourse').hide();
                courseDetail($(this).attr("cid"));
            })
            var opcid;
            // 退课
            $('#exitCourse').click(function () {
                var taskBdid = $("#" + opcid + "").attr("taskBdid");
                yzmdropCourses(taskBdid, opcid);
                $('.dialog-mes').hide()
                // if (!$('#Curriculum').is(':hidden')) {
                //     var rowSpanCell = parseInt(selCourseEle.parent().attr('rowspan'));
                //     if (!rowSpanCell) {
                //         selCourseEle.addClass("selCourse").find('h5').text("待选课")
                //         return false;
                //     }
                //     var trIndex = selCourseEle.parents('tr').index();
                //     var tdIndex = selCourseEle.parent().index();
                //     selCourseEle.parent().removeAttr('rowspan').removeClass('line')
                //
                //     for (var i = trIndex; i < trIndex + rowSpanCell; i++) {
                //         $(".z-table tbody tr").eq(i).find("td").eq(tdIndex).show().find(".course").addClass("selCourse").find('h5').text("待选课");
                //     }
                //
                // } else {
                //     var p = detailEle.parents('.z-course')
                //
                //     detailEle.hide().next().hide()
                //     detailEle.nextAll('.btn-sel').show()
                //     p.removeClass('active')
                //     var em = p.find('em')
                //     var txt = parseInt(em.text()) + 1
                //     em.text(txt)
                // }
            })

            // 列表选择课程
            $('#selCourseList').on('click', '.z-course .btn-sel,.btn-follow', function () {
                // var p = $(this).parents('.z-course')
                // var em = p.find('em')
                // var txt = parseInt(em.text()) - 1
                // em.text(txt)
                // $(this).prev().show()
                // $(this).hide()
                // p.addClass('active')
                var taskBdid = $(this).attr("taskBdid");
                var cid = $(this).attr("cid");
                opcid = cid;
                yzmelectiveCourses(taskBdid, cid,false);
            })

            $('#selCourseList').on('click', '.z-course .btn-Self', function () {
                var taskBdid = $(this).attr("taskBdid");
                var cid = $(this).attr("cid");
                opcid = cid;
                yzmelectiveCourses(taskBdid, cid,true);
            })

            function courseDetail(cid) {
                // if (opcid === cid) {
                //     $(".dialog-mes").show();
                //     return false;
                // }
                opcid = cid;
                U.ajax({
                    type: 'post',
                    url: "../elective/task/course/detail",
                    data: {fid: fid, cid: cid},
                    dataType: 'json',
                    async: false,
                    success: function (result) {
                        if (result.code !== 200) {
                            layer.msg(result.msg)
                            return false;
                        }
                        var html = "";
                        var d = result.data;
                        $(".dialog-mes .dialog .dialog-title").html("<h3 id='" + d.id + "'  taskBdid = '" + d.taskBdid + "'>" + d.xxkmc + "</h3><span class='dialog-close'></span>");
                        // html += "<h3 id='" + d.id + "'  taskBdid = '" + d.taskBdid + "'>" + d.xxkmc + "</h3>";
                        html += "<p><span style='width: 103px;'>所属选课计划 ：</span>" + d.xxkmc + "</p>";
                        html += "<p><span>开设课程 ：</span>" + d.xxkXxkmc + "</p>";
                        html += "<p><span>开课年级 ：</span>" + d.xxkKknj + "</p>";
                        html += " <p><span>开课院系 ：</span>" + d.xxkKkyx + "</p>";
                        html += "<p><span>开课专业 ：</span>" + d.xxkKkzy + "</p>";
                        html += " <p><span>课程类型 ：</span>" + d.xxkKclx + "</p>";
                        html += " <p><span>上课时间 ：</span>" + getsksjStr(d.xxkSksj) + "</p>";
                        html += "<p><span>上课地点 ：</span>" + d.xxkSkdd + "</p>";
                        html += "<p><span>授课教师 ：</span>" + d.xxkSkjs + "</p>";
                        html += "<p><span>学分 ：</span>" + d.xxkXxkxf + "分</p>";
                        if (d.xxkXxkrl == -1) {
                            html += "<p><span>课程容量：</span>无上限</p>";
                        } else {
                            html += "<p><span>课程容量：</span>" + d.xxkXxkrl + "</p>";
                        }
                        html += "<p><span>可选性别：</span>" + d.xxkKxxsxb + "</p>";
                        html += "<p><span>编组：</span>" + d.courseGroupName + "</p>";
                        $(".dialog-mes .dialog .dialog-con").html(html);
                        if (listtab ==1){
                            $("#exitCourse").show();
                            $("#cancelCourse").hide();
                        }else {
                            $("#exitCourse").hide();
                            $("#cancelCourse").show();
                        }
                        $(".dialog-mes").show();
                    }
                })
            }

            // 退课
            $('#selCourseList').on('click', '.z-course .btn-exit', function () {
                // var p = $(this).parents('.z-course')
                // $(this).next().show()
                // $(this).hide()
                // p.removeClass('active')
                // var em = p.find('em')
                // var txt = parseInt(em.text()) + 1
                // em.text(txt)
                var taskBdid = $(this).attr("taskBdid");
                var cid = $(this).attr("cid");
                opcid = cid;
                yzmdropCourses(taskBdid, cid);
            })
            // 查询
            $(".main-con .btn-search").click(function () {
                var prev = $(this).parent().prev();
                let params = {}
                prev.find('.schoolSel').each(function () {
                    var labelName = $(this).attr('name');
                    var labelVal = $(this).val();
                    params[labelName] = labelVal
                })
                console.log(params);
                curPage = 1;
                optional_courses(2);
            })
            // 重置
            $(".main-con .btn-reset").click(function () {
                var prev = $(this).parent().prev();
                prev.find('.schoolSel').val("");
                prev.find(".active").removeClass('active');
                prev.find(".all-selects ").text("全选")
            })
            // 添加课程-查询
            $(".dialog-course .btn-reset").click(function () {
                var prev = $(this).parent().prev();
                prev.find('.schoolSel').val("");
                prev.find(".active").removeClass('active');
                prev.find(".all-selects ").text("全选")
            })
            // 添加课程-重置
            $(".dialog-course .btn-search").click(function () {
                var prev = $(this).parent().prev();
                let params = {}
                prev.find('.schoolSel').each(function () {
                    var labelName = $(this).attr('name');
                    var labelVal = $(this).val();
                    params[labelName] = labelVal
                })
                console.log(params);
                optional_courses(1);
            })

            // 查看已选课程
            $('#courseSwitch').on('click', function () {
                $(this).toggleClass('active')

                if ($(this).hasClass('active')) {
                    $(this).next().text('开启')
                } else {
                    $(this).next().text('关闭')
                }
                // 处理开启关闭逻辑
                curPage = 1;
                optional_courses(2);
            })

            $(".dialog-wrap.dialog-plan").on("click", ".dialog .plan", function () {
                $(this).addClass("active").siblings().removeClass("active");
            })

            $(".j-search-con.single-box").on("click", ".j-select-year #pcIndejhDiv li", function (e) {
                thisxkjhid = $(this).attr("data-id");
                curPage = 1;
                if (listtab == 2){
                    optional_courses(2)
                }
            });

            //开课年级 xxk_kknj
            //开课院系 xxk_kkyx
            //开课专业  xxk_kkzy
            //开设课程  kskc
            //开课校区  xxk_kkxiaoqu
            //课程类型  xxk_kclx
            //选修课名称  xxk_xxkmc
            function optional_courses(divv) {
                var xxk_kknj = '';
                var xxk_kkyx = '';
                var xxk_kkzy = '';
                var kskc = '';
                var xxk_kkxiaoqu = '';
                var xxk_kclx = '';
                var xxk_xxkmc = '';
                var jc = '';
                var jh = $("#jhval").val();
                var skjs = '';
                var select = false;
                if (divv === 2) {
                    xxk_kknj = $("#njVal2").val();
                    xxk_kkyx = $("#yxVal2").val();
                    xxk_kkzy = $("#zyVal2").val();
                    kskc = $("#kcVal2").val();
                    xxk_kkxiaoqu = $("#xqVal2").val();
                    xxk_kclx = $("#lxVal2").val();
                    xxk_xxkmc = $("#xxkmc2").val();
                    // jh = $("#jhVal2").val();
                    skjs = $("#skjsVal2").val();
                    select = $("#courseSwitch").hasClass('active');
                } else {
                    xxk_kknj = $("#njVal1").val();
                    xxk_kkyx = $("#yxVal1").val();
                    xxk_kkzy = $("#zyVal1").val();
                    kskc = $("#kcVal1").val();
                    xxk_kkxiaoqu = $("#xqVal1").val();
                    xxk_kclx = $("#lxVal1").val();
                    xxk_xxkmc = $("#xxkmc1").val();
                    // jh = $("#jhVal1").val();
                    skjs = $("#skjsVal1").val();
                    if (selCourseEle) {
                        jc = zc + ',' + selCourseEle.attr("zc") + ',' + selCourseEle.attr("djj");
                    }
                }
                U.ajax({
                    type: 'post',
                    url: "../elective/task/optional/courses/list",
                    data: {
                        fid: fid,
                        xxk_kknj: xxk_kknj,
                        xxk_kkyx: xxk_kkyx,
                        xxk_kkzy: xxk_kkzy,
                        kskc: kskc,
                        xxk_kkxiaoqu: xxk_kkxiaoqu,
                        xxk_kclx: xxk_kclx,
                        xxk_xxkmc: xxk_xxkmc,
                        jc: jc,
                        jh: jh,
                        xnxqh: xnxqh,
                        curPage: curPage,
                        pageSize: pageSize,
                        xxk_skjs: skjs,
                        select: select

                    },
                    dataType: 'json',
                    async: false,
                    success: function (result) {
                        if (result.code !== 200) {
                            layer.msg(result.msg)
                            return false;
                        }
                        if (!result.data) {
                            return false;
                        }
                        var html = "";
                        for (let i = 0; i < result.data.length; i++) {
                            let d = result.data[i];
                            if (d.selectNum == d.xxkXxkrl) {
                                html += "<div class='z-course z-full' taskBdid = '" + d.taskBdid + "' id='" + d.id + "' bdid = '" + d.xxkBdid + "' source = '" + d.source + "'>";
                            } else {
                                html += "<div class='z-course' taskBdid = '" + d.taskBdid + "' id='" + d.id + "' bdid = '" + d.xxkBdid + "' source = '" + d.source + "'>";
                            }
                            html += "<div class='z-course-num'>" + (i + 1) + "</div>";
                            if (divv === 2) {
                                html += "<div class='z-course-mes'>";
                            }
                            html += "<div class='z-course-title'>";
                            html += "<h3>" + d.xxkXxkmc + "</h3>";
                            // html += "<span>" + d.taskName + "</span>";
                            html += "</div>";
                            html += "<div class='z-mes'>";
                            if (d.xxkXxkrl == -1) {
                                html += "<p>课程余量：<span>" + d.selectNum + "/无上限</span></p>";
                            } else {
                                html += "<p>课程余量：<span>" + d.selectNum + "/" + d.xxkXxkrl + "</span></p>";
                            }
                            html += "<p>上课时间：<span>" + getsksjStr(d.xxkSksj) + "</span></p>";
                            html += "<p>课程类型：<span>" + d.xxkKclx + "</span></p>";
                            html += " <p>学分：<span>" + d.xxkXxkxf + "分</span></p>";
                            html += "<p>上课地点：<span>" + d.xxkSkdd + "</span></p>";
                            html += "<p>授课教师：<span>" + d.xxkSkjs + "</span></p>";
                            html += "</div>";
                            //如果简介
                            html += "<div class='z-intro'>";
                            html += "</div>";
                            //     <h5>课程简介：</h5>
                            //     <p>
                            //         会议强调，要“用好政策空间、找准发力方向，扎实推动经济高质量发展”“大力推动现代化产业体系建设”“通过增加居民收入扩大消费”“切实优化民营企业发展环境”“加大民生保障力度”等。其中，消费、住房、就业等工作部署事关你我，一起来了解。
                            //     </p>
                            html += "</div>";
                            if (divv === 2) {
                                html += "<div class='z-btn'>";
                                if (d.source == 4){
                                    if (stuSelectCourseCid.indexOf(d.id) != -1) {
                                        html += "<span class='btn-detail' cid='" + d.id + "' style='display: inline;'>详情</span>";
                                        html += "<span class='btn-exit' taskBdid = '" + d.taskBdid + "' cid = '" + d.id + "' style='display: inline;'>退课</span>";
                                        html += "<span class='btn-sel' taskBdid = '" + d.taskBdid + "' cid = '" + d.id + "' style='display: none'>选课</span>";
                                    } else if (stuAppluSelectCourseCid.indexOf(d.id) != -1){
                                        html += "<span class='btn-detail' cid='" + d.id + "' >详情</span>";
                                        html += "<span class='btn-review'>审核中</span>";
                                    } else {
                                        html += "<span class='btn-detail' cid='" + d.id + "' >详情</span>";
                                        html += "<span class='btn-follow' taskBdid = '" + d.taskBdid + "' cid = '" + d.id + "' >跟班上课</span>";
                                        if (d.zx ==1){
                                            html += "<span class='btn-Self' taskBdid = '" + d.taskBdid + "' cid = '" + d.id + "'>自修</span>";
                                        }
                                    }
                                }else {
                                    if (stuSelectCourseCid.indexOf(d.id) != -1) {
                                        html += "<span class='btn-detail' cid='" + d.id + "' style='display: inline;'>详情</span>";
                                        html += "<span class='btn-exit' taskBdid = '" + d.taskBdid + "' cid = '" + d.id + "' style='display: inline;'>退课</span>";
                                        html += "<span class='btn-sel' taskBdid = '" + d.taskBdid + "' cid = '" + d.id + "' style='display: none'>选课</span>";
                                    } else if (d.selectNum == d.xxkXxkrl) {
                                        html += "<span class='btn-full'>已满</span>";
                                    } else {
                                        html += "<span class='btn-detail' cid='" + d.id + "' >详情</span>";
                                        html += "<span class='btn-exit' taskBdid = '" + d.taskBdid + "' cid = '" + d.id + "' >退课</span>";
                                        html += "<span class='btn-sel' taskBdid = '" + d.taskBdid + "' cid = '" + d.id + "'>选课</span>";
                                    }
                                }

                                html += "</div>";
                                html += "</div>";
                            }
                        }
                        if (divv === 2) {
                            $("#selCourseList").html(html);
                            $(".course-total span").text(result.count);
                            getIntro("selCourseList");
                            // 使用laypage进行分页
                            laypage.render({
                                elem: 'coursePage1' // 分页容器的id
                                , curr: curPage
                                , count: result.count // 数据总数
                                , limit: pageSize // 每页显示的数据条数
                                , limits: [10, 20, 30, 50], // 每页条数的选项
                                layout: ['prev', 'page', 'next', 'skip', 'limit']
                                , jump: function (obj, first) { // 分页跳转的回调
                                    // obj包含了分页的所有参数，first为true表示第一次
                                    if (!first) {
                                        // 根据obj.curr获取当前页，obj.limit获取每页显示的条数
                                        curPage = obj.curr;
                                        pageSize = obj.limit;
                                        optional_courses(2);
                                    }
                                }
                            });
                        } else {
                            $("#zList").html(html);
                            getIntro("zList");
                        }
                    }
                })
            }

            function getIntro(divId) {
                $("#"+divId).children(".z-course").each(function (){
                    var _this = $(this);
                    if (_this.attr("source")!=2){
                        return ;
                    }
                    $.ajax({
                        type: "POST",
                        url: "/elective/task/getIntro",
                        data: {fid: fid,bdid:_this.attr("bdid")},
                        dataType: 'json',
                        success: function (result) {
                            if (result.data){
                                _this.find(".z-intro").html("<h5>课程简介：</h5><p>"+result.data+"</p>");
                            }
                        }
                    });
                })

            }

            U.ajax({
                type: "POST",
                url: "../elective/getNj",
                data: {fid: fid},
                dataType: 'json',
                success: function (result) {
                    var html = "";
                    if (result) {
                        for (var i = 0; i < result.data.length; i++) {
                            var name = result.data[i].name;
                            var id = result.data[i].id;
                            html += "<li data-id=\"" + id + "\">" + name + "</li>";
                        }

                    }
                    $("#njDiv1").append(html);
                    $("#njDiv2").append(html);
                }
            });

            U.ajax({
                type: "POST",
                url: "../elective/getSkjs",
                data: {fid: fid},
                dataType: 'json',
                success: function (result) {
                    var html = "";
                    if (result) {
                        for (var i = 0; i < result.data.length; i++) {
                            html += "<li data-id=''>" + result.data[i] + "</li>";
                        }
                    }
                    $("#skjsDiv1").append(html);
                    $("#skjsDiv2").append(html);
                }
            });

            U.ajax({
                type: "POST",
                url: "../elective/getYX",
                data: {fid: fid},
                dataType: 'json',
                success: function (result) {
                    var html = "";
                    if (result) {
                        for (var i = 0; i < result.data.length; i++) {
                            var name = result.data[i].name;
                            var id = result.data[i].id;
                            html += "<li data-id=\"" + id + "\">" + name + "</li>";
                        }

                    }
                    $("#yxDiv2").append(html);
                    $("#yxDiv1").append(html);
                }
            });

            U.ajax({
                type: "POST",
                url: "../elective/getZY",
                data: {fid: fid},
                dataType: 'json',
                success: function (result) {
                    var html = "";
                    if (result) {
                        for (var i = 0; i < result.data.length; i++) {
                            var name = result.data[i].name;
                            var id = result.data[i].id;
                            html += "<li data-id=\"" + id + "\">" + name + "</li>";
                        }

                    }
                    $("#zyDiv2").append(html);
                    $("#zyDiv1").append(html);
                }
            });

            U.ajax({
                type: "POST",
                url: "../elective/getTaskKC",
                data: {fid: fid},
                dataType: 'json',
                success: function (result) {
                    var html = "";
                    if (result) {
                        for (var i = 0; i < result.data.length; i++) {
                            var name = result.data[i].name;
                            var id = result.data[i].id;
                            html += "<li data-id=\"" + id + "\">" + name + "</li>";
                        }

                    }
                    $("#kcDiv2").append(html);
                    $("#kcDiv1").append(html);
                }
            });

            U.ajax({
                type: "POST",
                url: "../elective/getXQ",
                data: {fid: fid},
                dataType: 'json',
                success: function (result) {
                    var html = "";
                    if (result) {
                        for (var i = 0; i < result.data.length; i++) {
                            var name = result.data[i].name;
                            var id = result.data[i].id;
                            html += "<li data-id=\"" + id + "\">" + name + "</li>";
                        }

                    }
                    $("#xqDiv2").append(html);
                    $("#xqDiv1").append(html);
                }
            });

            U.ajax({
                type: "POST",
                url: "../elective/getLX",
                data: {fid: fid},
                dataType: 'json',
                success: function (result) {
                    var html = "";
                    if (result) {
                        for (var i = 0; i < result.data.length; i++) {
                            var name = result.data[i].name;
                            var id = result.data[i].id;
                            html += "<li data-id=\"" + id + "\">" + name + "</li>";
                        }

                    }
                    $("#lxDiv2").append(html);
                    $("#lxDiv1").append(html);
                }
            });

            //[{"xxk_skjc":"3","xxk_skxq":"2","xxk_skzc":"1-5,10"}]
            //获取上课时间
            function getsksjStr(sj) {
                let sjsjon = $.parseJSON(sj);
                var sksjstr = "";
                for (let i = 0; i < sjsjon.length; i++) {
                    let split = sjsjon[i].xxk_skzc.split(",");
                    for (let j = 0; j < split.length; j++) {
                        sksjstr += split[j] + "周、";
                    }
                    sksjstr += "周" + getxq(sjsjon[i].xxk_skxq) + "、";
                    sksjstr += getjc(sjsjon[i].xxk_skjc);
                    sksjstr += ";";
                }
                return sksjstr;
            }

            function getjc(xxk_skjc) {
                var skxqstr = "";
                let split = xxk_skjc.split(",");
                for (let i = 0; i < split.length; i++) {
                    if (split[i].indexOf("-") != -1) {
                        if (skxqstr != '') {
                            skxqstr += ",";
                        }
                        let split1 = split[i].split("-");
                        skxqstr += getjcStr(split1[0]) + "-" + getjcStr(split1[1]);
                    } else {
                        if (skxqstr != '') {
                            skxqstr += ",";
                        }
                        skxqstr += getjcStr(split[i]);
                    }
                }
                return skxqstr;
            }

            function getxq(xxk_skxq) {
                var skxqstr = "";
                let split = xxk_skxq.split(",");
                for (let i = 0; i < split.length; i++) {
                    if (split[i].indexOf("-") != -1) {
                        if (skxqstr != '') {
                            skxqstr += ",";
                        }
                        let split1 = split[i].split("-");
                        skxqstr += getxqStr(split1[0]) + "-" + getxqStr(split1[1]);
                    } else {
                        if (skxqstr != '') {
                            skxqstr += ",";
                        }
                        skxqstr += getxqStr(split[i]);
                    }
                }
                return skxqstr;
                // for (let i = 0; i < xxk_skxq.length; i++) {
                //     let split = xxk_skxq[i].split(",");
                //     for (let j = 0; j < split.length; j++) {
                //         skxqstr += split[j] + "周、";
                //     }
                // }
            }

            function getxqStr(xq) {
                if (xq == 1) {
                    return "一";
                } else if (xq == 2) {
                    return "二";
                } else if (xq == 3) {
                    return "三";
                } else if (xq == 4) {
                    return "四";
                } else if (xq == 5) {
                    return "五";
                } else if (xq == 6) {
                    return "六";
                } else if (xq == 7) {
                    return "日";
                } else {
                    return "";
                }
            }

            function getjcStr(jc) {
                return jcArr[jc];
            }
        })
    })

</script>
</html>