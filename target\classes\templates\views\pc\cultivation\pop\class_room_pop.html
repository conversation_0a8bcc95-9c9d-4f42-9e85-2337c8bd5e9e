<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <title>指定教室</title>
    <link rel="stylesheet" th:href="@{../css/cultivation/jquery.searchableSelect.css}"/>
    <link rel="stylesheet" th:href="@{../css/cultivation/newStyle.css}"/>
    <link rel="stylesheet" th:href="@{../plugin/layui/css/layui.css}"/>
    <style>
        html, body {
            height: 100%;
        }

        .layui-laypage .layui-laypage-curr .layui-laypage-em {
            background-color: #4C88FF !important;
        }

        .layui-form-radio > i:hover, .layui-form-radioed > i {
            color: #4C88FF !important;
        }

        .layui-form .layui-table-cell input[type=checkbox], .layui-form .layui-table-cell input[type=radio] {
            display: none !important;
        }
    </style>
</head>
<body>
<div class="popDiv popMove wid840">
    <div class="popBody" id="popScroll">
        <div class="popSearch clearAfter">
            <div class="popSearch_row fl">
                <div class="popSearch_name">
                    <div class="popSearch_table">
                        <div class="popSearch_cell"><span>教学楼名称</span></div>
                    </div>
                </div>
                <div class="popSearch_per"><input htmlEscape="false" class="popSearch_input" maxlength="20" id="jxlmc"
                                                  name="jxlmc"/></div>
            </div>
            <div class="popSearch_row fl">
                <div class="popSearch_name">
                    <div class="popSearch_table">
                        <div class="popSearch_cell"><span>教室名称</span></div>
                    </div>
                </div>
                <div class="popSearch_per"><input htmlEscape="false" class="popSearch_input" maxlength="20" id="jsmc"
                                                  name="jsmc"/></div>
            </div>
            <div class="popSearch_row fl">
                <div class="popSearch_name">
                    <div class="popSearch_table">
                        <div class="popSearch_cell"><span>教室启用状态</span></div>
                    </div>
                </div>
                <select name="status" class="qselect">
                    <option value="">请选择</option>
                    <option value="是" selected>是</option>
                    <option value="否">否</option>
                </select>
            </div>
            <div class="popSearch_row fl" th:if="${#strings.isEmpty(classRoomType)}">
                <div class="popSearch_name">
                    <div class="popSearch_table">
                        <div class="popSearch_cell"><span>教室类型</span></div>
                    </div>
                </div>
                <select id="roomType" class="qselect" name="jslx">
                    <option value="">请选择</option>
                </select>
            </div>
        </div>
        <div class="popSearch_btns">
            <div class="popSearch_clear fr">清空筛选</div>
            <div class="popSearch_search_btn fr" lay-filter="search" lay-submit>筛选</div>
        </div>

        <div class="popSearch_cont">
            <table lay-filter="jsTable" class="layui-table" id="jsTable">
            </table>
        </div>
    </div>
</div>
<script th:src="@{../js/jquery-3.3.1.min.js}"></script>
<script th:src="@{../js/jquery.nicescroll.min.js}"></script>
<script th:src="@{../js/cultivation/jquery.searchableSelect.js}"></script>
<script th:src="@{../js/cultivation/select_data.js}"></script>
<script th:src="@{../plugin/layui/layui.js}"></script>
<script type="text/javascript">
    //弹窗居中
    function MoveFixed(userClass) {
        $(userClass).css({
            top: function () {
                return 0;
            }, left: function () {
                return ($(window).width() - $(this).width()) / 2;
            }
        });

    }

    window.onresize = function () {
        MoveFixed('.popMove')
    }
    MoveFixed('.popMove')

    function scrollBox(userID) {
        $(userID).niceScroll({
            cursorborder: "",
            cursorwidth: 8,
            cursorcolor: "#CFD8E6",
            boxzoom: false,
            autohidemode: true
        });
        $(userID).getNiceScroll().resize(); //检测滚动条是否重置大小（当窗口改变大小时）
    }

    scrollBox("#popScroll");
    $('.qselect').click();
    document.domain = document.domain.split('.').slice(-2).join('.');

    function getSelectData() {
        var checkRows = table.checkStatus('jsTable');
        if (checkRows.length == 0) {
            return 0;
        }
        var rowData = checkRows.data[0];
        return rowData;
    }

    $(".popSearch_search_btn").click(function () {
        var xnxq = $("select[name='xnxq']").val();
        var jsmc = $("input[name='jsmc']").val();
        var jxlmc = $("input[name='jxlmc']").val();
        var jszt = $("select[name='status']").val();
        var jslx = $("select[name='jslx']").val();
        var field = {
            xnxq: xnxq,
            teachBuilding: jxlmc,
            name: jsmc,
            status: jszt,
            roomType: jslx
        };
        insTb.reload({where: field, page: {curr: 1}});
    })

    $(".popSearch_clear").click(function () {
        location.reload();
    })

    var table = "", insTb = "";
    layui.use('table', function () {
        table = layui.table;
        insTb = table.render({
            elem: '#jsTable',
            url: '../processData/getClassRoomInfoData',
            where: {roomType: "[[${classRoomType}]]", majorCode: "[[${majorCode}]]"},
            page: true,
            cols: [
                [
                    {type: 'radio', field: 'id', fixed: 'left'},
                    {field: 'jsxx_jsbh', title: '教室编号'},
                    {field: 'jsxx_jsmc', title: '教室名称'},
                    {field: 'jsxx_jxl', title: '教学楼'},
                    {field: 'jsxx_jslx', title: '教室类型'},
                    {field: 'jsxx_xq', title: '所在校区'},
                    {field: 'jsxx_sfqy', title: '启用状态'}
                ]
            ],
            done: function () {
                scrollBox("#popScroll");
            }
        });

        table.on('row(jsTable)', function (obj) {
            obj.tr.addClass('tr_bj_color').siblings().removeClass('tr_bj_color');
        });
    });
</script>
</body>
</html>