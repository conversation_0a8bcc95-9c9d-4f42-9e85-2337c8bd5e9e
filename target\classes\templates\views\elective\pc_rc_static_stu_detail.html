<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" li-content="IE=edge">
    <meta name="viewport" li-content="width=device-width, initial-scale=1.0">
    <title>微点名</title>
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/global.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/layui1/css/layui.css'">
    <link rel="stylesheet" href="/elective/rollcall/css/commonPC.css">
    <link rel="stylesheet" href="/elective/rollcall/css/reset.css">
    <link rel="stylesheet" href="/elective/rollcall/css/poup.css">
    <link rel="stylesheet" href="/elective/rollcall/css/microdot-name_pc_stu_index.css">
    <link rel="stylesheet" href="/elective/rollcall/css/new-microdot-name.css">
    <link rel="stylesheet" href="/elective/rollcall/css/name-calling-end.css">
    <script th:src="${_CPR_}+'/elective/layui1/layui.js'"></script>
    <style>
        body .main .con .table {
            padding-left: 30px;
        }

        body .main .con {
            height: auto;
            min-height: calc(100vh - 196px);
        }

        body .main .con .filter-box .inform .i-item {
            margin-right: 127px;
        }

        body .main .con .filter-box.border {
            display: block;
            border-bottom: 1px solid rgba(232, 235, 241, 1);
            padding-top: 0;
        }

        body .main .con .filter-box.filter {
            justify-content: flex-start;
            padding-top: 24px;
        }

        body .main .con .filter-box.filter .layui-form .layui-form-item {
            margin-bottom: 16px;
        }

        .main .con .table .detail span i.color6 {
            color: rgba(175, 119, 255, 1);
        }

        body .main .con .filter-box .button-list {
            display: block;
            padding-left: 30px;
            border-left: 1px solid rgba(232, 235, 241, 1);
        }

        body .main .con .filter-box .button-list .reset {
            margin-bottom: 16px;
        }

        body .main .con .filter-box .button-list .reset {
            margin-right: 25px;
        }

        body .main .con .filter-box .layui-form .layui-form-item .sel .select-input em {
            background: url(/elective/rollcall/images/down-icon.png) no-repeat center;
            background-size: 10px;
        }

        .layui-table-cell .normal6 {
            width: 58px;
            height: 20px;
            text-align: center;
            line-height: 20px;
            border-radius: 15px;
            font-size: 14px;
            color: #ffffff;
            background-color: rgba(175, 119, 255, 1);
            margin: 8px auto;
        }

        .layui-table-cell {
            white-space: wrap;
            overflow: auto;
            text-overflow: clip;
        }
    </style>

</head>

<body>
    <div class="main">
        <div class="top">
            <div class="title">
                <div class="back">返回</div>
                <div class="levelone">学生点名统计</div>
                <div class="icon"></div>
                <div class="leveltwo">查看详情</div>
            </div>
        </div>
        <div class="con">
            <div class="filter-box border">
                <div class="inform">
                    <div class="i-item">
                        <div class="name">学年学期：</div>
                        <div class="text" th:text="${current.xnxq_xnxqh}"></div>
                    </div>
                    <div class="i-item">
                        <div class="name">姓名：</div>
                        <div class="text" th:text="${uname}"></div>
                    </div>
                    <div class="i-item">
                        <div class="name">学号：</div>
                        <div class="text" th:text="${stuCode}"></div>
                    </div>
                    <div class="i-item">
                        <div class="name">所属班级：</div>
                        <div class="text" th:text="${className}"></div>
                    </div>
                    <div class="i-item">
                        <div class="name">日期区间：</div>
                        <div class="text" th:text="${courseTime}"></div>
                    </div>
                </div>

            </div>
            <div class="filter-box filter">
                <form class="layui-form" action="" id="example" lay-filter="myFormFilter">
                    <div class="layui-form-item">
                        <label class="layui-form-label">课程名称</label>
                        <div class="layui-input-block w240 sel">
                            <div class="select-input">
                                <div class="name" data-name="请选择" id="courseName">请选择</div>
                                <em></em>
                                <div class="select-dropdown">
                                    <div class="all-selects">
                                        全选
                                    </div>
                                    <ul class="dropdown-lists" id="typeCover1">
                                    </ul>
                                </div>
                            </div>

                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">教学班名称</label>
                        <div class="layui-input-block w240">
                            <select name="className" lay-filter="className" lay-verify="required" lay-search="" id="typeCover2">
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">教学班组成</label>
                        <div class="layui-input-block w240">
                            <select name="classType" lay-filter="classType" lay-verify="required" lay-search="" id="typeCover3">
                            </select>
                        </div>
                    </div>
                    <!--<div class="layui-form-item">
                        <label class="layui-form-label">日期选择</label>
                        <div class="layui-input-block ">
                            <div class="times w240">
                                <input type="text" name="time" readonly="" placeholder="请选择" class="layui-input"
                                    id="startTime" lay-key="1">
                            </div>
                        </div>
                    </div>-->
                    <div class="layui-form-item">
                        <label class="layui-form-label">点名状态</label>
                        <div class="layui-input-block w240 sel">
                            <div class="select-input">
                                <div class="name" data-name="请选择" id="crstate">请选择</div>
                                <em></em>
                                <div class="select-dropdown">
                                    <div class="all-selects">
                                        全选
                                    </div>
                                    <ul class="dropdown-lists">
                                        <li id="statezc">
                                            <span stateId = "zc">正常</span>
                                        </li>
                                        <li th:each="state:${rollcallStateList}">
                                            <span th:attr="stateId=${state.type ==0?state.stateAlias:state.id}" th:text="${state.stateNamePc}"></span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <div class="button-list">
                    <div class="reset">重置</div>
                    <div class="searcher" lay-filter="formDemo">查询</div>
                </div>

            </div>
            <div class="table">
                <div class="detail" style="padding-top:14px;">
                    <span><em>正常：</em><i class="color1">0</i></span>
                    <span th:each="state,userStat:${rollcallStateList}"><em
                            th:inline="text">[[${state.stateNamePc}]]：</em><i
                            th:class="'color'+${userStat.count+1}">0</i></span>
                </div>
                <div class="tab-cons roll-call-record">
                    <table class="layui-table" id="materialTable" lay-filter="materialTable"></table>
                </div>
            </div>


        </div>
        <div id="coursePage"></div>
    </div>


</body>

<script th:src="${_CPR_}+'/elective/js/jquery-3.3.1.min.js'"></script>
<script src="/elective/rollcall/js/common1.js"></script>
<script th:inline="javascript">
    let pageIndex = 1;
    let pageSize = 10;
    var rollcallStateList = [[${rollcallStateList}]];
    var stuUid = [[${stuUid}]];
    var current = [[${current}]];
    var courseTime = [[${courseTime}]];
    layui.use(['table', 'jquery', 'laypage', 'laydate', 'form'], function () {

        var laypage = layui.laypage;
        var table = layui.table;
        var $ = layui.jquery;
        var layer = layui.layer;
        var laydate = layui.laydate;
        var form = layui.form;

        //日期选择
        /*laydate.render({
            elem: '#startTime',
            format: 'yyyy-MM-dd',
            type: 'date',
            min:[[${current.xnxq_xqkssj}]],
            max:[[${current.xnxq_xqjssj}]],
            range: '~'
        });*/

        $.ajax({
            type: "POST",
            url: "/elective/rollcall/getCourseListV1",
            dataType: 'json',
            success: function (data) {
                var html = "";
                if (data.status) {
                    for (var i = 0; i < data.list.length; i++) {
                        html += "<li> <span>" + data.list[i].mc + "</span></li>";
                    }
                }
                $("#typeCover1").html(html);
                layui.form.render("select");
                form.render();
            }
        });
        $.ajax({
            type: "POST",
            url: "/elective/rollcall/getJxbNameList",
            dataType: 'json',
            success: function (data) {
                var html = "<option value=''>请选择</option>";
                if (data.status) {
                    for (var i = 0; i < data.data.length; i++) {
                        html += "<option value='" + data.data[i].teachingClassCode + "'>" + data.data[i].teachingClassName + "</option>";
                    }
                }
                $("#typeCover2").html(html);
                layui.form.render("select");
            }
        });

        $.ajax({
            type: "POST",
            url: "/elective/rollcall/getRollcallLogJXBZC",
            dataType: 'json',
            success: function (data) {
                var html = "";
                html += "<option value=''>请选择</option>";
                for (var i = 0; i < data.kcs.length; i++) {
                    if (data.kcs[i].jxbzc == '') {
                        continue;
                    }
                    html += "<option value='" + data.kcs[i].jxbzc + "'>" + data.kcs[i].jxbzc + "</option>";
                }
                $("#typeCover3").html(html);
                layui.form.render("select");
            }
        });




        var table1 = table.render({
            elem: "#materialTable",
            id: 'materialTable',
            url: "/elective/rollcall/getStaticStudentsDetailPage",
            where:{
                xnxqh: current.xnxq_xnxqh,
                stuUid:stuUid,
                courseName:$("#courseName").text().replaceAll("请选择",""),
                jxbCode:$("#typeCover2").val(),
                jxbzc:$("#typeCover3").val(),
                // courseTime:$("#startTime").val(),
                courseTime:courseTime,
                crstate:$("#crstate").text().replaceAll("请选择",""),
            },
            cols: [
                [{
                        field: "course",
                        align: "center",
                        title: "课程名称",
                        width: 191
                    },
                    {
                        field: "teachingClassName",
                        align: "center",
                        title: "教学班名称",
                        width: 191
                    },
                    {
                        field: "jxbzc",
                        align: "center",
                        title: "教学班组成",
                        width: 191
                    },
                    {
                        field: "classDate",
                        align: "center",
                        title: "上课时间",
                        width: 191,
                        templet: function (d) {
                            // 第8周 周一 第6节
                            return "第"+d.zc+"周 周"+d.xq+" 第"+d.kj+"节";
                        }
                    },
                    {
                        field: "rollStatus",
                        align: "center",
                        title: "点名状态",
                        width: 191,
                        templet: function (d) {
                            let shtml = "";
                            if (d.cd == 0 && d.kk == 0 && d.zt == 0 && d.qj == 0 && d.state ==0) {
                                shtml += '<div class="normal1">正常</div>';
                            } else {
                                for (let i = 0; i < rollcallStateList.length; i++) {
                                    if (rollcallStateList[i].type == 0 && (rollcallStateList[i].stateAlias == 'cd'|| rollcallStateList[i].stateAlias == 'zt') && d.cd == 1&& d.zt == 1){
                                        shtml +='<div class="normal'+(i+2)+'">迟到,早退</div>';
                                        break;
                                    }else if ((rollcallStateList[i].type == 0 && rollcallStateList[i].stateAlias == 'cd' && d.cd == 1)||
                                        (rollcallStateList[i].type == 0 && rollcallStateList[i].stateAlias == 'kk' && d.kk == 1)||
                                        (rollcallStateList[i].type == 0 && rollcallStateList[i].stateAlias == 'zt' && d.zt == 1)||
                                        (rollcallStateList[i].type == 0 && rollcallStateList[i].stateAlias == 'qj' && d.qj == 1)||
                                        (rollcallStateList[i].type == 1 && rollcallStateList[i].id == d.state)){
                                        shtml +='<div class="normal'+(i+2)+'">'+rollcallStateList[i].stateNamePc+'</div>';
                                    }
                                }
                            }
                            return shtml
                        },
                    },
                    {
                        field: "val",
                        align: "center",
                        title: "评价",
                        minWidth: 200,
                    },

                ]
            ],
            done: function (res) {
                //分页
                laypage.render({
                    elem: 'coursePage',
                    curr: pageIndex,
                    groups: 5,
                    limit: pageSize,
                    limits: [10, 20, 30],
                    count: res.count,
                    layout: ['count', 'prev', 'page', 'next', 'limit', 'limits', 'skip'],
                    jump: function (obj, first) {
                        if (!first) {
                            pageIndex = obj.curr;
                            pageSize = obj.limit;
                            table.reload('materialTable', {
                                where:{
                                    xnxqh: current.xnxq_xnxqh,
                                    stuUid:stuUid,
                                    courseName:$("#courseName").text().replaceAll("请选择",""),
                                    jxbCode:$("#typeCover2").val(),
                                    jxbzc:$("#typeCover3").val(),
                                    // courseTime:$("#startTime").val(),
                                    courseTime:courseTime,
                                    crstate:$("#crstate").text().replaceAll("请选择",""),
                                    page:pageIndex,
                                    limit:pageSize
                                }
                            }); //只重载数据
                        }
                    }
                });
            }
        })


        //返回

        $(".main .top .title .back").click(function () {
            window.history.go(-1);
        })


        resizeHh();

        $('.filter-box .button-list .reset').click(function () {
            console.log("重置");

            form.val("myFormFilter", { // myFormFilter 是表单定义的lay-filter的值
                "course": '',
                "className": '',
                "classType": '',
            });
            // $("#startTime").val('');

            layui.form.render();
            let placeholdertext = $(
                ".filter-box .layui-form .layui-form-item .sel .select-input .name.ckd").attr(
                "data-name");
            $(".filter-box .layui-form .layui-form-item .sel .select-input .name.ckd").removeClass(
                "ckd").text(placeholdertext);
            $(".filter-box .layui-form .layui-form-item .sel .select-input .select-dropdown .dropdown-lists li")
                .removeClass("cur");
            $(".filter-box .layui-form .layui-form-item .sel .select-input .select-dropdown .all-selects.cur")
                .removeClass("cur").text("全选");
        });

        function resizeHh() {
            let hh = $(".roll-call-record").offset().top + 106;
            let cuurent = $(window).height() - hh;
            $(".roll-call-record").css("min-height", cuurent);
        }

        $(window).resize(function () {
            resizeHh();
        });

        $(".searcher").click(function () {
            pageIndex = 1;
            table.reload('materialTable', {
                where: {
                    xnxqh: current.xnxq_xnxqh,
                    stuUid:stuUid,
                    courseName:$("#courseName").text().replaceAll("请选择",""),
                    jxbCode:$("#typeCover2").val(),
                    jxbzc:$("#typeCover3").val(),
                    // courseTime:$("#startTime").val(),
                    courseTime:courseTime,
                    crstate:$("#crstate").text().replaceAll("请选择",""),
                    page:pageIndex,
                    limit:pageSize
                }
            }); //只重载数据
            getStuNum();
        })

        getStuNum();

        function getStuNum() {
            $.ajax({
                type: "POST",
                url: "/elective/rollcall/getStuDetailNum",
                data: {
                    xnxqh: current.xnxq_xnxqh,
                    stuUid:stuUid,
                    courseName:$("#courseName").text().replaceAll("请选择",""),
                    jxbCode:$("#typeCover2").val(),
                    jxbzc:$("#typeCover3").val(),
                    // courseTime:$("#startTime").val(),
                    courseTime:courseTime,
                    crstate:$("#crstate").text().replaceAll("请选择",""),
                },
                dataType: 'json',
                success: function (data) {
                    for (let i = 0; i < data.data.length; i++) {
                        $(".detail").find("span").eq(i).find("i").text(data.data[i]);
                    }
                }
            });
        }






    });
</script>

</html>