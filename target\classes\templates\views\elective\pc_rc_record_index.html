<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" li-content="IE=edge">
    <meta name="viewport" li-content="width=device-width, initial-scale=1.0">
    <title>微点名</title>
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/global.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/layui/css/layui.css'">
    <link rel="stylesheet" href="css/commonPC.css">
    <link rel="stylesheet" href="css/reset.css">
    <link rel="stylesheet" href="css/poup.css">
    <link rel="stylesheet" href="css/microdot-name.css">
    <script th:src="${_CPR_}+'/elective/layui/layui.js'"></script>
    <style>
        body .main .con .filter-box .oparate {
            display: flex;
            display: -webkit-flex;
            align-items: center;
            justify-content: flex-start;
            align-items: flex-start;
        }

        .layui-table-view .layui-table {
            width: 100%;
        }

        .layui-table-header .layui-table {
            width: 100%;
        }
    </style>

</head>

<body>
<div class="main">
    <div class="top">
        <div class="title">
            <div class="back" th:if="${js == '1'}"><a href="#" onclick="javascript:window.history.back();">返回</a></div>
            <div class="levelone">微点名</div>
            <div class="icon"></div>
            <div class="leveltwo">查看点名记录</div>
        </div>
    </div>
    <div class="con">
        <div class="filter-box">
            <div class="layui-form" action="">
                <div class="layui-form-item">
                    <label class="layui-form-label">课程名称</label>
                    <div class="layui-input-block w240">
                        <select name="typeCover1" lay-filter="typeCover1" id="typeCover1" lay-verify="required"
                                lay-search="">
                        </select>
                    </div>

                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">日期选择</label>
                    <div class="layui-input-block ">
                        <div class="times w360">
                            <input type="text" name="time" readonly="" placeholder="请选择" class="layui-input"
                                   id="startTime" lay-key="1">
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">教学班组成</label>
                    <div class="layui-input-block w240">
                        <select name="typeCover2" lay-filter="typeCover2" id="typeCover2" lay-verify="required"
                                lay-search="">
                        </select>
                    </div>

                </div>
                <div class="layui-form-item">
                    <div class="layui-input-block w240">
                        <div class="btns">
                            <div class="searchser">查询</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="oparate">
                <div class="export" id="export">导出</div>
                <div class="export" id="exportRecord"
                     style="background-color: #fff;color: #4d88ff;border: 1px solid #4d88ff;">导出记录
                </div>
            </div>
        </div>
        <div class="table">
            <div class="tab-cons">
                <table class="layui-table" id="materialTable" lay-filter="materialTable"></table>
            </div>

            <div id="coursePage"></div>
        </div>
        <div class="no-data" style="display:none;">
            <img src="images/no-datas.png" alt="">
            <p>当前时间无记录</p>
        </div>

    </div>
</div>


</body>
<script type="text/html" id="tmplToolBar">
    <div class="oprate-table">
        <div class="seek" lay-event="seek" style="color:#3A8BFF;cursor:pointer;">查看详情</div>
    </div>
</script>
<script type="text/html" id="statezcNum">
    <span lay-event="statezcNum" style="color: #3A8BFF;cursor: pointer;">{{= d.zcNum+"" }}</span>
</script>
<script type="text/html" id="statecdNum">
    <span lay-event="statecdNum" style="color: #3A8BFF;cursor: pointer;">{{= d.cd+"" }}</span>
</script>
<script type="text/html" id="statekkNum">
    <span lay-event="statekkNum" style="color: #3A8BFF;cursor: pointer;">{{= d.kk+"" }}</span>
</script>
<script type="text/html" id="stateqjNum">
    <span lay-event="stateqjNum" style="color: #3A8BFF;cursor: pointer;">{{= d.qj+"" }}</span>
</script>
<script type="text/html" id="stateztNum">
    <span lay-event="stateztNum" style="color: #3A8BFF;cursor: pointer;">{{= d.zt+"" }}</span>
</script>
<script th:src="${_CPR_}+'/elective/js/jquery-3.3.1.min.js'"></script>
<script th:inline="javascript">
    let pageIndex = 1;
    var pageSize = 10;
    var js = [[${js}]];
    var fid = [[${fid}]];
    var uid = [[${uid}]];
    var rollcallStateList = [[${rollcallStateList}]];
    layui.use(['table', 'jquery', 'laypage', 'laydate', 'form'], function () {

        var table = layui.table;
        var laypage = layui.laypage;
        var $ = layui.jquery;
        var layer = layui.layer;
        var laydate = layui.laydate;
        var form = layui.form;
        var now = new Date();
        var year = now.getFullYear();
        var month = now.getMonth() + 1;
        var day = now.getDate();

        var formattedDate = year + '-' + (month < 10 ? '0' + month : month) + '-' + (day < 10 ? '0' + day : day);

        //日期选择
        laydate.render({
            elem: '#startTime',
            format: 'yyyy-MM-dd',
            type: 'date',
            range: '~',
            value: formattedDate +' ~ ' +formattedDate,
        });

        $("#export").click(function () {
            $.ajax({
                type: 'post',
                url: "/elective/rollcall/exportAllRollcallLog",
                data: {
                    kc: $('select[name="typeCover1"] option:selected').val(),
                    jxbzc: $('select[name="typeCover2"] option:selected').val(),
                    courseTime: $("#startTime").val(),
                    js: js
                },
                dataType: 'json',
                success: function (data) {
                    layer.msg(data.msg);
                }
            });
            // location.href = "/elective/rollcall/exportAllRollcallLog?kc=" + $('select[name="typeCover1"] option:selected').val() + "&courseTime=" + $("#startTime").val()+"&js="+js;
        })
        $("#exportRecord").click(function () {
            layer.open({
                type: 2,  // 2表示弹出的是iframe，1表示弹出的是层
                offset: 'auto',
                title: [''],
                area: ['858px', '570px'],
                scrollbar: true,
                content: "/downloadCenter/list.html?fid=" + fid + "&uid=" + uid + "&formId=40000001",   // 弹出iframe的页面链接
                btn: '',
                shade: 0.3 //显示遮罩
            });
        })
        $(".searchser").click(function () {
            pageIndex = 1;
            table.reload('materialTable', {
                where: {
                    kc: $('select[name="typeCover1"] option:selected').val(),
                    jxbzc: $('select[name="typeCover2"] option:selected').val(),
                    courseTime: $("#startTime").val(),
                    curPage: pageIndex,
                    pageSize: pageSize,
                    js: js
                }
            }); //只重载数据
        })


        //表格
        var kohh;

        $(window).resize(function () {
            computedHh();
        })
        var titleArr = [{
            field: "course",
            align: "center",
            title: "课程名称",
            minWidth: 200
        },
            {
                field: "jxbzc",
                align: "center",
                title: "教学班组成",
                minWidth: 200
            },
            {
                align: "center",
                title: "上课时间",
                minWidth: 200,
                templet: function (d) {
                    return "第" + d.zc + "周 周" + d.xq + " 第" + d.kj + "节";
                }
            },
            {
                field: "teacherName",
                align: "center",
                title: "授课老师",
                minWidth: 80,
            },
            {
                // field: "zcNum",
                field: "options",
                align: "center",
                title: "正常",
                toolbar: "#statezcNum",
                minWidth: 100
            },];

        for (let i = 0; i < rollcallStateList.length; i++) {
            let toolbarName = "state";
            if (rollcallStateList[i].type==1){
                toolbarName +=rollcallStateList[i].id;
            }else {
                toolbarName +=rollcallStateList[i].stateAlias;
            }
            toolbarName +="Num";
            titleArr.push({
                field: "options",
                align: "center",
                title: rollcallStateList[i].stateNamePc,
                // toolbar: "#"+toolbarName,
                minWidth: 100,
                templet: function (d) {
                    return "<span lay-event='"+toolbarName+"' style='color: #3A8BFF;cursor: pointer'>"+d.stateNum[i]+"</span>";
                }
            });
        }


        titleArr.push({
                align: "center",
                title: "出勤率",
                minWidth: 100,
                templet: function (d) {
                    return ((d.num - d.qj - d.kk) * 100 / d.num).toFixed(1) + "%";
                }
            },
            {
                field: "updateTime",
                align: "center",
                title: "点名时间",
                minWidth: 220
            },
            {
                field: "options",
                align: "center",
                title: "操作",
                toolbar: "#tmplToolBar",
                minWidth: 150,
                fixed: "right",
            });

        var table1 = table.render({
            elem: "#materialTable",
            id: 'materialTable',
            url: '/elective/rollcall/getRollcallLogJXBPc', //数据接口
            where: {
                kc: $('select[name="typeCover1"] option:selected').val(),
                jxbzc: $('select[name="typeCover2"] option:selected').val(),
                name: "", courseTime: $("#startTime").val(), curPage: pageIndex,
                pageSize: pageSize,
                js: js
            },
            height: kohh,
            cols: [
                titleArr
            ],
            done: function (res) {
                console.log(res)
                if (res.count == 0) {
                    $(".table").hide();
                    $(".no-data").show();
                    return;
                } else {
                    $(".table").show();
                    $(".no-data").hide();
                }

                computedHh();

                laypage.render({
                    elem: 'coursePage',
                    curr: pageIndex,
                    groups: 5,
                    limit: pageSize,
                    limits: [10, 20, 30],
                    count: res.count,

                    layout: ['count', 'prev', 'page', 'next', 'limit', 'limits', 'skip'],
                    jump: function (obj, first) {
                        if (!first) {
                            pageIndex = obj.curr;
                            pageSize = obj.limit;
                            console.log(obj)
                            table.reload('materialTable', {
                                where: {
                                    kc: $('select[name="typeCover1"] option:selected').val(),
                                    jxbzc: $('select[name="typeCover2"] option:selected').val(),
                                    courseTime: $("#startTime").val(),
                                    curPage: pageIndex,
                                    pageSize: pageSize,
                                    js: js
                                }
                            }); //只重载数据
                        }
                    }
                });
            }
        })


        function computedHh() {
            let wrapperTop = $(".tab-cons").offset().top;
            kohh = $(window).height() - wrapperTop - 106;
            $("body .main .con .table .tab-cons").css("min-height", kohh);
        }


        table.on("tool(materialTable)", function (obj) {
            var data = obj.data; //获得当前行数据
            var url = "/elective/rollcall/jxbIndex?model=pc&jxb=" + data.teachingClassCode + "&jxbmc=" + data.teachingClassName + "&courseTime=" + data.classDate + "&kj=" + data.kj + "&courseName=" + data.course + "&zc=" + data.zc + "&xq=" + data.xq;
            var req = false;
            if (obj.event === "seek") {
                req = true;
            } else if (obj.event.startsWith("state") && obj.event.endsWith("Num")) {
                url += "&state=" + (obj.event.replace("state", "").replace("Num", ""));
                req = true;
            }
            if (req) {
                location.href = url;
            }
        })
        getKc();

        function getKc() {
            $.ajax({
                type: "POST",
                url: "/elective/rollcall/getRollcallLogKC",
                dataType: 'json',
                success: function (data) {
                    var html = "";
                    html += "<option value=''>请选择</option>";
                    for (var i = 0; i < data.kcs.length; i++) {
                        html += "<option value='" + data.kcs[i].course + "'>" + data.kcs[i].course + "</option>";
                    }
                    $("#typeCover1").html(html);
                    layui.form.render("select");
                }
            });
        }


        getJxbzc();

        function getJxbzc() {
            $.ajax({
                type: "POST",
                url: "/elective/rollcall/getRollcallLogJXBZC",
                dataType: 'json',
                success: function (data) {
                    var html = "";
                    html += "<option value=''>请选择</option>";
                    for (var i = 0; i < data.kcs.length; i++) {
                        if (data.kcs[i].jxbzc == '') {
                            continue;
                        }
                        html += "<option value='" + data.kcs[i].jxbzc + "'>" + data.kcs[i].jxbzc + "</option>";
                    }
                    $("#typeCover2").html(html);
                    layui.form.render("select");
                }
            });
        }

    });
</script>

</html>