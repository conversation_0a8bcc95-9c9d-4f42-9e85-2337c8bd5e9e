<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教师无课查询</title>
    <link rel="stylesheet" th:href="@{../css/basic/global.css}">
    <link rel="stylesheet" th:href="@{../css/basic/noClassSearch.css}">
</head>

<body>
    <div class="main">
        <div class="top">
            <h4>老师空闲查询</h4>
        </div>
        <div class="form-con">
            <div class="sel-box">
                <div class="sel-row">
                    <div class="sel-item" fieldAlias = "jsjbxx_js" id="jsjbxx_js">
                        <div class="sel-title"><span></span>选择角色</div>
                        <div class="sel" style="margin-right:0;">
                            <div class="select-input">
                                <div class="name" data-name="请选择角色">请选择角色</div>
                                <i></i>
                                <div class="select-dropdown">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <div class="all-selects">
                                        全选
                                    </div>
                                    <ul class="dropdown-lists">
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="sel-item" fieldAlias = "jsjbxx_yx" id="jsjbxx_yx">
                        <div class="sel-title"><span></span>选择系部</div>
                        <div class="sel" style="margin-right:0;">
                            <div class="select-input">
                                <div class="name" data-name="请选择系部">请选择系部</div>
                                <i></i>
                                <div class="select-dropdown">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <div class="all-selects">
                                        全选
                                    </div>
                                    <ul class="dropdown-lists">
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="sel-row">
                    <div class="sel-item" fieldAlias = "jsjbxx_zc" id="jsjbxx_zc">
                        <div class="sel-title"><span>*</span>选择周次</div>
                        <div class="sel" style="margin-right:0;">
                            <div class="select-input">
                                <div class="name" data-name="请选择周次">请选择周次</div>
                                <i></i>
                                <div class="select-dropdown">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>

                                    <ul class="dropdown-lists dropdown-lists-single">
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="sel-item" fieldAlias = "jsjbxx_xm" id="jsjbxx_xm">
                        <div class="sel-title"><span>*</span>选择老师</div>
                        <div class="sel" style="margin-right:0;">
                            <div class="select-input">
                                <div class="name" data-name="请选择老师">请选择老师</div>
                                <i></i>
                                <div class="select-dropdown">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <div class="all-selects">
                                        全选
                                    </div>
                                    <ul class="dropdown-lists">
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="sel-item" fieldAlias = "jsjbxx_jsgh" id="jsjbxx_jsgh">
                        <div class="sel-title"><span>*</span>老师工号</div>
                        <div class="sel" style="margin-right:0;">
                            <div class="select-input">
                                <div class="name" data-name="请选择老师工号">请选择老师工号</div>
                                <i></i>
                                <div class="select-dropdown">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <div class="all-selects">
                                        全选
                                    </div>
                                    <ul class="dropdown-lists">
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-btn">
                <button class="btn btn-search">查询</button>
                <button class="btn btn-reset">导出</button>
            </div>
            <div class="btns">
                <div class="resetting">重置选项</div>
                <div class="export-record">导出记录</div>
            </div>
        </div>
        <div class="table">
            <table>
                <thead>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>
    <div class="no-data" style="position: absolute;left: 50%;top: 50%;">
        <img src="../images/timetable/no-data.png" alt="">
    </div>
    <!--导出记录-->
    <div class="popup date-edit" style="display:none;">
        <div class="window">
            <div class="p-title">
                <span>导出记录</span>
            </div>
            <div class="p-content clearfixs">
                <div class="con">
                    <div class="total">共<i>4</i>条数据</div>
                    <div class="table">
                        <table class="layui-table" id="materialTable" lay-filter="materialTable">
                        </table>
                        <div class="no-data" style="display:none;"></div>
                    </div>
                    <div id="coursePages" style="margin-bottom: 0; text-align: center; "></div>
                </div>
            </div>
            <div class="p-btns">
                <div class="btn cancel">关闭</div>
                <div class="btn sure">刷新</div>
            </div>
        </div>
    </div>
</body>
<script>
    let fid = "[[${fid}]]";
    let uid = "[[${uid}]]";
    let _VR_ = "[[${_VR_}]]";
</script>
<script th:src="@{../js/jquery-3.3.1.min.js}"></script>
<script th:src="@{../js/my.util.js}"></script>
<script th:src="@{../js/basic/teacher_idle.js(v=${new java.util.Date().getTime()})}"></script>
<script th:src="@{~/plugin/layui/layui.js}"></script>
</html>