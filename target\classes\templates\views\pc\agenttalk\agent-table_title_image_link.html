<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能体内容-表格加应用</title>
    <link rel="stylesheet" type="text/css" th:href="@{~/css/agenttalk/global.css}"/>
    <link rel="stylesheet" type="text/css" th:href="@{~/css/agenttalk/index.css?t=2}"/>

</head>

<body>
<div id="table-title-image-link" class="chat-item-content-message">
    <div class="content">
        <div class="table">
            <h3>为您找到以下内容：</h3>
            <div class="t-con">
                <table>
                    <thead>
                    <tr>
                        <th th:each="header : ${data.data.headers}" th:text="${header}"></th>
                        <th th:if="${data.data.containsKey('linksName') && data.data.linksName!= '' }" >操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr th:each="row,iterStat : ${data.data.rows}">
                        <td th:each="value : ${row}" th:text="${value}"></td>
                        <td th:if="${data.data.containsKey('linksName') && data.data.linksName!= ''}" >
                            <a target="_blank" th:href="${data.data.links.get(iterStat.index)}" th:text="${data.data.linksName}"></a>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <h3 id="table-title-image-link-prompt" style="margin-top: 20px;">点击下方应用添加禁排老师</h3><a
                th:href="${data.data.appLink}"
                class="item" target="_blank">
            <div class="img"><img th:src="${data.data.imageUrl}" alt=""></div>
            <div id="table-title-image-link-title" class="name" th:text="${data.data.title}"></div>
        </a>
        </div>
    </div>

</div>


</body>
<script th:src="@{~/js/agenttalk/jquery-3.3.1.min.js}"></script>
<script th:src="@{~/js/base.js}"></script>
<script>
    $(function () {
        function sendHeight() {
            const message = {
                type: "CXBOT:resizeMessage",
                data: {
                    messageId: '[[${botMsg}]]', // 请从页面地址栏中获取，字段为 bot_msg
                    height: document.documentElement.scrollHeight, // 允许携带 px 作为单位
                    width: '100%' // 仅支持百分比
                }
            };
            window.parent.postMessage(message, '[[${botReferer}]]');
            console.log("宽高参数:"+JSON2Str(message))
        }
        sendHeight();
    })
</script>
</html>