<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>同步组织架构</title>
    <link rel="stylesheet" th:href="@{${_CPR_+_VR_}+'/css/basic/syncSet.css'(v=${new java.util.Date().getTime()})}">
</head>

<body>
<div class="dialog-wrap">
    <div class="dialog" id="syncSetDialog">
        <div class="dialog-title">
            <p>只同步勾选字段到组织架构</p>
        </div>
        <div class="dialog-con">
            <ul>
                <li>
                    <h5>部门</h5><span></span>
                </li>
                <li>
                    <h5>系部</h5><span></span>
                </li>
                <li>
                    <h5>教研室</h5><span></span>
                </li>
            </ul>
        </div>
        <div class="dialog-btn">
            <button>确定</button>
        </div>
    </div>
</div>
</body>
<script th:src="${_CPR_+_VR_+'/js/jquery-3.3.1.min.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/my.util.js'}"></script>
<script th:inline="javascript">
    $(document).ready(function () {
        const _VR_ = [[${_VR_}]];
        let fid = [[${fid}]];
        let info = [[${info}]];
        if (info && info.detail) {
            const detail = JSON.parse(info.detail);
            $('.dialog-con ul li span').each(function (index, element) {
                $(element).toggleClass('active', detail[`dept${index + 1}`]);
            });
        }
        $('.dialog-close').click(function () {
            $('.dialog-wrap').hide();
        })
        $('.dialog-con ul li').click(function () {
            $(this).toggleClass('active');
            $(this).find('span').toggleClass('active');
        })
        $(".dialog-btn").click(function () {
            let len = $('.dialog-con ul .active').length;
            if (len === 0) {
                U.fail("请勾选字段");
                return false;
            }
            let detail = {};
            $('.dialog-con ul li span').each((index, element) => {
                detail[`dept${index + 1}`] = $(element).hasClass("active");
            });
            $.post(_VR_ + "/basic/pop/syncOrganizationSet", {
                fid: fid,
                id: info ? info.id : "",
                detail: JSON.stringify(detail)
            }, function (result) {
                result.status ? U.success(result.msg) : U.fail(result.msg);
                setTimeout(successFun, 2000);
            });
        })

        function successFun() {
            window.parent.postMessage(JSON.stringify({action: 1}), "*");
        }
    })
</script>

</html>