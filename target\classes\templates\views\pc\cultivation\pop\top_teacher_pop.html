<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <title>排课系统</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/global.css'}"/>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/reset.css'}"/>
    <link rel="stylesheet" th:href="@{${_CPR_+_VR_}+'/css/cultivation/common.css'(v=${new java.util.Date().getTime()})}"/>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/plugin/layui/css/layui.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/reset.css'}"/>
    <link rel="stylesheet" th:href="@{${_CPR_+_VR_}+'/css/cultivation/teacherPop.css'(v=${new java.util.Date().getTime()})}"/>
    <script th:src="${_CPR_+_VR_+'/plugin/layui/layui.js'}"></script>
    <style>
        body{
            padding:0;
        }
        .sel-item {
            margin-bottom: 20px; /* 统一垂直间距 */
        }

        .sel-title {
            font-size: 14px;
            color: #333;
            margin-bottom: 8px;
        }
    </style>
</head>

<body>
<div class="main">
    <div id="editPoups" class="popup" style="display:block;">
        <div class="popup-con">
            <div class="illustrate">
                <ul>
                    <li class="cur" th:if="${formField=='teacher' || #strings.isEmpty(formField)}">使用说明：不勾选数据直接点击
                        [确定] 提交，即可清空已有授课教师。
                    </li>
                    <li class="cur" th:if="${formField=='assistant'}">使用说明：不勾选数据直接点击 [确定]
                        提交，即可清空已有助教。
                    </li>
                    <li class="cur" th:if="${formField=='scoreTeacher'}">使用说明：不勾选数据直接点击 [确定]
                        提交，即可清空已有成绩录入教师。
                    </li>
                </ul>
            </div>
            <div class="content">
                <div class="oprate" th:if="${formField!='scoreTeacher' || #strings.isEmpty(formField)}">
                    <div class="invisible-switch">
                        <div class="name">是否同步修改成绩录入教师</div>
                        <div class="switc-con">
                            <div th:if="${formField=='teacher' || #strings.isEmpty(formField)}"
                                 class="switch switch-open"><span></span></div>
                            <div th:if="${formField=='assistant'}" class="switch"><span></span></div>
                        </div>
                    </div>
                </div>
                <div class="screen-box">
                    <div class="sel-list">
                        <div class="sel-item">
                            <div class="sel-title">教师编号</div>
                            <div class="sel" style="margin-right:0;">
                                <div class="select-input">
                                    <input type="text" placeholder="请输入" id="jsbh" name="jsbh">
                                </div>
                            </div>
                        </div>
                        <div class="sel-item">
                            <div class="sel-title">教师姓名</div>
                            <div class="sel" style="margin-right:0;">
                                <div class="select-input">
                                    <input type="text" placeholder="请输入" id="xm" name="xm">
                                </div>
                            </div>
                        </div>
                        <div class="sel-item">
                            <div class="sel-title">系部</div>
                            <div class="sel" style="margin-right:0;">
                                <div class="select-input">
                                    <div class="name" data-name="请选择" formAlias = "yxsj" fieldAlias = "yxsj_yxmc">请选择</div>
                                    <i></i>
                                    <div class="select-dropdown">
                                        <div class="search">
                                            <input type="text" placeholder="搜索">
                                            <span></span>
                                        </div>
                                        <div class="all-selects">
                                            全选
                                        </div>
                                        <ul class="dropdown-lists" name="xb">
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="sel-item">
                            <div class="sel-title">教研组</div>
                            <div class="sel" style="margin-right:0;">
                                <div class="select-input">
                                    <div class="name" data-name="请选择" formAlias = "jyssj" fieldAlias = "jyssj_jysmc">请选择</div>
                                    <i></i>
                                    <div class="select-dropdown">
                                        <div class="search">
                                            <input type="text" placeholder="搜索">
                                            <span></span>
                                        </div>
                                        <div class="all-selects">
                                            全选
                                        </div>
                                        <ul class="dropdown-lists">
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="btns">
                        <div class="reset">重置</div>
                        <div class="screen">筛选</div>
                    </div>
                </div>
                <div class="p-table">
                    <table id="teacherTable" lay-filter="teacherTable"></table>
                    <div class="fixed-bottom">
                        <div class="selectAll">
                            <span>选中所有数据</span>
                        </div>
                        <div class="selected">已选中 <i>0</i> 条</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bottom">
            <button type="reset" class="layui-btn exam-cancle">
                取消
            </button>
            <button class="layui-btn exam-sure">确定</button>
        </div>
    </div>
</div>

</body>
<script th:src="${_CPR_+_VR_+'/js/jquery-3.3.1.min.js'}"></script>
<script th:src="@{${_CPR_+_VR_}+'/js/cultivation/top_teacher_pop.js'(v=${new java.util.Date().getTime()})}"></script>
<script th:src="${_CPR_+_VR_+'/js/cultivation/common.js'}"></script>
<script>
    const _VR_ = "[[${_VR_}]]";
    let fid = "[[${formTopBtnBO.fid}]]";
    let uid = "[[${formTopBtnBO.uid}]]";
    let queryId = "[[${formTopBtnBO.queryId}]]";
    let formField = "[[${formField}]]";
    let semester = "[[${semester}]]";
</script>

</html>