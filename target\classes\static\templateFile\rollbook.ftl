<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<?mso-application progid="Word.Document"?>
<pkg:package xmlns:pkg="http://schemas.microsoft.com/office/2006/xmlPackage">
    <pkg:part pkg:name="/_rels/.rels" pkg:contentType="application/vnd.openxmlformats-package.relationships+xml">
        <pkg:xmlData>
            <Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
                <Relationship Id="rId4"
                              Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument"
                              Target="word/document.xml"/>
                <Relationship Id="rId2"
                              Type="http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties"
                              Target="docProps/core.xml"/>
                <Relationship Id="rId1"
                              Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties"
                              Target="docProps/app.xml"/>
                <Relationship Id="rId3"
                              Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties"
                              Target="docProps/custom.xml"/>
            </Relationships>
        </pkg:xmlData>
    </pkg:part>
    <pkg:part pkg:name="/word/_rels/document.xml.rels"
              pkg:contentType="application/vnd.openxmlformats-package.relationships+xml">
        <pkg:xmlData>
            <Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
                <Relationship Id="rId8"
                              Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/fontTable"
                              Target="fontTable.xml"/>
                <Relationship Id="rId7" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme"
                              Target="theme/theme1.xml"/>
                <Relationship Id="rId6"
                              Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/footer"
                              Target="footer1.xml"/>
                <Relationship Id="rId5"
                              Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/header"
                              Target="header1.xml"/>
                <Relationship Id="rId4"
                              Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/endnotes"
                              Target="endnotes.xml"/>
                <Relationship Id="rId3"
                              Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/footnotes"
                              Target="footnotes.xml"/>
                <Relationship Id="rId2"
                              Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/settings"
                              Target="settings.xml"/>
                <Relationship Id="rId1"
                              Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles"
                              Target="styles.xml"/>
            </Relationships>
        </pkg:xmlData>
    </pkg:part>
    <pkg:part pkg:name="/word/document.xml"
              pkg:contentType="application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml">
        <pkg:xmlData>
            <w:document xmlns:wpc="http://schemas.microsoft.com/office/word/2010/wordprocessingCanvas"
                        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                        xmlns:o="urn:schemas-microsoft-com:office:office"
                        xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships"
                        xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math"
                        xmlns:v="urn:schemas-microsoft-com:vml"
                        xmlns:wp14="http://schemas.microsoft.com/office/word/2010/wordprocessingDrawing"
                        xmlns:wp="http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing"
                        xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main"
                        xmlns:w14="http://schemas.microsoft.com/office/word/2010/wordml"
                        xmlns:w10="urn:schemas-microsoft-com:office:word"
                        xmlns:w15="http://schemas.microsoft.com/office/word/2012/wordml"
                        xmlns:wpg="http://schemas.microsoft.com/office/word/2010/wordprocessingGroup"
                        xmlns:wpi="http://schemas.microsoft.com/office/word/2010/wordprocessingInk"
                        xmlns:wne="http://schemas.microsoft.com/office/word/2006/wordml"
                        xmlns:wps="http://schemas.microsoft.com/office/word/2010/wordprocessingShape"
                        xmlns:wpsCustomData="http://www.wps.cn/officeDocument/2013/wpsCustomData"
                        mc:Ignorable="w14 w15 wp14">
                <#assign data_index=0>
                <#list courseInfoMap?keys as course>
                    <#assign data_index++>
                    <w:body>
                        <w:p>
                            <w:pPr>
                                <w:spacing w:line="264" w:lineRule="auto"/>
                                <w:rPr>
                                    <w:rFonts w:ascii="Arial"/>
                                    <w:sz w:val="21"/>
                                </w:rPr>
                            </w:pPr>
                            <w:bookmarkStart w:id="0" w:name="_GoBack"/>
                            <w:bookmarkEnd w:id="0"/>
                        </w:p>
                        <w:p>
                            <w:pPr>
                                <w:spacing w:line="264" w:lineRule="auto"/>
                                <w:rPr>
                                    <w:rFonts w:ascii="Arial"/>
                                    <w:sz w:val="21"/>
                                </w:rPr>
                            </w:pPr>
                        </w:p>
                        <w:p>
                            <w:pPr>
                                <w:spacing w:line="265" w:lineRule="auto"/>
                                <w:rPr>
                                    <w:rFonts w:ascii="Arial"/>
                                    <w:sz w:val="21"/>
                                </w:rPr>
                            </w:pPr>
                        </w:p>
                        <w:p>
                            <w:pPr>
                                <w:spacing w:before="56" w:line="378" w:lineRule="exact"/>
                                <w:ind w:left="800"/>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:hAnsi="宋体" w:eastAsia="宋体" w:cs="宋体"/>
                                    <w:sz w:val="17"/>
                                    <w:szCs w:val="17"/>
                                </w:rPr>
                            </w:pPr>
                            <w:r>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:hAnsi="宋体" w:eastAsia="宋体" w:cs="宋体"/>
                                    <w:spacing w:val="3"/>
                                    <w:position w:val="15"/>
                                    <w:sz w:val="17"/>
                                    <w:szCs w:val="17"/>
                                </w:rPr>
                                <w:t>承担单位:${course?split("|")[2]}</w:t>
                            </w:r>
                        </w:p>
                        <w:p>
                            <w:pPr>
                                <w:spacing w:line="240" w:lineRule="auto"/>
                                <w:ind w:left="799"/>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:hAnsi="宋体" w:eastAsia="宋体" w:cs="宋体"/>
                                    <w:sz w:val="17"/>
                                    <w:szCs w:val="17"/>
                                </w:rPr>
                            </w:pPr>
                            <w:r>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:hAnsi="宋体" w:eastAsia="宋体" w:cs="宋体"/>
                                    <w:spacing w:val="3"/>
                                    <w:sz w:val="17"/>
                                    <w:szCs w:val="17"/>
                                </w:rPr>
                                <w:t>任课教师:${course?split("|")[5]}</w:t>
                            </w:r>
                        </w:p>
                        <w:p>
                            <w:pPr>
                                <w:spacing w:line="14" w:lineRule="auto"/>
                                <w:rPr>
                                    <w:rFonts w:ascii="Arial"/>
                                    <w:sz w:val="2"/>
                                </w:rPr>
                            </w:pPr>
                            <w:r>
                                <w:rPr>
                                    <w:rFonts w:ascii="Arial" w:hAnsi="Arial" w:eastAsia="Arial" w:cs="Arial"/>
                                    <w:sz w:val="2"/>
                                    <w:szCs w:val="2"/>
                                </w:rPr>
                                <w:br w:type="column"/>
                            </w:r>
                        </w:p>
                        <w:p>
                            <w:pPr>
                                <w:spacing w:before="49" w:line="263" w:lineRule="auto"/>
                                <w:ind w:left="1132" w:right="2545" w:hanging="24"/>
                                <w:outlineLvl w:val="0"/>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:hAnsi="宋体" w:eastAsia="宋体" w:cs="宋体"/>
                                    <w:sz w:val="18"/>
                                    <w:szCs w:val="18"/>
                                </w:rPr>
                            </w:pPr>
                            <w:r>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:hAnsi="宋体" w:eastAsia="宋体" w:cs="宋体"/>
                                    <w:spacing w:val="3"/>
                                    <w:sz w:val="26"/>
                                    <w:szCs w:val="26"/>
                                </w:rPr>
                                <w:t>上课点名册</w:t>
                            </w:r>
                        </w:p>
                        <w:p>
                            <w:pPr>
                                <w:spacing w:line="190" w:line-rule="auto"/>
                                <w:ind w:left="1133"/>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:cs="宋体" w:hint="fareast"/>
                                    <w:sz w:val="18"/>
                                    <w:sz-cs w:val="18"/>
                                    <w:lang w:val="EN-US" w:fareast="ZH-CN"/>
                                </w:rPr>
                            </w:pPr>
                            <w:r>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:cs="宋体" w:hint="default"/>
                                    <w:spacing w:val="21"/>
                                    <w:sz w:val="18"/>
                                    <w:sz-cs w:val="18"/>
                                </w:rPr>
                                <w:t>${course?split("|")[1]}</w:t>
                            </w:r>
                        </w:p>
                        <w:p>
                            <w:pPr>
                                <w:spacing w:before="176" w:line="224" w:lineRule="auto"/>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:hAnsi="宋体" w:eastAsia="宋体" w:cs="宋体"/>
                                    <w:sz w:val="17"/>
                                    <w:szCs w:val="17"/>
                                </w:rPr>
                            </w:pPr>
                            <w:r>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:hAnsi="宋体" w:eastAsia="宋体" w:cs="宋体"/>
                                    <w:spacing w:val="2"/>
                                    <w:sz w:val="17"/>
                                    <w:szCs w:val="17"/>
                                </w:rPr>
                                <w:t>课程:${course?split("|")[3]}</w:t>
                            </w:r>
                        </w:p>
                        <w:p>
                            <w:pPr>
                                <w:spacing w:before="171" w:line="240" w:lineRule="auto"/>
                                <w:ind w:left="2"/>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:hAnsi="宋体" w:eastAsia="宋体" w:cs="宋体"/>
                                    <w:sz w:val="17"/>
                                    <w:szCs w:val="17"/>
                                </w:rPr>
                            </w:pPr>
                            <w:r>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:hAnsi="宋体" w:eastAsia="宋体" w:cs="宋体"/>
                                    <w:spacing w:val="3"/>
                                    <w:sz w:val="17"/>
                                    <w:szCs w:val="17"/>
                                </w:rPr>
                                <w:t>上课班级:${course?split("|")[6]}</w:t>
                            </w:r>
                        </w:p>
                        <w:p>
                            <w:pPr>
                                <w:spacing w:line="14" w:lineRule="auto"/>
                                <w:rPr>
                                    <w:rFonts w:ascii="Arial"/>
                                    <w:sz w:val="2"/>
                                </w:rPr>
                            </w:pPr>
                            <w:r>
                                <w:rPr>
                                    <w:rFonts w:ascii="Arial" w:hAnsi="Arial" w:eastAsia="Arial" w:cs="Arial"/>
                                    <w:sz w:val="2"/>
                                    <w:szCs w:val="2"/>
                                </w:rPr>
                                <w:br w:type="column"/>
                            </w:r>
                        </w:p>
                        <w:p>
                            <w:pPr>
                                <w:spacing w:line="264" w:lineRule="auto"/>
                                <w:rPr>
                                    <w:rFonts w:ascii="Arial"/>
                                    <w:sz w:val="21"/>
                                </w:rPr>
                            </w:pPr>
                        </w:p>
                        <w:p>
                            <w:pPr>
                                <w:spacing w:line="264" w:lineRule="auto"/>
                                <w:rPr>
                                    <w:rFonts w:ascii="Arial"/>
                                    <w:sz w:val="21"/>
                                </w:rPr>
                            </w:pPr>
                        </w:p>
                        <w:p>
                            <w:pPr>
                                <w:spacing w:line="264" w:lineRule="auto"/>
                                <w:rPr>
                                    <w:rFonts w:ascii="Arial"/>
                                    <w:sz w:val="21"/>
                                </w:rPr>
                            </w:pPr>
                        </w:p>
                        <w:p>
                            <w:pPr>
                                <w:spacing w:before="55" w:line="225" w:lineRule="auto"/>
                                <w:ind w:left="2"/>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:hAnsi="宋体" w:eastAsia="宋体" w:cs="宋体"/>
                                    <w:sz w:val="17"/>
                                    <w:szCs w:val="17"/>
                                </w:rPr>
                            </w:pPr>
                            <w:r>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:hAnsi="宋体" w:eastAsia="宋体" w:cs="宋体"/>
                                    <w:sz w:val="17"/>
                                    <w:szCs w:val="17"/>
                                </w:rPr>
                                <w:t>学分:${course?split("|")[4]}</w:t>
                            </w:r>
                        </w:p>
                        <w:p>
                            <w:pPr>
                                <w:spacing w:before="171" w:line="240" w:lineRule="auto"/>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:hAnsi="宋体" w:eastAsia="宋体" w:cs="宋体"/>
                                    <w:sz w:val="17"/>
                                    <w:szCs w:val="17"/>
                                </w:rPr>
                            </w:pPr>
                            <w:r>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:hAnsi="宋体" w:eastAsia="宋体" w:cs="宋体"/>
                                    <w:spacing w:val="2"/>
                                    <w:sz w:val="17"/>
                                    <w:szCs w:val="17"/>
                                </w:rPr>
                                <w:t>上课人数:${course?split("|")[7]}</w:t>
                            </w:r>
                        </w:p>
                        <w:p>
                            <w:pPr>
                                <w:spacing w:line="189" w:lineRule="auto"/>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:hAnsi="宋体" w:eastAsia="宋体" w:cs="宋体"/>
                                    <w:sz w:val="17"/>
                                    <w:szCs w:val="17"/>
                                </w:rPr>
                                <w:sectPr>
                                    <w:pgSz w:w="16839" w:h="11900"/>
                                    <w:pgMar w:top="608" w:right="587" w:bottom="0" w:left="987" w:header="0" w:footer="0"
                                             w:gutter="0"/>
                                    <w:cols w:equalWidth="0" w:num="3">
                                        <w:col w:w="5769" w:space="100"/>
                                        <w:col w:w="4971" w:space="100"/>
                                        <w:col w:w="4325"/>
                                    </w:cols>
                                </w:sectPr>
                            </w:pPr>
                        </w:p>
                        <w:p>
                            <w:pPr>
                                <w:spacing w:line="106" w:lineRule="exact"/>
                            </w:pPr>
                        </w:p>
                        <w:tbl>
                            <w:tblPr>
                                <w:tblStyle w:val="5"/>
                                <w:tblW w:w="15253" w:type="dxa"/>
                                <w:tblInd w:w="5" w:type="dxa"/>
                                <w:tblBorders>
                                    <w:top w:val="single" w:color="000000" w:sz="4" w:space="0"/>
                                    <w:left w:val="single" w:color="000000" w:sz="4" w:space="0"/>
                                    <w:bottom w:val="single" w:color="000000" w:sz="4" w:space="0"/>
                                    <w:right w:val="single" w:color="000000" w:sz="4" w:space="0"/>
                                    <w:insideH w:val="single" w:color="000000" w:sz="4" w:space="0"/>
                                    <w:insideV w:val="single" w:color="000000" w:sz="4" w:space="0"/>
                                </w:tblBorders>
                                <w:tblLayout w:type="fixed"/>
                                <w:tblCellMar>
                                    <w:top w:w="0" w:type="dxa"/>
                                    <w:left w:w="0" w:type="dxa"/>
                                    <w:bottom w:w="0" w:type="dxa"/>
                                    <w:right w:w="0" w:type="dxa"/>
                                </w:tblCellMar>
                            </w:tblPr>
                            <w:tblGrid>
                                <w:gridCol w:w="2884"/>
                                <w:gridCol w:w="1206"/>
                                <w:gridCol w:w="930"/>
                                <w:gridCol w:w="784"/>
                                <w:gridCol w:w="494"/>
                                <w:gridCol w:w="494"/>
                                <w:gridCol w:w="494"/>
                                <w:gridCol w:w="494"/>
                                <w:gridCol w:w="508"/>
                                <w:gridCol w:w="494"/>
                                <w:gridCol w:w="494"/>
                                <w:gridCol w:w="494"/>
                                <w:gridCol w:w="508"/>
                                <w:gridCol w:w="494"/>
                                <w:gridCol w:w="494"/>
                                <w:gridCol w:w="494"/>
                                <w:gridCol w:w="508"/>
                                <w:gridCol w:w="494"/>
                                <w:gridCol w:w="494"/>
                                <w:gridCol w:w="494"/>
                                <w:gridCol w:w="494"/>
                                <w:gridCol w:w="508"/>
                                <w:gridCol w:w="501"/>
                            </w:tblGrid>
                            <w:tr>
                                <w:tblPrEx>
                                    <w:tblBorders>
                                        <w:top w:val="single" w:color="000000" w:sz="4" w:space="0"/>
                                        <w:left w:val="single" w:color="000000" w:sz="4" w:space="0"/>
                                        <w:bottom w:val="single" w:color="000000" w:sz="4" w:space="0"/>
                                        <w:right w:val="single" w:color="000000" w:sz="4" w:space="0"/>
                                        <w:insideH w:val="single" w:color="000000" w:sz="4" w:space="0"/>
                                        <w:insideV w:val="single" w:color="000000" w:sz="4" w:space="0"/>
                                    </w:tblBorders>
                                    <w:tblCellMar>
                                        <w:top w:w="0" w:type="dxa"/>
                                        <w:left w:w="0" w:type="dxa"/>
                                        <w:bottom w:w="0" w:type="dxa"/>
                                        <w:right w:w="0" w:type="dxa"/>
                                    </w:tblCellMar>
                                </w:tblPrEx>
                                <w:trPr>
                                    <w:trHeight w:val="389" w:hRule="atLeast"/>
                                </w:trPr>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="2884" w:type="dxa"/>
                                        <w:vMerge w:val="restart"/>
                                        <w:tcBorders>
                                            <w:bottom w:val="nil"/>
                                        </w:tcBorders>
                                        <w:vAlign w:val="top"/>
                                    </w:tcPr>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="6"/>
                                            <w:spacing w:line="248" w:lineRule="auto"/>
                                        </w:pPr>
                                    </w:p>
                                    <w:p>
                                        <w:pPr>
                                            <w:spacing w:before="55" w:line="226" w:lineRule="auto"/>
                                            <w:ind w:left="1264"/>
                                            <w:rPr>
                                                <w:rFonts w:ascii="宋体" w:hAnsi="宋体" w:eastAsia="宋体" w:cs="宋体"/>
                                                <w:sz w:val="17"/>
                                                <w:szCs w:val="17"/>
                                            </w:rPr>
                                        </w:pPr>
                                        <w:r>
                                            <w:rPr>
                                                <w:rFonts w:ascii="宋体" w:hAnsi="宋体" w:eastAsia="宋体" w:cs="宋体"/>
                                                <w:spacing w:val="1"/>
                                                <w:sz w:val="17"/>
                                                <w:szCs w:val="17"/>
                                            </w:rPr>
                                            <w:t>班级</w:t>
                                        </w:r>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="1206" w:type="dxa"/>
                                        <w:vMerge w:val="restart"/>
                                        <w:tcBorders>
                                            <w:bottom w:val="nil"/>
                                        </w:tcBorders>
                                        <w:vAlign w:val="top"/>
                                    </w:tcPr>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="6"/>
                                            <w:spacing w:line="248" w:lineRule="auto"/>
                                        </w:pPr>
                                    </w:p>
                                    <w:p>
                                        <w:pPr>
                                            <w:spacing w:before="55" w:line="226" w:lineRule="auto"/>
                                            <w:ind w:left="428"/>
                                            <w:rPr>
                                                <w:rFonts w:ascii="宋体" w:hAnsi="宋体" w:eastAsia="宋体" w:cs="宋体"/>
                                                <w:sz w:val="17"/>
                                                <w:szCs w:val="17"/>
                                            </w:rPr>
                                        </w:pPr>
                                        <w:r>
                                            <w:rPr>
                                                <w:rFonts w:ascii="宋体" w:hAnsi="宋体" w:eastAsia="宋体" w:cs="宋体"/>
                                                <w:spacing w:val="-1"/>
                                                <w:sz w:val="17"/>
                                                <w:szCs w:val="17"/>
                                            </w:rPr>
                                            <w:t>学号</w:t>
                                        </w:r>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="930" w:type="dxa"/>
                                        <w:vMerge w:val="restart"/>
                                        <w:tcBorders>
                                            <w:bottom w:val="nil"/>
                                        </w:tcBorders>
                                        <w:vAlign w:val="top"/>
                                    </w:tcPr>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="6"/>
                                            <w:spacing w:line="248" w:lineRule="auto"/>
                                        </w:pPr>
                                    </w:p>
                                    <w:p>
                                        <w:pPr>
                                            <w:spacing w:before="55" w:line="224" w:lineRule="auto"/>
                                            <w:ind w:left="287"/>
                                            <w:rPr>
                                                <w:rFonts w:ascii="宋体" w:hAnsi="宋体" w:eastAsia="宋体" w:cs="宋体"/>
                                                <w:sz w:val="17"/>
                                                <w:szCs w:val="17"/>
                                            </w:rPr>
                                        </w:pPr>
                                        <w:r>
                                            <w:rPr>
                                                <w:rFonts w:ascii="宋体" w:hAnsi="宋体" w:eastAsia="宋体" w:cs="宋体"/>
                                                <w:spacing w:val="1"/>
                                                <w:sz w:val="17"/>
                                                <w:szCs w:val="17"/>
                                            </w:rPr>
                                            <w:t>姓名</w:t>
                                        </w:r>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="784" w:type="dxa"/>
                                        <w:vMerge w:val="restart"/>
                                        <w:tcBorders>
                                            <w:bottom w:val="nil"/>
                                        </w:tcBorders>
                                        <w:vAlign w:val="top"/>
                                    </w:tcPr>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="6"/>
                                            <w:spacing w:line="248" w:lineRule="auto"/>
                                        </w:pPr>
                                    </w:p>
                                    <w:p>
                                        <w:pPr>
                                            <w:spacing w:before="55" w:line="225" w:lineRule="auto"/>
                                            <w:ind w:left="212"/>
                                            <w:rPr>
                                                <w:rFonts w:ascii="宋体" w:hAnsi="宋体" w:eastAsia="宋体" w:cs="宋体"/>
                                                <w:sz w:val="17"/>
                                                <w:szCs w:val="17"/>
                                            </w:rPr>
                                        </w:pPr>
                                        <w:r>
                                            <w:rPr>
                                                <w:rFonts w:ascii="宋体" w:hAnsi="宋体" w:eastAsia="宋体" w:cs="宋体"/>
                                                <w:spacing w:val="-1"/>
                                                <w:sz w:val="17"/>
                                                <w:szCs w:val="17"/>
                                            </w:rPr>
                                            <w:t>性别</w:t>
                                        </w:r>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="9449" w:type="dxa"/>
                                        <w:gridSpan w:val="19"/>
                                        <w:vAlign w:val="top"/>
                                    </w:tcPr>
                                    <w:p>
                                        <w:pPr>
                                            <w:spacing w:before="101" w:line="224" w:lineRule="auto"/>
                                            <w:ind w:left="4402"/>
                                            <w:rPr>
                                                <w:rFonts w:ascii="宋体" w:hAnsi="宋体" w:eastAsia="宋体" w:cs="宋体"/>
                                                <w:sz w:val="17"/>
                                                <w:szCs w:val="17"/>
                                            </w:rPr>
                                        </w:pPr>
                                        <w:r>
                                            <w:rPr>
                                                <w:rFonts w:ascii="宋体" w:hAnsi="宋体" w:eastAsia="宋体" w:cs="宋体"/>
                                                <w:spacing w:val="-4"/>
                                                <w:sz w:val="17"/>
                                                <w:szCs w:val="17"/>
                                            </w:rPr>
                                            <w:t>日期周次</w:t>
                                        </w:r>
                                    </w:p>
                                </w:tc>
                            </w:tr>
                            <w:tr>
                                <w:tblPrEx>
                                    <w:tblBorders>
                                        <w:top w:val="single" w:color="000000" w:sz="4" w:space="0"/>
                                        <w:left w:val="single" w:color="000000" w:sz="4" w:space="0"/>
                                        <w:bottom w:val="single" w:color="000000" w:sz="4" w:space="0"/>
                                        <w:right w:val="single" w:color="000000" w:sz="4" w:space="0"/>
                                        <w:insideH w:val="single" w:color="000000" w:sz="4" w:space="0"/>
                                        <w:insideV w:val="single" w:color="000000" w:sz="4" w:space="0"/>
                                    </w:tblBorders>
                                    <w:tblCellMar>
                                        <w:top w:w="0" w:type="dxa"/>
                                        <w:left w:w="0" w:type="dxa"/>
                                        <w:bottom w:w="0" w:type="dxa"/>
                                        <w:right w:w="0" w:type="dxa"/>
                                    </w:tblCellMar>
                                </w:tblPrEx>
                                <w:trPr>
                                    <w:trHeight w:val="382" w:hRule="atLeast"/>
                                </w:trPr>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="2884" w:type="dxa"/>
                                        <w:vMerge w:val="continue"/>
                                        <w:tcBorders>
                                            <w:top w:val="nil"/>
                                        </w:tcBorders>
                                        <w:vAlign w:val="top"/>
                                    </w:tcPr>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="6"/>
                                        </w:pPr>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="1206" w:type="dxa"/>
                                        <w:vMerge w:val="continue"/>
                                        <w:tcBorders>
                                            <w:top w:val="nil"/>
                                        </w:tcBorders>
                                        <w:vAlign w:val="top"/>
                                    </w:tcPr>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="6"/>
                                        </w:pPr>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="930" w:type="dxa"/>
                                        <w:vMerge w:val="continue"/>
                                        <w:tcBorders>
                                            <w:top w:val="nil"/>
                                        </w:tcBorders>
                                        <w:vAlign w:val="top"/>
                                    </w:tcPr>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="6"/>
                                        </w:pPr>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="784" w:type="dxa"/>
                                        <w:vMerge w:val="continue"/>
                                        <w:tcBorders>
                                            <w:top w:val="nil"/>
                                        </w:tcBorders>
                                        <w:vAlign w:val="top"/>
                                    </w:tcPr>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="6"/>
                                        </w:pPr>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="494" w:type="dxa"/>
                                        <w:vAlign w:val="top"/>
                                    </w:tcPr>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="6"/>
                                        </w:pPr>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="494" w:type="dxa"/>
                                        <w:vAlign w:val="top"/>
                                    </w:tcPr>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="6"/>
                                        </w:pPr>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="494" w:type="dxa"/>
                                        <w:vAlign w:val="top"/>
                                    </w:tcPr>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="6"/>
                                        </w:pPr>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="494" w:type="dxa"/>
                                        <w:vAlign w:val="top"/>
                                    </w:tcPr>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="6"/>
                                        </w:pPr>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="508" w:type="dxa"/>
                                        <w:vAlign w:val="top"/>
                                    </w:tcPr>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="6"/>
                                        </w:pPr>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="494" w:type="dxa"/>
                                        <w:vAlign w:val="top"/>
                                    </w:tcPr>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="6"/>
                                        </w:pPr>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="494" w:type="dxa"/>
                                        <w:vAlign w:val="top"/>
                                    </w:tcPr>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="6"/>
                                        </w:pPr>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="494" w:type="dxa"/>
                                        <w:vAlign w:val="top"/>
                                    </w:tcPr>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="6"/>
                                        </w:pPr>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="508" w:type="dxa"/>
                                        <w:vAlign w:val="top"/>
                                    </w:tcPr>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="6"/>
                                        </w:pPr>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="494" w:type="dxa"/>
                                        <w:vAlign w:val="top"/>
                                    </w:tcPr>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="6"/>
                                        </w:pPr>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="494" w:type="dxa"/>
                                        <w:vAlign w:val="top"/>
                                    </w:tcPr>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="6"/>
                                        </w:pPr>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="494" w:type="dxa"/>
                                        <w:vAlign w:val="top"/>
                                    </w:tcPr>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="6"/>
                                        </w:pPr>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="508" w:type="dxa"/>
                                        <w:vAlign w:val="top"/>
                                    </w:tcPr>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="6"/>
                                        </w:pPr>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="494" w:type="dxa"/>
                                        <w:vAlign w:val="top"/>
                                    </w:tcPr>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="6"/>
                                        </w:pPr>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="494" w:type="dxa"/>
                                        <w:vAlign w:val="top"/>
                                    </w:tcPr>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="6"/>
                                        </w:pPr>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="494" w:type="dxa"/>
                                        <w:vAlign w:val="top"/>
                                    </w:tcPr>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="6"/>
                                        </w:pPr>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="494" w:type="dxa"/>
                                        <w:vAlign w:val="top"/>
                                    </w:tcPr>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="6"/>
                                        </w:pPr>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="508" w:type="dxa"/>
                                        <w:vAlign w:val="top"/>
                                    </w:tcPr>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="6"/>
                                        </w:pPr>
                                    </w:p>
                                </w:tc>
                                <w:tc>
                                    <w:tcPr>
                                        <w:tcW w:w="501" w:type="dxa"/>
                                        <w:vAlign w:val="top"/>
                                    </w:tcPr>
                                    <w:p>
                                        <w:pPr>
                                            <w:pStyle w:val="6"/>
                                        </w:pPr>
                                    </w:p>
                                </w:tc>
                            </w:tr>
                            <#list courseInfoMap[course] as student>
                                <w:tr>
                                    <w:tblPrEx>
                                        <w:tblBorders>
                                            <w:top w:val="single" w:color="000000" w:sz="4" w:space="0"/>
                                            <w:left w:val="single" w:color="000000" w:sz="4" w:space="0"/>
                                            <w:bottom w:val="single" w:color="000000" w:sz="4" w:space="0"/>
                                            <w:right w:val="single" w:color="000000" w:sz="4" w:space="0"/>
                                            <w:insideH w:val="single" w:color="000000" w:sz="4" w:space="0"/>
                                            <w:insideV w:val="single" w:color="000000" w:sz="4" w:space="0"/>
                                        </w:tblBorders>
                                        <w:tblCellMar>
                                            <w:top w:w="0" w:type="dxa"/>
                                            <w:left w:w="0" w:type="dxa"/>
                                            <w:bottom w:w="0" w:type="dxa"/>
                                            <w:right w:w="0" w:type="dxa"/>
                                        </w:tblCellMar>
                                    </w:tblPrEx>
                                    <w:trPr>
                                        <w:trHeight w:val="382" w:hRule="atLeast"/>
                                    </w:trPr>
                                    <w:tc>
                                        <w:tcPr>
                                            <w:tcW w:w="2884" w:type="dxa"/>
                                            <w:vAlign w:val="top"/>
                                        </w:tcPr>
                                        <w:p>
                                            <w:pPr>
                                                <w:spacing w:before="97" w:line="224" w:lineRule="auto"/>
                                                <w:ind w:left="42"/>
                                                <w:rPr>
                                                    <w:rFonts w:ascii="宋体" w:hAnsi="宋体" w:eastAsia="宋体" w:cs="宋体"/>
                                                    <w:sz w:val="17"/>
                                                    <w:szCs w:val="17"/>
                                                </w:rPr>
                                            </w:pPr>
                                            <w:r>
                                                <w:rPr>
                                                    <w:rFonts w:ascii="宋体" w:hAnsi="宋体" w:eastAsia="宋体" w:cs="宋体"/>
                                                    <w:spacing w:val="3"/>
                                                    <w:sz w:val="17"/>
                                                    <w:szCs w:val="17"/>
                                                </w:rPr>
                                                <w:t><#if student.xsjbxx_bjxx??>${student.xsjbxx_bjxx}</#if></w:t>
                                            </w:r>
                                        </w:p>
                                    </w:tc>
                                    <w:tc>
                                        <w:tcPr>
                                            <w:tcW w:w="1206" w:type="dxa"/>
                                            <w:vAlign w:val="top"/>
                                        </w:tcPr>
                                        <w:p>
                                            <w:pPr>
                                                <w:spacing w:before="124" w:line="187" w:lineRule="auto"/>
                                                <w:ind w:left="29"/>
                                                <w:rPr>
                                                    <w:rFonts w:ascii="宋体" w:hAnsi="宋体" w:eastAsia="宋体" w:cs="宋体"/>
                                                    <w:sz w:val="17"/>
                                                    <w:szCs w:val="17"/>
                                                </w:rPr>
                                            </w:pPr>
                                            <w:r>
                                                <w:rPr>
                                                    <w:rFonts w:ascii="宋体" w:hAnsi="宋体" w:eastAsia="宋体" w:cs="宋体"/>
                                                    <w:sz w:val="17"/>
                                                    <w:szCs w:val="17"/>
                                                </w:rPr>
                                                <w:t><#if student.xsjbxx_xh??>${student.xsjbxx_xh}</#if></w:t>
                                            </w:r>
                                        </w:p>
                                    </w:tc>
                                    <w:tc>
                                        <w:tcPr>
                                            <w:tcW w:w="930" w:type="dxa"/>
                                            <w:vAlign w:val="top"/>
                                        </w:tcPr>
                                        <w:p>
                                            <w:pPr>
                                                <w:spacing w:before="97" w:line="223" w:lineRule="auto"/>
                                                <w:ind w:left="36"/>
                                                <w:rPr>
                                                    <w:rFonts w:ascii="宋体" w:hAnsi="宋体" w:eastAsia="宋体" w:cs="宋体"/>
                                                    <w:sz w:val="17"/>
                                                    <w:szCs w:val="17"/>
                                                </w:rPr>
                                            </w:pPr>
                                            <w:r>
                                                <w:rPr>
                                                    <w:rFonts w:ascii="宋体" w:hAnsi="宋体" w:eastAsia="宋体" w:cs="宋体"/>
                                                    <w:spacing w:val="1"/>
                                                    <w:sz w:val="17"/>
                                                    <w:szCs w:val="17"/>
                                                </w:rPr>
                                                <w:t><#if student.xsjbxx_xm??>${student.xsjbxx_xm}</#if></w:t>
                                            </w:r>
                                        </w:p>
                                    </w:tc>
                                    <w:tc>
                                        <w:tcPr>
                                            <w:tcW w:w="784" w:type="dxa"/>
                                            <w:vAlign w:val="top"/>
                                        </w:tcPr>
                                        <w:p>
                                            <w:pPr>
                                                <w:spacing w:before="97" w:line="225" w:lineRule="auto"/>
                                                <w:ind w:left="36"/>
                                                <w:rPr>
                                                    <w:rFonts w:ascii="宋体" w:hAnsi="宋体" w:eastAsia="宋体" w:cs="宋体"/>
                                                    <w:sz w:val="17"/>
                                                    <w:szCs w:val="17"/>
                                                </w:rPr>
                                            </w:pPr>
                                            <w:r>
                                                <w:rPr>
                                                    <w:rFonts w:ascii="宋体" w:hAnsi="宋体" w:eastAsia="宋体" w:cs="宋体"/>
                                                    <w:sz w:val="17"/>
                                                    <w:szCs w:val="17"/>
                                                </w:rPr>
                                                <w:t><#if student.xsjbxx_xb??>${student.xsjbxx_xb}</#if></w:t>
                                            </w:r>
                                        </w:p>
                                    </w:tc>
                                    <w:tc>
                                        <w:tcPr>
                                            <w:tcW w:w="494" w:type="dxa"/>
                                            <w:vAlign w:val="top"/>
                                        </w:tcPr>
                                        <w:p>
                                            <w:pPr>
                                                <w:pStyle w:val="6"/>
                                            </w:pPr>
                                        </w:p>
                                    </w:tc>
                                    <w:tc>
                                        <w:tcPr>
                                            <w:tcW w:w="494" w:type="dxa"/>
                                            <w:vAlign w:val="top"/>
                                        </w:tcPr>
                                        <w:p>
                                            <w:pPr>
                                                <w:pStyle w:val="6"/>
                                            </w:pPr>
                                        </w:p>
                                    </w:tc>
                                    <w:tc>
                                        <w:tcPr>
                                            <w:tcW w:w="494" w:type="dxa"/>
                                            <w:vAlign w:val="top"/>
                                        </w:tcPr>
                                        <w:p>
                                            <w:pPr>
                                                <w:pStyle w:val="6"/>
                                            </w:pPr>
                                        </w:p>
                                    </w:tc>
                                    <w:tc>
                                        <w:tcPr>
                                            <w:tcW w:w="494" w:type="dxa"/>
                                            <w:vAlign w:val="top"/>
                                        </w:tcPr>
                                        <w:p>
                                            <w:pPr>
                                                <w:pStyle w:val="6"/>
                                            </w:pPr>
                                        </w:p>
                                    </w:tc>
                                    <w:tc>
                                        <w:tcPr>
                                            <w:tcW w:w="508" w:type="dxa"/>
                                            <w:vAlign w:val="top"/>
                                        </w:tcPr>
                                        <w:p>
                                            <w:pPr>
                                                <w:pStyle w:val="6"/>
                                            </w:pPr>
                                        </w:p>
                                    </w:tc>
                                    <w:tc>
                                        <w:tcPr>
                                            <w:tcW w:w="494" w:type="dxa"/>
                                            <w:vAlign w:val="top"/>
                                        </w:tcPr>
                                        <w:p>
                                            <w:pPr>
                                                <w:pStyle w:val="6"/>
                                            </w:pPr>
                                        </w:p>
                                    </w:tc>
                                    <w:tc>
                                        <w:tcPr>
                                            <w:tcW w:w="494" w:type="dxa"/>
                                            <w:vAlign w:val="top"/>
                                        </w:tcPr>
                                        <w:p>
                                            <w:pPr>
                                                <w:pStyle w:val="6"/>
                                            </w:pPr>
                                        </w:p>
                                    </w:tc>
                                    <w:tc>
                                        <w:tcPr>
                                            <w:tcW w:w="494" w:type="dxa"/>
                                            <w:vAlign w:val="top"/>
                                        </w:tcPr>
                                        <w:p>
                                            <w:pPr>
                                                <w:pStyle w:val="6"/>
                                            </w:pPr>
                                        </w:p>
                                    </w:tc>
                                    <w:tc>
                                        <w:tcPr>
                                            <w:tcW w:w="508" w:type="dxa"/>
                                            <w:vAlign w:val="top"/>
                                        </w:tcPr>
                                        <w:p>
                                            <w:pPr>
                                                <w:pStyle w:val="6"/>
                                            </w:pPr>
                                        </w:p>
                                    </w:tc>
                                    <w:tc>
                                        <w:tcPr>
                                            <w:tcW w:w="494" w:type="dxa"/>
                                            <w:vAlign w:val="top"/>
                                        </w:tcPr>
                                        <w:p>
                                            <w:pPr>
                                                <w:pStyle w:val="6"/>
                                            </w:pPr>
                                        </w:p>
                                    </w:tc>
                                    <w:tc>
                                        <w:tcPr>
                                            <w:tcW w:w="494" w:type="dxa"/>
                                            <w:vAlign w:val="top"/>
                                        </w:tcPr>
                                        <w:p>
                                            <w:pPr>
                                                <w:pStyle w:val="6"/>
                                            </w:pPr>
                                        </w:p>
                                    </w:tc>
                                    <w:tc>
                                        <w:tcPr>
                                            <w:tcW w:w="494" w:type="dxa"/>
                                            <w:vAlign w:val="top"/>
                                        </w:tcPr>
                                        <w:p>
                                            <w:pPr>
                                                <w:pStyle w:val="6"/>
                                            </w:pPr>
                                        </w:p>
                                    </w:tc>
                                    <w:tc>
                                        <w:tcPr>
                                            <w:tcW w:w="508" w:type="dxa"/>
                                            <w:vAlign w:val="top"/>
                                        </w:tcPr>
                                        <w:p>
                                            <w:pPr>
                                                <w:pStyle w:val="6"/>
                                            </w:pPr>
                                        </w:p>
                                    </w:tc>
                                    <w:tc>
                                        <w:tcPr>
                                            <w:tcW w:w="494" w:type="dxa"/>
                                            <w:vAlign w:val="top"/>
                                        </w:tcPr>
                                        <w:p>
                                            <w:pPr>
                                                <w:pStyle w:val="6"/>
                                            </w:pPr>
                                        </w:p>
                                    </w:tc>
                                    <w:tc>
                                        <w:tcPr>
                                            <w:tcW w:w="494" w:type="dxa"/>
                                            <w:vAlign w:val="top"/>
                                        </w:tcPr>
                                        <w:p>
                                            <w:pPr>
                                                <w:pStyle w:val="6"/>
                                            </w:pPr>
                                        </w:p>
                                    </w:tc>
                                    <w:tc>
                                        <w:tcPr>
                                            <w:tcW w:w="494" w:type="dxa"/>
                                            <w:vAlign w:val="top"/>
                                        </w:tcPr>
                                        <w:p>
                                            <w:pPr>
                                                <w:pStyle w:val="6"/>
                                            </w:pPr>
                                        </w:p>
                                    </w:tc>
                                    <w:tc>
                                        <w:tcPr>
                                            <w:tcW w:w="494" w:type="dxa"/>
                                            <w:vAlign w:val="top"/>
                                        </w:tcPr>
                                        <w:p>
                                            <w:pPr>
                                                <w:pStyle w:val="6"/>
                                            </w:pPr>
                                        </w:p>
                                    </w:tc>
                                    <w:tc>
                                        <w:tcPr>
                                            <w:tcW w:w="508" w:type="dxa"/>
                                            <w:vAlign w:val="top"/>
                                        </w:tcPr>
                                        <w:p>
                                            <w:pPr>
                                                <w:pStyle w:val="6"/>
                                            </w:pPr>
                                        </w:p>
                                    </w:tc>
                                    <w:tc>
                                        <w:tcPr>
                                            <w:tcW w:w="501" w:type="dxa"/>
                                            <w:vAlign w:val="top"/>
                                        </w:tcPr>
                                        <w:p>
                                            <w:pPr>
                                                <w:pStyle w:val="6"/>
                                            </w:pPr>
                                        </w:p>
                                    </w:tc>
                                </w:tr>
                            </#list>
                        </w:tbl>
                        <#if data_index != courseInfoMap?size>
                        <w:p>
                            <w:pPr>
                                <w:spacing w:line="14" w:lineRule="auto"/>
                                <w:rPr>
                                    <w:rFonts w:ascii="Arial" w:hAnsi="Arial" w:eastAsia="Arial" w:cs="Arial"/>
                                    <w:sz w:val="2"/>
                                    <w:szCs w:val="2"/>
                                </w:rPr>
                                <w:sectPr>
                                    <w:type w:val="continuous"/>
                                    <w:pgSz w:w="16839" w:h="11900"/>
                                    <w:pgMar w:top="608" w:right="587" w:bottom="0" w:left="987" w:header="0" w:footer="0"
                                             w:gutter="0"/>
                                    <w:cols w:equalWidth="0" w:num="1">
                                        <w:col w:w="15264"/>
                                    </w:cols>
                                </w:sectPr>
                            </w:pPr>
                        </w:p>
                        <w:p>
                            <w:pPr>
                                <w:rPr>
                                    <w:rFonts w:ascii="Arial"/>
                                    <w:sz w:val="21"/>
                                </w:rPr>
                            </w:pPr>
                        </w:p>
                        <w:p>
                            <w:pPr>
                                <w:rPr>
                                    <w:rFonts w:ascii="Arial" w:hAnsi="Arial" w:eastAsia="Arial" w:cs="Arial"/>
                                    <w:sz w:val="21"/>
                                    <w:szCs w:val="21"/>
                                </w:rPr>
                                <w:sectPr>
                                    <w:headerReference r:id="rId5" w:type="default"/>
                                    <w:footerReference r:id="rId6" w:type="default"/>
                                    <w:pgSz w:w="16839" w:h="11900"/>
                                    <w:pgMar w:top="0" w:right="0" w:bottom="0" w:left="0" w:header="0" w:footer="0"
                                             w:gutter="0"/>
                                    <w:cols w:space="720" w:num="1"/>
                                </w:sectPr>
                            </w:pPr>
                        </w:p>
                        </#if>
                        <#if data_index == courseInfoMap?size>
                            <w:p>
                                <w:pPr>
                                    <w:spacing w:before="97" w:line="285" w:lineRule="auto"/>
                                    <w:ind w:left="48" w:right="68" w:hanging="11"/>
                                    <w:rPr>
                                        <w:rFonts w:ascii="宋体" w:hAnsi="宋体" w:eastAsia="宋体" w:cs="宋体"/>
                                        <w:sz w:val="17"/>
                                        <w:szCs w:val="17"/>
                                    </w:rPr>
                                </w:pPr>
                                <w:r>
                                    <w:rPr>
                                        <w:rFonts w:ascii="宋体" w:hAnsi="宋体" w:eastAsia="宋体" w:cs="宋体"/>
                                        <w:spacing w:val="4"/>
                                        <w:sz w:val="17"/>
                                        <w:szCs w:val="17"/>
                                    </w:rPr>
                                    <w:t>备注：学生考勤是实施教学管理、维护正常的教学秩序，督促学生努力学习的重要环节之一，希望授课教师予以高度重视。授课教师应对学生的旷课、迟到、公假、事假、病假认真记录。登载考勤时</w:t>
                                </w:r>
                                <w:r>
                                    <w:rPr>
                                        <w:rFonts w:ascii="宋体" w:hAnsi="宋体" w:eastAsia="宋体" w:cs="宋体"/>
                                        <w:spacing w:val="18"/>
                                        <w:sz w:val="17"/>
                                        <w:szCs w:val="17"/>
                                    </w:rPr>
                                    <w:t xml:space="preserve"> </w:t>
                                </w:r>
                                <w:r>
                                    <w:rPr>
                                        <w:rFonts w:ascii="宋体" w:hAnsi="宋体" w:eastAsia="宋体" w:cs="宋体"/>
                                        <w:spacing w:val="3"/>
                                        <w:sz w:val="17"/>
                                        <w:szCs w:val="17"/>
                                    </w:rPr>
                                    <w:t>间分为1-10周登载（1-9）考勤，10-19周登载（10-18周）考勤。如有未及时销假的学生，只能凭假条到授课教师处销假。</w:t>
                                </w:r>
                            </w:p>
                        </#if>
                        <w:p>
                            <w:pPr>
                                <w:spacing w:line="74" w:lineRule="exact"/>
                            </w:pPr>
                        </w:p>
                        <w:p>
                            <w:pPr>
                                <w:pStyle w:val="2"/>
                                <w:spacing w:line="14" w:lineRule="auto"/>
                                <w:rPr>
                                    <w:sz w:val="2"/>
                                </w:rPr>
                            </w:pPr>
                            <w:bookmarkStart w:id="0" w:name="_GoBack"/>
                            <w:bookmarkEnd w:id="0"/>
                        </w:p>
                        <w:sectPr>
                            <w:type w:val="continuous"/>
                            <w:pgSz w:w="16839" w:h="11900"/>
                            <w:pgMar w:top="400" w:right="587" w:bottom="400" w:left="987" w:header="0" w:footer="0"
                                     w:gutter="0"/>
                            <w:cols w:equalWidth="0" w:num="1">
                                <w:col w:w="15264"/>
                            </w:cols>
                        </w:sectPr>
                    </w:body>
                </#list>
            </w:document>
        </pkg:xmlData>
    </pkg:part>
    <pkg:part pkg:name="/docProps/app.xml"
              pkg:contentType="application/vnd.openxmlformats-officedocument.extended-properties+xml">
        <pkg:xmlData>
            <Properties xmlns="http://schemas.openxmlformats.org/officeDocument/2006/extended-properties"
                        xmlns:vt="http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes">
                <TotalTime>0</TotalTime>
                <ScaleCrop>false</ScaleCrop>
                <LinksUpToDate>false</LinksUpToDate>
                <Application>WPS Office_12.1.0.16399_F1E327BC-269C-435d-A152-05C5408002CA</Application>
            </Properties>
        </pkg:xmlData>
    </pkg:part>
    <pkg:part pkg:name="/docProps/core.xml"
              pkg:contentType="application/vnd.openxmlformats-package.core-properties+xml">
        <pkg:xmlData>
            <cp:coreProperties xmlns:cp="http://schemas.openxmlformats.org/package/2006/metadata/core-properties"
                               xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:dcterms="http://purl.org/dc/terms/"
                               xmlns:dcmitype="http://purl.org/dc/dcmitype/"
                               xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <dcterms:created xsi:type="dcterms:W3CDTF">2024-02-28T01:40:00Z</dcterms:created>
                <dc:creator>pc</dc:creator>
                <cp:lastModifiedBy>WPS_1661481478</cp:lastModifiedBy>
                <dcterms:modified xsi:type="dcterms:W3CDTF">2024-03-04T06:43:20Z</dcterms:modified>
            </cp:coreProperties>
        </pkg:xmlData>
    </pkg:part>
    <pkg:part pkg:name="/docProps/custom.xml"
              pkg:contentType="application/vnd.openxmlformats-officedocument.custom-properties+xml">
        <pkg:xmlData>
            <Properties xmlns="http://schemas.openxmlformats.org/officeDocument/2006/custom-properties"
                        xmlns:vt="http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes">
                <property fmtid="{D5CDD505-2E9C-101B-9397-08002B2CF9AE}" pid="2" name="CRO">
                    <vt:lpwstr>wqlLaW5nc29mdCBQREYgdG8gV1BTIDkw</vt:lpwstr>
                </property>
                <property fmtid="{D5CDD505-2E9C-101B-9397-08002B2CF9AE}" pid="3" name="Created">
                    <vt:filetime>2024-03-04T14:15:32Z</vt:filetime>
                </property>
                <property fmtid="{D5CDD505-2E9C-101B-9397-08002B2CF9AE}" pid="4" name="KSOProductBuildVer">
                    <vt:lpwstr>2052-12.1.0.16399</vt:lpwstr>
                </property>
                <property fmtid="{D5CDD505-2E9C-101B-9397-08002B2CF9AE}" pid="5" name="ICV">
                    <vt:lpwstr>DDC28E4487384BFC8781DEA233F2EB34_13</vt:lpwstr>
                </property>
            </Properties>
        </pkg:xmlData>
    </pkg:part>
    <pkg:part pkg:name="/word/endnotes.xml"
              pkg:contentType="application/vnd.openxmlformats-officedocument.wordprocessingml.endnotes+xml">
        <pkg:xmlData>
            <w:endnotes xmlns:wpc="http://schemas.microsoft.com/office/word/2010/wordprocessingCanvas"
                        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                        xmlns:o="urn:schemas-microsoft-com:office:office"
                        xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships"
                        xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math"
                        xmlns:v="urn:schemas-microsoft-com:vml"
                        xmlns:wp14="http://schemas.microsoft.com/office/word/2010/wordprocessingDrawing"
                        xmlns:wp="http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing"
                        xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main"
                        xmlns:w14="http://schemas.microsoft.com/office/word/2010/wordml"
                        xmlns:w15="http://schemas.microsoft.com/office/word/2012/wordml"
                        xmlns:w10="urn:schemas-microsoft-com:office:word"
                        xmlns:wpg="http://schemas.microsoft.com/office/word/2010/wordprocessingGroup"
                        xmlns:wpi="http://schemas.microsoft.com/office/word/2010/wordprocessingInk"
                        xmlns:wne="http://schemas.microsoft.com/office/word/2006/wordml"
                        xmlns:wps="http://schemas.microsoft.com/office/word/2010/wordprocessingShape"
                        xmlns:wpsCustomData="http://www.wps.cn/officeDocument/2013/wpsCustomData"
                        mc:Ignorable="w14 w15 wp14">
                <w:endnote w:type="separator" w:id="0">
                    <w:p>
                        <w:pPr>
                            <w:spacing w:line="240" w:lineRule="auto"/>
                        </w:pPr>
                        <w:r>
                            <w:separator/>
                        </w:r>
                    </w:p>
                </w:endnote>
                <w:endnote w:type="continuationSeparator" w:id="1">
                    <w:p>
                        <w:pPr>
                            <w:spacing w:line="240" w:lineRule="auto"/>
                        </w:pPr>
                        <w:r>
                            <w:continuationSeparator/>
                        </w:r>
                    </w:p>
                </w:endnote>
            </w:endnotes>
        </pkg:xmlData>
    </pkg:part>
    <pkg:part pkg:name="/word/fontTable.xml"
              pkg:contentType="application/vnd.openxmlformats-officedocument.wordprocessingml.fontTable+xml">
        <pkg:xmlData>
            <w:fonts xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                     xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships"
                     xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main"
                     xmlns:w14="http://schemas.microsoft.com/office/word/2010/wordml" mc:Ignorable="w14">
                <w:font w:name="Times New Roman">
                    <w:panose1 w:val="02020603050405020304"/>
                    <w:charset w:val="00"/>
                    <w:family w:val="roman"/>
                    <w:pitch w:val="variable"/>
                    <w:sig w:usb0="20007A87" w:usb1="80000000" w:usb2="00000008" w:usb3="00000000" w:csb0="000001FF"
                           w:csb1="00000000"/>
                </w:font>
                <w:font w:name="宋体">
                    <w:panose1 w:val="02010600030101010101"/>
                    <w:charset w:val="86"/>
                    <w:family w:val="auto"/>
                    <w:pitch w:val="default"/>
                    <w:sig w:usb0="00000203" w:usb1="288F0000" w:usb2="00000006" w:usb3="00000000" w:csb0="00040001"
                           w:csb1="00000000"/>
                </w:font>
                <w:font w:name="Wingdings">
                    <w:panose1 w:val="05000000000000000000"/>
                    <w:charset w:val="02"/>
                    <w:family w:val="auto"/>
                    <w:pitch w:val="default"/>
                    <w:sig w:usb0="00000000" w:usb1="00000000" w:usb2="00000000" w:usb3="00000000" w:csb0="80000000"
                           w:csb1="00000000"/>
                </w:font>
                <w:font w:name="Arial">
                    <w:panose1 w:val="020B0604020202020204"/>
                    <w:charset w:val="01"/>
                    <w:family w:val="swiss"/>
                    <w:pitch w:val="default"/>
                    <w:sig w:usb0="E0002EFF" w:usb1="C000785B" w:usb2="00000009" w:usb3="00000000" w:csb0="400001FF"
                           w:csb1="FFFF0000"/>
                </w:font>
                <w:font w:name="黑体">
                    <w:panose1 w:val="02010609060101010101"/>
                    <w:charset w:val="86"/>
                    <w:family w:val="auto"/>
                    <w:pitch w:val="default"/>
                    <w:sig w:usb0="800002BF" w:usb1="38CF7CFA" w:usb2="00000016" w:usb3="00000000" w:csb0="00040001"
                           w:csb1="00000000"/>
                </w:font>
                <w:font w:name="Courier New">
                    <w:panose1 w:val="02070309020205020404"/>
                    <w:charset w:val="01"/>
                    <w:family w:val="modern"/>
                    <w:pitch w:val="default"/>
                    <w:sig w:usb0="E0002EFF" w:usb1="C0007843" w:usb2="00000009" w:usb3="00000000" w:csb0="400001FF"
                           w:csb1="FFFF0000"/>
                </w:font>
                <w:font w:name="Symbol">
                    <w:panose1 w:val="05050102010706020507"/>
                    <w:charset w:val="02"/>
                    <w:family w:val="roman"/>
                    <w:pitch w:val="default"/>
                    <w:sig w:usb0="00000000" w:usb1="00000000" w:usb2="00000000" w:usb3="00000000" w:csb0="80000000"
                           w:csb1="00000000"/>
                </w:font>
                <w:font w:name="Calibri">
                    <w:panose1 w:val="020F0502020204030204"/>
                    <w:charset w:val="00"/>
                    <w:family w:val="swiss"/>
                    <w:pitch w:val="default"/>
                    <w:sig w:usb0="E4002EFF" w:usb1="C200247B" w:usb2="00000009" w:usb3="00000000" w:csb0="200001FF"
                           w:csb1="00000000"/>
                </w:font>
                <w:font w:name="微软雅黑">
                    <w:panose1 w:val="020B0503020204020204"/>
                    <w:charset w:val="86"/>
                    <w:family w:val="auto"/>
                    <w:pitch w:val="default"/>
                    <w:sig w:usb0="80000287" w:usb1="2ACF3C50" w:usb2="00000016" w:usb3="00000000" w:csb0="0004001F"
                           w:csb1="00000000"/>
                </w:font>
            </w:fonts>
        </pkg:xmlData>
    </pkg:part>
    <pkg:part pkg:name="/word/footer1.xml"
              pkg:contentType="application/vnd.openxmlformats-officedocument.wordprocessingml.footer+xml">
        <pkg:xmlData>
            <w:ftr xmlns:wpc="http://schemas.microsoft.com/office/word/2010/wordprocessingCanvas"
                   xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                   xmlns:o="urn:schemas-microsoft-com:office:office"
                   xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships"
                   xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math"
                   xmlns:v="urn:schemas-microsoft-com:vml"
                   xmlns:wp14="http://schemas.microsoft.com/office/word/2010/wordprocessingDrawing"
                   xmlns:wp="http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing"
                   xmlns:w10="urn:schemas-microsoft-com:office:word"
                   xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main"
                   xmlns:w14="http://schemas.microsoft.com/office/word/2010/wordml"
                   xmlns:w15="http://schemas.microsoft.com/office/word/2012/wordml"
                   xmlns:wpg="http://schemas.microsoft.com/office/word/2010/wordprocessingGroup"
                   xmlns:wpi="http://schemas.microsoft.com/office/word/2010/wordprocessingInk"
                   xmlns:wne="http://schemas.microsoft.com/office/word/2006/wordml"
                   xmlns:wps="http://schemas.microsoft.com/office/word/2010/wordprocessingShape"
                   xmlns:wpsCustomData="http://www.wps.cn/officeDocument/2013/wpsCustomData"
                   mc:Ignorable="w14 w15 wp14">
                <w:p>
                    <w:pPr>
                        <w:spacing w:line="14" w:lineRule="auto"/>
                        <w:rPr>
                            <w:rFonts w:ascii="Arial"/>
                            <w:sz w:val="2"/>
                        </w:rPr>
                    </w:pPr>
                </w:p>
            </w:ftr>
        </pkg:xmlData>
    </pkg:part>
    <pkg:part pkg:name="/word/footnotes.xml"
              pkg:contentType="application/vnd.openxmlformats-officedocument.wordprocessingml.footnotes+xml">
        <pkg:xmlData>
            <w:footnotes xmlns:wpc="http://schemas.microsoft.com/office/word/2010/wordprocessingCanvas"
                         xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                         xmlns:o="urn:schemas-microsoft-com:office:office"
                         xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships"
                         xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math"
                         xmlns:v="urn:schemas-microsoft-com:vml"
                         xmlns:wp14="http://schemas.microsoft.com/office/word/2010/wordprocessingDrawing"
                         xmlns:wp="http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing"
                         xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main"
                         xmlns:w14="http://schemas.microsoft.com/office/word/2010/wordml"
                         xmlns:w15="http://schemas.microsoft.com/office/word/2012/wordml"
                         xmlns:w10="urn:schemas-microsoft-com:office:word"
                         xmlns:wpg="http://schemas.microsoft.com/office/word/2010/wordprocessingGroup"
                         xmlns:wpi="http://schemas.microsoft.com/office/word/2010/wordprocessingInk"
                         xmlns:wne="http://schemas.microsoft.com/office/word/2006/wordml"
                         xmlns:wps="http://schemas.microsoft.com/office/word/2010/wordprocessingShape"
                         xmlns:wpsCustomData="http://www.wps.cn/officeDocument/2013/wpsCustomData"
                         mc:Ignorable="w14 w15 wp14">
                <w:footnote w:type="separator" w:id="0">
                    <w:p>
                        <w:pPr>
                            <w:spacing w:line="240" w:lineRule="auto"/>
                        </w:pPr>
                        <w:r>
                            <w:separator/>
                        </w:r>
                    </w:p>
                </w:footnote>
                <w:footnote w:type="continuationSeparator" w:id="1">
                    <w:p>
                        <w:pPr>
                            <w:spacing w:line="240" w:lineRule="auto"/>
                        </w:pPr>
                        <w:r>
                            <w:continuationSeparator/>
                        </w:r>
                    </w:p>
                </w:footnote>
            </w:footnotes>
        </pkg:xmlData>
    </pkg:part>
    <pkg:part pkg:name="/word/header1.xml"
              pkg:contentType="application/vnd.openxmlformats-officedocument.wordprocessingml.header+xml">
        <pkg:xmlData>
            <w:hdr xmlns:wpc="http://schemas.microsoft.com/office/word/2010/wordprocessingCanvas"
                   xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                   xmlns:o="urn:schemas-microsoft-com:office:office"
                   xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships"
                   xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math"
                   xmlns:v="urn:schemas-microsoft-com:vml"
                   xmlns:wp14="http://schemas.microsoft.com/office/word/2010/wordprocessingDrawing"
                   xmlns:wp="http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing"
                   xmlns:w10="urn:schemas-microsoft-com:office:word"
                   xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main"
                   xmlns:w14="http://schemas.microsoft.com/office/word/2010/wordml"
                   xmlns:w15="http://schemas.microsoft.com/office/word/2012/wordml"
                   xmlns:wpg="http://schemas.microsoft.com/office/word/2010/wordprocessingGroup"
                   xmlns:wpi="http://schemas.microsoft.com/office/word/2010/wordprocessingInk"
                   xmlns:wne="http://schemas.microsoft.com/office/word/2006/wordml"
                   xmlns:wps="http://schemas.microsoft.com/office/word/2010/wordprocessingShape"
                   xmlns:wpsCustomData="http://www.wps.cn/officeDocument/2013/wpsCustomData"
                   mc:Ignorable="w14 w15 wp14">
                <w:p>
                    <w:pPr>
                        <w:spacing w:line="14" w:lineRule="auto"/>
                        <w:rPr>
                            <w:rFonts w:ascii="Arial"/>
                            <w:sz w:val="2"/>
                        </w:rPr>
                    </w:pPr>
                </w:p>
            </w:hdr>
        </pkg:xmlData>
    </pkg:part>
    <pkg:part pkg:name="/word/settings.xml"
              pkg:contentType="application/vnd.openxmlformats-officedocument.wordprocessingml.settings+xml">
        <pkg:xmlData>
            <w:settings xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                        xmlns:o="urn:schemas-microsoft-com:office:office"
                        xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships"
                        xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math"
                        xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w10="urn:schemas-microsoft-com:office:word"
                        xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main"
                        xmlns:w14="http://schemas.microsoft.com/office/word/2010/wordml"
                        xmlns:sl="http://schemas.openxmlformats.org/schemaLibrary/2006/main"
                        xmlns:wpsCustomData="http://www.wps.cn/officeDocument/2013/wpsCustomData" mc:Ignorable="w14">
                <w:zoom w:percent="120"/>
                <w:displayBackgroundShape w:val="1"/>
                <w:documentProtection w:enforcement="0"/>
                <w:characterSpacingControl w:val="doNotCompress"/>
                <w:footnotePr>
                    <w:footnote w:id="0"/>
                    <w:footnote w:id="1"/>
                </w:footnotePr>
                <w:endnotePr>
                    <w:endnote w:id="0"/>
                    <w:endnote w:id="1"/>
                </w:endnotePr>
                <w:compat>
                    <w:spaceForUL/>
                    <w:ulTrailSpace/>
                    <w:useFELayout/>
                    <w:compatSetting w:name="compatibilityMode" w:uri="http://schemas.microsoft.com/office/word"
                                     w:val="14"/>
                </w:compat>
                <w:rsids>
                    <w:rsidRoot w:val="00000000"/>
                    <w:rsid w:val="42D10A91"/>
                </w:rsids>
                <m:mathPr>
                    <m:brkBin m:val="before"/>
                    <m:brkBinSub m:val="--"/>
                    <m:smallFrac m:val="0"/>
                    <m:dispDef/>
                    <m:lMargin m:val="0"/>
                    <m:rMargin m:val="0"/>
                    <m:defJc m:val="centerGroup"/>
                    <m:wrapIndent m:val="1440"/>
                    <m:intLim m:val="subSup"/>
                    <m:naryLim m:val="undOvr"/>
                </m:mathPr>
                <w:themeFontLang w:val="en-US" w:eastAsia="zh-CN"/>
                <w:clrSchemeMapping w:bg1="light1" w:t1="dark1" w:bg2="light2" w:t2="dark2" w:accent1="accent1"
                                    w:accent2="accent2" w:accent3="accent3" w:accent4="accent4" w:accent5="accent5"
                                    w:accent6="accent6" w:hyperlink="hyperlink"
                                    w:followedHyperlink="followedHyperlink"/>
            </w:settings>
        </pkg:xmlData>
    </pkg:part>
    <pkg:part pkg:name="/word/styles.xml"
              pkg:contentType="application/vnd.openxmlformats-officedocument.wordprocessingml.styles+xml">
        <pkg:xmlData>
            <w:styles xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                      xmlns:o="urn:schemas-microsoft-com:office:office"
                      xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships"
                      xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math"
                      xmlns:v="urn:schemas-microsoft-com:vml"
                      xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main"
                      xmlns:w14="http://schemas.microsoft.com/office/word/2010/wordml"
                      xmlns:w10="urn:schemas-microsoft-com:office:word"
                      xmlns:sl="http://schemas.openxmlformats.org/schemaLibrary/2006/main"
                      xmlns:wpsCustomData="http://www.wps.cn/officeDocument/2013/wpsCustomData" mc:Ignorable="w14">
                <w:docDefaults>
                    <w:rPrDefault>
                        <w:rPr>
                            <w:rFonts w:ascii="Arial" w:hAnsi="Arial" w:eastAsia="Arial" w:cs="Arial"/>
                        </w:rPr>
                    </w:rPrDefault>
                    <w:pPrDefault/>
                </w:docDefaults>
                <w:latentStyles w:count="260" w:defQFormat="0" w:defUnhideWhenUsed="1" w:defSemiHidden="1"
                                w:defUIPriority="99" w:defLockedState="0">
                    <w:lsdException w:qFormat="1" w:unhideWhenUsed="0" w:uiPriority="0" w:name="Normal"/>
                    <w:lsdException w:qFormat="1" w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0"
                                    w:name="heading 1"/>
                    <w:lsdException w:qFormat="1" w:uiPriority="0" w:name="heading 2"/>
                    <w:lsdException w:qFormat="1" w:uiPriority="0" w:name="heading 3"/>
                    <w:lsdException w:qFormat="1" w:uiPriority="0" w:name="heading 4"/>
                    <w:lsdException w:qFormat="1" w:uiPriority="0" w:name="heading 5"/>
                    <w:lsdException w:qFormat="1" w:uiPriority="0" w:name="heading 6"/>
                    <w:lsdException w:qFormat="1" w:uiPriority="0" w:name="heading 7"/>
                    <w:lsdException w:qFormat="1" w:uiPriority="0" w:name="heading 8"/>
                    <w:lsdException w:qFormat="1" w:uiPriority="0" w:name="heading 9"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="index 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="index 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="index 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="index 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="index 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="index 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="index 7"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="index 8"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="index 9"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="toc 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="toc 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="toc 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="toc 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="toc 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="toc 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="toc 7"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="toc 8"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="toc 9"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Normal Indent"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="footnote text"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="annotation text"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="header"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="footer"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="index heading"/>
                    <w:lsdException w:qFormat="1" w:uiPriority="0" w:name="caption"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="table of figures"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="envelope address"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="envelope return"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="footnote reference"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0"
                                    w:name="annotation reference"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="line number"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="page number"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="endnote reference"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="endnote text"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0"
                                    w:name="table of authorities"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="macro"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="toa heading"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Bullet"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Number"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Bullet 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Bullet 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Bullet 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Bullet 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Number 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Number 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Number 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Number 5"/>
                    <w:lsdException w:qFormat="1" w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0"
                                    w:name="Title"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Closing"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Signature"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:name="Default Paragraph Font"/>
                    <w:lsdException w:qFormat="1" w:unhideWhenUsed="0" w:uiPriority="0" w:name="Body Text"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Body Text Indent"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Continue"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Continue 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Continue 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Continue 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="List Continue 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Message Header"/>
                    <w:lsdException w:qFormat="1" w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0"
                                    w:name="Subtitle"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Salutation"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Date"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0"
                                    w:name="Body Text First Indent"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0"
                                    w:name="Body Text First Indent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Note Heading"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Body Text 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Body Text 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Body Text Indent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Body Text Indent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Block Text"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Hyperlink"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="FollowedHyperlink"/>
                    <w:lsdException w:qFormat="1" w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0"
                                    w:name="Strong"/>
                    <w:lsdException w:qFormat="1" w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0"
                                    w:name="Emphasis"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Document Map"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Plain Text"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="E-mail Signature"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Normal (Web)"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="HTML Acronym"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="HTML Address"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="HTML Cite"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="HTML Code"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="HTML Definition"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="HTML Keyboard"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="HTML Preformatted"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="HTML Sample"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="HTML Typewriter"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="HTML Variable"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:name="Normal Table"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="annotation subject"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Simple 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Simple 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Simple 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Classic 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Classic 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Classic 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Classic 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Colorful 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Colorful 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Colorful 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Columns 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Columns 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Columns 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Columns 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Columns 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Grid 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Grid 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Grid 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Grid 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Grid 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Grid 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Grid 7"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Grid 8"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table List 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table List 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table List 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table List 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table List 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table List 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table List 7"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table List 8"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table 3D effects 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table 3D effects 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table 3D effects 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Contemporary"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Elegant"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Professional"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Subtle 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Subtle 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Web 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Web 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Web 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Balloon Text"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Grid"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="0" w:semiHidden="0" w:name="Table Theme"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="60" w:semiHidden="0" w:name="Light Shading"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="61" w:semiHidden="0" w:name="Light List"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="62" w:semiHidden="0" w:name="Light Grid"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="63" w:semiHidden="0" w:name="Medium Shading 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="64" w:semiHidden="0" w:name="Medium Shading 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="65" w:semiHidden="0" w:name="Medium List 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="66" w:semiHidden="0" w:name="Medium List 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="67" w:semiHidden="0" w:name="Medium Grid 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="68" w:semiHidden="0" w:name="Medium Grid 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="69" w:semiHidden="0" w:name="Medium Grid 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="70" w:semiHidden="0" w:name="Dark List"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="71" w:semiHidden="0" w:name="Colorful Shading"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="72" w:semiHidden="0" w:name="Colorful List"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="73" w:semiHidden="0" w:name="Colorful Grid"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="60" w:semiHidden="0"
                                    w:name="Light Shading Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="61" w:semiHidden="0"
                                    w:name="Light List Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="62" w:semiHidden="0"
                                    w:name="Light Grid Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="63" w:semiHidden="0"
                                    w:name="Medium Shading 1 Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="64" w:semiHidden="0"
                                    w:name="Medium Shading 2 Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="65" w:semiHidden="0"
                                    w:name="Medium List 1 Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="66" w:semiHidden="0"
                                    w:name="Medium List 2 Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="67" w:semiHidden="0"
                                    w:name="Medium Grid 1 Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="68" w:semiHidden="0"
                                    w:name="Medium Grid 2 Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="69" w:semiHidden="0"
                                    w:name="Medium Grid 3 Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="70" w:semiHidden="0"
                                    w:name="Dark List Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="71" w:semiHidden="0"
                                    w:name="Colorful Shading Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="72" w:semiHidden="0"
                                    w:name="Colorful List Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="73" w:semiHidden="0"
                                    w:name="Colorful Grid Accent 1"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="60" w:semiHidden="0"
                                    w:name="Light Shading Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="61" w:semiHidden="0"
                                    w:name="Light List Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="62" w:semiHidden="0"
                                    w:name="Light Grid Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="63" w:semiHidden="0"
                                    w:name="Medium Shading 1 Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="64" w:semiHidden="0"
                                    w:name="Medium Shading 2 Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="65" w:semiHidden="0"
                                    w:name="Medium List 1 Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="66" w:semiHidden="0"
                                    w:name="Medium List 2 Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="67" w:semiHidden="0"
                                    w:name="Medium Grid 1 Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="68" w:semiHidden="0"
                                    w:name="Medium Grid 2 Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="69" w:semiHidden="0"
                                    w:name="Medium Grid 3 Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="70" w:semiHidden="0"
                                    w:name="Dark List Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="71" w:semiHidden="0"
                                    w:name="Colorful Shading Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="72" w:semiHidden="0"
                                    w:name="Colorful List Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="73" w:semiHidden="0"
                                    w:name="Colorful Grid Accent 2"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="60" w:semiHidden="0"
                                    w:name="Light Shading Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="61" w:semiHidden="0"
                                    w:name="Light List Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="62" w:semiHidden="0"
                                    w:name="Light Grid Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="63" w:semiHidden="0"
                                    w:name="Medium Shading 1 Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="64" w:semiHidden="0"
                                    w:name="Medium Shading 2 Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="65" w:semiHidden="0"
                                    w:name="Medium List 1 Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="66" w:semiHidden="0"
                                    w:name="Medium List 2 Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="67" w:semiHidden="0"
                                    w:name="Medium Grid 1 Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="68" w:semiHidden="0"
                                    w:name="Medium Grid 2 Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="69" w:semiHidden="0"
                                    w:name="Medium Grid 3 Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="70" w:semiHidden="0"
                                    w:name="Dark List Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="71" w:semiHidden="0"
                                    w:name="Colorful Shading Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="72" w:semiHidden="0"
                                    w:name="Colorful List Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="73" w:semiHidden="0"
                                    w:name="Colorful Grid Accent 3"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="60" w:semiHidden="0"
                                    w:name="Light Shading Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="61" w:semiHidden="0"
                                    w:name="Light List Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="62" w:semiHidden="0"
                                    w:name="Light Grid Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="63" w:semiHidden="0"
                                    w:name="Medium Shading 1 Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="64" w:semiHidden="0"
                                    w:name="Medium Shading 2 Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="65" w:semiHidden="0"
                                    w:name="Medium List 1 Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="66" w:semiHidden="0"
                                    w:name="Medium List 2 Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="67" w:semiHidden="0"
                                    w:name="Medium Grid 1 Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="68" w:semiHidden="0"
                                    w:name="Medium Grid 2 Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="69" w:semiHidden="0"
                                    w:name="Medium Grid 3 Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="70" w:semiHidden="0"
                                    w:name="Dark List Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="71" w:semiHidden="0"
                                    w:name="Colorful Shading Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="72" w:semiHidden="0"
                                    w:name="Colorful List Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="73" w:semiHidden="0"
                                    w:name="Colorful Grid Accent 4"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="60" w:semiHidden="0"
                                    w:name="Light Shading Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="61" w:semiHidden="0"
                                    w:name="Light List Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="62" w:semiHidden="0"
                                    w:name="Light Grid Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="63" w:semiHidden="0"
                                    w:name="Medium Shading 1 Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="64" w:semiHidden="0"
                                    w:name="Medium Shading 2 Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="65" w:semiHidden="0"
                                    w:name="Medium List 1 Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="66" w:semiHidden="0"
                                    w:name="Medium List 2 Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="67" w:semiHidden="0"
                                    w:name="Medium Grid 1 Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="68" w:semiHidden="0"
                                    w:name="Medium Grid 2 Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="69" w:semiHidden="0"
                                    w:name="Medium Grid 3 Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="70" w:semiHidden="0"
                                    w:name="Dark List Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="71" w:semiHidden="0"
                                    w:name="Colorful Shading Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="72" w:semiHidden="0"
                                    w:name="Colorful List Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="73" w:semiHidden="0"
                                    w:name="Colorful Grid Accent 5"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="60" w:semiHidden="0"
                                    w:name="Light Shading Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="61" w:semiHidden="0"
                                    w:name="Light List Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="62" w:semiHidden="0"
                                    w:name="Light Grid Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="63" w:semiHidden="0"
                                    w:name="Medium Shading 1 Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="64" w:semiHidden="0"
                                    w:name="Medium Shading 2 Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="65" w:semiHidden="0"
                                    w:name="Medium List 1 Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="66" w:semiHidden="0"
                                    w:name="Medium List 2 Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="67" w:semiHidden="0"
                                    w:name="Medium Grid 1 Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="68" w:semiHidden="0"
                                    w:name="Medium Grid 2 Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="69" w:semiHidden="0"
                                    w:name="Medium Grid 3 Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="70" w:semiHidden="0"
                                    w:name="Dark List Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="71" w:semiHidden="0"
                                    w:name="Colorful Shading Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="72" w:semiHidden="0"
                                    w:name="Colorful List Accent 6"/>
                    <w:lsdException w:unhideWhenUsed="0" w:uiPriority="73" w:semiHidden="0"
                                    w:name="Colorful Grid Accent 6"/>
                </w:latentStyles>
                <w:style w:type="paragraph" w:default="1" w:styleId="1">
                    <w:name w:val="Normal"/>
                    <w:autoRedefine/>
                    <w:semiHidden/>
                    <w:qFormat/>
                    <w:uiPriority w:val="0"/>
                    <w:pPr>
                        <w:kinsoku w:val="0"/>
                        <w:autoSpaceDE w:val="0"/>
                        <w:autoSpaceDN w:val="0"/>
                        <w:adjustRightInd w:val="0"/>
                        <w:snapToGrid w:val="0"/>
                        <w:spacing w:line="240" w:lineRule="auto"/>
                        <w:jc w:val="left"/>
                        <w:textAlignment w:val="baseline"/>
                    </w:pPr>
                    <w:rPr>
                        <w:rFonts w:ascii="Arial" w:hAnsi="Arial" w:eastAsia="Arial" w:cs="Arial"/>
                        <w:snapToGrid w:val="0"/>
                        <w:color w:val="000000"/>
                        <w:kern w:val="0"/>
                        <w:sz w:val="21"/>
                        <w:szCs w:val="21"/>
                        <w:lang w:val="en-US" w:eastAsia="en-US" w:bidi="ar-SA"/>
                    </w:rPr>
                </w:style>
                <w:style w:type="character" w:default="1" w:styleId="4">
                    <w:name w:val="Default Paragraph Font"/>
                    <w:semiHidden/>
                    <w:uiPriority w:val="0"/>
                </w:style>
                <w:style w:type="table" w:default="1" w:styleId="3">
                    <w:name w:val="Normal Table"/>
                    <w:semiHidden/>
                    <w:uiPriority w:val="0"/>
                    <w:tblPr>
                        <w:tblCellMar>
                            <w:top w:w="0" w:type="dxa"/>
                            <w:left w:w="108" w:type="dxa"/>
                            <w:bottom w:w="0" w:type="dxa"/>
                            <w:right w:w="108" w:type="dxa"/>
                        </w:tblCellMar>
                    </w:tblPr>
                </w:style>
                <w:style w:type="paragraph" w:styleId="2">
                    <w:name w:val="Body Text"/>
                    <w:basedOn w:val="1"/>
                    <w:autoRedefine/>
                    <w:semiHidden/>
                    <w:qFormat/>
                    <w:uiPriority w:val="0"/>
                    <w:rPr>
                        <w:rFonts w:ascii="Arial" w:hAnsi="Arial" w:eastAsia="Arial" w:cs="Arial"/>
                        <w:sz w:val="21"/>
                        <w:szCs w:val="21"/>
                        <w:lang w:val="en-US" w:eastAsia="en-US" w:bidi="ar-SA"/>
                    </w:rPr>
                </w:style>
                <w:style w:type="table" w:customStyle="1" w:styleId="5">
                    <w:name w:val="Table Normal"/>
                    <w:autoRedefine/>
                    <w:semiHidden/>
                    <w:unhideWhenUsed/>
                    <w:qFormat/>
                    <w:uiPriority w:val="0"/>
                    <w:tblPr>
                        <w:tblCellMar>
                            <w:top w:w="0" w:type="dxa"/>
                            <w:left w:w="0" w:type="dxa"/>
                            <w:bottom w:w="0" w:type="dxa"/>
                            <w:right w:w="0" w:type="dxa"/>
                        </w:tblCellMar>
                    </w:tblPr>
                </w:style>
                <w:style w:type="paragraph" w:customStyle="1" w:styleId="6">
                    <w:name w:val="Table Text"/>
                    <w:basedOn w:val="1"/>
                    <w:autoRedefine/>
                    <w:semiHidden/>
                    <w:qFormat/>
                    <w:uiPriority w:val="0"/>
                    <w:rPr>
                        <w:rFonts w:ascii="Arial" w:hAnsi="Arial" w:eastAsia="Arial" w:cs="Arial"/>
                        <w:sz w:val="21"/>
                        <w:szCs w:val="21"/>
                        <w:lang w:val="en-US" w:eastAsia="en-US" w:bidi="ar-SA"/>
                    </w:rPr>
                </w:style>
            </w:styles>
        </pkg:xmlData>
    </pkg:part>
    <pkg:part pkg:name="/word/theme/theme1.xml"
              pkg:contentType="application/vnd.openxmlformats-officedocument.theme+xml">
        <pkg:xmlData>
            <a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office">
                <a:themeElements>
                    <a:clrScheme name="Office">
                        <a:dk1>
                            <a:sysClr val="windowText" lastClr="000000"/>
                        </a:dk1>
                        <a:lt1>
                            <a:sysClr val="window" lastClr="FFFFFF"/>
                        </a:lt1>
                        <a:dk2>
                            <a:srgbClr val="1F497D"/>
                        </a:dk2>
                        <a:lt2>
                            <a:srgbClr val="EEECE1"/>
                        </a:lt2>
                        <a:accent1>
                            <a:srgbClr val="4F81BD"/>
                        </a:accent1>
                        <a:accent2>
                            <a:srgbClr val="C0504D"/>
                        </a:accent2>
                        <a:accent3>
                            <a:srgbClr val="9BBB59"/>
                        </a:accent3>
                        <a:accent4>
                            <a:srgbClr val="8064A2"/>
                        </a:accent4>
                        <a:accent5>
                            <a:srgbClr val="4BACC6"/>
                        </a:accent5>
                        <a:accent6>
                            <a:srgbClr val="F79646"/>
                        </a:accent6>
                        <a:hlink>
                            <a:srgbClr val="0000FF"/>
                        </a:hlink>
                        <a:folHlink>
                            <a:srgbClr val="800080"/>
                        </a:folHlink>
                    </a:clrScheme>
                    <a:fontScheme name="Office">
                        <a:majorFont>
                            <a:latin typeface="Cambria"/>
                            <a:ea typeface=""/>
                            <a:cs typeface=""/>
                            <a:font script="Jpan" typeface="ＭＳ ゴシック"/>
                            <a:font script="Hang" typeface="맑은 고딕"/>
                            <a:font script="Hans" typeface="宋体"/>
                            <a:font script="Hant" typeface="新細明體"/>
                            <a:font script="Arab" typeface="Times New Roman"/>
                            <a:font script="Hebr" typeface="Times New Roman"/>
                            <a:font script="Thai" typeface="Angsana New"/>
                            <a:font script="Ethi" typeface="Nyala"/>
                            <a:font script="Beng" typeface="Vrinda"/>
                            <a:font script="Gujr" typeface="Shruti"/>
                            <a:font script="Khmr" typeface="MoolBoran"/>
                            <a:font script="Knda" typeface="Tunga"/>
                            <a:font script="Guru" typeface="Raavi"/>
                            <a:font script="Cans" typeface="Euphemia"/>
                            <a:font script="Cher" typeface="Plantagenet Cherokee"/>
                            <a:font script="Yiii" typeface="Microsoft Yi Baiti"/>
                            <a:font script="Tibt" typeface="Microsoft Himalaya"/>
                            <a:font script="Thaa" typeface="MV Boli"/>
                            <a:font script="Deva" typeface="Mangal"/>
                            <a:font script="Telu" typeface="Gautami"/>
                            <a:font script="Taml" typeface="Latha"/>
                            <a:font script="Syrc" typeface="Estrangelo Edessa"/>
                            <a:font script="Orya" typeface="Kalinga"/>
                            <a:font script="Mlym" typeface="Kartika"/>
                            <a:font script="Laoo" typeface="DokChampa"/>
                            <a:font script="Sinh" typeface="Iskoola Pota"/>
                            <a:font script="Mong" typeface="Mongolian Baiti"/>
                            <a:font script="Viet" typeface="Times New Roman"/>
                            <a:font script="Uigh" typeface="Microsoft Uighur"/>
                            <a:font script="Geor" typeface="Sylfaen"/>
                        </a:majorFont>
                        <a:minorFont>
                            <a:latin typeface="Calibri"/>
                            <a:ea typeface=""/>
                            <a:cs typeface=""/>
                            <a:font script="Jpan" typeface="ＭＳ 明朝"/>
                            <a:font script="Hang" typeface="맑은 고딕"/>
                            <a:font script="Hans" typeface="宋体"/>
                            <a:font script="Hant" typeface="新細明體"/>
                            <a:font script="Arab" typeface="Arial"/>
                            <a:font script="Hebr" typeface="Arial"/>
                            <a:font script="Thai" typeface="Cordia New"/>
                            <a:font script="Ethi" typeface="Nyala"/>
                            <a:font script="Beng" typeface="Vrinda"/>
                            <a:font script="Gujr" typeface="Shruti"/>
                            <a:font script="Khmr" typeface="DaunPenh"/>
                            <a:font script="Knda" typeface="Tunga"/>
                            <a:font script="Guru" typeface="Raavi"/>
                            <a:font script="Cans" typeface="Euphemia"/>
                            <a:font script="Cher" typeface="Plantagenet Cherokee"/>
                            <a:font script="Yiii" typeface="Microsoft Yi Baiti"/>
                            <a:font script="Tibt" typeface="Microsoft Himalaya"/>
                            <a:font script="Thaa" typeface="MV Boli"/>
                            <a:font script="Deva" typeface="Mangal"/>
                            <a:font script="Telu" typeface="Gautami"/>
                            <a:font script="Taml" typeface="Latha"/>
                            <a:font script="Syrc" typeface="Estrangelo Edessa"/>
                            <a:font script="Orya" typeface="Kalinga"/>
                            <a:font script="Mlym" typeface="Kartika"/>
                            <a:font script="Laoo" typeface="DokChampa"/>
                            <a:font script="Sinh" typeface="Iskoola Pota"/>
                            <a:font script="Mong" typeface="Mongolian Baiti"/>
                            <a:font script="Viet" typeface="Arial"/>
                            <a:font script="Uigh" typeface="Microsoft Uighur"/>
                            <a:font script="Geor" typeface="Sylfaen"/>
                        </a:minorFont>
                    </a:fontScheme>
                    <a:fmtScheme name="Office">
                        <a:fillStyleLst>
                            <a:solidFill>
                                <a:schemeClr val="phClr"/>
                            </a:solidFill>
                            <a:gradFill rotWithShape="1">
                                <a:gsLst>
                                    <a:gs pos="0">
                                        <a:schemeClr val="phClr">
                                            <a:tint val="50000"/>
                                            <a:satMod val="300000"/>
                                        </a:schemeClr>
                                    </a:gs>
                                    <a:gs pos="35000">
                                        <a:schemeClr val="phClr">
                                            <a:tint val="37000"/>
                                            <a:satMod val="300000"/>
                                        </a:schemeClr>
                                    </a:gs>
                                    <a:gs pos="100000">
                                        <a:schemeClr val="phClr">
                                            <a:tint val="15000"/>
                                            <a:satMod val="350000"/>
                                        </a:schemeClr>
                                    </a:gs>
                                </a:gsLst>
                                <a:lin ang="16200000" scaled="1"/>
                            </a:gradFill>
                            <a:gradFill rotWithShape="1">
                                <a:gsLst>
                                    <a:gs pos="0">
                                        <a:schemeClr val="phClr">
                                            <a:shade val="51000"/>
                                            <a:satMod val="130000"/>
                                        </a:schemeClr>
                                    </a:gs>
                                    <a:gs pos="80000">
                                        <a:schemeClr val="phClr">
                                            <a:shade val="93000"/>
                                            <a:satMod val="130000"/>
                                        </a:schemeClr>
                                    </a:gs>
                                    <a:gs pos="100000">
                                        <a:schemeClr val="phClr">
                                            <a:shade val="94000"/>
                                            <a:satMod val="135000"/>
                                        </a:schemeClr>
                                    </a:gs>
                                </a:gsLst>
                                <a:lin ang="16200000" scaled="0"/>
                            </a:gradFill>
                        </a:fillStyleLst>
                        <a:lnStyleLst>
                            <a:ln w="9525" cap="flat" cmpd="sng" algn="ctr">
                                <a:solidFill>
                                    <a:schemeClr val="phClr">
                                        <a:shade val="95000"/>
                                        <a:satMod val="105000"/>
                                    </a:schemeClr>
                                </a:solidFill>
                                <a:prstDash val="solid"/>
                            </a:ln>
                            <a:ln w="25400" cap="flat" cmpd="sng" algn="ctr">
                                <a:solidFill>
                                    <a:schemeClr val="phClr"/>
                                </a:solidFill>
                                <a:prstDash val="solid"/>
                            </a:ln>
                            <a:ln w="38100" cap="flat" cmpd="sng" algn="ctr">
                                <a:solidFill>
                                    <a:schemeClr val="phClr"/>
                                </a:solidFill>
                                <a:prstDash val="solid"/>
                            </a:ln>
                        </a:lnStyleLst>
                        <a:effectStyleLst>
                            <a:effectStyle>
                                <a:effectLst>
                                    <a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0">
                                        <a:srgbClr val="000000">
                                            <a:alpha val="38000"/>
                                        </a:srgbClr>
                                    </a:outerShdw>
                                </a:effectLst>
                            </a:effectStyle>
                            <a:effectStyle>
                                <a:effectLst>
                                    <a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0">
                                        <a:srgbClr val="000000">
                                            <a:alpha val="35000"/>
                                        </a:srgbClr>
                                    </a:outerShdw>
                                </a:effectLst>
                            </a:effectStyle>
                            <a:effectStyle>
                                <a:effectLst>
                                    <a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0">
                                        <a:srgbClr val="000000">
                                            <a:alpha val="35000"/>
                                        </a:srgbClr>
                                    </a:outerShdw>
                                </a:effectLst>
                                <a:scene3d>
                                    <a:camera prst="orthographicFront">
                                        <a:rot lat="0" lon="0" rev="0"/>
                                    </a:camera>
                                    <a:lightRig rig="threePt" dir="t">
                                        <a:rot lat="0" lon="0" rev="1200000"/>
                                    </a:lightRig>
                                </a:scene3d>
                                <a:sp3d>
                                    <a:bevelT w="63500" h="25400"/>
                                </a:sp3d>
                            </a:effectStyle>
                        </a:effectStyleLst>
                        <a:bgFillStyleLst>
                            <a:solidFill>
                                <a:schemeClr val="phClr"/>
                            </a:solidFill>
                            <a:gradFill rotWithShape="1">
                                <a:gsLst>
                                    <a:gs pos="0">
                                        <a:schemeClr val="phClr">
                                            <a:tint val="40000"/>
                                            <a:satMod val="350000"/>
                                        </a:schemeClr>
                                    </a:gs>
                                    <a:gs pos="40000">
                                        <a:schemeClr val="phClr">
                                            <a:tint val="45000"/>
                                            <a:satMod val="350000"/>
                                            <a:shade val="99000"/>
                                        </a:schemeClr>
                                    </a:gs>
                                    <a:gs pos="100000">
                                        <a:schemeClr val="phClr">
                                            <a:shade val="20000"/>
                                            <a:satMod val="255000"/>
                                        </a:schemeClr>
                                    </a:gs>
                                </a:gsLst>
                                <a:path path="circle">
                                    <a:fillToRect l="50000" t="-80000" r="50000" b="180000"/>
                                </a:path>
                            </a:gradFill>
                            <a:gradFill rotWithShape="1">
                                <a:gsLst>
                                    <a:gs pos="0">
                                        <a:schemeClr val="phClr">
                                            <a:tint val="80000"/>
                                            <a:satMod val="300000"/>
                                        </a:schemeClr>
                                    </a:gs>
                                    <a:gs pos="100000">
                                        <a:schemeClr val="phClr">
                                            <a:shade val="30000"/>
                                            <a:satMod val="200000"/>
                                        </a:schemeClr>
                                    </a:gs>
                                </a:gsLst>
                                <a:path path="circle">
                                    <a:fillToRect l="50000" t="50000" r="50000" b="50000"/>
                                </a:path>
                            </a:gradFill>
                        </a:bgFillStyleLst>
                    </a:fmtScheme>
                </a:themeElements>
                <a:objectDefaults/>
            </a:theme>
        </pkg:xmlData>
    </pkg:part>
</pkg:package>