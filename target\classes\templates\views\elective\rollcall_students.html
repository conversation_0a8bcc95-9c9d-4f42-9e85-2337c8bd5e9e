<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
        content="width=device-width, user-scalable=no,initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <title>学生管理</title>
    <script src="js/responsive.js"></script>
    <link rel="stylesheet" href="css/global.css">
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/stuManage.css">
</head>
<body>
    <div class="search-wrap">
        <div class="search flex">
            <img src="images/search.png" alt="">
            <input type="text" id="keywordIp" placeholder="搜索学生姓名或学号" onkeyup="getList();">
        </div>
    </div>
    <div class="stu-wrap">
        <div class="intro">
            说明：删除学生后该学生将不再出现在本教学班点名名单中
        </div>
        <div class="stu-list" id="dataBox">
        </div>
    </div>
    <div class="dialog-wrap" id="dialogTips">
        <div class="dialog dialog-tips">
            <div class="dialog-con">
                确定<span class="stu-state1">删除</span><span class="stu-name">测试学生5</span>？
            </div>
            <div class="dialog-btn">
                <div class="btn btn-cancel">取消</div>
                <div class="btn btn-sure">确定</div>
            </div>
        </div>
    </div>
</body>
<script src="js/zepto.min.js"></script>
<script src="js/iosSelect.js"></script>
<script src="js/CXJSBridge.js"></script>
<script>
	var teachingClassCode = '[[${teachingClassCode}]]';
    $(document).ready(function () {
        var stuH = $(window).height() - $(".stu-wrap").offset().top;
        $(".stu-wrap").css("height", stuH + "px");
        // 删除
        var $stuEle = "";
        var uid = "";
        var xsid = "";
        $(".stu-list").on('click', ".stu .stu-btn", function () {
            $stuEle = $(this);
            uid = $(this).attr("uuuid");
            xsid = $(this).attr("uuxsid");
            var txt = $(this).prev().text();
            $("#dialogTips .stu-name").text(txt);
            // 删除
            if ($(this).hasClass("del")) {
                $("#dialogTips .stu-state1").text("删除");
            }
            // 恢复
            if ($(this).hasClass("restore")) {
                $("#dialogTips .stu-state1").text("恢复");
            }
            $("#dialogTips").show();
        })
        // 确定
        $("#dialogTips .dialog-btn .btn-sure").click(function () {
        	var status = $("#dialogTips .stu-state1").text();
            if ($("#dialogTips .stu-state1").text() == "删除") {
                $stuEle.text('恢复').addClass('restore').removeClass('del');
            } else if ($("#dialogTips .stu-state1").text() == "恢复") {
                $stuEle.text('删除').addClass('del').removeClass('restore');
            }
            $.ajax({
	        	type: "POST",
	        	url: "/elective/rollcall/updateRollcallStudentsDel",
	        	data:{teachingClassCode:teachingClassCode,status:status,uid:uid,xsid:xsid},
	        	success: function () {
	       		}
			});
            $("#dialogTips").hide();
        })
        // 取消
        $("#dialogTips .dialog-btn .btn-cancel").click(function () {
            $("#dialogTips").hide();
        })
    })
    getList();
    function getList(){
    	var keyword = $("#keywordIp").val();
    	$.ajax({
        	type: "POST",
        	url: "/elective/rollcall/getStudents",
        	data:{teachingClassCode:teachingClassCode,keyword:keyword},
        	dataType:'json',
        	success: function (data) {
        		var html = "";
            	if(data.status){
            		for(var i = 0;i<data.list.length;i++){
            			html += "<div class=\"stu\">";
                		html += "<h1>"+data.list[i].jxbxsb_xsxm+"</h1>";
                		if(data.list[i].sjly == "0"){
                			html += "<span class=\"stu-btn del\" uuuid=\""+data.list[i].jxbxsb_xslxr.puid+"\" uuxsid=\""+data.list[i].xsdataid+"\">删除</span>";
                		}else{
                			html += "<span class=\"stu-btn restore\" uuuid=\""+data.list[i].jxbxsb_xslxr.puid+"\" uuxsid=\""+data.list[i].xsdataid+"\">恢复</span>";
                		}
            			html += "</div>";
            		}
            	}
            	$("#dataBox").html(html);
       		}
		});
    }
</script>
</html>