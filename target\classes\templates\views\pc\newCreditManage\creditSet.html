<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学分管理</title>
    <link rel="stylesheet" th:href="@{~/css/creditManage/global.css}">
    <link rel="stylesheet" th:href="@{~/plugin/layui-v2.8.18/layui/css/layui.css}">
    <link rel="stylesheet" th:href="@{~/css/creditManage/common.css}">
    <link rel="stylesheet" th:href="@{~/css/creditManage/reset.css}">
    <link rel="stylesheet" th:href="@{~/css/creditManage/index.css}">
    <style>
        .main .con .c-item {
            margin-bottom: 40px;
            display: none;
        }
    </style>
</head>

<body>
<div class="main">
    <div class="top">
        <div class="tab">
            <ul>
                <li class="cur">基础参数设置</li>
                <li>学分规则设置</li>
            </ul>
        </div>
        <div class="btn">保存设置</div>
    </div>
    <div class="con">
        <div class="c-item active layui-form">
            <div class="c-top">
                <h4>基础设置</h4>
            </div>
            <form class="layui-form" lay-filter="secondaryEditForm">
                <div class="lab">
                    <div class="name">学年学期</div>
                    <div class="j-search-con single-box">
                        <input type="text" placeholder="请选择" readonly class="schoolSel" id="xnxq" name="xnxq">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <ul class="xnxqul" id="xnxqul1">
                                <li>2022-10-11</li>
                                <li>2022-10-12</li>
                                <li>2022-10-13</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="lab">
                    <div class="name">学分保留小数位数</div>
                    <div class="input">
                        <input type="number" id="decimal" name="decimal"
                               lay-verify="title" autocomplete="off"
                               placeholder="请输入整数"
                               class="layui-input"
                               oninput="value=value.replace(/[^\d]/g,'');if(value>5)value=5;if(value<0)value=0;">
                    </div>
                </div>
                <div class="lab layui-form">
                    <div class="name">学分是否需要四舍五入</div>
                    <div class="radio">
                        <div class="limit-switch">
                            <input type="checkbox" checked lay-skin="switch"
                                   lay-filter="switchGradeFilter1" id="round" name="round">
                            <em class="tit">是</em>
                        </div>
                    </div>
                </div>
                <div class="lab">
                    <div class="name">是否复制以往学年学期学分规则</div>
                    <div class="radio">
                        <div class="limit-switch">
                            <input type="checkbox" name="switchGradeFilter2" lay-skin="switch"
                                   lay-filter="switchGradeFilter2">
                            <em class="tit">否</em>
                        </div>
                    </div>
                </div>
                <div class="lab hide multiplex">
                    <div class="name">复用学期学年</div>
                    <div class="j-search-con single-box">
                        <input type="text" placeholder="请选择" readonly class="schoolSel" id="copyXnxq">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <ul class="xnxqul" id="xnxqul2">
                                <li>2022-10-11</li>
                                <li>2022-10-12</li>
                                <li>2022-10-13</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="lab hide multiplex creditRange">
                    <div class="name">复制学分范围</div>
                    <div class="radio">
                        <span class="cur">全部</span>
                        <span>部分</span>
                    </div>
                </div>

                <div class="selectBox hide" id="copyName">
                    <div class="name">复制学分名称</div>
                    <div class="j-search-con multiple-box">
                        <input type="text" placeholder="请选择" readonly class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year ">
                            <div class="allSelect">
                                全选
                            </div>
                            <ul id="creditNameList">
                            </ul>
                        </div>
                    </div>
                </div>
            </form>

        </div>
        <div class="c-item">
            <div class="c-top">
                <h4>学分设置</h4>
                <div class="btns-list">
                    <span class="add">添加学分类型</span>
                    <span class="set">学分编组设置</span>
                    <span class="delet">删除</span>
                </div>
            </div>
            <div class="c-table">
                <table class="layui-table" id="materialTable" lay-filter="materialTable"></table>
            </div>
        </div>
    </div>
</div>

<!-- 添加 -->
<div class="popup addPopup" style="display:none;">
    <div class="popup-box">
        <div class="pu-title">添加学分类型</div>
        <input type="hidden" class="creditId">
        <div class="pu-con">
            <div class="lab">
                <div class="name"><em style="visibility: hidden;">*</em>学分代码</div>
                <div class="input">
                    <input type="text" name="title" lay-verify="title" autocomplete="off" placeholder="请输入"
                           class="layui-input creditCode" readonly id="creditCode"
                           style="color: #ccc;cursor: not-allowed">
                </div>
            </div>
            <div class="lab">
                <div class="name"><em>*</em>学分名称</div>
                <div class="input">
                    <input type="text" name="title" lay-verify="title" autocomplete="off" placeholder="请输入"
                           class="layui-input creditName">
                </div>
            </div>
            <div class="lab">
                <div class="name"><em>*</em>学分类型</div>
                <div class="j-search-con single-box score-type">
                    <input type="text" placeholder="请选择" readonly class="schoolSel creditType">
                    <span class="j-arrow"></span>
                    <div class="j-select-year">
                        <ul>
                            <li>
                                <span>课程类学分</span>
                                <div class="tips"><em>通过课程成绩获得的学分</em></div>
                            </li>
                            <li>
                                <span>竞赛类学分</span>
                                <div class="tips"><em>参加各类竞赛获得的学分</em></div>
                            </li>
                            <li>
                                <span>证书类学分</span>
                                <div class="tips"><em>技能等级证书等获得的学分</em></div>
                            </li>
                            <li>
                                <span>实习类学分</span>
                                <div class="tips"><em>见习、实习等获得的学分</em></div>
                            </li>
                            <li>
                                <span>德育类学分</span>
                                <div class="tips"><em>在校平时表现获得的学分</em></div>
                            </li>
                            <li>
                                <span>其他类学分</span>
                                <div class="tips"><em>除以上类别，人工导入的学分</em></div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="lab">
                <div class="name">学分所属编组</div>
                <div class="j-search-con single-box" style="width:150px">
                    <input type="text" placeholder="请选择" readonly="" class="schoolSel creditGroup"
                           style="width:100%">
                    <span class="j-arrow"></span>
                    <div class="j-select-year group" style="width:100%">
                        <ul id="groupList">

                        </ul>
                    </div>
                </div>
                <div class="setGroup">配置编组</div>
            </div>
            <div class="lab" id="isRegisterCredit">
                <div class="name" style="width:154px;">是否直接登记学分</div>
                <div class="switc-con">
                    <div class="switch"><span></span></div>
                    <div class="switch-con">关闭</div>
                </div>
                <div class="tips"><em>开启后，手动登记学分结果</em></div>
            </div>
        </div>
        <div class="pu-btn">
            <button class="pu-cancel">取消</button>
            <button class="pu-sure">确定</button>
        </div>

    </div>
</div>

<div class="popup creditGroupSet" style="display:none;">
    <div class="popup-box">
        <div class="pu-title">学分编组设置</div>
        <div class="pu-con">
            <div class="p-top">
                <div class="btns-list">
                    <span class="add">添加</span>
                    <span class="delet">删除</span>
                </div>
            </div>
            <div class="p-table">
                <table
                        class="layui-table"
                        id="scoreTable"
                        lay-filter="scoreTable"
                ></table>
            </div>
        </div>
        <div class="pu-btn">
            <button class="pu-cancel">取消</button>
            <button class="pu-sure">确定</button>
        </div>

    </div>
</div>

<div class="popup addGroup" style="display:none;">
    <div class="popup-box">
        <div class="pu-title">添加</div>
        <input type="hidden" class="groupid">
        <div class="pu-con">
            <div class="lab">
                <div class="name"><em>*</em>学分编组代码</div>
                <div class="input">
                    <input type="text" name="title" lay-verify="title" autocomplete="off" placeholder="请输入"
                           class="layui-input lay2" readonly id="groupCode" style="color: #ccc;cursor: not-allowed">
                </div>
            </div>
            <div class="lab">
                <div class="name"><em>*</em>学分编组名称</div>
                <div class="input">
                    <input type="text" name="title" lay-verify="title" autocomplete="off" placeholder="请输入"
                           class="layui-input lay1">
                </div>
            </div>
        </div>
        <div class="pu-btn">
            <button class="pu-cancel">取消</button>
            <button class="pu-sure">确定</button>
        </div>

    </div>
</div>

</body>
<script th:src="@{~/js/jquery-3.3.1.min.js}"></script>
<script th:src="@{~/plugin/layui/layui2.8.12.js}"></script>
<script th:src="@{~/js/creditManage/common.js}"></script>
<script th:src="@{~/js/my.util.js}"></script>
<script type="text/html" id="tmplToolBar2">
    <div class="oprate-table">
        {{# if(d.creditType !='其他类学分' || d.isRegisterCredit == 0){ }}
        <div class="set" lay-event="set">规则设置</div>
        {{# } }}
        <div class="edit" lay-event="edit">编辑</div>
        {{# if(d.status =='1'){ }}
        <div class="deactivate" lay-event="deactivate">停用</div>
        {{# } else { }}
        <div class="deactivate" lay-event="deactivate">启用</div>
        {{# } }}
        <div class="delet" lay-event="delet">删除</div>
    </div>
</script>
<script type="text/html" id="status">
    {{#  if(d.status==1){ }}
    <span>是</span>
    {{#  } else { }}
    <span>否</span>
    {{#  } }}
</script>
<script type="text/html" id="tmplToolBar3">
    <div class="oprates-table">
        <div class="edit" lay-event="edit">编辑</div>
        {{# if(d.status =='1'){ }}
        <div class="deactivate" lay-event="deactivate">停用</div>
        {{# } else { }}
        <div class="deactivate" lay-event="deactivate">启用</div>
        {{# } }}
        <div class="delet" lay-event="delet">删除</div>
    </div>
</script>
<script th:inline="javascript">
    var fid = [[${fid}]];
    var uid = [[${uid}]];

    layui.use(["table", "jquery", "form"], function () {
        var table = layui.table;
        var $ = layui.jquery;
        var form = layui.form;

        //切換
        $(".main .top").on("click", ".tab ul li", function () {
            $(this).addClass("cur").siblings().removeClass("cur");
            let nus = $(this).index();
            $(".main .con .c-item").eq(nus).addClass("active").siblings().removeClass("active");
            if (nus == 1) {
                $(".btn").hide()
            } else {
                $(".btn").show()
            }
        })

        /*基础设置 start*/
        $(function () {
            getXnxq();
        });

        function getXnxq() {
            $.ajax({
                type: 'get',
                dataType: 'json',
                url: '/basic/xnxq',
                data: {fid: fid},
                success: function (res) {
                    if (res.code == 200) {
                        var curxnxq = '';
                        var data = res.data;
                        var html = '';
                        for (let i = 0; i < data.length; i++) {
                            if (data[i].xnxq_sfdqxq == '是') {
                                curxnxq = data[i].xnxq_xnxqh;
                                html += "<li class='active'>" + data[i].xnxq_xnxqh + "</li>"
                            } else {
                                html += "<li>" + data[i].xnxq_xnxqh + "</li>"
                            }
                        }
                        $("#xnxq").val(curxnxq);
                        $(".xnxqul").html(html);
                        getCreditSet(curxnxq);
                    }
                }
            })
        }

        var sid = -1;

        function getCreditSet(xnxq) {
            $.get("/new/creditBasicSet/getBasicSet", {fid: fid, xnxq: xnxq}, function (res) {
                let val1 = 0;
                let val2 = 0;
                let em = "否";
                if (res.code == 200) {
                    $("#decimal").val(res.data.decimal);
                    if (res.data.round) {
                        val1 = 1;
                        em = "是";
                    } else {
                        val1 = 0;
                        em = "否";
                    }
                    sid = res.data.id;
                } else {
                    $("#decimal").val("");
                    if ($("#round").attr("checked")) {
                        val1 = 0;
                        val2 = 0
                    }
                    em = "否";
                    sid = -1;
                }
                $("#copyXnxq").parents(".lab").addClass("hide");
                $(".creditRange").addClass("hide");
                $("#copyName").addClass("hide");
                form.val("secondaryEditForm", {
                    "round": val1,
                    "switchGradeFilter2": val2,
                });
                $("#round").parent("div").children("em").html(em)
                //学分规则
                getCreditRule(xnxq);
                updateGroup(xnxq);
            })
        }

        //切换学年学期
        $('#xnxqul1').on('click', "li", function () {
            getCreditSet($(this).text());
        })

        form.on('switch(switchGradeFilter1)', function (data) {
            if (data.elem.checked) {
                $(this).parent().find(".tit").text('是');
            } else {
                $(this).parent().find(".tit").text('否');
            }
        });

        form.on('switch(switchGradeFilter2)', function (data) {
            if (data.elem.checked) {
                $(this).parent().find(".tit").text('是');
                $(".multiplex").removeClass("hide");
                $(".creditRange .radio span").eq(0).addClass("cur").siblings().removeClass("cur");
            } else {
                $(this).parent().find(".tit").text('否');
                $(".multiplex").addClass("hide");
                $("#copyName").addClass("hide");
            }
        });


        //顶部保存按钮
        $(".main .top .btn").click(function () {
            var val = form.val("secondaryEditForm");
            if (sid != -1) {
                val["id"] = sid;
            }
            val["fid"] = fid;
            if (val.round) {
                val.round = 1;
            } else {
                val["round"] = 0;
            }
            if (val.decimal == undefined || val.decimal == "") {
                U.fail("请设置小数点保留位数")
                return;
            }
            var number = parseInt(val.decimal);
            if (isNaN(number) || number > 5 || number < 0) {
                U.fail("请设置正确的小数点位数")
                return;
            }
            $.get("/new/creditBasicSet/saveBasicSet", {cs: JSON.stringify(val)}, function (res) {
                if (res.code == 200) {
                    sid = res.data;
                    if (val.switchGradeFilter2) {
                        copy()
                    } else {
                        U.success("保存成功");
                    }
                } else {
                    U.fail("保存失败");
                }
            })
        });


        //复制时获取学分设置
        $('.multiplex .single-box .j-select-year').on('click', "ul li", function () {
            $.get("/new/creditRuleSet/getRuleByXnxq", {fid: fid, xnxq: $(this).html()}, function (res) {
                if (res.code == 200) {
                    var data = res.data;
                    var html = '';
                    for (let i = 0; i < data.length; i++) {
                        html += "<li data-id='" + data[i].id + "'>" + data[i].creditName + "</li>"
                    }
                    $("#creditNameList").html(html);
                } else {
                    U.fail("当前学期无可用学分")
                }
            })
        })

        //复制时确认
        function copy() {
            var source = $('#xnxq').val();
            var reuse = $('#copyXnxq').val();
            var type = $('.creditRange .radio span.cur').index();
            let copyIds = []
            $("#creditNameList li.active").each(function () {
                copyIds.push($(this).attr("data-id"))
            })
            if (source == undefined || source == '') {
                U.fail("请选择当前学年学期");
                return;
            }
            if (reuse == undefined || reuse == '') {
                U.fail("请选择复用学年学期");
                return;
            }
            if (type == 1) {
                if (copyIds.length == 0) {
                    U.fail("请选择要复制的学分");
                    return;
                }
            }
            var _table = table;
            $.ajax({
                type: 'get',
                dataType: 'json',
                url: '/new/creditRuleSet/copy',
                data: {source: source, reuse: reuse, type: type, copyIds: copyIds.join(",")},
                success: function (res) {
                    if (res.code == 200) {
                        _table.reload("scoreTable")
                        _table.reload("materialTable")
                        U.success("复制成功");
                    } else if (res.msg != undefined || res.msg != "") {
                        if (res.msg.indexOf("已设置") != -1) {
                            U.confirm({
                                title: "提示",
                                msg: "当前学年学期学分规则已设置,是否覆盖？",
                                sureBtnTxt: '确定',
                                cancelBtnTxt: '取消',
                                sure: function () {
                                    $.get("/new/creditRuleSet/forceCopy", {
                                        source: source,
                                        reuse: reuse,
                                        type: type,
                                        copyIds: copyIds.join(",")
                                    }, function (res) {
                                        if (res.code == 200) {
                                            U.success("复制成功");
                                            _table.reload("scoreTable")
                                            _table.reload("materialTable")
                                        } else {
                                            U.fail("复制失败");
                                        }
                                    });
                                },
                                cancel: function () {
                                    U.closePop();
                                }
                            });
                        } else {
                            U.fail(res.msg);
                        }
                    }
                }
            })
        }

        /*基础设置 end*/

        /*学分规则 start*/

        function getCreditRule(xnxq) {
            table.render({
                elem: "#materialTable",
                url: '/new/creditRuleSet/getRule', //数据接口
                where: {
                    fid: fid,
                    xnxq: xnxq,
                },
                parseData: function (res) {
                    var result = "";
                    if (res.code == 200) {
                        result = {
                            "code": 0, //解析接口状态
                            "msg": res.msg, //解析提示文本
                            "count": res.data.length, //解析数据长度
                            "data": res.data //解析数据列表
                        }
                    } else {
                        result = {
                            "code": 1, //解析接口状态
                            "msg": "无数据", //解析提示文本
                            "count": 0, //解析数据长度
                            "data": [] //解析数据列表
                        }
                    }
                    return result;
                },
                cols: [
                    [
                        //表头
                        {
                            type: "checkbox",
                            width: 86,
                        },
                        {
                            field: "creditCode",
                            title: "学分代码",
                            align: "center",
                        },
                        {
                            field: "creditName",
                            title: "学分名称",
                            align: "center",
                        },
                        {
                            field: "creditType",
                            title: "学分类型",
                            align: "center",
                        },
                        {
                            field: "groupName",
                            title: "所属学分编组",
                            align: "center",
                            templet: function (data) {
                                if (data.groupName == undefined || data.groupName == null || data.groupName == "") {
                                    return "/"
                                } else {
                                    return data.groupName;
                                }
                            }

                        },
                        {
                            field: "status",
                            title: "是否启用",
                            templet: "#status",
                            align: "center",
                        },
                        {
                            title: "操作",
                            align: "center",
                            toolbar: "#tmplToolBar2",
                            width: 254
                        },
                    ],
                ],
                done: function () {
                    $("table").css("width", "100%");
                }
            });
        }


        $(".setGroup").click(function () {
            type = 1;
            $(".addPopup").hide();
            $(".creditGroupSet").show();
        })

        table.on('tool(materialTable)', function (obj) {
            var data = obj.data;
            if (obj.event === 'delet') {
                U.confirm({
                    title: "提示",
                    msg: '该操作会删除学分规则，是否继续？',
                    sureBtnTxt: '确定',
                    cancelBtnTxt: '取消',
                    sure: function () {
                        $.get("/new/creditRuleSet/deleteById", {ids: data.id}, function (res) {
                            if (res.code == 200) {
                                U.success("删除成功")
                                obj.del();
                            } else {
                                U.fail("删除失败")
                            }
                        })
                    },
                    cancel: function () {
                        U.closePop();
                    }
                })
            } else if (obj.event === 'edit') {
                $(".addPopup .input .creditName").val(data.creditName);
                $(".addPopup .input .creditCode").val(data.creditCode);
                $(".addPopup .creditType").val(data.creditType);
                $(".addPopup .creditGroup").val(data.creditGroup);
                $(".addPopup .creditId").val(data.id);
                $(".addPopup .radio.status span").removeClass("cur");
                if (data.isRegisterCredit === 1) {
                    $("#isRegisterCredit .switc-con .switch").addClass("switch-open");
                    $("#isRegisterCredit .switc-con .switch-con").text("开启");
                } else {
                    $("#isRegisterCredit .switc-con .switch").removeClass("switch-open");
                    $("#isRegisterCredit .switc-con .switch-con").text("关闭");
                }
                $("#groupList li").each((index, value) => {
                    var groupId = $(value).attr("data-id");
                    if (groupId == data.groupId) {
                        $(".addPopup .creditGroup").val($(value).html());
                        $(value).addClass("active");
                        return;
                    }
                })

                $(".addPopup .popup-box .pu-title").text("编辑");
                $(".addPopup").show();
                if ("其他类学分" === data.creditType) {
                    $("#isRegisterCredit").css({
                        display: "flex",
                        display: "-webkit-flex"
                    });
                } else {
                    $("#isRegisterCredit").hide();
                }

            } else if (obj.event === 'set') {
                var xnxq = $("#xnxq").val();
                if (!obj.data.status) {
                    U.fail("当前学分已停用", 2000);
                    return false;
                }
                if (data.creditType === '课程学分(高中)') {
                    window.location.href = "/credit/manage/courseCreditType?csId=" + sid;
                } else if (data.creditType === '竞赛类学分') {
                    window.location.href = "/new/credit/competition/ruleSet?csId=" + sid + "&term=" + xnxq;
                } else if (data.creditType === '证书类学分') {
                    window.location.href = "/new/credit/certificate/ruleSet?csId=" + sid + "&term=" + xnxq;
                } else if (data.creditType === '实习类学分') {
                    window.location.href = "/new/credit/internship/ruleSet?csId=" + sid + "&term=" + xnxq;
                } else if (data.creditType === "德育类学分") {
                    window.location.href = "/new/credit/moral/education/ruleSet?csId=" + sid + "&term=" + xnxq;
                } else if (data.creditType === "其他类学分") {
                    window.location.href = "/new/credit/other/ruleSet?id=" + data.id + "&csId=" + sid + "&term=" + xnxq;
                } else {
                    window.location.href = "/new/credit/course/ruleSet?fid=" + fid + "&uid=" + uid + "&xnxq=" + xnxq + "&nh=0";
                }
            } else if (obj.event === 'deactivate') {
                var deact = $(this).text();
                U.confirm({
                    title: "提示",
                    msg: '确定' + deact + '吗？',
                    sureBtnTxt: '确定',
                    cancelBtnTxt: '取消',
                    sure: function () {
                        if ("停用" == deact) {
                            data.status = 0;
                        } else {
                            data.status = 1;
                        }
                        $.get("/new/creditRuleSet/saveOrUpdate", {cd: JSON.stringify(data)}, function (res) {
                            console.log(res)
                        })
                        obj.update(data, true)
                    },
                    cancel: function () {
                        U.closePop();
                    }
                });
            }
        });

        //添加 学分
        $(".main .con .c-item .c-top .btns-list span.add").click(function () {
            $(".addPopup .popup-box .pu-title").text("添加");
            $(".addPopup .input .creditName").val("");
            $(".addPopup .input .creditCode").val("");
            $(".addPopup .creditType").val("");
            $(".addPopup .creditGroup").val("");
            $(".addPopup .creditId").val("");
            $(".addPopup .radio.status span:eq(0)").addClass("cur").siblings().removeClass("cur");
            $("#isRegisterCredit .switc-con .switch").removeClass("switch-open");
            $("#isRegisterCredit .switc-con .switch-con").text("关闭");
            $(".addPopup").show();
            getCreditCode();
        })

        function getCreditCode() {
            var data = table.getData("materialTable");

            if (data.length == 0) {
                $("#creditCode").val("XFLX001");
                return
            }
            var max = 0;
            for (let i = 0; i < data.length; i++) {
                var creditCode = data[i].creditCode;
                if (creditCode == undefined || creditCode == "") {
                    continue;
                }
                var num = parseInt(creditCode.replace(/[^0-9]/ig, ""));
                if (isNaN(num)) {
                    continue;
                }
                max = Math.max(max, num);
            }
            max += 1;
            creditCode = "XFLX" + (Array(3).join(0) + max).slice(-3);
            $("#creditCode").val(creditCode);
        }

        //添加学分设置
        $(".popup.addPopup .popup-box .pu-btn button.pu-sure").click(function () {
            var name = $(".addPopup .input .creditName").val();
            var code = $(".addPopup .input .creditCode").val();
            var creditType = $(".addPopup .creditType").val();
            var groupName = $(".addPopup .creditGroup").val();
            var isRegisterCreditText = $(".addPopup #isRegisterCredit .switc-con .switch-con").text();
            var isRegisterCredit = isRegisterCreditText === "开启" ? 1 : 0;
            var groupId = "";
            if (groupName != undefined && groupName != "") {
                groupId = $("#groupList li.active").attr("data-id");
            }
            var id = $(".addPopup .creditId").val();
            if (name == '' || code == '' || creditType == '') {
                U.fail("学分名称、学分代码、学分类型不能为空");
                return;
            }
            if (groupId == undefined || groupId == '') {
                groupId = "";
            }
            var data = {
                id: id,
                fid: fid,
                xnxq: $("#xnxq").val(),
                basicId: sid,
                creditName: name,
                creditCode: code,
                creditType: creditType,
                isRegisterCredit: isRegisterCredit,
                groupId: groupId,
                status: 1,
            }
            var creditData = table.getData("materialTable");
            //判断学分类型是否重复
            var creditData2 = [];
            for (let i = 0; i < creditData.length; i++) {
                var info = creditData[i];
                if (info.id != data.id && data.creditCode === info.creditCode) {
                    U.fail("学分代码不可重复");
                    return;
                }
                if (info.id != data.id && info.creditName === data.creditName) {
                    U.fail("学分名称不能重复");
                    return;
                }
                if (info.id != data.id && info.creditType === data.creditType && data.creditType != "其他类学分") {
                    U.fail("学分类型不能重复");
                    return;
                }
                if (info.id == id) {
                    creditData2.push(data);
                } else {
                    creditData2.push(info);
                }
            }
            let _table = table;
            let _this = $(this);
            $.get("/new/creditRuleSet/saveOrUpdate", {cd: JSON.stringify(data)}, function (res) {
                if (res.code == 200) {
                    _table.reload("materialTable");
                    _this.parents(".popup").hide();
                } else {
                    U.fail(res.msg)
                }
            })


        })

        //学分设置顶部删除
        $(".main .con .c-item .c-top .btns-list span.delet").click(function () {
            var checked = table.checkStatus("materialTable");
            if (checked.data.length == 0) {
                U.fail("请选择要删除的学分");
                return;
            }
            U.confirm({
                title: "提示",
                msg: '确定删除吗？',
                sureBtnTxt: '确定',
                cancelBtnTxt: '取消',
                sure: function () {
                    let ids = []
                    checked.data.forEach((data, index) => {
                        ids.push(data.id)
                    });
                    $.get("/new/creditRuleSet/deleteById", {ids: ids.join(",")}, function (res) {
                        if (res.code == 200) {
                            U.success("删除成功")
                            setTimeout(function () {
                                table.reload("materialTable");
                            }, 1500)

                        } else {
                            U.fail("删除失败")
                        }
                    })
                },
                cancel: function () {
                    U.closePop();
                }
            });
        })

        /*学分设置 end*/

        /*编组设置 start*/


        function updateGroup(xnxq) {
            table.render({
                elem: "#scoreTable",
                url: "/new/creditSet/get/group",
                where: {xnxq: xnxq},
                parseData: function (res) {
                    var result = "";
                    if (res.code == 200) {
                        result = {
                            "code": 0, //解析接口状态
                            "msg": res.msg, //解析提示文本
                            "count": res.data.length, //解析数据长度
                            "data": res.data //解析数据列表
                        }
                    } else {
                        result = {
                            "code": 1, //解析接口状态
                            "msg": "无数据", //解析提示文本
                            "count": 0, //解析数据长度
                            "data": [] //解析数据列表
                        }
                    }
                    return result;
                },
                cols: [
                    [
                        //表头
                        {
                            type: "checkbox",
                            width: 86,
                        },
                        {
                            field: "groupCode",
                            title: "学分编组代码",
                            align: "center",
                            width: 177,
                        },
                        {
                            field: "groupName",
                            title: "学分编组名称",
                            align: "center",
                            width: 177,
                        },
                        {
                            field: "status",
                            title: "是否启用",
                            align: "center",
                            templet: "#status",
                            width: 177,
                        },
                        {
                            title: "操作",
                            width: 177,
                            align: "center",
                            toolbar: "#tmplToolBar3",
                        },
                    ],
                ],
                done: function (res) {
                    var data = res.data;
                    var html = "<li>---请选择---</li>";
                    for (let i = 0; i < data.length; i++) {
                        if (data[i].status == 1) {
                            html += '<li data-id="' + data[i].id + '">' + data[i].groupName + '</li>'
                        }
                    }
                    $("#groupList").html(html);
                }
            });
        }


        table.on('tool(scoreTable)', function (obj) {
            var data = obj.data;
            if (obj.event === 'delet') {
                U.confirm({
                    title: "提示",
                    msg: '确定删除吗？',
                    sureBtnTxt: '确定',
                    cancelBtnTxt: '取消',
                    sure: function () {
                        $.get("/new/creditSet/delete/group", {gid: data.id}, function (res) {
                            if (res.code == 200) {
                                obj.del()
                                U.success("删除成功")
                            } else {
                                U.fail("删除失败")
                            }
                        })

                    },
                    cancel: function () {
                        U.closePop();
                    }
                });
            } else if (obj.event === 'edit') {
                $(".addGroup .popup-box .pu-title").text("编辑");
                $(".addGroup .popup-box .pu-con .lab .input .lay1").val(data.groupName);
                $(".addGroup .popup-box .pu-con .lab .input .lay2").val(data.groupCode);
                $(".addGroup .groupid").val(data.id);
                $(".addGroup .radio span").removeClass("cur");
                if (data.status == '是') {
                    $(".addGroup .radio span:eq(0)").addClass("cur");
                } else {
                    $(".addGroup .radio span:eq(1)").addClass("cur");
                }
                $(".addGroup").show();
            } else if (obj.event === 'deactivate') {
                var deact = $(this).text();
                let _tbale = table;
                U.confirm({
                    title: "提示",
                    msg: '确定' + deact + '吗？',
                    sureBtnTxt: '确定',
                    cancelBtnTxt: '取消',
                    sure: function () {
                        if ("停用" == deact) {
                            data.status = 0;
                        } else {
                            data.status = 1;
                        }
                        $.get("/new/creditSet/save/group", {gd: JSON.stringify(data)}, function (res) {
                            _tbale.reload("scoreTable")
                        })
                    },
                    cancel: function () {
                        U.closePop();
                    }
                });
            }
        });

        $(".creditGroupSet .popup-box .pu-btn .pu-sure").click(function () {
            $(this).parents(".popup").hide()
        })


        //设置添加
        $(".creditGroupSet .popup-box .pu-con .p-top .btns-list span.add").click(function () {
            $(".addGroup .popup-box .pu-title").text("添加");
            $(".addGroup .popup-box .pu-con .lab .input .lay1").val("");
            $(".addGroup .popup-box .pu-con .lab .input .lay2").val("");
            $(".addGroup .radio span:eq(0)").addClass("cur").siblings().removeClass("cur");
            $(".addGroup .groupid").val("");
            $(".addGroup").show();

            getGroupCode();
        })

        //添加编组
        $(".popup.addGroup .popup-box .pu-btn button.pu-sure").click(function () {
            var name = $(".addGroup .input .lay1").val();
            var code = $(".addGroup .input .lay2").val();
            var id = $(".addGroup .groupid").val();
            if (name == '' || code == '') {
                U.fail("编组名称和编组代码不能为空");
                return;
            }
            var data = {
                id: id,
                fid: fid,
                xnxq: $("#xnxq").val(),
                groupName: name,
                groupCode: code,
                status: 1
            }
            var tableData = table.getData("scoreTable");
            for (let i = 0; i < tableData.length; i++) {
                var info = tableData[i]
                if (data.groupName === info.groupName && info.id != data.id) {
                    U.fail("学分编组名称不可重复，请重新设置");
                    return;
                }
            }
            var _this = $(this)
            var _table = table
            $.post("/new/creditSet/save/group", {gd: JSON.stringify(data)}, function (res) {
                if (res.code == 200) {
                    U.success("添加成功")
                    _table.reload("scoreTable")
                    _table.reload("materialTable")
                    _this.parents(".popup").hide();
                } else {
                    U.fail("添加失败")
                }
            })
        })

        function getGroupCode() {
            var groupData = table.getData("scoreTable");
            if (groupData.length == 0) {
                $("#groupCode").val("XFBZ001");
                return;
            }
            var max = 0;
            for (let i = 0; i < groupData.length; i++) {
                var creditCode = groupData[i].groupCode;
                if (creditCode == undefined || creditCode == "") {
                    continue;
                }
                var num = parseInt(creditCode.replace(/[^0-9]/ig, ""));
                if (isNaN(num)) {
                    continue;
                }
                max = Math.max(max, num);
            }
            max += 1;
            creditCode = "XFBZ" + (Array(3).join(0) + max).slice(-3);
            $("#groupCode").val(creditCode);
        }

        //编组顶部删除
        $(".creditGroupSet .popup-box .pu-con .p-top .btns-list span.delet").click(function () {
            var checked = table.checkStatus("scoreTable");
            if (checked.data.length == 0) {
                U.fail("请选择要删除的编组");
                return;
            }
            U.confirm({
                title: "提示",
                msg: '确定删除吗？',
                sureBtnTxt: '确定',
                cancelBtnTxt: '取消',
                sure: function () {
                    let ids = []
                    checked.data.forEach((data, index) => {
                        ids.push(data.id)
                    });
                    var _table = table;
                    $.get("/new/creditSet/delete/group", {gid: ids.join(",")}, function (res) {
                        if (res.code == 200) {
                            U.success("删除成功")
                            _table.reload("scoreTable")
                        } else {
                            U.fail("删除失败")
                        }
                    })
                },
                cancel: function () {
                    U.closePop();
                }
            });
        })
        /*学分编组 end*/


        $(".main .con .c-item .lab .radio span").click(function () {
            $(this).addClass("cur").siblings().removeClass("cur");

            if ($(this).parents(".lab").hasClass("creditRange")) {
                let ind = $(this).index();
                if (ind == 0) {
                    $("#copyName").addClass("hide");
                } else {
                    $("#copyName").removeClass("hide");
                }
            }
        })

        //弹窗取消
        $(".popup .popup-box .pu-btn .pu-cancel").click(function () {
            $(this).parents(".popup").hide();
        })

        $(".creditGroupSet .popup-box .pu-btn .pu-cancel").click(function () {
            if (type == 1) {
                $(".addPopup").show();
                $(".creditGroupSet").hide();
            } else {
                updateGroup($("#xnxq").val());
            }
        })


        //从什么位置打开的学分编组 1 添加页面 0 顶部按钮
        var type = 0;
        //学分编组设置
        $(".main .con .c-item .c-top .btns-list span.set").click(function () {
            type = 0;
            $(".creditGroupSet").show();
        })

        // 点击【是否直接等级学分】开关
        $("#isRegisterCredit .switc-con .switch").click(function () {
            if ($(this).hasClass("switch-open")) {
                $(this).removeClass("switch-open");
                $(this).next().text('关闭');
            } else {
                $(this).addClass("switch-open");
                $(this).next().text('开启');
            }
        });
    });

    $('.score-type .j-select-year').on('click', "ul li", function () {
        var text = $(this).find("span").text();
        if (text === "其他类学分") {
            $("#isRegisterCredit").css({
                display: "flex",
                display: "-webkit-flex"
            });
        } else {
            $("#isRegisterCredit").hide();
        }
    });


</script>
</html>