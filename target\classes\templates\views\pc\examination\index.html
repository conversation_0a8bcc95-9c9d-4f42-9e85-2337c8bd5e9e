<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>考试规则设置</title>
    <link rel="stylesheet" th:href="@{~/css/examination/global2.css}">
    <link rel="stylesheet" th:href="@{~/plugin/layui/css/layui.css}">
    <link rel="stylesheet" th:href="@{~/css/examination/reset.css}">
    <link rel="stylesheet" th:href="@{~/css/examination/perfect-scrollbar.css}">
    <link rel="stylesheet" th:href="@{~/css/examination/index.css?v=20240326}">
    <style>
        .z-main .z-title h2 {
            padding: 0 20px;
        }
        .z-main .box-teacher .layui-form .layui-input-block{
            margin-left:220px;
        }
        /*.z-main .box-classroom .layui-form .layui-select-title .layui-input{*/
        /*    width: 100px;*/
        /*}*/

        #addTeacherTeach{
        background: #4D88FF;
        box-shadow: 0px 0px 10px rgba(77, 136, 255, 0.3);
        border-radius: 4px;
        width: auto;
        padding:0 10px;
        height: 36px;
        font-size: 14px;
        color: #FFFFFF;
        line-height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        }
        #addClassRoomUse{
            background: #4D88FF;
            box-shadow: 0px 0px 10px rgba(77, 136, 255, 0.3);
            border-radius: 4px;
            width: auto;
            padding:0 10px;
            height: 36px;
            font-size: 14px;
            color: #FFFFFF;
            line-height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        /*遮罩层*/
        .loading{
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            background: #fff;
            opacity: 0.3;
            z-index: 999;
        }
        /*图标*/
        .loadIcon{
            font-size: 35px;
            color: #000;
            position: relative;
            left: 45%;
            top: 45%;
        }

         .j-search-con .j-select-year .search input {
             width: 190px;
         }

        .j-search-con .j-select-year .search1 input{
            width: 190px;
        }

        .j-search-con input {
            padding: 0 32px 0 10px;
        }

        .j-search-con .j-select-year p {
            color: #86909C;
            font-size: 12px;
            text-align: center;
            line-height: 30px;
        }

        .j-search-con .j-select-year ul li {
            background-position: 93% center;
        }

        .ps__rail-y {
            opacity: 1;
        }

        .j-search-con.disabled input{
            background: #f7f8fa;
            color: #c6c7cd !important;
            cursor: default;
        }
        /*.box-classroom .layui-form .layui-input-block .layui-input {*/
        /*    width: 100px !important;*/
        /*}*/
    </style>
</head>

<body>
<div class="z-main">
    <div class="z-title">
        <h3>考试规则设置</h3>
        <h2>当前学期:<span id="curXnxq" th:text="${curxnxq}"></span></h2>
        <h2>当前考试批次：<span id="curBatch" th:text="${curbatchName}"></span></h2>
    </div>
    <div class="z-tab">
        <ul>
            <!-- 2024.11.12 -->
            <li style="display: none" id="rule_97591">学生排考安排</li>
            <!-- 2024.11.12 -->
            <li class="active">教室参数</li>
            <li>监考教师参数</li>
            <li>禁排教师参数</li>
            <li>禁排教室参数</li>
            <li>通知参数</li>
            <li>考试时长参数</li>
        </ul>
    </div>
    <div class="z-box">
        <!--学生排考安排 -->
        <div class="box-con" style="display: none;">
            <iframe th:src="'./studentRule1.html?formUserId='+${formUserId}" id="iframe"
                    style="display: block; width:100%; min-height:calc( 100vh - 160px);" frameborder="0"
                    scrolling="auto"></iframe>
        </div>
        <!-- 教室参数 -->
        <div class="box-con box-classroom" style="display: none;">
            <form class="layui-form" lay-filter="classRoomForm"
                  id="classroomForm"
                  action="/examination/rule/classroom/save"
                  method="post"
                  onsubmit="return saveReport();">
                <input type="hidden" name="id" id="classroomRuleId">
                <div class="layui-form-item" id="rule_97591_classRoom">
                    <label class="layui-form-label">优先行政班在本班教室排考<span class="layui-tips"
                                                                      data-tip="自动排考是否根据考试班级中的人数最多的所属行政班分配考"></span></label>
                    <div class="layui-input-block item-radio">
                        <div class="radio-list">
                            <input type="radio" lay-filter="name1" name="thisClass" value="1" title="是" checked>
                            <input type="radio" lay-filter="name1" name="thisClass" value="0" title="否">
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">是否跨校区排考<span class="layui-tips"
                                                                 data-tip="自动排考是否分配考试教学班中行政班固定教室所在校区之外的教室"></span></label>
                    <div class="layui-input-block item-radio">
                        <div class="radio-list">
                            <input type="radio" lay-filter="campus" name="campus" value="1" title="是">
                            <input type="radio" lay-filter="campus" name="campus" value="0" title="否" checked>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">是否在上课教室考试<span class="layui-tips"
                                                                 data-tip="是否在上课教室考试"></span></label>
                    <div class="layui-input-block item-radio">
                        <div class="radio-list">
                            <input type="radio" lay-filter="classroom" name="classroom" value="1" title="是">
                            <input type="radio" lay-filter="classroom" name="classroom" value="0" title="否" checked>
                        </div>
                    </div>
                </div>
<!--                <div class="layui-form-item">-->
<!--                    <label class="layui-form-label">按考场容量上限拆分考试数据<span class="layui-tips"-->
<!--                                                                       data-tip="适用于考试任务人数较多需要安排多个考场进行考试的情况，如：一条任务为100人的考试，考场容量参数设置为50，则拆分为2条50人的考试数据进行排考"></span></label>-->
<!--                    <div class="layui-input-block item-radio">-->
<!--                        <div class="radio-list">-->
<!--                            <input type="radio" lay-filter="exam-limit" name="split" value="1" title="是">-->
<!--                            <input type="radio" lay-filter="exam-limit" name="split" value="0" title="否" checked>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->
<!--                <div class="layui-form-item" style="display: none">-->
<!--                    <label class="layui-form-label">考场容量设置<span class="layui-tips" data-tip="仅可输入数字"></span></label>-->
<!--                    <div class="layui-input-block">-->
<!--                        <input type="text" name="capacity" lay-verify="number" placeholder="请输入数字" autocomplete="off"-->
<!--                               onblur="value=zhzs(this.value)" class=" layui-input"/>-->
<!--                    </div>-->
<!--                </div>-->
<!--                <div class="layui-form-item" style="display: none">-->
<!--                    <label class="layui-form-label">剩余学生处理规则<span class="layui-tips"-->
<!--                                                                 data-tip="剩余学生处理规则"></span></label>-->
<!--                    <div class="layui-input-block item-radio">-->
<!--                        <div class="radio-list">-->
<!--                            <input type="radio" lay-filter="remainStudentHandleRule" name="remainStudentHandleRule" value="0" title="手动安排" checked>-->
<!--                            <input type="radio" lay-filter="remainStudentHandleRule" name="remainStudentHandleRule" value="1" title="生成新的考场">-->
<!--                            <input type="radio" lay-filter="remainStudentHandleRule" name="remainStudentHandleRule" value="2" title="均分到所有考场">-->
<!--                            <input type="radio" lay-filter="remainStudentHandleRule" name="remainStudentHandleRule" value="3" title="平均安排到同年级同科目考场">-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->
<!--                <div class="layui-form-item">-->
<!--                    <label class="layui-form-label">座位号规则<span class="layui-tips"-->
<!--                                                                 data-tip="考生学号：根据考试班内考生学号排列（从小到大）-->
<!--                                                                 </br>-->
<!--                                                                 行政班+考生考号：考试班内，优先行政班的学生相邻，再按照行政班内考生考号排列（从小到大）"></span></label>-->
<!--                    <div class="layui-input-block item-radio">-->
<!--                        <div class="radio-list">-->
<!--                            <input type="radio" lay-filter="zwh" name="zwh" value="1" title="考生学号" checked>-->
<!--                            <input type="radio" lay-filter="zwh" name="zwh" value="0" title="行政班+考生考号">-->
<!--                            <input type="radio" lay-filter="zwh" name="zwh" value="2" title="成绩排名">-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->
<!--                <div class="layui-form-item">-->
<!--                    <div class="layui-form-label"> 考场门位置</div>-->
<!--                    <div class="layui-input-block" style="width:100px;">-->
<!--                        <select id="address1" name="address1" lay-filter="address1" >-->
<!--                            <option value="">请选择</option>-->
<!--                            <option value="1">左</option>-->
<!--                            <option value="2">右</option>-->
<!--                        </select>-->
<!--                    </div>-->
<!--                </div>-->
<!--                <div id="seatStart1" class="layui-form-item" style="display: none">-->
<!--                    <label class="layui-form-label">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;座位起始位置</label>-->
<!--                    <div class="layui-input-block item-radio">-->
<!--                        <div class="radio-list">-->
<!--                            <input type="radio" lay-filter="leftOrRight1" name="leftOrRight1" value="1" title="左边第一座" checked>-->
<!--                            <input type="radio" lay-filter="leftOrRight1" name="leftOrRight1" value="0" title="右边第一座" >-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->
<!--                <div id="zwplfs1" class="layui-form-item" style="display: none">-->
<!--                    <div class="layui-form-label">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;座位排列方式</div>-->
<!--                    <div class="layui-input-block" style="width:100px;">-->
<!--                        <select id="arrangement1" name="arrangement1" lay-filter="arrangement1" >-->
<!--                            <option value="">请选择</option>-->
<!--                            <option value="1">纵向S型</option>-->
<!--                            <option value="2">横向S型</option>-->
<!--                            <option value="3">纵向线型</option>-->
<!--                            <option value="4">横向线型</option>-->
<!--                        </select>-->
<!--                    </div>-->
<!--                </div>-->

<!--                <div id="kcmwz2" class="layui-form-item" style="display: none">-->
<!--                    <div class="layui-form-label"> 考场门位置</div>-->
<!--                    <div class="layui-input-block" style="width:100px;">-->
<!--                        <select id="address2" name="address2" lay-filter="address2"  disabled>-->
<!--                            <option value="">请选择</option>-->
<!--                            <option value="1">左</option>-->
<!--                            <option value="2">右</option>-->
<!--                        </select>-->
<!--                    </div>-->
<!--                </div>-->
<!--                <div id="seatStart2" class="layui-form-item" style="display: none">-->
<!--                    <label class="layui-form-label">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;座位起始位置</label>-->
<!--                    <div class="layui-input-block item-radio">-->
<!--                        <div class="radio-list">-->
<!--                            <input type="radio" lay-filter="leftOrRight2" name="leftOrRight2" value="1" title="左边第一座" checked>-->
<!--                            <input type="radio" lay-filter="leftOrRight2" name="leftOrRight2" value="0" title="右边第一座" >-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->
<!--                <div id="zwplfs2" class="layui-form-item" style="display: none">-->
<!--                    <div class="layui-form-label">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;座位排列方式</div>-->
<!--                    <div class="layui-input-block" style="width:100px;">-->
<!--                        <select id="arrangement2" name="arrangement2" lay-filter="arrangement2" >-->
<!--                            <option value="">请选择</option>-->
<!--                            <option value="1">纵向S型</option>-->
<!--                            <option value="2">横向S型</option>-->
<!--                            <option value="3">纵向线型</option>-->
<!--                            <option value="4">横向线型</option>-->
<!--                        </select>-->
<!--                    </div>-->
<!--                </div>-->


            </form>
        </div>
        <!-- 监考教师设置 -->
        <div class="box-con box-teacher" style="display: none;">
            <form class="layui-form"
                  id="invigilatorRule"
                  action="/examination/rule/invigilator/save"
                  method="post"
                  onsubmit="return saveInvigilator();">
                <input type="hidden" name="id" id="invigilatorId">

                <div class="layui-form-item">
                    <label class="layui-form-label">任课教师不能监考任课科目<span class="layui-tips"
                                                                      data-tip="任课教师不能监考任课科目"></span></label>
                    <div class="layui-input-block item-radio">
                        <div class="radio-list">
                            <input id="noInvigilationTeachCourse" type="radio" lay-filter="noInvigilationTeachCourse" name="noInvigilationTeachCourse" value="1" title="是">
                            <input type="radio" lay-filter="noInvigilationTeachCourse" name="noInvigilationTeachCourse" value="0" title="否" checked>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">任课教师不得监考任课班级<span class="layui-tips"
                                                                    data-tip="任课教师不能监考自己所任教班级的所有考试"></span></label>
                    <div class="layui-input-block item-radio">
                        <div class="radio-list">
                            <input id="noInvigilationTeachClass" type="radio" lay-filter="noInvigilationTeachClass" name="noInvigilationTeachClass" value="1" title="是">
                            <input type="radio" lay-filter="noInvigilationTeachClass" name="noInvigilationTeachClass" value="0" title="否" checked>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">班主任不得监考本班级考试<span class="layui-tips"
                                                                      data-tip="班主任不得监考本班级考试"></span></label>
                    <div class="layui-input-block item-radio">
                        <div class="radio-list">
                            <input id="classTeacherNoInvigilationClass" type="radio" lay-filter="classTeacherNoInvigilationClass" name="classTeacherNoInvigilationClass" value="1" title="是">
                            <input type="radio" lay-filter="classTeacherNoInvigilationClass" name="classTeacherNoInvigilationClass" value="0" title="否" checked>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">同一考场多个监考教师<span class="layui-tips"
                                                                    data-tip="自动排考是否给同一场考试安排多个监考教师"></span></label>
                    <div class="layui-input-block item-radio">
                        <div class="radio-list">
                            <input type="radio" lay-filter="status" name="status" value="1" title="是">
                            <input type="radio" lay-filter="status" name="status" value="0" title="否" checked>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item" style="display: none">
                    <label class="layui-form-label">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;多个监考教师规则</label>
                    <div class="layui-input-block item-radio">
                        <div class="radio-list">
                            <input type="radio" lay-filter="multipleInvigilatorRule" name="multipleInvigilatorRule" value="1" title="同一个考场多个监考教师">
                            <input type="radio" lay-filter="multipleInvigilatorRule" name="multipleInvigilatorRule" value="0" title="按学生数量设置监考教师" checked>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item" style="display: none">
                    <label class="layui-form-label">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;同一考场内监考教师数量</label>
                    <div class="layui-input-block">
                        <input type="text" name="teacherNum" lay-verify="teacherNum" placeholder="请输入数字"
                               autocomplete="off"
                               onblur="value=zhzs(this.value)" class=" layui-input"/>
                    </div>
                </div>
                <div class="layui-form-item" style="display: none">
                    <label class="layui-form-label">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;同一教师监考最大学生数</label>
                    <div class="layui-input-block">
                        <input type="text" name="maximum" lay-verify="maximum" placeholder="请输入数字"
                               autocomplete="off"
                               onblur="value=zhzs(this.value)" class=" layui-input"/>
                    </div>
                </div>

                <div class="layui-form-item" style="display: none">
                    <label class="layui-form-label">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;是否按男女比1：1安排监考</label>
                    <div class="layui-input-block item-radio">
                        <div class="radio-list">
                            <input type="radio" lay-filter="sex" name="sex" value="1" title="是">
                            <input type="radio" lay-filter="sex" name="sex" value="0" title="否" checked>
                        </div>
                    </div>
                </div>
<!--                <div class="layui-form-item" style="display: none">-->
<!--                    <label class="layui-form-label">监考教师数量<span class="layui-tips" data-tip="仅可输入数字"></span></label>-->
<!--                    <div class="layui-input-block">-->
<!--                        <input type="text" name="teacherNum" lay-verify="teacherNum" placeholder="请输入数字"-->
<!--                               autocomplete="off"-->
<!--                               onblur="value=zhzs(this.value)" class=" layui-input"/>-->
<!--                    </div>-->
<!--                </div>-->
<!--                <div class="layui-form-item" >-->
<!--                    <label class="layui-form-label">单个教师可监考最大人数<span class="layui-tips" data-tip="仅可输入数字"></span></label>-->
<!--                    <div class="layui-input-block">-->
<!--                        <input type="text" name="maximum" lay-verify="maximum" placeholder="请输入数字"-->
<!--                               autocomplete="off"-->
<!--                               onblur="value=zhzs(this.value)" class=" layui-input"/>-->
<!--                    </div>-->
<!--                </div>-->
                <div class="layui-form-item">
                    <label class="layui-form-label">优先安排上课系部的教师监考<span class="layui-tips"
                                                                    data-tip="优先安排上课系部的教师监考"></span></label>
                    <div class="layui-input-block item-radio">
                        <div class="radio-list">
                            <input type="radio" lay-filter="sameDept" name="sameDept" value="1" title="是">
                            <input type="radio" lay-filter="sameDept" name="sameDept" value="0" title="否" checked>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">教师监考场次尽量相邻<span class="layui-tips"
                                                                    data-tip="自动排考时尽量将监考教师安排到相邻的场次"></span></label>
                    <div class="layui-input-block item-radio">
                        <div class="radio-list">
                            <input type="radio" lay-filter="near" name="near" value="1" title="是">
                            <input type="radio" lay-filter="near" name="near" value="0" title="否" checked>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">是否跨校区排考<span class="layui-tips"
                                                                 data-tip="自动排考是否分配其他校区监考教师"></span></label>
                    <div class="layui-input-block item-radio">
                        <div class="radio-list">
                            <input type="radio" lay-filter="campus2" name="campus2" value="1" title="是">
                            <input type="radio" lay-filter="campus2" name="campus2" value="0" title="否" checked>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">优先授课教师监考本教学班考试<span class="layui-tips"
                                                                 data-tip="优先授课教师监考本教学班考试"></span></label>
                    <div class="layui-input-block item-radio">
                        <div class="radio-list">
                            <input type="radio" lay-filter="thisCourseTeacher" name="thisCourseTeacher" value="1" title="是">
                            <input type="radio" lay-filter="thisCourseTeacher" name="thisCourseTeacher" value="0" title="否" checked>
                        </div>
                    </div>
                </div>

                <div class="layui-form-item" style="margin-bottom:20px;">
                    <label class="layui-form-label">最大监考场次上限<span class="layui-tips"
                                                                  data-tip="最大监考场次上限"></span></label>
                    <div class="layui-input-block">
                        <div class="addRule" id="addRule">添加规则</div>
                    </div>
                </div>

                <div class="z-table">
                    <table class="layui-hide materialTable2" id="materialTable2" lay-filter="materialTable2">
                    </table>
                </div>
            </form>
        </div>
        <!-- 禁排教师参数 -->
        <div class="box-con box-teacher" style="display: none; position: relative;">
            <div id="teacherTeachLoading" class="loading" style="display: none;">
                <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop loadIcon"></i>
            </div>
            <form class="layui-form">

                <div class="layui-form-item" style="margin-bottom:20px;">
                    <label class="layui-form-label">添加禁排教师规则<span class="layui-tips"
                                                                  data-tip="设置本次排考任务中不可安排监考任务的教师，安排监考教师时将不再显示处于禁排时段的教师"></span></label>
                    <div class="layui-input-block" style="display: flex; align-items: center;justify-content: flex-end;">

                        <div class="addRule" id="teacheraddRule" style="width: 114px; background: #fff; margin-right:24px; border-radius: 4px; color:#4D88FF; border:1px solid #4D88FF; display: inline-block; height: 34px;line-height: 34px;cursor: pointer; text-align: center;">添加规则
                        </div>
                        <div id="addTeacherTeach" class="btn" >同步教师上课时间</div>
                    </div>
                </div>
                <div class="z-table">
                    <table class="layui-hide materialTable3" id="materialTable3" lay-filter="materialTable3">
                    </table>
                </div>
            </form>

        </div>
        <!-- 禁排教室参数 -->
        <div class="box-con box-teacher" style="display: none;position: relative;">
            <div id="classRoomUseLoading" class="loading" style="display: none;">
                <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop loadIcon"></i>
            </div>
            <form class="layui-form">
                <div class="layui-form-item" style="margin-bottom:20px;">
                    <label class="layui-form-label">添加禁排教室规则<span class="layui-tips"
                                                                  data-tip="设置本次排考任务中不可作为考场的教室，安排考场时将不再显示处于禁排时段的教室"></span></label>
                    <div class="layui-input-block" style="display: flex; align-items: center;justify-content: flex-end;">
                        <div class="addRule" id="crAddRule" style="width: 114px; background: #fff; margin-right:24px; border-radius: 4px; color:#4D88FF; border:1px solid #4D88FF; display: inline-block; height: 34px;line-height: 34px;cursor: pointer; text-align: center;">添加规则
                        </div>
                        <div id="addClassRoomUse" class="btn">教室上课占用录入</div>
                    </div>
                </div>
                <div class="z-table">
                    <table class="layui-hide materialTable4" id="materialTable4" lay-filter="materialTable4">
                    </table>
                </div>
            </form>
        </div>


        <!-- 添加发送内容  2024.3.18 -->
        <div class="box-con not-parameters" style="display: none;">
            <form class="layui-form"
                  id="noticeRule"
                  action="/examination/notice/saveOrUpdate"
                  method="post"
                  onsubmit="return saveNotice();">
                <input type="hidden" name="id" id="noticeId">
                <div class="layui-form-item">
                    <label class="layui-form-label" style="width:185px;">发布考试任务后通知监考教师</label>
                    <div class="layui-input-block item-radio">
                        <div class="radio-list">
                            <input type="radio" lay-filter="notice" name="isNoticeTeacher" value="1" title="是">
                            <input type="radio" lay-filter="notice" name="isNoticeTeacher" value="0" title="否" checked>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item note-content" style="display:none;">
                    <label class="layui-form-label" style="width:185px; float:none;margin-botttom:px;">通知内容</label>
                    <div class="textarea">
                        <textarea name="content" id="notice-content"
                                  placeholder="各位监考教师，考表已经发布，请前往“查看考表”应用查看自己的监考安排！"></textarea>
                    </div>
                    <div class="handle">
                        <div class="texts">通知内容可编辑,点击确定后保存编辑内容</div>
                        <div class="button">确定</div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="width:250px;">发布考试任务后发送考表给指定角色<span class="layui-tips"
                                                                                               data-tip="发布考试任务后将考试安排表以Excle表的格式发送给指定角色的教师"></span></label>
                    <div class="layui-input-block item-radio" style="margin-left:250px;">
                        <div class="radio-list">
                            <input type="radio" lay-filter="pubRole" name="isNoticeRole" value="1" title="是">
                            <input type="radio" lay-filter="pubRole" name="isNoticeRole" value="0" title="否" checked>
                        </div>
                    </div>
                </div>

                <div class="z-table" style="overflow: visible; display:none;">
                    <div class="top">
                        <div class="title">角色和考表内容关系</div>
                        <div class="button" id="addSendContent"><img th:src="@{~/images/examination/add-icon.png}">添加发送内容
                        </div>
                    </div>
                    <table class="layui-hide materialTable5" id="materialTable5" lay-filter="materialTable5">
                    </table>
                </div>


            </form>
        </div>

        <div class="box-con" style="display: block;">
            <div class="z-sel">
                <div class="set-limit" id="setExamDuration">设置科目考试时长</div>
            </div>
            <div class="z-table">
                <table class="layui-hide materialTable6" id="materialTable6" lay-filter="materialTable6" >
                </table>
            </div>
        </div>
    </div>
</div>
</body>
<!-- 最大监考场次上限 -->
<div class="dialog" id="invigilateMax">
    <div class="dialog-title">最大监考场次上限</div>
    <div class="dialog-con">
        <form class="layui-form" lay-filter="invigilateLimitForm"
              id="invigilateLimitForm"
              action="/examination/rule/limit/save"
              method="post"
              onsubmit="return saveLimit();">
            <input type="hidden" name="id">
            <div class="layui-form-item">
                <label class="layui-form-label">规则名称</label>
                <div class="layui-input-block">
                    <input type="text" name="ruleName" lay-verify="number" placeholder="请输入" autocomplete="off"
                           class=" layui-input"/>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">选择院系</label>
                <div class="layui-input-block">
                    <div class="j-search-con">
                        <input type="text" name="college" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year ">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <ul id="college" name="college">

                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">角色范围</label>
                <div class="layui-input-block">
                    <div class="j-search-con">
                        <input type="text" name="roleRange" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year ">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <ul id="roleRange" name="roleRange">

                            </ul>
                        </div>
                    </div>
                </div>
            </div>



            <div class="layui-form-item">
                <label class="layui-form-label">教师姓名</label>
                <div class="layui-input-block">
                    <div class="j-search-con">
                        <input type="text" name="teacher" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year ">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <div id="all-select" class="all-selects">全选</div>
                            <ul name="teacher">

                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">教师工号</label>
                <div class="layui-input-block">
                    <div class="j-search-con">
                        <input type="text" name="teacherNo" readonly style="background: #EFEFEF">
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <div class="all-selects">全选</div>
                            <ul name="teacherNo">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">最大监考场次上限</label>
                <div class="layui-input-block">
                    <input type="number" name="examLimit" lay-verify="number" placeholder="请输入数字" autocomplete="off"
                           onblur="value=zhzs(this.value)" class=" layui-input"/>
                </div>
            </div>
        </form>
    </div>
    <div class="dialog-btn">
        <button class="pu-cancel">取消</button>
        <button class="pu-sure" id="invigilateSure">确定</button>
    </div>
</div>
<!-- 添加禁排教师规则 -->
<div class="dialog forbitRule" id="teacherForbitRule">
    <div class="dialog-title">添加禁排教师规则</div>
    <div class="dialog-con">
        <form class="layui-form" lay-filter="forbitForm"
              id="banTeacherForm"
              action="/examination/rule/teacher/save"
              method="post"
              onsubmit="return saveTeacher();">
            <input type="hidden" name="id">
            <div class="layui-form-item">
                <label class="layui-form-label">规则名称</label>
                <div class="layui-input-block">
                    <input type="text" name="ruleName" lay-verify="number" placeholder="请输入" autocomplete="off"
                           class=" layui-input"/>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">选择院系</label>
                <div class="layui-input-block">
                    <div class="j-search-con ">
                        <input type="text" name="college2" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <ul id="college2" name="college2">

                            </ul>
                        </div>
                    </div>
                </div>
            </div>


            <div class="layui-form-item">
                <label class="layui-form-label">角色范围</label>
                <div class="layui-input-block">
                    <div class="j-search-con">
                        <input type="text" name="roleRange2" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year ">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <ul id="roleRange2" name="roleRange2">

                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label ">选择教师</label>
                <div class="layui-input-block">
                    <div class="j-search-con ">
                        <input type="text" name="teacher" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <div class="all-selects">全选</div>
                            <ul name="teacher">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label label-require">选择教师工号</label>
                <div class="layui-input-block">
                    <div class="j-search-con ">
                        <input type="text" name="teacherNo" readonly class="schoolSel" style="background: #ebebeb">
                    </div>
                </div>
            </div>
            <div class="layui-form-item" style="margin-bottom:14px;">
                <label class="layui-form-label label-require">禁排时间</label>
            </div>
            <div class="table-con banteacher">
                <ul class="con">
                </ul>
                <ul class="con">
                    <li class="time">05月18日</li>
                    <li class="active">08:00 - 08:45
                    </li>
                    <li>08:00 - 08:45</li>
                    <li class="disabled">08:00 - 08:45</li>
                    <li>08:00 - 08:45</li>
                </ul>
                <ul class="con">
                    <li class="time">05月18日</li>
                    <li>08:00 - 08:45</li>
                    <li class="active">08:00 - 08:45</li>
                    <li>08:00 - 08:45</li>
                    <li>08:00 - 08:45</li>
                </ul>
                <ul class="con">
                    <li class="time">05月18日</li>
                    <li>08:00 - 08:45</li>
                    <li>08:00 - 08:45</li>
                    <li>08:00 - 08:45</li>
                    <li>08:00 - 08:45</li>
                </ul>
            </div>
        </form>
    </div>
    <div class="dialog-btn">
        <button class="pu-cancel">取消</button>
        <button class="pu-sure" id="forbitSure">确定</button>
    </div>
</div>
<!-- 添加禁排教室规则 -->
<div class="dialog forbitRule" id="crForbitRule">
    <div class="dialog-title">添加禁排教室规则</div>
    <div class="dialog-con">
        <form class="layui-form" lay-filter="forbitForm">
            <input type="hidden" name="id">
            <div class="layui-form-item">
                <label class="layui-form-label">规则名称</label>
                <div class="layui-input-block">
                    <input type="text" name="ruleName" lay-verify="number" placeholder="请输入" autocomplete="off"
                           class=" layui-input"/>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">选择教学楼</label>
                <div class="layui-input-block">
                    <div class="j-search-con">
                        <input type="text" name="build" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <div class="all-selects">全选</div>
                            <ul name="build">
                                <li data-id="0">教学楼11</li>
                                <li data-id="1">教学楼22</li>
                                <li data-id="2">教学楼33</li>
                                <li data-id="3">教学楼4</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label ">选择教室</label>
                <div class="layui-input-block">
                    <div class="j-search-con">
                        <input type="text" placeholder="请选择" name="classroom" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <div class="all-selects">全选</div>
                            <ul name="classroom">
                                <li data-id="0">教室11</li>
                                <li data-id="1">教室22</li>
                                <li data-id="2">教室33</li>
                                <li data-id="3">教室4</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-form-item" style="margin-bottom:14px;">
                <label class="layui-form-label label-require">禁排时间</label>
            </div>
            <div class="table-con banclassroom">
                <ul class="con">
                    <li class="time">05月18日</li>
                    <li class="disabled">08:00 - 08:45</li>
                    <li>08:00 - 08:45</li>
                    <li class="disabled">08:00 - 08:45</li>
                    <li>08:00 - 08:45</li>
                </ul>
                <ul class="con">
                    <li class="time">05月18日</li>
                    <li class="active">08:00 - 08:45
                    </li>
                    <li>08:00 - 08:45</li>
                    <li class="disabled">08:00 - 08:45</li>
                    <li>08:00 - 08:45</li>
                </ul>
                <ul class="con">
                    <li class="time">05月18日</li>
                    <li>08:00 - 08:45</li>
                    <li class="active">08:00 - 08:45</li>
                    <li>08:00 - 08:45</li>
                    <li>08:00 - 08:45</li>
                </ul>
                <ul class="con">
                    <li class="time">05月18日</li>
                    <li>08:00 - 08:45</li>
                    <li>08:00 - 08:45</li>
                    <li>08:00 - 08:45</li>
                    <li>08:00 - 08:45</li>
                </ul>
            </div>
        </form>
    </div>
    <div class="dialog-btn">
        <button class="pu-cancel">取消</button>
        <button class="pu-sure" id="crSure">确定</button>
    </div>
</div>
<!-- 添加发送内容  2024.3.18 -->
<div class="dialog addSendingContent" id="addSendingContent">
    <div class="dialog-title">添加发送内容</div>
    <div class="dialog-con">
        <form class="layui-form" lay-filter="addSendForm">
            <div class="layui-form-item">
                <label class="layui-form-label">接收考表的角色</label>
                <div class="layui-input-block">
                    <div class="j-search-con">
                        <input type="text" name="role" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <div class="all-selects">全选</div>
                            <ul name="role">
                                <li data-id="1">校领导</li>
                                <li data-id="2">系部领导</li>
                                <li data-id="0">监考教师</li>
                                <li data-id="0">教师</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item" style="margin-bottom:0;">
                <label class="layui-form-label">考表内容</label>
                <div class="layui-input-block">
                    <div class="check-list">
                        <ul name="field">
                            <li>班级</li>
                            <li>系部</li>
                            <li>课程</li>
                            <li>任课教师</li>
                            <li>考场</li>
                            <li>考试时间</li>
                            <li>监考教师</li>
                        </ul>
                    </div>
                </div>
            </div>

        </form>
    </div>
    <div class="dialog-btn">
        <button class="pu-cancel">取消</button>
        <button class="pu-sure" id="addSendSure">确定</button>
    </div>
</div>
<!-- 设置科目考试时长  2025.5.20 -->
<div class="dialog subjectExams" id="setdurationSubjectExams">
    <div class="dialog-title">设置考试时长</div>
    <div class="dialog-con">
        <form class="layui-form" lay-filter="subjectExamsForm">

            <div class="layui-form-item">
                <label class="layui-form-label">选择考试科目</label>
                <div class="layui-input-block">
                    <div class="j-search-con multiple-box">
                        <input id="subjectInput" name="subject" type="text" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow j-arrow-slide"></span>
                        <div class="j-select-year slideShow">
                            <div class="search1">
                                <input id="searchKc" type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <div id="scroll1" style="max-height: 200px;overflow: hidden; position: relative;">
                                <ul name="subject" id="kcmc" style="max-height: none;">

                                </ul>
                            </div>

                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">教学班名称</label>
                <div class="layui-input-block">
                    <div class="j-search-con multiple-box">
                        <input id="subjectInput1" name="teachClass" type="text" placeholder="请选择" readonly="" class="schoolSel">
                        <span class="j-arrow"></span>
                        <div class="j-select-year">
                            <div class="search">
                                <input type="text" placeholder="搜索">
                                <span></span>
                            </div>
                            <div class="all-selects">全选</div>
                            <ul name="teachClass" id="jxbmc">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">教学班编号</label>
                <div class="layui-input-block">
                    <input id="jxbbh" type="text" disabled name="numberId" value="自动填写" placeholder="请输入" autocomplete="off"
                           class="layui-input disabled" />
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">时长</label>
                <div class="layui-input-block">
                    <input id="duration" type="text" name="duration" placeholder="请输入" autocomplete="off" class="layui-input" />
                </div>
            </div>

        </form>
    </div>
    <div class="dialog-btn"><button class="pu-cancel">取消</button>
        <button class="pu-sure" id="crSure1">确定</button>
    </div>
</div>

<script type="text/html" id="tmplToolBar">
    <div class="oprate-table">
        {{# if(d.ruleName != "已有课" ){ }}
        <span class="edit" lay-event="edit">编辑</span>
        <span class="delete" lay-event="delete">删除</span>
        {{# } else { }}
        <span class="handle" lay-event="handle">—</span>
        {{# }}}
    </div>
</script>
<script th:src="@{~/js/jquery-3.3.1.min.js}"></script>
<script th:src="@{~/js/jquery-form.js}"></script>
<script th:src="@{~/plugin/layui/layui.js}"></script>
<script th:src="@{~/js/my.util.js}"></script>
<script th:src="@{~/js/base.js}" type="text/javascript" charset="utf-8"></script>
<script th:src="@{~/js/examination/perfect-scrollbar.min.js}" type="text/javascript" charset="utf-8"></script>
<script th:inline="javascript">
    var  _VR_ = ''
    var fid = [[${fid}]];
    //转入上课占用按钮显隐
    if (fid != '17809' && fid != '250119'){
        $('#addClassRoomUse').hide()
        $('#addTeacherTeach').hide()
    }
    var formUserId = [[${formUserId}]];
    var curxnxq = [[${curxnxq}]]
    var curbatch = [[${curbatch}]]
    var multipleInvigilatorRule = 0;
    // var kinds = 0
    // function getKinds() {
    //     U.ajax({
    //         type: 'get',
    //         url: "/examination/rule/examHall/getKinds",
    //         data: {
    //             fid: fid
    //         },
    //         dataType: 'json',
    //         async: false,
    //         success: function (res) {
    //             if (res.code == 200) {
    //                 kinds = res.data.kinds
    //             }
    //         }
    //     })
    // }
    $(function () {
        //学生排考方式模块显隐
        // if (fid==97591||fid==250119||fid==251502||fid==299360|fid==133816){
            $("#rule_97591").show()
            $("#rule_97591").parent("ul").children().removeClass("active")
            $("#rule_97591").parent("ul").children().eq(0).addClass("active")
            $(".box-con").hide()
            $(".box-con").eq(0).show()
            // $("#rule_97591_classRoom").hide()
        // }else {
        //     $(".box-con").eq(1).show()
        // }
        getInvigilator();
        getBuilding();
        getHall();
        getClassroomRule(curxnxq, curbatch)
        getInvigilatorRule(curxnxq, curbatch);
        getNoticeRule(curxnxq, curbatch);
        getSession(curbatch);
        getSubject()

    })

    function getSession(batch) {
        $.get("/examination/basic/sessions", {fid: fid, bc: batch}, function (res) {
            if (res.code == 200) {
                var data = sort(res.data);
                var html = ""
                for (let i = 0; i < data.length; i++) {
                    if (i == 0) {
                        html += '  <ul class="con">'
                        html += '  <li class="time">' + format1(data[i].ksccgl_kssj) + "</li>"
                    }
                    if (i != 0 && format1(data[i - 1].ksccgl_kssj) != format1(data[i].ksccgl_kssj)) {
                        html += '  <ul class="con">'
                        html += '  <li class="time">' + format1(data[i].ksccgl_kssj) + "</li>"
                    }
                    html += ' <li data-kssj="' + data[i].ksccgl_kssj + '" data-jssj="' + data[i].ksccgl_jssj + '" data-ccmc="' + data[i].ksccgl_ccmc + '" data-ccbh="' + data[i].ksccgl_ccbh + '">' + format2(data[i].ksccgl_kssj, data[i].ksccgl_jssj) + '</li>'
                    if (i != 0 && i < data.length - 1 && format1(data[i].ksccgl_kssj) != format1(data[i + 1].ksccgl_kssj)) {
                        html += "</ul>"
                    }
                    if (i == data.length - 1) {
                        html += "</ul>"
                    }
                }
                $(".table-con").html(html);
            } else {
                $(".table-con").html("")
            }
        })
    }

    function sort(data) {
        if (data == null || data == undefined || data == "" || data.length <= 1) {
            return data;
        }
        for (let i = 0; i < data.length; i++) {
            let flag = true;
            for (let j = 0; j < data.length - i - 1; j++) {
                if (new Date(data[j].kssj).getTime() > new Date(data[j + 1].kssj).getTime()) {
                    let temp = data[j];
                    data[j] = data[j + 1];
                    data[j + 1] = temp;
                    flag = false;
                }
            }
            if (flag) {
                break;
            }
        }
        return data;
    }

    var invigilatorData = [];

    function getInvigilator() {
        $.get("/examination/basic/invigilator2", {fid: fid}, function (res) {
            if (res.code == 200) {

                var data = res.data;
                invigilatorData = data;
                flashInvigilatorData()
            }
        })
    }

    function flashInvigilatorData() {
        var html = '';
        var yx = '';
        var js = '';
        var set = new Set();
        var jsSet = new Set();
        for (let i = 0; i < invigilatorData.length; i++) {
            if (invigilatorData[i].jkjsgl_jsxm.uname == undefined || invigilatorData[i].jkjsgl_jsxm.uname == "") {
                continue;
            }
            html += '<li data-js="' + invigilatorData[i].jkjsgl_js + '" data-xgh="' + invigilatorData[i].jkjsgl_jsgh + '"  data-uid="' + invigilatorData[i].jkjsgl_jsxm.puid + '"  data-dept="' + invigilatorData[i].jkjsgl_ssyx + '">' + invigilatorData[i].jkjsgl_jsxm.uname + '</li>'
            set.add(invigilatorData[i].jkjsgl_ssyx)
            jsSet.add(invigilatorData[i].jkjsgl_js)
        }
        set.forEach(data => {
            yx += '<li data-name="' + data + '">' + data + '</li>'
        })
        jsSet.forEach(data => {
            js += '<li data-name="' + data + '">' + data + '</li>'
        })
        $("ul[name='teacher']").html(html);
        $("ul[name='college']").html(yx);
        $("ul[name='college2']").html(yx);
        $("ul[name='roleRange']").html(js);
        $("ul[name='roleRange2']").html(js);
    }

    var college = ''
    $("ul[name='college']").on("click", "li ", function (res) {
        college = ''
        var elements = document.getElementById("college").getElementsByClassName("active");
        for (let element of elements) {
            college += "," + element.innerHTML
        }
        var dept = $(this).html();
        if (college.indexOf(dept) == -1){
            college += "," + dept;
        }else {
            college = college.replace(dept,"")
        }
        $("ul[name='teacher'] li").each(function () {
            $(this).show();
        })
        if (college == ','){
            return;
        }
        $("ul[name='teacher'] li").each(function () {

            if (college.indexOf($(this).attr("data-dept")) == -1) {

                $(this).hide();
            }
        })



        if (jsjs == ',' || jsjs == ''){
            return;
        }
        $("ul[name='teacher'] li").each(function () {
            if (jsjs.indexOf($(this).attr("data-js")) == -1) {
                $(this).hide();
            }
        })
    })
    $("ul[name='college2']").on("click", "li ", function (res) {
        college = ''
        var elements = document.getElementById("college2").getElementsByClassName("active");
        for (let element of elements) {
            college += "," + element.innerHTML
        }
        var dept = $(this).html();
        if (college.indexOf(dept) == -1){
            college += "," + dept;
        }else {
            college = college.replace(dept,"")
        }
        $("ul[name='teacher'] li").each(function () {
            $(this).show();
        })
        if (college == ','){
            return;
        }
        $("ul[name='teacher'] li").each(function () {

            if (college.indexOf($(this).attr("data-dept")) == -1) {

                $(this).hide();
            }
        })



        if (jsjs == ',' || jsjs == ''){
            return;
        }
        $("ul[name='teacher'] li").each(function () {
            if (jsjs.indexOf($(this).attr("data-js")) == -1) {
                $(this).hide();
            }
        })

    })

    var jsjs = ''
    $("ul[name='roleRange']").on("click", "li ", function (res) {
        jsjs = ''
        var elements = document.getElementById("roleRange").getElementsByClassName("active");
        for (let element of elements) {
            jsjs += "," + element.innerHTML
        }
        var js = $(this).html();
        if (jsjs.indexOf(js) == -1){
            jsjs += "," + js;
        }else {
            jsjs = jsjs.replace(js,"")
        }
        $("ul[name='teacher'] li").each(function () {
            $(this).show();
        })

        if (jsjs == ','){
            return;
        }
        $("ul[name='teacher'] li").each(function () {

            if (jsjs.indexOf($(this).attr("data-js")) == -1) {
                $(this).hide();
            }
        })

        if (college == ',' || college == ''){
            return;
        }
        $("ul[name='teacher'] li").each(function () {

            if (college.indexOf($(this).attr("data-dept")) == -1) {

                $(this).hide();
            }
        })
    })

    $("ul[name='roleRange2']").on("click", "li ", function (res) {
        jsjs = ''
        var elements = document.getElementById("roleRange2").getElementsByClassName("active");
        for (let element of elements) {
            jsjs += "," + element.innerHTML
        }

        var js = $(this).html();

        if (jsjs.indexOf(js) == -1){
            jsjs += "," + js;
        }else {
            jsjs = jsjs.replace(js,"")
        }
        $("ul[name='teacher'] li").each(function () {
            $(this).show();
        })
        if (jsjs == ','){
            return;
        }
        $("ul[name='teacher'] li").each(function () {

            if (jsjs.indexOf($(this).attr("data-js")) == -1) {
                $(this).hide();
            }
        })

        if (college == ',' || college == ''){
            return;
        }
        $("ul[name='teacher'] li").each(function () {
            if (college.indexOf($(this).attr("data-dept")) == -1) {

                $(this).hide();
            }
        })
    })


    function checkData() {
        $(".table-con .con").each(function () {
            var dateValue = $(this).find("li").eq(0).attr("data-kssj");
            var format = format1(dateValue);
            $(this).find("li").each(function (index, e) {
                var html = $(".table-con .head li").eq(index).html();
                if (html != format) {
                    $(this).remove();
                }
            })
        })
    }


    function getClassroomRule(xnxq, batch) {
        $.get("/examination/rule/classroom/get", {xnxq: xnxq, bc: batch}, function (res) {
            if (res.code == 200) {
                var data = res.data;
                //
                // getKinds()
                // var address1 = data.address1
                // var leftOrRight1 = data.leftOrRight1
                // var arrangement1 = data.arrangement1
                // var leftOrRight2 = data.leftOrRight2
                // var arrangement2 = data.arrangement2
                // //导出座次大表字段
                // layui.use(['form', 'jquery'], function(){
                //     var form = layui.form;
                //     var $ = layui.jquery;
                //     // 设置选中值（例如设置为 value="2" 的选项）
                //     $('select[name="address1"]').val(address1); // 设置 DOM 值
                //     // 必须重新渲染 select 组件
                //     form.render('select', 'address1'); // 第二个参数是 name 属性
                //
                //
                //     // 被选中的元素在当前选项中的索引
                //     function setkcmwz2(value) {
                //         $('#address2').find("option[value="+value+"]").attr("selected",true).siblings().attr("selected",false);
                //         // form.render('select') //再次渲染
                //     }
                //     if (address1 == '1' || '2' == address1 ){
                //         console.log(1)
                //         document.getElementById("seatStart1").style.display = '';
                //         $("input[name=leftOrRight1][value='" + leftOrRight1 + "']").prop("checked", true);
                //         document.getElementById("zwplfs1").style.display = '';
                //         $('select[name="arrangement1"]').val(arrangement1); // 设置 DOM 值
                //         // 必须重新渲染 select 组件
                //         form.render('select', 'arrangement1'); // 第二个参数是 name 属性
                //         if (kinds == 2){
                //             document.getElementById("kcmwz2").style.display = '';
                //             console.log(2)
                //             if (address1 == 1){setkcmwz2(2)}
                //             if (address1 == 2){setkcmwz2(1)}
                //             document.getElementById("seatStart2").style.display = '';
                //             $("input[name=leftOrRight2][value='" + leftOrRight2 + "']").prop("checked", true);
                //             document.getElementById("zwplfs2").style.display = '';
                //             $('select[name="arrangement2"]').val(arrangement2); // 设置 DOM 值
                //
                //         }
                //     }else {
                //         console.log(3)
                //         document.getElementById("kcmwz2").style.display = 'none';
                //         document.getElementById("seatStart1").style.display = 'none';
                //         document.getElementById("zwplfs1").style.display = 'none';
                //         document.getElementById("seatStart2").style.display = 'none';
                //         document.getElementById("zwplfs2").style.display = 'none';
                //     }
                //     // 必须重新渲染 select 组件
                //     form.render('select') //再次渲染
                //     // 设置选中值（例如设置为 value="2" 的选项）
                //     $('input[name="leftOrRight1"]').val(data.leftOrRight1); // 设置 DOM 值
                //
                //
                //
                //
                // });
                //
                //
                $("input[name=thisClass][value='" + data.thisClass + "']").prop("checked", true);
                $("input[name=split][value='" + data.split + "']").prop("checked", true);
                $("input[name=campus][value='" + data.campus + "']").prop("checked", true);
                $("input[name=classroom][value='" + data.classroom + "']").prop("checked", true);
                $("input[name=zwh][value='" + data.zwh + "']").prop("checked", true);
                $("input[name=remainStudentHandleRule][value='" + data.remainStudentHandleRule + "']").prop("checked", true);
                $("#classroomRuleId").val(data.id);
                // if (data.split == 1) {
                //     $("input[name=capacity]").parents(".layui-form-item").show()
                //     $("input[name=capacity]").val(data.capacity)
                //     $("input[name=remainStudentHandleRule]").parents(".layui-form-item").show()
                // } else {
                //     $("input[name=capacity]").parents(".layui-form-item").hide()
                //     $("input[name=capacity]").val("")
                //     $("input[name=remainStudentHandleRule]").parents(".layui-form-item").hide()
                //     $("input[name=remainStudentHandleRule]").val("")
                // }
            }
            layui.use(['form'], function () {
                var form = layui.form;
                form.render('radio');
            })

        })
    }


    function zhzs(value) {
        value = value.replace(/[^\d]/g, '').replace(/^0{1,}/g, '');
        if (value != '') {
            value = parseInt(value);
        }
        return value;
    }


    function saveReport() {
        var spilt = $("input[name=split]:checked").val();
        if (spilt == 1) {
            var capacity = $("input[name=capacity]").val();
            if (capacity == undefined || capacity == '' || capacity == 0) {
                layer.msg("请填写考场容量", {icon: 2, time: 3000});
                return false;
            }
        }
        addParam($("#classroomForm")[0]);

        // jquery 表单提交
        $("#classroomForm").ajaxSubmit(function (res) {
            if (res.code == 200) {
                $("#classroomRuleId").val(res.data);
                U.success("保存成功");
            } else {
                U.fail(res.msg);
            }

        });
        return false;
    }

    function saveInvigilator() {
        // var status = $("input[name=status]:checked").val();
        // if (status == 1) {
        //     var teacherNum = $("input[name=teacherNum]").val();
        //     if (teacherNum == undefined || teacherNum == '' || teacherNum == 0) {
        //         layer.msg("请填写监考教师数量", {icon: 2, time: 3000});
        //         return false;
        //     }
        // }
        $("input[name=near]:checked").val();
        addParam($("#invigilatorRule")[0]);

        // jquery 表单提交
        $("#invigilatorRule").ajaxSubmit(function (res) {
            if (res.code == 200) {
                $("#invigilatorId").val(res.data);
                U.success("保存成功");
            } else {
                U.fail(res.msg);
            }

        });
        return false;
    }

    function saveNotice() {
        addParam($("#noticeRule")[0]);

        // jquery 表单提交
        $("#noticeRule").ajaxSubmit(function (res) {
            if (res.code == 200) {
                $("#noticeId").val(res.data);
                U.success("保存成功");
            } else {
                U.fail(res.msg);
            }

        });
        return false;
    }


    function saveTeacher() {
        addParam($("#banTeacherForm")[0]);

        var teacher = [];
        $("#banTeacherForm ul[name=teacher]").find("li.active").each(function () {
            var uid = $(this).attr("data-uid");
            var uname = $(this).text();
            var json = {
                uid: uid,
                uname: uname
            }
            teacher.push(json);
        })

        var time = [];
        $(".banteacher .con").each(function () {
            $(this).find(".active").each(function () {
                var json = {
                    kssj: $(this).attr("data-kssj"),
                    jssj: $(this).attr("data-jssj"),
                }
                var timeJson = {
                    ccmc: $(this).attr("data-ccmc"),
                    ccbh: $(this).attr("data-ccbh"),
                    data: json
                }
                time.push(timeJson);
            })
        })

        if ($("#banTeacherForm")[0].teacherJson == undefined) {
            var opt = document.createElement('input')
            opt.name = 'teacherJson'
            opt.value = JSON.stringify(teacher);
            opt.type = "hidden";
            $("#banTeacherForm")[0].appendChild(opt)
        } else {
            $("input[name=teacherJson]").val(JSON.stringify(teacher))
        }


        if ($("#banTeacherForm")[0].time == undefined) {
            var opt = document.createElement('input')
            opt.name = 'time'
            opt.value = JSON.stringify(time);
            opt.type = "hidden";
            $("#banTeacherForm")[0].appendChild(opt)
        } else {
            $("#banTeacherForm input[name=time]").val(JSON.stringify(time))
        }


        $("#banTeacherForm").ajaxSubmit(function (res) {
            if (res.code == 200) {
                U.success("保存成功");
            } else {
                U.fail(res.msg);
            }

        });
        return false;
    }

    function saveLimit() {
        addParam($("#invigilateLimitForm")[0]);

        var teacher = [];
        $("#invigilateLimitForm ul[name=teacher]").find("li.active").each(function () {
            var uid = $(this).attr("data-uid");
            var xgh = $(this).attr("data-xgh");
            var uname = $(this).text();
            var json = {
                uid: uid,
                uname: uname,
                xgh: xgh
            }
            teacher.push(json);
        })

        if ($("#invigilateLimitForm")[0].teacherJson == undefined) {
            var opt = document.createElement('input')
            opt.name = 'teacherJson'
            opt.value = JSON.stringify(teacher);
            opt.type = "hidden";
            $("#invigilateLimitForm")[0].appendChild(opt)
        } else {
            $("#invigilateLimitForm input[name=teacherJson]").val(JSON.stringify(teacher))
        }


        $("#invigilateLimitForm").ajaxSubmit(function (res) {
            if (res.code != 200) {
                U.fail(res.msg);
            }

        });
        return false;
    }

    function checkTeacher() {
        var teacher = $("#banTeacherForm input[name=teacher]").val();
        if (teacher == undefined || teacher == '') {
            U.fail("请选择禁排教师");
            return false;
        }
        var active = $(".banteacher").find("li.active");
        if (active.length == 0) {
            U.fail("请选择禁排时间");
            return false;
        }
        return true;
    }

    function addParam(e) {
        if (e.fid == undefined) {
            var opt = document.createElement('input')
            opt.name = 'fid'
            opt.value = fid
            opt.type = "hidden";
            e.appendChild(opt)
        }

        if (e.xnxq == undefined) {
            var opt = document.createElement('input')
            opt.name = 'xnxq'
            opt.value = curxnxq
            opt.type = "hidden";
            e.appendChild(opt)
        }


        if (e.examinationBatchCode == undefined) {
            var opt = document.createElement('input')
            opt.name = 'examinationBatchCode'
            opt.value = curbatch
            opt.type = "hidden";
            e.appendChild(opt)
        }
    }

    function getBuilding() {
        $.get("/examination/basic/building", {fid: fid}, function (res) {
            var html = ''
            if (res.code == 200) {
                var data = res.data;
                for (let i = 0; i < data.length; i++) {
                    html += '<li>' + data[i].jxl_jxlmc + '</li>'
                }
            }
            $("ul[name=build]").html(html);
        })
    }

    function getHall() {
        $.get("/examination/basic/hall", {fid: fid}, function (res) {
            var html = ''
            if (res.code == 200) {
                var data = res.data;
                for (let i = 0; i < data.length; i++) {
                    html += '<li data-build="' + data[i].kcgl_kcssjxl + '" data-value="' + data[i].kcgl_kcbh + '">' + data[i].kcgl_kcmc + '</li>'
                }
            }
            $("ul[name=classroom]").html(html);
        })
    }

    function getInvigilatorRule(curxnxq, curbatch) {
        $.get("/examination/rule/invigilator/get", {fid: fid, xnxq: curxnxq, bc: curbatch}, function (res) {
            if (res.code == 200) {
                var data = res.data;
                $("input[name=classTeacherNoInvigilationClass][value='" + data.classTeacherNoInvigilationClass + "']").prop("checked", true);
                $("input[name=noInvigilationTeachCourse][value='" + data.noInvigilationTeachCourse + "']").prop("checked", true);
                $("input[name=noInvigilationTeachClass][value='" + data.noInvigilationTeachClass + "']").prop("checked", true);
                $("input[name=status][value='" + data.status + "']").prop("checked", true);
                $("input[name=sameDept][value='" + data.sameDept + "']").prop("checked", true);
                $("input[name=near][value='" + data.near + "']").prop("checked", true);
                $("input[name=campus2][value='" + data.campus2+ "']").prop("checked", true);
                $("input[name=thisCourseTeacher][value='" + data.thisCourseTeacher+ "']").prop("checked", true);
                $("input[name=sex][value='" + data.sex+ "']").prop("checked", true);
                $("input[name=multipleInvigilatorRule][value='" + data.multipleInvigilatorRule + "']").prop("checked", true);
                multipleInvigilatorRule = data.multipleInvigilatorRule
                $("input[name=maximum]").val(data.maximum)
                $("input[name=teacherNum]").val(data.teacherNum)
                $("#invigilatorId").val(data.id);

                if (data.status == 1) {
                    $("input[name=multipleInvigilatorRule]").parents(".layui-form-item").show()
                    if (data.multipleInvigilatorRule == 0){
                        $("input[name=maximum]").parents(".layui-form-item").show()
                        $("input[name=maximum]").val(data.maximum)
                    }else if (data.multipleInvigilatorRule == 1){
                        $("input[name=teacherNum]").parents(".layui-form-item").show()
                        $("input[name=teacherNum]").val(data.teacherNum)
                        if (data.teacherNum == 2){
                            $("input[name=sex]").parents(".layui-form-item").show()
                        }
                    }
                } else {
                    $("input[name=multipleInvigilatorRule]").parents(".layui-form-item").hide()
                    $("input[name=multipleInvigilatorRule]").val("")
                    $("input[name=maximum]").parents(".layui-form-item").hide()
                    $("input[name=maximum]").val(data.maximum)
                    $("input[name=teacherNum]").parents(".layui-form-item").hide()
                    $("input[name=teacherNum]").val(data.teacherNum)
                }
            }
            layui.use(['form'], function () {
                var form = layui.form;
                form.render('radio');
            })
        })
    }

    function getNoticeRule(curxnxq, curbatch) {
        $.get("/examination/notice/getNoticeInfo", {fid: fid, term: curxnxq, batchCode: curbatch}, function (res) {
            if (res.code == 200) {
                var data = res.data;
                $("input[name=isNoticeTeacher][value='" + data.isNoticeTeacher + "']").prop("checked", true);
                $("input[name=isNoticeRole][value='" + data.isNoticeRole + "']").prop("checked", true);
                $("#noticeId").val(data.id);
                if (data.isNoticeTeacher == 1) {
                    $("#notice-content").parents(".layui-form-item").show()
                    $("#notice-content").val(data.content)
                } else {
                    $("#notice-content").parents(".layui-form-item").hide()
                    $("#notice-content").val("")
                }
                if (data.isNoticeRole == 1) {
                    $("#materialTable5").parents(".z-table").show();
                }
            }
            layui.use(['form'], function () {
                var form = layui.form;
                form.render('radio');
            })
        })
    }

    $("#crSure").click(function () {
        var classroom = $("input[name=classroom]").val();
        if (classroom == undefined || classroom == '') {
            U.fail("请选择禁用教室");
            return false;
        }
        var classroom = [];
        $("ul[name=classroom] li.active").each(function () {
            var json = {
                bh: $(this).attr("data-value"),
                name: $(this).text()
            }
            classroom.push(json)
        })
        var time = [];
        $(".banclassroom .con").each(function () {
            $(this).find(".active").each(function () {
                var json = {
                    kssj: $(this).attr("data-kssj"),
                    jssj: $(this).attr("data-jssj"),
                }
                var timeJson = {
                    ccmc: $(this).attr("data-ccmc"),
                    ccbh: $(this).attr("data-ccbh"),
                    data: json
                }
                time.push(timeJson);
            })
        })
        if (time.length == 0) {
            U.fail("请选择禁用时间");
            return false;
        }
        $.post("/examination/rule/classroom2/save",
            {
                fid: fid,
                xnxq: curxnxq,
                id: $("#crForbitRule input[name=id]").val(),
                examinationBatchCode: curbatch,
                ruleName: $("#crForbitRule input[name=ruleName]").val(),
                jxl: $("#crForbitRule input[name=build]").val(),
                classroom: JSON.stringify(classroom),
                time: JSON.stringify(time)
            }, function (res) {
                if (res.code == 200) {
                    U.success("保存成功");
                } else {
                    U.fail(res.msg);
                }
            })
    })


    function format1(date) {
        if (date == undefined || date == null || date == '') {
            return '';
        }
        var dateTime = new Date(date);
        var month = dateTime.getMonth() + 1;
        var day = dateTime.getDate();
        if (day < 10) {
            day = '0' + day;
        }
        return month + "月" + day + "日";

    }

    function format2(d1, d2) {
        if (d1 == undefined || d1 == null || d1 == '' || d2 == undefined || d2 == null || d2 == '') {
            return '';
        }
        var dateTime1 = new Date(d1);
        var dateTime2 = new Date(d2);
        var hours1 = dateTime1.getHours();
        var hours2 = dateTime2.getHours();
        var minutes1 = dateTime1.getMinutes();
        if (minutes1 < 10) {
            minutes1 = "0" + minutes1;
        }
        var minutes2 = dateTime2.getMinutes();
        if (minutes2 < 10) {
            minutes2 = "0" + minutes2;
        }
        return hours1 + ":" + minutes1 + " - " + hours2 + ":" + minutes2;
    }



    // layui.use(['form'], function () {
    //     var form = layui.form;
    //     function setkcmwz2(value) {
    //         $('#address2').find("option[value="+value+"]").attr("selected",true).siblings().attr("selected",false);
    //         form.render('select') //再次渲染
    //     }
    //     form.on('select(address1)', function(data){
    //         console.log(data.value); // 被选中的值
    //         console.log(data.elem); // 被选中的元素DOM对象
    //         console.log(data.index); // 被选中的元素在当前选项中的索引
    //         if (data.value == '1' || '2' == data.value ){
    //             document.getElementById("seatStart1").style.display = '';
    //             document.getElementById("zwplfs1").style.display = '';
    //             if (kinds == 2){
    //                 document.getElementById("kcmwz2").style.display = '';
    //                 if (data.value == 1){setkcmwz2(2)}
    //                 if (data.value == 2){setkcmwz2(1)}
    //                 document.getElementById("seatStart2").style.display = '';
    //                 document.getElementById("zwplfs2").style.display = '';
    //             }
    //         }else {
    //             document.getElementById("kcmwz2").style.display = 'none';
    //             document.getElementById("seatStart1").style.display = 'none';
    //             document.getElementById("zwplfs1").style.display = 'none';
    //             document.getElementById("seatStart2").style.display = 'none';
    //             document.getElementById("zwplfs2").style.display = 'none';
    //         }
    //
    //
    //         // 在这里编写你的逻辑代码
    //     });
    //
    //
    // })
    var kc1
//$('#noInvigilationTeachClass').parent(".radio-list").parent('.layui-input-block').parent('.layui-form-item').show()
    function getSubject() {
        $.post("/examination/rule/subject/get", {fid: fid, bc: curbatch, xnxq:curxnxq}, function (res) {
            if (res.code == 200) {
                var kc = res.data.kc;
                kc1 = res.data.kc;
                var html = ""
                for (let i = 0; i < kc.length; i++) {
                    html += '<li class="isNotSearch" data-id="'+kc[i].kcbh+'">'+kc[i].kcmc+'</li>'
                }
                $("#kcmc").html(html);
            } else {
                $("#jxbmc").html("")
                $("#kcmc").html("")
            }
        })
    }

    $("#kcmc").on("click", "li ", function (res) {
        var kcbh = $(this).attr('data-id')
        if ($("#kcmc").attr('data-id') == undefined){
            $("#kcmc").attr('data-id', kcbh);
        }else {
            //如果是选中  else为取消选中
            if ($(this).hasClass('active') && !$(this).is(':hidden')){
                $("#kcmc").attr('data-id', $("#kcmc").attr('data-id').replace(kcbh, ''));

            }else {
                if ($("#kcmc").attr('data-id').indexOf(kcbh) == -1){
                    $("#kcmc").attr('data-id', $("#kcmc").attr('data-id') + ',' +kcbh);
                }
            }


        }
        var html2 = ""
        for (let i = 0; i < kc1.length; i++) {
            var kcmcs = $("#kcmc").attr('data-id').split(',')
            for (let kcmcsKey in kcmcs) {

                if (kc1[i].kcbh == kcmcs[kcmcsKey]){

                    for (let jxbbhKey in kc1[i].jxb) {
                        var jxb = kc1[i].jxb[jxbbhKey]
                        html2 += '<li data-id="'+jxb.jxbbh+'" title="'+jxb.jxbmc+'">'+jxb.jxbmc+'</li>'
                    }
                }
            }
        }
        $("#jxbmc").html(html2);

    })
    $("#jxbmc").on("click", "li ", function (res) {
        $("#jxbbh").val($(this).attr('data-id'));
        var jxbbh = $(this).attr('data-id')
        if ($("#jxbbh").attr('data-id') == undefined){
            $("#jxbbh").attr('data-id', jxbbh);
            $("#jxbbh").val(jxbbh);
        }else {
            //如果是选中  else为取消选中
            if ($(this).hasClass('active') && !$(this).is(':hidden')){
                $("#jxbbh").attr('data-id', $("#jxbbh").attr('data-id').replace(jxbbh, ''));
                $("#jxbbh").val($("#jxbbh").attr('data-id').replace(jxbbh, ''));
            }else {
                if ($("#jxbbh").attr('data-id').indexOf(jxbbh) == -1){
                    $("#jxbbh").attr('data-id', $("#jxbbh").attr('data-id') + ',' +jxbbh);
                    $("#jxbbh").val($("#jxbbh").attr('data-id') + ',' +jxbbh);
                }
            }
        }

    })
    function myAlert(mes){
        $(".myAlert").remove();
        var html = "<div class='myAlert' style='font-size: 20px;display:inline-block;width:240px;position:fixed;left:50%;top:50%;margin:-25px 0 0 -120px;z-index:19891016; text-align:center; background:rgba(0,0,0,.7); border-radius:5px; display:none;' ><a href=\"javascript:\" style='width:100%; min-height:50px; padding:10px;display:-webkit-box;-webkit-box-pack:center; -webkit-box-align:center; color:#FFF !important;'>"+mes+"</a></div>";

        $("body").append("\r\n"+html+"\r\n");
        $(".myAlert").fadeIn();
        function hide(){
            $(".myAlert").fadeOut();
        }
        setTimeout(hide, 2000)

    }


    const container = document.getElementById('scroll1');
    const ps = new PerfectScrollbar(container, {
        wheelSpeed: 0.5,
        wheelPropagation: true,
        minScrollbarLength: 30
    });
    var page = 1;
    var pageSize = 100;
    var runnning = false;
    container.addEventListener('ps-scroll-y', function (e) {
        if (isSearch){
            return;
        }
        let hh = $("#scroll1").outerHeight(true);
        $("#scroll1 p").show();
        if (e.target.scrollTop >= container.scrollHeight - hh - 5) {
            if (runnning == false){
                runnning = true
                if ($("#scroll1 p").length == 0) {
                    $("#scroll1").append("<p>正在加载中...</p>");
                }

                page++;
                $.get("/examination/rule/subject/get", {fid: fid, bc: curbatch, xnxq:curxnxq ,page: page, pageSize: pageSize}, function (res) {
                    if (res.code == 200) {
                        var kc = res.data.kc;
                        var html = ""
                        for (let i = 0; i < kc.length; i++) {
                            html += '<li class="isNotSearch" data-id="'+kc[i].kcbh+'">'+kc[i].kcmc+'</li>'
                        }

                        if(kc.length==0){
                            $("#scroll1 p").hide();
                        }
                        $("#scroll1 ul").append(html);
                        ps.update();
                    }
                })
                // $("#scroll1 p").hide()
                runnning = false
            }
        }
    });


    const inputElement = document.getElementById('searchKc');
    var kcliHtml = '';
    var isSearch = false

    // 查询课程
    function searchKc(value) {
        if (value != ''){
            isSearch = true
            kcliHtml = $("#scroll1 ul").html()
            page++;
            $.get("/examination/rule/subject/get", {fid: fid, bc: curbatch, xnxq:curxnxq ,page: 1, pageSize: 50, searchKc: value}, function (res) {
                if (res.code == 200) {
                    var kc = res.data.kc;
                    kc1.push(...res.data.kc)
                    var html = ""
                    for (let i = 0; i < kc.length; i++) {
                        html += '<li class="isSearch" data-id="'+kc[i].kcbh+'">'+kc[i].kcmc+'</li>'
                    }
                    $("#scroll1 ul .isSearch").remove()
                    $("#scroll1 ul").append(html);
                    $("#scroll1 ul .isNotSearch").hide()
                    $("#scroll1 p").hide();
                    $("#jxbmc").html(html);
                    ps.update();
                }
            })
            console.log('if')
        }else {
            isSearch = false
            $("#scroll1 ul .isNotSearch").show()
            $("#scroll1 ul .isSearch").remove()
            console.log('else')
        }

    }

    // 监听input事件（值改变时触发）
    inputElement.addEventListener('input', (event) => {
        debouncedSearch(event.target.value);
    });

    const debouncedSearch = debounce(searchKc, 500);

    function debounce(func, delay) {
        let timer;
        return function(...args) {
            clearTimeout(timer);
            timer = setTimeout(() => {
                func.apply(this, args);
            }, delay);
        };
    }

</script>
<script th:src="@{~/js/examination/index.js?v=20240326}"></script>

</html>