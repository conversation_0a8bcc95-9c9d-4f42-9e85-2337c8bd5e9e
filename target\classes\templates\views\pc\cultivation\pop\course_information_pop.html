<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <title>课程列表</title>
    <link rel="stylesheet" th:href="@{../css/cultivation/jquery.searchableSelect.css}"/>
    <link rel="stylesheet" th:href="@{../css/cultivation/newStyle.css}"/>
    <link rel="stylesheet" th:href="@{../plugin/layui/css/layui.css}"/>
    <style>
        html, body {
            height: 100%;
        }

        .layui-laypage .layui-laypage-curr .layui-laypage-em {
            background-color: #4C88FF !important;
        }

        .layui-laypage .layui-laypage-limits {
            display: none !important;
        }

        .layui-laypage .layui-laypage-curr .layui-laypage-em {
            background-color: #4C88FF !important;
        }

        .layui-form-radio > i:hover, .layui-form-radioed > i {
            color: #4C88FF !important;
        }
        .layui-form-checked[lay-skin="primary"] i {
            border-color: #4C85FA !important;
            background-color: #4C85FA;
        }

        body .layui-layer-btn .layui-layer-btn0 {
            background: #4C88FF;
            border: 1px solid #4C88FF;
            border-radius: 20px;
            display: inline-block;
            width: 60px;
            height: 34px;
            text-align: center;
            line-height: 34px;
            font-size: 14px;
            color: #FFFFFF;
        }

        body .layui-layer-btn .layui-layer-btn1 {
            background: #FFFFFF;
            border: 1px solid #94C1FF;
            border-radius: 20px;
            display: inline-block;
            width: 60px;
            height: 34px;
            text-align: center;
            line-height: 34px;
            font-size: 14px;
            color: #4C88FF;
        }

        .opt_data_top {
            margin: 13px 31px -18px;
        }

        .opt_data_top .opt_data_num em {
            margin: 0 4px;
            color: #4C88FF;
            font-style: normal;
        }
        .layui-table-page {
            text-align: right;
        }
        .fixed-bottom {
            position: relative;
            height: 1px;
        }
        .fixed-bottom .selectAll {
            position: absolute;
            left: 19px;
            top: -30px;
            display: block;
            font-size: 14px;
            color: #6581BA;
            background-size: 16px;
            cursor: pointer;
        }
        .fixed-bottom .selectAll span {
            position: relative;
            display: block;
            padding-left: 28px;
        }
        .fixed-bottom .selectAll span.cur:after {
            background: url(/images/cultivation/checked-icon.png) no-repeat center;
            border: none;
            color: transparent;
        }
        .fixed-bottom .selectAll span:after {
            content: '';
            position: absolute;
            left: -3px;
            top: 2px;
            width: 18px;
            height: 18px;
            box-sizing: borDer-box;
            -webkit-box-sizing: borDer-box;
            -moz-box-sizing: borDer-box;
            -ms-box-sizing: borDer-box;
            -o-box-sizing: borDer-box;
            border: 1px solid #d2d2d2;
            border-radius: 2px;
        }
    </style>
</head>
<body>
<div class="popDiv popMove wid840" style="width: 1071px;max-height: 720px;">
    <div class="popBody" id="popScroll" style="max-height: 680px;">
        <div class="popSearch clearAfter" style="padding: 20px 0 0 24px;">
            <div class="popSearch_row fl">
                <div class="popSearch_name">
                    <div class="popSearch_table">
                        <div class="popSearch_cell"><span>开课学期</span></div>
                    </div>
                </div>
                <div class="popSearch_per" th:text="${teachClassGroupForm?.jxbbz_kkxq}">
                    vessel
                </div>
            </div>
            <div class="popSearch_row fl">
                <div class="popSearch_name">
                    <div class="popSearch_table">
                        <div class="popSearch_cell"><span>开课系部</span></div>
                    </div>
                </div>
                <div class="popSearch_per">
                    <select class="qselect" name="kkxxb_kkyxyx">
                        <option value="">请选择</option>
                    </select>
                </div>
            </div>
            <div class="popSearch_row fl">
                <div class="popSearch_name">
                    <div class="popSearch_table">
                        <div class="popSearch_cell"><span>课程名称</span></div>
                    </div>
                </div>
                <div class="popSearch_per">
                    <select class="qselect" name="kkxxb_kcmc">
                        <option value="">请选择</option>
                    </select>
                </div>
            </div>
            <div class="popSearch_row fl">
                <div class="popSearch_name">
                    <div class="popSearch_table">
                        <div class="popSearch_cell"><span>选必修</span></div>
                    </div>
                </div>
                <div class="popSearch_per">
                    <select class="qselect" name="kkxxb_xbx">
                        <option value="">请选择</option>
                        <option value="选修">选修</option>
                        <option value="必修">必修</option>
                    </select>
                </div>
            </div>
            <div class="popSearch_row fl">
                <div class="popSearch_name">
                    <div class="popSearch_table">
                        <div class="popSearch_cell"><span>教学班编号</span></div>
                    </div>
                </div>
                <div class="popSearch_per">
                    <select class="qselect" name="kkxxb_jxbbh">
                        <option value="">请选择</option>
                    </select>
                </div>
            </div>
            <div class="popSearch_row fl">
                <div class="popSearch_name">
                    <div class="popSearch_table">
                        <div class="popSearch_cell"><span>教学班名称</span></div>
                    </div>
                </div>
                <div class="popSearch_per">
                    <select class="qselect" name="kkxxb_jxbmc">
                        <option value="">请选择</option>
                    </select>
                </div>
            </div>
            <div class="popSearch_btns">
                <div class="popSearch_clear fr">清空筛选</div>
                <div class="popSearch_search_btn fr" lay-filter="search" lay-submit>筛选</div>
            </div>
        </div>
        <div class="opt_data_top">
            <label class="opt_data_num">
                <span>已选<em th:text="${teachClassGroupForm?.jxbbz_yykkxxs}"></em>条</span>
                <span style="color: #4C88FF;cursor: pointer;" id="clearGroup">清空</span>
            </label>
        </div>
        <div class="popSearch_cont">
            <table lay-filter="courseInformationTable" class="layui-table" id="courseInformationTable">
            </table>
            <div class="fixed-bottom">
                <div class="selectAll">
                    <span>选中所有数据</span>
                </div>
            </div>
        </div>
    </div>
    <div class="layui-layer-btn"><a class="layui-layer-btn1">取消</a><a class="layui-layer-btn0">确定</a></div>
</div>
<script th:src="@{../js/jquery-3.3.1.min.js}"></script>
<script th:src="@{../js/my.util.js}"></script>
<script th:src="@{../js/jquery.nicescroll.min.js}"></script>
<script th:src="@{../js/cultivation/jquery.searchableSelect.js}"></script>
<script th:src="@{../plugin/layui/layui.js}"></script>
<script th:src="@{../js/cultivation/select_data.js?v=1}"></script>
<script th:inline="javascript">
    const fid = [[${fid}]];
    const formUserId = [[${formUserId}]];
    const formId = [[${formId}]];
    let cancelArray = [];
    let uniqueArray = [];
    let curPageArray = [];
    $(function () {
        $('.qselect').click();
    })

    //弹窗居中
    function MoveFixed(userClass) {
        $(userClass).css({
            top: function () {
                return 0;
            }, left: function () {
                return ($(window).width() - $(this).width()) / 2;
            }
        });

    }

    window.onresize = function () {
        MoveFixed('.popMove')
    }
    MoveFixed('.popMove')

    function scrollBox(userID) {
        $(userID).niceScroll({
            cursorborder: "",
            cursorwidth: 8,
            cursorcolor: "#CFD8E6",
            boxzoom: false,
            autohidemode: true
        });
        $(userID).getNiceScroll().resize(); //检测滚动条是否重置大小（当窗口改变大小时）
    }

    //scrollBox("#popScroll");
    document.domain = "chaoxing.com";
    var table = "", insTb = "", moocId = "";
    layui.use('table', function () {
        table = layui.table;
        insTb = table.render({
            elem: '#courseInformationTable',
            url: '../processData/getCourseInformationData',
            where: {
                kkxxb_jxbbzid: [[${teachClassGroupForm?.jxbbz_bzid}]],
                kkxxb_kkxq: [[${teachClassGroupForm?.jxbbz_kkxq}]]
            },
            page: true,
            cols: [
                [
                    {type: 'checkbox', field: 'id', fixed: 'left'},
                    {field: 'kkxxb_kkxq', title: '开课学期'},
                    {field: 'kkxxb_kkyxyx', title: '开课系部'},
                    {field: 'kkxxb_kcmc', title: '课程名称'},
                    {field: 'kkxxb_kcxz', title: '课程性质'},
                    {field: 'kkxxb_xbx', title: '选必修'},
                    {field: 'kkxxb_jxbmc', title: '教学班名称'},
                    {field: 'kkxxb_jxbbh', title: '教学班编号'},
                    {
                        field: 'kkxxb_skjsxm', title: '授课教师', templet: function (d) {
                            let teacher = [];
                            for (let i = 0; i < d.kkxxb_skjsxm.length; i++) {
                                teacher.push(d.kkxxb_skjsxm[i].uname);
                            }
                            return teacher.join(', ');
                        }
                    },
                    {field: 'rowInfo', hide: true},
                    {field: 'kkxxb_jxbbzid', hide: true},
                    {field: 'kkxxb_jxbbzmc', hide: true}
                ]
            ],
            done: function (res, curr, count) {
                scrollBox("#popScroll");
                curPageArray = res.data;
                let number = 0;
                $(".opt_data_num").attr("count", res.count);
                if ($(".selectAll .cur").length > 0) {
                    res.data.forEach((item) => {
                        item.LAY_CHECKED = true;
                        const index = item.LAY_TABLE_INDEX;
                        $('.layui-table th input[type="checkbox"]').prop('checked', true).next().addClass('layui-form-checked');
                        $('.layui-table tr[data-index=' + index + '] input[type="checkbox"]').prop('checked', true).next().addClass('layui-form-checked');
                    })
                    return false;
                }
                layui.each(res.data, function (index, item) {
                    const idx = res.data[index]['LAY_TABLE_INDEX'];
                    $('.layui-table tr[data-index=' + idx + '] input[type="checkbox"]').prop('checked', false);
                    $('.layui-table tr[data-index=' + idx + '] input[type="checkbox"]').next().removeClass('layui-form-checked');
                    if (item.kkxxb_jxbbzid && item.kkxxb_jxbbzid.indexOf([[${teachClassGroupForm?.jxbbz_bzid}]]) !== -1) {
                        uniqueArray.push(res.data[index]);
                        number++;
                    }
                    for (let j = 0; j < uniqueArray.length; j++) {
                        //数据id和要勾选的id相同时checkbox选中
                        if (res.data[index].kkxxb_jxbbh === uniqueArray[j].kkxxb_jxbbh) {
                            //这里才是真正的有效勾选
                            res.data[index]["LAY_CHECKED"] = 'true';
                            //找到对应数据改变勾选样式，呈现出选中效果
                            const idx = res.data[index]['LAY_TABLE_INDEX'];
                            $('.layui-table tr[data-index=' + idx + '] input[type="checkbox"]').prop('checked', true);
                            $('.layui-table tr[data-index=' + idx + '] input[type="checkbox"]').next().addClass('layui-form-checked');
                        }
                    }
                })
                $(".opt_data_num em").text($(".opt_data_num em").text() ? $(".opt_data_num em").text() : number);
            },
            text: {
                none: '当前学期在对应筛选条件下无可选数据'  //自定义无数据时的提示文本
            }
        });
        table.on('tool(courseInformationTable)', function (obj) {
            if (obj.event === "add") {
                let arr = [];
                arr.push(obj.data.rowInfo.formUserId);
                bindCourseGroup(arr, $(this));
            }
            if (obj.event === "cancel") {
                clearCourseGroup(obj.data.rowInfo.formUserId, $(this));
            }
        });
        table.on('checkbox(courseInformationTable)', function (obj) {
            check();
            let checkRows = table.checkStatus('courseInformationTable');
            if (obj.checked) {
                uniqueArray.push(...checkRows.data);
                cancelArray = cancelArray.filter(item => !checkRows.data.some(row => row.rowInfo.formUserId === item) || (obj.data.rowInfo && obj.data.rowInfo.formUserId !== item));
            } else {
                if (obj.type === "all") {
                    cancelArray = [...curPageArray.map(item => item.rowInfo.formUserId)];
                    uniqueArray = uniqueArray.filter(element => !curPageArray.some(item => item.kkxxb_jxbbh === element.kkxxb_jxbbh));
                } else {
                    cancelArray.push(obj.data.rowInfo.formUserId);
                    uniqueArray = uniqueArray.filter(data => data.kkxxb_jxbbh !== obj.data.kkxxb_jxbbh);
                }
            }
            uniqueArray = Array.from(new Set(uniqueArray.map(obj => obj.kkxxb_jxbbh))).map(name => {
                return uniqueArray.find(obj => obj.kkxxb_jxbbh === name);
            });
            $(".opt_data_num em").text(uniqueArray.length);
        });
    });

    $(".popSearch_search_btn").click(function () {
        var kkxxb_kkyxyx = $("select[name='kkxxb_kkyxyx']").val();
        let kkxxb_kcmc = $("select[name='kkxxb_kcmc']").val();
        let kkxxb_xbx = $("select[name='kkxxb_xbx']").val();
        let kkxxb_jxbbh = $("select[name='kkxxb_jxbbh']").val();
        let kkxxb_jxbmc = $("select[name='kkxxb_jxbmc']").val();
        var field = {
            kkxxb_kkyxyx: kkxxb_kkyxyx,
            kkxxb_kcmc: kkxxb_kcmc,
            kkxxb_xbx: kkxxb_xbx,
            kkxxb_jxbbh: kkxxb_jxbbh,
            kkxxb_jxbmc: kkxxb_jxbmc
        };
        insTb.reload({where: field, page: {curr: 1}});
    })
    $(".layui-layer-btn0").click(function () {
        let tableData = [];
        for (let i = 0; i < uniqueArray.length; i++) {
            tableData.push(uniqueArray[i].rowInfo.formUserId);
        }
        bindCourseGroup(tableData);
        clearCourseGroup();
    })

    function bindCourseGroup(tableData, _this) {
        if (!check()) {
            return false;
        }
        if (tableData && tableData.length === 0) {
            window.parent.postMessage(JSON.stringify({action: 1}), "*");
            return false;
        }
        if (tableData && tableData.length > 0) {
            let loading = layer.load(1);
            let kkxxb_kkyxyx = $("select[name='kkxxb_kkyxyx']").val();
            let kkxxb_kcmc = $("select[name='kkxxb_kcmc']").val();
            let kkxxb_xbx = $("select[name='kkxxb_xbx']").val();
            let kkxxb_jxbbh = $("select[name='kkxxb_jxbbh']").val();
            let kkxxb_jxbmc = $("select[name='kkxxb_jxbmc']").val();
            const field = {
                kkxxb_kkyxyx: kkxxb_kkyxyx,
                kkxxb_kcmc: kkxxb_kcmc,
                kkxxb_xbx: kkxxb_xbx,
                kkxxb_jxbbh: kkxxb_jxbbh,
                kkxxb_jxbmc: kkxxb_jxbmc
            };
            $.ajax({
                url: '../processData/bindCourseGroup',
                data: {
                    term: [[${teachClassGroupForm?.jxbbz_kkxq}]],
                    fid: fid,
                    formId: formId,
                    formUserId: formUserId,
                    courseId: tableData.join(","),
                    delCourseId: cancelArray.join(","),
                    type: 1,
                    all: $(".selectAll .cur").length,
                    condition: JSON.stringify(field)
                },
                dataType: 'json',
                type: 'get',
                success: function (data) {
                    if (data.status) {
                        setTimeout(function () {
                            layer.close(loading);
                            window.parent.postMessage(JSON.stringify({action: 1}), "*");
                        }, 1000);
                    } else {
                        layer.alert("程序异常！");
                    }
                }
            })
        }
    }

    function clearCourseGroup() {
        if (cancelArray.length > 0) {
            $.ajax({
                url: '../processData/clearCourseGroup',
                data: {fid: fid, courseFormUserId: cancelArray.join(","), formUserId: formUserId},
                dataType: 'json',
                type: 'get',
                success: function (data) {
                    if (data.status) {
                        window.parent.postMessage(JSON.stringify({action: 1}), "*");
                    } else {
                        layer.alert("程序异常！");
                    }
                }
            })
        }
    }

    $("#clearGroup").click(function () {
        layer.confirm('确定清空吗？', {icon: 3}, function () {
            layer.load(1);
            $.ajax({
                url: '/processData/clearAllCourseGroup',
                data: {fid: fid, formUserId: formUserId},
                dataType: 'json',
                type: 'post',
                success: function (data) {
                    if (data.status) {
                        uniqueArray = [];
                        $(".opt_data_num em").text(0);
                        table.reload('courseInformationTable', {
                            kkxxb_jxbbzid: [[${teachClassGroupForm?.jxbbz_bzid}]],
                            kkxxb_kkxq: [[${teachClassGroupForm?.jxbbz_kkxq}]]
                        });
                        layer.closeAll();
                    } else {
                        layer.alert("程序异常！");
                    }
                }
            })
        }, function () {
        });
    })

    function check() {
        if ([[${teachClassGroupForm?.jxbbz_sfazlrcj}]] === "是") {
            let checkData = table.checkStatus('courseInformationTable').data;
            let teachGroupIds = checkData.filter(item => item.kkxxb_jxbbzid).map(item => item.kkxxb_jxbbzid);
            if (teachGroupIds) {
                let count = 0;
                $.ajax({
                    url: '/processData/getTeachGroupDetail',
                    data: {deptId: fid, teachGroupIds: teachGroupIds.join(",")},
                    dataType: 'json',
                    type: 'post',
                    async: false,
                    success: function (data) {
                        if (data.list) {
                            for (let i = 0; i < data.list.length; i++) {
                                let item = data.list[i];
                                if (item.jxbbz_sfazlrcj === "是" && teachGroupIds.indexOf([[${teachClassGroupForm?.jxbbz_bzid}]]) === -1) {
                                    count++;
                                }
                            }
                        }
                    }
                })
                if (count > 0) {
                    layer.alert('一条开课信息不能存在于多个录成绩的编组，请修改开课信息的所在编组或修改当前编组属性后重新添加', {
                        icon: 2,
                        title: '',
                        btn: []
                    })
                    return false;
                }
            }
        }
        return true;
    }

    $(".layui-layer-btn1").click(function () {
        window.parent.postMessage(JSON.stringify({action: 1}), "*");
    })
    $(".popSearch_clear").click(function () {
        location.reload();
    })
    $(".fixed-bottom .selectAll span").click(function () {
        $(this).toggleClass("cur");
        let dataArray = layui.table.cache["courseInformationTable"];
        uniqueArray = dataArray;
        if ($(this).hasClass("cur")) {
            $(".opt_data_num em").text($(".opt_data_num").attr("count"));
            dataArray.forEach((item) => {
                item.LAY_CHECKED = true;
                const index = item.LAY_TABLE_INDEX;
                $('.layui-table th input[type="checkbox"]').prop('checked', true).next().addClass('layui-form-checked');
                $('.layui-table tr[data-index=' + index + '] input[type="checkbox"]').prop('checked', true).next().addClass('layui-form-checked');
            })
        } else {
            $(".opt_data_num em").text(0);
            dataArray.forEach((item) => {
                item.LAY_CHECKED = false;
                const index = item.LAY_TABLE_INDEX;
                $('.layui-table th input[type="checkbox"]').prop('checked', false).next().removeClass('layui-form-checked');
                $('.layui-table tr[data-index=' + index + '] input[type="checkbox"]').prop('checked', false).next().removeClass('layui-form-checked');
            })
        }

    });
</script>
</body>
</html>
