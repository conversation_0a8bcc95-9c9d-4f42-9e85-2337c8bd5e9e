<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no,initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <title>点名页</title>
</head>
<script src="js/responsive.js"></script>
<script src="js/CXJSBridge.js"></script>
<link rel="stylesheet" href="css/global.css">
<link rel="stylesheet" href="css/rollCallPage.css?v=1">
<style>
    .stu-list .stu .stu-con .stu-state {
        justify-content: space-between;

    }

    .stu-class {
        display: flex;
        flex-wrap: wrap;
    }

    .stu-list .stu .stu-con .stu-state {
        white-space: nowrap;
    }

    .stu-list .stu .stu-con .stu-state ul {
        margin-right: 0;
        padding-right: 0;
    }

    .stu-list .stu .stu-con .stu-state ul li {
        width: 0.68rem;
        height: 0.68rem;
        margin-right: 0.32rem;
        line-height: 0.68rem;
    }

    .stu-list .stu .stu-con .stu-mes .stu-class h3 {
        margin-right: 0.16rem;
        line-height: 0.32rem;
        flex-shrink: 0;
    }

    .stu-list .stu .stu-con .stu-mes .stu-class p {
        line-height: 0.32rem;
    }

    .stu-list .stu .stu-con .stu-state ul li:nth-child(5).active {
        background-color: rgba(175, 119, 255, 1);
        color: #fff;
    }

    .stu-list .stu .stu-con .stu-state .evaluate {
        padding-left: 0.22rem;
        position: relative;
    }

    .stu-list .stu .stu-con .stu-state .evaluate:after {
        content: "";
        position: absolute;
        left: 0;
        top: 0.06rem;
        bottom: 0;
        width: 1px;
        height: 0.48rem;
        background-color: #E5E6EB;
    }

    .stu-list .stu.for-leave .stu-con .stu-state .evaluate:after {
        content: "";
        position: absolute;
        left: 0;
        top: 0.06rem;
        bottom: 0;
        width: 1px;
        height: 0.48rem;
        background-color: #F1F3F6;
    }
</style>
<body>
<div class="course-mes">
    <div class="course-item">
        <h1>课 程：</h1>
        <p id="courseNameArea"></p>
    </div>
    <div class="course-item">
        <h1>教学班：</h1>
        <p id="teachingClassNameArea"></p>
    </div>
    <div class="course-item">
        <h1>上课时间：</h1>
        <p><span id="zcArea"></span><span id="xqArea"></span><span id="kjArea"></span></p>
    </div>
</div>
<div class="search-wrap">
    <div class="search flex">
        <img src="images/search.png" alt="">
        <input type="text" id="keywordIp" placeholder="搜索学生姓名或学号">
    </div>
</div>
<div class="stu-list" id="dataBox">
</div>
<div id="btnSure">
    <div class="btn" onclick="addRollcall(false);">保存点名结果</div>
</div>
<div class="dialog-wrap" id="dialogTips">
    <div class="dialog dialog-tips">
        <div class="dialog-con">
            确定将<span class="stu-name">测试学生2</span>记为<span class="stu-state">迟到</span>？
        </div>
        <div class="dialog-btn">
            <div class="btn btn-cancel">取消</div>
            <div class="btn btn-sure">确定</div>
        </div>
    </div>
</div>
<div class="dialog-wrap" id="dialogComment">
    <div class="dialog dialog-comment">
        <div class="dialog-con">
            <h1 class="dialog-title">请输入评价内容</h1>
            <textarea name="" id="" cols="30" rows="10" placeholder="请输入"></textarea>
            <!-- <p id="errorTips">请输入评价内容</p> -->
        </div>
        <div class="dialog-btn">
            <div class="btn btn-cancel">取消</div>
            <div class="btn btn-sure">确定</div>
        </div>
    </div>
</div>
</body>
<script src="js/zepto.min.js"></script>
<script th:inline="javascript">
    var isForm = [[${isForm}]];
    var teachingClassCode = [[${teachingClassCode}]];
    var teachingClassName = [[${teachingClassName}]];
    var teacherUid = [[${teacherUid}]];
    var teacherName = [[${teacherName}]];
    var courseName = [[${courseName}]];
    var classDate = [[${classDate}]];
    var today = [[${today}]];
    var bldm = [[${bldm}]];
    var qjUid = [[${qjUid}]];

    var rollcallStateList = [[${rollcallStateList}]];
    var storage = window.localStorage;
    if (bldm == '2' && today == '1') {
        alert("不允许对过去时间的课程补录点名");
    }
    if (today == '2') {
        alert("不允许对未来时间的课程补录点名");
    }
    var zc = [[${zc}]];
    var kj = [[${kj}]];
    var xq = [[${xq}]];
    var storageKey = teachingClassCode + "_" + classDate + "_" + kj + "_";
    var array = [];
    var json = {};
    $("#teachingClassNameArea").text(teachingClassName);
    $("#courseNameArea").text(courseName);
    $("#zcArea").text("第" + zc + "周");
    $("#xqArea").text("周" + xq);
    $("#kjArea").text("第" + kj + "节");
    // $("#keywordIp").keyup(function (e) {
    // if (e.keyCode == 13) {
    //     getList();
    // }
    // })
    var search = false;
    $('#keywordIp').on({
        'compositionstart': function () {
            search = true;
        },
        'compositionend': function () {
            search = false;
            if (!search) {
                getList();
            }
        },
        'input propertychange': function () {
            if (!search) {
                getList();
            }
        }
    });
    var menuArr = [{menu: '学生管理', option: 'stuManage()'}, {menu: '点名记录', option: 'record()'}];
    function _jsBridgeReady() {
        // 图标地址 为临时测试地址，上线后替换为正式线上地址，图标在images文件夹中
        // 点击确定
        $("#btnSure .btn").click(function () {
            jsBridge.postNotification('CLIENT_DISPLAY_MESSAGE', {'message': '已保存点名结果', 'gravity': 1})
            setTimeout(function () {
                jsBridge.postNotification('CLIENT_EXIT_LEVEL', {"level": "1"});
            }, 1500)
        })
    }

    // 学生管理
    function stuManage() {
        var webUrl = window.location.origin + "/elective/rollcall/studengtsListIndex?teachingClassCode=" + teachingClassCode;
        jsBridge.postNotification('CLIENT_OPEN_URL', {"toolbarType": 2, "webUrl": webUrl, "loadType": 1});
    }

    // 点名记录
    function record() {
        var webUrl = window.location.origin + "/elective/rollcall/rollcallLogIndex?teachingClassCode=" + teachingClassCode + "&courseName=" + courseName + "&teachingClassName=" + teachingClassName;
        jsBridge.postNotification('CLIENT_OPEN_URL', {"toolbarType": 2, "webUrl": webUrl, "loadType": 1});
    }

    $(document).ready(function () {
        var stuH = $(window).height() - $(".stu-list").offset().top;
        $(".stu-list").css("height", stuH + "px");
        // 选择学生状态
        var $stuEle = "";
        $(".stu-list").on('click', '.stu .stu-state li:not(.active)', function () {
            if (bldm == '2' && today != '0') {
                return false;
            }
            if (today == '2') {
                return false;
            }
            var uid = $(this).parents(".stu-con").attr("uuuid");
            $.each(array, function (index, item) {
                if (item != undefined && item.uid == uid) {
                    json = item;
                }
            });
            if (json.uid == undefined || json.uid != uid) {
                json = {};
                json.uid = uid;
            }
            var stuState = $(this).attr('data-mes');
            var thisstateid = $(this).attr("stateid");
            if (stuState != '迟到' && stuState != '早退') {
                if (json.kk == "1" || json.cd == "1" || json.zt == "1" || json.qj == "1" || (json.state != undefined && json.state != '0')) {
                    jsBridge.postNotification('CLIENT_DISPLAY_MESSAGE', {'message': '该状态不能与其他状态同时存在', 'gravity': '1'});
                    return false;
                }
            } else {
                if (json.kk == "1" ||  json.qj == "1" || (json.state != undefined && json.state != '0')) {
                    jsBridge.postNotification('CLIENT_DISPLAY_MESSAGE', {'message': '该状态不能与其他状态同时存在', 'gravity': '1'});
                    return false;
                }
            }
            // if (json.state !=undefined && thisstateid !=undefined && json.state!='0' && json.state != thisstateid) {
            //     layer.msg('自定义状态与其他状态不能同时存在')
            //     return false;
            // }
            // if (((json.kk == "1" || json.zt == "1" || json.cd == "1" || (json.state != undefined && json.state != "0")) && stuState == '请假') || (json.qj == "1" && stuState != '请假')) {
            //     jsBridge.postNotification('CLIENT_DISPLAY_MESSAGE', {'message': '其他状态和请假状态不能同时存在', 'gravity': '1'});
            //     return false;
            // }
            // if (((json.qj == "1" || json.zt == "1" || json.cd == "1" || (json.state != undefined && json.state != "0")) && stuState == '旷课') || (json.kk == "1" && stuState != '旷课')) {
            //     jsBridge.postNotification('CLIENT_DISPLAY_MESSAGE', {'message': '其他状态和旷课状态不能同时存在', 'gravity': '1'});
            //     return false;
            // }
            $stuEle = $(this);
            var stuName = $(this).parents('.stu-con').find('h3').text();
            var tipCon = $("#dialogTips .dialog-con");
            tipCon.find('.stu-name').text(stuName);
            tipCon.find('.stu-state').text(stuState);
            $("#dialogTips").show();
        })
        // 删除选择状态
        $(".stu-list").on('click', '.stu .stu-state li.active .cancel', function () {
            if (bldm == '2' && today != '0') {
                return false;
            }
            if (today == '2') {
                return false;
            }
            var statusStr = $(this).parent().attr("data-mes");
            var thisstateid = $(this).parent().attr("stateid");
            var uid = $(this).parents(".stu-con").attr("uuuid");
            $.each(array, function (index, item) {
                if (item != undefined && item.uid == uid) {
                    json = item;
                }
            });
            if (statusStr == '迟到') {
                json.cd = "0";
            }else if (statusStr == '旷课') {
                json.kk = "0";
            }else if (statusStr == '早退') {
                json.zt = "0";
            }else if (statusStr == '请假') {
                json.qj = "0";
            }else {
                json.state = "0";
            }
            delArray(json.uid);
            array.push(json);
            if (storage) {
                storage.setItem(storageKey + json.uid, JSON.stringify(json));
            }
            $(this).parent().removeClass('active');
        })
        // 点击确定
        $("#dialogTips .btn-sure").click(function () {
            var statusStr = $stuEle.attr("data-mes");
            var thisstateid = $stuEle.attr("stateid");
            if (statusStr == '迟到') {
                json.cd = "1";
            }else if (statusStr == '旷课') {
                json.kk = "1";
            }else if (statusStr == '早退') {
                json.zt = "1";
            }else if (statusStr == '请假') {
                json.qj = "1";
            }else {
                json.state = thisstateid;
            }
            delArray(json.uid);
            array.push(json);
            if (storage) {
                storage.setItem(storageKey + json.uid, JSON.stringify(json));
            }
            $stuEle.addClass('active');
            $stuEle.find('.cancel').show();
            $("#dialogTips").hide();
        })
        // 点击取消
        $(".dialog .btn-cancel").click(function () {
            $(".dialog-wrap").hide();
        })
        // 评价
        var $commentEle = "";
        $(".stu-list").on('click', ".stu .evaluatedNot", function () {
            if (bldm == '2' && today != '0') {
                return false;
            }
            if (today == '2') {
                return false;
            }
            var uid = $(this).parents(".stu-con").attr("uuuid");
            $.each(array, function (index, item) {
                if (item != undefined && item.uid == uid) {
                    json = item;
                }
            });
            if (json.uid == undefined || json.uid != uid) {
                json = {};
                json.uid = uid;
            }
            $commentEle = $(this).parents('.stu-con');
            var next = $(this).parent().next();
            if (next.length > 0) {
                var val = next.find('span').text();
                $("#dialogComment textarea").val(val);
            } else {
                $("#dialogComment textarea").val('');
            }
            $("#errorTips").hide();
            $("#dialogComment").show();
        })
        // 评价确定
        $("#dialogComment .btn-sure").click(function () {
            var val = $("#dialogComment textarea").val();
            if ($("#dialogComment textarea").val() != "") {
                $commentEle.find('.evaluatedNot').text('已评价').addClass('reviewed');
                var comment = $commentEle.find('.stu-comment');
                if (comment.length > 0) {
                    comment.find('span').text(val)
                } else {
                    $commentEle.append('<div class="stu-comment">评价：<span>' + val + '</span></div>');
                }
            } else {
                $commentEle.find('.evaluatedNot').text('待评价').removeClass('reviewed');
                $commentEle.find('.stu-comment').remove();
            }
            json.val = val;
            delArray(json.uid);
            array.push(json);
            if (storage) {
                storage.setItem(storageKey + json.uid, JSON.stringify(json));
            }
            $("#dialogComment").hide();
        })
    })
    getList();

    function getList() {
        var keyword = $("#keywordIp").val();
        $.ajax({
            type: "POST",
            url: "/elective/rollcall/getStudents",
            data: {teachingClassCode: teachingClassCode, keyword: keyword, zc: zc, xq: xq, kj: kj},
            dataType: 'json',
            async: false,
            success: function (data) {
                var html = "";
                html += "<div class=\"intro\">";
                html += "说明：默认状态为正常,点击修改状态为迟到、旷课、请假、早退";
                html += "</div>";
                if (data.status) {
                    if (data.dataFlag == "vo") {
                        jsBridge.postNotification('CLIENT_CUSTOM_MENU', {
                            show: 1,
                            icon: 'https://app.jichu.chaoxing.com/jw/userTest/memu.png',
                            option: 'b()',
                            children: menuArr
                        });
                        for (var i = 0; i < data.list.length; i++) {
                            if (data.list[i].sjly == "0") {
                                var thisJson = {};
                                if (storage) {
                                    let item = storage.getItem(storageKey + data.list[i].jxbxsb_xslxr.puid);
                                    if (item) {
                                        thisJson = JSON.parse(item);
                                    }
                                }
                                var qj = false;
                                if (qjUid.indexOf(data.list[i].jxbxsb_xslxr.puid) != -1) {
                                    html += "<div class=\"stu for-leave\">";
                                    qj = true;
                                } else {
                                    html += "<div class=\"stu\">";
                                }
                                html += "<div class=\"stu-con\" uuuid=\"" + data.list[i].jxbxsb_xslxr.puid + "\" uuzzbmc=\"" + data.list[i].jxbxsb_zzbmc + "\" uuzzbbh=\"" + data.list[i].jxbxsb_zzbbh + "\" uuzy=\"" + data.list[i].jxbxsb_zy + "\">";
                                html += "<div class=\"stu-mes\">";
                                html += "<div class=\"stu-class\">";
                                html += "<h3>" +(parseInt(i) + 1)+"、"+ data.list[i].jxbxsb_xsxm + "</h3>";
                                html += "<p>" + data.list[i].jxbxsb_zzbmc + "</p>";
                                html += "</div></div>";
                                html += "<div class=\"stu-state\"><ul>";


                                for (let j = 0; j < rollcallStateList.length; j++) {
                                    if ((rollcallStateList[j].type == 0 && rollcallStateList[j].stateAlias == 'cd' && thisJson.cd == 1) ||
                                        (rollcallStateList[j].type == 0 && rollcallStateList[j].stateAlias == 'kk' && thisJson.kk == 1) ||
                                        (rollcallStateList[j].type == 0 && rollcallStateList[j].stateAlias == 'zt' && thisJson.zt == 1) ||
                                        (rollcallStateList[j].type == 0 && rollcallStateList[j].stateAlias == 'qj' && thisJson.qj == 1) ||
                                        (rollcallStateList[j].type == 0 && rollcallStateList[j].stateAlias == 'qj' && qj) ||
                                        (rollcallStateList[j].type == 1 && rollcallStateList[j].id == thisJson.state)) {
                                        html += "<li stateid = '"+rollcallStateList[j].id+"' data-mes='"+rollcallStateList[j].stateNamePc+"' class=\"active\">"+rollcallStateList[j].stateNameMobile+" <span class=\"cancel\"></span></li>";
                                    }else {
                                        html += "<li stateid = '"+rollcallStateList[j].id+"' data-mes='"+rollcallStateList[j].stateNamePc+"'>"+rollcallStateList[j].stateNameMobile+" <span class=\"cancel\"></span></li>";
                                    }
                                }

                                html += "</ul>";
                                if (thisJson.val && thisJson.val != '') {
                                    html += "<div class=\"evaluate evaluatedNot reviewed\">已评价</div>";
                                } else {
                                    html += "<div class=\"evaluate evaluatedNot\">待评价</div>";
                                }
                                html += "</div>";
                                if (thisJson.val) {
                                    html += "<div class=\"stu-comment\">评价：<span>" + thisJson.val + "</span></div>";
                                }
                                html += "</div></div>";
                                if (thisJson.uid) {
                                    delArray(thisJson.uid);
                                    thisJson.uid = data.list[i].jxbxsb_xslxr.puid;
                                    thisJson.code = data.list[i].jxbxsb_xsxh;
                                    thisJson.xsid = data.list[i].xsdataid;
                                    thisJson.realname = data.list[i].jxbxsb_xsxm;
                                    thisJson.zzbmc = data.list[i].jxbxsb_zzbmc;
                                    thisJson.zy = data.list[i].jxbxsb_zy;
                                    thisJson.classCode = data.list[i].jxbxsb_zzbbh;
                                    if (qj) {
                                        thisJson.qj = "1";
                                    }
                                    array.push(thisJson);
                                } else {
                                    var addJson = {};
                                    addJson.uid = data.list[i].jxbxsb_xslxr.puid;
                                    addJson.code = data.list[i].jxbxsb_xsxh;
                                    addJson.xsid = data.list[i].xsdataid;
                                    addJson.realname = data.list[i].jxbxsb_xsxm;
                                    addJson.zzbmc = data.list[i].jxbxsb_zzbmc;
                                    addJson.zy = data.list[i].jxbxsb_zy;
                                    addJson.classCode = data.list[i].jxbxsb_zzbbh;
                                    if (qj) {
                                        addJson.qj = "1";
                                    }
                                    delArray(addJson.uid);
                                    array.push(addJson);
                                }
                            }
                        }
                    }
                    if (data.dataFlag == "po") {
                        menuArr.push({menu: '更新学生名单', option: 'updateStudent()'});
                        jsBridge.postNotification('CLIENT_CUSTOM_MENU', {
                            show: 1,
                            icon: 'https://app.jichu.chaoxing.com/jw/userTest/memu.png',
                            option: 'b()',
                            children: menuArr
                        });
                        for (var i = 0; i < data.list.length; i++) {
                            var code = data.list[i].studentCode;
                            var qj = false;
                            if (qjUid.indexOf(data.list[i].studentUid) != -1) {
                                html += "<div class=\"stu for-leave\">";
                                qj = true;
                            } else {
                                html += "<div class=\"stu\">";
                            }
                            html += "<div class=\"stu-con\" uuuid=\"" + data.list[i].studentUid + "\">";
                            html += "<div class=\"stu-mes\">";
                            html += "<div class=\"stu-class\">";
                            html += "<h3>" +(parseInt(i) + 1)+"、"+ data.list[i].studentRealname + "</h3>";
                            html += "<p>" + data.list[i].administrationClassName + "</p>";
                            html += "</div></div>";
                            html += "<div class=\"stu-state\"><ul>";

                            for (let j = 0; j < rollcallStateList.length; j++) {
                                if ((rollcallStateList[j].type == 0 && rollcallStateList[j].stateAlias == 'cd' && data.list[i].cd == 1) ||
                                    (rollcallStateList[j].type == 0 && rollcallStateList[j].stateAlias == 'kk' && data.list[i].kk == 1) ||
                                    (rollcallStateList[j].type == 0 && rollcallStateList[j].stateAlias == 'zt' && data.list[i].zt == 1) ||
                                    (rollcallStateList[j].type == 0 && rollcallStateList[j].stateAlias == 'qj' && data.list[i].qj == 1) ||
                                    (rollcallStateList[j].type == 0 && rollcallStateList[j].stateAlias == 'qj' && qj) ||
                                    (rollcallStateList[j].type == 1 && rollcallStateList[j].id == data.list[i].state)) {
                                    html += "<li stateid = '"+rollcallStateList[j].id+"' data-mes='"+rollcallStateList[j].stateNamePc+"' class=\"active\">"+rollcallStateList[j].stateNameMobile+" <span class=\"cancel\"></span></li>";
                                }else {
                                    html += "<li stateid = '"+rollcallStateList[j].id+"' data-mes='"+rollcallStateList[j].stateNamePc+"'>"+rollcallStateList[j].stateNameMobile+" <span class=\"cancel\"></span></li>";
                                }
                            }

                            html += "</ul>";


                            if (data.list[i].val != undefined && data.list[i].val != '') {
                                html += "<div class=\"evaluate evaluatedNot reviewed\">已评价</div>";
                            } else {
                                html += "<div class=\"evaluate evaluatedNot\">待评价</div>";
                            }


                            html += "</div>";

                            if (data.list[i].val != undefined && data.list[i].val != '') {
                                html += "<div class=\"stu-comment\">评价：<span>" + data.list[i].val + "</span></div>";
                            }
                            html += "</div></div>";

                            var addJson = {};
                            addJson.uid = data.list[i].studentUid;
                            addJson.code = data.list[i].studentCodeDecrypt;
                            addJson.xsid = data.list[i].xsid;
                            addJson.cd = data.list[i].cd == undefined ? 0 : data.list[i].cd;
                            addJson.kk = data.list[i].kk == undefined ? 0 : data.list[i].kk;
                            addJson.zt = data.list[i].zt == undefined ? 0 : data.list[i].zt;
                            addJson.qj = data.list[i].qj == undefined ? 0 : data.list[i].qj;
                            addJson.state = data.list[i].state == undefined ? 0 : data.list[i].state;
                            addJson.val = data.list[i].val == undefined ? 0 : data.list[i].val;

                            addJson.realname = data.list[i].studentRealname;
                            addJson.zzbmc = data.list[i].administrationClassName;
                            addJson.zy = data.list[i].zy;
                            addJson.classCode = data.list[i].administrationClassCode;
                            delArray(addJson.uid);
                            array.push(addJson);
                        }
                    }
                }
                $("#dataBox").html(html);
            }
        });
    }
    var add = true;
    function addRollcall(onlyAdd) {
        if (add){
            add = false;
        }else {
            jsBridge.postNotification('CLIENT_DISPLAY_MESSAGE', {'message': '正在保存！', 'gravity': '1'});
            return false;
        }
        if (today == '2') {
            add = true;
            return false;
        }
        if (bldm == '2' && today != '0') {
            add = true;
            return false;
        }
        $.ajax({
            type: "POST",
            url: "/elective/rollcall/addRollcall",
            data: {
                array: JSON.stringify(array),
                teachingClassCode: teachingClassCode,
                teachingClassName: teachingClassName,
                zc: zc,
                xq: xq,
                kj: kj,
                onlyAdd: onlyAdd,
                classDate: classDate,
                courseName: courseName,
                teacherUid: teacherUid,
                teacherName: teacherName
            },
            dataType: 'json',
            success: function (data) {
                add = true;
                if (data.status) {
                    jsBridge.postNotification('CLIENT_DISPLAY_MESSAGE', {'message': '已保存点名结果！', 'gravity': '1'});
                    if (isForm){
                        var wdm_chidao = 0;
                        var wdm_qingjia = 0;
                        var wdm_kuangke = 0;
                        var wdm_zaotui = 0;
                        var xkjl_sdxssl = 0;
                        for (let ob of array) {
                            xkjl_sdxssl++;
                            if (ob.zt && ob.zt =='1'){
                                wdm_zaotui++;
                            }
                            if (ob.kk && ob.kk =='1'){
                                wdm_kuangke++;
                            }
                            if (ob.qj && ob.qj =='1'){
                                wdm_qingjia++;
                            }
                            if (ob.cd && ob.cd =='1'){
                                wdm_chidao++;
                            }
                        }
                        ;
                        jsBridge.postNotification('CLIENT_WEB_EXTRAINFO',
                            {
                                "typeflag": "CXWEB_FORM_BUTTON_PUSH",
                                "info": [
                                    {
                                        "type": "setFieldValue",
                                        'data': [
                                            {
                                                'alias': 'wdm_chidao',
                                                'val': [
                                                    ''+wdm_chidao
                                                ],
                                                'compt': 'numberInput'
                                            }, {
                                                'alias': 'wdm_qingjia',
                                                'val': [
                                                    ''+wdm_qingjia
                                                ],
                                                'compt': 'numberInput'
                                            },
                                            {
                                                'alias': 'wdm_kuangke',
                                                'val': [
                                                    ''+wdm_kuangke
                                                ],
                                                'compt': 'numberInput'
                                            },
                                            {
                                                'alias': 'wdm_zaotui',
                                                'val': [
                                                    ''+wdm_zaotui
                                                ],
                                                'compt': 'numberInput'
                                            },
                                            {
                                                'alias': 'xkjl_sdxssl',
                                                'val': [
                                                    ''+(xkjl_sdxssl - wdm_qingjia -wdm_kuangke)
                                                ],
                                                'compt': 'numberInput'
                                            }
                                        ]
                                    }
                                ]
                            }
                        )
                        jsBridge.postNotification('CLIENT_EXIT_LEVEL', { "level": "1"});
                        return;
                    }
                } else {
                    jsBridge.postNotification('CLIENT_DISPLAY_MESSAGE', {'message': data.msg, 'gravity': '1'});

                }
            }
        });
    }

    var update = true;
    function updateStudent(){
        if (update){
            update = false;
        }else {
            jsBridge.postNotification('CLIENT_DISPLAY_MESSAGE', {'message': '正在保存！', 'gravity': '1'});
            return false;
        }
        $.ajax({
            type: "POST",
            url: "/elective/rollcall/updateRollcallData",
            data: {
                teachingClassCode: teachingClassCode,
                teachingClassName: teachingClassName,
                zc: zc,
                xq: xq,
                kj: kj,
                classDate: classDate,
                courseName: courseName,
                teacherUid: teacherUid,
                teacherName: teacherName
            },
            dataType: 'json',
            success: function (data) {
                update = true;
                if (!data.status) {
                    jsBridge.postNotification('CLIENT_DISPLAY_MESSAGE', {'message': data.msg, 'gravity': '1'});
                    return;
                }
                jsBridge.postNotification('CLIENT_DISPLAY_MESSAGE', {'message': '更新成功!', 'gravity': '1'});

                setTimeout(function () {
                    window.location.reload();
                }, 1500)
            }
        })
    }

    function delArray(uid) {
        $.each(array, function (index, item) {
            if (item != undefined && item.uid == uid) {
                array.splice(index, 1);
            }
        });
    }

    function getRollcall(uid) {
        var d;
        $.ajax({
            type: "POST",
            url: "/elective/rollcall/getRollcall",
            data: {teachingClassCode: teachingClassCode, zc: zc, xq: xq, kj: kj, uid: uid},
            dataType: 'json',
            async: false,
            success: function (data) {
                d = data;
            }
        });
        return d;
    }
</script>
</html>