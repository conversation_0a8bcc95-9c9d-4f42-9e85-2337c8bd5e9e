<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学生范围</title>
    <th:block th:include="common :: header('学生范围')"/>
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/global.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/layui/css/layui.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/reset.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/selCourseSet.css?v=1'">
</head>
<body>
	<input type="hidden" id="addStuIds">
	<input type="hidden" id="addBjIds">
	<input type="hidden" id="delids">
	<input type="hidden" id="delBjbh">
	<input type="hidden" id="delYxbh">
	<input type="hidden" id="addAllStuNotIn">
	<input type="hidden" id="addAllBjNotIn">
    <div class="z-main">
        <div class="z-title">
            <h3>学生范围</h3>
            <div id="saveBth" style="display:none;"> 保存设置</div>
        </div>
        <div class="z-box" id="z-box">
            <!-- 学生范围 -->
            <div class="box-con box-common" style="display: block;">
                <div class="z-search">
                    <form action="" class="layui-form" onsubmit="return false">
                    	<div class="layui-form-item">
                            <label class="layui-form-label">所在校区 </label>
                            <div class="layui-input-block">
                                <div class="j-search-con multiple-box">
                                    <input type="text" name="teacherName" placeholder="请选择" readonly=""
                                        class="schoolSel" id="selStuXqIp2">
                                    <span class="j-arrow"></span>
                                    <div class="j-select-year">
                                        <div class="search">
                                            <input type="text" placeholder="搜索">
                                            <span></span>
                                        </div>
                                        <ul name="teacherName" id="xqDiv3">
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">所属年级</label>
                            <div class="layui-input-block">
                                <div class="j-search-con multiple-box">
                                    <input type="text" name="teacherName" placeholder="请选择" readonly=""
                                        class="schoolSel" id="selStuNjIp2">
                                    <span class="j-arrow"></span>
                                    <div class="j-select-year">
                                        <div class="search">
                                            <input type="text" placeholder="搜索">
                                            <span></span>
                                        </div>
                                        <ul name="teacherName" id="njDiv3">
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">所属院系 </label>
                            <div class="layui-input-block">
                                <div class="j-search-con multiple-box">
                                    <input type="text" name="teacherName" placeholder="请选择" readonly=""
                                        class="schoolSel" id="selStuYxIp2">
                                    <span class="j-arrow"></span>
                                    <div class="j-select-year">
                                        <div class="search">
                                            <input type="text" placeholder="搜索">
                                            <span></span>
                                        </div>
                                        <ul name="teacherName" id="yxDiv3">
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">所属专业 </label>
                            <div class="layui-input-block">
                                <div class="j-search-con multiple-box">
                                    <input type="text" name="teacherName" placeholder="请选择" readonly=""
                                        class="schoolSel" id="selStuZyIp2">
                                    <span class="j-arrow"></span>
                                    <div class="j-select-year">
                                        <div class="search">
                                            <input type="text" placeholder="搜索">
                                            <span></span>
                                        </div>
                                        <ul name="teacherName" id="zyDiv3">
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label"> 所在班级 </label>
                            <div class="layui-input-block">
                                <div class="j-search-con multiple-box">
                                    <input type="text" name="teacherName" placeholder="请选择" readonly=""
                                        class="schoolSel" id="selStuBjIp2">
                                    <span class="j-arrow"></span>
                                    <div class="j-select-year">
                                        <div class="search">
                                            <input type="text" placeholder="搜索">
                                            <span></span>
                                        </div>
                                        <ul name="teacherName" id="bjDiv3">
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button class="z-btn" id="z-btn1">查询</button>
                        <div class="clear"></div>

                    </form>
                </div>
                <div class="z-btns">
                    <div class="addRule" id="addStu"><img th:src="${_CPR_}+'/elective/images/add-icon.png'">添加选课学生</div>
                    <div class="importStu">导入选课学生</div>
                    <div class="quotas" id="quotas">分配选课名额</div>
                    <div class="quotas" id="quotas1">分配选课名额</div>
                    <div class="del">删除</div>
                    <div style="margin-left:20px;display:none;" id="addFlag"><span id="addFlagSpan">正在添加数据中...</span></div>
                </div>
                <div class="z-tab-search">
                    <ul>
                        <li class="active">按学生显示</li>
                        <li>按班级显示</li>
                        <li>按院系显示</li>
                    </ul>
                    <input type="text" placeholder="请输入" id="selStuXmIp2">
                    <img th:src="${_CPR_}+'/elective/images/search-icon.png'" alt="">
                </div>
                <div class="z-table z-table-range">
                    <div class="z-range">
                        <table class="layui-hide materialTable" id="materialTable" lay-filter="materialTable">
                        </table>
                    </div>
                    <div class="z-range" style="display: none;">
                        <table class="layui-hide materialTable1" id="materialTable1" lay-filter="materialTable1">
                        </table>
                    </div>
                    <div class="z-range" style="display: none;">
                        <table class="layui-hide materialTable2" id="materialTable2" lay-filter="materialTable2">
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 添加选课学生 -->
    <div id="selStu" class="dialog" style="display: none;">
        <div class="dialog-title">
            <h3>可选学生</h3><span class="pu-close"></span>
        </div>
        <div class="dialog-con">
            <div class="z-relation">
                <h3>学生课程关系</h3>
                <div class="tips layui-tips" data-tip="面向关系即所选学生可选此课程，其他学生不可选此课程；限制关系即所选学生不可选此课程，其他学生可选此课程"></div>
                <ul>
                    <li class="active">面向</li>
                    <li>限制</li>
                </ul>
            </div>
            <div class="z-search">
                <form action="" class="layui-form" onsubmit="return false">
                	<div class="layui-form-item">
                        <label class="layui-form-label">所在校区 </label>
                        <div class="layui-input-block">
                            <div class="j-search-con multiple-box">
                                <input type="text" name="teacherName" placeholder="请选择" readonly="" class="schoolSel" id="selStuXqIp">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <ul name="teacherName" id="xqDiv4">
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">所属年级</label>
                        <div class="layui-input-block">
                            <div class="j-search-con multiple-box">
                                <input type="text" name="teacherName" placeholder="请选择" readonly="" class="schoolSel" id="selStuNjIp">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <ul name="teacherName" id="njDiv4">
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">所属院系 </label>
                        <div class="layui-input-block">
                            <div class="j-search-con multiple-box">
                                <input type="text" name="teacherName" placeholder="请选择" readonly="" class="schoolSel" id="selStuYxIp">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <ul name="teacherName" id="yxDiv4">
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">所属专业 </label>
                        <div class="layui-input-block">
                            <div class="j-search-con multiple-box">
                                <input type="text" name="teacherName" placeholder="请选择" readonly="" class="schoolSel" id="selStuZyIp">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <ul name="teacherName" id="zyDiv4">
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"> 所属班级 </label>
                        <div class="layui-input-block">
                            <div class="j-search-con multiple-box">
                                <input type="text" name="teacherName" placeholder="请选择" readonly="" class="schoolSel" id="selStuBjIp">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <ul name="teacherName" id="bjDiv4">
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <button class="z-btn" id="z-btn2">查询</button>
                    <div class="clear"></div>
                </form>
            </div>
            <div class="z-tab-search">
                <ul>
                    <li class="active">按学生显示</li>
                    <li>按班级显示</li>
                </ul>
                <input type="text" placeholder="请输入" id="selStuXmIp">
                <img th:src="${_CPR_}+'/elective/images/search-icon.png'" alt="">
            </div>
            <div class="z-table z-table-sel">
                <div class="tabBox table1">
                    <table class="layui-hide mtTable2" id="mtTable2" lay-filter="mtTable2">
                    </table>
                    <div class="z-check">
                        <span class="check" id="checkAll"></span>选择全部数据
                    </div>
                </div>
                <div class="tabBox table2" style="display: none;">
                    <table class="layui-hide mtTable3" id="mtTable3" lay-filter="mtTable3">
                    </table>
                    <div class="z-check">
                        <span class="check" id="checkAllClass"></span>选择全部数据
                    </div>
                </div>
            </div>
        </div>
        <div class="dialog-btn"><button class="pu-cancel">取消</button><button class="pu-sure" id="stuSure">确定</button>
        </div>
    </div>
    <!-- 导入选课学生 -->
    <div id="importStu" class="dialog" style="display: none;">
        <div class="dialog-title">
            <h3>导入选课学生</h3><span class="pu-close"></span>
        </div>
        <div class="dialog-con">
            <div class="tips" style="margin: 20px 0; ">请下载导入模板，按格式修改后导入 <a href="/templateFile/importStu.xls">下载导入模板</a> <span style="margin-left:30px;" id="failButton"></span><span id="failButton2" style="cursor:pointer;color: #4d88ff;margin-left:15px;" onclick="downFails();">下载失败数据</span></div>
            <div class="layui-upload-drag" style="display: block;" id="ID-upload-demo-drag">
                <img th:src="${_CPR_}+'/elective/images/add-icon1.png'" alt="">
                <div class="intro">点击或拖拽文件到此处上传</div>
                <div class="intro1">只能上传EXCEL文件</div>
                <div class="layui-hide" id="uploadExcel">
                    <img th:src="${_CPR_}+'/elective/images/excel.png'"><span></span>
                </div>
            </div>
        </div>
        <div class="dialog-btn"><button class="pu-cancel">取消</button><button class="pu-sure" id="stuSure1">确定</button>
        </div>
    </div>
    <!-- 分科选课名额 -->
    <div id="stuQuotas" class="dialog">
        <div class="dialog-title">
            <h3>分配选课名额</h3><span class="pu-close"></span>
        </div>
        <div class="intro">说明：请输入数字为班级分配选课名额，没有输入的班级自动分配剩余名额。</div>
        <div class="dialog-con">

            <table class="layui-hide mtTable4" id="mtTable4" lay-filter="mtTable4">
            </table>
        </div>
        <div class="dialog-btn">
            <button class="pu-cancel">取消</button>
            <button class="pu-sure" id="stuSure2">确定</button>
        </div>
    </div>

    <!-- 院系选课名额 -->
    <div id="deptQuotas" class="dialog">
        <div class="dialog-title">
            <h3>分配选课名额</h3><span class="pu-close"></span>
        </div>
        <div class="intro">说明：请输入数字为院系分配选课名额，没有输入的院系自动分配剩余名额。</div>
        <div class="dialog-con">

            <table class="layui-hide mtTable5" id="mtTable5" lay-filter="mtTable5">
            </table>
        </div>
        <div class="dialog-btn">
            <button class="pu-cancel">取消</button>
            <button class="pu-sure" id="stuSure3">确定</button>
        </div>
    </div>
</body>
<script type="text/html" id="tmplToolBar">
    <div class="oprate-table">
        <span class="edit" lay-event="check">查看</span>
        <span class="edit" lay-event="edit">编辑</span>
        <span class="delete" lay-event="delete">删除</span>
    </div>
</script>
<script th:src="${_CPR_}+'/elective/js/jquery-3.3.1.min.js'"></script>
<script th:src="${_CPR_}+'/elective/js/common.js'"></script>
<script th:src="${_CPR_}+'/elective/layui/layui.js'"></script>
<script type="text/javascript">
	var array = [];
	var deptArray = [];
	var formId = [[${formId}]];
	var formUserId = '[[${formUserId}]]';
	var batchOperateFormUserIds = '[[${batchOperateFormUserIds}]]';
	var fid = [[${fid}]];
	var exitOptinal = '[[${exitOptinal}]]';
	var canBatchOperate = [[${canBatchOperate}]];
    let stuSource = new Map();
    let bjSource = new Map();
    // var canUpdateOperate = [[${canUpdateOperate}]];
	function downFails(){
		window.location = "/elective/studentRange/downFail?formUserId="+formUserId+"&batchOperateFormUserIds="+batchOperateFormUserIds;
	}
	if(!canBatchOperate){
        alert("学生范围不一致，无法批量编辑");
        $("#selStuXmIp2").attr("readonly",true);
	}
	if(canBatchOperate){
		$.ajax({
		   	type: "POST",
		     	url: "/elective/getNj",
		      	data:{fid:fid},
		    	dataType: 'json',
		     	success: function (result) {
		       	var html = "";
		         	if (result) {
		           	for(var i = 0;i<result.data.length;i++){
		           		var name = result.data[i].name;
		           	 	var id = result.data[i].id;
		           	 	html += "<li data-id=\""+id+"\">"+name+"</li>";
		           	}
		        	}
		           $("#njDiv3").html(html);
		           $("#njDiv4").html(html);
		   	}
		});
		$.ajax({
		   	type: "POST",
		   	url: "/elective/getYX",
		   	data:{fid:fid},
		   	dataType: 'json',
		   	success: function (result) {
		       	var html = "";
		       	if (result) {
		           	for(var i = 0;i<result.data.length;i++){
		           		var name = result.data[i].name;
		           		var id = result.data[i].id;
		           		html += "<li data-id=\""+id+"\">"+name+"</li>";
		           	}
		         	}
		           $("#yxDiv3").html(html);
		           $("#yxDiv4").html(html);
		  		}
		});
		$.ajax({
	    	type: "POST",
	    	url: "/elective/getXQ",
	    	data:{fid:fid},
	    	dataType: 'json',
	    	success: function (result) {
	          	var html = "";
	        	if (result) {
	            	for(var i = 0;i<result.data.length;i++){
	            		var name = result.data[i].name;
	            		var id = result.data[i].id;
	            		html += "<li data-id=\""+id+"\">"+name+"</li>";
	            	}
	         	}
	            $("#xqDiv3").html(html);
	            $("#xqDiv4").html(html);
	     	}
		});
	}
	function getZY(yx){
		$.ajax({
		   	type: "POST",
		    url: "/elective/getZY",
		    data:{fid:fid,addScoreYx:yx},
		   	dataType: 'json',
		    success: function (result) {
		       	var html = "";
		        	if (result) {
		           	for(var i = 0;i<result.data.length;i++){
		           		var name = result.data[i].name;
		           	  	var id = result.data[i].id;
		           	 	html += "<li data-id=\""+id+"\">"+name+"</li>";
		           	}
		         	}
		           $("#zyDiv3").html(html);
		           $("#selStuZyIp2").val("");
		           $("#zyDiv4").html(html);
		           $("#selStuZyIp").val("");
		     	}
		});
	}
	function getBJ(yx,zy){
		$.ajax({
	    	type: "POST",
	    	url: "/elective/getBj",
	    	data:{fid:fid,yx:yx,zy:zy},
	    	dataType: 'json',
	    	success: function (result) {
	          	var html = "";
	         	if (result) {
	            	for(var i = 0;i<result.data.length;i++){
	            		var name = result.data[i].name;
	            		var id = result.data[i].id;
	            		html += "<li data-id=\""+id+"\">"+name+"</li>";
	            	}
	         	}
	            $("#bjDiv3").html(html);
	            $("#selStuBjIp2").val("");
	            $("#bjDiv4").html(html);
	            $("#selStuBjIp").val("");
	      	}
		});
	}
    if(canBatchOperate){
    	var yx = $("#selStuYxIp2").val();
		var zy = $("#selStuZyIp2").val();
		getZY(yx);
	    getBJ(yx,zy);
    }
    layui.use(["jquery", "table", "layer", "upload"], function () {
        var table = layui.table,
            form = layui.form,
            layer = layui.layer,
            upload = layui.upload,
            $ = layui.jquery;
        // tab切换
        $(".z-tab ul li").click(function () {
            var idx = $(this).index();

            $(this).addClass("active").siblings().removeClass("active");
            $(".z-box .box-con").eq(idx).show().siblings().hide();
        });
        /*********************************** 学生范围* start ****************************/
        $(".importStu").click(function () {
            // if (!canUpdateOperate){
            //   alert("该课程的面向对象来自培养过程管理或选修课申报表，无法在此处修改");
            //   return false;
            // }
            if(canBatchOperate){
                if(!$("#addFlag").is(":hidden")){
                    alert("数据正在操作中，稍后再试");
                    return false;
                }
                $.ajax({
                    type: "POST",
                    url: "/elective/studentRange/delImportStu",
                    data:{fid:fid,formUserId:formUserId,batchOperateFormUserIds:batchOperateFormUserIds},
                    async:false,
                    success: function () {
                    }
                });
                getStatus(0);
                layerIndex2 = layer.open({
                    type: 1,
                    title: false,
                    closeBtn: false,
                    shadeClose: true,
                    isOutAnim: true,
                    shadeClose: false,
                    content: $("#importStu"),
                    area: ["auto", "auto"],
                });
                $("#uploadExcel").addClass("layui-hide");
            }
        });
        var inr;
        $("#stuSure1").click(function () {
            $("#failButton").show();
            $("#failButton2").hide();
            $("#failButton").text("正在导入...");
            $.ajax({
                type: "POST",
                url: "/elective/studentRange/startImportStu",
                data:{fid:fid,formUserId:formUserId,batchOperateFormUserIds:batchOperateFormUserIds},
                success: function () {
                    /*searchStuList();
                    searchBjList();*/

                }
            });
            inr = setInterval(function (){
                getStatus(1);
            },1000);
            //layer.close(layerIndex2);
        });
        // 导入选课学生
        upload.render({
            elem: "#ID-upload-demo-drag",
            url: "/elective/studentRange/importUser?fid="+fid+"&formUserId="+formUserId+"&batchOperateFormUserIds="+batchOperateFormUserIds, // 此处用的是第三方的 http 请求演示，实际使用时改成您自己的上传接口即可。

            accept: "file",
            exts: "xlsx|xls",
            size: 500,

            before: function (obj) {
                var files = (this.files = obj.pushFile()); // layui文档标注这是一个文件队列，(每次选定文件都会往其中添加)
                var fileName, fileKey;
                var i = 0;
                var j = 0;
                for (var key in files) {
                    i++;
                    fileName = files[key].name; //针对一个文件直接赋值就可以了
                }
                //判断队列每次只保存最后一个文件

                if (i > 1) {
                    for (var key in files) {
                        j++;
                        if (i == j) {
                            fileName = files[key].name;
                            fileKey = key;
                        } else {
                            delete files[key]; //删除队列中的文件
                        }
                    }
                }
                var ext = fileName.substr(fileName.lastIndexOf(".") + 1); //获得后缀
                if ("xlsx,xls".indexOf(ext) == -1) {
                    layer.msg("请上传excel文件");
                    return false;
                }
            },
            done: function (res) {
//      $("#ID-upload-demo-preview")
//        .removeClass("layui-hide")
//        .find("img")
//        .attr("src", res.files.file);
                if(res.status){
                    layer.msg("上传成功");
                    $("#uploadExcel")
                        .removeClass("layui-hide")
                        .find("span")
                        .text(res.failname);
                }else{
                    layer.msg(res.msg);
                }

            },
            error: function (index, upload) {
                layer.msg("上传失败，请重新上传");
            },
        });

        //  选择
        $(".box-common .z-tab-search ul li").click(function () {
            if(canBatchOperate){
                $(this).addClass("active").siblings().removeClass("active");
                var idx = $(this).index();
                $(".z-table-range .z-range").eq(idx).show().siblings().hide();
                if (idx == 1){
                    $("#quotas").show()
                    $("#quotas1").hide()
                    searchBjList();
                }else if (idx==2){
                    $("#quotas1").show()
                    $("#quotas").hide()
                    searchYxList();
                }else {
                    $("#quotas").hide()
                    $("#quotas1").hide()
                    searchStuList();
                }
            }
        });

        function getStatus(flag){
            $.ajax({
                type: "POST",
                url: "/elective/studentRange/getFinished",
                data:{fid:fid,formUserId:formUserId,batchOperateFormUserIds:batchOperateFormUserIds},
                dataType: 'json',
                success: function (result) {
                    if(result.data!=''){
                        if(result.data.finished == '1'){
                            if(result.data.failCount == '0'){
                                $("#failButton").show();
                                $("#failButton2").hide();
                                $("#failButton").text("导入完成，无失败数据");
                            }else{
                                $("#failButton").hide();
                                $("#failButton2").show();
                                $("#failButton").text("下载失败数据");
                            }
                            clearInterval(inr);
                            if(flag == '1'){
                                setTimeout(function(){
                                    searchStuList();
                                    searchBjList();
                                    searchYxList();
                                    layer.close(layerIndex2);
                                },2000);
                            }
                        }else{
                            $("#failButton").show();
                            $("#failButton2").hide();
                            $("#failButton").text("正在导入...");
                        }
                    }else{
                        $("#failButton").hide();
                        $("#failButton2").hide();
                    }
                }
            });
        }

        if(canBatchOperate){
            searchStuList();
            searchBjList();
            searchYxList();
        }else{
            searchNoDataStuList();
        }
        function searchStuList(){
            var nj = $("#selStuNjIp2").val();
            var yx = $("#selStuYxIp2").val();
            var zy = $("#selStuZyIp2").val();
            var bj = $("#selStuBjIp2").val();
            var xq = $("#selStuXqIp2").val();
            var xm = $("#selStuXmIp2").val();
            // 按学生显示
            var courseData1 = [];
            table.render({
                elem: "#materialTable",
                url: '/elective/studentRange/getTaskCourseStudent?formId='+formId+'&formUserId='+formUserId+'&fid='+fid+'&nj='+nj+'&yx='+yx+'&zy='+zy+'&bj='+bj+'&xq='+xq+'&xm='+xm,
                data: courseData1,
                page: {
                    layout: ["count", "prev", "page", "next", "limit", "skip"],
                },
                cols: [
                    [
                        {
                            type: "checkbox",
                            fixed: "left",
                            width: 80,
                        },
                        {
                            field: "xsxxXm",
                            title: "姓名",
                            align: "center",
                            minWidth: 150,
                        },
                        {
                            field: "xsxxXhDesensitize",
                            title: "学号",
                            align: "center",
                            minWidth: 150,
                        },
                        {
                            field: "optionalName",
                            title: "学生课程关系",
                            align: "center",
                            minWidth: 150,
                        },
                        {
                            field: "xsxxSsnj",
                            title: "所属年级",
                            align: "center",
                            minWidth: 150,
                        },
                        {
                            field: "xsxxSsyx",
                            title: "所属院系",
                            align: "center",
                            minWidth: 150,
                        },
                        {
                            field: "xsxxSszy",
                            title: "所属专业",
                            align: "center",
                            minWidth: 150,
                        },
                        {
                            field: "xsxxSzbj",
                            title: "所在班级",
                            align: "center",
                            minWidth: 150,
                        },
                        {
                            field: "xsxxSzxq",
                            title: "所在校区",
                            align: "center",
                            minWidth: 150,
                        },
                        {
                            field: "id",
                            title: "id",
                            align: "center",
                            minWidth: 110,
                            hide:true,
                        },
                    ],
                ],
                done: function (res, curr, count) {
                    if(count==0){
                        exitOptinal = '';
                    }
                    res.data.forEach(function (item, index) {
                        stuSource.set(item.id+"", item.saveSource);
                    });
                },
            });
        }

        function searchNoDataStuList(){
            var courseData1 = [];
            table.render({
                elem: "#materialTable",
                data: courseData1,
                page: {
                    layout: ["count", "prev", "page", "next", "limit", "skip"],
                },
                cols: [
                    [
                        {
                            type: "checkbox",
                            fixed: "left",
                            width: 80,
                        },
                        {
                            field: "xsxxXm",
                            title: "姓名",
                            align: "center",
                            minWidth: 150,
                        },
                        {
                            field: "xsxxXhDesensitize",
                            title: "学号",
                            align: "center",
                            minWidth: 150,
                        },
                        {
                            field: "optionalName",
                            title: "学生课程关系",
                            align: "center",
                            minWidth: 150,
                        },
                        {
                            field: "xsxxSsnj",
                            title: "所属年级",
                            align: "center",
                            minWidth: 150,
                        },
                        {
                            field: "xsxxSsyx",
                            title: "所属院系",
                            align: "center",
                            minWidth: 150,
                        },
                        {
                            field: "xsxxZydm",
                            title: "所属专业",
                            align: "center",
                            minWidth: 150,
                        },
                        {
                            field: "xsxxSzbj",
                            title: "所在班级",
                            align: "center",
                            minWidth: 150,
                        },
                        {
                            field: "xsxxSzbj",
                            title: "所在校区",
                            align: "center",
                            minWidth: 150,
                        },
                        {
                            field: "id",
                            title: "id",
                            align: "center",
                            minWidth: 110,
                            hide:true,
                        },
                    ],
                ],
                done: function (res, curr, count) {
                },
            });
        }

        function searchBjList(){
            var nj = $("#selStuNjIp2").val();
            var yx = $("#selStuYxIp2").val();
            var zy = $("#selStuZyIp2").val();
            var bj = $("#selStuBjIp2").val();
            var xq = $("#selStuXqIp2").val();
            var xm = $("#selStuXmIp2").val();
            // 按班级显示
            var courseData2 = [];
            table.render({
                elem: "#materialTable1",
                url: '/elective/studentRange/getTaskCourseStudentBj?formId='+formId+'&formUserId='+formUserId+'&fid='+fid+'&nj='+nj+'&yx='+yx+'&zy='+zy+'&bj='+bj+'&xq='+xq+'&xm='+xm,
                data: courseData2,
                page: {
                    layout: ["count", "prev", "page", "next", "limit", "skip"],
                },
                cols: [
                    [
                        {
                            type: "checkbox",
                            fixed: "left",
                            width: 80,
                        },
                        {
                            field: "xsxxSzbj",
                            title: "班级名称",
                            align: "center",
                            minWidth: 150,
                        },
                        {
                            field: "optionalName",
                            title: "学生课程关系",
                            align: "center",
                            minWidth: 150,
                        },
                        {
                            field: "xsxxSsnj",
                            title: "所属年级",
                            align: "center",
                            minWidth: 150,
                        },

                        {
                            field: "xsxxSsyx",
                            title: "所属院系",
                            align: "center",
                            minWidth: 150,
                        },
                        {
                            field: "xsxxSszy",
                            title: "所属专业",
                            align: "center",
                            minWidth: 150,
                        },

                        {
                            field: "xsxxSzxq",
                            title: "所在校区",
                            align: "center",
                            minWidth: 150,
                        },
                        {
                            field: "num",
                            title: "班级人数",
                            align: "center",
                            minWidth: 150,
                        },
                        {
                            field: "selnum",
                            title: "选课名额",
                            align: "center",
                            minWidth: 150,
                        },
                        {
                            field: "xsxxBjdm",
                            title: "xsxx_bjdm",
                            align: "center",
                            minWidth: 110,
                            hide:true,
                        },
                        {
                            field: "optional",
                            title: "关系",
                            align: "center",
                            minWidth: 110,
                            hide:true,
                        },
                    ],
                ],
                done: function (res, curr, count) {
                    res.data.forEach(function (item, index) {
                        bjSource.set(item.xsxxBjdm, item.maxSaveSource);
                    });
                },
            });
        }

        // 添加选课学生-按学生显示
        // var mtTable2 = [];
        // table.render({
        //     elem: "#mtTable2",
        //     //url: '/elective/studentRange/getStudent?formId='+formId+'&formUserId='+formUserId+'&fid='+fid,
        //     data: mtTable2,
        //     page: true,
        //     height: 396,
        //     cols: [
        //         [
        //             {
        //                 type: "checkbox",
        //                 fixed: "left",
        //                 width: 70,
        //             },
        //             {
        //                 field: "xsjbxx_xm",
        //                 title: "姓名",
        //                 align: "center",
        //                 minWidth: 140,
        //             },
        //             {
        //                 field: "xsjbxx_xh",
        //                 title: "学号",
        //                 align: "center",
        //                 minWidth: 178,
        //             },
        //             {
        //                 field: "xsjbxx_sznj",
        //                 title: "所属年级",
        //                 align: "center",
        //                 minWidth: 109,
        //             },
        //             {
        //                 field: "xsjbxx_yxxx",
        //                 title: "所属院系",
        //                 align: "center",
        //                 minWidth: 136,
        //             },
        //             {
        //                 field: "xsjbxx_zyxx",
        //                 title: "所属专业",
        //                 align: "center",
        //                 minWidth: 105,
        //             },
        //             {
        //                 field: "xsjbxx_bjxx",
        //                 title: "所在班级",
        //                 align: "center",
        //                 minWidth: 129,
        //             },
        //             {
        //                 field: "xsjbxx_szxq",
        //                 title: "所在校区",
        //                 align: "center",
        //                 minWidth: 128,
        //             },
        //             {
        //                 field: "rowInfo",
        //                 title: "rowInfo",
        //                 align: "center",
        //                 minWidth: 110,
        //                 hide:true,
        //             },
        //         ],
        //     ],
        //     done: function (res, curr, count) {},
        // });
        function searchBj(){
            var selStuNjIp = $("#selStuNjIp").val();
            var selStuYxIp = $("#selStuYxIp").val();
            var selStuZyIp = $("#selStuZyIp").val();
            var selStuBjIp = $("#selStuBjIp").val();
            var selStuXqIp = $("#selStuXqIp").val();
            var selStuXmIp = $("#selStuXmIp").val();
            var mtTable3 = [];
            table.render({
                elem: "#mtTable3",
                url: '/elective/studentRange/getBj?formId='+formId+'&formUserId='+formUserId+'&fid='+fid+'&nj='+selStuNjIp+'&yx='+selStuYxIp+'&zy='+selStuZyIp+'&bj='+selStuBjIp+'&xq='+selStuXqIp+"&xm="+selStuXmIp,
                data: mtTable3,
                page: true,
                height: 396,
                cols: [
                    [
                        {
                            type: "checkbox",
                            fixed: "left",
                            width: 70,
                        },
                        {
                            field: "bjxx_bjmc",
                            title: "班级名称",
                            align: "center",
                            minWidth: 140,
                        },

                        {
                            field: "bjxx_rxnf",
                            title: "所属年级",
                            align: "center",
                            minWidth: 129,
                        },
                        {
                            field: "bjxx_ssyx",
                            title: "所属院系",
                            align: "center",
                            minWidth: 136,
                        },
                        {
                            field: "bjxx_zy",
                            title: "所属专业",
                            align: "center",
                            minWidth: 125,
                        },

                        {
                            field: "bjxx_szxq",
                            title: "所在校区",
                            align: "center",
                            minWidth: 128,
                        },
                        {
                            field: "bjxx_njrs",
                            title: "班级人数",
                            align: "center",
                            minWidth: 80,
                        },
                        {
                            field: "rowInfo",
                            title: "rowInfo",
                            align: "center",
                            minWidth: 110,
                            hide:true,
                        },
                    ],
                ],
                done: function (res, curr, count) {
                    var num = 0;
                    if($("#checkAllClass").hasClass("checked")){
                        var addAllBjNotIn = $("#addAllBjNotIn").val();
                        var temp = addAllBjNotIn.split(",");
                        var ids = [];
                        for(var i = 0;i<temp.length;i++){
                            if(temp[i] != ''){
                                ids.push(temp[i]);
                            }
                        }
                        res.data.forEach(function (item, index) {
                            var formUserid = item.rowInfo.formUserId.toString();
                            if($.inArray(formUserid, ids)==-1){
                                num++;
                                $('div[lay-id="mtTable3"] .layui-table-fixed-l .layui-table-body').find('tr').eq(index).find('input[type=checkbox]').prop('checked', true);
                                $('div[lay-id="mtTable3"] .layui-table-fixed-l .layui-table-body').find('tr').eq(index).find(".layui-form-checkbox").addClass('layui-form-checked');
                            }
                        });
                    }else{
                        var addBjIds = $("#addBjIds").val();
                        var temp = addBjIds.split(",");
                        var ids = [];
                        for(var i = 0;i<temp.length;i++){
                            if(temp[i] != ''){
                                ids.push(temp[i]);
                            }
                        }
                        res.data.forEach(function (item, index) {
                            var formUserid = item.rowInfo.formUserId.toString();
                            if($.inArray(formUserid, ids)!=-1){
                                num++;
                                $('div[lay-id="mtTable3"] .layui-table-fixed-l .layui-table-body').find('tr').eq(index).find('input[type=checkbox]').prop('checked', true);
                                $('div[lay-id="mtTable3"] .layui-table-fixed-l .layui-table-body').find('tr').eq(index).find(".layui-form-checkbox").addClass('layui-form-checked');
                            }
                        });
                    }
                    if(num==res.data.length){
                        $('div[lay-id="mtTable3"] input[type=checkbox]').prop("checked", true);
                        $('div[lay-id="mtTable3"] .layui-form-checkbox').addClass(
                            "layui-form-checked"
                        );
                    }
                },
            });
        }
        function searchStu(){
            var selStuNjIp = $("#selStuNjIp").val();
            var selStuYxIp = $("#selStuYxIp").val();
            var selStuZyIp = $("#selStuZyIp").val();
            var selStuBjIp = $("#selStuBjIp").val();
            var selStuXqIp = $("#selStuXqIp").val();
            var selStuXmIp = $("#selStuXmIp").val();
            // var mtTable2 = [];
            table.render({
                elem: "#mtTable2",
                url: '/elective/studentRange/getStudentV3?formId='+formId+'&formUserId='+formUserId+'&fid='+fid+'&nj='+selStuNjIp+'&yx='+selStuYxIp+'&zy='+selStuZyIp+'&bj='+selStuBjIp+'&xq='+selStuXqIp+"&xm="+selStuXmIp+"&sett=true",
                // data: mtTable2,
                page: true,
                height: 396,
                cols: [
                    [
                        {
                            type: "checkbox",
                            fixed: "left",
                            width: 70,
                        },
                        {
                            field: "xsxkjg_xm",
                            title: "姓名",
                            align: "center",
                            minWidth: 140,
                        },
                        {
                            field: "xsxkjg_xh",
                            title: "学号",
                            align: "center",
                            minWidth: 178,
                        },
                        {
                            field: "xsxkjg_ssnj",
                            title: "所属年级",
                            align: "center",
                            minWidth: 109,
                        },
                        {
                            field: "xsxkjg_ssxy",
                            title: "所属院系",
                            align: "center",
                            minWidth: 136,
                        },
                        {
                            field: "xsxkjg_sszy",
                            title: "所属专业",
                            align: "center",
                            minWidth: 105,
                        },
                        {
                            field: "xsxkjg_szbj",
                            title: "所在班级",
                            align: "center",
                            minWidth: 129,
                        },
                        {
                            field: "xsxkjg_szxq",
                            title: "所在校区",
                            align: "center",
                            minWidth: 128,
                        },
                        {
                            field: "rowInfo",
                            title: "rowInfo",
                            align: "center",
                            minWidth: 110,
                            hide:true,
                        },
                    ],
                ],
                done: function (res, curr, count) {
                    var num = 0;
                    if($("#checkAll").hasClass("checked")){
                        var addAllStuNotIn = $("#addAllStuNotIn").val();
                        var temp = addAllStuNotIn.split(",");
                        var ids = [];
                        for(var i = 0;i<temp.length;i++){
                            if(temp[i] != ''){
                                ids.push(temp[i]);
                            }
                        }
                        res.data.forEach(function (item, index) {
                            var uid = item.xsxkjg_uid;
                            if($.inArray(uid, ids)==-1 ){
                                num ++;
                                $('div[lay-id="mtTable2"] .layui-table-fixed-l .layui-table-body').find('tr').eq(index).find('input[type=checkbox]').prop('checked', true);
                                $('div[lay-id="mtTable2"] .layui-table-fixed-l .layui-table-body').find('tr').eq(index).find(".layui-form-checkbox").addClass('layui-form-checked');
                            }
                        });
                    }else{
                        var addStuIds = $("#addStuIds").val();
                        var temp = addStuIds.split(",");
                        var ids = [];
                        for(var i = 0;i<temp.length;i++){
                            if(temp[i] != ''){
                                ids.push(temp[i]);
                            }
                        }
                        res.data.forEach(function (item, index) {
                            var uid = item.xsxkjg_uid;
                            if($.inArray(uid, ids)!=-1 ){
                                num ++;
                                $('div[lay-id="mtTable2"] .layui-table-fixed-l .layui-table-body').find('tr').eq(index).find('input[type=checkbox]').prop('checked', true);
                                $('div[lay-id="mtTable2"] .layui-table-fixed-l .layui-table-body').find('tr').eq(index).find(".layui-form-checkbox").addClass('layui-form-checked');
                            }
                        });
                    }
                    if(num==res.data.length){
                        $('div[lay-id="mtTable2"] input[type=checkbox]').prop("checked", true);
                        $('div[lay-id="mtTable2"] .layui-form-checkbox').addClass(
                            "layui-form-checked"
                        );
                    }
                },
            });
        }
        // 选择全部
        $("#checkAll").click(function () {
            $(this).toggleClass("checked");
            if ($(this).hasClass("checked")) {
                $('div[lay-id="mtTable2"] input[type=checkbox]').prop("checked", true);
                $('div[lay-id="mtTable2"] .layui-form-checkbox').addClass(
                    "layui-form-checked"
                );
                // mtTable2.map((item) => {
                //     item.isAdd = true;
                // });
            } else {
                $("#addStuIds").val("");
                $('div[lay-id="mtTable2"] input[type=checkbox]').prop("checked", false);
                $('div[lay-id="mtTable2"] .layui-form-checkbox').removeClass(
                    "layui-form-checked"
                );
                // mtTable2.map((item) => {
                //     item.isAdd = false;
                // });
            }
        });
        // 添加选课学生-按班级显示
        // var mtTable3 = [
        //     {
        //         id: 1,
        //         name: "23计算机科学",
        //         code: "************",
        //         grade: "2023",
        //         depth: "计算机学院",
        //         major: "计算机学院",
        //         class: "23计算机1班",
        //         school: "南校区",
        //         isAdd: false,
        //         classCount: 20,
        //     },
        //     {
        //         id: 2,
        //         name: "23计算机科学",
        //         code: "************",
        //         grade: "2023",
        //         depth: "计算机学院",
        //         major: "计算机学院",
        //         class: "23计算机1班",
        //         school: "南校区",
        //         isAdd: false,
        //         classCount: 20,
        //     },
        // ];
        // table.render({
        //     elem: "#mtTable3",
        //     // url: '../../demo/table/user/-page=1&limit=20.js',
        //     data: mtTable3,
        //     page: true,
        //     height: 396,
        //     cols: [
        //         [
        //             {
        //                 type: "checkbox",
        //                 fixed: "left",
        //                 width: 70,
        //             },
        //             {
        //                 field: "class",
        //                 title: "班级姓名",
        //                 align: "center",
        //                 minWidth: 140,
        //             },
        //
        //             {
        //                 field: "grade",
        //                 title: "所属年级",
        //                 align: "center",
        //                 minWidth: 129,
        //             },
        //             {
        //                 field: "depth",
        //                 title: "所属院系",
        //                 align: "center",
        //                 minWidth: 136,
        //             },
        //             {
        //                 field: "major",
        //                 title: "所属专业",
        //                 align: "center",
        //                 minWidth: 125,
        //             },
        //
        //             {
        //                 field: "school",
        //                 title: "所在校区",
        //                 align: "center",
        //                 minWidth: 128,
        //             },
        //             {
        //                 field: "classCount",
        //                 title: "班级人数",
        //                 align: "center",
        //                 minWidth: 80,
        //             },
        //         ],
        //     ],
        //     done: function (res, curr, count) {},
        // });
        // 选择全部
        $("#checkAllClass").click(function () {
            $(this).toggleClass("checked");
            if ($(this).hasClass("checked")) {
                $('div[lay-id="mtTable3"] input[type=checkbox]').prop("checked", true);
                $('div[lay-id="mtTable3"] .layui-form-checkbox').addClass(
                    "layui-form-checked"
                );
                // mtTable3.map((item) => {
                //     item.isAdd = true;
                // });
            } else {
                $("#addBjIds").val("");
                $('div[lay-id="mtTable3"] input[type=checkbox]').prop("checked", false);
                $('div[lay-id="mtTable3"] .layui-form-checkbox').removeClass(
                    "layui-form-checked"
                );
                // mtTable3.map((item) => {
                //     item.isAdd = false;
                // });
            }
        });
        var layerIndex2;
        $("#addStu,.addStu").click(function () {
            // if (!canUpdateOperate){
            // 	alert("该课程的面向对象来自培养过程管理或选修课申报表，无法在此处修改");
            // 	return false;
            // }
            if(!$("#addFlag").is(":hidden")){
                alert("数据正在操作中，稍后再试");
                return false;
            }
            if(canBatchOperate){
                $("#addStuIds").val("");
                $("#addBjIds").val("");
                $("#addAllStuNotIn").val("");
                $("#checkAll").removeClass("checked");
                $("#addAllBjNotIn").val("");
                $("#checkAllClass").removeClass("checked");
                layerIndex2 = layer.open({
                    type: 1,
                    title: false,
                    closeBtn: false,
                    shadeClose: true,
                    isOutAnim: true,
                    shadeClose: false,
                    content: $("#selStu"),
                    area: ["auto", "auto"],
                    success: function () {
                        var str = $("#selStu .z-tab-search ul").find(".active").text();
                        if(str == '按学生显示'){
                            searchStu();
                        }
                        if(str == '按班级显示'){
                            searchBj();
                        }
                    },
                });
            }
        });
        //  选择
        $("#selStu .z-relation ul li").click(function () {
            $(this).addClass("active").siblings().removeClass("active");
        });
        $(".del").click(function () {
            // if (!canUpdateOperate){
            //     alert("该课程的面向对象来自培养过程管理或选修课申报表，无法在此处修改");
            //     return false;
            // }
            if(!$("#addFlag").is(":hidden")){
                alert("数据正在操作中，稍后再试");
                return false;
            }
            if(canBatchOperate){
                var str = $("#z-box .z-tab-search ul").find(".active").text();
                var t = new Date().getTime();

                if(str == '按学生显示'){
                    var ids = $("#delids").val();
                    let strings = ids.split(",");
                    for (let string of strings) {
                        if (stuSource.get(string) == 1){
                            alert("该课程的面向对象来自培养过程管理或选修课申报表，无法在此处修改");
                            return false;
                        }
                    }
                    $("#addFlagSpan").text("正在删除数据中...");
                    $("#addFlag").show();
                    $.ajax({
                        type: "POST",
                        url: "/elective/studentRange/delStudent",
                        data:{ids:ids,t:t,fid:fid,batchOperateFormUserIds:batchOperateFormUserIds},
                        success: function () {
                        }
                    });
                    //table.reload("materialTable");
                }
                if(str == '按班级显示'){
                    var bjdms = $("#delBjbh").val();
                    let strings = bjdms.split(",");
                    for (let string of strings) {
                        if (bjSource.get(string) == 1){
                            alert("该课程的面向对象来自培养过程管理或选修课申报表，无法在此处修改");
                            return false;
                        }
                    }
                    $("#addFlagSpan").text("正在删除数据中...");
                    $("#addFlag").show();
                    $.ajax({
                        type: "POST",
                        url: "/elective/studentRange/delStudentBj",
                        data:{bjdms:bjdms,formUserId:formUserId,fid:fid,t:t,batchOperateFormUserIds:batchOperateFormUserIds},
                        success: function () {

                        }
                    });
                    //table.reload("materialTable1");
                }
                if(str == '按院系显示'){
                    var bjdms = $("#delYxbh").val();
                    let strings = bjdms.split(",");
                    for (let string of strings) {
                        if (bjSource.get(string) == 1){
                            alert("该课程的面向对象来自培养过程管理或选修课申报表，无法在此处修改");
                            return false;
                        }
                    }
                    $("#addFlagSpan").text("正在删除数据中...");
                    $("#addFlag").show();
                    $.ajax({
                        type: "POST",
                        url: "/elective/studentRange/delStudentBj",
                        data:{bjdms:bjdms,formUserId:formUserId,fid:fid,t:t,batchOperateFormUserIds:batchOperateFormUserIds,type:1},
                        success: function () {

                        }
                    });
                    //table.reload("materialTable1");
                }
                delInterval = setInterval(function(){
                    getDelStatus(t);
                }, 1000);
            }
        });
        $("#z-btn2").click(function () {
            if(canBatchOperate){
                var str = $("#selStu .z-tab-search ul").find(".active").text();
                if(str == '按学生显示'){
                    searchStu();
                }
                if(str == '按班级显示'){
                    searchBj();
                }
            }
        });
        $("#z-btn1").click(function () {
            if(canBatchOperate){
                var str = $("#z-box .z-tab-search ul").find(".active").text();
                if(str == '按学生显示'){
                    searchStuList();
                }
                if(str == '按班级显示'){
                    searchBjList();
                }
                if(str == '按院系显示'){
                    searchYxList();
                }
            }
        });
        $("#selStuXmIp").keyup(function(){
            if(event.keyCode == 13){
                if(canBatchOperate){
                    var str = $("#selStu .z-tab-search ul").find(".active").text();
                    if(str == '按学生显示'){
                        searchStu();
                    }
                    if(str == '按班级显示'){
                        searchBj();
                    }
                }
            }
        });
        $("#selStuXmIp2").keyup(function(){
            if(event.keyCode == 13){
                if(canBatchOperate){
                    var str = $("#z-box .z-tab-search ul").find(".active").text();
                    if(str == '按学生显示'){
                        searchStuList();
                    }
                    if(str == '按班级显示'){
                        searchBjList();
                    }
                    if(str == '按院系显示'){
                        searchYxList();
                    }
                }
            }
        });
        //  选择
        $("#selStu .z-tab-search ul li").click(function () {
            $(this).addClass("active").siblings().removeClass("active");
            var idx = $(this).index();
            $(".z-table-sel .tabBox").eq(idx).show().siblings().hide();
            if (idx == 0) searchStu();
            if (idx == 1) searchBj();
        });
        var addInterval;
        var status = "";
        var delInterval;
        var delStatus = "";
        function getAddStatus(t){
            $.ajax({
                type: "POST",
                url: "/elective/studentRange/getAddStatus",
                data:{fid:fid,t:t},
                dataType:'json',
                success: function (data) {
                    status = data.status;
                    if(status == '1'){
                        clearInterval(addInterval);
                        $("#addFlag").hide();
                        searchStuList();
                        searchBjList();
                        searchYxList();
                    }
                }
            });
        }
        function getDelStatus(t){
            $.ajax({
                type: "POST",
                url: "/elective/studentRange/getDelStatus",
                data:{fid:fid,t:t},
                dataType:'json',
                success: function (data) {
                    status = data.status;
                    if(status == '1'){
                        clearInterval(delInterval);
                        $("#addFlag").hide();
                        searchStuList();
                        searchBjList();
                        searchYxList();
                    }
                }
            });
        }
        // 确定
        $("#stuSure").click(function () {
            var t = new Date().getTime();
            var optinal = $("#selStu .z-relation ul").find(".active").text();
            var str = $("#selStu .z-tab-search ul").find(".active").text();
            if(exitOptinal != '' && exitOptinal != optinal){
                alert("已存在"+exitOptinal+"规则");
                return false;
            }
            $("#addFlagSpan").text("正在添加数据中...");
            $("#addFlag").show();
            if(str == '按学生显示'){
                var userIds = $("#addStuIds").val();
                var flag = $("#checkAll").hasClass("checked");
                var isAll = false;
                if(flag){
                    isAll = true;
                }
                var selStuNjIp = $("#selStuNjIp").val();
                var selStuYxIp = $("#selStuYxIp").val();
                var selStuZyIp = $("#selStuZyIp").val();
                var selStuBjIp = $("#selStuBjIp").val();
                var selStuXqIp = $("#selStuXqIp").val();
                var selStuXmIp = $("#selStuXmIp").val();
                var addAllStuNotIn = $("#addAllStuNotIn").val();
                $.ajax({
                    type: "POST",
                    url: "/elective/studentRange/addStudent",
                    data:{batchOperateFormUserIds:batchOperateFormUserIds,userIds:userIds,formUserId:formUserId,fid:fid,formId:formId,optinal:optinal,isAll:isAll,selStuNjIp:selStuNjIp,selStuYxIp:selStuYxIp,selStuZyIp:selStuZyIp,selStuBjIp:selStuBjIp,selStuXqIp:selStuXqIp,selStuXmIp:selStuXmIp,t:t,addAllStuNotIn:addAllStuNotIn},
                    success: function () {
//	        	  if(!flag){
//	        		  layer.close(layerIndex2);
//		        	  searchStuList();
//		        	  searchBjList();
//	    		  }
                    }
                });
                //if(flag){
                layer.close(layerIndex2);
//    		  setInterval(function(){
                searchStuList();
                searchBjList();
//    		 }, 2000);
                //}
            }
            if(str == '按班级显示'){
                var checkStatus = table.checkStatus('mtTable3');
                var formUserIds = $("#addBjIds").val();
                var flag = $("#checkAllClass").hasClass("checked")
                var isAll = false;
                if(flag){
                    isAll = true;
                }
                var selStuNjIp = $("#selStuNjIp").val();
                var selStuYxIp = $("#selStuYxIp").val();
                var selStuZyIp = $("#selStuZyIp").val();
                var selStuBjIp = $("#selStuBjIp").val();
                var selStuXqIp = $("#selStuXqIp").val();
                var selStuXmIp = $("#selStuXmIp").val();
                var addAllBjNotIn = $("#addAllBjNotIn").val();
                $.ajax({
                    type: "POST",
                    url: "/elective/studentRange/addBj",
                    data:{batchOperateFormUserIds:batchOperateFormUserIds,formUserIds:formUserIds,formUserId:formUserId,fid:fid,formId:formId,optinal:optinal,isAll:isAll,selStuNjIp:selStuNjIp,selStuYxIp:selStuYxIp,selStuZyIp:selStuZyIp,selStuBjIp:selStuBjIp,selStuXqIp:selStuXqIp,selStuXmIp:selStuXmIp,t:t,addAllBjNotIn:addAllBjNotIn},
                    success: function () {
                    }
                });
                layer.close(layerIndex2);
//		  setInterval(function(){
                searchBjList();
                searchStuList();
//	 }, 2000);
            }
            addInterval = setInterval(function(){
                getAddStatus(t);
            }, 1000);
        });
        // 取消
        $("#importStu .pu-cancel,#importStu .pu-close").click(function () {
            $.ajax({
                type: "POST",
                url: "/elective/studentRange/delImportStu",
                data:{fid:fid,formUserId:formUserId,batchOperateFormUserIds:batchOperateFormUserIds},
                success: function () {
                    layer.close(layerIndex2);
                }
            });
        });
        $(".layui-tips").on({
            mouseenter: function () {
                var that = this;
                var con = $(this).attr("data-tip");
                layTips = layer.tips(con, that, {
                    tips: 1,
                });
            },
            mouseleave: function () {
                layer.close(layTips);
            },
        });
        table.on('checkbox(mtTable2)', function(obj){
            if($("#checkAll").hasClass("checked")){
                var addAllStuNotIn = $("#addAllStuNotIn").val();
                var temp = addAllStuNotIn.split(",");
                var ids = [];
                for(var i = 0;i<temp.length;i++){
                    if(temp[i] != ''){
                        ids.push(temp[i]);
                    }
                }
                if(obj.data.rowInfo!=undefined){
                    var id = obj.data.xsxkjg_uid;
                    if(obj.checked){
                        ids.splice($.inArray(id, ids),1);
                    }else{
                        if($.inArray(id, ids)==-1){
                            ids.push(id);
                        }
                    }
                }else{
                    var checkStatus = table.checkStatus('mtTable2');
                    if(checkStatus.data.length==0){
                        var data = table.cache.mtTable2;
                        for(var i = 0;i<data.length;i++){
                            var allid = data[i].xsxkjg_uid;
                            if($.inArray(allid, ids)==-1){
                                ids.push(allid);
                            }
                        }
                    }else{
                        for(var i = 0;i<checkStatus.data.length;i++){
                            var notallid = checkStatus.data[i].xsxkjg_uid;
                            if($.inArray(notallid, ids)>-1){
                                ids.splice($.inArray(notallid, ids),1);
                            }
                        }
                    }
                }
                $("#addAllStuNotIn").val(ids.toString());
            }else{
                var addStuIds = $("#addStuIds").val();
                var temp = addStuIds.split(",");
                var ids = [];
                for(var i = 0;i<temp.length;i++){
                    if(temp[i] != ''){
                        ids.push(temp[i]);
                    }
                }
                if(obj.data.rowInfo!=undefined){
                    var id = obj.data.xsxkjg_uid;
                    if(obj.checked){
                        if($.inArray(id, ids)==-1){
                            ids.push(id);
                        }
                    }else{
                        ids.splice($.inArray(id, ids),1);
                    }
                }
                else{
                    var checkStatus = table.checkStatus('mtTable2');
                    if(checkStatus.data.length==0){
                        var data = table.cache.mtTable2;
                        for(var i = 0;i<data.length;i++){
                            var notallid = data[i].xsxkjg_uid;
                            ids.splice($.inArray(notallid, ids),1);
                        }
                    }else{
                        for(var i = 0;i<checkStatus.data.length;i++){
                            var allid = checkStatus.data[i].xsxkjg_uid;
                            if($.inArray(allid, ids)==-1){
                                ids.push(allid);
                            }
                        }
                    }
                }
                $("#addStuIds").val(ids.toString());
            }
        });

        table.on('checkbox(mtTable3)', function(obj){
            if($("#checkAllClass").hasClass("checked")){
                var addAllBjNotIn = $("#addAllBjNotIn").val();
                var temp = addAllBjNotIn.split(",");
                var ids = [];
                for(var i = 0;i<temp.length;i++){
                    if(temp[i] != ''){
                        ids.push(temp[i]);
                    }
                }
                if(obj.data.rowInfo!=undefined){
                    var id = obj.data.rowInfo.formUserId.toString();
                    if(obj.checked){
                        ids.splice($.inArray(id, ids),1);
                    }else{
                        if($.inArray(id, ids)==-1){
                            ids.push(id);
                        }
                    }
                }else{
                    var checkStatus = table.checkStatus('mtTable3');
                    if(checkStatus.data.length==0){
                        var data = table.cache.mtTable3;
                        for(var i = 0;i<data.length;i++){
                            var allid = data[i].rowInfo.formUserId.toString();
                            if($.inArray(allid, ids)==-1){
                                ids.push(allid);
                            }
                        }
                    }else{
                        for(var i = 0;i<checkStatus.data.length;i++){
                            var notallid = checkStatus.data[i].rowInfo.formUserId.toString();
                            if($.inArray(notallid, ids)>-1){
                                ids.splice($.inArray(notallid, ids),1);
                            }
                        }
                    }
                }
                $("#addAllBjNotIn").val(ids.toString());
            }else{
                var addBjIds = $("#addBjIds").val();
                var temp = addBjIds.split(",");
                var ids = [];
                for(var i = 0;i<temp.length;i++){
                    if(temp[i] != ''){
                        ids.push(temp[i]);
                    }
                }
                if(obj.data.rowInfo!=undefined){
                    var id = obj.data.rowInfo.formUserId.toString();
                    if(obj.checked){
                        if($.inArray(id, ids)==-1){
                            ids.push(id);
                        }
                    }else{
                        ids.splice($.inArray(id, ids),1);
                    }
                }else{
                    var checkStatus = table.checkStatus('mtTable3');
                    if(checkStatus.data.length==0){
                        var data = table.cache.mtTable3;
                        for(var i = 0;i<data.length;i++){
                            var notallid = data[i].rowInfo.formUserId.toString();
                            ids.splice($.inArray(notallid, ids),1);
                        }
                    }else{
                        for(var i = 0;i<checkStatus.data.length;i++){
                            var allid = checkStatus.data[i].rowInfo.formUserId.toString();
                            if($.inArray(allid, ids)==-1){
                                ids.push(allid);
                            }
                        }
                    }
                }
                $("#addBjIds").val(ids.toString());
            }
        });

        table.on('checkbox(materialTable)', function(obj){
            var delids = $("#delids").val();
            var temp = delids.split(",");
            var ids = [];
            for(var i = 0;i<temp.length;i++){
                if(temp[i] != ''){
                    ids.push(temp[i]);
                }
            }
            if(obj.data.rowInfo!=undefined){
                var id = obj.data.id.toString();
                if(obj.checked){
                    if($.inArray(id, ids)==-1){
                        ids.push(id);
                    }
                }else{
                    ids.splice($.inArray(id, ids),1);
                }
            }else{
                var checkStatus = table.checkStatus('materialTable');
                if(checkStatus.data.length==0){
                    var data = table.cache.materialTable;
                    for(var i = 0;i<data.length;i++){
                        var notallid = data[i].id.toString();
                        ids.splice($.inArray(notallid, ids),1);
                    }
                }else{
                    for(var i = 0;i<checkStatus.data.length;i++){
                        var allid = checkStatus.data[i].id.toString();
                        if($.inArray(allid, ids)==-1){
                            ids.push(allid);
                        }
                    }
                }
            }
            $("#delids").val(ids.toString());
        });

        table.on('checkbox(materialTable1)', function(obj){
            var delBjbh = $("#delBjbh").val();
            var temp = delBjbh.split(",");
            var ids = [];
            for(var i = 0;i<temp.length;i++){
                if(temp[i] != ''){
                    ids.push(temp[i]);
                }
            }
            if(obj.data.rowInfo!=undefined){
                var id = obj.data.xsxxBjdm.toString();
                if(obj.checked){
                    if($.inArray(id, ids)==-1){
                        ids.push(id);
                    }
                }else{
                    ids.splice($.inArray(id, ids),1);
                }
            }else{
                var checkStatus = table.checkStatus('materialTable1');
                if(checkStatus.data.length==0){
                    var data = table.cache.materialTable1;
                    for(var i = 0;i<data.length;i++){
                        var notallid = data[i].xsxxBjdm.toString();
                        ids.splice($.inArray(notallid, ids),1);
                    }
                }else{
                    for(var i = 0;i<checkStatus.data.length;i++){
                        var allid = checkStatus.data[i].xsxxBjdm.toString();
                        if($.inArray(allid, ids)==-1){
                            ids.push(allid);
                        }
                    }
                }
            }
            $("#delBjbh").val(ids.toString());
        });
        //
        /*********************************** 学生范围* end ****************************/
        /*********************************** 分配选课名额* end ****************************/
        table.on('edit(mtTable4)',function(obj){
            var oldnum = $(this).prev().text();
            var slecount = obj.data.selnum;
            if(slecount != ''){
                if(isNaN(parseInt(slecount))){
                    alert("请输入数字");
                    $(this).val(oldnum);
                    return false;
                }
                if(parseInt(slecount)<0){
                    alert("选课名额小于0");
                    $(this).val(oldnum);
                    return false;
                }
                var classNum = obj.data.num;
                if(parseInt(slecount)>parseInt(classNum)){
                    alert("选课名额不能大于班级人数");
                    $(this).val(oldnum);
                    return false;
                }
            }
            var json = {};
            var bjdm = obj.data.xsxxBjdm;
            json.bjdm = bjdm;
            var bjmc = obj.data.xsxxSzbj;
            json.bjmc = bjmc;
            json.selcount = slecount;
            $.each(array,function(index,item){
                if(item != undefined && item.bjdm == bjdm){
                    array.splice(index, 1);
                }
            });
            array.push(json);
        });
        function getSlecount(){
            var mtTable4 = [];
            table.render({
                elem: "#mtTable4",
                url: '/elective/studentRange/getTaskCourseStudentBj?formId='+formId+'&formUserId='+formUserId+'&fid='+fid,
                data: mtTable4,
                page: true,
                height: 453,
                cols: [
                    [
                        {
                            field: "xsxxSzbj",
                            title: "班级名称",
                            align: "center",
                            width: 250,
                        },

                        {
                            field: "num",
                            title: "班级人数",
                            align: "center",
                            width: 240,
                        },
                        {
                            field: "selnum",
                            title: "选课名额",
                            align: "center",
                            width: 240,
                            edit: 'textarea'
                        }
                    ],
                ],
                done: function (res, curr, count) { },
            });
        }
        var layerIndex3;
        $("#quotas").click(function () {
            // if (!canUpdateOperate){
            //     alert("该课程的面向对象来自培养过程管理或选修课申报表，无法在此处修改");
            //     return false;
            // }
            getSlecount();
            layerIndex3 = layer.open({
                type: 1,
                title: false,
                closeBtn: false,
                shadeClose: true,
                isOutAnim: true,
                shadeClose: false,
                content: $("#stuQuotas"),
                area: ["auto", "auto"],
            });
        })
        // 取消
        $("#stuQuotas .pu-cancel,#stuQuotas .pu-close").click(function () {
            array = [];
            layer.close(layerIndex3)
        })
        $("#selStu .pu-cancel,#selStu .pu-close").click(function () {
            layer.close(layerIndex2)
        })
        // 确定
        $("#stuQuotas #stuSure2").click(function () {
            $.ajax({
                type: "POST",
                url: '/elective/studentRange/editClassSlecount?formId='+formId+'&formUserId='+formUserId+'&fid='+fid,
                data:{array:JSON.stringify(array)},
                dataType:'json',
                success: function (data) {
                    if(data.status){
                        array = [];
                        searchBjList();
                        layer.close(layerIndex3)
                    }else{
                        alert(data.msg);
                    }
                }
            });
        })
        /*********************************** 分配选课名额* end ****************************/

        /*********************************** 院系分配选课名额* start ****************************/
        table.on('edit(mtTable5)',function(obj){
            var oldnum = $(this).prev().text();
            var slecount = obj.data.selnum;
            if(slecount != ''){
                if(isNaN(parseInt(slecount))){
                    alert("请输入数字");
                    $(this).val(oldnum);
                    return false;
                }
                if(parseInt(slecount)<0){
                    alert("选课名额小于0");
                    $(this).val(oldnum);
                    return false;
                }
                var classNum = obj.data.num;
                if(parseInt(slecount)>parseInt(classNum)){
                    alert("选课名额不能大于院系人数");
                    $(this).val(oldnum);
                    return false;
                }
            }
            var json = {};
            var bjdm = obj.data.xsxxYxdm;
            json.bjdm = bjdm;
            var bjmc = obj.data.xsxxSsyx;
            json.bjmc = bjmc;
            json.selcount = slecount;
            $.each(deptArray,function(index,item){
                if(item != undefined && item.bjdm == bjdm){
                    deptArray.splice(index, 1);
                }
            });
            deptArray.push(json);
        });
        function getYxSlecount(){
            table.render({
                elem: "#mtTable5",
                url: '/elective/studentRange/getTaskCourseStudentYx?formId='+formId+'&formUserId='+formUserId+'&fid='+fid,
                page: true,
                height: 453,
                cols: [
                    [
                        {
                            field: "xsxxSsyx",
                            title: "院系名称",
                            align: "center",
                            width: 250,
                        },
                        {
                            field: "classNum",
                            title: "班级数量",
                            align: "center",
                            width: 120,
                        },
                        {
                            field: "num",
                            title: "院系人数",
                            align: "center",
                            width: 120,
                        },
                        {
                            field: "selnum",
                            title: "选课名额",
                            align: "center",
                            width: 240,
                            edit: 'textarea'
                        }
                    ],
                ],
                done: function (res, curr, count) { },
            });
        }
        var layerIndex4;
        $("#quotas1").click(function () {
            // if (!canUpdateOperate){
            //     alert("该课程的面向对象来自培养过程管理或选修课申报表，无法在此处修改");
            //     return false;
            // }
            getYxSlecount();
            layerIndex4 = layer.open({
                type: 1,
                title: false,
                closeBtn: false,
                shadeClose: true,
                isOutAnim: true,
                shadeClose: false,
                content: $("#deptQuotas"),
                area: ["auto", "auto"],
            });
        })
        // 取消
        $("#deptQuotas .pu-cancel,#deptQuotas .pu-close").click(function () {
            deptArray = [];
            layer.close(layerIndex4)
        })

        // 确定
        $("#deptQuotas #stuSure3").click(function () {
            $.ajax({
                type: "POST",
                url: '/elective/studentRange/editClassSlecount?formId='+formId+'&formUserId='+formUserId+'&fid='+fid+'&type=1',
                data:{array:JSON.stringify(deptArray)},
                dataType:'json',
                success: function (data) {
                    if(data.status){
                        deptArray = [];
                        searchYxList();
                        layer.close(layerIndex4)
                    }else{
                        alert(data.msg);
                    }
                }
            });
        })

        function searchYxList(){
            var nj = $("#selStuNjIp2").val();
            var yx = $("#selStuYxIp2").val();
            var zy = $("#selStuZyIp2").val();
            var bj = $("#selStuBjIp2").val();
            var xq = $("#selStuXqIp2").val();
            var xm = $("#selStuXmIp2").val();
            // 按班级显示
            table.render({
                elem: "#materialTable2",
                url: '/elective/studentRange/getTaskCourseStudentYx?formId='+formId+'&formUserId='+formUserId+'&fid='+fid+'&nj='+nj+'&yx='+yx+'&zy='+zy+'&bj='+bj+'&xq='+xq+'&xm='+xm,
                page: {
                    layout: ["count", "prev", "page", "next", "limit", "skip"],
                },
                cols: [
                    [
                        {
                            type: "checkbox",
                            fixed: "left",
                            width: 80,
                        },
                        {
                            field: "xsxxSsyx",
                            title: "院系名称",
                            align: "center",
                            minWidth: 150,
                        },
                        {
                            field: "optionalName",
                            title: "学生课程关系",
                            align: "center",
                            minWidth: 150,
                        },
                        {
                            field: "xsxxSsnj",
                            title: "所属年级",
                            align: "center",
                            minWidth: 150,
                        },

                        {
                            field: "xsxxSzxq",
                            title: "所在校区",
                            align: "center",
                            minWidth: 150,
                        },
                        {
                            field: "classNum",
                            title: "班级数量",
                            align: "center",
                            minWidth: 150,
                        },
                        {
                            field: "num",
                            title: "院系人数",
                            align: "center",
                            minWidth: 150,
                        },
                        {
                            field: "selnum",
                            title: "选课名额",
                            align: "center",
                            minWidth: 150,
                        },
                        {
                            field: "xsxxYxdm",
                            title: "xsxx_yxdm",
                            align: "center",
                            minWidth: 110,
                            hide:true,
                        },
                        {
                            field: "optional",
                            title: "关系",
                            align: "center",
                            minWidth: 110,
                            hide:true,
                        },
                    ],
                ],
                done: function (res, curr, count) {
                    res.data.forEach(function (item, index) {
                        bjSource.set(item.xsxxBjdm, item.maxSaveSource);
                    });
                },
            });
        }

        table.on('checkbox(materialTable2)', function(obj){
            var delYxbh = $("#delYxbh").val();
            var temp = delYxbh.split(",");
            var ids = [];
            for(var i = 0;i<temp.length;i++){
                if(temp[i] != ''){
                    ids.push(temp[i]);
                }
            }
            if(obj.data.rowInfo!=undefined){
                var id = obj.data.xsxxYxdm.toString();
                if(obj.checked){
                    if($.inArray(id, ids)==-1){
                        ids.push(id);
                    }
                }else{
                    ids.splice($.inArray(id, ids),1);
                }
            }else{
                var checkStatus = table.checkStatus('materialTable2');
                if(checkStatus.data.length==0){
                    var data = table.cache.materialTable2;
                    for(var i = 0;i<data.length;i++){
                        var notallid = data[i].xsxxYxdm.toString();
                        ids.splice($.inArray(notallid, ids),1);
                    }
                }else{
                    for(var i = 0;i<checkStatus.data.length;i++){
                        var allid = checkStatus.data[i].xsxxYxdm.toString();
                        if($.inArray(allid, ids)==-1){
                            ids.push(allid);
                        }
                    }
                }
            }
            $("#delYxbh").val(ids.toString());
        });
        /*********************************** 院系分配选课名额* end ****************************/
    });

</script>
<!--<script th:src="${_CPR_}+'/elective/js/stuRange.js?v=3'"></script>-->
</html>