<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" li-content="IE=edge">
    <meta name="viewport" li-content="width=device-width, initial-scale=1.0">
    <title>微点名</title>
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/global.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/layui/css/layui.css'">
    <link rel="stylesheet" href="css/commonPC.css">
    <link rel="stylesheet" href="css/reset.css">
    <link rel="stylesheet" href="css/poup.css">
    <link rel="stylesheet" href="css/microdot-name_pc_jxb.css"> <!-- 2024.12.5 -->
    <link rel="stylesheet" href="css/new-microdot-name.css"> <!-- 2025.3.20 新增 -->
    <script th:src="${_CPR_}+'/elective/layui/layui.js'"></script>
    <style>
        body .main .con .table {
            padding-left: 30px;
        }


        body .main .con .filter-box.border {
            display: block;
            border-bottom: 1px solid rgba(232, 235, 241, 1);
        }

        body .main .con .filter-box.filter {
            justify-content: flex-start;
            padding-top: 24px;
        }

        body .main .con .filter-box.filter .layui-form .layui-form-item {
            margin-bottom: 32px;
        }

        .main .con .table .detail span i.color6 {
            color: rgba(175, 119, 255, 1);
        }
    </style>
</head>

<body>
<div class="main">
    <div class="top">
        <div class="title">
            <div class="back"><a href="#" onclick="javascript:window.history.back();">返回</a></div>
            <div class="levelone">微点名统计</div>
            <!--            <div class="icon"></div>-->
            <!--            <div class="leveltwo">[[${teachingClassName}]]</div>-->
        </div>
    </div>
    <div class="con">
        <!-- 2025.3.20 start -->
        <div class="filter-box border">
            <div class="inform">
                <div class="i-item" style="margin-right:127px;">
                    <div class="name">教学班名称：</div>
                    <div class="text">[[${teachingClassName}]]</div>
                </div>
                <div class="i-item" style="margin-right:127px;">
                    <div class="name">课程名称：</div>
                    <div class="text">[[${course}]]</div>
                </div>
                <div class="i-item" style="margin-right:127px;">
                    <div class="name">老师：</div>
                    <div class="text">[[${teacherName}]]</div>
                </div>
                <div class="i-item" style="margin-right:127px;">
                    <div class="name">班级：</div>
                    <div class="text" id="clazzName">-</div>
                </div>
            </div>
            <div class="inform">
                <div class="i-item" style="margin-right:127px;">
                    <div class="name">周次：</div>
                    <div class="text" id="zc">-</div>
                </div>
                <div class="i-item" style="margin-right:127px;">
                    <div class="name">星期：</div>
                    <div class="text" id="xq">-</div>
                </div>
                <div class="i-item" style="margin-right:127px;">
                    <div class="name">课节：</div>
                    <div class="text" id="kj">-</div>
                </div>
                <div class="i-item" style="margin-right:127px;">
                    <div class="name">日期：</div>
                    <div class="text" id="courseTime">-</div>
                </div>
            </div>

        </div>
        <!-- 2025.3.20  end-->
        <!-- 2024.12.5 -->
        <div class="filter-box filter">
            <form class="layui-form" action="" id="example" lay-filter="myFormFilter">
                <div class="layui-form-item">
                    <label class="layui-form-label">学生姓名</label>
                    <div class="layui-input-block w240">
                        <input type="text" name="studentName" id="keyWord" class="layui-input" placeholder="请输入">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">学生学号</label>
                    <div class="layui-input-block w240">
                        <input type="text" name="studentId" id="uname" class="layui-input" placeholder="请输入">
                    </div>
                </div>
                <div class="button-list" style="margin-top: -20px">
                    <!--                <div class="reset">重置</div>-->
                    <div class="reset" lay-filter="formDemo" id="search">查询</div>
                </div>
                <!--                <div class="layui-form-item">-->
                <!--                    <label class="layui-form-label">点名状态</label>-->
                <!--                    <div class="layui-input-block w240 sel">-->
                <!--                        <div class="select-input">-->
                <!--                            <div class="name" data-name="请选择点名状态" id="selectState">请选择点名状态</div>-->
                <!--                            <em></em>-->
                <!--                            <div class="select-dropdown">-->
                <!--                                <div class="all-selects">-->
                <!--                                    全选-->
                <!--                                </div>-->
                <!--                                <ul class="dropdown-lists">-->
                <!--                                    <li id="statezc">-->
                <!--                                        <span>正常</span>-->
                <!--                                    </li>-->
                <!--                                    <li id="stateqj">-->
                <!--                                        <span>请假</span>-->
                <!--                                    </li>-->
                <!--                                    <li id="statekk">-->
                <!--                                        <span>旷课</span>-->
                <!--                                    </li>-->
                <!--                                    <li id="statecd">-->
                <!--                                        <span>迟到</span>-->
                <!--                                    </li>-->
                <!--                                    <li id="statezt">-->
                <!--                                        <span>早退</span>-->
                <!--                                    </li>-->
                <!--                                </ul>-->
                <!--                            </div>-->
                <!--                        </div>-->
                <!--                    </div>-->
                <!--                </div>-->
            </form>
            <!--            <div class="button-list">-->
            <!--&lt;!&ndash;                <div class="reset">重置</div>&ndash;&gt;-->
            <!--                <div class="searcher" lay-filter="formDemo" id="search">查询</div>-->
            <!--            </div>-->

        </div>
        <div class="filter-box">
            <div class="inform" id="allStateNum">
                <div class="i-item">
                    <div class="name">正常：</div>
                    <div class="text" th:inline="text" style="color: #4d88ff;">[[${zcNum}]]</div>
                </div>
            </div>
        </div>
        <div class="table">
            <div class="tab-cons roll-call-record">
                <table class="layui-table" id="materialTable" lay-filter="materialTable"></table>
            </div>

            <div id="coursePage"></div>
        </div>


    </div>
</div>


</body>

<script th:src="${_CPR_}+'/elective/js/jquery-3.3.1.min.js'"></script>
<script th:inline="javascript">
    let pageIndex = 1;
    let pageSize = 10;
    var teachingClassCode = [[${teachingClassCode}]];
    var courseTime = [[${courseTime}]];

    var course = [[${course}]];
    var clazzCode = [[${clazzCode}]];
    var clazzName = [[${clazzName}]];
    var zc = [[${zc}]];
    var xq = [[${xq}]];
    var kj = [[${kj}]];

    if (clazzName) {
        $("#clazzName").text(clazzName)
    }
    if (courseTime) {
        $("#courseTime").text(courseTime)
    }
    if (zc) {
        $("#zc").text("第" + zc + "周")
    }
    if (xq) {
        $("#xq").text("星期" + xq)
    }
    if (kj) {
        $("#kj").text("第" + kj + "节")
    }


    var rollcallStateList = [[${rollcallStateList}]];
    var stateColorArr = ["color: #ffb026;", "color: #f76560;", "color: #5ab176;", "color: #ff99b1;", "color: #af77ff;"];

    for (let i = 0; i < rollcallStateList.length; i++) {
        let stateA = rollcallStateList[i].stateAlias;
        if (rollcallStateList[i].type == 1) {
            stateA = rollcallStateList[i].id;
        }
        var stateNumHtml = "";
        stateNumHtml += "<div class='i-item'>";
        stateNumHtml += "<div class='name'>" + rollcallStateList[i].stateNamePc + "：</div>";
        stateNumHtml += "<div class='text' style='" + stateColorArr[i] + "' id='state" + stateA + "Num'>0</div>";
        stateNumHtml += "</div>";
        $("#allStateNum").append(stateNumHtml);
        $.ajax({
            type: "POST",
            url: "/elective/rollcall/getRollcallStateNum",
            data: {teachingClassCode: teachingClassCode, courseTime: courseTime, zc: zc, xq: xq, kj: kj, state: stateA},
            dataType: 'json',
            success: function (data) {
                $("#state" + stateA + "Num").text(data.data);
            }
        })
    }

    layui.use(['table', 'jquery', 'laypage', 'laydate', 'form'], function () {

        var table = layui.table;
        var laypage = layui.laypage;
        var $ = layui.jquery;
        var layer = layui.layer;
        var laydate = layui.laydate;
        var form = layui.form;
        //2024.12.5
        resizeHh();

        // $('.filter-box .button-list .reset').click(function () {
        //     console.log("重置");
        //
        //     form.val("myFormFilter", { // myFormFilter 是表单定义的lay-filter的值
        //         "studentName": '',
        //         "studentId": '',
        //         "callStatus": '',
        //     });
        //
        //     layui.form.render();
        //     let placeholdertext = $(
        //         ".filter-box .layui-form .layui-form-item .sel .select-input .name.ckd").attr(
        //         "data-name");
        //     $(".filter-box .layui-form .layui-form-item .sel .select-input .name.ckd").removeClass(
        //         "ckd").text(placeholdertext);
        //     $(".filter-box .layui-form .layui-form-item .sel .select-input .select-dropdown .dropdown-lists li")
        //         .removeClass("cur");
        //     $(".filter-box .layui-form .layui-form-item .sel .select-input .select-dropdown .all-selects.cur")
        //         .removeClass("cur").text("全选");
        // });

        function resizeHh() {
            let hh = $(".roll-call-record").offset().top + 106;
            let cuurent = $(window).height() - hh;
            $(".roll-call-record").css("min-height", cuurent);
        }

        $(window).resize(function () {
            resizeHh();
        });


        // 2024.2.4
        $(document).on("click", function (event) {
            var _con = $('.select-input');
            if (!_con.is(event.target) && _con.has(event.target).length === 0) {
                $(".select-input").removeClass("clicked");
            }
        })
        //下拉点击
        $(".select-input .name").click(function (e) {
            $(this).parent().toggleClass("clicked");
            stopBubble(e);
        })


        //多选下拉
        $(".sel .select-input").on("click", ".select-dropdown .dropdown-lists li", function () {
            if ($(this).parent().hasClass("dropdown-lists-single")) {
                let txt = $(this).text();
                $(this).parents(".select-input").find(".name").addClass("ckd").text(txt);
                $(this).addClass('cur').siblings().removeClass('cur');
                $(this).parents(".clicked").toggleClass("clicked");
            } else {
                let txtarr = [];
                $(this).toggleClass("cur");
                $(this).parent().find("li").each(function () {
                    if ($(this).hasClass("cur")) {
                        txtarr.push($(this).find("span").text());
                    }
                })
                let totals = $(this).parents(".dropdown-lists").find("li").length;
                if (txtarr.length == 0) {
                    let nameText = $(this).parents(".select-input").find(".name").attr('data-name');
                    $(this).parents(".select-input").find(".name").removeClass("ckd").text(nameText)
                } else if (totals == txtarr.length) {
                    $(this).parents(".select-dropdown").find(".all-selects").addClass("cur").text("取消全选");
                    $(this).parents(".select-input").find(".name").addClass("ckd").text(txtarr.join(","));
                } else {
                    $(this).parents(".select-dropdown").find(".all-selects").removeClass("cur").text("全选");
                    $(this).parents(".select-input").find(".name").addClass("ckd").text(txtarr.join(","));
                }
            }
        })


        //全选
        $("body").on("click", ".sel .select-input .select-dropdown .all-selects", function () {
            $(this).toggleClass("cur");
            if ($(this).hasClass("cur")) {
                $(this).text("取消全选");
                $(this).next().find("li").addClass("cur");
                let txtarr = [];
                $(this).next().find("li").each(function () {
                    txtarr.push($(this).find("span").text());
                })
                $(this).parents(".select-input").find(".name").addClass("ckd").text(txtarr.join(","));

            } else {
                $(this).text("全选");
                $(this).next().find("li").removeClass("cur");

                let nameText = $(this).parents(".select-input").find(".name").attr('data-name');
                $(this).parents(".select-input").find(".name").removeClass("ckd").text(nameText);
            }

        })

        //输入框筛选

        $(".sel .select-input .select-dropdown .search input").on("keyup", function () {
            var value = $(this).val().toLowerCase();
            var obj = $(this).parents(".select-dropdown").find(".dropdown-lists li");
            obj.filter(function () {
                $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
            });
        });
        // if (state!=''){
        //     $("#state"+state).click();
        // }
        $("#keyWord,#uname").keyup(function (e) {
            if (e.keyCode == 13) {
                pageIndex = 1;
                table.reload('materialTable', {
                    where: {
                        teachingClassCode: teachingClassCode,
                        curPage: pageIndex,
                        pageSize: pageSize,
                        courseTime: courseTime,
                        keyword: $("#keyWord").val(),
                        xh: $("#uname").val(),
                    }
                }); //只重载数据
            }
        })

        $("#search").click(function () {
            pageIndex = 1;
            table.reload('materialTable', {
                where: {
                    teachingClassCode: teachingClassCode,
                    curPage: pageIndex,
                    pageSize: pageSize,
                    courseTime: courseTime,
                    keyword: $("#keyWord").val(),
                    xh: $("#uname").val(),
                }
            }); //只重载数据
        })
        var titleArr = [{
            field: "studentRealname",
            align: "center",
            title: "姓名",
        },
            {
                field: "studentCodeDesensitize",
                align: "center",
                title: "学号",
            },
            {
                field: "zcNum",
                align: "center",
                title: "正常",
            }];
        for (let i = 0; i < rollcallStateList.length; i++) {
            titleArr.push({
                align: "center",
                title: rollcallStateList[i].stateNamePc,
                templet: function (d) {
                    return d.stateNum[i];
                }
            });
        }
        //表格

        var table1 = table.render({
            elem: "#materialTable",
            id: 'materialTable',
            url: '/elective/rollcall/getJxbStaticStudentsPage', //数据接口
            where: {
                teachingClassCode: teachingClassCode,
                curPage: pageIndex,
                pageSize: pageSize,
                courseTime: courseTime,
                keyword: $("#keyWord").val(),
                xh: $("#uname").val(),
                clazzCode: clazzCode,
                zc: zc,
                xq: xq,
                kj: kj,
            },
            cols: [titleArr],
            done: function (res) {
                laypage.render({
                    elem: 'coursePage',
                    curr: pageIndex,
                    groups: 5,
                    limit: pageSize,
                    limits: [10, 20, 30],
                    count: res.count,
                    layout: ['count', 'prev', 'page', 'next', 'limit', 'limits', 'skip'],
                    jump: function (obj, first) {
                        if (!first) {
                            pageIndex = obj.curr;
                            pageSize = obj.limit;
                            table.reload('materialTable', {
                                where: {
                                    teachingClassCode: teachingClassCode,
                                    curPage: pageIndex,
                                    pageSize: pageSize,
                                    courseTime: courseTime,
                                    keyword: $("#keyWord").val(),
                                    xh: $("#uname").val(),
                                    clazzCode: clazzCode,
                                    zc: zc,
                                    xq: xq,
                                    kj: kj,
                                }
                            });
                        }
                    }
                });
            }
        })


        function stopBubble(e) {
            if (e && e.stopPropagation)
                e.stopPropagation();
            else {
                window.event.cancelBubble = true;
            }
        }


    });
</script>

</html>