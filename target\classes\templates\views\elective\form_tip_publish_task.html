<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>进度条</title>
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/global.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/progress.css'">
</head>

<body>

<div class="mask"></div>
<div class="pouop progressPup">
    <div class="title">选课计划发布中...</div>
    <div class="progress-container">
        <div class="progress-bar" id="progressBar">
            <div class="progress-text" id="progressText">0%</div>
        </div>
    </div>
</div>
</body>

<script th:src="${_CPR_}+'/elective/js/jquery-3.3.1.min.js'"></script>
<script th:src="${_CPR_}+'/elective/js/progressBar.js'"></script>
<script th:inline="javascript">
    var formUserId = [[${formUserId}]];
    // 初始化进度条
    $('#progressBar').maskProgressBar({
        value: 0,
        animationSpeed: 800,
        onUpdate: function (value) {
            $('#progressText').text(Math.round(value) + '%');
        },
        onComplete: function () {
            $('#progressText').html('完成!');
            setTimeout(function () {
                window.parent.postMessage(JSON.stringify({action: 1}), "*");
            }, 2000);
        }
    });
    $(".progressPup").show();
    $(".mask").show();
    // 获取进度条实例
    const progressBar = $('#progressBar').data('maskProgressBar');
    let interval = setInterval(function () {
        $.ajax({
            type: "get",
            url: "/elective/task/publishTask/progress",
            data: {formUserId: formUserId},
            dataType: 'json',
            success: function (data) {
                if (data.status) {
                    if (data.data == 'ok') {
                        progressBar.start(100);
                        clearInterval(interval);
                    } else if (data.data == '100') {
                        progressBar.start(99);
                    }else {
                        progressBar.start(data.data);
                    }
                } else {
                    clearInterval(interval);
                    alert(data.msg)
                }
            }
        });
    }, 1000);

    // clearInterval(interval);


</script>

</html>