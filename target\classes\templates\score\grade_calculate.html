<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>成绩管理</title>
    <link rel="stylesheet" href="/score/css/global.css">
    <link rel="stylesheet" href="/plugin/layui-v2.8.18/layui/css/layui.css">
    <link rel="stylesheet" href="/score/css/common.css">
    <link rel="stylesheet" href="/score/css/reset.css">
    <link rel="stylesheet" href="/score/css/indexRule.css">

    <style>
        #gsbjsubitem .layui-form-select dl {
            position: fixed;
            width: 240px;
            min-width: auto;
            margin-top: 4px;
        }
        .layui-table-grid-down {

            display: none;

        }
    </style>

</head>

<body>

<div class="main">
    <div class="content calculRuleSet">
        <form id="savegrdcalcuRuleForm">
            <input type="hidden" id="grdcalcuRule_id" name="id" th:value="${grdcalcuRule?.id}">
            <input type="hidden" id="grdcalcuRule_fid" name="fid" th:value="${fid}">
            <input type="hidden" id="grdcalcuRule_xnxq" name="xnxq" th:value="${xnxq}">
            <input type="hidden" id="grdcalcuRule_createUid" name="createUid" th:value="${uid}">
            <input type="hidden" id="grdcalcuRule_decimalLen" name="decimalLen" th:value="${grdcalcuRule?.decimalLen}">
            <input type="hidden" id="grdcalcuRule_halfAdjustFlag" name="halfAdjustFlag" th:value="${grdcalcuRule?.halfAdjustFlag}">
            <input type="hidden" id="grdcalcuRule_formulaFlag" name="formulaFlag" th:value="${grdcalcuRule?.formulaFlag}">
            <input type="hidden" id="grdcalcuRule_teachereditFlag" name="teachereditFlag" th:value="${grdcalcuRule?.teachereditFlag}">
            <input type="hidden" id="grdcalcuRule_gradeShowType" name="gradeShowType" th:value="${grdcalcuRule?.gradeShowType}">
        </form>
        <div class="c-top">
            <h2>成绩计算规则模板设置</h2>
        </div>
        <div class="form-box">
            <form class="layui-form" lay-filter="switch-form">
                <div class="layui-form-item" style="margin-bottom:20px;">
                    <label class="layui-form-label">是否启用公式编辑：</label>
                    <div class="layui-input-block ">
                        <div class="limit-switch" id="sfqyEdit">
                            <input type="checkbox" name="switchormuladiting" th:checked="${grdcalcuRule?.formulaFlag == 1}" lay-skin="switch"
                                   lay-filter="switchormuladiting">
                            <span th:text="${grdcalcuRule?.formulaFlag == 1} ? '是':'否'">否</span>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="add-lab">
            <span>添加</span>
        </div>
        <div class="tabWrap">
            <table class="layui-table" id="main-table1" lay-filter="dataTable1" lay-data='{page: true}'>
            </table>
        </div>
        <div class="c-top">
            <h2>高级设置</h2>
        </div>

        <div class="form-box">
            <form class="layui-form " lay-filter="editForm">
                <div class="layui-form-item">
                    <label class="layui-form-label">是否允许任课教师自行创建成绩计算规则：</label>
                    <div class="layui-input-block ">
                        <div class="limit-switch">
                            <input type="checkbox" name="switchTeacherFilter" th:checked="${grdcalcuRule?.teachereditFlag == 1}" lay-skin="switch"
                                   lay-filter="switchTeacherFilter">
                            <span th:text="${grdcalcuRule?.teachereditFlag == 1} ? '是':'否'">否</span>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">成绩保留小数位数：</label>
                    <div class="layui-input-block w60">
                        <input type="number" name="addpName" placeholder="" th:value="${grdcalcuRule?.decimalLen}" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">成绩是否需要四舍五入：</label>
                    <div class="layui-input-block">
                        <div class="limit-switch">
                            <input type="checkbox" name="switchGradeFilter" th:checked="${grdcalcuRule?.halfAdjustFlag == 1}" lay-skin="switch"
                                   lay-filter="switchGradeFilter">
                            <span th:text="${grdcalcuRule?.halfAdjustFlag == 1} ? '是':'否'">否</span>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">总分成绩显示规则：</label>
                    <div class="layui-input-block">
                        <input type="radio" name="subitem" lay-filter="subitemSwitch" th:checked="${grdcalcuRule?.gradeShowType == 0}" value="0" title="系统计算" checked="">
                        <input type="radio" name="subitem" lay-filter="subitemSwitch" th:checked="${grdcalcuRule?.gradeShowType == 1}" value="1" title="人工录入">
                    </div>
                </div>

            </form>
        </div>
    </div>
</div>

<!-- 添加 -->
<div id="calcAddPoups" class="addPoups popup">
    <div class="title">
        <div class="name">添加</div>
        <div class="close"></div>
    </div>
    <div class="popup-con">
        <form class="layui-form flex-form" lay-filter="addeditForm">
            <input type="hidden" id="ruletempId" value="">
            <input type="hidden" id="curformulatype" value="">
            <div class="layui-form-item">
                <label class="layui-form-label"><em>*</em>模板名称</label>
                <div class="layui-input-block w240">
                    <input type="text" id="templateName" name="templateName" lay-filter="templateName" placeholder="请输入"
                           autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><em>*</em>是否启用：</label>
                <div class="layui-input-block">

                    <div class="limit-switch">
                        <input type="checkbox" id="switchGradeFilter1" name="switchGradeFilter1" lay-skin="switch"
                               lay-filter="switchGradeFilter1">
                        <span>否</span>
                    </div>
                </div>
            </div>
        </form>
        <h3><em>*</em>计算公式</h3>
        <div class="add-lab">
            <span>添加</span>
        </div>
        <div class="table table-box1">
            <table class="layui-table" id="main-table2" lay-filter="dataTable2">
            </table>
        </div>
        <div class="table table-box2" style="display:none;">
            <table class="layui-table" id="main-table3" lay-filter="dataTable3">
            </table>
        </div>
        <div class="layui-form" id="totalLevel">
            <div class="layui-form-item">
                <label class="layui-form-label"><em>*</em>总分计分级制</label>
                <div class="layui-input-block w240">
                    <select name="modules" id="pointsRuleType" lay-verify="required">
                        <option value="">请选择</option>
                        <option th:each="info : ${gradeMakeRuleList}" th:attr="value=${info.id},name=${info.gradeName}"
                                th:text="${info.gradeName}"></option>
                    </select>
                </div>
            </div>
        </div>
    </div>
    <div class="bottom">
        <button type="reset" class="layui-btn layui-btn-primary exam-cancle">取消</button>
        <button class="layui-btn exam-sure" id="add-score-grdcalcu-ruletemp">确定</button>
    </div>

</div>

<!-- 公式编辑 -->
<div id="gsbjsubitem" class="subitem popup">
    <div class="title">
        <div class="name">公式编辑</div>
        <div class="close"></div>
    </div>
    <div class="popup-con">
        <form class="layui-form flex-form" lay-filter="addeditForm">
            <input type="hidden" id="totaluuid" value="">
            <div class="layui-form-item" style="margin-bottom:24px;">
                <label class="layui-form-label"><em>*</em>计分级制：</label>
                <div class="layui-input-block w240">
                    <div class="select">
                        <select name="modules" id="total-jfjz-filter" lay-verify="required">
                            <option value="">请选择</option>
                            <option th:each="info : ${gradeMakeRuleList}" th:attr="value=${info.id},name=${info.gradeName}"
                                    th:text="${info.gradeName}"></option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="lable">
                <h3><em>*</em>计算公式：</h3>
                <div class="formula">
                    <div class="name">总分 =</div>
                    <div class="f-con" id="total-formula-content"></div>
                </div>
            </div>
            <div class="grade-keyboard">
                <div class="score-breakdown">
                    <div class="sb-top">
                        <div class="titles">成绩分项</div>
                        <div class="tips"><span>请先选中成绩分项，再选择下拉框内的计分级制</span></div>
                        <div class="limit-switch">
                            <span>显示二级分项</span>
                            <input type="checkbox" name="switch_filter" lay-skin="switch"
                                   lay-filter="switch_filter">
                        </div>
                    </div>
                    <div class="sb-cons scrollBox">
                        <div class="sb-list" id="total-cjfx-ul-list">
                            <div class="item" th:each="info : ${itemDictionaryList}">
                                <div class="i-con">
                                    <div class="left pgrade">
                                        <div class="name" th:attr="gid=${info.id}" th:text="${info.itemName}"></div>
                                    </div>
                                    <div class="right scrollBox1">
                                        <ul>
                                            <li th:each="subs : ${info.subsList}">
                                                <div class="name" th:attr="gid=${subs.id}" th:text="${subs.itemName}"></div>
                                                <div class="select w102">
                                                    <select name="modules" lay-filter="selctOnchange4" disabled
                                                            lay-verify="required">
                                                        <option value="">请选择</option>
                                                        <option th:each="info : ${gradeMakeRuleList}" th:attr="value=${info.id},name=${info.gradeName}"
                                                                th:text="${info.gradeName}"></option>
                                                    </select>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="i-bottom">
                                    <div class="select">
                                        <select name="modules total-bottom"  disabled lay-filter="oneSelect1" lay-verify="required">
                                            <option value="">请选择</option>
                                            <option th:each="info : ${gradeMakeRuleList}" th:attr="value=${info.id},name=${info.gradeName}"
                                                    th:text="${info.gradeName}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="keyboard">
                    <h3>计算键盘</h3>
                    <div class="k-con">
                        <span class="num">7</span>
                        <span class="num">8</span>
                        <span class="num">9</span>
                        <span class="delet"></span>
                        <span class="empty">清空</span>
                        <span class="num">4</span>
                        <span class="num">5</span>
                        <span class="num">6</span>
                        <span class="sign lbracket">(</span>
                        <span class="sign rbracket">)</span>
                        <span class="num">1</span>
                        <span class="num">2</span>
                        <span class="num">3</span>
                        <span class="sign sign-cancle">－</span>
                        <span class="sign sign-add">+</span>
                        <span class="num zero">0</span>
                        <span class="num spot">.</span>
                        <span class="sign sign-mul">×</span>
                        <span class="sign sign-except">÷</span>
                    </div>
                </div>
            </div>
        </form>

    </div>
    <div class="bottom">
        <button type="reset" class="layui-btn layui-btn-primary exam-cancle">取消</button>
        <button class="layui-btn exam-sure" id="total-formula-sure">确定</button>
    </div>

</div>

<div id="subitemadd" class="subitemadd popup">
    <div class="title">
        <div class="name">新增</div>
        <div class="close"></div>
    </div>
    <div class="popup-con">
        <div class="layui-form" lay-filter="secondaryEditForm">
            <input id="grdcalcuRuletempScoreruleid" type="hidden" value="">
            <div class="layui-form-item">
                <label class="layui-form-label"><em>*</em>成绩分项名称：</label>
                <div class="layui-input-block w275">
                    <select id="itemDictionary" lay-filter="itemDictionary" lay-verify="required">
                        <option value="">请选择</option>
                        <option th:each="info : ${itemDictionaryList}" th:attr="value=${info.id},name=${info.itemName},code=${info.itemCode}"
                                th:text="${info.itemName}"></option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><em>*</em>计分级制：</label>
                <div class="layui-input-block w275">
                    <select id="gradeMakeRule" lay-filter="gradeMakeRule" lay-verify="required">
                        <option value="">请选择</option>
                        <option th:each="info : ${gradeMakeRuleList}" th:attr="value=${info.id},name=${info.gradeName}"
                                th:text="${info.gradeName}"></option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><em>*</em>占比(%)</label>
                <div class="layui-input-block w275">
                    <input type="number" name="seconCode" lay-filter="seconCode" lay-verify="number" placeholder="请输入" autocomplete="off"
                           class="layui-input">
                </div>
            </div>
        </div>
    </div>
    <div class="bottom">
        <button type="reset" class="layui-btn layui-btn-primary exam-cancle">取消</button>
        <button class="layui-btn exam-sure" id="add-score-grdcalcu-ruletemp-scorerule">确定</button>
    </div>
</div>

<!-- 公式编辑2 -->
<div id="gsbjsubitemTwo" class="subitem popup">
    <div class="title">
        <div class="name">公式编辑</div>
        <div class="close"></div>
    </div>
    <div class="popup-con">
        <form class="layui-form flex-form" lay-filter="addeditForm1">
            <input type="hidden" id="grdcalcuRuletempScoreruleid_1" value="">
            <div class="layui-form-item" style="margin-bottom:24px;">
                <label class="layui-form-label"><em>*</em>公式选择：</label>
                <div class="layui-input-block w240">
                    <div class="select">
                        <select name="modules" id="gsxz-filter" lay-filter="gsxz-filter" lay-verify="required">
                            <option value="">请选择</option>
                            <option th:each="info : ${itemDictionaryList}" th:if="${#lists.size(info.subsList) > 0}"
                                    th:attr="value=${info.id},name=${info.itemName},code=${info.itemCode}"
                                    th:text="${info.itemName}"></option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="layui-form-item" style="margin-bottom:24px;">
                <label class="layui-form-label"><em>*</em>计分级制：</label>
                <div class="layui-input-block w240">
                    <div class="select">
                        <select name="modules" id="jfjz-filter" lay-filter="jfjz-filter" lay-verify="required">
                            <option value="">请选择</option>
                            <option th:each="info : ${gradeMakeRuleList}" th:attr="value=${info.id},name=${info.gradeName}"
                                    th:text="${info.gradeName}"></option>
                        </select>
                    </div>
                </div>
            </div>
        </form>
        <form class="layui-form" lay-filter="addeditForm2">

            <div class="lable">
                <h3><em>*</em>计算公式：</h3>
                <div class="formula">
                    <div class="name">平时 =</div>
                    <div class="f-con" id="formula-content"></div>
                </div>
            </div>
            <div class="grade-keyboard">
                <div class="score-breakdown">
                    <div class="sb-top">
                        <div class="titles">成绩分项</div>
                        <div class="tips"><span>请先选中成绩分项，再选择下拉框内的计分级制</span></div>
                    </div>
                    <div class="sb-cons scrollBox">
                        <div class="sb-list">
                            <ul id="cjfx-ul-list">

                            </ul>
                        </div>
                    </div>
                </div>
                <div class="keyboard">
                    <h3>计算键盘</h3>
                    <div class="k-con">
                        <span class="num">7</span>
                        <span class="num">8</span>
                        <span class="num">9</span>
                        <span class="delet"></span>
                        <span class="empty">清空</span>
                        <span class="num">4</span>
                        <span class="num">5</span>
                        <span class="num">6</span>
                        <span class="sign lbracket">(</span>
                        <span class="sign rbracket">)</span>
                        <span class="num">1</span>
                        <span class="num">2</span>
                        <span class="num">3</span>
                        <span class="sign sign-cancle">－</span>
                        <span class="sign sign-add">+</span>
                        <span class="num zero">0</span>
                        <span class="num spot">.</span>
                        <span class="sign sign-mul">×</span>
                        <span class="sign sign-except">÷</span>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="bottom">
        <button type="reset" class="layui-btn layui-btn-primary exam-cancle">取消</button>
        <button class="layui-btn exam-sure" id="formula-sure">确定</button>
    </div>
</div>

<script type="text/html" id="barDemo1">
    <a style="color:#3A8BFF;;cursor:pointer;margin-right:8px;" lay-event="edit">编辑</a>
    <a style="color:#F33131;cursor:pointer;" lay-event="del">删除</a>
</script>
<script type="text/html" id="barDemo4">
    <a style="color:#3A8BFF;;cursor:pointer;margin-right:8px;" lay-event="edit">编辑</a>
    <a style="color:#F33131;cursor:pointer;" lay-event="del">删除</a>
</script>
<script type="text/html" id="barDemo5">
    <a style="color:#3A8BFF;;cursor:pointer;margin-right:8px;" lay-event="edit">公式编辑</a>
    <a style="color:#F33131;cursor:pointer;" lay-event="del">删除</a>
</script>
<script src="/js/jquery-3.3.1.min.js"></script>
<script src="/js/jquery.mCustomScrollbar.concat.min.js"></script>
<script src="/plugin/layui-v2.8.18/layui/layui.js"></script>
<script src="/score/js/common.js"></script>
<script type="text/javascript" th:src="@{~/js/my.util.js}"></script>
<script th:inline="javascript">
    var gradeMakeRuleList = [[${gradeMakeRuleList}]];
    var calcAddPoupsindex;
    var subitemaddindex;
    var main_table2_dataArr = [];
    var main_table3_dataArr = [];
    var gsbjsubitemTwoindex;
    var gsbjsubitemindex;
    $(function () {
        var form, table, laydate, layer;
        layui.use(['form', 'table', 'laydate'],
            function () {
                var $ = layui.jquery;
                form = layui.form;
                table = layui.table;
                laydate = layui.laydate;
                layer = layui.layer;

                //开关监听
                form.on('switch(switchormuladiting)', function (data) {
                    if (data.elem.checked) {
                        $(this).parent().find("span").text('是');
                        $("#grdcalcuRule_formulaFlag").val(1);
                    } else {
                        $(this).parent().find("span").text('否');
                        $("#grdcalcuRule_formulaFlag").val(0);
                    }
                    savegrdcalcuRule();
                });

                form.on('radio(subitemSwitch)', function (data) {
                    $("#grdcalcuRule_gradeShowType").val(data.value);
                    savegrdcalcuRule();
                });

                form.on('switch(switch_filter)', function (data) {
                    if (data.elem.checked) {
                        $("#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item")
                            .addClass("showMore");
                    } else {
                        $("#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item")
                            .removeClass("showMore");
                    }
                });

                form.on('switch(switchTeacherFilter)', function (data) {
                    if (data.elem.checked) {
                        $(this).parent().find("span").text('是');
                        $("#grdcalcuRule_teachereditFlag").val(1);
                    } else {
                        $(this).parent().find("span").text('否');
                        $("#grdcalcuRule_teachereditFlag").val(0);
                    }
                    savegrdcalcuRule();
                });

                form.on('switch(switchGradeFilter)', function (data) {
                    if (data.elem.checked) {
                        $(this).parent().find("span").text('是');
                        $("#grdcalcuRule_halfAdjustFlag").val(1);
                    } else {
                        $(this).parent().find("span").text('否');
                        $("#grdcalcuRule_halfAdjustFlag").val(0);
                    }
                    savegrdcalcuRule();
                });

                form.on('switch(switchGradeFilter1)', function (data) {
                    if (data.elem.checked) {
                        $(this).parent().find("span").text('是');
                    } else {
                        $(this).parent().find("span").text('否');
                    }
                });

                $("input[name='addpName']").blur(function(){
                    var len = $(this).val();
                    $("#grdcalcuRule_decimalLen").val(len);
                    savegrdcalcuRule();
                })

                form.on('select(gsxz-filter)', function (data) {
                    var value = data.value;
                    $.ajax({
                        type: "post",
                        url: "/api/score/itemDictionary/getList",
                        data: {'fid': $("#grdcalcuRule_fid").val(), "pid": value},
                        dataType: 'json',
                        success: function (result) {
                            var html = "";
                            for(var i = 0; i < result.data.length; i++){
                                html += "<li gid='"+result.data[i].id+"'>";
                                html += "<div class='name'>"+result.data[i].itemName+"</div>";
                                html += "<div class='select w102'>";
                                html += "<select name='modules' lay-filter='selctOnchanges1' disabled lay-verify='required'>";
                                html += "<option value=''>请选择</option>";
                                for (let i = 0; i < gradeMakeRuleList.length; i++) {
                                    html += "<option value='"+gradeMakeRuleList[i].id+"'>"+gradeMakeRuleList[i].gradeName+"</option>";
                                }
                                html += "</select></div></li>";
                            }
                            $("#cjfx-ul-list").html(html);
                            form.render();
                            $("#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list ul li")
                                .each(function () {
                                    var layTitle = $(this).find("select").attr("lay-filter");
                                    form.on('select(' + layTitle + ')', function (data) {
                                        var e = data.elem;
                                        var text = e[e.selectedIndex].text;
                                        var gid = $(this).parents("li").attr("gid");
                                        var ptext = $(this).parents("li").find(".name").text();
                                        console.log(ptext);
                                        $("#gsbjsubitemTwo .popup-con .lable .formula .f-con").addClass("cons");
                                        $("#gsbjsubitemTwo .popup-con .lable .formula .f-con").append(
                                            '<span class="asso" markId="' + layTitle + '" gid="'+gid+'" oid="'+data.value+'">' + ptext + " " + text + '</span>');
                                    })
                                })
                        }
                    });
                });


                var table1fieldArray = []
                table1fieldArray.push({field: "templateName", align: "center", title: "模板名称", minWidth: 120});
                table1fieldArray.push({field: "markFlag", align: "center", title: "是否启用计算规则", minWidth: 120, templet: function (d) {
                        if (d.markFlag == "1") {
                            return '<span class="wstatus correct">是</span>';
                        } else {
                            return '<span class="wstatus deny">否</span>';
                        }
                    }});
                table1fieldArray.push({field: "options", align: "center", title: "操作", minWidth: 120, toolbar: "#barDemo1"});
                var table2 = table.render({
                    elem: "#main-table1",
                    id: 'main-table1',
                    page: {
                        limit: 10,
                        limits: [10, 20, 30, 40, 50],
                        layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                    },
                    where: {
                        'fid': $("#grdcalcuRule_fid").val(),
                        "xnxq": $("#grdcalcuRule_xnxq").val()
                    },
                    request: {
                        pageName: "curPage",
                        limitName: "pageSize"
                    },
                    url: '/api/score/gradeCalculate/getGrdcalcuRuletempList',
                    cols: [table1fieldArray],
                    parseData: function (res) {
                        return {
                            "count": res.data.count,
                            "data": res.data.dataList,
                            "code": 0
                        }
                    },
                    done: function (res) {}
                })

                /* 监听工具条 */
                table.on("tool(dataTable1)", function (obj) {
                    var data = obj.data; //获得当前行数据
                    if (obj.event === "del") {
                        deleteRuleTemp(data.id);
                    } else if (obj.event === "edit") {
                        console.log(data);
                        var grdcalcuRule_formulaFlag = data.formulaType;
                        $("#calcAddPoups .title .name").html("编辑");
                        calcAddPoupsindex = layer.open({
                            type: 1,
                            title: false,
                            closeBtn: false,
                            shadeClose: true,
                            isOutAnim: true,
                            content: $('#calcAddPoups'),
                            area: ['auto', 'auto'],
                            success: function () {
                                $("#ruletempId").val(data.id);
                                $("#curformulatype").val(grdcalcuRule_formulaFlag);
                                $("#templateName").val(data.templateName);
                                $("#switchGradeFilter1").val(data.markFlag);
                                var markFlag = $("#switchGradeFilter1").is(":checked")?"1":"0";
                                if(data.markFlag != markFlag){
                                    $("#switchGradeFilter1").click();
                                }
                                if(grdcalcuRule_formulaFlag == 0){
                                    $(".table-box1").show();
                                    $(".table-box2").hide();
                                    $("#totalLevel").show();
                                    $("#pointsRuleType").val(data.pointsRuleType);
                                    if(data.formulaTextOff != null && data.formulaTextOff != ""){
                                        main_table2_dataArr = JSON.parse(data.formulaTextOff);
                                    } else {
                                        main_table2_dataArr = [];
                                    }
                                    table.reload('main-table2', {
                                        data: main_table2_dataArr
                                    })
                                } else {
                                    $(".table-box1").hide();
                                    $(".table-box2").show();
                                    $("#totalLevel").hide();
                                    if(data.formulaTextOn != null && data.formulaTextOn != ""){
                                        main_table3_dataArr = JSON.parse(data.formulaTextOn);
                                    }
                                    table.reload('main-table3', {
                                        data: main_table3_dataArr
                                    })
                                    console.log(main_table3_dataArr);
                                }
                                form.render();
                            },

                        }, function () {});
                    }
                })

                table.on("tool(dataTable2)", function (obj) {
                    var data = obj.data; //获得当前行数据
                    if (obj.event === "del") {
                        for(var i in main_table2_dataArr){
                            if(main_table2_dataArr[i].id == data.id){
                                main_table2_dataArr.splice(i);
                            }
                        }
                        obj.del();
                        console.log(main_table2_dataArr);
                    } else if (obj.event === "edit") {
                        $("#subitemadd .title").text("编辑");
                        form.val("secondaryEditForm", {
                            "name1": '',
                            "level1": '',
                            "seconCode": ''
                        });
                        subitemaddindex = layer.open({
                            type: 1,
                            title: false,
                            closeBtn: false,
                            shadeClose: true,
                            isOutAnim: true,
                            content: $('#subitemadd'),
                            area: ['auto', 'auto'],
                            success: function () {
                                $("#grdcalcuRuletempScoreruleid").val(data.id);
                                $("#itemDictionary").val(data.itemDictionaryid);
                                $("#gradeMakeRule").val(data.gradeMakeRuleid);
                                $("input[name='seconCode']").val(data.proportion);
                                form.render();
                            },
                        }, function () {});
                    }
                })

                table.on("tool(dataTable3)", function (obj) {
                    var data = obj.data; //获得当前行数据
                    if (obj.event === "del") {
                        for(var i in main_table3_dataArr){
                            if(main_table3_dataArr[i].id == data.id){
                                main_table3_dataArr.splice(i);
                            }
                        }
                        obj.del();
                        console.log(main_table3_dataArr);
                    } else if (obj.event === "edit") {
                        // layer.msg("编辑")
                        console.log(data);
                        if(data.totalType == 1){
                            gsbjsubitemindex = layer.open({
                                type: 1,
                                title: false,
                                closeBtn: false,
                                shadeClose: true,
                                isOutAnim: true,
                                content: $('#gsbjsubitem'),
                                area: ['auto', 'auto'],
                                success: function () {
                                    $("#totaluuid").val(data.id);
                                    var formula = data.formulaText || "";
                                    if(formula != ""){
                                        var formulaText = JSON.parse(formula) || "";
                                        var gradeMakeRuleid = data.gradeMakeRuleid || "";
                                        var twolevel = data.twolevel || "";
                                        if(twolevel == "1"){
                                            $("input[name='switch_filter']").attr("checked", "checked");
                                            $("#total-cjfx-ul-list").find(".item").addClass("showMore");
                                        }
                                        if(twolevel == "0"){
                                            $("input[name='switch_filter']").removeAttr("checked");
                                            $("#total-cjfx-ul-list").find(".item").removeClass("showMore");
                                        }
                                        $("#total-jfjz-filter").val(gradeMakeRuleid);
                                        var html = "";
                                        for(var i in formulaText){
                                            var type = formulaText[i]["type"];
                                            var p_grade_option_id = formulaText[i]["p_grade_option_id"] || "";
                                            var grade_option_id = formulaText[i]["grade_option_id"];
                                            var grade_level_id = formulaText[i]["grade_level_id"];
                                            var text = formulaText[i]["text"];
                                            var className = getClaName(type);
                                            if(type == "gl"){
                                                if(p_grade_option_id == 0){
                                                    html += "<span markid='oneSelect1' class='"+className+"' pid='"+grade_option_id+"' oid='"+grade_level_id+"'>"+text+"</span>";
                                                } else {
                                                    html += "<span markid='selctOnchange4' class='"+className+"' pid='"+p_grade_option_id+"' oid='"+grade_level_id+"' gid='"+grade_option_id+"'>"+text+"</span>";
                                                }
                                                $("#total-cjfx-ul-list").find(".item").each(function(){
                                                    if(p_grade_option_id == 0){
                                                        if($(this).find(".i-con").find(".pgrade").find(".name").attr("gid") == grade_option_id){
                                                            $(this).find(".i-con").find(".pgrade").find(".name").addClass("cur");
                                                            $(this).find(".i-bottom").find(".select").find("select").val(grade_level_id);
                                                            $(this).find(".i-bottom").find(".select").find("select").removeAttr("disabled");
                                                        }
                                                    } else {
                                                        $(this).find(".i-con").find(".scrollBox1").find("li").each(function(){
                                                            if($(this).find(".name").attr("gid") == grade_option_id){
                                                                $(this).find(".name").addClass("cur");
                                                                $(this).find(".select").find("select").val(grade_level_id);
                                                                $(this).find(".select").find("select").removeAttr("disabled");
                                                            }
                                                        })
                                                    }
                                                })
                                            } else {
                                                html += "<span class='"+className+"'>"+text+"</span>";
                                            }
                                        }
                                        form.render();
                                        if(html.length > 0){
                                            $("#total-formula-content").addClass("cons")
                                        }
                                        $("#total-formula-content").html(html);
                                    } else {
                                        $("#total-formula-content").html("");
                                        $("#total-formula-content").removeClass("cons");
                                        $("#total-jfjz-filter").val("");
                                        $("#total-cjfx-ul-list").find(".item").removeClass("showMore");
                                        $("input[name='switch_filter']").removeAttr("checked");
                                        $("#total-cjfx-ul-list").find(".item").each(function(){
                                            $(this).find(".i-con").find(".pgrade").find(".name").removeClass("cur");
                                            $(this).find(".i-bottom").find(".select").find("select").val("");
                                            $(this).find(".i-bottom").find(".select").find("select").attr("disabled","disabled");
                                            $(this).find(".i-con").find(".scrollBox1").find("li").each(function(){
                                                $(this).find(".name").removeClass("cur");
                                                $(this).find(".select").find("select").val("");
                                                $(this).find(".select").find("select").attr("disabled", "disabled");
                                            })
                                        })
                                        form.render();
                                    }
                                },
                            }, function () {});
                        } else {
                            gsbjsubitemTwoindex = layer.open({
                                type: 1,
                                title: false,
                                closeBtn: false,
                                shadeClose: true,
                                isOutAnim: true,
                                content: $('#gsbjsubitemTwo'),
                                area: ['auto', 'auto'],
                                success: function () {
                                    $("#grdcalcuRuletempScoreruleid_1").val(data.id);
                                    var formula = data.formulaText || "";
                                    if(formula != ""){
                                        var formulaText = JSON.parse(formula) || "";
                                        var gradeMakeRuleid = data.gradeMakeRuleid || "";
                                        var itemDictionaryid = data.itemDictionaryid || "";
                                        $("#gsxz-filter").val(itemDictionaryid);
                                        $("#jfjz-filter").val(gradeMakeRuleid);
                                        var html = "";

                                        $.ajax({
                                            type: "post",
                                            url: "/api/score/itemDictionary/getList",
                                            data: {'fid': $("#grdcalcuRule_fid").val(), "pid": itemDictionaryid},
                                            async: false,
                                            dataType: 'json',
                                            success: function (result) {
                                                var html = "";
                                                for(var i = 0; i < result.data.length; i++){
                                                    html += "<li gid='"+result.data[i].id+"'>";
                                                    html += "<div class='name'>"+result.data[i].itemName+"</div>";
                                                    html += "<div class='select w102'>";
                                                    html += "<select name='modules' lay-filter='selctOnchanges1' disabled lay-verify='required'>";
                                                    html += "<option value=''>请选择</option>";
                                                    for (let i = 0; i < gradeMakeRuleList.length; i++) {
                                                        html += "<option value='"+gradeMakeRuleList[i].id+"'>"+gradeMakeRuleList[i].gradeName+"</option>";
                                                    }
                                                    html += "</select></div></li>";
                                                }
                                                $("#cjfx-ul-list").html(html);
                                                form.render();
                                                $("#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list ul li")
                                                    .each(function () {
                                                        var layTitle = $(this).find("select").attr("lay-filter");
                                                        form.on('select(' + layTitle + ')', function (data) {
                                                            var e = data.elem;
                                                            var text = e[e.selectedIndex].text;
                                                            var gid = $(this).parents("li").attr("gid");
                                                            var ptext = $(this).parents("li").find(".name").text();
                                                            console.log(ptext);
                                                            $("#gsbjsubitemTwo .popup-con .lable .formula .f-con").addClass("cons");
                                                            $("#gsbjsubitemTwo .popup-con .lable .formula .f-con").append(
                                                                '<span class="asso" markId="' + layTitle + '" gid="'+gid+'" oid="'+data.value+'">' + ptext + " " + text + '</span>');
                                                        })
                                                    })
                                            }
                                        });

                                        for(var i in formulaText){
                                            var type = formulaText[i]["type"];
                                            var grade_option_id = formulaText[i]["grade_option_id"];
                                            var grade_level_id = formulaText[i]["grade_level_id"];
                                            var text = formulaText[i]["text"];
                                            var className = getClaName(type);
                                            if(type == "gl"){
                                                html += "<span class='"+className+"' markid='selctOnchanges1' gid='"+grade_option_id+"' oid='"+grade_level_id+"'>"+text+"</span>";
                                                $("#cjfx-ul-list").find("li").each(function(){
                                                    if($(this).attr("gid") == grade_option_id){
                                                        $(this).find(".name").addClass("cur");
                                                        $(this).find(".select").find("select").val(grade_level_id);
                                                        $(this).find(".select").find("select").removeAttr("disabled");
                                                    }
                                                })
                                            } else {
                                                html += "<span class='"+className+"'>"+text+"</span>";
                                            }
                                        }
                                        form.render();
                                        if(html.length > 0){
                                            $("#formula-content").addClass("cons")
                                        }
                                        $("#formula-content").html(html);
                                    }
                                },
                            }, function () {});
                        }
                    }
                })

                var table2fieldArray = [];
                table2fieldArray.push({field: "scoreSubCode", align: "center", title: "成绩分项代码"});
                table2fieldArray.push({field: "scoreSubName", align: "center", title: "成绩分项名称"});
                table2fieldArray.push({field: "classificationSystem", align: "center", title: "计分级制"});
                table2fieldArray.push({field: "proportion", align: "center", title: "占比（%）"});
                table2fieldArray.push({field: "options", align: "center", title: "操作", toolbar: "#barDemo4"});

                var table2 = table.render({
                    elem: "#main-table2",
                    id: 'main-table2',
                    cols: [table2fieldArray],
                    data: [],
                    done: function (res) {}
                })

                /*var table2fieldArray = []
                table2fieldArray.push({field: "scoreSubCode", align: "center", title: "成绩分项代码"});
                table2fieldArray.push({field: "scoreSubName", align: "center", title: "成绩分项名称"});
                table2fieldArray.push({field: "classificationSystem", align: "center", title: "计分级制"});
                table2fieldArray.push({field: "proportion", align: "center", title: "占比（%）"});
                table2fieldArray.push({field: "options", align: "center", title: "操作", toolbar: "#barDemo4"});

                var table2 = table.render({
                    elem: "#main-table2",
                    id: 'main-table2',
                    where: {
                        'fid': $("#grdcalcuRule_fid").val(),
                        "xnxq": $("#grdcalcuRule_xnxq").val(),
                        "templateId": $("#ruletempId").val()
                    },
                    request: {
                        pageName: "curPage",
                        limitName: "pageSize"
                    },
                    url: '/api/score/gradeCalculate/getGrdcalcuRuletempScoreruleList',
                    cols: [table2fieldArray],
                    parseData: function (res) {
                        return {
                            "count": res.data.count,
                            "data": res.data.dataList,
                            "code": 0
                        }
                    },
                    done: function (res) {}
                })*/
                var totalUid = guid();
                var table3 = table.render({
                    elem: "#main-table3",
                    id: 'main-table3',
                    data: [{
                        "scoreSubName": "总分",
                        "classificationSystem": "",
                        "totalType": 1,
                        "id": totalUid,
                    }],
                    cols: [[{field: "scoreSubName", align: "center", title: "公式",
                        },{field: "classificationSystem", align: "center", title: "计分级制",
                        },{field: "options", align: "center", title: "操作", toolbar: "#barDemo5",},]
                    ],
                    done: function (res) {
                    }
                })

                //添加
                $(".content .add-lab span").on("click", function () {
                    $("#calcAddPoups .title .name").html("添加");
                    let pdState = $("#sfqyEdit").find(".layui-form-switch").hasClass(
                        "layui-form-onswitch");
                    if (!pdState) {
                        $("#grdcalcuRuletempScoreruleid").val("");
                        $(".table-box1").show();
                        $(".table-box2").hide();
                        form.val("addeditForm", {
                            "templateName": '',
                        });
                        calcAddPoupsindex = layer.open({
                            type: 1,
                            title: false,
                            closeBtn: false,
                            shadeClose: true,
                            isOutAnim: true,
                            content: $('#calcAddPoups'),
                            area: ['auto', 'auto'],
                            success: function () {
                                $("#ruletempId").val("");
                                $("#templateName").val("");
                                if ($("#switchGradeFilter1").is(":checked")) {
                                    $("#switchGradeFilter1").click();
                                }
                                $("#pointsRuleType").val("");
                                table.reload('main-table2');
                                $("#totalLevel").show();
                                form.render();
                            },
                        }, function () {

                        });
                    } else {
                        $(".table-box2").show();
                        $(".table-box1").hide();
                        form.val("addeditForm", {
                            "templateName": '',
                        });
                        calcAddPoupsindex = layer.open({
                            type: 1,
                            title: false,
                            closeBtn: false,
                            shadeClose: true,
                            isOutAnim: true,
                            content: $('#calcAddPoups'),
                            area: ['auto', 'auto'],
                            success: function () {
                                $("#ruletempId").val("");
                                $("#templateName").val("");
                                if ($("#switchGradeFilter1").is(":checked")) {
                                    $("#switchGradeFilter1").click();
                                }
                                main_table3_dataArr = [];
                                var obj = {};
                                obj["scoreSubName"] = "总分";
                                obj["classificationSystem"] = "";
                                obj["totalType"] = "1";
                                obj["id"] = guid();
                                main_table3_dataArr.push(obj);
                                table.reload('main-table3',{
                                    data: main_table3_dataArr
                                });
                                $("#totalLevel").hide();
                                form.render();
                            },
                        }, function () {

                        });
                    }
                })

                //隐藏弹窗
                $('.close,.exam-cancle').on("click", function () {
                    var index = $(this).parents(".layui-layer").attr("times");
                    layer.close(index);
                })

                $("#calcAddPoups .exam-cancle,#calcAddPoups .exam-sure").click(function (){
                    table.reload('main-table2', {
                        data: []
                    })
                })

                //组件切换
                $(".main").on("click", ".m-tab ul li", function () {
                    $(this).addClass("cur").siblings().removeClass("cur");
                })

                //添加
                $("#calcAddPoups .add-lab span").click(function () {
                    let kols = $("#totalLevel").css("display");
                    if (kols == 'none') {
                        gsbjsubitemTwoindex = layer.open({
                            type: 1,
                            title: false,
                            closeBtn: false,
                            shadeClose: true,
                            isOutAnim: true,
                            content: $('#gsbjsubitemTwo'),
                            area: ['auto', 'auto'],
                            success: function () {
                                $("#grdcalcuRuletempScoreruleid_1").val("");
                                $("#gsxz-filter").val("");
                                $("#jfjz-filter").val("");
                                $("#cjfx-ul-list").html("");
                                $("#formula-content").html("");
                                $("#formula-content").removeClass("cons");
                                form.render();
                            },

                        }, function () {});
                    } else {
                        $("#subitemadd .title").text("新增");
                        form.val("secondaryEditForm", {
                            "name1": '',
                            "level1": '',
                            "seconCode": ''
                        });
                        subitemaddindex = layer.open({
                            type: 1,
                            title: false,
                            closeBtn: false,
                            shadeClose: true,
                            isOutAnim: true,
                            content: $('#subitemadd'),
                            area: ['auto', 'auto'],
                            success: function () {
                                $("#grdcalcuRuletempScoreruleid").val("");
                                $("#itemDictionary").val("");
                                $("#gradeMakeRule").val("");
                                $("input[name='seconCode']").val("");
                                form.render();
                            },
                        }, function () {});
                    }
                })


                //公式计算

                //后移
                $(".layui-form").on("click", ".keyboard .k-con .delet", function () {
                    let parentsEle = $(this).parents('.grade-keyboard');
                    let conEle = parentsEle.prev().find('.f-con');
                    let txt = conEle.find("span").last().text();
                    let extraStr = conEle.find("span").last().attr("markId");
                    conEle.find("span").last().remove();

                    if (conEle.find("span").length == 0) {
                        parentsEle.prev().find('.f-con').removeClass("cons");
                    }

                    if (extraStr) {
                        $("#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item select")
                            .each(function () {
                                var kpl = $(this).attr("lay-filter");
                                console.log(kpl);
                                if (kpl == extraStr) {
                                    $(this).val("");
                                    $(this).attr("disabled", "disabled");
                                    form.render('select');
                                    if ($(this).parent().parent().hasClass("i-bottom")) {
                                        $(this).parents(".item").find(".left .name")
                                            .removeClass("cur");
                                    } else {
                                        $(this).parents("li").find(".name").removeClass(
                                            "cur");
                                    }


                                }
                            })


                        $("#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .select select")
                            .each(function () {
                                var kpl = $(this).attr("lay-filter");
                                console.log(kpl);
                                if (kpl == extraStr) {
                                    $(this).val("");
                                    $(this).attr("disabled", "disabled");
                                    form.render('select');
                                    $(this).parents("li").find(".name").removeClass("cur");

                                }
                            })

                    }
                })
                //清空
                $(".layui-form").on("click", " .keyboard .k-con .empty", function () {
                    let parentsEle = $(this).parents('.grade-keyboard');
                    parentsEle.prev().find('.f-con span').remove();
                    parentsEle.prev().find('.f-con').removeClass("cons");
                    $("#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item .name")
                        .removeClass("cur");
                    $("#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item select")
                        .val('').attr("disabled", "disabled");

                    $("#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list ul li .name")
                        .removeClass("cur");
                    $("#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list ul li select")
                        .val('').attr("disabled", "disabled");
                    form.render('select');
                })

                //加减乘除
                $(".layui-form").on("click", ".keyboard .k-con .sign", function () {
                    let signTxt = $(this).text();
                    let cls = $(this).attr("class");
                    let parentsEle = $(this).parents('.grade-keyboard');
                    parentsEle.prev().find('.f-con').append('<span class="' + cls + '">' +
                        signTxt + '</span>');
                    parentsEle.prev().find('.f-con').addClass("cons");
                })

                //输入数字
                $(".layui-form").on("click", ".keyboard .k-con .num", function () {
                    let signTxt = $(this).text();
                    let cls = $(this).attr("class");
                    let parentsEle = $(this).parents('.grade-keyboard');
                    parentsEle.prev().find('.f-con').append('<span class="' + cls + '">' +
                        signTxt + '</span>');
                    parentsEle.prev().find('.f-con').addClass("cons");
                })


                //分项选中

                $(".popup-con .grade-keyboard .score-breakdown").on("click",
                    " .sb-cons .sb-list .item .i-con .right ul li .name", function () {
                        $(this).toggleClass("cur");
                        if (!$(this).hasClass("cur")) {
                            $(this).parent().addClass("disabled");
                            $(this).parent().find("select").attr("disabled", "disabled");
                            form.render('select');
                        } else {
                            $(this).parent().removeClass("disabled");
                            $(this).parent().find("select").removeAttr("disabled");
                            form.render('select');
                        }
                    })

                $("#gsbjsubitemTwo .popup-con .grade-keyboard .score-breakdown").on("click",
                    ".sb-cons .sb-list ul li .name",
                    function () {
                        $(this).toggleClass("cur");
                        if (!$(this).hasClass("cur")) {
                            $(this).parent().addClass("disabled");
                            $(this).parent().find("select").attr("disabled", "disabled");
                            form.render('select');
                        } else {
                            $(this).parent().removeClass("disabled");
                            $(this).parent().find("select").removeAttr("disabled");
                            form.render('select');
                        }
                    })

                $("#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item .i-con .right ul li")
                    .each(function () {
                        var layTitle = $(this).find("select").attr("lay-filter");
                        form.on('select(' + layTitle + ')', function (data) {
                            //获取选中的文本
                            var e = data.elem;
                            var text = e[e.selectedIndex].text;
                            var ptext = $(this).parents("li").find(".name").text();
                            var gid = $(this).parents("li").find(".name").attr("gid");
                            var ftext = $(this).parents(".item").find(".left .name").text();
                            var pid = $(this).parents(".i-con").find(".pgrade .name").attr("gid");
                            var ptext1 = $(this).parents(".i-con").find(".pgrade .name").text();
                            $("#gsbjsubitem .popup-con .lable .formula .f-con").addClass(
                                "cons");
                            $("#gsbjsubitem .popup-con .lable .formula .f-con").append(
                                '<span markId="' + layTitle + '" class="asso" ' +
                                'ptext="'+ptext1+'" pid="'+pid+'" ' +
                                'oid="'+data.value+'" otext="'+text+'"' +
                                ' gid="'+gid+'" gtext="'+ptext+'">' + ptext + " " + "(" +
                                ftext + ")" + " " + text + '</span>');
                        })
                    })

                $("#gsbjsubitem .popup-con .grade-keyboard .score-breakdown .sb-cons .sb-list .item")
                    .each(function () {
                        var layTitle = $(this).find(".i-bottom select").attr("lay-filter");
                        form.on('select(' + layTitle + ')', function (data) {
                            //获取选中的文本
                            var e = data.elem;
                            var text = e[e.selectedIndex].text;
                            var ftext = $(this).parents(".item").find(".left .name").text();
                            var pid = $(this).parents(".item").find(".left .name").attr("gid");
                            var ptext = $(this).parents(".item").find(".left .name").text();
                            $("#gsbjsubitem .popup-con .lable .formula .f-con").addClass("cons");
                            $("#gsbjsubitem .popup-con .lable .formula .f-con").append(
                                '<span markId="' + layTitle + '" class="asso" ' +
                                'pid="'+pid+'" ptext="'+ptext+'"' +
                                ' oid="'+data.value+'" otext="'+text+'">' + ftext + " " +
                                text + '</span>');
                        })
                    })

                //以及分项选中

                $("#gsbjsubitem .popup-con .grade-keyboard").on("click",
                    ".score-breakdown .sb-cons .sb-list .item .i-con .left .name",
                    function () {
                        $(this).toggleClass("cur");
                        if (!$(this).hasClass("cur")) {
                            $(this).parents(".item").find(".i-bottom select").attr("disabled",
                                "disabled");
                            form.render('select');
                        } else {
                            $(this).parents(".item").find(".i-bottom select").removeAttr(
                                "disabled");
                            form.render('select');
                        }
                    })

                //select 点击事件
                $("#gsbjsubitem").on("click", ".layui-form-select .layui-select-title", function (e) {
                    console.log($(e.target));
                    let objll = $(this).offset().left;
                    let objtt = $(this).offset().top + 34;
                    console.log(objll);
                    console.log(objtt);
                    $(this).parent().find(".layui-anim-upbit").css({
                        'left': objll,
                        'top': objtt
                    });
                })

                $("#gsbjsubitemTwo").on("click", ".w102 .layui-form-select .layui-select-title", function (e) {
                    console.log($(e.target));
                    let objll = $(this).offset().left;
                    let objtt = $(this).offset().top + 34;
                    console.log(objll);
                    console.log(objtt);
                    $(this).parent().find(".layui-anim-upbit").css({
                        'left': objll,
                        'top': objtt
                    });
                })

                $(".scrollBox").mousedown(function (e) {
                    var _con = $('.select');
                    if (!_con.is(event.target) && _con.has(event.target).length === 0) {
                        $(".layui-form-select").removeClass("layui-form-selected")
                    }
                });

                $("#add-score-grdcalcu-ruletemp-scorerule").click(function(){
                    let itemDictionaryid = $("#itemDictionary").find("option:selected").val();
                    let itemDictionaryname = $("#itemDictionary").find("option:selected").attr("name");
                    let itemDictionarycode = $("#itemDictionary").find("option:selected").attr("code");
                    let gradeMakeRuleid = $("#gradeMakeRule").find("option:selected").val();
                    let gradeMakeRulename = $("#gradeMakeRule").find("option:selected").attr("name");
                    let seconCode = $("input[name='seconCode']").val();
                    if(U.isEmpty(itemDictionaryid)){
                        layer.msg("成绩分项名称不得为空！");
                        return;
                    }
                    if(U.isEmpty(gradeMakeRuleid)){
                        layer.msg("计分级制不得为空！");
                        return;
                    }
                    if(U.isEmpty(seconCode)){
                        layer.msg("占比不得为空！");
                        return;
                    }
                    var reg = new RegExp("^([1-9]|[1-9]\\d|100)$");
                    if(!seconCode.match(reg)){
                        layer.msg("占比请输入0-100的数字！");
                        return;
                    }
                    var grdcalcuRuletempScoreruleid = $("#grdcalcuRuletempScoreruleid").val();
                    if(U.isEmpty(grdcalcuRuletempScoreruleid)){
                        var addObj = {};
                        addObj["id"] = guid();
                        addObj["itemDictionaryid"] = itemDictionaryid;
                        addObj["scoreSubName"] = itemDictionaryname;
                        addObj["scoreSubCode"] = itemDictionarycode;
                        addObj["gradeMakeRuleid"] = gradeMakeRuleid;
                        addObj["classificationSystem"] = gradeMakeRulename;
                        addObj["proportion"] = seconCode;
                        main_table2_dataArr.push(addObj);
                        table.reload('main-table2', {
                            data: main_table2_dataArr
                        })
                        layer.close(subitemaddindex);
                        $("#itemDictionary").val("");
                        $("#gradeMakeRule").val("");
                        $("#input[name='seconCode']").val("");
                        form.render();
                    } else {
                        for(var i in main_table2_dataArr){
                            if(main_table2_dataArr[i].id == grdcalcuRuletempScoreruleid){
                                main_table2_dataArr[i]["itemDictionaryid"] = itemDictionaryid;
                                main_table2_dataArr[i]["scoreSubName"] = itemDictionaryname;
                                main_table2_dataArr[i]["scoreSubCode"] = itemDictionarycode;
                                main_table2_dataArr[i]["gradeMakeRuleid"] = gradeMakeRuleid;
                                main_table2_dataArr[i]["classificationSystem"] = gradeMakeRulename;
                                main_table2_dataArr[i]["proportion"] = seconCode;
                            }
                        }
                        table.reload('main-table2', {
                            data: main_table2_dataArr
                        })
                        layer.close(subitemaddindex);
                        $("#itemDictionary").val("");
                        $("#gradeMakeRule").val("");
                        $("#input[name='seconCode']").val("");
                        form.render();
                    }
                })

                $("#formula-sure").click(function(){
                    var formula_text = new Array();
                    var glindex = 0;
                    /*if(!reasonFormula("formula-content")){
                        layer.msg("公式不合法");
                        return;
                    }*/
                    $("#formula-content").children("span").each(function(){
                        var ctype = getformulaType($(this).prop("class"));
                        var obj = {};
                        obj["type"] = ctype;
                        obj["text"] = $(this).text();
                        if(ctype == 'gl'){
                            glindex++;
                            obj["grade_option_id"] = $(this).attr("gid");
                            obj["grade_level_id"] = $(this).attr("oid");
                        }
                        formula_text.push(obj);
                    })
                    if(glindex == 0){
                        layer.msg("请选择成绩分项");
                        return;
                    }
                    var optionId = $("#gsxz-filter").find("option:selected").val();
                    var optionname = $("#gsxz-filter").find("option:selected").attr("name");
                    var optioncode = $("#gsxz-filter").find("option:selected").attr("code");
                    var levelId = $("#jfjz-filter").find("option:selected").val();
                    var levelname = $("#jfjz-filter").find("option:selected").attr("name");
                    var grdcalcuRuletempScoreruleid = $("#grdcalcuRuletempScoreruleid_1").val();
                    if(U.isEmpty(optionId)){
                        layer.msg("请选择公式");
                        return;
                    }
                    if(U.isEmpty(levelId)){
                        layer.msg("请选择计分级制");
                        return;
                    }
                    if(U.isEmpty(grdcalcuRuletempScoreruleid)){
                        var addObj = {};
                        addObj["id"] = guid();
                        addObj["itemDictionaryid"] = optionId;
                        addObj["scoreSubName"] = optionname;
                        addObj["scoreSubCode"] = optioncode;
                        addObj["gradeMakeRuleid"] = levelId;
                        addObj["classificationSystem"] = levelname;
                        addObj["formulaText"] = JSON.stringify(formula_text);
                        addObj["totalType"] = 0;
                        main_table3_dataArr.push(addObj);
                        table.reload('main-table3', {
                            data: main_table3_dataArr
                        })
                    } else {
                        for(var i in main_table3_dataArr){
                            if(main_table3_dataArr[i].id == grdcalcuRuletempScoreruleid){
                                main_table3_dataArr[i]["itemDictionaryid"] = optionId;
                                main_table3_dataArr[i]["scoreSubName"] = optionname;
                                main_table3_dataArr[i]["scoreSubCode"] = optioncode;
                                main_table3_dataArr[i]["gradeMakeRuleid"] = levelId;
                                main_table3_dataArr[i]["classificationSystem"] = levelname;
                                main_table3_dataArr[i]["formulaText"] = JSON.stringify(formula_text);
                            }
                        }
                        table.reload('main-table3', {
                            data: main_table3_dataArr
                        })
                        $("#gsxz-filter").val("");
                        $("#jfjz-filter").val("");
                        form.render();
                    }
                    layer.close(gsbjsubitemTwoindex);
                })

                $("#total-formula-sure").click(function(){
                    var formula_text = new Array();
                    var glindex = 0;
                    /*if(!reasonFormula("total-formula-content")){
                        layer.msg("公式不合法");
                        return;
                    }*/
                    $("#total-formula-content").children("span").each(function(){
                        var ctype = getformulaType($(this).prop("class"));
                        var obj = {};
                        obj["type"] = ctype;
                        obj["text"] = $(this).text();
                        if(ctype == 'gl'){
                            glindex++;
                            if($(this).attr("markid") == 'oneSelect1'){
                                obj["p_grade_option_id"] = "0";
                                obj["grade_option_id"] = $(this).attr("pid");
                                obj["grade_level_id"] = $(this).attr("oid");
                            } else {
                                obj["p_grade_option_id"] = $(this).attr("pid");
                                obj["grade_option_id"] = $(this).attr("gid");
                                obj["grade_level_id"] = $(this).attr("oid");
                            }
                        }
                        formula_text.push(obj);
                    })
                    if(glindex == 0){
                        layer.msg("请选择成绩分项");
                        return;
                    }
                    var levelId = $("#total-jfjz-filter").find("option:selected").val();
                    var levelname = $("#total-jfjz-filter").find("option:selected").attr("name");
                    var twolevel = $("input[name='switch_filter']").is(":checked") ? "1" : "0";
                    var totaluuid = $("#totaluuid").val();
                    if(U.isEmpty(levelId)){
                        layer.msg("请选择计分级制");
                        return;
                    }
                    if(U.isEmpty(totaluuid)){
                        var addObj = {};
                        addObj["id"] = guid();
                        addObj["gradeMakeRuleid"] = levelId;
                        addObj["classificationSystem"] = levelname;
                        addObj["formulaText"] = JSON.stringify(formula_text);
                        addObj["twolevel"] = twolevel;
                        addObj["totalType"] = 1;
                        main_table3_dataArr.push(addObj);
                        table.reload('main-table3', {
                            data: main_table3_dataArr
                        })
                    } else {
                        if(main_table3_dataArr.length == 0){
                            var obj = {};
                            obj["scoreSubName"] = "总分";
                            obj["classificationSystem"] = "";
                            obj["totalType"] = 1;
                            obj["id"] = totaluuid;
                            main_table3_dataArr.push(obj);
                        }
                        for(var i in main_table3_dataArr){
                            if(main_table3_dataArr[i].id == totaluuid){
                                main_table3_dataArr[i]["gradeMakeRuleid"] = levelId;
                                main_table3_dataArr[i]["classificationSystem"] = levelname;
                                main_table3_dataArr[i]["formulaText"] = JSON.stringify(formula_text);
                                main_table3_dataArr[i]["twolevel"] = twolevel;
                                main_table3_dataArr[i]["totalType"] = 1;
                            }
                        }
                        table.reload('main-table3', {
                            data: main_table3_dataArr
                        })
                        form.render();
                    }
                    layer.close(gsbjsubitemindex);
                })

                $("#add-score-grdcalcu-ruletemp").click(function(){
                    let templateName = $("#templateName").val();
                    let markFlag = $("#switchGradeFilter1").is(":checked") ? "1" : "0";
                    let formdata = new FormData();
                    if(U.isEmpty(templateName)){
                        layer.msg("模板名称不得为空！");
                        return;
                    }
                    var forMulaType = "";
                    if(U.isEmpty($("#ruletempId").val())){
                        forMulaType = $("#grdcalcuRule_formulaFlag").val();
                    } else {
                        forMulaType = $("#curformulatype").val();
                    }
                    if(forMulaType == 0){
                        let pointsRuleType = $("#pointsRuleType").find("option:selected").val();
                        var gslength = main_table2_dataArr.length;
                        if(U.isEmpty(pointsRuleType)){
                            layer.msg("总分计分级制不得为空！");
                            return;
                        }
                        if(gslength == 0){
                            layer.msg("计算公式不得为空！");
                            return;
                        }
                        formdata.append("pointsRuleType", pointsRuleType);
                        formdata.append("formulaType", 0);
                        formdata.append("formulaTextOff", JSON.stringify(main_table2_dataArr));
                    } else {
                        var gslength = main_table3_dataArr.length;
                        if(gslength == 0){
                            layer.msg("计算公式不得为空！");
                            return;
                        } else if(gslength == 1){
                            if(main_table3_dataArr[0].totalType == 1 && main_table3_dataArr[0].gradeMakeRuleid == null){
                                layer.msg("计算公式不得为空！");
                                return;
                            }
                        }
                        formdata.append("formulaTextOn", JSON.stringify(main_table3_dataArr));
                        formdata.append("formulaType", 1);
                    }
                    formdata.append("templateName", templateName);
                    formdata.append("markFlag", markFlag);
                    formdata.append("fid", $("#grdcalcuRule_fid").val());
                    formdata.append("xnxq", $("#grdcalcuRule_xnxq").val());
                    formdata.append("id", $("#ruletempId").val());
                    formdata.append("createUid", $("#grdcalcuRule_createUid").val());
                    $.ajax({
                        type: "POST",
                        url: "/api/score/gradeCalculate/saveGrdcalcuRuletemp",
                        data: formdata,
                        contentType : false,
                        processData : false,
                        dataType: 'json',
                        success: function (result) {
                            if(result.code == 200){
                                layer.close(calcAddPoupsindex);
                                table.reload('main-table1');
                            } else {
                                layer.msg(result.msg);
                            }
                        }
                    });
                })

                window.deleteRuleTemp = function(id){
                    var confirmindex = layer.confirm('确认删除？', {
                        btn: ['确定', '取消'] //按钮
                    }, function () {
                        $.ajax({
                            type: "POST",
                            url: "/api/score/gradeCalculate/deleteGrdcalcuRuletemp",
                            data: {"id": id},
                            dataType: 'json',
                            success: function (result) {
                                if(result.code == 200){
                                    layer.msg("删除成功");
                                    table.reload('main-table1');
                                }
                            },
                            error: function(){
                                layer.msg("系统错误");
                            }
                        });
                    }, function () {
                        layer.close(confirmindex);
                    });
                }
            })
    })

    function savegrdcalcuRule(){
        $.ajax({
            type: "POST",
            url: "/api/score/gradeCalculate/saveGrdCalRule",
            data: $("#savegrdcalcuRuleForm").serialize(),
            dataType: 'json',
            success: function (result) {
                console.log(result);
            }
        });
    }

    function guid () {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
            var r = Math.random() * 16 | 0
            var v = c === 'x' ? r : (r & 0x3 | 0x8)
            return v.toString(16)
        })
    }

    function getformulaType(content){
        var type = "";
        if( content.indexOf("asso") != -1){
            type = "gl"; // 关联数据
        } else if( content.indexOf("spot") != -1 ){
            type = "dian";// 小数点
        } else if( content.indexOf("lbracket") != -1 ){
            type = "zuokuohao"; // 左括号
        } else if( content.indexOf("sign-cancle") != -1 ){
            type = "jian"; // 减
        } else if( content.indexOf("sign-mul") != -1 ){
            type = "cheng"; // 乘
        } else if( content.indexOf("sign-except") != -1 ){
            type = "chu"; //除
        } else if( content.indexOf("sign-add") != -1 ){
            type = "jia"; //加
        } else if( content.indexOf("rbracket") != -1 ){
            type = "youkuohao"; //右括号
        } else if( content.indexOf("zero") != -1 ){
            type = "ling"; //0
        } else if( content == "num" ){
            type = "shuzi"; //数字
        }
        return type;
    }

    function getClaName(type){
        var content = "";
        if(type == "gl"){
            content = "asso";
        } else if(type == "dian"){
            content = "num spot";
        } else if(type == "zuokuohao"){
            content = "sign lbracket";
        } else if(type == "jian"){
            content = "sign sign-cancle";
        } else if(type == "cheng"){
            content = "sign sign-mul";
        } else if(type == "chu"){
            content = "sign sign-except";
        } else if(type == "jia"){
            content = "sign sign-add";
        } else if(type == "youkuohao"){
            content = "sign rbracket";
        } else if(type == "ling"){
            content = "num zero";
        } else if(type == "shuzi"){
            content = "num";
        }
        return content;
    }

    function reasonFormula(id) {
        var formula = "";
        $("#" + id).find("span").each(function(){
            if($(this).hasClass("asso")){
                formula += "1";
            } else if($(this).hasClass("sign-mul")){
                formula += "*";
            } else {
                formula += $(this).text();
            }
        });
        return check_gongshi(formula);
    }

    function check_gongshi(cal) {
        if (cal.search(/^[\+\-\*\/\.\)]|[\+\-\*\/\.\(]$|[\+\-\*\/\.]{2}|[^\+\-\*\/\(\)\d\.]|([\d\.\)]\()|(\)[\d\.])|(\([\+\-\*\/\.\)])|([\+\-\*\/\.]\))|(\.\d+\.)/) > -1) {
            return false;
        } else {
            var num_left = 0;
            for (i = 0; i < cal.length; i++) {
                if (cal[i] == '(') {
                    num_left++;
                }
                if (cal[i] == ')') {
                    if (num_left > 0) {
                        num_left--;
                    } else {
                        return false;
                    }
                }
            }
            if (num_left > 0) {
                return false;
            } else {
                return true;
            }
        }
    }

</script>

</body>

</html>