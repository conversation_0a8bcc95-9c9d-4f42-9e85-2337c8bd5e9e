<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>正考成绩管理</title>
    <link rel="stylesheet" href="/score/mainexam/css/global.css">
    <link rel="stylesheet" href="/score/mainexam/layui/css/layui.css">
    <link rel="stylesheet" href="/score/mainexam/css/common.css">
    <link rel="stylesheet" href="/score/mainexam/css/reset.css?t=1">
    <link rel="stylesheet" href="/score/mainexam/css/index.css">
    <link rel="stylesheet" href="/score/mainexam/css/voice.css">
    <style>
        .main {
            padding: 0;
        }
        .layui-table-cell{padding:0;}
    </style>
</head>

<body>

<div class="main">
    <div class="content" style="padding:0;">
        <div class="top">
            <div class="title">
                <div class="back">返回</div>
                <div class="levelone">正考成绩管理</div>
                <div class="icon"></div>
                <div class="leveltwo">录入</div>
                <div class="inform"
                     th:text="'课程名称：'+${classCourse?.courseName}+' 班级名称：'+${classCourse?.className}"></div>
            </div>

        </div>
        <div class="tab">
            <ul>
                <li th:each="info,iterStat : ${tabsMap}" th:class="${iterStat.index == 0 ? 'cur' : ''}"
                    th:text="${info.tabsName}" th:attr="tid=${info.tabsId}"></li>
            </ul>
        </div>
        <div class="btns-list">
            <div class="enter">
                <span class="entry">成绩录入</span>

                <em class="save">保存</em>
                <em class="cancle">取消</em>
            </div>
            <span class="import">批量导入</span>
            <span class="speech">语音录入</span>
            <span class="export">导出</span>
        </div>
        <div class="item-list">
            <div class="item active">
                <div class="table" id="studentScore">
                    <table class="layui-table" id="materialTable" lay-filter="materialTable"></table>
                </div>
            </div>
            <!--<div class="item ">
                <div class="table">
                    <table class="layui-table" id="materialTable1" lay-filter="materialTable1"></table>
                </div>
            </div>
            <div class="item">
                <div class="table">
                    <table class="layui-table" id="materialTable2" lay-filter="materialTable2"></table>
                </div>
            </div>-->
        </div>


    </div>
</div>


<!-- 名单排序 -->
<div id="listSorting" class="addPoups popup">
    <div class="title">
        <div class="name">名单排序</div>
        <div class="close"></div>
    </div>
    <div class="popup-con" style="height: 230px;">
        <form class="layui-form" action="">
            <div class="layui-form-item">
                <label class="layui-form-label">排序条件</label>
                <div class="layui-input-block">
                    <select name="modules" lay-verify="required">
                        <option value="">请选择</option>
                        <option value="realname">姓名</option>
                        <option value="uname">学号</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">排序规则</label>
                <div class="layui-input-block">
                    <input type="radio" name="gradeType" checked lay-filter="gradeType" value="0" title="升序">
                    <input type="radio" name="gradeType" lay-filter="gradeType" value="1" title="降序">
                </div>
            </div>
        </form>

    </div>
    <div class="bottom">
        <button type="reset" class="layui-btn layui-btn-primary exam-cancle">取消</button>
        <button class="layui-btn exam-sure">确定</button>
    </div>

</div>


<!-- 批量导入 -->
<div id="formulaConversion" class="addPoups popup" style="display: none;">

    <div class="title">
        <div class="name">导入</div>
        <div class="closeVoice"></div>
    </div>
    <div class="popup-con">
        <div class="formulaConversion">
            <div class="layui-upload">
                <div class="layui-upload-drag" id="test2">
                    <div class="label-box">
                        <span class="plus"></span>
                        <p>点击或拖拽文件到此处上传</p>
                        <p class="tips">只能上传EXCEL文件</p>
                        <blockquote class="layui-elem-quote layui-quote-nm">
                            <div class="layui-upload-list" id="demo2"></div>
                        </blockquote>
                        <!-- <p id="tipTxt"></p> -->
                    </div>
                </div>

            </div>

        </div>
    </div>
    <div class="bottom">
        <div class="download">下载导入模板</div>
        <button type="reset" class="layui-btn layui-btn-primary exam-cancle">取消</button>
        <button class="layui-btn exam-sure">确定</button>
    </div>


</div>
<!-- 语音识别 -->
<div id="voice" class="addPoups popup" style="display: none;">
    <div class="popup-con defaults">
        <div class="closeVoice"></div>
        <div class="p-top">
            <h2>请点击话筒开始录入</h2>
        </div>
        <div class="p-con">
            <div class="img">
                <img src="/score/mainexam/images/voice-img.png" alt="">
                <div class="glow hide"></div>
            </div>
            <div class="loading hide">
            </div>
            <div class="loading1 hide">
            </div>
            <div class="info">
                <div class="time"><em>6</em>
                    <input type="number" name="addpName" value="" autocomplete="off"
                           class="layui-input w60">
                </div>
                <div class="edit">修改</div>
                <div class="seek">
                    <span></span>
                </div>
            </div>
        </div>
        <div class="view-instructions">
            <p>请以姓名+分项+分数的形式唱录，您可以说：“张三 期中 60分”</p><span>查看说明</span>
        </div>

        <div class="view-boxs" style="display:none">
            <div class="view-head">查看说明</div>
            <div class="view-con">
                <p>请在规定时间内以姓名+分项+分数的形式对当前页内容进行语音录入<br>
                    您可以说：“<em>张三 期中 60分</em>”，或“<em>张三 期中 60分 期末 80分……</em>”</p>
            </div>
            <div class="view-footer">
                <span class="confirm">确定</span>
            </div>
        </div>
        <div class="incomplete" style="display:none">
            <div class="inc-con">
                <p>当前录入未完成，确定退出录入</p>
            </div>
            <div class="inc-footer">
                <div class="cancle">取消</div>
                <div class="confirm">确定</div>
            </div>
        </div>
    </div>

    <div class="popup-con identify">
        <div class="identifying">
            <h3>识别中</h3>
            <img src="/score/mainexam/images/identing.png" alt="">
        </div>
    </div>


</div>


<script type="text/html" id="barDemo3">
    <a class="edit opbtn" style="color:#3A8BFF;;cursor:pointer;" lay-event="edit">编辑</a>
    <a class="save opbtn" style="color:#3A8BFF;;cursor:pointer; display:none;margin-right:14px; "
       lay-event="save">保存</a>
    <a class="cancle opbtn" style="color:#3A8BFF;;cursor:pointer; display:none;" lay-event="cancle">取消</a>
</script>
<script src="/score/mainexam/js/jquery-3.3.1.min.js"></script>
<script src="/score/mainexam/layui/layui.js"></script>
<script type="text/javascript" src="/score/mainexam/js/recorder/recorder-core.js"></script>
<script type="text/javascript" src="/score/mainexam/js/recorder/engine/pcm.js"></script>
<script type="text/javascript" src="/score/mainexam/js/recorder/engine/wav.js"></script>
<script type="text/javascript" src="/score/mainexam/js/pinyin4js.js"></script>
<script type="text/javascript" th:src="@{~/js/my.util.js}"></script>
<script th:inline="javascript">
    var tabsMap = [[${tabsMap}]];
    var specialList = [[${specialList}]];
    var classCourse = [[${classCourse}]];
    var tid = $(".content").find(".tab").find("ul").find("li.cur").attr("tid");

    $(function () {
        var form, table, laydate, layer;
        layui.use(['form', 'table', 'laydate', 'upload'],
            function () {
                var $ = layui.jquery;
                form = layui.form;
                table = layui.table;
                laydate = layui.laydate;
                layer = layui.layer;
                upload = layui.upload;

                $(".main .content .back").click(function () {
                   window.close()
                });
                $(".content .btns-list span.export").click(function () {
                    if (!$(this).parent().hasClass("disabled")) {
                        layer.open({
                            type: 1,
                            title: false,
                            closeBtn: false,
                            shadeClose: true,
                            isOutAnim: true,
                            content: $('#listSorting'),
                            area: ['auto', 'auto'],
                            success: function () {
                            },
                        }, function () {

                        });
                    }
                })
                $("#listSorting .exam-sure").click(function () {
                	$("#listSorting .close").click();
                    var orderField = $("#listSorting select[name='modules']").val();
                    var orderInt = $("#listSorting input[name='gradeType']:checked").val();
                    window.location.href = "/api/score/mainexamgrade/importExcel?orderField=" + orderField + "&orderInt=" + orderInt + "&classCode=" + [[${classCourse?.classCode}]]
                        + "&courseCode=" + [[${classCourse?.courseCode}]] + "&xnxq=" + [[${xnxq}]] + "&courseId=" + [[${classCourse?.id}]] + "&levelid=" + tid;
                });
                $(".download").click(function (){
                	window.location.href = "/api/score/mainexamgrade/importExcel?classCode=" + [[${classCourse?.classCode}]]
                        + "&courseCode=" + [[${classCourse?.courseCode}]] + "&xnxq=" + [[${xnxq}]] + "&courseId=" + [[${classCourse?.id}]] + "&levelid=" + tid;
                });
                //批量导入
                $(".content .btns-list span.import").click(function () {

                    if (!$(this).parent().hasClass("disabled")) {
                        layer.open({
                            type: 1,
                            title: false,
                            closeBtn: false,
                            shadeClose: true,
                            isOutAnim: true,
                            content: $('#formulaConversion'),
                            area: ['auto', 'auto'],
                            success: function () {
                            },
                        }, function () {

                        });
                    }
                })

                upload.render({
                    elem: '#test2',
                    url: '/api/score/mainexamgrade/importStu?levelid=' + tid + "&classCode=" + [[${classCourse?.classCode}]]
                        + "&courseCode=" + [[${classCourse?.courseCode}]] + "&courseId=" + [[${classCourse?.id}]] + "&xnxq=" + [[${xnxq}]] //改成您自己的上传接口
                    ,
                    multiple: true,
                    accept: 'images',
                    exts: 'xls|xlsx',
                    drag: true,
                    allDone: function (obj) {

                    },
                    before: function (obj) {
                        //预读本地文件示例，不支持ie8
                        obj.preview(function (index, file, result) {
                            $('#demo2').append('<p>' + file.name + '</p>')
                        });

                    },
                    done: function (res) {
                        $('#demoText').html('');
                        //上传完毕
                        if (res.code == 200) {
                            layer.msg('上传完毕', {
                                icon: 1
                            });
                        }else {
                        	layer.msg(res.msg);
                        }
                    }
                });
				$("#formulaConversion .exam-sure").click(function (){
					$("#formulaConversion .exam-cancle").click();
				});
                var table1fieldArray = [];
                table1fieldArray.push({
                    field: 'stuNum', align: "center", title: "学号", minWidth: 200,
                    templet: function (e) {
                        return "<div data-id='" + e.id + "'>" + e.stuNum + "</div>";
                    }
                });
                table1fieldArray.push({field: 'stuName', align: 'center', title: "姓名", minWidth: 120});
                if (!U.isEmpty(tabsMap)) {
                    tabsMap.forEach(function (e) {
                        if (e.tabsId == tid) {
                            e.clos.forEach(function (c) {
                                var sectionSetList = "";
                                if (!U.isEmpty(c.gradeMakeRule)) {
                                    sectionSetList = c.gradeMakeRule.sectionSetList;
                                }
                                if (!U.isEmpty(c.gradeMakeRule) && c.gradeMakeRule.type == 0) {
                                    table1fieldArray.push({
                                        field: c.colValue,
                                        align: 'center',
                                        title: c.colName,
                                        minWidth: 120,
                                        templet: function (d) {
                                            var showValue = "";
                                            var colName = "";
                                            $.each(d, function (key, value) {
                                                if (key == c.colValue) {
                                                    showValue = value;
                                                    colName = c.colName;
                                                }
                                            });
                                            return '<div class="inputs td_' + d.id + '">' +
                                                '<div class="final"data-userName="' + getPinYi(d.stuName) + '"  data-colName="' + getPinYi(colName) + '">' + showValue + '</div>' +
                                                '<input type="text" name="addpName" colid="' + c.colId + '" ' +
                                                'userid="' + d.stuNum + '" username="' + d.stuName + '" ' +
                                                'value="' + showValue + '" ' +
                                                'dataid="' + d.id + '" ' +
                                                'autocomplete="off" ' +
                                                'data-userName="' + getPinYi(d.stuName) + '" ' +
                                                'data-colName="' + getPinYi(colName) + '" ' +
                                                'class="layui-input w60 user-option-input"></div>';
                                        },
                                    });
                                } else if (!U.isEmpty(c.gradeMakeRule) && c.gradeMakeRule.type == 1) {
                                    table1fieldArray.push({
                                        field: c.colValue,
                                        align: 'center',
                                        title: c.colName,
                                        minWidth: 120,
                                        templet: function (d) {
                                            var showValue = "";
                                            $.each(d, function (key, value) {
                                                if (key == c.colValue) {
                                                    showValue = value;
                                                }
                                            });
                                            var html = '<div class="selects w110">' +
                                                '<div class="final">' + showValue + '</div>' +
                                                '<select name="module4" lay-filter="module4" lay-verify="required" colid="' + c.colId + '" userid="' + d.stuNum + '" username="' + d.stuName + '" dataid="' + d.id + '" class="user-option-select">' +
                                                '<option value="">请选择</option>';
                                            if (!U.isEmpty(sectionSetList)) {
                                                sectionSetList.forEach(function (s) {
                                                    if (s.id == showValue) {
                                                        html += "<option selected value='" + s.id + "'>" + s.name + "</option>";
                                                    } else {
                                                        html += "<option value='" + s.id + "'>" + s.name + "</option>";
                                                    }
                                                })
                                            }
                                            html += '</select></div>';
                                            return html;
                                        },
                                    });
                                } else {
                                    table1fieldArray.push({
                                        field: c.colValue,
                                        align: 'center',
                                        title: c.colName,
                                        minWidth: 120
                                    });
                                }
                                if (!U.isEmpty(c.gradeMakeRule)) {
                                    table1fieldArray.push({
                                        field: "specialResult" + c.colId,
                                        align: 'center',
                                        title: "特殊成绩标识",
                                        minWidth: 120,
                                        templet: function (d) {
                                            var showValue = "";
                                            var showName = "";
                                            $.each(d, function (key, value) {
                                                if (key == "specialResult" + c.colId) {
                                                    showValue = value;
                                                }
                                            });
                                            specialList.forEach(function (s) {
                                                if (s.id == showValue) {
                                                    showName = s.specialResultName;
                                                }
                                            })
                                            var html = '<div class="selects w110" colid="' + c.colId + '"> <div class="final">' + showName + '</div>  <select id="specialResult_' + c.colId + '_' + d.stuNum + '" name="module4" lay-filter="module4" lay-verify="required">' +
                                                '<option value="">请选择</option>';
                                            if (!U.isEmpty(specialList)) {
                                                specialList.forEach(function (s) {
                                                    if (s.id == showValue) {
                                                        html += "<option selected value='" + s.id + "' code='" + s.specialResultCode + "' score='" + s.specialResultScore + "'>" + s.specialResultName + "</option>";
                                                    } else {
                                                        html += "<option value='" + s.id + "' code='" + s.specialResultCode + "' score='" + s.specialResultScore + "'>" + s.specialResultName + "</option>";
                                                    }
                                                })
                                            }
                                            html += '</select></div>';
                                            return html;
                                        },
                                    });
                                }
                            })
                        }
                    })
                }
                /* if(!U.isEmpty(classCourse.gpaRule)){
                    table1fieldArray.push({
                        field: "gpa",
                        align: 'center',
                        title: "绩点",
                        minWidth: 120
                    });
                } */
                table1fieldArray.push({
                    field: "options",
                    align: 'center',
                    title: "操作",
                    toolbar: "#barDemo3",
                    fixed: "right",
                    minWidth: 200
                });

                var table1 = table.render({
                    elem: "#materialTable",
                    id: 'materialTable',
                    page: {
                        limit: 10,
                        limits: [10, 20, 30, 40, 50],
                        layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                    },
                    url: "/api/score/mainexamgrade/getStudentList",
                    where: {
                        'fid': [[${fid}]],
                        "classCode": [[${classCourse?.classCode}]],
                        "courseCode": [[${classCourse?.courseCode}]],
                        "courseId": [[${classCourse?.id}]],
                        'xnxq': [[${xnxq}]],
                        "tid": tid
                    },
                    request: {
                        pageName: "curPage",
                        limitName: "pageSize"
                    },
                    cols: [table1fieldArray],
                    parseData: function (res) {
                        return {
                            "count": res.data.count,
                            "data": res.data.dataList,
                            "code": 0
                        }
                    },
                    done: function (res) {
                    }
                })
                //切换

                $(".content").on("click", ".tab ul li", function () {
                    $(this).addClass("cur").siblings().removeClass("cur");
                    let ind = $(this).index();
                    $(".content .item-list .item").eq(ind).addClass("active").siblings().removeClass("active");
                    tid = $(this).attr("tid");
                    initTable(tid);
                })

                //隐藏弹窗
                $('.close,.exam-cancle').on("click", function () {
                    var index = $(this).parents(".layui-layer").attr("times");
                    layer.close(index);
                    table.reload('materialTable');
                })

                //成绩录入
                $(".content .btns-list .entry").click(function () {
                    $(this).hide();
                    $(this).parent().find("em").show();
                    $(".layui-table tbody tr").addClass("editable");
                    $(".layui-table tbody tr").addClass("disabled");
                    $(this).parents(".btns-list").addClass("disabled");
                })

                //取消
                $(".content .btns-list .enter .cancle").click(function () {
                    $(this).parent().find("em").hide();
                    $(this).parent().find("span").show();
                    $(this).parents(".btns-list").removeClass("disabled");
                    table.reload('materialTable');
                })

                //保存
                $(".content .btns-list .enter .save").click(function () {
                    $(this).parent().find("em").hide();
                    $(this).parent().find("span").show();
                    $(".layui-table tbody tr").removeClass("editable");
                    $(".layui-table tbody tr").removeClass("disabled");
                    $(".layui-table-cell .save,.layui-table-cell .cancle").hide();
                    $(".layui-table-cell .edit").show();
                    $(this).parents(".btns-list").removeClass("disabled");
                    var dataArr = [];
                    var fid = [[${fid}]];
                    var xnxq = [[${xnxq}]];
                    var creatUid = [[${uid}]];
                    var levelid = tid;
                    if ($(".user-option-input").length > 0) {
                        $(".user-option-input").each(function () {
                            var obj = {};
                            var stuNum = $(this).attr("userid");
                            var stuName = $(this).attr("username");
                            var score = $(this).val();
                            var scoreType = 0;
                            var secondLevelId = $(this).attr("colid");
                            var specialId = $("#specialResult_" + secondLevelId + "_" + stuNum).find("option:selected").val();
                            obj["fid"] = fid;
                            obj["xnxq"] = xnxq;
                            obj["creatUid"] = creatUid;
                            obj["levelid"] = levelid;
                            obj["stuNum"] = stuNum;
                            obj["stuName"] = stuName;
                            obj["score"] = score;
                            obj["scoreType"] = scoreType;
                            obj["secondLevelId"] = secondLevelId;
                            obj["specialId"] = specialId;
                            obj["teachclasstudentid"] = $(this).attr("dataid");
                            dataArr.push(obj);
                        })
                    }
                    if ($(".user-option-select").length > 0) {
                        $(".user-option-select").each(function () {
                            var stuNum = $(this).attr("userid");
                            var stuName = $(this).attr("username");
                            var scoreType = 1;
                            var score = $(this).find("option:selected").val();
                            var secondLevelId = $(this).attr("colid");
                            var specialId = $("#specialResult_" + secondLevelId + "_" + stuNum).find("option:selected").val();
                            obj["fid"] = fid;
                            obj["xnxq"] = xnxq;
                            obj["creatUid"] = creatUid;
                            obj["levelid"] = levelid;
                            obj["stuNum"] = stuNum;
                            obj["stuName"] = stuName;
                            obj["score"] = score;
                            obj["scoreType"] = scoreType;
                            obj["secondLevelId"] = secondLevelId;
                            obj["specialId"] = specialId;
                            obj["teachclasstudentid"] = $(this).attr("dataid");
                            dataArr.push(obj);
                        })
                    }
                    $.ajax({
                        type: "POST",
                        url: "/api/score/mainexamgrade/saveMainexamScoreenterlog",
                        data: {dataArr: JSON.stringify(dataArr)},
                        dataType: 'json',
                        async: false,
                        success: function (result) {
                            if (result.code == 200) {
                                table.reload('materialTable');
                            } else {
                                layer.msg(result.msg);
                            }
                        }
                    });
                })

                table.on("tool(materialTable)", function (obj) {
                    var data = obj.data; //获得当前行数据
                    var tr = obj.tr
                    console.log(tr);
                    if (!$(this).parents("tr").hasClass("disabled")) {
                    	var index = $(this).parents("tr").index();
                        if (obj.event === "edit") {
                        	$(this).parents(".layui-table-box").find(".layui-table-main").find("tr").eq(index).addClass("editable");
                            //$(this).parents("tr").addClass("editable");
                            $(this).parents("tr").find(".save").show();
                            $(this).parents("tr").find(".cancle").show();
                            $(this).parents("tr").find(".edit").hide();
                        }
                        if (obj.event === "save") {
                            $(this).parents(".layui-table-box").find(".layui-table-main").find("tr").eq(index).removeClass("editable");
                            $(this).parents("tr").find(".save").hide();
                            $(this).parents("tr").find(".cancle").hide();
                            $(this).parents("tr").find(".edit").show();
                            var dataArr = [];
                            var teachclasstudentid = data.id;
                            var stuNum = data.stuNum;
                            var stuName = data.stuName;
                            var fid = [[${fid}]];
                            var xnxq = [[${xnxq}]];
                            var creatUid = [[${uid}]];
                            var levelid = tid;
                            if ($(".user-option-input").length > 0) {
                                $(".user-option-input").each(function () {
                                    if ($(this).attr("userid") == stuNum) {
                                        var obj = {};
                                        var score = $(this).val();
                                        var scoreType = 0;
                                        var secondLevelId = $(this).attr("colid");
                                        var specialId = $("#specialResult_" + secondLevelId + "_" + stuNum).find("option:selected").val();
                                        obj["fid"] = fid;
                                        obj["xnxq"] = xnxq;
                                        obj["creatUid"] = creatUid;
                                        obj["levelid"] = levelid;
                                        obj["stuNum"] = stuNum;
                                        obj["stuName"] = stuName;
                                        obj["score"] = score;
                                        obj["scoreType"] = scoreType;
                                        obj["secondLevelId"] = secondLevelId;
                                        obj["specialId"] = specialId;
                                        obj["teachclasstudentid"] = teachclasstudentid;
                                        dataArr.push(obj);
                                    }
                                });
                            }
                            if ($(".user-option-select").length > 0) {
                                $(".user-option-select").each(function () {
                                    if ($(this).attr("userid") == stuNum) {
                                        var obj = {};
                                        var scoreType = 1;
                                        var score = $(this).find("option:selected").val();
                                        var secondLevelId = $(this).attr("colid");
                                        var specialId = $("#specialResult_" + secondLevelId + "_" + stuNum).find("option:selected").val();
                                        obj["fid"] = fid;
                                        obj["xnxq"] = xnxq;
                                        obj["creatUid"] = creatUid;
                                        obj["levelid"] = levelid;
                                        obj["stuNum"] = stuNum;
                                        obj["stuName"] = stuName;
                                        obj["score"] = score;
                                        obj["scoreType"] = scoreType;
                                        obj["secondLevelId"] = secondLevelId;
                                        obj["specialId"] = specialId;
                                        obj["teachclasstudentid"] = $(this).attr("dataid");
                                        dataArr.push(obj);
                                    }
                                })
                            }
                            $.ajax({
                                type: "POST",
                                url: "/api/score/mainexamgrade/saveMainexamScoreenterlog",
                                data: {dataArr: JSON.stringify(dataArr)},
                                dataType: 'json',
                                success: function (result) {
                                    if (result.code == 200) {
                                        table.reload('materialTable');
                                    } else {
                                        layer.msg(result.msg);
                                    }
                                }
                            });
                        }
                        if (obj.event === "cancle") {
                            /* $(this).parents(".layui-table-box").find(".layui-table-main").find("tr").eq(index).removeClass("editable");
                            $(this).parents("tr").find(".save").hide();
                            $(this).parents("tr").find(".cancle").hide();
                            $(this).parents("tr").find(".edit").show(); */
                            table.reload('materialTable');
                        }
                    }

                });

                function initTable(tid) {
                    var table1fieldArray = [];
                    table1fieldArray.push({
                        field: 'stuNum', align: "center", title: "学号", minWidth: 200,
                        templet: function (e) {
                            return "<div data-id='" + e.id + "'>" + e.stuNum + "</div>";
                        }
                    });
                    table1fieldArray.push({field: 'stuName', align: 'center', title: "姓名", minWidth: 120});
                    if (!U.isEmpty(tabsMap)) {
                        tabsMap.forEach(function (e) {
                            if (e.tabsId == tid) {
                                e.clos.forEach(function (c) {
                                    var sectionSetList = "";
                                    if (!U.isEmpty(c.gradeMakeRule)) {
                                        sectionSetList = c.gradeMakeRule.sectionSetList;
                                    }
                                    if (!U.isEmpty(c.gradeMakeRule) && c.gradeMakeRule.type == 0) {
                                        table1fieldArray.push({
                                            field: c.colValue,
                                            align: 'center',
                                            title: c.colName,
                                            minWidth: 120,
                                            templet: function (d) {
                                                var showValue = "";
                                                $.each(d, function (key, value) {
                                                    if (key == c.colValue) {
                                                        showValue = value;
                                                    }
                                                });
                                                return '<div class="inputs td_' + d.id + '">' +
                                                    '<div class="final">' + showValue + '</div>' +
                                                    '<input type="text" name="addpName" colid="' + c.colId + '" userid="' + d.stuNum + '" username="' + d.stuName + '" dataid="' + d.id + '" value="' + showValue + '" autocomplete="off" class="layui-input w60 user-option-input"></div>';
                                            },
                                        });
                                    } else if (!U.isEmpty(c.gradeMakeRule) && c.gradeMakeRule.type == 1) {
                                        table1fieldArray.push({
                                            field: c.colValue,
                                            align: 'center',
                                            title: c.colName,
                                            minWidth: 120,
                                            templet: function (d) {
                                                var showValue = "";
                                                $.each(d, function (key, value) {
                                                    if (key == c.colValue) {
                                                        showValue = value;
                                                    }
                                                });
                                                var html = '<div class="selects w110" colid="' + c.colId + '" >' +
                                                    '<div class="final">' + showValue + '</div>' +
                                                    '<select name="module4" lay-filter="module4" lay-verify="required" colid="' + c.colId + '" userid="' + d.stuNum + '" username="' + d.stuName + '" dataid="' + d.id + '" class="user-option-select">' +
                                                    '<option value="">请选择</option>';
                                                if (!U.isEmpty(sectionSetList)) {
                                                    sectionSetList.forEach(function (s) {
                                                        if (s.id == showValue) {
                                                            html += "<option selected value='" + s.id + "'>" + s.name + "</option>";
                                                        } else {
                                                            html += "<option value='" + s.id + "'>" + s.name + "</option>";
                                                        }
                                                    })
                                                }
                                                html += '</select></div>';
                                                return html;
                                            },
                                        });
                                    } else {
                                        table1fieldArray.push({
                                            field: c.colValue,
                                            align: 'center',
                                            title: c.colName,
                                            minWidth: 120
                                        });
                                    }
                                    if (!U.isEmpty(c.gradeMakeRule)) {
                                        table1fieldArray.push({
                                            field: "specialResult" + c.colId,
                                            align: 'center',
                                            title: "特殊成绩标识",
                                            minWidth: 120,
                                            templet: function (d) {
                                                var showValue = "";
                                                var showName = "";
                                                $.each(d, function (key, value) {
                                                    if (key == "specialResult" + c.colId) {
                                                        showValue = value;
                                                    }
                                                });
                                                specialList.forEach(function (s) {
                                                    if (s.id == showValue) {
                                                        showName = s.specialResultName;
                                                    }
                                                })
                                                var html = '<div class="selects w110" colid="' + c.colId + '"> <div class="final">' + showName + '</div>  <select name="module4" lay-filter="module4" lay-verify="required" id="specialResult_' + c.colId + '_' + d.stuNum + '">' +
                                                    '<option value="">请选择</option>';
                                                if (!U.isEmpty(specialList)) {
                                                    specialList.forEach(function (s) {
                                                        if (s.id == showValue) {
                                                            html += "<option selected value='" + s.id + "' code='" + s.specialResultCode + "'>" + s.specialResultName + "</option>";
                                                        } else {
                                                            html += "<option value='" + s.id + "' code='" + s.specialResultCode + "'>" + s.specialResultName + "</option>";
                                                        }
                                                    })
                                                }
                                                html += '</select></div>';
                                                return html;
                                            },
                                        });
                                    }
                                })
                            }
                        })
                    }
                    /* if(!U.isEmpty(classCourse.gpaRule)){
                        table1fieldArray.push({
                            field: "gpa",
                            align: 'center',
                            title: "绩点",
                            minWidth: 120
                        });
                    } */
                    table1fieldArray.push({
                        field: "options",
                        align: 'center',
                        title: "操作",
                        toolbar: "#barDemo3",
                        fixed: "right",
                        minWidth: 200
                    });

                    var table1 = table.render({
                        elem: "#materialTable",
                        id: 'materialTable',
                        page: {
                            limit: 10,
                            limits: [10, 20, 30, 40, 50],
                            layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                        },
                        url: "/api/score/mainexamgrade/getStudentList",
                        where: {
                            'fid': [[${fid}]],
	                        "classCode": [[${classCourse?.classCode}]],
	                        "courseCode": [[${classCourse?.courseCode}]],
	                        "courseId": [[${classCourse?.id}]],
	                        'xnxq': [[${xnxq}]],
	                        "tid": tid
                        },
                        request: {
                            pageName: "curPage",
                            limitName: "pageSize"
                        },
                        cols: [table1fieldArray],
                        parseData: function (res) {
                            return {
                                "count": res.data.count,
                                "data": res.data.dataList,
                                "code": 0
                            }
                        },
                        done: function (res) {
                        }
                    })
                }

                /*table.on("tool(materialTable1)", function (obj) {
                    var data = obj.data; //获得当前行数据
                    if (!$(this).parents("tr").hasClass("disabled")) {
                        if (obj.event === "edit") {
                            $(this).parents("tr").addClass("editable");
                            $(this).parents("tr").find(".save").show();
                            $(this).parents("tr").find(".cancle").show();
                            $(this).parents("tr").find(".edit").hide();
                        }
                        if (obj.event === "save") {
                            $(this).parents("tr").removeClass("editable");
                            $(this).parents("tr").find(".save").hide();
                            $(this).parents("tr").find(".cancle").hide();
                            $(this).parents("tr").find(".edit").show();
                        }
                        if (obj.event === "cancle") {
                            $(this).parents("tr").removeClass("editable");
                            $(this).parents("tr").find(".save").hide();
                            $(this).parents("tr").find(".cancle").hide();
                            $(this).parents("tr").find(".edit").show();
                        }
                    }

                });*/
                /*table.on("tool(materialTable2)", function (obj) {
                    var data = obj.data; //获得当前行数据
                    if (!$(this).parents("tr").hasClass("disabled")) {
                        if (obj.event === "edit") {
                            $(this).parents("tr").addClass("editable");
                            $(this).parents("tr").find(".save").show();
                            $(this).parents("tr").find(".cancle").show();
                            $(this).parents("tr").find(".edit").hide();
                        }
                        if (obj.event === "save") {
                            $(this).parents("tr").removeClass("editable");
                            $(this).parents("tr").find(".save").hide();
                            $(this).parents("tr").find(".cancle").hide();
                            $(this).parents("tr").find(".edit").show();
                        }
                        if (obj.event === "cancle") {
                            $(this).parents("tr").removeClass("editable");
                            $(this).parents("tr").find(".save").hide();
                            $(this).parents("tr").find(".cancle").hide();
                            $(this).parents("tr").find(".edit").show();
                        }
                    }
                });*/

                $(".speech").click(function () {
                    initLayTime();
                })

                function initLayTime() {
                    layTime = layer.open({
                        type: 1,
                        title: false,
                        closeBtn: false,
                        shadeClose: false,
                        isOutAnim: true,
                        content: $('#voice'),
                        area: ['auto', 'auto'],
                        success: function () {
                        },
                    }, function () {

                    });
                }


                var timers = null;

                //识别点击

                $("#voice .popup-con .p-con .img img").click(function () {
                    if (!$(this).hasClass("cur")) {
                        startRec();
                    } else {
                        $(".defaults").hide();
                        $(".identify").show();
                        stopRec()
                    }

                })

                function listen(){
                    if (!$("#voice .popup-con .p-con .img img").hasClass("cur")) {
                        $("#voice .popup-con .p-con .img img").addClass("cur");
                    }
                    $("#voice .popup-con .p-top h2").text("倾听中");
                    let tempTime = time;
                    timers = setInterval(function () {
                        tempTime--;
                        if (tempTime <= 0) {
                            clearInterval(timers);
                            $(".defaults").hide();
                            $(".identify").show();

                            stopRec()
                            $("#voice .popup-con .p-con .img img").click()
                        } else {
                            $("#voice .popup-con .p-con .info .time em").text(tempTime);
                        }
                    }, 1000)

                    $("#voice .popup-con .p-con .img .glow").removeClass("hide");
                    $("#voice .popup-con .p-con .loading").removeClass("hide");
                    $("#voice .popup-con .p-con .loading1").removeClass("hide");
                    $("#voice .popup-con .p-con .info .edit").hide();
                    $("#voice .popup-con .p-con .info .seek").hide();
                    $("#voice .popup-con .view-instructions span").hide();
                }

                function clear(){
                    if ($("#voice .popup-con .p-con .img img").hasClass("cur")) {
                        $("#voice .popup-con .p-con .img img").removeClass("cur");
                    }
                    clearInterval(timers);
                    $("#voice .popup-con .p-con .info .time em").text(time);
                    $("#voice .popup-con .p-top h2").text("请点击话筒开始录入");
                    $("#voice .popup-con .p-con .img .glow").addClass("hide");
                    $("#voice .popup-con .p-con .loading").addClass("hide");
                    $("#voice .popup-con .p-con .loading1").addClass("hide");
                    $("#voice .popup-con .p-con .info .edit").show();
                    $("#voice .popup-con .p-con .info .seek").show();
                    $("#voice .popup-con .view-instructions span").show();
                }


                //关闭

                $("#voice .popup-con .closeVoice").click(function () {
                    if ($(this).parent().find(".img img").hasClass("cur")) {
                        $(".incomplete").show();
                    } else {
                        layer.close(layTime);
                        //如果处于正在录音状态需要重置录音
                        var voice = $("#voice .popup-con .p-con .img img");
                        if (voice.hasClass("cur")) {
                            voice.click()
                        }
                    }
                })

                $("#voice .popup-con .incomplete .inc-footer .cancle").click(function () {
                    $(".incomplete").hide();
                })
                $("#voice .popup-con .incomplete .inc-footer .confirm").click(function () {
                    $(".incomplete").hide();
                    layer.close(layTime);
                    //如果处于正在录音状态需要重置录音
                    var voice = $("#voice .popup-con .p-con .img img");
                    if (voice.hasClass("cur")) {
                        voice.click()
                    }
                })

                //查看说明
                $("#voice .popup-con .view-instructions span").click(function () {
                    $(".view-boxs").show();
                })

                $("#voice .popup-con .view-boxs .view-footer span").click(function () {
                    $(".view-boxs").hide();
                })
                var time = 6;
                //修改
                $("#voice .popup-con .p-con .info .edit").click(function () {
                    $(this).toggleClass("cur");
                    if ($(this).hasClass("cur")) {
                        $(this).text('保存');
                        let values = $("#voice .popup-con .p-con .info .time").find("em").text();
                        $("#voice .popup-con .p-con .info .time").find("em").hide();
                        $("#voice .popup-con .p-con .info .time .layui-input").val(values).show();

                    } else {
                        let mids = $("#voice .popup-con .p-con .info .time .layui-input").val();
                        if (mids > 60) {
                            return alert("仅支持60秒以内的文本")
                        }
                        $("#voice .popup-con .p-con .info .time").find("em").text(mids);
                        $("#voice .popup-con .p-con .info .time .layui-input").hide();
                        $("#voice .popup-con .p-con .info .time").find("em").show();
                        $(this).text('修改');
                        time = mids;
                    }

                })

                //调用录音
                var rec;
                var userNotAllow;
                var pcmSet = {
                    type: "pcm",
                    sampleRate: 16000,
                    bitRate: 16,
                    onProcess: function (buffers, powerLevel, bufferDuration, bufferSampleRate) {
                        //推入实时处理，因为是unknown格式，buffers和rec.buffers是完全相同的，只需清理buffers就能释放内存。
                        // RealTimeSendTry(buffers,bufferSampleRate,false);
                    }
                };

                function startRec() {
                    rec = Recorder(pcmSet);
                    rec.open(function () {//打开麦克风授权获得相关资源
                        listen();
                        rec.start();
                    }, function (msg, isUserNotAllow) {
                        userNotAllow = isUserNotAllow;
                        alert((isUserNotAllow ? "UserNotAllow，" : "") + "无法录音:" + msg);
                        if (!userNotAllow) {
                            clear();
                            rec = null;
                        }
                    });
                }

                function stopRec() {
                    if (rec==null){
                        clear();
                        return;
                    }
                    rec.stop(function (blob, duration) {
                        rec.close();//释放录音资源，当然可以不释放，后面可以连续调用start；但不释放时系统或浏览器会一直提示在录音，最佳操作是录完就close掉
                        var form = new FormData();
                        form.append("file", blob, "recorder.pcm"); //和普通form表单并无二致，后端接收到upfile参数的文件，文件名为recorder.mp3
                        $.ajax({
                            url: "/api/socket/pcm/audio2text" //上传接口地址
                            , type: "POST"
                            , contentType: false //让xhr自动处理Content-Type header，multipart/form-data需要生成随机的boundary
                            , processData: false //不要处理data，让xhr自动处理
                            , data: form
                            , dataType: "json"
                            , success: function (res) {
                                console.log("上传成功", res);
                                $(".defaults").show();
                                $(".identify").hide();
                                clear();
                                if (res.code == 200) {
                                    //切割字符
                                    var result = splitVoice(res.data);
                                    if (result==null||result==undefined||result==""){
                                        alert("语音识别失败或无对应分项")
                                        return;
                                    }
                                    //成绩是否修改 默认未修改
                                    var flag={"status":false};
                                    for (let key in result) {
                                        if (key == "name") {
                                            continue;
                                        }
                                        //给所有相同名称的学生赋值
                                        $("input[data-username='" + result.name + "'][data-colname='" + key + "']").each((index, data) => {
                                            if (!flag.status){
                                                flag.status=true
                                            }
                                            $(data).val(result[key])
                                        })
                                        $("div[data-username='" + result.name + "'][data-colname='" + key + "']").each((index, data) => {
                                            if (!flag.status){
                                                flag.status=true
                                            }
                                            $(data).html(result[key])
                                        })
                                    }
                                    //成绩有修改，调用保存方法
                                    if (flag.status){
                                        $(".content .btns-list .enter .save").click();
                                    }else {
                                        alert("语音识别结果："+res.data+"无对应分项成绩")
                                    }

                                } else {
                                    alert("语音识别失败")
                                }
                            }
                            , error: function (s) {
                                alert("语音识别失败")
                                $(".defaults").show();
                                $(".identify").hide();
                                clear();
                            }
                        });
                        rec = null;
                    }, function (msg) {

                    })
                }

                //将汉字转化成拼音
                function getPinYi(data) {
                    if (data == null || data == undefined || data == "") {
                        return "";
                    }
                    return PinyinHelper.convertToPinyinString(data, ' ', PinyinFormat.WITHOUT_TONE);
                }

                /**
                 * 切割语音识别后的字符串
                 * @param voice 语音识别的结果
                 * @returns {json|string|{}}
                 */
                function splitVoice(voice) {
                    if (voice == null || voice == undefined || voice == "") {
                        return "";
                    }
                    voice = getPinYi(voice);
                    var tid = $(".tab ul li.cur").attr("tid");
                    var data = ''
                    tabsMap.forEach((tempData, index) => {
                        if (tempData.tabsId == tid) {
                            data = tempData.clos;
                        }
                    })

                    if (data==''){
                        return null;
                    }

                    var result = {};
                    data.forEach((data, index) => {
                        var colName = getPinYi(data.colName)
                        if (voice.indexOf(colName) != -1) {
                            if (!result.name){
                                result["name"] = voice.slice(0, voice.indexOf(colName) - 1)
                            }
                            var start = voice.indexOf(colName) + colName.length;
                            result[colName] = parseFloat(voice.slice(start, start + 6))
                        }
                    })
                    return result;

                }

            })
    })

</script>

</body>

</html>