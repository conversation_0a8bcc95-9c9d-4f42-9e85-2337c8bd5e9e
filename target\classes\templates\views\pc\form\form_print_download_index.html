<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
    <head>
        <meta charset="UTF-8"/>
        <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
        <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
        <title>报表打印下载</title>
        <link rel="stylesheet" th:href="@{~/css/form/printUtil/global.css}">
        <link rel="stylesheet" th:href="@{~/plugin/layui/css/layui.css}">
        <link rel="stylesheet" th:href="@{~/css/form/printUtil/reset.css}">
        <link rel="stylesheet" th:href="@{~/css/form/printUtil/common.css}">
        <link rel="stylesheet" th:href="@{~/css/form/printUtil/index.css}">
        <script th:src="@{~/plugin/layui/layui.js}"></script>
        <style>
            .layui-form-select dl {
                max-height: 150px;
            }
            @keyframes layui-upbit {
            }
        </style>
    </head>
    <body>
        <!-- 导出模板 -->
        <div id="export-template" class="subitemadd popup" style="width: 458px;">
            <div class="title">
                <div class="name">导出模版</div>
                <!--                <div class="close"></div>-->
            </div>
            <div class="popup-con">
                <div class="layui-form" lay-filter="editForm">
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width: auto;">模版样式</label>
                        <div class="layui-input-block w240">
                            <select id="templateStyle" name="templateStyle" lay-filter="templateStyle"
                                    lay-verify="required">
                                <!--                                <option value="">请选择</option>-->
                                <!--                                <option value="0">样式一</option>-->
                                <!--                                <option value="1">样式二</option>-->
                                <!--                                <option value="2">样式三</option>-->
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item" style="margin-bottom:0;">
                        <label class="layui-form-label" style="width: auto;">导出格式</label>
                        <div class="layui-input-block w275" id="exportTypeRadio">
                            <!--                            <input type="radio" id="wordRadio" name="exportFormat" lay-filter="exportFormat" checked title="Word">-->
                            <!--                            <input type="radio" name="exportFormat" lay-filter="exportFormat" value="1" title="Excel">-->
                            <!--                            <input type="radio" id="pdfRadio" name="exportFormat" lay-filter="exportFormat" title="PDF" disabled>-->
                        </div>
                    </div>
                </div>
            </div>
            <div class="bottom">
                <button type="reset" class="layui-btn layui-btn-primary exam-cancle">取消</button>
                <button class="layui-btn exam-sure">确定</button>
            </div>
        </div>
        <script th:src="@{~/js/jquery-3.3.1.min.js}"></script>
        <script th:src="@{~/js/my.util.js}"></script>
        <script th:inline="javascript">
            var vo = {
                "formId": [[${vo.formId}]],
                "fid": [[${vo.fid}]],
                "uid": [[${vo.uid}]],
                "roleid": [[${vo.roleid}]],
                "queryId": [[${vo.queryId}]],
                "selectTotal": [[${vo.selectTotal}]]
            };

            // 立即执行函数，进入页面即执行
            $(function () {
                layui.use(["form", "jquery"], function () {
                    var $ = layui.jquery;
                    var form = layui.form;

                    var templateId;

                    // 存放模板样式数据的小缓存
                    var templateSelectArray = [];

                    $(".exam-sure").on("click", function () {
                        if (!templateId) {
                            layer.msg("请先选择1个模板样式", {icon: 2, time: 2000});
                            return;
                        }
                        downloadByTemplate(templateId);
                    });

                    $(".exam-cancle").on("click", function () {
                        layer.msg("取消下载", {icon: 1, time: 2000});
                        U.closePop();
                    });

                    // 绑定表单下拉框的列表项点击事件
                    form.on('select(templateStyle)', function (data) {
                        var elem = data.elem; // 获得 select 原始 DOM 对象
                        var value = data.value; // 获得被选中的值
                        var othis = data.othis; // 获得 select 元素被替换后的 jQuery 对象
                        templateId = value;
                        for (let i = 0; i < templateSelectArray.length; i++) {
                            var templateSelect = templateSelectArray[i];
                            if (value === templateSelect.id + "") {
                                renderExportTypeRadio(templateSelect);
                                break;
                            }
                        }
                    });

                    // 立即执行函数，进入页面执行
                    $(function () {
                        // 初始化显示页面
                        exportTemplateOpen();
                        // 初始化模板文件下拉框
                        initFormTemplateSelect();
                    });

                    // 打开导出模板的弹窗
                    function exportTemplateOpen() {
                        layer.open({
                            type: 1,
                            title: false,
                            closeBtn: false,
                            shadeClose: false,
                            isOutAnim: true,
                            content: $('#export-template'),
                            area: ['auto', 'auto'],
                            success: function (res) {
                            },
                        }, function () {
                            layer.msg("下载失败", {icon: 2, time: 2000});
                        });
                    }

                    // 初始化此表单的模板文件列表
                    function initFormTemplateSelect() {
                        $.ajax({
                            type: 'get',
                            url: '/api/form/print/util/template/list',
                            dataType: 'json',
                            data: vo,
                            success: function (res) {
                                if (res.code === 200) {
                                    renderTemplateSelect(res.data);
                                    templateSelectArray = res.data;
                                } else {
                                    layer.msg(res.msg, {icon: 2, time: 2000});
                                }
                            }
                        });
                    }

                    // 渲染模板样式下拉框
                    function renderTemplateSelect(templateList) {
                        $("#templateStyle").empty();
                        var defaultTemplate;
                        for (let i = 0; i < templateList.length; i++) {
                            var template = templateList[i];
                            if (template.isDefaultTemplate === 0) {
                                $("#templateStyle").append("<option selected value='" + template.id + "'>" + template.templateName + "</option>");
                                defaultTemplate = template;
                            } else {
                                $("#templateStyle").append("<option value='" + template.id + "'>" + template.templateName + "</option>");
                            }
                            form.render("select");
                        }
                        // 首先渲染默认的模板，如果未设置默认模板，则渲染第一个模板
                        if (defaultTemplate) {
                            renderExportTypeRadio(defaultTemplate);
                            templateId = defaultTemplate.id;
                        } else if (templateList.length > 0) {
                            renderExportTypeRadio(templateList[0]);
                            templateId = templateList[0].id;
                        }
                    }

                    // 渲染模板类型单选框
                    function renderExportTypeRadio(template) {
                        $("#exportTypeRadio").empty();
                        var html = "<input type='radio' name='exportFormat' title='Word' disabled>" +
                            "<input type='radio' name='exportFormat' title='PDF' disabled>";
                        if (template) {
                            var templateFileType = template.templateFileType;
                            if ("doc" === templateFileType || "docx" === templateFileType) {
                                html = "<input type='radio' name='exportFormat' checked title='Word'>" +
                                    "<input type='radio' name='exportFormat' title='PDF' disabled>";
                            } else if ("pdf" === templateFileType) {
                                html = "<input type='radio' name='exportFormat' title='Word' disabled>" +
                                    "<input type='radio' name='exportFormat' checked title='PDF'>";
                            }
                        }
                        $("#exportTypeRadio").append(html);
                        form.render("radio");
                    }

                    // 根据模板文件，下载数据的压缩包
                    function downloadByTemplate(templateId) {
                        vo.templateId = templateId;
                        $.ajax({
                            type: 'get',
                            url: '/api/form/print/util/file/download',
                            dataType: 'json',
                            data: vo,
                            success: function (res) {
                                layer.confirm(res.msg, function (index) {
                                    layer.close(index);
                                    U.closePop();
                                });
                            }
                        });
                    }
                });
            });
        </script>
    </body>
</html>