<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">

<head>
    <meta charset="UTF-8">
    <title>课表结构-模板页</title>
</head>

<body>
<th:block th:fragment="timetable">
    <div class="row theader">
        <div class="lab"></div>
        <div class="lab fb" th:each="day : ${weekDays}" th:text="${day}">周一</div>
    </div>
    <div class="row linear" pos="0">
                    <span class="icons" pos="0">
                        <em></em>
                    </span>
    </div>
    <th:block th:each="lessons : ${allDayLessons}">
        <div th:class="${lessons[0].lessonNum > 0 ? 'row row-colw' : 'row break'}">
            <div class="lab">
                <span th:text="${lessons[0].lessonNumName}">第1节</span>
                <div class="ckable">
                    <div class="up"></div>
                    <div class="down"></div>
                </div>
            </div>
            <div class="lab fb" th:each="lesson : ${lessons}">
                <div class="time-sel" th:id="'pos_'+${lesson.actualPos}" th:attr="no=${lesson.actualNum}">
                    <div class="t-start" th:text="${lesson.begin}">08:00</div>
                    <div class="sign">-</div>
                    <div class="t-end" th:text="${lesson.end}">08:40</div>
                    <div class="select-dropdown">
                        <ul class="dropdown-list lesson-time">
                            <li class="cur">08:30</li>
                            <li>08:35</li>
                            <li>08:40</li>
                            <li>08:45</li>
                            <li>08:50</li>
                            <li>08:55</li>
                            <li>08:60</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="delet" th:attr="pos=${lessons[0].actualNum}" th:if="${lessons[0].lessonNum == 0}"></div>
        </div>
        <div class="row linear" th:attr="pos=${lessons[0].actualNum}">
                    <span class="icons" th:attr="pos=${lessons[0].actualNum}">
                        <em></em>
                    </span>
        </div>
    </th:block>
    <script th:inline="javascript">
        $(function () {
            $("#timetable-id").val([[${id}]]);
            if ([[${!#lists.isEmpty(weekDays)}]]) {
                initLessonSelectTime();
                $(".no-data").hide();
            } else {
                $(".no-data").show();
            }
            $(".select-dropdown").mCustomScrollbar({
                axis: "y"
            });
        });
    </script>
</th:block>
</body>

</html>