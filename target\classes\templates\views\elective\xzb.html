<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no,initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <title>[[${xzb}]]</title>
    <script src="js/responsive.js"></script>
    <link rel="stylesheet" href="css/global.css">
    <link rel="stylesheet" href="css/iosSelect.css">
    <link rel="stylesheet" href="css/commonV1.css">
    <link rel="stylesheet" href="css/staV1.css">
</head>

<body>
<div class="search-wrap">
    <div class="current-state">
        <div class="time">
            <span>当前时间：</span>
            <em id="dateTime">[[${courseTime}]]</em>
        </div>
        <div class="btn selDate" style="display: none">
            <span>切换时间</span>
        </div>
    </div>
    <div class="search flex">
        <img src="images/search.png" alt="">
        <input type="text" placeholder="搜索学生姓名或学号" id="keyword" >
    </div>
</div>
<div class="stu-list-wrap">
    <div class="class-mes">
        <ul>
            <li >正常:<i>0</i></li>
            <li th:each="state:${rollcallStateList}" th:inline="text">[[${state.stateNamePc}]]:<i>0</i></li>
<!--            <li id="kk">旷课:<i>0</i></li>-->
<!--            <li id="qj">请假:<i>0</i></li>-->
<!--            <li id="zt">早退:<i>0</i></li>-->
        </ul>
    </div>
    <div class="total">
        <span>共<i>0</i>名学生</span>
    </div>
    <div class="class-list">
    </div>
</div>


</body>
<script src="js/CXJSBridge.js"></script>
<script src="js/iosSelect.js"></script>
<script src="js/zepto.min.js"></script>
<script src="js/slideV1.js"></script>
<script src="js/export.js"></script>
<script th:inline="javascript">
    var xzb = [[${xzb}]];
    var xzbCode = [[${xzbCode}]];
    var courseTime = [[${courseTime}]];
    var rollCall = [[${rollCall}]];
    var bzr = [[${bzr}]];
    var fid = [[${fid}]];
    var rollcallStateList = [[${rollcallStateList}]];
    var children = [{menu: '导出', option: 'exportXzbData()'}];
    if (bzr){
        children.push({menu: '请假', option: 'leave()'});
        $(".selDate").show();
    }
    // $("#keyword").keyup(function (e) {
        // if (e.keyCode == 13) {
        //    getList();
        // }
    // })
    var search = false;
    $('#keyword').on({
        'compositionstart': function() {
            search = true;
        },
        'compositionend': function() {
            search = false;
            if(!search) {
                getList();
            }
        },
        'input propertychange': function() {
            if(!search) {
                getList();
            }
        }
    });
    function _jsBridgeReady() {

        jsBridge.postNotification('CLIENT_CUSTOM_MENU', {
            show: 1,
            icon: 'https://p.ananas.chaoxing.com/star3/origin/39834c7ca00ad3b5885a12722b25ee8b.png',
            height: 24,
            width: 24,
            menu: '',
            children: children
        });
    }

    function exports() {
        console.log("111");
    }

    function leave() {
        $.ajax({
            type: "POST",
            url: "/elective/rollcall/getLeaveUrl",
            data: {},
            dataType: 'json',
            success: function (data) {
                if (data.code==200){
                    jsBridge.postNotification('CLIENT_OPEN_URL', {
                        "title": "请假",
                        "toolbarType": 2,
                        "webUrl": data.data,
                        "loadType": 1
                    });
                }else {
                    jsBridge.postNotification('CLIENT_DISPLAY_MESSAGE', {'message': '打开失败，请重试', 'gravity': '1'});
                }
            }
        });
    }

    $(document).ready(function () {
        var stuH = $(window).height() - $(".stu-list-wrap").offset().top;
        $(".stu-list-wrap").css({height: stuH + "px"});


        //跳转

        $(".stu-list-wrap").on('click', ".class-list .stu", function () {
            if (!rollCall) {
                jsBridge.postNotification('CLIENT_DISPLAY_MESSAGE', {'message': '没有点名数据', 'gravity': '1'});
                return false;
            }
            // 以实际地址为准，此地址为本地测试
            var title = $(this).attr("uuxm");
            var webUrl = window.location.origin + "/elective/rollcall/xzbDetail?courseTime="+courseTime+"&uid="+$(this).attr("uuuid")+"&uname="+title;
            jsBridge.postNotification('CLIENT_OPEN_URL', {
                "title": title,
                "toolbarType": 2,
                "webUrl": webUrl,
                "loadType": 1
            });
            return false;
        });


    })

    var uids = [];
    getList(true);

    function getList(first) {
        var keyword = $("#keyword").val();
        var classDate = $("#dateTime").text();
        if (classDate != courseTime) {
            courseTime = classDate;
            $.ajax({
                type: "POST",
                url: "/elective/rollcall/isRollCall",
                data: {courseTime: courseTime, xzbCode: xzbCode},
                dataType: 'json',
                async: false,
                success: function (data) {
                    rollCall = data.data;
                }
            });
            getAllNum();
            first = true;
        }
        $.ajax({
            type: "POST",
            url: "/elective/rollcall/getXzbStudents",
            data: {xzbCode: xzbCode, keyword: keyword},
            dataType: 'json',
            async: false,
            success: function (data) {
                var html = "";
                uids = [];
                for (var i = 0; i < data.data.length; i++) {
                    uids.push(data.data[i].xsjbxx_uid);
                    html += "<div class='stu' uuuid = '" + data.data[i].xsjbxx_uid + "' uuxm = '" + data.data[i].xsjbxx_xm + "'>";
                    html += "<div class='order'>" + (i + 1) + "</div>";
                    html += "<h1><span>" + data.data[i].xsjbxx_xm + "</span><em>" + data.data[i].xsjbxx_xh + "</em></h1>";
                    html += "<ul id='stu" + data.data[i].xsjbxx_uid + "'>";
                    html += "<li>正常:<i>0</i></li>";
                    for (let j = 0; j < rollcallStateList.length; j++) {
                        html += "<li>"+rollcallStateList[j].stateNamePc+":<i>0</i></li>";
                    }
                    html += "</ul>";
                    html += "</div>";
                }
                if (first) {
                    $(".total").find("i").html(data.data.length);
                }
                $(".class-list").html(html);
            }
        });
        if (rollCall) {
            getDtail();
        }
    }

    function getDtail() {
        $.each(uids, function (index, item) {
            if (item != undefined) {
                getStuNum(item);
            }
        });
    }

    function getStuNum(uid) {
        $.ajax({
            type: "POST",
            url: "/elective/rollcall/getStuNumV2",
            data: {courseTime: courseTime, uid: uid},
            dataType: 'json',
            success: function (data) {
                for (let i = 0; i < data.data.length; i++) {
                    $("#stu" + uid).find("li").eq(i).find("i").text(data.data[i]);
                }
            }
        });
    }

    function getAllNum() {
        $.ajax({
            type: "POST",
            url: "/elective/rollcall/getXzbNumV2",
            data: {courseTime: $("#dateTime").val(), xzbCode: xzbCode},
            dataType: 'json',
            success: function (data) {
                for (let i = 0; i < data.data.length; i++) {
                    $(".class-mes ul").find("li").eq(i).find("i").text(data.data[i]);
                }
            }
        });
    }
</script>
</html>