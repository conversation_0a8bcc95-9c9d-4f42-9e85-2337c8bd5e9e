<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/maintainTeachPlan.css'}">
</head>
<body>
<div class="dialog" id="releaseDialog" style="display: block;">
    <div class="dialog-con">
        <p>确认删除吗？</p>
    </div>
    <div class="dialog-footer">
        <button class="btn-cancel">取消</button>
        <button class="btn-sure">确定</button>
    </div>
</div>
</body>
<script th:src="${_CPR_+_VR_+'/js/jquery-3.3.1.min.js'}"></script>
<script th:inline="javascript">
    let flag = /*[[${flag}]]*/ '';
    let formUserId = /*[[${formUserId}]]*/ '';
    let _VR_ = /*[[${_VR_}]]*/ '';
    if (flag) {
        $("#releaseDialog p").text("该专业已维护教学计划，确定删除吗？");
    }
    $(".btn-sure").click(function () {
        $.post(_VR_ + '/cultivation/deleteMajorTeachPlan', {formUserId: formUserId}).done(function (result) {
            window.parent.postMessage(JSON.stringify({action: 0}), '*')
        }).fail(function () {
            console.error("获取字段数据失败");
        });
    })
    $(".btn-cancel").click(function () {
        window.parent.postMessage(JSON.stringify({action: 0}), '*')
    })
</script>