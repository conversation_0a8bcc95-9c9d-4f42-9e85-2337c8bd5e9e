<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>手动排考</title>
    <link rel="stylesheet" th:href="@{~/css/examination/global.css}">
    <link rel="stylesheet" th:href="@{~/plugin/layui/css/layui.css}">
    <link rel="stylesheet" th:href="@{~/css/examination/tableFilter.css}">
    <link rel="stylesheet" th:href="@{~/css/examination/reset.css}">
    <link rel="stylesheet" th:href="@{~/css/examination/slideCommon2.css}">
    <link rel="stylesheet" th:href="@{~/css/examination/handSchedule1.css}">
    <link rel="stylesheet" th:href="@{~/css/examination/global2.css}">
    <link rel="stylesheet" th:href="@{~/css/examination/dialog.css}">
    <style>
        #invigilateMax {
            width: 458px;
            position: relative;
            left: auto;
            top: auto;
            transform: translate(0, 0);
        }
        .j-search-con .j-select-year ul{
            max-height: 120px;
        }
        .dialog .dialog-con{
            height: 165px;
        }

        .dialog .dialog-con .item .radio ul{
            overflow: hidden;
        }
        .dialog .dialog-con .item .radio ul li{
            float:left;
            padding-left:20px;
            background: url("../../images/examination/radio-icon.png") no-repeat left center;
            cursor: pointer;
            color: #1D2129;
            font-size: 14px;
            margin-right: 14px;
        }
        .dialog .dialog-con .item .radio ul li.cur{
            background: url("../../images/examination/radio-cur-icon.png") no-repeat left center;
        }

        .j-search-con .j-select-year{
            position: fixed;
            left:auto;
            top:auto;
        }
        .j-search-con{
            align-items: flex-start;
        }
        .j-select-year{
            margin-top:40px;
        }
        .j-search-con .j-select-year{
            width: 240px;
            max-height: none;
        }
        .dialog .dialog-con .item .label{
            width: 85px;
        }

        .marker {
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.6);
            position: fixed;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
            z-index: 9999;
        }
        #invigilateMax{
            width: 458px;
            position: fixed;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            z-index: 10000;
        }

    </style>
</head>

<body>
    <div class="z-main">
        <div class="z-title">
            <div class="z-title-con">
                <h3>手动排考</h3>
                <span>当前考试批次：<span id="bcCode"></span></span>
            </div>
            <div class="z-btn"><button id="clearData">撤销本次操作</button> <button id="save">保存</button></div>

        </div>
        <div class="z-con">
            <div id="slideMenu"></div>
            <div class="z-menu">
                <div class="z-nav">
                    <ul>
                        <li class="active">考场</li>
                        <li>监考教师</li>
                    </ul>
                </div>

                <div class="z-box z-classroom">
                    <div class="z-search">
                        <input type="text" class="search-box" autocomplete="off" placeholder="请输入考场">
                        <div class="j-search-con single-box" style="width: 100px;">
                            <div class="search-icon schoolSel"></div>
                            <div class="j-select-year searchType" style=" width: 160px;left: 0;">
                                <ul name="role">
                                    <li value="0" class="active" onclick="getClassroom2(2)">教学楼</li>
                                    <li value="1" onclick="getClassroom2(1)">校区</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <dl class="title">
                        <dt>按教学楼</dt>
                        <dd>教室/容量</dd>
                    </dl>
                    <div class="con-list con-list-default">
                        <ul id="examinationHall"></ul>
                    </div>
                    <div class="con-list con-list-search " style="display: none;">
                        <ul>

                        </ul>
                    </div>
                    <div class="z-batch">
                        <div class="z-batch-wrap"></div>
                        <div class="slideLeft"></div>
                    </div>
                </div>
                <div class="z-box z-invigilate" style="display: none;">
                    <div class="z-search">
                        <input type="text" class="search-box" autocomplete="off" placeholder="请输入监考教师">
                        <div class="j-search-con single-box" style="width: 100px;">
                            <div class="search-icon schoolSel"></div>
                            <div class="j-select-year searchType" style=" width: 160px;left: 0;">
                                <ul name="role">
                                    <li value="0" class="active" onclick="getInvigilator2(1)" >院系</li>
                                    <li value="1" onclick="getInvigilator2(2)">角色</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <dl class="title">
                        <dt>按院系</dt>
                        <dd>姓名/工作量</dd>
                    </dl>
                    <div class="con-list con-list-default">
                        <ul id="workload"></ul>
                    </div>
                    <div class="con-list con-list-search " style="display: none;">
                        <ul>

                        </ul>
                    </div>
                    <div class="z-batch">
                        <div class="z-batch-wrap">
                            批量选择 <div class="switch" id="batchTeacherSwitch"><span></span></div>
                        </div>
                        <div class="slideLeft"></div>
                    </div>
                </div>
            </div>

            <div class="z-table">
                <form action="" class="layui-form form-search slideDown" lay-filter='formSearch'>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: auto;">年级</label>
                        <div class="layui-input-inline">
                            <div class="j-search-con single-box">
                                <input type="text" name="grade" placeholder="请选择" readonly="" class="schoolSel">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <ul id="nj">
                                        <li value="">请选择</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: auto;">班级名称</label>
                        <div class="layui-input-inline">
                            <div class="j-search-con single-box">
                                <input type="text" name="class" placeholder="请选择" readonly="" class="schoolSel">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <ul id="bj">
                                        <li value="">请选择</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: auto;">排考状态</label>
                        <div class="layui-input-inline">
                            <div class="j-search-con single-box">
                                <input type="text" name="examStatus" placeholder="请选择" readonly="" class="schoolSel">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <ul>
                                        <li value="">请选择</li>
                                        <li value="1">已完成</li>
                                        <li value="2">未排教师</li>
                                        <li value="3">未排教室</li>
                                        <!-- <li value="3">均未排</li> -->
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: auto;">课程名称</label>
                        <div class="layui-input-inline">
                            <div class="j-search-con single-box">
                                <input type="text" name="courseName" placeholder="请选择" readonly="" class="schoolSel">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <ul id="kc">
                                        <li value="">请选择</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: auto;">考场</label>
                        <div class="layui-input-inline">
                            <div class="j-search-con single-box">
                                <input type="text" name="classroom" placeholder="请选择" readonly="" class="schoolSel">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <ul id="kc2">
                                        <li value="">请选择</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: auto;">监考教师</label>
                        <div class="layui-input-inline">
                            <div class="j-search-con single-box">
                                <input type="text" name="teacher" placeholder="请选择" readonly="" class="schoolSel">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <ul id="jkjs">
                                        <li value="">请选择</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="width: auto;">场次</label>
                        <div class="layui-input-inline">
                            <div class="j-search-con single-box">
                                <input type="text" name="kscc" placeholder="请选择" readonly="" class="schoolSel">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <div class="search">
                                        <input type="text" placeholder="搜索">
                                        <span></span>
                                    </div>
                                    <ul id="kscc">
                                        <li value="">请选择</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-inline layui-inline-btn" style="display: flex;width: 160px;">
                        <div class="search-btn clear" id="searchReset">重置</div>
                        <div class="search-btn" id="searchBtn">查询</div>
                    </div>
                    <div class="z-slide-up" id="formSlide"><span class="txt">展开</span><span class="arrow"></span></div>
                    <div class="z-opt">
                        <a onclick="exportExa()" class="z-export">导出考表</a>
                        <div class="z-batch-course">
                            批量选择 <div class="switch" id="batchCourseSwitch"><span></span></div>
                        </div>
                    </div>
                </form>

                <table class="layui-hide materialTable" id="materialTable" lay-filter="materialTable">
                </table>
            </div>
        </div>
    </div>
    <div class="marker" style="display: none"></div>
    <div class="dialog" style="display: none" id="invigilateMax">
        <div class="dialog-title">导出考表</div>
        <div class="dialog-con">
<!--            <div class="item">-->
<!--                <div class="label">选择学年学期</div>-->
<!--                <div class="j-search-con single-box">-->
<!--                    <input id="xnxq" type="text" name="xnxq" placeholder="请选择" readonly="" class="schoolSel" >-->
<!--                    <span class="j-arrow"></span>-->
<!--                    <div class="j-select-year ">-->
<!--                        <div class="search">-->
<!--                            <input type="text" placeholder="搜索">-->
<!--                            <span></span>-->
<!--                        </div>-->
<!--                        <ul class="xnxqUl">-->

<!--                        </ul>-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
<!--            <div class="item">-->
<!--                <div class="label">选择考试批次</div>-->
<!--                <div class="j-search-con single-box">-->
<!--                    <input type="text" name="teacherName" placeholder="请选择" readonly="" class="schoolSel"-->
<!--                           id="examinationBatch">-->
<!--                    <span class="j-arrow"></span>-->
<!--                    <div class="j-select-year ">-->
<!--                        <div class="search">-->
<!--                            <input type="text" placeholder="搜索">-->
<!--                            <span></span>-->
<!--                        </div>-->
<!--                        <ul class="batchUl">-->
<!--                        </ul>-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->

            <div id="dcfw" class="item">
                <div class="label">导出范围</div>
                <div class="radio">
                    <ul>
                        <li class="cur">全部</li>
                        <li>指定科目</li>
                    </ul>
                </div>
            </div>

            <div class="item" id="designated" style="display: none;">
                <div class="label">选择指定科目</div>
                <div class="j-search-con mul-box">
                    <input type="text" name="roleRange" placeholder="请选择" readonly="" class="schoolSel">
                    <span class="j-arrow"></span>
                    <div class="j-select-year">
                        <div class="search">
                            <input id="examinationSubjectValue" type="text" placeholder="搜索">
                            <span></span>
                        </div>
                        <ul id="examinationSubject" name="examinationSubject">
                        </ul>
                    </div>
                </div>
            </div>


        </div>
        <div class="dialog-btn" >
            <button class="pu-cancel" onclick="cancel()">取消</button>
            <button class="pu-sure" onclick="exportExam()">开始导出</button>
        </div>

    </div>

</body>

<script th:inline="javascript">
    let _VR_ = [[${_VR_}]];
</script>
<script th:src="@{~/js/jquery-3.3.1.min.js}"></script>
<script th:src="@{~/plugin/layui/layui.js}"></script>
<script th:src="@{~/js/my.util.js}"></script>
<script th:src="@{~/js/examination/handSchedule1.js}"></script>
<script th:src="@{~/js/examination/slideCommon2.js}"></script>
<script>
    $(document).ready(function () {
        // 下拉多选
        $(".j-search-con").on("click", ".schoolSel", function (e) {
            $(".j-select").hide();
            let parent = $(this).parent();
            parent.find('.j-arrow').toggleClass('j-arrow-slide');
            parent.find('.j-select-year').toggleClass('slideShow')
            stopBubble(e)
        })

        // 选择-单选
        $(".j-search-con.single-box").on("click",
            ".j-select-year li ",
            function (e) {
                $(this).addClass('active').siblings().removeClass();
                var parents = $(this).parents('.j-search-con');
                var schoolSelEle = parents.find('.schoolSel');
                var txt = $(this).text();
                schoolSelEle.val(txt);
                parents.find('.j-arrow').removeClass('j-arrow-slide');
                parents.find('.j-select-year').removeClass('slideShow')
                stopBubble(e);
            })

        $(".j-search-con.mul-box").on("click", ".j-select-year li ",
            function (e) {
                $(this).toggleClass("active");
                var parentEle = $(this).parent();
                var totallis = parentEle.find("li").length;
                var curlis = parentEle.find(".active").length;
                var prev = parentEle.prev(".all-selects")
                if (totallis == curlis) {
                    prev.addClass("active");
                    prev.text('取消全选')
                } else {
                    prev.removeClass("active");
                    prev.text('全选')
                }

                let selCon = [];
                let curEles = parentEle.find(".active");

                curEles.each((index, ele) => {
                    let txt = $(ele).text();
                    selCon.push(txt);
                });
                var schoolSelEle = $(this).parents('.j-search-con').find('.schoolSel')
                if (curEles.length > 0) {
                    schoolSelEle.val(selCon.join(','))
                } else {
                    schoolSelEle.val('');
                }
                stopBubble(e);

            })

        // 搜索
        $(".j-search-con").on('click', '.search input', function (e) {
            stopBubble(e);
        })
        $(".j-search-con").on('input', '.search input', function (e) {
            let val = $(this).val();
            var nextEle = $(this).parents('.j-search-con').find('ul')
            if (val != "") {

                nextEle.find('li').each(function (i, ele) {
                    let txt = $(ele).text();
                    if (txt.indexOf(val) >= 0) {
                        $(ele).show();
                    } else {
                        $(ele).hide();
                    }
                })
            } else {
                nextEle.find('li').show();
            }
            stopBubble(e)
        })

        function stopBubble(e) {
            if (e && e.stopPropagation)
                e.stopPropagation();
            else {
                window.event.cancelBubble = true;
            }

        }

        $(document).on("click", function (event) {
            var _con = $('.select-input');
            if (!_con.is(event.target) && _con.has(event.target).length === 0) {
                $(".j-search-con").find('.j-arrow').removeClass('j-arrow-slide');
                $(".j-search-con").find('.j-select-year').removeClass('slideShow')
            }
        })

        $(" .dialog .dialog-con .item .radio ul li").click(function(){
            $(this).addClass("cur").siblings().removeClass("cur");
            let index=$(this).index();
            if(index==0){
                $("#designated").hide();
                console.log('全部')
            }else{
                $("#designated").show();
                console.log('范围')
                getExaminationSubject()
            }

        })

    })
</script>
<script th:inline="javascript">
    var fid = [[${fid}]]
    var xnxq = [[${xnxq}]]
    var kspcbh = ''
    var bc = [[${bc}]]
    var campus1=[[${campus1}]]
    var campus2=[[${campus2}]]
    var status=[[${status}]]
    var multipleInvigilatorRule=[[${multipleInvigilatorRule}]]
    var teacherNum=[[${teacherNum}]]
    var maximum=[[${maximum}]]
    var banTeacher = []
    var banClassroom = [];
    var limitTeacher = {};
    var kcrsJson = {};
    var changeKc = {};
    var changeJs = {};
    var collageList = '[[${collageList}]]'.replaceAll("[","").replaceAll("]","").split(",");
    /**
     * 考场分页
     * @type {number}
     */
    var page1 = 1;
    /**
     * 考场关键字
     * @type {string}
     */
    var kw1 = "";
    /**
     * 监考教师分页
     * @type {number}
     */
    var page2 = 1;
    /**
     * 监考教师关键字
     * @type {string}
     */
    var kw2 = "";


    var xq1="";
    var xq2="";
    $(function () {

    })


    function getBatch() {
        $.get("/examination/basic/batch", {fid: fid, bc: bc}, function (res) {
            if (res.code == 200) {
                $("#bcCode").html(res.data[0].kspcgl_kspcmc);
                $("#bcCode").attr("data-value", bc);
                $("#examinationBatch").attr("data-value", res.data[0].kspcgl_kspcmc)
                $("#examinationBatch").val(res.data[0].kspcgl_kspcmc)
                $("#xnxq").attr("data-value", res.data[0].kspcgl_xnxq)
                $("#xnxq").val(res.data[0].kspcgl_xnxq)
                kspcbh = res.data[0].kspcgl_kspcbh
                $.get("/examination/rule/classroom/get",{fid:fid,bc:bc,xnxq:xnxq},function (res2){
                    if (res2.code==500||res2.data.campus==0){
                        xq1=res.data[0].kapcgl_xq;
                    }
                    getClassroom();

                })
                $.get("/examination/rule/invigilator/get",{fid:fid,bc:bc,xnxq:xnxq},function (res2){
                    if (res2.code==500||res2.data.campus2==0){
                        xq2=res.data[0].kapcgl_xq;
                    }
                    getInvigilator();
                })
            }
        })
    }

    // //滚动加载
    // var classroom = false;
    // $('.z-classroom .con-list').scroll(function () {
    //     if ($(this).scrollTop() + $(this).innerHeight() >= $(this)[0].scrollHeight - 20) {
    //         if (!classroom) {
    //             classroom = true;
    //             $('#load-more-button-classroom').show();
    //             // 这里调用你的加载更多内容的函数
    //             page1 += 1
    //             getClassroom(kw1)
    //         }
    //     }
    // });

    var invigilate = false;
    // $('.z-invigilate .con-list').scroll(function () {
    //     if ($(this).scrollTop() + $(this).innerHeight() >= $(this)[0].scrollHeight - 20) {
    //         if (!invigilate) {
    //             invigilate = true;
    //             $('#load-more-button-teacher').show();
    //             page2 += 1
    //             // 这里调用你的加载更多内容的函数
    //             getInvigilator(kw2);
    //         }
    //     }
    // });

    function getClassroom2(type) {
        if (type == 1){
            hallByCampus = 1
        }else {
            hallByCampus = 2
        }
        getClassroom()
    }

    //1 按校区 2 按教学楼
    var hallByCampus = 2
    function getClassroom(kw,clearData,zrs,banClassRooms) {
        if (campus1){
            xq1="";
        }
        $.get("/examination/basic/getHallCount", {fid: fid, page: page1,campus:xq1}, function (res) {
            $.get("/examination/basic/hall21", {fid: fid, page: page1, kw: kw,campus:xq1, hallCount: res.data}, function (res) {
                if (res.code == 200) {
                    //先清空，避免因为响应时间而导致出现多条重复数据

                    $("#examinationHall").html("")


                    $('#load-more-button-classroom').hide()
                    var html = '';
                    var data = res.data;
                    const groupBy = (array, key) => {
                        return array.reduce((result, currentItem) => {
                            // 使用 key 作为分组的键
                            const group = currentItem[key];

                            // 如果 result 中不存在这个分组，则创建它
                            if (!result[group]) {
                                result[group] = [];
                            }

                            // 将当前项添加到对应分组
                            result[group].push(currentItem);

                            return result;
                        }, {});
                    };
                    var flag;
                    if (hallByCampus == 1){
                        flag = 'kcgl_kcssxq'
                    }else {
                        flag = 'kcgl_kcssjxl'
                    }
                    const grouped = groupBy(data,flag );
                    for (let groupedKey in grouped) {
                        grouped[groupedKey].sort(function(a, b) {
                            return b.kcgl_kcrl - a.kcgl_kcrl;
                        });
                        html += '<li>\n' +
                            '                                <div class="item level1">\n' +
                            '                                    <span class="arrow layui-icon layui-icon-triangle-d"></span>\n' +
                            '                                    <h3>'+groupedKey+'</h3>\n' +
                            '                                </div>\n'+
                            '                                <ul>\n';
                        for (let groupedElementKey in grouped[groupedKey]) {
                            var kcrl = grouped[groupedKey][groupedElementKey].kcgl_kcrl
                            var kcbh = grouped[groupedKey][groupedElementKey].kcgl_kcbh
                            var kcmc = grouped[groupedKey][groupedElementKey].kcgl_kcmc
                            // var kcrlText = (kcrl-(kcrsJson[kcbh] == null || kcrsJson[kcbh] == '' ? 0 : kcrsJson[kcbh]))+'/'+kcrl
                            // if (clearData!='clearData' && changeKc[kcbh] != null && changeKc[kcbh] != ''){
                            //     kcrlText = changeKc[kcbh]
                            // }
                            if (banClassRooms != null && banClassRooms != ''){
                                if (banClassRooms.includes(','+kcbh+'')){
                                    continue
                                }

                                // for (let banClassRoom of banClassRooms.split(",")) {
                                //     if (kcbh == banClassRoom){
                                //         console.log(banClassRoom)
                                //         console.log(kcbh)
                                //         continue
                                //     }
                                // }
                            }
                            if (zrs != null && zrs != ''){
                                if (parseInt(zrs)>parseInt(kcrl)){
                                    continue
                                }
                            }
                            html += '                                    <li>\n' +
                                '                                        <div class="item lastLevel" data-roomname="'+kcmc+'" data-room="'+kcbh+'">\n' +
                                '                                            <span class="arrow"></span>\n' +
                                '                                            <h3>'+kcmc+'</h3>\n' +
                                '                                            <span class="remaining">'+kcrl+'</span>\n' +
                                '                                            <span class="surplus">/<i>'+kcrl+'</i></span>\n' +
                                '                                        </div>\n' +
                                '                                    </li>\n';
                        }


                        html += '                                </ul>\n'+
                            '                            </li>';
                    }
                    $("#examinationHall").append(html);
                    classroom = false;
                } else {
                    // $('#load-more-button-classroom').show().text('没有更多内容了');
                    // setTimeout(function () {
                    //     $('#load-more-button-classroom').hide()
                    // }, 2000)
                }
            })
        })
    }

    //1 按院系 2 按角色
    var invigilatorByYx = 1
    function getInvigilator2(type) {
        if (type == 1){
            invigilatorByYx = 1
        }else {
            invigilatorByYx = 2
        }
        getInvigilator()
    }
    function getInvigilator(kw) {
        if (campus2){
            xq2="";
        }
        $.get("/examination/basic/getWorkloadCount", {fid: fid, page: page1,campus:xq2,bc:bc,xnxq:xnxq}, function (res) {
            $.get("/examination/basic/workload2", {fid: fid, xnxq: xnxq, bc: bc, page: page2, kw: kw,campus:xq2, workloadCount:res.data}, function (res) {
                if (res.code == 200) {
                    //先清空，避免因为响应时间而导致出现多条重复数据
                    $("#workload").html("")

                    $('#load-more-button-teacher').hide()
                    var html = '';
                    var data = res.data;
                    const groupBy = (array, key) => {
                        return array.reduce((result, currentItem) => {
                            // 使用 key 作为分组的键
                            const group = currentItem[key];

                            // 如果 result 中不存在这个分组，则创建它
                            if (!result[group]) {
                                result[group] = [];
                            }

                            // 将当前项添加到对应分组
                            result[group].push(currentItem);

                            return result;
                        }, {});
                    };
                    var flag;
                    if (invigilatorByYx == 1){
                        flag = 'jkjsgzl_ssyx'
                    }else {
                        flag = 'jkjsgzl_js'
                    }

                    const grouped = groupBy(data,flag );
                    for (let groupedKey in grouped) {
                        html += ' <li>\n' +
                            '                                <div class="item level1">\n' +
                            '                                    <span class="arrow layui-icon layui-icon-triangle-d"></span>\n' +
                            '                                    <h3>'+groupedKey+'</h3>\n' +
                            '                                    <span class="check"></span>\n' +
                            '                                </div>\n' +
                            '                                <ul>\n' ;
                        for (let groupedElementKey in grouped[groupedKey]) {
                            if (grouped[groupedKey][groupedElementKey].jkjsgzl_jsxm == null || grouped[groupedKey][groupedElementKey].jkjsgzl_jsxm == undefined || grouped[groupedKey][groupedElementKey].jkjsgzl_jsxm == '') {
                                continue;
                            }


                            var name = grouped[groupedKey][groupedElementKey].jkjsgzl_jsxm.uname
                            var xgh = grouped[groupedKey][groupedElementKey].jkjsgzl_jsgh
                            var puid = grouped[groupedKey][groupedElementKey].jkjsgzl_jsxm.puid
                            var zjkcc = grouped[groupedKey][groupedElementKey].jkjsgzl_zjkcc
                            if (clearData!='clearData' && changeJs[xgh] != null && changeJs[xgh] != ''){
                                zjkcc = changeJs[xgh]
                            }
                            var xq = grouped[groupedKey][groupedElementKey].jkjsgzl_xq
                            var jkjsgzl_jkxbfw = grouped[groupedKey][groupedElementKey].jkjsgzl_jkxbfw
                            var jkxbfw = ''
                            for (let key in jkjsgzl_jkxbfw) {
                                jkxbfw += jkjsgzl_jkxbfw[key] + ','
                            }

                            var limit = limitTeacher[xgh] == null  ? 100000 : limitTeacher[xgh]
                            html += '                                    <li data-value="'+xq+'">\n' +
                                '                                        <div class="item lastLevel" data-name="'+name+'" >\n' +
                                '                                            <span class="arrow"></span>\n' +
                                '                                            <h3 data-jkxbfw="'+ jkxbfw +'" data-limit="'+limit+'" data-name="'+name+'" data-value="'+puid+'" data-xgh="'+xgh+'">'+name+'</h3>\n' +
                                '                                            <span class="remaining">'+zjkcc+'</span>\n' +
                                '                                            <span class="check"></span>\n' +
                                '                                        </div>\n' +
                                '                                    </li>\n';
                        }


                        html += '                                </ul>\n' +
                            '                            </li>';
                    }



                    $("#workload").append(html);
                    //刷新监考教师工作量
                    // for (let i = 0; i < data.length; i++) {
                    //     if (data[i].jkjsgl_jsxm == null || data[i].jkjsgl_jsxm == undefined || data[i].jkjsgl_jsxm == '') {
                    //         continue;
                    //     }
                    //     setTimeout(function (){
                    //         getCurInvigilationNum(data[i].jkjsgl_jsxm.puid)
                    //         getLimit(data[i].jkjsgl_jsxm.puid)
                    //     },50)
                    // }

                    invigilate = false;
                } else {
                    // $('#load-more-button-teacher').show().text('没有更多内容了');
                    // setTimeout(function () {
                    //     $('#load-more-button-teacher').hide()
                    // }, 2000)
                }
            })
        })
    }


    function getCurInvigilationNum(uid) {
        $.get("/examination/basic/getInvigilationNum", {fid: fid, xnxq: xnxq, bc: bc, uid: uid}, function (res) {
            if (res.code == 200) {
                $("h3[data-value=" + uid + "]").next().html(res.data)
            }
        })
    }


    function getLimit() {
        $.get("/examination/basic/getLimit2", {fid: fid, xnxq: xnxq, bc: bc}, function (res) {
            if (res.code == 200) {
                limitTeacher = res.data
                // $("h3[data-value=" + uid + "]").parent().attr("data-limit", res.data)
            }
        })
    }

    // $('.teacher-name').bind('keypress', function (event) {
    //     if (event.keyCode == "13") {
    //         var tempValue = $(this).val();
    //         var parents = $(this).parents("div.z-box");
    //         if (parents.hasClass("z-classroom")) {
    //             $("#examinationHall").html("")
    //             page1 = 1;
    //             kw1 = tempValue;
    //             getClassroom(tempValue)
    //         }
    //
    //         if (parents.hasClass("z-invigilate")) {
    //             $("#workload").html("")
    //             page2 = 1;
    //             kw2 = tempValue;
    //             getInvigilator(tempValue);
    //         }
    //
    //
    //         // var val = $(this).parent(".z-search").children(".j-search-con").children("input").val();
    //         // var list = $(this).parents(".z-box").children(".con-list").find("li");
    //         //
    //         // for (let i = 0; i < list.length; i++) {
    //         //     if (val != undefined && val != '') {
    //         //         var selCon = val.split(",");
    //         //         var value = $(list[i]).attr("data-value");
    //         //         for (let j = 0; j < selCon.length; j++) {
    //         //             if (selCon[j] == value) {
    //         //                 check(list, i, tempValue);
    //         //                 break
    //         //             } else {
    //         //                 $(list[i]).hide();
    //         //             }
    //         //         }
    //         //     } else {
    //         //         check(list, i, tempValue);
    //         //     }
    //         // }
    //     }
    // })

    function getBanTeacher() {
        $.get("/examination/rule/teacher/get", {xnxq: xnxq, bc: bc}, function (res) {
            if (res.code == 200) {
                banTeacher = res.data.data;
            }
        })
    }

    function getBanClassroom() {
        $.get("/examination/rule/classroom2/get", {xnxq: xnxq, bc: bc}, function (res) {
            if (res.code == 200) {
                banClassroom = res.data.data;
            }
        })
    }

    $(".z-search span").click(function () {
        var prev = $(this).prev();
        if (!prev.val()) {
            return false
        }
        alert(111)
        prev.val("")
        $('#load-more-button-classroom').show().text('加载中...');
        $('#load-more-button-teacher').show().text('加载中...');
        var parents = $(this).parents("div.z-box");
        if (parents.hasClass("z-classroom")) {
            $("#examinationHall").html("")
            page1 = 1;
            kw1 = "";
            getClassroom()
        }

        if (parents.hasClass("z-invigilate")) {
            $("#workload").html("")
            page2 = 1;
            kw2 = "";
            getInvigilator();
        }

    })

    function exportExam() {
        // 获取所有目标li元素
        const activeItems = document.querySelectorAll('#examinationSubject li.active');

        // 提取data-value值并过滤无效项
        const values = Array.from(activeItems)
            .map(li => li.dataset.value)  // 获取data-value属性
            .filter(value => value !== undefined);  // 过滤未定义的值

        // 用逗号连接所有值
        const examinationSubjects = values.join(',');
        fetch("/examinationManagement/exportGraphics1?xnxq="+xnxq + "&kspcbh=" + bc+ "&fid=" + fid + "&examinationSubjects="+ examinationSubjects)
            .then(response => response.blob())
            .then(blob => {
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                console.log(url)
                a.download = '手动排考导出.xlsx';
                a.click();
            });
    }


    function getExaminationSubject() {
        $.get("/examination/basic/examinationSubject", {fid: fid, kspcbh: bc}, function (res) {
            console.log(res)
            if (res.code == 200) {
                var data = res.data;
                var html = '';
                if (data.length == 1) {
                    html += "<li data-value='" + data[0].name + "' class='active' title='" + data[0].name + "'>" + data[0].name + "</li>"
                    $(".batchUl").html(html);
                    $("#examinationSubjectValue").attr("data-value", data[0].name)
                    $("#examinationSubjectValue").val(data[0].name)
                    return;
                }
                for (let i = 0; i < data.length; i++) {
                    html += "<li data-value='" + data[i].name + "' title='" + data[i].name + "'>" + data[i].name + "</li>"
                }
                $("#examinationSubject").html(html);
            } else {
                U.fail(res.msg)
                $("#examinationSubject").html("");
            }
        })
    }
    function exportExa() {
        $('.marker').show()
        $('#invigilateMax').show()
    }


    function cancel() {
        $('.marker').hide()
        $('#invigilateMax').hide()
    }
</script>

</html>