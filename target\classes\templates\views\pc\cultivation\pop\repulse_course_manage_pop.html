<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生成教学计划</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/global.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/slideCommon.css'}"/>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/cultivation/teachPlan.css'}"/>
    <style>
        .layui-textarea {
            position: relative;
            min-height: 100px;
            height: auto;
            line-height: 20px;
            padding: 6px 10px;
            resize: vertical;
            width: 100%;
            border-radius: 4px;
            border-color: #E5E6EB;
        }
    </style>
</head>

<body>
<div class="marker"></div>
<div class="dialog" id="teachPlanDialog">
    <div class="textarea" style="padding: 40px 76px;">
        <textarea rows="7" class="layui-textarea" style="resize: none;" placeholder="请输入打回原因" name=""></textarea>
    </div>
    <div class="dialog-footer">
        <button class="btn-cancel">取消</button>
        <button class="btn-sure">确定</button>
    </div>
</div>
</body>
<script th:src="${_CPR_+_VR_+'/js/jquery-3.3.1.min.js'}"></script>
<script th:src="${_CPR_+_VR_+'/plugin/layui/layui.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/cultivation/slideCommon.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/my.util.js'}"></script>
<script th:inline="javascript">
    const formId = [[${formTopBtnVO.formId}]];
    const fid = [[${formTopBtnVO.fid}]];
    const uid = [[${formTopBtnVO.uid}]];
    const selectTotal = [[${formTopBtnVO.selectTotal}]];
    const queryId = [[${formTopBtnVO.queryId}]];
    const _VR_ = [[${_VR_}]];
    $(document).ready(function () {
        $('#teachPlanDialog .btn-sure').click(function () {
            let reason = $(".layui-textarea").val();
            if (!reason) {
                U.fail("请输入打回原因！");
                return false;
            }
            location.href = _VR_ + "/api/form-btn/gm/outpost.popup?code=2T10012&formId=" + formId + "&fid=" + fid + "&queryId=" + queryId + "&uid=" + uid + "&selectTotal=" + selectTotal + "&reason=" + reason;
        });
        $('#teachPlanDialog .btn-cancel').click(function () {
            window.parent.postMessage(JSON.stringify({action: 1}), "*");
        });
    })
</script>
</html>