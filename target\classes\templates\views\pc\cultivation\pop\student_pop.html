<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <title>添加学生</title>
    <link rel="stylesheet" th:href="@{/css/cultivation/jquery.searchableSelect.css}"/>
    <link rel="stylesheet" th:href="@{/css/cultivation/newStyle.css}"/>
    <link rel="stylesheet" th:href="@{/plugin/layui/css/layui.css}"/>
    <style>
        html, body {
            height: 100%;
        }

        .layui-laypage .layui-laypage-curr .layui-laypage-em {
            background-color: #4C88FF !important;
        }

        .opt_data_top {
            margin: 13px 31px -18px;
        }

        .opt_data_top .opt_data_num em {
            margin: 0 4px;
            color: #4C88FF;
            font-style: normal;
        }
    </style>
</head>
<body>
<div class="popDiv popMove wid840" style="width: 1071px;">
    <div class="popBody" id="popScroll">
        <div class="popSearch clearAfter">
            <div class="popSearch_row fl">
                <div class="popSearch_name">
                    <div class="popSearch_table">
                        <div class="popSearch_cell"><span>学生学号</span></div>
                    </div>
                </div>
                <div class="popSearch_per"><input type="text" placeholder="请输入" name="xsxh"
                                                  class="popSearch_input"></div>
            </div>
            <div class="popSearch_row fl">
                <div class="popSearch_name">
                    <div class="popSearch_table">
                        <div class="popSearch_cell"><span>学生姓名</span></div>
                    </div>
                </div>
                <div class="popSearch_per"><input type="text" placeholder="请输入" name="xsxm"
                                                  class="popSearch_input"></div>
            </div>
            <div class="popSearch_row fl">
                <div class="popSearch_name">
                    <div class="popSearch_table">
                        <div class="popSearch_cell"><span>学生性别</span></div>
                    </div>
                </div>
                <div class="popSearch_per">
                    <select class="qselect" name="xsxb">
                        <option value="">请选择</option>
                        <option value="男">男</option>
                        <option value="女">女</option>
                    </select>
                </div>
            </div>
            <div class="popSearch_btns">
                <div class="popSearch_clear fr">清空筛选</div>
                <div class="popSearch_search_btn fr" lay-filter="search" lay-submit>筛选</div>
            </div>
        </div>
        <div class="opt_data_top">
            <label class="opt_data_num">
                <span>已选<em>0</em>条</span>
            </label>
        </div>
        <div class="popSearch_cont">
            <table lay-filter="studentTable" class="layui-table" id="studentTable">
            </table>
        </div>
    </div>
</div>
<script th:src="@{/js/jquery-3.3.1.min.js}"></script>
<script th:src="@{/js/jquery.nicescroll.min.js}"></script>
<script th:src="@{/js/cultivation/jquery.searchableSelect.js}"></script>
<script th:src="@{/plugin/layui/layui.js}"></script>
<script>
    let clazz = "[[${clazz}]]";
    let major = "[[${major}]]";
    let fid = "[[${fid}]]";
    let formUserId = "[[${formUserId}]]";

    //弹窗居中
    function MoveFixed(userClass) {
        $(userClass).css({
            top: function () {
                return 0;
            }, left: function () {
                return ($(window).width() - $(this).width()) / 2;
            }
        });

    }

    window.onresize = function () {
        MoveFixed('.popMove')
    }
    MoveFixed('.popMove')

    function scrollBox(userID) {
        $(userID).niceScroll({
            cursorborder: "",
            cursorwidth: 8,
            cursorcolor: "#CFD8E6",
            boxzoom: false,
            autohidemode: true
        });
        $(userID).getNiceScroll().resize(); //检测滚动条是否重置大小（当窗口改变大小时）
    }

    scrollBox("#popScroll");
    $('.qselect').searchableSelect();
    document.domain = document.domain.split('.').slice(-2).join('.');

    $(".popSearch_search_btn").click(function () {
        var field = {
            xsjbxx_xh: $("input[name='xsxh']").val(),
            xsjbxx_xm: $("input[name='xsxm']").val(),
            xsjbxx_xb: $("select[name='xsxb']").val()
        };
        insTb.reload({where: field, page: {curr: 1}});
    })

    $(".popSearch_clear").click(function () {
        location.reload();
    })

    function getStudents() {
        const tableData = [];
        const checkRows = table.checkStatus('studentTable');
        uniqueArray = uniqueArray.length === 0 ? checkRows.data : uniqueArray;
        for (let i = 0; i < uniqueArray.length; i++) {
            const rowData = uniqueArray[i];
            const row = {
                xsjbxx_xh: rowData.xsjbxx_xh,
                xsjbxx_xm: rowData.xsjbxx_xm,
                xsjbxx_sznj: rowData.xsjbxx_sznj,
                xsjbxx_yxxx: rowData.xsjbxx_yxxx,
                xsjbxx_xb: rowData.xsjbxx_xb,
                xsjbxx_zyxx: rowData.xsjbxx_zyxx,
                xsjbxx_bjxx: rowData.xsjbxx_bjxx
            };
            tableData.push(row);
        }
        return tableData;
    }

    let table = "";
    let insTb = "";
    let uniqueArray = [];
    layui.use('table', function () {
        table = layui.table;
        insTb = table.render({
            elem: '#studentTable',
            url: '/processData/getStudentInfoData',
            where: {
                xsjbxx_zyxx: major,
                fid: fid,
                xsjbxx_bjxx: clazz,
                formUserId: formUserId,
                xsjbxx_xh: $("input[name='xsxh']").val(),
                xsjbxx_xm: $("input[name='xsxm']").val(),
                xsjbxx_xb: $("select[name='xsxb']").val()
            },
            page: true,
            cols: [
                [{
                    type: 'checkbox',
                    fixed: 'left',
                    width: 80,
                }, {
                    field: 'xsjbxx_xm',
                    title: '学生',
                    width: 130,
                    align: "center",
                }, {
                    field: 'xsjbxx_xh',
                    title: '学号',
                    width: 200,
                    align: "center",
                }, {
                    field: 'xsjbxx_sznj',
                    title: '年级',
                    width: 100,
                    align: "center",

                }, {
                    field: 'xsjbxx_yxxx',
                    title: '院系',
                    width: 124,
                    align: "center",

                }, {
                    field: 'xsjbxx_xb',
                    title: '性别',
                    width: 124,
                    align: "center",

                }, {
                    field: 'xsjbxx_zyxx',
                    title: '专业名称',
                    width: 124,
                    align: "center",

                }, {
                    field: 'xsjbxx_bjxx',
                    title: '行政班名称',
                    width: 124,
                    align: "center",

                }]
            ],
            done: function (res) {
                for (let i = 0; i < res.data.length; i++) {
                    for (let j = 0; j < uniqueArray.length; j++) {
                        //数据id和要勾选的id相同时checkbox选中
                        if (res.data[i].xsjbxx_xh === uniqueArray[j].xsjbxx_xh) {
                            //这里才是真正的有效勾选
                            res.data[i]["LAY_CHECKED"] = 'true';
                            //找到对应数据改变勾选样式，呈现出选中效果
                            const index = res.data[i]['LAY_TABLE_INDEX'];
                            $('.layui-table tr[data-index=' + index + '] input[type="checkbox"]').prop('checked', true);
                            $('.layui-table tr[data-index=' + index + '] input[type="checkbox"]').next().addClass('layui-form-checked');
                        }
                    }
                }
                scrollBox("#popScroll");
            }
        });
        //监听表格复选框选择
        table.on('checkbox(studentTable)', function (obj) {
            const checkStatus = table.checkStatus('studentTable');
            if (obj.checked) {
                uniqueArray.push(...checkStatus.data);
            } else {
                // 使用 filter 方法创建一个新的数组，其中不包含要移除的对象
                uniqueArray = uniqueArray.filter(function (data) {
                    return data.xsjbxx_xh !== obj.data.xsjbxx_xh;
                });
            }
            uniqueArray = Array.from(new Set(uniqueArray.map(obj => obj.xsjbxx_xh))).map(name => {
                return uniqueArray.find(obj => obj.xsjbxx_xh === name);
            });
            $(".opt_data_num em").text(uniqueArray.length);
        });
    });
</script>
</body>
</html>
