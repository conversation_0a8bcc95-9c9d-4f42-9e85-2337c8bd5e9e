<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>培养方案</title>
    <link rel="stylesheet" th:href="@{../../plugin/layui/css/layui.css}">
    <link rel="stylesheet" th:href="@{../../plugin/layui/css/modules/formSelects-v4.css}">
    <script th:src="@{../../plugin/layui/layui.js(v=${new java.util.Date().getTime()})}"></script>
    <script th:src="@{../../plugin/layui/lay/modules/formSelects-v4.js}"></script>
</head>
<body>
    <form class="layui-form" style="width: 750px;height: 300px;margin: 40px 40px;">
        <div class="layui-form-item">
            <label class="layui-form-label" style="margin-left: 110px;color: red;font-weight: bold;">从</label>
            <label class="layui-form-label" style="margin-left: 200px;color: red;font-weight: bold;">复制到</label>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">年级</label>
            <div class="layui-input-inline">
                <select name="grade" class="layui-input" lay-verify="required">
                    <option value=""></option>
                </select>
            </div>
            <label class="layui-form-label">年级</label>
            <div class="layui-input-inline">
                <select name="toGrade" class="layui-input" xm-select="grade" xm-select-show-count="6" xm-select-search="" xm-select-search-type="dl">
                    <option value=""></option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">培养层次</label>
            <div class="layui-input-inline">
                <select name="level" class="layui-input" lay-verify="required">
                    <option value=""></option>
                </select>
            </div>
            <label class="layui-form-label">培养层次</label>
            <div class="layui-input-inline">
                <select name="toLevel" class="layui-input" xm-select="level" xm-select-show-count="6" xm-select-search="" xm-select-search-type="dl">
                    <option value=""></option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">专业</label>
            <div class="layui-input-inline">
                <select name="major" class="layui-input" lay-verify="required">
                    <option value=""></option>
                </select>
            </div>
            <label class="layui-form-label">专业</label>
            <div class="layui-input-inline">
                <select name="toMajor" class="layui-input" xm-select="major" xm-select-show-count="6" xm-select-search="" xm-select-search-type="dl">
                    <option value=""></option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="btn-complate">提交</button>
                <button type="reset" class="layui-btn layui-btn-primary">取消</button>
            </div>
        </div>
    </form>
</body>
<script th:src="@{../../js/jquery-1.11.3.min.js}"></script>
<script th:src="@{../../js/cultivation/cultivationProcess.js}"></script>
<script th:inline="javascript">
    document.domain = "chaoxing.com";
    var formUserId = [[${formUserId}]];
    var formId = [[${formId}]];
    var fid = [[${fid}]];
    var uid = [[${uid}]];
    var formSelects = "";
    layui.config({
        base: '/plugin/layui/lay/modules/' //此处路径请自行处理, 可以使用绝对路径
    }).extend({
        formSelects: 'formSelects-v4'
    });
    layui.use(['form','layer','formSelects'], function () {
        var form = layui.form;
        var layer = layui.layer;
        formSelects = layui.formSelects;
        //监听提交
        form.on('submit(btn-complate)', function(data){
            var grade = data.field.grade;
            var level = data.field.level;
            var major = data.field.major;
            if(major) {
                major = major.split(",")[0];
            }
            var toGrade = formSelects.value('grade', 'valStr');
            var toLevel = formSelects.value('level', 'valStr');
            var toMajor = formSelects.value('major', 'valStr');
            let loading = layer.load(1);
            $.post("/api/pygc/trainingProgramBatchCopy", {
                formUserId:formUserId,
                formId:formId,
                fid:fid,
                uid:uid,
                grade:grade,
                level:level,
                major:major,
                toGrade:toGrade,
                toLevel:toLevel,
                toMajor:toMajor
            }, function(result){
                if(result.status){
                    window.parent.postMessage(JSON.stringify({action:1}),"*");
                }else {
                    layer.msg(result.message, {icon: 2, time: 2000});
                }
                layer.close(loading);
            }, "json");
            return false;
        })
    })
    $(".layui-btn-primary").click(function (){
        window.parent.postMessage(JSON.stringify({action:1}),"*");
    })
</script>
</html>