<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>开班</title>
    <link rel="stylesheet" th:href="@{../css/cultivation/global.css}">
    <link rel="stylesheet" th:href="@{../plugin/layui/css/layui.css}">
    <link rel="stylesheet" th:href="@{../css/cultivation/reset.css}">
    <link rel="stylesheet" th:href="@{../css/cultivation/index2.0.css(v=${new java.util.Date().getTime()})}">
    <script th:src="@{../plugin/layui/layui.js}"></script>
</head>

<body>
<div class="main">
    <div class="wrap-item">
        <div class="item">
            <div class="i-top" style="padding-top: 16px;">
                <h3>开课信息</h3>
                <span class="arrow slide"></span>
            </div>
            <div class="i-con">
                <div class="course-inform">
                    <ul>
                        <li>
                            <div class="name">开课学期：</div>
                            <div class="tit" th:text="${teachPlanForm?.jxjhgl_xnxq}"></div>
                        </li>
                        <li>
                            <div class="name">开课院系：</div>
                            <div class="tit" th:text="${teachPlanForm?.jxjhgl_ssxb}"></div>
                        </li>
                        <li>
                            <div class="name">所属专业：</div>
                            <div class="tit" th:text="${teachPlanForm?.jxjhgl_zy}"></div>
                        </li>
                        <li>
                            <div class="name">培养层次：</div>
                            <div class="tit" th:text="${teachPlanForm?.jxjhgl_pycc}"></div>
                        </li>
                        <li>
                            <div class="name">课程名称：</div>
                            <div class="tit" th:text="${teachPlanForm?.jxjhgl_kcmc}"></div>
                        </li>
                        <li>
                            <div class="name">课程性质：</div>
                            <div class="tit" th:text="${teachPlanForm?.jxjhgl_kclb}"></div>
                        </li>
                        <li>
                            <div class="name">是否纯实践环节：</div>
                            <div class="tit" th:text="${teachPlanForm?.jxjhgl_sfcsjhj}"></div>
                        </li>
                        <li>
                            <div class="name">选必修：</div>
                            <div class="tit" th:text="${teachPlanForm?.jxjhgl_xbx}"></div>
                        </li>
                        <li>
                            <div class="name">总学分：</div>
                            <div class="tit" th:text="${teachPlanForm?.jxjhgl_xf}"></div>
                        </li>
                        <li>
                            <div class="name">总学时：</div>
                            <div class="tit" th:text="${teachPlanForm?.jxjhgl_zxs}"></div>
                        </li>
                        <li>
                            <div class="name">理论学时：</div>
                            <div class="tit" th:text="${teachPlanForm?.jxjhgl_llxs}?${teachPlanForm?.jxjhgl_llxs}:0"></div>
                        </li>
                        <li>
                            <div class="name">实践学时：</div>
                            <div class="tit" th:text="${teachPlanForm?.jxjhgl_sjxs}?${teachPlanForm?.jxjhgl_sjxs}:0"></div>
                        </li>
                        <li>
                            <div class="name">上机学时：</div>
                            <div class="tit" th:text="${teachPlanForm?.jxjhgl_sjxss}?${teachPlanForm?.jxjhgl_sjxss}:0"></div>
                        </li>
                        <li>
                            <div class="name">实验学时：</div>
                            <div class="tit" th:text="${teachPlanForm?.jxjhgl_syxs}?${teachPlanForm?.jxjhgl_syxs}:0"></div>
                        </li>
                        <li>
                            <div class="name">其他学时：</div>
                            <div class="tit" th:text="${teachPlanForm?.jxjhgl_qtxs}?${teachPlanForm?.jxjhgl_qtxs}:0"></div>
                        </li>
                        <li>
                            <div class="name">计划人数：</div>
                            <div class="tit" th:text="${teachPlanForm?.jxjhgl_zrs}?${teachPlanForm?.jxjhgl_zrs}:0+'人'"></div>
                        </li>
                        <li>
                            <div class="name">未安排人数：</div>
                            <div class="tit" id="distribution_tit"></div>
                        </li>
                    </ul>
                </div>
                <div class="class-box">
                    <div class="cb-top">
                        <div class="tit">已生成教学班</div>
                    </div>
                    <!--<div class="j-search clearfixs tct">
                        <div class="j-search-item-wrap">
                            <div class="j-search-item">
                                <h5>年级</h5>
                                <div class="j-search-con">
                                    <input type="text" placeholder="请选择年级" readonly="" class="schoolSel">
                                    <span class="j-arrow"></span>
                                    <div class="j-select-year">
                                        <ul class="nj">

                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="j-search-item">
                                <h5>学院</h5>
                                <div class="j-search-con">
                                    <input type="text" placeholder="选择学院" readonly="" class="schoolSel">
                                    <span class="j-arrow"></span>
                                    <div class="j-select-year">
                                        <ul class="skyx">

                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="j-search-item">
                                <h5>班级</h5>
                                <div class="j-search-con">
                                    <input type="text" placeholder="选择班级" readonly="" class="schoolSel">
                                    <span class="j-arrow"></span>
                                    <div class="j-select-year">
                                        <ul class="bjdm">

                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="j-search-item">
                                <h5>教师</h5>
                                <div class="j-search-con">
                                    <input type="text" placeholder="选择教师姓名" class="schoolSel" name="teacher">
                                </div>
                            </div>
                        </div>
                        <div class="button">查询</div>
                        <div class="clearSel"></div>
                    </div>-->
                    <div class="j-table" style="border:none;">
                        <table class="layui-table" id="teachClassTable" lay-filter="teachClassTable">
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="item" style="background: #ffffff;border-radius: 8px;padding-top: 16px;overflow: hidden;">
        <div class="i-top">
            <h3>开班</h3>
            <span class="arrow slide"></span>
        </div>
        <div class="i-con" style="display: flex;overflow: unset;">
            <div class="class-box ct" style="width: 47.5%;">
                <div class="cb-top">
                    <div class="tit" style="margin-right:24px;">可选行政班</div>
                    <!--<div class="switch" id="classSwitch">
                        <div class="switch-con"><i></i></div>
                        跨专业合班开课
                    </div>-->
                </div>
               <!-- <div class="j-search clearfixs">
                    <div class="j-search-item-wrap">
                        <div class="j-search-item j-search-class">
                            <h5>年级</h5>
                            <div class="j-search-con">
                                <input type="text" placeholder="请选择年级" readonly="" class="schoolSel">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <ul class="nj">

                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="j-search-item j-search-class">
                            <h5>学院</h5>
                            <div class="j-search-con">
                                <input type="text" placeholder="选择学院" readonly="" class="schoolSel">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <ul class="skyx">

                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="j-search-item j-search-class">
                            <h5>班级</h5>
                            <div class="j-search-con">
                                <input type="text" placeholder="选择班级" readonly="" class="schoolSel">
                                <span class="j-arrow"></span>
                                <div class="j-select-year">
                                    <ul class="bjdm">

                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="button">搜索</div>
                    <div class="clearSel"></div>
                </div>-->
                <div class="j-table" style="border:none;">
                    <table class="layui-table" id="classTable" lay-filter="classTable">
                    </table>
                </div>
            </div>

            <div class="mutate">
                <div class="up" id="up"></div>
                <div class="down" id="down"></div>
            </div>

            <div class="class-box" style="width: 47.5%;overflow: unset;">
                <div class="cb-top">
                    <div class="tit">已生成教学班</div>

                </div>
                <div class="i-mes">
                    <h5>教学班人数：<em>0</em>人</h5>
                    <button>确认生成教学班</button>
                </div>
                <div class="j-table" style="border:none;">
                    <table class="layui-table" id="teachingClassForm" lay-filter="teachingClassForm">
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
<script th:src="@{../js/jquery-3.3.1.min.js}"></script>
<script th:src="@{../js/cultivation/common.js}"></script>
<script th:src="@{../js/cultivation/class_starts_detail.js(v=${new java.util.Date().getTime()})}"></script>
<script type="text/html" id="tmplToolBar2">
    <div class="delet" lay-event="delet">删除</div>
</script>
<script type="text/html" id="titleTpl">
    <div class="stu">
        <span class="stuPhoto"></span>
    </div>
</script>
<script>
    document.domain = "chaoxing.com";
    let formUserId = "[[${formUserId}]]";
    let courseId = "[[${teachPlanForm?.jxjhgl_kcid}]]";
    let courseName = "[[${teachPlanForm?.jxjhgl_kcmc}]]";
    let semester = "[[${teachPlanForm?.jxjhgl_xnxq}]]";
    let totalClassHour = "[[${teachPlanForm?.jxjhgl_zxs}]]";
    let grade = "[[${teachPlanForm?.jxjhgl_nj}]]";
    let major = "[[${teachPlanForm?.jxjhgl_zy}]]";
    let fid = "[[${fid}]]";
    let teachPlanNo = "[[${teachPlanForm?.jxjhgl_zdbh}]]";
</script>
</html>