<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学生基本信息</title>
    <link rel="stylesheet" th:href="@{~/css/basic/global.css}">
    <link rel="stylesheet" th:href="@{~/css/basic/personMessage.css}">
</head>

<body>
    <div class="main">
        <div class="m-top">
            <div class="title">学生基本信息</div>
        </div>
        <div class="person-mes">
            <div class="photo">
                <img th:src="${photoUrl}" alt="">
            </div>
            <div class="stu-info">
                <dl>
                    <dt th:text="${studentInfo.xsjbxx_xm}">姓名：<span>孙棣楚</span></dt>
                    <dd th:text="${studentInfo.xsjbxx_xh}">学号：<span>2019000000</span></dd>
                </dl>
                <button onclick="editInfo()">进入编辑</button>
            </div>
        </div>
        <div class="person-intro"></div>
        <div class="i-top">
            <h3>学生信息</h3>
        </div>
        <ul class="person-nav">
            <li class="active">基本信息</li>
            <li>学籍信息</li>
            <li>招生信息</li>
            <li>家庭信息</li>
            <li>其他信息</li>
        </ul>
        <div class="person-box-wrap">
            <div class="person-box">
                <dl>
                    <dt>姓名拼音：</dt>
                    <dd th:text="${studentInfo.xsjbxx_xmpyi}">居民身份证</dd>
                </dl>
                <dl>
                    <dt>身份证件类型：</dt>
                    <dd th:text="${studentInfo.xsjbxx_sfzjlx}">中国</dd>
                </dl>
                <dl>
                    <dt>身份证号：</dt>
                    <dd th:text="${studentInfo.xsjbxx_sfzjh}">统一招生考试</dd>
                </dl>
                <dl>
                    <dt>性别：</dt>
                    <dd th:text="${studentInfo.xsjbxx_xb}">012105200012120012</dd>
                </dl>
                <dl>
                    <dt>出生日期：</dt>
                    <dd th:text="${studentInfo.xsjbxx_csrq}">012105200012120012</dd>
                </dl>
                <dl>
                    <dt>联系电话：</dt>
                    <dd th:text="${studentInfo.xsjbxx_lxdh}">住校</dd>
                </dl>
                <dl>
                    <dt>国籍：</dt>
                    <dd th:text="${studentInfo.xsjbxx_gj}">shun di chu</dd>
                </dl>
                <dl>
                    <dt>港澳台侨外：</dt>
                    <dd th:text="${studentInfo.xsjbxx_gaqtw}">未婚</dd>
                </dl>
                <dl>
                    <dt>民族：</dt>
                    <dd th:text="${studentInfo.xsjbxx_mz}">全日制</dd>
                </dl>
                <dl>
                    <dt>籍贯地：</dt>
                    <dd th:text="${studentInfo.xsjbxx_jg}">全日制</dd>
                </dl>
                <dl>
                    <dt>出生地：</dt>
                    <dd th:text="${studentInfo.xsjbxx_csd}">全日制</dd>
                </dl>
                <dl>
                    <dt>生源地：</dt>
                    <dd th:text="${studentInfo.xsjbxx_syd}">全日制</dd>
                </dl>
                <dl>
                    <dt>户口所在地：</dt>
                    <dd th:text="${studentInfo.xsjbxx_hkszd}">全日制</dd>
                </dl>
                <dl>
                    <dt>政治面貌：</dt>
                    <dd th:text="${studentInfo.jsjcxxb_zzmm}">全日制</dd>
                </dl>
                <dl>
                    <dt>宗教信仰：</dt>
                    <dd th:text="${studentInfo.xsjbxx_zjxy}">全日制</dd>
                </dl>
                <dl>
                    <dt>血型：</dt>
                    <dd th:text="${studentInfo.xsjbxx_xx}">全日制</dd>
                </dl>
                <dl>
                    <dt>曾用名：</dt>
                    <dd th:text="${studentInfo.xsjbxx_cym}">全日制</dd>
                </dl>
                <dl>
                    <dt>英文名：</dt>
                    <dd th:text="${studentInfo.xsjbxx_ywm}">全日制</dd>
                </dl>
            </div>
            <div class="person-box">
                <dl>
                    <dt>所在校区：</dt>
                    <dd th:text="${studentInfo.xsjbxx_szxq}">shun di chu</dd>
                </dl>
                <dl>
                    <dt>所在年级：</dt>
                    <dd th:text="${studentInfo.xsjbxx_sznj}">未婚</dd>
                </dl>
                <dl>
                    <dt>入学年份：</dt>
                    <dd th:text="${studentInfo.xsjbxx_rxnj}">全日制</dd>
                </dl>
                <dl>
                    <dt>系部信息：</dt>
                    <dd th:text="${studentInfo.xsjbxx_yxxx}">全日制</dd>
                </dl>
                <dl>
                    <dt>专业信息：</dt>
                    <dd th:text="${studentInfo.xsjbxx_zyxx}">居民身份证</dd>
                </dl>
                <dl>
                    <dt>班级信息：</dt>
                    <dd th:text="${studentInfo.xsjbxx_bjxx}">中国</dd>
                </dl>
                <dl>
                    <dt>学制：</dt>
                    <dd th:text="${studentInfo.xsjbxx_xz}">统一招生考试</dd>
                </dl>
                <dl>
                    <dt>培养层次：</dt>
                    <dd th:text="${studentInfo.xsjbxx_pycc}">012105200012120012</dd>
                </dl>
                <dl>
                    <dt>学生当前状态：</dt>
                    <dd th:text="${studentInfo.xsjbxx_xsdqzt}">012105200012120012</dd>
                </dl>
                <dl>
                    <dt>学生是否在校：</dt>
                    <dd th:text="${studentInfo.xsjbxx_xssfzx}">住校</dd>
                </dl>
                <dl>
                    <dt>专业方向：</dt>
                    <dd th:text="${studentInfo.xsjbxx_zyfx}">住校</dd>
                </dl>
                <dl>
                    <dt>学生是否住校：</dt>
                    <dd th:text="${studentInfo.xsjbxx_sfzx}">住校</dd>
                </dl>

            </div>
            <div class="person-box">
                <dl>
                    <dt>考生号：</dt>
                    <dd th:text="${studentInfo.xsjbxx_ksh}">居民身份证</dd>
                </dl>
                <dl>
                    <dt>学生来源：</dt>
                    <dd th:text="${studentInfo.xsjbxx_xsly}">中国</dd>
                </dl>
                <dl>
                    <dt>招生方式：</dt>
                    <dd th:text="${studentInfo.xsjbxx_zsfs}">统一招生考试</dd>
                </dl>
                <dl>
                    <dt>招生对象：</dt>
                    <dd th:text="${studentInfo.xsjbxx_zsdx}">012105200012120012</dd>
                </dl>
                <dl>
                    <dt>入学方式：</dt>
                    <dd th:text="${studentInfo.xsjbxx_rxfs}">012105200012120012</dd>
                </dl>
                <dl>
                    <dt>学习形式：</dt>
                    <dd th:text="${studentInfo.xsjbxx_xxxs}">住校</dd>
                </dl>
                <dl>
                    <dt>就读方式：</dt>
                    <dd th:text="${studentInfo.xsjbxx_jdfs}">住校</dd>
                </dl>
                <dl>
                    <dt>是否建档立卡贫困户：</dt>
                    <dd th:text="${studentInfo.xsjbxx_pkh}">住校</dd>
                </dl>
                <dl>
                    <dt>入学总分：</dt>
                    <dd th:text="${studentInfo.xsjbxx_rxzf}">住校</dd>
                </dl>
                <dl>
                    <dt>准考证号：</dt>
                    <dd th:text="${studentInfo.xsjbxx_zkzh}">住校</dd>
                </dl>
                <dl>
                    <dt>联招合作类型：</dt>
                    <dd th:text="${studentInfo.xsjbxx_lzhzlx}">住校</dd>
                </dl>
                <dl>
                    <dt>联招合作办学形式：</dt>
                    <dd th:text="${studentInfo.xsjbxx_lzbxxs}">住校</dd>
                </dl>
                <dl>
                    <dt>联招合作学校代码：</dt>
                    <dd th:text="${studentInfo.xsjbxx_lzxxdm}">住校</dd>
                </dl>
            </div>
            <div class="person-box">
                <dl>
                    <dt>家庭现住址：</dt>
                    <dd th:text="${studentInfo.xsjbxx_jtzz}">shun di chu</dd>
                </dl>
                <dl>
                    <dt>家庭邮政编码：</dt>
                    <dd th:text="${studentInfo.xsjbxx_jtyzbm}">未婚</dd>
                </dl>
                <dl>
                    <dt>家庭电话：</dt>
                    <dd th:text="${studentInfo.xsjbxx_jtdh}">全日制</dd>
                </dl>
                <dl>
                    <dt>是否随迁子女：</dt>
                    <dd th:text="${studentInfo.xsjbxx_sfsqzn}">全日制</dd>
                </dl>
                <dl>
                    <dt>是否独生子女：</dt>
                    <dd th:text="${studentInfo.xsjbxx_sfdszn}">012105200012120012</dd>
                </dl>
            </div>
            <div class="person-box">
                <dl>
                    <dt>健康状况：</dt>
                    <dd th:text="${studentInfo.xsjbxx_jkzk}">居民身份证</dd>
                </dl>
                <dl>
                    <dt>婚姻状况：</dt>
                    <dd th:text="${studentInfo.xsjbxx_hyzk}">中国</dd>
                </dl>
                <dl>
                    <dt>毕业中学：</dt>
                    <dd th:text="${studentInfo.xsjbxx_byzx}">统一招生考试</dd>
                </dl>
            </div>
        </div>
    </div>
</body>
<script th:src="@{~/js/jquery-3.6.0.min.js}"></script>
<script th:inline="javascript">
    var editUrl = [[${editUrl}]];
    $(function () {
        $(".person-nav li").click(function () {
          $(this).addClass("active").siblings().removeClass("active")
          var index = $(this).index()
          $(".person-box-wrap .person-box").eq(index).css('display','flex').siblings().hide()
        })
    })
    function editInfo() {
        window.open(editUrl);
    }

</script>

</html>