<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" li-content="IE=edge">
    <meta name="viewport" li-content="width=device-width, initial-scale=1.0">
    <title>微点名</title>
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/css/global.css'">
    <link rel="stylesheet" th:href="${_CPR_}+'/elective/layui/css/layui.css'">
    <link rel="stylesheet" href="css/commonPC.css">
    <link rel="stylesheet" href="css/reset.css">
    <link rel="stylesheet" href="css/poup.css">
    <link rel="stylesheet" href="css/microdot-name.css">
    <link rel="stylesheet" href="css/new-microdot-name.css">
    <script th:src="${_CPR_}+'/elective/layui/layui.js'"></script>
    <style>
        .main .con .table .tab-cons {
            min-height: calc(100vh - 250px) !important;
        }
    </style>

</head>

<body>
<div class="main">
    <div class="top">
        <div class="title">
            <div class="back" onclick='javascript:window.history.back();'>返回</div>
            <div class="levelone">[[${xzb}]]</div>
            <div class="icon"></div>
            <div class="leveltwo">[[${uname}]]</div>
        </div>
        <div class="edit-result">修改点名结果</div>
    </div>
    <div class="con">

        <div class="table">
            <div class="detail" style="padding-top:0;">
                <span><em>正常：</em><i class="color1" id="zc">0</i></span>
                <span th:each="state,userStat:${rollcallStateList}"><em
                        th:inline="text">[[${state.stateNamePc}]]：</em><i
                        th:class="'color'+${userStat.count+1}">0</i></span>
            </div>
            <div class="tab-cons roll-call-record">
                <table class="layui-table" id="materialTable" lay-filter="materialTable"></table>
            </div>

            <div class="bottom">
                <div class="total">
                    <em>共</em><i>0</i><em>条</em>
                </div>
                <div id="coursePage"></div>
                <div class="refresh"><i></i>刷新</div>
            </div>
        </div>


    </div>
</div>


</body>

<script th:src="${_CPR_}+'/elective/js/jquery-3.3.1.min.js'"></script>
<script th:inline="javascript">
    let pageIndex = 1;
    var pageSize = 10;
    var courseTime = [[${courseTime}]];
    var uid = [[${uid}]];
    var rollcallStateList = [[${rollcallStateList}]];
    var stateCss = ["be-late", "truant", "ask-leave", "leave-early", "others"];
    layui.use(['table', 'jquery', 'laypage', 'laydate', 'form'], function () {

        var laypage = layui.laypage;
        var table = layui.table;
        var $ = layui.jquery;
        var layer = layui.layer;
        var laydate = layui.laydate;
        var form = layui.form;


        //表格

        var table1 = table.render({
            elem: "#materialTable",
            id: 'materialTable',
            url: "/elective/rollcall/getXzbStuDetailList",
            where: {
                uid: uid,
                courseTime: courseTime,
                curPage: pageIndex,
                pageSize: pageSize
            },
            cols: [
                [{
                    field: "course",
                    align: "center",
                    title: "课程名称",
                    width: 336
                },
                    {
                        field: "classTime",
                        align: "center",
                        title: "上课时间",
                        width: 336,
                        templet: function (d) {
                            return "第" + d.zc + "周 周" + d.xq + " 第" + d.kj + "节";
                        },
                    },
                    {
                        field: "rollStatus",
                        align: "center",
                        title: "点名状态",
                        width: 150,
                        templet: function (d) {

                            let shtml = '<div class="sels disabled" style="margin-right:0;">' +
                                '<div class="select-input" data-id = "' + d.id + '">' +
                                '<div class="name-wrapper">';
                            if (d.cd == 0 && d.zt == 0 && d.kk == 0 && d.qj == 0 && d.state == 0) {
                                shtml += '<div class="name normal">正常</div>'
                            } else {
                                for (let j = 0; j < rollcallStateList.length; j++) {
                                    if ((rollcallStateList[j].type == 0 && rollcallStateList[j].stateAlias == 'cd' && d.cd == 1) ||
                                        (rollcallStateList[j].type == 0 && rollcallStateList[j].stateAlias == 'kk' && d.kk == 1) ||
                                        (rollcallStateList[j].type == 0 && rollcallStateList[j].stateAlias == 'zt' && d.zt == 1) ||
                                        (rollcallStateList[j].type == 0 && rollcallStateList[j].stateAlias == 'qj' && d.qj == 1) ||
                                        (rollcallStateList[j].type == 1 && d.state == rollcallStateList[j].id)) {
                                        let stateName = rollcallStateList[j].stateNamePc;
                                        // if (d.cd == 1 && d.zt == 1) {
                                        //     stateName = "迟到,早退";
                                        // }
                                        shtml += '<div class="name ' + stateCss[j] + '">' + stateName + '</div>'
                                        // break;
                                    }
                                }
                            }

                            shtml += '</div>' +
                                '<em></em>' +
                                '<div class="select-dropdown">' +
                                '<ul class="dropdown-lists dropdown-lists-single">' +
                                '<li class="single normal"><span>正常</span></li>';
                            for (let j = 0; j < rollcallStateList.length; j++) {
                                if (rollcallStateList[j].stateAlias == 'zt' || rollcallStateList[j].stateAlias == 'cd') {
                                    shtml += "<li class='multi " + stateCss[j] + "'><span>" + rollcallStateList[j].stateNamePc + "</span></li>";
                                } else {
                                    shtml += "<li class='single " + stateCss[j] + "'><span>" + rollcallStateList[j].stateNamePc + "</span></li>";
                                }
                            }
                            shtml += '</ul>' +
                                '</div>' +
                                '</div>' +
                                '</div>';
                            return shtml
                        },
                    },
                    {
                        field: "val",
                        align: "center",
                        title: "评价",
                        minWidth: 200,
                    },

                ]
            ],
            done: function (res) {
                $(".total i").text(res.count)
                //分页
                laypage.render({
                    elem: 'coursePage',
                    curr: pageIndex,
                    groups: 5,
                    limit: pageSize,
                    limits: [10, 20, 30],
                    count: res.count,
                    layout: ['prev', 'page', 'next', 'skip', 'limit'],
                    jump: function (obj, first) {
                        if (!first) {
                            pageIndex = obj.curr;
                            pageSize = obj.limit;
                            table.reload('materialTable', {
                                where: {
                                    uid: uid,
                                    courseTime: courseTime,
                                    curPage: pageIndex,
                                    pageSize: pageSize
                                }
                            });
                        }
                    }
                });
            }
        })


        $(".bottom .refresh").click(function () {
            $(this).toggleClass("clicked");
            location.reload()
        })


        computedHh();

        var kohh;

        function computedHh() {
            let wrapperTop = $(".tab-cons").offset().top;
            console.log(wrapperTop);
            console.log($(window).height());
            kohh = $(window).height() - wrapperTop - 106;
            $("body .main .con .table .tab-cons").css("min-height", kohh);
        }

        $(window).resize(function () {
            computedHh();
        })

        getStuNum();

        function getStuNum() {
            $.ajax({
                type: "POST",
                url: "/elective/rollcall/getStuNumV2",
                data: {courseTime: courseTime, uid: uid},
                dataType: 'json',
                success: function (data) {
                    for (let i = 0; i < data.data.length; i++) {
                        $(".detail").find("span").eq(i).find("i").text(data.data[i]);
                    }
                }
            });
        }

        //2024.11.21
        var editid = 0;
        var editData = [];

        $(".main .top .edit-result").click(function () {
            $(this).toggleClass("clicked");
            if ($(this).hasClass("clicked")) {
                $(this).text("保存点名结果");
                $(".main .con .tab-cons .sels").removeClass("disabled");
            } else {
                $(this).text("修改点名结果");
                $(".main .con .tab-cons .sels").addClass("disabled");
                editRollCallData();
            }
        })


        $(document).on("click", function (event) {
            var _con = $('.select-input');
            if (!_con.is(event.target) && _con.has(event.target).length === 0) {
                $(".select-input").removeClass("clicked");
            }

        })


        $("body").on("click", ".sels .select-input", function (e) {

            if ($(this).parent().hasClass("disabled")) {
                return false;
            }


            $(this).parents("tr").siblings().find(".select-input").removeClass("clicked");
            $(this).toggleClass("clicked");
            let texts = $(this).find(".name").text();

            $(this).find(".select-dropdown .dropdown-lists li").each(function () {
                if ($(this).text() == texts) {
                    $(this).addClass("cur").siblings().removeClass("cur");
                }
            })
            editid = $(this).attr("data-id")
            stopBubble(e);
        })


        $("body").on("click", ".sels .select-input .select-dropdown .dropdown-lists-single li.single", function (e) {
            let txt = $(this).text();
            // console.log(txt);
            var dataState = {id: editid, cd: '0', zt: '0', kk: '0', qj: '0', state: '0'};
            let className = 'normal';
            for (let j = 0; j < rollcallStateList.length; j++) {
                if (rollcallStateList[j].stateNamePc == txt) {
                    className = stateCss[j];
                    if (rollcallStateList[j].type==1){
                        dataState.state = rollcallStateList[j].id;
                    }else {
                        switch (txt) {
                            case '请假':
                                dataState.qj = '1';
                                break;
                            case '旷课':
                                dataState.kk = '1';
                                break;
                            case '迟到':
                                dataState.cd = '1';
                                break;
                            case '早退':
                                dataState.zt = '1';
                                break;
                        }
                    }
                }
            }
            $(this).parents(".select-input").find(".name-wrapper").empty();
            $(this).parents(".select-input").find(".name-wrapper").append('<div class="name ' + className + '">' +
                txt + '</div>');
            $(this).addClass('cur').siblings().removeClass('cur');
            $(this).parents(".clicked").toggleClass("clicked");
            delEditData(editid);
            editData.push(dataState);
            stopBubble(e);
        })


        $("body").on("click", ".sels .select-input .select-dropdown .dropdown-lists-single li.multi", function (e) {

            let parentObj = $(this).parents(".sels");
            var dataState = {id: editid, cd: '0', zt: '0', kk: '0', qj: '0'};
            parentObj.find(".dropdown-lists-single li.single").removeClass("cur");
            $(this).toggleClass("cur");

            let textArr = [];
            let colorArr = [];
            parentObj.find(".dropdown-lists-single li.cur").each(function () {
                textArr.push($(this).text());
                colorArr.push($(this).attr("class").split(" ")[1]);
                switch ($(this).text()) {
                    case '迟到':
                        dataState.cd = '1';
                        break;
                    case '早退':
                        dataState.zt = '1';
                        break;
                }
            })
            let textHtml = '';
            for (let i = 0; i < textArr.length; i++) {
                let className = colorArr[i];
                let txt = textArr[i];
                textHtml += '<div class="name ' + className + '">' + txt + '</div>'
            }

            if (textArr.length == 2) {
                parentObj.find(".select-input").addClass("two");
            } else {
                parentObj.find(".select-input").removeClass("two");
            }

            if (textArr.length == 0) {
                $(this).toggleClass("cur");
                return false;
            }
            console.log(textArr);
            parentObj.find(".name-wrapper").empty().append(textHtml);
            delEditData(editid);
            editData.push(dataState);
            stopBubble(e);
        })

        function editRollCallData() {
            if (editData.length == 0) {
                return
            }
            var loading = layer.load(0, {
                shade: [0.5, '#c0c0c0']
            });
            $.ajax({
                type: "POST",
                url: "/elective/rollcall/editRollCallData",
                contentType: "application/json; charset=utf-8",
                data: JSON.stringify(editData),
                // async:false,
                dataType: 'json',
                success: function (data) {
                    layer.close(loading);
                    layer.msg("修改成功")
                    setTimeout(function () {
                        location.reload()
                    }, 500)
                }
            })
        }


        function stopBubble(e) {
            if (e && e.stopPropagation)
                e.stopPropagation();
            else {
                window.event.cancelBubble = true;
            }
        }


        function delEditData(id) {
            $.each(editData, function (index, item) {
                if (item != undefined && item.id == id) {
                    editData.splice(index, 1);
                }
            });
        }

    });
</script>

</html>