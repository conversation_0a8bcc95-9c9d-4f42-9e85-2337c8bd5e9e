<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量报道设置</title>
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/basic/global.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/basic/slideCommon.css'}">
    <link rel="stylesheet" th:href="${_CPR_+_VR_+'/css/basic/dialog_report_register.css'}">
</head>

<body>
    <div class="mask"></div>
    <div class="dialog" id="batchRegistrationRegistration" style="height:224px;">
<!--        <div class="dialog-title">-->
<!--            <h5>批量报到注册</h5><span class="close"></span>-->
<!--        </div>-->
        <div class="dialog-con">
            <div class="item-list">
                <div class="item">
                    <div class="name">报到状态</div>
                    <div class="con">
                        <ul>
                            <li class="cur" data-name="report" value="0">未报到</li>
                            <li data-name="report" value="1">已报到</li>
                        </ul>
                    </div>
                </div>
                <div class="item">
                    <div class="name">注册状态</div>
                    <div class="con">
                        <ul>
                            <li class="cur" data-name="register" value="0">未注册</li>
                            <li data-name="register" value="1">已注册</li>
                        </ul>
                    </div>
                </div>
            </div>

        </div>
        <div class="dialog-footer">
            <button class="btnCancel">取消</button>
            <button class="btnSure" id="btnInheritSure">确定</button>
        </div>
    </div>
</body>
<script th:src="${_CPR_+_VR_+'/js/jquery-3.3.1.min.js'}"></script>
<script th:src="${_CPR_+_VR_+'/js/my.util.js'}"></script>
<script th:inline="javascript">
    const _VR_ = [[${_VR_}]];
    const queryId = [[${queryId}]];
    $(function () {

        //单选切换
        $("#batchRegistrationRegistration").on("click", ".dialog-con .item-list .item .con ul li", function () {
            $(this).addClass("cur").siblings().removeClass("cur")
        })

        $("#btnInheritSure").click(function () {
            // 设置内容
            var selParams = {}
            $("#batchRegistrationRegistration .dialog-con .item-list .item").each(function () {
                var key = $(this).find(".con ul .cur").attr("data-name")
                var value = $(this).find(".con ul .cur").attr('value')
                selParams[key] = value
            })
            selParams["queryId"] = queryId;
            console.log(selParams);
            U.ajax({
                type: 'post',
                url: "/basic/pop/batchReportRegister",
                data: $.param(selParams),
                dataType: 'json',
                success: function (result) {
                    if (!result.status) {
                        U.fail(result.msg);
                        return false;
                    }
                    U.success("修改成功");
                    setTimeout(function () {
                        window.parent.postMessage(JSON.stringify({action: 1}), "*");
                    }, 2000);
                }
            })
            // $("#batchRegistrationRegistration,.mask").hide();
        })

        $("#batchRegistrationRegistration .close,#batchRegistrationRegistration .btnCancel").click(function () {
            window.parent.postMessage(JSON.stringify({action: 1}), "*");
            $("#batchRegistrationRegistration,.mask").hide();
        })
    })
</script>

</html>