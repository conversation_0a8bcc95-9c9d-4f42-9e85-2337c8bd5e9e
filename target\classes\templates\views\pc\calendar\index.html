<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>校历信息</title>
    <link rel="stylesheet" th:href="@{../plugin/layui/css/layui.css}">
    <link rel="stylesheet" th:href="@{../css/calendar/indexs.css(v=${new java.util.Date().getTime()})}">
    <link rel="stylesheet" th:href="@{../css/calendar/common.css}">
    <link rel="stylesheet" th:href="@{../css/calendar/make-lessons.css}">
    <link rel="stylesheet" th:href="@{../css/jquery.mCustomScrollbar.css}">
    <script th:src="@{../plugin/layui/layui.js}"></script>
    <style>
        .genschool-calendar .window .p-content .school-calendar .sc-lable .input {
            width: 219px;
            margin-right: 24px;
        }

        .genschool-calendar .window .p-content .school-calendar .sc-lable {
            justify-content: flex-start;
        }

        .layui-form-checkbox[lay-skin=primary] span {
            padding-left: 0;
            padding-right: 15px;
            line-height: 18px;
            background: 0 0;
            color: #666;
            display: none;
        }

        .main .cons .c-top {
            position: relative;
        }

        .main .cons .c-top .batchManagement {
            position: absolute;
            top: 0;
            right: 0;
            box-shadow: 0px 0px 10px 0px #4D88FF4D;
            width: 96px;
            height: 34px;
            text-align: center;
            line-height: 34px;
            font-size: 14px;
            color: #FFFFFF;
            border-radius: 4px;
            background: #4D88FF;
            cursor: pointer;
        }

        .main .cons .j-table .j-head .j-th.th-week-remarks {
            flex: 30;
            min-width: 100px;
        }

        .main .cons .j-table .j-body .j-tr .j-td.td-week-remarks {
            flex: 30;
            min-width: 100px;
            overflow: hidden;
        }

        .main .cons .j-table .j-body .j-tr .j-td.td-week-remarks ul {
            border-bottom: none;
        }

        .main .cons .j-table .j-body .j-tr .j-td.td-week-remarks ul li {
            box-sizing: unset;
            height: 35px;
            cursor: pointer;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding: 0 8px;
        }

        .main .cons .j-table .j-body .j-tr .j-td.td-week-remarks ul li:last-child {
            border-bottom: none;
        }

        .edit-notes .window .p-content .top .lable:last-child {
            margin-right: 0;
        }

        .edit-notes .window .p-content .top .lable .btn.xnxqh {
            width: 110px;
        }

        .edit-notes .window .p-content .top .lable .btn.nytime {
            width: 100px;
        }

        .edit-notes .window .p-content .top .lable .btn.nyWeek {
            width: 70px;
        }


    </style>
</head>

<body>
<div class="main">
    <div class="j-title">
        <h4>校历信息</h4>
    </div>

    <div class="cons">
        <div class="c-top">
            <div class="select-input">
                <div class="name ckd"></div>
                <em></em>
                <div class="select-dropdown">
                    <ul class="dropdown-list">
                    </ul>
                </div>
            </div>
            <div class="generate">生成校历</div>
            <div class="export">导出校历</div>
            <div class="batchManagement">批量管理</div>
        </div>
        <div class="j-table">
            <div class="j-head">
                <div class="j-th th-sort">周序</div>
                <div class="j-th th-time">
                    年月
                </div>
                <div class="j-th th-week">星期一</div>
                <div class="j-th th-week">星期二</div>
                <div class="j-th th-week">星期三</div>
                <div class="j-th th-week">星期四</div>
                <div class="j-th th-week">星期五</div>
                <div class="j-th th-week">星期六</div>
                <div class="j-th th-week">星期日</div>
                <div class="j-th th-week-remarks">周备注</div>
                <div class="j-th th-remarks">月备注</div>
            </div>
            <div class="j-body">
            </div>
        </div>
        <p id="schoolCalendaRemarks">备注：无</p>
    </div>

</div>

<!--编辑备注-->
<div class="popup edit-notes month-remark" style="display:none;">
    <div class="window">
        <div class="p-title">
            <span>编辑备注</span>
        </div>
        <div class="p-content clearfixs">

            <div class="top">
                <div class="lable">
                    <div class="name">学年学期号</div>
                    <div class="btn xnxqh" id="monthRemarkXnxqh">2022-2023-1</div>
                </div>
                <div class="lable">
                    <div class="name">年月</div>
                    <div class="btn nytime" id="monthRemarkYearMonthTime">2022-09</div>
                </div>
                <div class="lable">
                    <div class="name">周序</div>
                    <div class="btn nyWeek">1</div>
                </div>

            </div>
            <div class="bz-con">
                <div class="name">备注</div>
                <div class="kalamu-area" id="textareaaddress" contenteditable="true" placeholder="请输入备注"></div>
            </div>

        </div>
        <div class="p-btns">
            <div class="btn cancel">取消</div>
            <div class="btn sure month-sure">确定</div>
        </div>
    </div>
</div>

<!--提示-->

<div class="popup tips" style="display:none;">
    <div class="window">

        <div class="p-content clearfixs">
            <p>保存成功</p>
        </div>
        <div class="p-btns">
            <div class="btn sure" style="margin-right: 1px;">确定</div>
        </div>
    </div>
</div>

<!--生成校历-->
<div class="popup genschool-calendar" style="display:none;">
    <div class="window">
        <div class="p-title">
            <span>生成校历</span>
        </div>
        <div class="p-content clearfixs">

            <div class="school-calendar">
                <div class="sc-lable">
                    <div class="item">
                        <div class="names">学年学期号</div>
                        <div class="select-input">
                            <div class="name">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <ul class="dropdown-list">
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="item">
                        <div class="names">学期开始时间</div>
                        <div class="input">
                            <input class="input inp layui-input layui-disabled name" disabled type="text"
                                   placeholder="请输入">
                        </div>
                        <!--<div class="select-input">
                            <div class="name">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <ul class="dropdown-list">
                                </ul>
                            </div>
                        </div>-->
                    </div>
                </div>
                <div class="sc-lable">
                    <div class="item">
                        <div class="names">开始周次</div>
                        <div class="input">
                            <input class="input inp layui-input layui-disabled name" disabled type="text"
                                   placeholder="请输入">
                        </div>
                        <!--<div class="select-input">
                            <div class="name">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <ul class="dropdown-list">
                                </ul>
                            </div>
                        </div>-->
                    </div>
                    <div class="item">
                        <div class="names">结束周次</div>
                        <div class="input">
                            <input class="input inp layui-input layui-disabled name" disabled type="text"
                                   placeholder="请输入">
                        </div>
                        <!--<div class="select-input">
                            <div class="name">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <ul class="dropdown-list">
                                </ul>
                            </div>
                        </div>-->
                    </div>
                </div>
                <div class="bz-con">
                    <div class="name">校历备注</div>
                    <div class="kalamu-area" id="textareaaddres" contenteditable="true" placeholder="请输入"></div>
                </div>
            </div>

        </div>
        <div class="p-btns">
            <div class="btn cancel">取消</div>
            <div class="btn sure">确定</div>
        </div>
    </div>
</div>

<!--编辑课表结构-->
<div class="popup edit-schedule" style="display:none;">
    <div class="window">
        <div class="p-title">
            <span>编辑课表结构</span>
        </div>
        <div class="p-content clearfixs">

            <div class="schedule">
                <h2>时段</h2>
                <div class="lable">
                    <div class="item">
                        <div class="names">晨读</div>
                        <div class="select-input morning w78">
                            <div class="name">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <ul class="dropdown-list">
                                    <li>1</li>
                                    <li>2</li>
                                    <li>3</li>
                                    <li>4</li>
                                    <li>5</li>
                                    <li>6</li>
                                    <li>7</li>
                                </ul>
                            </div>
                        </div>
                        <div class="tit">节</div>
                    </div>
                    <div class="item">
                        <div class="names">上午</div>
                        <div class="select-input w78">
                            <div class="name">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <ul class="dropdown-list">
                                    <li>1</li>
                                    <li>2</li>
                                    <li>3</li>
                                    <li>4</li>
                                    <li>5</li>
                                    <li>6</li>
                                    <li>7</li>
                                </ul>
                            </div>
                        </div>
                        <div class="tit">节</div>
                    </div>
                    <div class="item">
                        <div class="names">下午</div>
                        <div class="select-input w78">
                            <div class="name">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <ul class="dropdown-list">
                                    <li>1</li>
                                    <li>2</li>
                                    <li>3</li>
                                    <li>4</li>
                                    <li>5</li>
                                    <li>6</li>
                                    <li>7</li>
                                </ul>
                            </div>
                        </div>
                        <div class="tit">节</div>
                    </div>
                    <div class="item">
                        <div class="names">晚上</div>
                        <div class="select-input night w78">
                            <div class="name">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <ul class="dropdown-list">
                                    <li>1</li>
                                    <li>2</li>
                                    <li>3</li>
                                    <li>4</li>
                                    <li>5</li>
                                    <li>6</li>
                                    <li>7</li>
                                </ul>
                            </div>
                        </div>
                        <div class="tit">节</div>
                    </div>
                </div>
                <!-- <div class="lable">
                    <div class="item">
                        <div class="names">下午</div>
                        <div class="select-input">
                            <div class="name">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <ul class="dropdown-list">
                                    <li>1</li>
                                    <li>2</li>
                                    <li>3</li>
                                    <li>4</li>
                                    <li>5</li>
                                    <li>6</li>
                                    <li>7</li>
                                </ul>
                            </div>
                        </div>
                        <div class="tit">节</div>
                    </div>
                    <div class="item">
                        <div class="names">晚上</div>
                        <div class="select-input night">
                            <div class="name">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <ul class="dropdown-list">
                                    <li>1</li>
                                    <li>2</li>
                                    <li>3</li>
                                    <li>4</li>
                                    <li>5</li>
                                    <li>6</li>
                                    <li>7</li>
                                </ul>
                            </div>
                        </div>
                        <div class="tit">节</div>
                    </div>
                </div> -->
            </div>
            <div class="schedule">
                <h2>时间</h2>
                <div class="lable wn-lab cd-lab hide">
                    <div class="item">
                        <div class="names w98">晨读开始时间</div>
                        <div class="select-input w150">
                            <div class="name">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <ul class="dropdown-list">
                                    <li>05：00</li>
                                    <li>05：06</li>
                                    <li>05：11</li>
                                    <li>05：15</li>
                                    <li>05：20</li>
                                    <li>05：25</li>
                                    <li>05：30</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="item">
                        <div class="names">课节时长</div>
                        <div class="select-input w80">
                            <div class="name">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <ul class="dropdown-list">
                                    <li>20</li>
                                    <li>25</li>
                                    <li>30</li>
                                    <li>35</li>
                                    <li>40</li>
                                    <li>45</li>
                                    <li>50</li>
                                </ul>
                            </div>
                        </div>
                        <div class="tit">分钟</div>
                    </div>
                    <div class="item">
                        <div class="names">课节时长</div>
                        <div class="select-input w80">
                            <div class="name">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <ul class="dropdown-list">
                                    <li>20</li>
                                    <li>25</li>
                                    <li>30</li>
                                    <li>35</li>
                                    <li>40</li>
                                    <li>45</li>
                                    <li>50</li>
                                </ul>
                            </div>
                        </div>
                        <div class="tit">分钟</div>
                    </div>
                </div>
                <div class="lable">
                    <div class="item">
                        <div class="names w98">上午开始时间</div>
                        <div class="select-input w150">
                            <div class="name">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <ul class="dropdown-list">
                                    <li>05：00</li>
                                    <li>05：06</li>
                                    <li>05：11</li>
                                    <li>05：15</li>
                                    <li>05：20</li>
                                    <li>05：25</li>
                                    <li>05：30</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="item">
                        <div class="names">课节时长</div>
                        <div class="select-input w80">
                            <div class="name">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <ul class="dropdown-list">
                                    <li>20</li>
                                    <li>25</li>
                                    <li>30</li>
                                    <li>35</li>
                                    <li>40</li>
                                    <li>45</li>
                                    <li>50</li>
                                </ul>
                            </div>
                        </div>
                        <div class="tit">分钟</div>
                    </div>
                    <div class="item">
                        <div class="names">课节时长</div>
                        <div class="select-input w80">
                            <div class="name">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <ul class="dropdown-list">
                                    <li>20</li>
                                    <li>25</li>
                                    <li>30</li>
                                    <li>35</li>
                                    <li>40</li>
                                    <li>45</li>
                                    <li>50</li>
                                </ul>
                            </div>
                        </div>
                        <div class="tit">分钟</div>
                    </div>
                </div>
                <div class="lable">
                    <div class="item">
                        <div class="names w98">下午开始时间</div>
                        <div class="select-input w150">
                            <div class="name">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <ul class="dropdown-list">
                                    <li>05：00</li>
                                    <li>05：06</li>
                                    <li>05：11</li>
                                    <li>05：15</li>
                                    <li>05：20</li>
                                    <li>05：25</li>
                                    <li>05：30</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="item">
                        <div class="names">课节时长</div>
                        <div class="select-input w80">
                            <div class="name">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <ul class="dropdown-list">
                                    <li>20</li>
                                    <li>25</li>
                                    <li>30</li>
                                    <li>35</li>
                                    <li>40</li>
                                    <li>45</li>
                                    <li>50</li>
                                </ul>
                            </div>
                        </div>
                        <div class="tit">分钟</div>
                    </div>
                    <div class="item">
                        <div class="names">课节时长</div>
                        <div class="select-input w80">
                            <div class="name">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <ul class="dropdown-list">
                                    <li>20</li>
                                    <li>25</li>
                                    <li>30</li>
                                    <li>35</li>
                                    <li>40</li>
                                    <li>45</li>
                                    <li>50</li>
                                </ul>
                            </div>
                        </div>
                        <div class="tit">分钟</div>
                    </div>
                </div>
                <div class="lable wd-lab hide">
                    <div class="item">
                        <div class="names w98">晚上开始时间</div>
                        <div class="select-input w150">
                            <div class="name">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <ul class="dropdown-list">
                                    <li>05：00</li>
                                    <li>05：06</li>
                                    <li>05：11</li>
                                    <li>05：15</li>
                                    <li>05：20</li>
                                    <li>05：25</li>
                                    <li>05：30</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="item">
                        <div class="names">课节时长</div>
                        <div class="select-input w80">
                            <div class="name">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <ul class="dropdown-list">
                                    <li>20</li>
                                    <li>25</li>
                                    <li>30</li>
                                    <li>35</li>
                                    <li>40</li>
                                    <li>45</li>
                                    <li>50</li>
                                </ul>
                            </div>
                        </div>
                        <div class="tit">分钟</div>
                    </div>
                    <div class="item">
                        <div class="names">课节时长</div>
                        <div class="select-input w80">
                            <div class="name">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <ul class="dropdown-list">
                                    <li>20</li>
                                    <li>25</li>
                                    <li>30</li>
                                    <li>35</li>
                                    <li>40</li>
                                    <li>45</li>
                                    <li>50</li>
                                </ul>
                            </div>
                        </div>
                        <div class="tit">分钟</div>
                    </div>
                </div>

            </div>

        </div>
        <div class="p-btns">
            <div class="btn cancel">取消</div>
            <div class="btn sure">确定</div>
        </div>
    </div>
</div>

<!--生成校历-->

<div class="popup create-cal" style="display:none;">
    <div class="window">
        <div class="p-title">
            <span>生成校历</span>
        </div>
        <div class="p-content clearfixs">
            <p>已成功生成校历</p>
        </div>
        <div class="p-btns">
            <div class="btn cancel">取消</div>
            <div class="btn sure">确定</div>
        </div>
    </div>
</div>

<!--校历日期编辑-->
<div class="popup date-edit" style="display:none;">
    <div class="window">
        <div class="p-title">
            <span>校历日期编辑</span>
        </div>
        <div class="p-content clearfixs">
            <div class="con">

                <div class="scalp">
                    <div class="item">
                        <div class="names">日程</div>
                        <div class="input">
                            <input class="input inp layui-input" id="name" type="text" placeholder="请输入">
                        </div>
                    </div>
                    <div class="item">
                        <div class="names">地点</div>
                        <div class="input">
                            <input class="input inp layui-input" id="place" type="text" placeholder="请输入">
                        </div>
                    </div>
                    <div class="btn-list">
                        <div class="btn">添加</div>
                        <div class="delet">删除</div>
                        <div class="search">查询</div>
                    </div>

                    <div class="item" style="display: none">
                        <div class="names">是否停课</div>
                        <div class="select-input">
                            <div class="name" id="stop">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <ul class="dropdown-list">
                                    <li value="1">是</li>
                                    <li value="0">否</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="item" style="display: none">
                        <div class="names">是否全天</div>
                        <div class="select-input">
                            <div class="name" id="whole">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <ul class="dropdown-list">
                                    <li value="1">是</li>
                                    <li value="0">否</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="table">
                    <table class="layui-table" id="materialTable" lay-filter="materialTable">
                    </table>
                    <div class="no-data" style="display:none;"></div>
                </div>
                <div id="coursePage"></div>
                <div class="all-select" style="display:none;">选中全部数据</div>
            </div>
        </div>
        <div class="p-btns">
            <div class="btn cancel">取消</div>
            <div class="btn sure">确定</div>
        </div>
    </div>
</div>

<!--添加日程-->
<div class="popup add-schedule" style="display:none;">
    <div class="window">
        <div class="p-title">
            <span>添加日程</span>
        </div>
        <div class="p-content clearfixs">
            <div class="con">
                <input type="hidden" id="scheduleID">
                <div class="lable">
                    <div class="names">标题</div>
                    <div class="input">
                        <input id="scheduleName" class="input inp layui-input" type="text" placeholder="请输入">
                    </div>
                </div>
                <div class="lable">
                    <div class="names">地点</div>
                    <div class="input">
                        <input id="schedulePlace" class="input inp layui-input" type="text" placeholder="请输入">
                    </div>
                </div>
                <div class="lable">
                    <div class="names">日期</div>
                    <div class="select-input w476">
                        <input type="text" name="time" placeholder="请选择" autocomplete="off"
                               class="layui-input lay" id="dateSel" readonly>
                    </div>
                </div>

                <!--<div class="lable closeschool-state">
                    <div class="names">是否停课</div>
                    <div class="switch" id="suspendStudy">
                        <span></span>
                    </div>
                    <div class="tit">是</div>
                </div>-->
                <!--                <div class="lable">-->
                <!--                    <div class="names">是否全天</div>-->
                <!--                    <div class="switch allDay" id="allDay">-->
                <!--                        <span></span>-->
                <!--                    </div>-->
                <!--                    <div class="tit">是</div>-->
                <!--                </div>-->
                <div class="lable suspension-time" style="display:none;">
                    <div class="names">停课时间</div>
                    <div class="radio">
                        <ul id="allDay">
                            <li class="cur">全天</li>
                            <li>非全天</li>
                        </ul>
                    </div>
                </div>
                <div class="lable allDayResult" style="display:none;">
                    <div class="names">停课节选范围</div>
                    <div class="item">
                        <div class="names">开始节次</div>
                        <div class="select-input">
                            <div class="name" id="start">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <ul class="dropdown-list" id="start_section">

                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="item">
                        <div class="names">结束节次</div>
                        <div class="select-input">
                            <div class="name" id="end">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <ul class="dropdown-list" id="end_section">

                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="lable lesson-state" style="display:none;">
                    <div class="names">是否补课</div>
                    <div class="switch" id="makeUpLessons">
                        <span></span>
                    </div>
                    <div class="tit">是</div>
                </div>
                <div class="lable lesson-time" style="display:none;">
                    <div class="names">补课日期</div>
                    <div class="times">
                        <input type="text" name="time" placeholder="请选择" class="layui-input lay1"
                               id="makeUpLessonsTime">
                    </div>
                </div>
                <div class="lable lesson-range" style="display:none;">
                    <div class="names">补课节选范围</div>
                    <div class="item">
                        <div class="names">开始节次</div>
                        <div class="select-input">
                            <div class="name" id="makeUpLessonsStart">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <ul class="dropdown-list" id="makeUpLessonsStartSection">
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="item">
                        <div class="names">结束节次</div>
                        <div class="select-input">
                            <div class="name" id="makeUpLessonsEnd">请选择</div>
                            <em></em>
                            <div class="select-dropdown">
                                <ul class="dropdown-list" id="makeUpLessonsEndSection">

                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="lable">
                    <div class="names">备注</div>
                    <div class="kalamu-area" id="textareaaddre" contenteditable="true" placeholder="请输入"></div>
                </div>
            </div>
        </div>
        <div class="p-btns">
            <div class="btn cancel">取消</div>
            <div class="btn sure editSure">确定</div>
        </div>
    </div>
</div>
<script th:src="@{../js/jquery-1.11.3.min.js}"></script>
<script th:src="@{../js/jquery.mCustomScrollbar.concat.min.js}"></script>
<script th:src="@{../js/chinese_lunar_calendar.min.js}"></script>
<script type="text/html" id="tmplToolBar2">
    <span class="edit" lay-event="edit">编辑</span>
    <span class="delete" lay-event="delete">删除</span>
</script>
<script th:src="@{../js/calendar/index.js(v=${new java.util.Date().getTime()})}"></script>
<script>
    $(document).ready(function () {
        getSemesterData();
    })

    function getSemesterData() {
        var html = "";
        $.post("../calendar/getSemesterData", {}, function (data) {
            if (data.status) {
                for (var i = 0; i < data.list.length; i++) {
                    var semester = data.list[i];
                    var curStr = "";
                    if (semester.xnxq_sfdqxq === "是") {
                        curStr = "cur";
                        $(".c-top .ckd").text(semester.xnxq_xnxqh);
                        let data = getSchoolCalendar(this, semester.xnxq_xnxqh, 1);
                        if (!data) {
                            $(".main .cons .c-top .generate").click();
                        }
                    }
                    html += "<li onclick='getSchoolCalendar(this,\"\",1);' class='" + curStr + "'>" + semester.xnxq_xnxqh + "</li>";
                }
                $(".select-input").eq(0).find(".dropdown-list").html(html);
                $(".genschool-calendar .sc-lable .item").eq(0).find(".dropdown-list").html(html);
            }
        }, "json");
    }

    function getMonthRemark() {
        $(".j-table .j-tr").each(function () {
            var _this = $(this);
            var date = $(this).find(".ym").text();
            $.post("../calendar/month/remark/single", {dateTime: date, xnxq: $(".ckd").text(),}, function (data) {
                if (data.single) {
                    _this.find(".marks").text(data.single.descr);
                    _this.attr("id", data.single.id);
                }
            }, "json");
        })
    }

    function getSchoolCalendarScheduleDay(xnxq) {
        $(".j-table .j-tr .m-table tr td span p").text("");
        festival();
        $.post("../calendar/getSchoolCalendarScheduleDay", {xnxq: xnxq, type: 1}, function (data) {
            if (data.status) {
                for (var i = 0; i < data.list.length; i++) {
                    var shedule = data.list[i];
                    $(".j-table .j-tr .m-table tr td span em").each(function () {
                        var day = $(this).text();
                        if (day < 10) {
                            day = "0" + day;
                        }
                        var date = $(this).parents(".j-tr").attr("date") + "-" + day;
                        if (shedule.dateTime == date) {
                            $(this).next().text(shedule.name);
                            return false;
                        }
                    })
                }
            }
        }, "json");
    }

    /**
     * 获取日期段所有的日期字符串
     * var weak = getAllWeak(begintime,endtime)+"," 加“，”  //调用方法将动态的开始时间，结束时间放
     * 入参数中
     * weak.split(",")[i]  //将获取的字符串截取
     * @param start_time
     * @param end_time
     * @returns  返回所有日期的字符串
     */
    function getAllWeak(start_time, end_time) {
        var begin = new Date(start_time), end = new Date(end_time);
        var begin_time = begin.getTime(), end_time = end.getTime(), time_diff = end_time - begin_time;
        var all_d = [];
        for (var i = 0; i <= time_diff; i += 86400000) {
            var ds = new Date(begin_time + i);
            all_d.push(ds.getFullYear() + "-" + (ds.getMonth() + 1) + "-" + ds.getDate());
        }
        return all_d;
    }

    /**
     * 获取日期对对应的星期几
     * getWeakDays(weak.split(",")[i]) //将拆分的日期获取周几
     * @param days
     * @returns
     */
    function getWeakDays(days) {
        var weekDay = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];
        var myDate = new Date(Date.parse(days));
        return myDate.getDay();
    }

    function getMonthBetween(start, end) {
        var result = [];
        var s = start.split("-");
        var e = end.split("-");
        var min = new Date();
        var max = new Date();
        min.setFullYear(s[0], s[1] * 1 - 1, 1);//开始日期
        max.setFullYear(e[0], e[1] * 1 - 1, 1);//结束日期
        var curr = min;
        while (curr <= max) {
            var month = curr.getMonth() + 1;
            var temp = month;
            if (temp < 10) {
                temp = "0" + month;
            }
            var year = curr.getFullYear();
            result.push(year + "-" + temp);
            curr.setMonth(month);
        }
        return result;
    }

    function getMonthDays(year, month) {
        var thisDate = new Date(year, month, 0); //当天数为0 js自动处理为上一月的最后一天
        return thisDate.getDate();
    }

    function getSchoolCalendar(_this, xnxq_xnxqh, type) {
        let loading = layer.load(1);
        xnxq_xnxqh = xnxq_xnxqh ? xnxq_xnxqh : $(_this).text();
        if (type == 1) {
            $(".j-body").empty();
            $("#schoolCalendaRemarks").text("备注：");
        }
        var resData = "";
        $.ajaxSettings.async = false;
        $.post("../calendar/getXnxqDetailData", {xnxq: xnxq_xnxqh, type: type}, function (data) {
            resData = data.calendarSet ? data.calendarSet : data.xnxqForm;
            if (resData && type == 1) {
                var startTime = resData.startTime;
                var endTime = resData.endTime;
                var descr = resData.descr;
                schoolCalendaHtml(startTime, endTime, descr);
                getSchoolCalendarScheduleDay(xnxq_xnxqh);
            }
        }, "json");
        $.ajaxSettings.async = true;
        layer.close(loading);
        return resData;
    }

    function weeksHtml() {
        var trLen = 0;
        $(".j-body .td-table").each(function () {
            var sortHtml = "";
            let weekHtml = "";
            var curLastVal = $(this).find(".m-table tr td").last().text();
            var nextFirstVal = $(this).parents(".j-tr").next().find(".m-table tr td").first().text();
            var idx = $(this).index();
            var start = idx == 0 ? 0 : trLen;
            trLen = trLen + $(this).find("tr").length;
            for (var i = start; i < trLen; i++) {
                if (!curLastVal && !nextFirstVal && i == trLen - 1) {
                    trLen = trLen - 1;
                }
                sortHtml += "<li>" + (i + 1) + "</li>";
                weekHtml += "<li></li>";
            }
            $(this).parents(".j-tr").find(".td-sort ul").html(sortHtml);
            $(this).parents(".j-tr").find(".td-week-remarks ul").html(weekHtml);
        })
        getCalendarWeekSet();
    }

    function getCalendarWeekSet() {
        $.post("/calendar/getCalendarWeekSet", {
            term: $(".c-top .ckd").text()
        }, function (data) {
            if (data.list) {
                for (let i = 0; i < data.list.length; i++) {
                    let info = data.list[i];
                    let idx = 0;
                    $(".j-body .td-sort li").each(function () {
                        idx++;
                        if ($(this).text() == info.week) {
                            $(this).parents(".j-body").find(".td-week-remarks li:eq(" + (idx - 1) + ")").text(info.remarks).attr("id", info.id);
                        }
                    })
                }
            }
        }, "json");
    }


    function schoolCalendaHtml(startTime, endTime, descr) {
        var calendarHtml = "";
        var monthArray = getMonthBetween(startTime, endTime);
        let startDay = new Date(startTime).getDate();
        let endDay = new Date(endTime).getDate();
        for (var k = 0; k < monthArray.length; k++) {
            var year = new Date(Date.parse(monthArray[k])).getFullYear();
            var month = new Date(Date.parse(monthArray[k])).getMonth() + 1;
            var ym = year + "年" + month + "月";
            if (month < 10) {
                month = "0" + month;
            }
            let day = k === 0 ? startDay : 1;
            let counter = day;
            let daysInMonth = getDaysInMonth(year, month);
            const dayOfWeek = getDayOfWeek(year, new Date(Date.parse(monthArray[k])).getMonth(), counter);
            daysInMonth = k === monthArray.length - 1 ? endDay : daysInMonth;
            calendarHtml += "<div class=\"j-tr\" date = \"" + year + "-" + month + "\">";
            calendarHtml += "<div class=\"j-td td-sort\">";
            calendarHtml += "<ul><li>1</li><li>2</li><li>3</li><li>4</li><li>5</li></ul></div>";
            calendarHtml += "<div class=\"j-td td-time\"><div class=\"ym\"><span>" + ym + "</span></div></div>";
            calendarHtml += "<div class=\"j-td td-table\"><table class=\"m-table\"><tbody>";
            for (let i = 0; i < Math.ceil((daysInMonth + dayOfWeek - day) / 7); i++) {
                if (k === monthArray.length - 1 && endDay < counter) {
                    break;
                }
                calendarHtml += "<tr>";
                for (let j = 0; j < 7; j++) {
                    if (i === 0 && j < dayOfWeek - 1 || counter > daysInMonth) {
                        calendarHtml += "<td></td>";
                    } else {
                        calendarHtml += "<td><span><em>" + counter++ + "</em><p></p></span></td>";
                    }
                }
                calendarHtml += "</tr>";
            }
            calendarHtml += "</tbody></table></div>";
            calendarHtml += "<div class=\"j-td td-week-remarks\">";
            calendarHtml += "<ul><li>1</li><li>2</li><li>3</li><li>4</li><li>5</li></ul></div>";
            calendarHtml += "<div class=\"j-td td-remarks\"><div class=\"marks\"></div>";
            calendarHtml += "</div></div>";
        }
        $("#schoolCalendaRemarks").text("备注：" + descr);
        $(".j-body").html(calendarHtml);
        weeksHtml();
        getMonthRemark();
    }

    function getDaysInMonth(year, month) {
        return new Date(year, month, 0).getDate();
    }

    function getDayOfWeek(year, month, day) {
        return new Date(year, month, day).getDay() === 0 ? 7 : new Date(year, month, day).getDay();
    }

    //切换
    $(".add-schedule").on("click", ".window .p-content .con .lable .switch", function () {
        /*if ($(this).parents(".disable").length > 0) {
            return false;
        }*/
        $(this).toggleClass("active");
        if ($(this).hasClass("active")) {
            $(this).parent(".lable").find(".tit").text("是");
            $(this).parent(".lable").find(".tit").attr("data-value", "1");
        } else {
            $(this).parent(".lable").find(".tit").text("否");
            $(this).parent(".lable").find(".tit").attr("data-value", "0");
        }

        boxstate();
        // if ($(this).hasClass("allDay") && $(this).hasClass("active")) {
        //     $(".add-schedule .allDayResult").hide();
        // } else if ($(this).hasClass("allDay") && !$(this).hasClass("active")) {
        //     $(".add-schedule .allDayResult").show();
        // }
    })

    //停课时间切换
    $(".add-schedule .window").on("click", ".p-content .con .lable .radio ul li", function () {
        /*if ($(this).parents(".disable").length > 0) {
            return false;
        }*/
        $(this).addClass("cur").siblings().removeClass("cur");
        boxstate();
    })

    //显隐方法

    function boxstate() {
        let p1 = $(".add-schedule .window .p-content .con .suspension-time .radio ul li.cur").index();
        let p2 = $(".add-schedule .window .p-content .con .lesson-state .switch").hasClass("active");
        let p3 = $(".add-schedule .window .p-content .con .closeschool-state .switch").hasClass("active");
        if (p3) {
            if (p1 == 0) {
                $(".suspension-time").show();
                $(".lesson-state").show();
                $(".allDayResult").hide();
                if (p2) {
                    $(".lesson-time").show();
                    $(".lesson-range").hide();
                } else {
                    $(".lesson-time").hide();
                    $(".lesson-range").hide();
                }
            } else {
                $(".allDayResult").show();
                if (p2) {
                    $(".lesson-time").show();
                    $(".lesson-range").show();
                } else {
                    $(".lesson-time").hide();
                    $(".lesson-range").hide();
                }
            }

        } else {
            $(".suspension-time").hide();
            $(".lesson-state").hide();
            $(".add-schedule .window .p-content .con .suspension-time .radio ul li").eq(0).addClass("cur").siblings().removeClass("cur");
            $(".add-schedule .window .p-content .con .lesson-state .switch").removeClass("active");
            $(".allDayResult").hide();
            $(".lesson-time").hide();
            $(".lesson-range").hide();

        }


    }

    //下拉滚动条
    $(".select-input .select-dropdown").mCustomScrollbar({
        axis: "y"
    });
    //下拉
    $("body").on("click", ".select-input .name", function (e) {
        /*if ($(this).parents(".disable").length > 0) {
            return false;
        }*/
        $(this).parent().toggleClass("clicked");
        stopBubble(e);
    })
    $("body").on("click", ".select-input .select-dropdown .dropdown-list li", function (e) {
        $(this).addClass("cur").siblings().removeClass("cur");
        let kosl = '';
        let val = '';
        kosl = $(this).text();
        val = $(this).val();
        $(this).parents(".select-input").find(".name").addClass("ckd");
        $(this).parents(".select-input").find(".name").text(kosl);
        $(this).parents(".select-input").find(".name").attr("data-value", val);
        $(this).parents(".select-input").removeClass("clicked");

        if ($(this).parents(".select-input").hasClass("morning")) {
            $(".edit-schedule .window .p-content .schedule .lable.cd-lab").removeClass("hide");
        }
        if ($(this).parents(".select-input").hasClass("night")) {
            $(".edit-schedule .window .p-content .schedule .lable.wd-lab").removeClass("hide");
        }
    })


    function stopBubble(e) {
        if (e && e.stopPropagation)
            e.stopPropagation();
        else {
            window.event.cancelBubble = true;
        }
    }

    $(document).on("click", function (event) {
        var _con = $('.select-input');
        if (!_con.is(event.target) && _con.has(event.target).length === 0) {
            $(".select-input").removeClass("clicked");
        }
    })

    //校历弹窗业务 start
    //获取日程列表
    var tableData = [];

    function getScheduleList(page, event) {
        var xnxq = $(".c-top .ckd").html();
        var name = $("#name").val();
        var place = $("#place").val();
        var stop = $("#stop").attr("data-value");
        var whole = $("#whole").attr("data-value");
        let dateTime = $("#dateSel").val();
        if (page < 1) {
            page = 1;
        }
        $.post("../calendar/schedule/list", {
            xnxq: xnxq,
            name: name,
            place: place,
            stop: stop,
            whole: whole,
            page: page,
            dateTime: dateTime
        }, function (res) {
            let count = 0;
            tableData = [];
            if (res.status) {
                count = res.count == null ? 0 : res.count
                tableData = res.data == null ? [] : res.data;
            } else {
                count = 0;
                layer.msg(res.msg == null ? res.message : res.msg, {icon: 2, time: 3000});
            }
            if (count === 0 && !event && dateTime) {
                $(".add-schedule").show();
                $(".lesson-state,.suspension-time,.allDayResult,.lesson-time,.lesson-range").hide();
                $(".add-schedule .window .p-title span").text("添加日程");
                getKbjg();
                edit();
            }
            if (parseInt(count) === 1 && !event && dateTime) {
                $(".add-schedule").show();
                $(".add-schedule .window .p-title span").text("编辑日程");
                getKbjg();
                edit(tableData[0].id);
            }
            if (parseInt(count) > 1) {
                $(".date-edit").show();
            }
            reloadData();
            flushPage(page, count)
        })
    }

    //查询
    $(".search").click(function () {
        getScheduleList(1, "search");
    })

    //刷新页面
    function flushPage(curr, count) {
        layui.use(['laypage'], function () {
            var laypage = layui.laypage;
            laypage.render({
                elem: 'coursePage',
                curr: curr,
                groups: 5,
                limit: 5,
                count: count,
                layout: ['count', 'prev', 'page', 'next', 'skip'],
                jump: function (obj, first) {
                    if (!first) {
                        getScheduleList(obj.curr);
                    }
                }
            });
        })
    }

    function remove(ids) {
        let xnxq = $(".c-top .ckd").html();
        $.post("../calendar/schedule/del", {ids: ids}, function (res) {
            if (res.status) {
                layer.msg(res.msg == null ? res.message : res.msg, {icon: 1, time: 3000});
                getScheduleList(1, "del");
                getSchoolCalendarScheduleDay(xnxq);
            } else {
                layer.msg(res.msg == null ? res.message : res.msg, {icon: 2, time: 3000});
            }
        })
    }


    function edit(id) {
        getEndTime("#makeUpLessonsTime");
        var xnxq = $(".c-top .ckd").html();
        $("#scheduleID").val("")
        $("#scheduleName").val("")
        $("#schedulePlace").val("")
        $("#suspendStudy").removeClass("active");
        $("#allDay").removeClass("active");
        $("#allDay li").eq(0).click();
        $("#start").removeClass("clicked")
        $("#start").text("请选择")
        $("#end").removeClass("clicked")
        $("#end").text("请选择")
        $("#makeUpLessonsEnd").removeClass("clicked").text("请选择")
        $("#makeUpLessonsStart").removeClass("clicked").text("请选择")
        $("#makeUpLessons").removeClass("active");
        $("#makeUpLessonsTime").val("")
        $("#makeUpLessonsRange li").eq(0).click();
        $("#textareaaddre").val("")
        $(".closeschool-state .tit,.lesson-state .tit").text("否");
        if (id != null) {
            $("#dateSel").val("");
            $.post("../calendar/schedule/search", {id: id}, function (res) {
                if (res.status) {
                    let data = res.data;
                    $("#scheduleID").val(data.id)
                    $("#scheduleName").val(data.name)
                    $("#schedulePlace").val(data.place)
                    $("#dateSel").val(data.dateTime)
                    if (data.suspendStudy == 1) {
                        $(".lesson-state,.suspension-time").show();
                        $("#suspendStudy").addClass("active");
                        $(".closeschool-state .tit").text("是");
                    } else {
                        $(".lesson-state,.suspension-time").hide();
                        $("#suspendStudy").removeClass("active");
                        $(".closeschool-state .tit").text("否");
                    }
                    if (data.allDay == 1) {
                        // $("#allDay").addClass("active");
                        // $(".add-schedule .allDayResult").hide();
                        $("#allDay li").eq(0).addClass("cur").siblings().removeClass("cur");
                    } else {
                        // $("#allDay").removeClass("active");
                        // $(".add-schedule .allDayResult").show();
                        $("#allDay li").eq(1).addClass("cur").siblings().removeClass("cur");
                    }
                    if (data.startSection != null) {
                        $("#start").html(data.startSection).addClass("clicked")
                        $("#start_section li").eq(data.startSection - 1).addClass("cur");
                    }
                    if (data.endSection != null) {
                        $("#end").html(data.endSection).addClass("clicked")
                        $("#end_section li").eq(data.endSection - 1).addClass("cur");
                    }
                    if (data.makeUpLessons == 1) {
                        $("#makeUpLessons").addClass("active");
                        $(".lesson-state .tit").text("是");
                    } else {
                        $("#makeUpLessons").removeClass("active");
                        $(".lesson-state .tit").text("否");
                    }
                    if (data.makeUpLessonsStartSection) {
                        $("#makeUpLessonsStart").html(data.makeUpLessonsStartSection).addClass("clicked");
                        $("#makeUpLessonsStartSection li").eq(data.makeUpLessonsStartSection - 1).addClass("cur");
                    }
                    if (data.makeUpLessonsEndSection) {
                        $("#makeUpLessonsEnd").html(data.makeUpLessonsEndSection).addClass("clicked");
                        $("#makeUpLessonsEndSection li").eq(data.makeUpLessonsEndSection - 1).addClass("cur");
                    }
                    $("#makeUpLessonsTime").val(data.makeUpLessonsTime)
                    boxstate();
                    $("#textareaaddre").val(data.descr)
                    getSchoolCalendarScheduleDay(xnxq)
                } else {
                    layer.msg(res.msg == null ? res.message : res.msg, {icon: 2, time: 3000});
                }
            })
        }
    }

    //教学周
    var jxz = null;
    $(".editSure").click(function () {
        var id = $("#scheduleID").val();
        var xnxq = $(".c-top .ckd").html();
        var name = $("#scheduleName").val();
        var place = $("#schedulePlace").val();
        var dateTime = $("#dateSel").val();
        var suspendStudy = 0
        if ($("#suspendStudy").hasClass('active')) {
            suspendStudy = 1;
        }
        if (!name) {
            layer.msg('请输入标题', {icon: 2, time: 3000});
            return false;
        }
        if (name.length > 8) {
            layer.msg('标题字数不能超过8个字符', {icon: 2, time: 3000});
            return false;
        }
        /*if (!place) {
            layer.msg('请输入地点', {icon: 2, time: 3000});
            return false;
        }*/
        if (!dateTime) {
            layer.msg('请选择日期', {icon: 2, time: 3000});
            return false;
        }
        var allDay = 0;
        var startSection = null;
        var endSection = null;
        if ($("#allDay li.cur").index() === 0) {
            allDay = 1
            startSection = min;
            endSection = max;
        } else {
            startSection = $("#start").html();
            endSection = $("#end").html();
            if (startSection === '请选择' || endSection === '请选择') {
                layer.msg('请选择停课节次范围', {icon: 2, time: 3000});
                return false;
            }
            if (endSection < startSection) {
                layer.msg('请选择正确的停课节次范围', {icon: 2, time: 3000});
                return false;
            }
        }
        var makeUpLessons = 0;
        if ($("#makeUpLessons").hasClass('active')) {
            makeUpLessons = 1;
        }
        var makeUpLessonsTime = '';
        let makeUpLessonsStartSection = '';
        let makeUpLessonsEndSection = '';
        if (makeUpLessons === 1) {
            makeUpLessonsTime = $("#makeUpLessonsTime").val();
            if (!makeUpLessonsTime) {
                layer.msg('请选择补课日期', {icon: 2, time: 3000});
                return false;
            }
            if ($("#allDay li.cur").index() > 0) {
                makeUpLessonsStartSection = $("#makeUpLessonsStart").html();
                makeUpLessonsEndSection = $("#makeUpLessonsEnd").html();
                if (makeUpLessonsStartSection === '请选择' || makeUpLessonsEndSection === '请选择') {
                    layer.msg('请选择补课节次范围', {icon: 2, time: 3000});
                    return false;
                }
                if (makeUpLessonsEndSection < makeUpLessonsStartSection) {
                    layer.msg('请选择正确的补课节次范围', {icon: 2, time: 3000});
                    return false;
                }
            }
        }
        var descr = $("#textareaaddre").html();
        var popup = $(this).parents(".popup");
        $.post("../calendar/schedule/saveOrUpdate", {
            id: id,
            xnxq: xnxq,
            dateTime: dateTime,
            name: name,
            place: place,
            suspendStudy: suspendStudy,
            allDay: allDay,
            startSection: startSection,
            endSection: endSection,
            type: 1,
            jxz: jxz,
            descr: descr,
            makeUpLessons: makeUpLessons,
            makeUpLessonsTime: makeUpLessonsTime,
            makeUpLessonsStartSection: makeUpLessonsStartSection,
            makeUpLessonsEndSection: makeUpLessonsEndSection
        }, function (res) {
            if (res.status) {
                popup.hide();
                if (id == null || id == '') {
                    layer.msg("添加成功", {icon: 1, time: 3000});
                } else {
                    layer.msg("修改成功", {icon: 1, time: 3000});
                }
                if ($("#dateSel").attr("readonly")) {
                    getSchoolCalendarScheduleDay(xnxq);
                    return false;
                }
                $("#dateSel").val("");
                getScheduleList(1, "add");
                getSchoolCalendarScheduleDay(xnxq);
            } else {
                layer.msg(res.msg == null ? res.message : res.msg, {icon: 2, time: 3000});
            }
        })
    })

    function flashData() {
        getKbjg()
        //getEndTime();
    }

    //最小节次
    var min = null;
    //最大节次
    var max = null;

    //获取课表结构
    function getKbjg() {
        $.ajaxSettings.async = false;
        const xnxq = $(".c-top .ckd").html();
        $.get("../calendar/kbjg", {xnxq: xnxq}, function (res) {
            if (res.status) {
                let data = res.data;
                min = data[0];
                max = data[data.length - 1];
                let html = "";
                for (let i = 0; i < data.length; i++) {
                    html += '<li>' + data[i] + '</li>'
                }
                $("#start_section,#end_section,#makeUpLessonsStartSection,#makeUpLessonsEndSection").html(html);
            }
        })
        $.ajaxSettings.async = true;
    }

    //获取本学期的最后一天
    function getEndTime(elem) {
        var xnxq = $(".c-top .ckd").html();
        $.get("../calendar/end_time", {xnxq: xnxq}, function (res) {
            if (res.status) {
                let maxTime = res.data;
                flashMaxTime(maxTime, elem);
            }
        })
    }

    //刷新日历控件的最大日期
    function flashMaxTime(maxTime, elem) {
        layui.use(['laydate'], function () {
            var laydate = layui.laydate;
            laydate.render({
                elem: elem
                , format: 'yyyy-MM-dd',
                max: maxTime,
            });
        })
    }

    layui.use(['table', 'jquery', 'laydate'], function () {
        var laydate = layui.laydate;
        var table = layui.table;
        var $ = layui.jquery;
        table.render({
            elem: '#materialTable',
            id: 'scheduleList',
            data: tableData, //静态数据，真实数据用url接口
            cols: [
                [ //表头
                    {
                        type: 'checkbox',
                        width: 80
                    },
                    {
                        field: 'dateTime',
                        title: '日期',
                        align: 'center',
                        minWidth: 110
                    }, {
                    field: 'name',
                    title: '日程',
                    align: 'center',
                    minWidth: 100
                }, {
                    field: 'place',
                    title: '地点',
                    align: 'center',
                    minWidth: 100
                }, {
                    field: 'allDay',
                    title: '是否全天',
                    align: 'center',
                    minWidth: 100,
                    templet: function (d) {
                        if (d.allDay == "0") {
                            return '<span class="wstatus">否</span>';
                        } else {
                            return '<span class="wstatus">是</span>';
                        }
                    }
                }, {
                    field: 'suspendStudy',
                    title: '是否停课',
                    align: 'center',
                    minWidth: 100,
                    templet: function (d) {
                        if (d.suspendStudy == "0") {
                            return '<span class="wstatus">否</span>';
                        } else {
                            return '<span class="wstatus">是</span>';
                        }
                    }
                }, {
                    field: 'startSection',
                    title: '停课开始节次',
                    align: 'center',
                    minWidth: 100
                }, {
                    field: 'endSection',
                    title: '停课结束节次',
                    align: 'center',
                    minWidth: 100
                }, {
                    field: 'makeUpLessons',
                    title: '是否补课',
                    align: 'center',
                    minWidth: 100,
                    templet: function (d) {
                        if (d.makeUpLessons == "0") {
                            return '<span class="wstatus">否</span>';
                        } else {
                            return '<span class="wstatus">是</span>';
                        }
                    }
                }, {
                    field: 'makeUpLessonsTime',
                    title: '补课日期',
                    align: 'center',
                    minWidth: 100
                }, {
                    field: 'makeUpLessonsStartSection',
                    title: '补课开始节次',
                    align: 'center',
                    minWidth: 100
                }, {
                    field: 'makeUpLessonsEndSection',
                    title: '补课结束节次',
                    align: 'center',
                    minWidth: 100
                }, {
                    title: '操作',
                    width: 158,
                    align: 'center',
                    toolbar: '#tmplToolBar2'
                }
                ]
            ]
        });
        reloadData = function () {
            table.reload("scheduleList", {
                data: tableData   // 将新数据重新载入表格
            })
        }

        table.on('tool(materialTable)', function (obj) {
            if ('delete' == obj.event) {
                remove(obj.data.id);
            }
            if ('edit' == obj.event) {
                edit(obj.data.id);
            }
        });

        //校历日期编辑删除
        $(".date-edit .window .p-content .con .btn-list .delet").click(function () {
            let data = table.cache["scheduleList"];
            let ids = [];
            for (let i = 0; i < data.length; i++) {
                if (data[i].LAY_CHECKED) {
                    ids.push(data[i].id)
                }
            }
            remove(ids.toString());
        })
    });


    // 点击保存月备注保存
    $(".popup .window .p-btns .sure.month-sure").click(function () {
        console.log("点击保存月备注信息");
        var xnxqh = $("#monthRemarkXnxqh").text();
        var yearMonth = $("#monthRemarkYearMonthTime").text();
        var remark = $("#textareaaddress").text();
        let type = $(".month-remark").attr("type");
        if (type === "2") {
            let week = $(".nyWeek").text();
            $.ajax({
                type: "post",
                url: "/calendar/addOrUpdateCalendarWeekSet",
                data: {
                    id: $(".month-remark").attr("id"),
                    term: xnxqh,
                    remarks: remark,
                    week: week
                },
                dataType: 'json',
                success: function (result) {
                    if (result) {
                        if (result.success) {
                            let idx = 0;
                            $(".j-body .td-sort li").each(function () {
                                idx++;
                                if ($(this).text() == week) {
                                    $(this).parents(".j-body").find(".td-week-remarks li:eq(" + (idx - 1) + ")").text(remark);
                                }
                            })
                        } else {
                            console.log("保存失败")
                        }
                    }
                }
            })
            return;
        }
        $.ajax({
            type: "post",
            url: "../calendar/month/remark/save",
            data: {
                id: $(".month-remark .window").attr("id"),
                xnxq: xnxqh,
                dateTime: yearMonth,
                descr: remark,
            },
            dataType: 'json',
            success: function (result) {
                if (result) {
                    if (result.success) {
                        console.log("保存成功")
                    } else {
                        console.log("保存失败：" + result.msg);
                    }
                }
            }
        });

    });

    let holidays = ['元旦', '春节', '清明', '劳动节', '端午节', '中秋节', '国庆节'];

    function festival() {
        $(".j-table .j-tr .m-table tr td span em").each(function () {
            let day = $(this).text();
            if (day < 10) {
                day = "0" + day;
            }
            let name = "";
            const date = $(this).parents(".j-tr").attr("date") + "-" + day;
            const dateStr = new Date(date);
            const sd = Solar.fromYmd(dateStr.getFullYear(), dateStr.getMonth() + 1, dateStr.getDate());
            const s = sd.getFestivals();
            if (s && s.length > 0) {
                name = s[0];
            } else {
                const solar = Solar.fromDate(new Date(date));
                // 转阴历
                const lunar = solar.getLunar();
                const ld = Lunar.fromYmd(lunar.getYear(), lunar.getMonth(), lunar.getDay());
                const l = ld.getFestivals();
                name = l && l.length > 0 ? l[0] : "";
                if (!name) {
                    const d = Lunar.fromYmd(lunar.getYear(), lunar.getMonth(), lunar.getDay());
                    name = d.getJie();
                }
            }
            if ($.inArray(name, holidays) > -1) {
                $(this).next().append("<p>" + name + "</p>");
            }
        })
    }

    //导出校历
    $(".export").click(function () {
        //window.print();
        let holidayArray = [];
        $(".j-table .j-tr .m-table tr td span p p").each(function () {
            let ym = $(this).parents(".j-tr").attr("date");
            let day = $(this).parents("span").find("em").text();
            day = day < 10 ? "0" + day : day;
            let holiday = $(this).text();
            holidayArray.push({time: ym + "-" + day, holiday: holiday});
        })
        let loading = layer.load(1);
        $.post("/calendar/exportSchoolCalendar", {
            xnxq: $(".ckd").text(),
            type: 1,
            holidayArray: JSON.stringify(holidayArray)
        }, function (response) {
            let downloadLink = document.createElement('a');
            downloadLink.href = "/down/" + response.file + ".xls";
            downloadLink.download = "校历.xls";
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);
            layer.close(loading);
        })
    })

    //批量管理
    $(".batchManagement").click(function () {
        $(".date-edit").show();
        $("#dateSel").attr("readOnly", false).removeClass("layui-disabled").val("");
        getEndTime("#dateSel");
        getScheduleList(1);
    })

    //月备注
    $(".main .cons").on("click", ".j-table .j-body .j-tr .j-td.td-remarks .marks", function () {
        var ele = $(this)
        remarkFun(ele, 1)
    })
    // 周备注
    $(".main .cons").on("click", ".j-table .j-body .j-tr .j-td.td-week-remarks li", function () {
        var ele = $(this)
        remarkFun(ele, 2)
    })

    /* ele 当前点击元素 type 1月备注 2周备注 */
    function remarkFun(ele, type) {
        $(".month-remark").attr("type", type);
        if (type === 1) {
            $(".edit-notes .top .lable:last-child").hide()
        } else {
            $(".month-remark").attr("id", ele.attr("id"));
            $(".edit-notes .top .lable:last-child").show()
            var index = ele.index()
            var yw = ele.parents(".j-tr").find(".td-sort ul li").eq(index).text();
            $(".edit-notes .nyWeek").text(yw);
        }
        $(".edit-notes").show();
        var txt = $(".main .cons .c-top .select-input .name.ckd").text();
        var ym = ele.parents(".j-tr").find(".ym").text();
        $(".edit-notes .xnxqh").text(txt);
        $(".edit-notes .nytime").text(ym);
        var ktml = ele.html();
        $("#textareaaddress").html(ktml);
    }

</script>
</body>

</html>